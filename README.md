# 全球电力系统扩展规划MILP模型

基于Gurobi优化器的Python实现，用于全球电力系统容量扩展和运行优化。

## 模型概述

本模型实现了一个综合的电力系统扩展规划模型，包含以下特性：

- **多技术类型**: 火电、可再生能源、水电、核电、地热、储能
- **多时间尺度**: 8760小时分辨率的年度运行优化
- **多国家/区域**: 支持全球202个国家的协同优化
- **多约束类型**: 电力平衡、机组技术约束、可靠性约束等

## 文件结构

```
├── global_power_model.py    # 主模型类
├── constraints.py           # 约束条件模块
├── data_manager.py         # 数据管理模块
├── main.py                 # 主运行脚本
├── README.md               # 项目说明
└── data/                   # 数据文件夹
    ├── countries.csv       # 国家列表
    ├── demand.csv          # 需求数据
    ├── capacity_factors.csv # 容量因子数据
    ├── costs.csv           # 成本数据
    └── potential.csv       # 技术潜力数据
```

## 环境要求

### Python依赖

```bash
pip install gurobipy pandas numpy
```

### Gurobi许可证

需要有效的Gurobi许可证。可以申请学术许可证或商业许可证。

## 使用方法

### 运行简化模型（推荐测试）

```bash
python main.py simplified
```

### 运行完整模型

```bash
python main.py
```

### 自定义参数运行

```python
from main import run_global_power_model

model = run_global_power_model(
    countries_file="data/countries.csv",
    time_limit=3600,  # 1小时时间限制
    gap=0.01,         # 1%求解间隙
    output_prefix="my_results"
)
```

## 模型参数

### 技术类型

- **火电技术**: coal, gas_oil, bio
- **可再生能源**: onw (陆上风电), offw (海上风电), solar (太阳能)
- **水电**: hydro_res (水库式), hydro_ror (径流式)
- **其他**: geo (地热), nuclear (核电)
- **储能**: PSH (抽水蓄能), BAT (电池储能)

### 决策变量

- 新增装机容量变量 `I_{tech}`
- 发电出力变量 `p_{tech}`
- 储能充放电变量 `p_ch`, `p_dis`
- 输电功率变量 `p_trans`

### 约束条件

1. 电力供需平衡约束
2. 火电运行约束（启停、爬坡等）
3. 可再生能源出力约束
4. 水电运行约束（库容平衡等）
5. 储能运行约束
6. 输电容量约束
7. 系统可靠性约束

## 数据格式

### 需求数据 (demand.csv)
```csv
country,hour,demand_mw
CHN,0,800000
CHN,1,750000
...
```

### 容量因子数据 (capacity_factors.csv)
```csv
tech,country,hour,capacity_factor
solar,CHN,0,0.0
solar,CHN,12,0.8
onw,CHN,0,0.3
...
```

### 成本数据 (costs.csv)
```csv
tech,cost_type,country,value
coal,investment,CHN,2000
coal,fuel,CHN,30
...
```

## 输出结果

模型运行后会生成以下文件：

- `{prefix}_capacity.csv`: 新增装机容量结果
- `{prefix}_storage.csv`: 储能装机结果
- `global_power_model.log`: 详细运行日志

## 模型规模

对于全球50个主要国家、8760小时的模型：

- 变量数量: 约 2×10^7
- 约束数量: 约 5×10^7
- 求解时间: 1-10小时（取决于硬件和求解参数）

## 扩展说明

### 添加新技术

1. 在 `ModelParameters` 中添加技术类型
2. 在 `GlobalPowerModel` 中添加相应变量
3. 在 `ConstraintBuilder` 中添加技术约束

### 支持多年规划

修改 `ModelParameters.planning_horizon` 并扩展变量和约束的时间维度。

### 空间分辨率细化

将国家级别细化为1°×1°网格单元，需要：
1. 修改数据结构支持网格坐标
2. 添加网格间输电约束
3. 处理更大规模的数据集

## 注意事项

1. **内存需求**: 完整模型需要16GB+内存
2. **求解时间**: 可能需要数小时求解
3. **数据质量**: 结果质量依赖于输入数据的准确性
4. **许可证**: 确保Gurobi许可证有效

## 故障排除

### 常见问题

1. **Gurobi许可证错误**: 检查许可证文件路径和有效性
2. **内存不足**: 减少国家数量或时间分辨率
3. **求解超时**: 增加时间限制或放宽求解间隙
4. **模型不可行**: 检查约束设置和数据一致性

### 性能优化

1. 使用多线程求解: `model.setParam('Threads', 8)`
2. 调整求解算法: `model.setParam('Method', 2)`
3. 设置合理的求解间隙: `gap=0.05`

## 联系方式

如有问题或建议，请联系开发团队。

## 许可证

本项目基于MIT许可证发布。 