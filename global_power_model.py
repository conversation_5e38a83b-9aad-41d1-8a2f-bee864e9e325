"""
全球电力系统扩展规划MILP模型实现
Global Power System Expansion Planning Model

基于Gurobi优化器的Python实现
作者: [Your Name]
日期: 2025-01-18
"""

import gurobipy as gp
from gurobipy import GRB
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
import logging
from dataclasses import dataclass
from collections import defaultdict

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ModelParameters:
    """模型参数数据类"""
    # 基本参数
    countries: List[str]  # 国家列表
    time_periods: int = 8760  # 时间段数
    planning_horizon: int = 1  # 规划年限
    
    # 技术集合
    thermal_techs: List[str] = None  # 火电技术
    renewable_techs: List[str] = None  # 可再生能源技术
    hydro_techs: List[str] = None  # 水电技术
    other_techs: List[str] = None  # 其他技术
    storage_techs: List[str] = None  # 储能技术
    
    # 水电站点数据
    hydro_stations: Dict[str, List[str]] = None  # 每个国家的水电站列表 {country: [station_ids]}
    
    def __post_init__(self):
        """初始化默认技术集合"""
        if self.thermal_techs is None:
            self.thermal_techs = ['coal', 'gas_oil', 'bio']  # 将gas和oil合并为gas_oil
        if self.renewable_techs is None:
            self.renewable_techs = ['onw', 'offw', 'solar']
        if self.hydro_techs is None:
            self.hydro_techs = ['hydro_res', 'hydro_ror']
        if self.other_techs is None:
            self.other_techs = ['geo', 'nuclear']
        if self.storage_techs is None:
            self.storage_techs = ['PSH', 'BAT']
        if self.hydro_stations is None:
            # 默认每个国家有一个虚拟水电站
            self.hydro_stations = {country: [f'{country}_dam_01'] for country in self.countries}

class GlobalPowerModel:
    """全球电力系统扩展规划模型主类"""
    
    def __init__(self, parameters: ModelParameters):
        """
        初始化模型
        
        Args:
            parameters: 模型参数对象
        """
        self.params = parameters
        self.model = None
        self.variables = {}
        self.constraints = {}
        
        # 数据存储
        self.data = {
            'demand': {},  # 需求数据
            'capacity_factors': {},  # 容量因子
            'existing_capacity': {},  # 现有装机
            'investment_costs': {},  # 投资成本
            'operating_costs': {},  # 运行成本
            'transmission': {},  # 输电数据
            'reliability': {},  # 可靠性参数
        }
        
        logger.info(f"初始化全球电力系统模型 - {len(self.params.countries)}个国家")
    
    def create_model(self):
        """创建Gurobi模型"""
        self.model = gp.Model("GlobalPowerSystemPlanning")
        self.model.setParam('OutputFlag', 1)
        self.model.setParam('Method', 2)  # 使用barrier算法
        self.model.setParam('Threads', 0)  # 使用所有可用线程
        logger.info("Gurobi模型已创建")
    
    def add_variables(self):
        """添加决策变量"""
        if self.model is None:
            self.create_model()
        
        countries = self.params.countries
        T = range(self.params.time_periods)
        
        # 1. 扩展决策变量
        self._add_capacity_variables()
        
        # 2. 火电运行变量
        self._add_thermal_variables()
        
        # 3. 可再生能源变量
        self._add_renewable_variables()
        
        # 4. 水电变量
        self._add_hydro_variables()
        
        # 5. 其他发电变量
        self._add_other_generation_variables()
        
        # 6. 储能变量
        self._add_storage_variables()
        
        # 7. 输电变量
        self._add_transmission_variables()
        
        # 8. 松弛变量
        self._add_slack_variables()
        
        logger.info("所有决策变量已添加")
    
    def _add_capacity_variables(self):
        """添加装机容量变量"""
        countries = self.params.countries
        
        # 发电装机容量
        for tech in self.params.thermal_techs:
            self.variables[f'I_{tech}'] = self.model.addVars(
                countries, lb=0, name=f'I_{tech}'
            )
        
        for tech in self.params.renewable_techs:
            self.variables[f'I_{tech}'] = self.model.addVars(
                countries, lb=0, name=f'I_{tech}'
            )
        
        # 水电装机容量 - 水库式水电按站点级别建模
        # 径流式水电仍为国家级别
        self.variables['I_hydro_ror'] = self.model.addVars(
            countries, lb=0, name='I_hydro_ror'
        )
        
        # 水库式水电按站点级别建模
        self.variables['I_hydro_res'] = self.model.addVars(
            [(n, h) for n in countries for h in self.params.hydro_stations.get(n, [])], 
            lb=0, name='I_hydro_res'
        )
        
        # 储能装机容量
        for tech in self.params.storage_techs:
            self.variables[f'I_{tech}_P'] = self.model.addVars(
                countries, lb=0, name=f'I_{tech}_P'
            )
            self.variables[f'I_{tech}_E'] = self.model.addVars(
                countries, lb=0, name=f'I_{tech}_E'
            )
        
        # 输电线路容量（需要定义线路集合）
        # 暂时用国家间连接表示
        self.variables['L'] = self.model.addVars(
            countries, countries, lb=0, name='L'
        )
    
    def _add_thermal_variables(self):
        """添加火电运行变量"""
        countries = self.params.countries
        T = range(self.params.time_periods)
        
        for tech in self.params.thermal_techs:
            # 在线容量
            self.variables[f'p_bar_{tech}'] = self.model.addVars(
                countries, T, lb=0, name=f'p_bar_{tech}'
            )
            # 实际出力
            self.variables[f'p_{tech}'] = self.model.addVars(
                countries, T, lb=0, name=f'p_{tech}'
            )
            # 启动容量
            self.variables[f'x_su_{tech}'] = self.model.addVars(
                countries, T, lb=0, name=f'x_su_{tech}'
            )
            # 停机容量
            self.variables[f'x_sd_{tech}'] = self.model.addVars(
                countries, T, lb=0, name=f'x_sd_{tech}'
            )
    
    def _add_renewable_variables(self):
        """添加可再生能源变量"""
        countries = self.params.countries
        T = range(self.params.time_periods)
        
        for tech in self.params.renewable_techs:
            self.variables[f'p_{tech}'] = self.model.addVars(
                countries, T, lb=0, name=f'p_{tech}'
            )
    
    def _add_hydro_variables(self):
        """添加水电变量"""
        countries = self.params.countries
        T = range(self.params.time_periods)
        
        # 水库式水电（按站点级别建模）
        # p_{n,h,t}^{res}: 国家n水电站h在时段t的发电出力
        self.variables['p_hydro_res'] = self.model.addVars(
            [(n, h, t) for n in countries for h in self.params.hydro_stations.get(n, []) for t in T],
            lb=0, name='p_hydro_res'
        )
        
        # q_{n,h,t}^{out}: 国家n水电站h在时段t的出水流量
        self.variables['q_out'] = self.model.addVars(
            [(n, h, t) for n in countries for h in self.params.hydro_stations.get(n, []) for t in T],
            lb=0, name='q_out'
        )
        
        # q_{n,h,t}^{spill}: 国家n水电站h在时段t的溢流量
        self.variables['q_spill'] = self.model.addVars(
            [(n, h, t) for n in countries for h in self.params.hydro_stations.get(n, []) for t in T],
            lb=0, name='q_spill'
        )
        
        # v_{n,h,t}: 国家n水电站h在时段t末的库容
        self.variables['v'] = self.model.addVars(
            [(n, h, t) for n in countries for h in self.params.hydro_stations.get(n, []) for t in T],
            lb=0, name='v'
        )
        
        # 径流式水电（仍为国家级别）
        # p_{n,t}^{ror}: 国家n径流式水电在时段t的发电出力
        self.variables['p_hydro_ror'] = self.model.addVars(
            countries, T, lb=0, name='p_hydro_ror'
        )
    
    def _add_other_generation_variables(self):
        """添加其他发电变量"""
        countries = self.params.countries
        T = range(self.params.time_periods)
        
        # 地热
        self.variables['p_geo'] = self.model.addVars(
            countries, T, lb=0, name='p_geo'
        )
        
        # 核电
        self.variables['p_nuclear'] = self.model.addVars(
            countries, T, lb=0, name='p_nuclear'
        )
    
    def _add_storage_variables(self):
        """添加储能变量"""
        countries = self.params.countries
        T = range(self.params.time_periods)
        
        for tech in self.params.storage_techs:
            # 充电功率
            self.variables[f'p_ch_{tech}'] = self.model.addVars(
                countries, T, lb=0, name=f'p_ch_{tech}'
            )
            # 放电功率
            self.variables[f'p_dis_{tech}'] = self.model.addVars(
                countries, T, lb=0, name=f'p_dis_{tech}'
            )
            # 能量水平
            self.variables[f'e_{tech}'] = self.model.addVars(
                countries, T, lb=0, name=f'e_{tech}'
            )
    
    def _add_transmission_variables(self):
        """添加输电变量"""
        countries = self.params.countries
        T = range(self.params.time_periods)
        
        # 输电功率（简化为双向）
        self.variables['p_trans'] = self.model.addVars(
            countries, countries, T, lb=-GRB.INFINITY, name='p_trans'
        )
    
    def _add_slack_variables(self):
        """添加松弛变量"""
        countries = self.params.countries
        T = range(self.params.time_periods)
        
        # 缺电量
        self.variables['shed'] = self.model.addVars(
            countries, T, lb=0, name='shed'
        )
        
        # 平衡松弛
        self.variables['slack'] = self.model.addVars(
            countries, T, lb=0, name='slack'
        )
    
    def set_objective(self):
        """设置目标函数 - 最小化总年成本"""
        if self.model is None:
            raise ValueError("模型尚未创建")
        
        objective = 0
        countries = self.params.countries
        T = range(self.params.time_periods)
        
        # 1a: 发电技术投资成本
        for n in countries:
            for tech in self.params.thermal_techs:
                if f'IC_{tech}' in self.data['investment_costs']:
                    ic = self.data['investment_costs'][f'IC_{tech}'].get(n, 0)
                    objective += ic * self.variables[f'I_{tech}'][n]
            
            for tech in self.params.renewable_techs:
                if f'IC_{tech}' in self.data['investment_costs']:
                    ic = self.data['investment_costs'][f'IC_{tech}'].get(n, 0)
                    objective += ic * self.variables[f'I_{tech}'][n]
            
            # 径流式水电投资成本（国家级别）
            if 'IC_hydro_ror' in self.data['investment_costs']:
                ic = self.data['investment_costs']['IC_hydro_ror'].get(n, 0)
                objective += ic * self.variables['I_hydro_ror'][n]
            
            # 水库式水电投资成本（站点级别求和）
            for h in self.params.hydro_stations.get(n, []):
                if f'IC_hydro_res_{h}' in self.data['investment_costs']:
                    ic = self.data['investment_costs'][f'IC_hydro_res_{h}'].get(n, 0)
                    objective += ic * self.variables['I_hydro_res'][n, h]
                elif 'IC_hydro_res' in self.data['investment_costs']:
                    # 如果没有站点级别的数据，使用通用数据
                    ic = self.data['investment_costs']['IC_hydro_res'].get(n, 0)
                    objective += ic * self.variables['I_hydro_res'][n, h]
        
        # 1b: 储能和输电投资成本
        for n in countries:
            for tech in self.params.storage_techs:
                if f'IC_{tech}_P' in self.data['investment_costs']:
                    ic_p = self.data['investment_costs'][f'IC_{tech}_P'].get(n, 0)
                    objective += ic_p * self.variables[f'I_{tech}_P'][n]
                if f'IC_{tech}_E' in self.data['investment_costs']:
                    ic_e = self.data['investment_costs'][f'IC_{tech}_E'].get(n, 0)
                    objective += ic_e * self.variables[f'I_{tech}_E'][n]
        
        # 1c-1d: 固定运维成本（需要现有装机数据）
        # 暂时跳过，需要数据支持
        
        # 1e: 燃料消耗成本
        for n in countries:
            for t in T:
                for tech in self.params.thermal_techs:
                    if f'FC_{tech}' in self.data['operating_costs']:
                        fc = self.data['operating_costs'][f'FC_{tech}'].get(n, 0)
                        objective += fc * self.variables[f'p_{tech}'][n, t]
        
        # 1f: 启停成本和运行成本
        for n in countries:
            for t in T:
                for tech in self.params.thermal_techs:
                    if f'SC_{tech}' in self.data['operating_costs']:
                        sc = self.data['operating_costs'][f'SC_{tech}'].get(n, 0)
                        objective += sc * self.variables[f'x_su_{tech}'][n, t]
                
                # 水库式水电运行成本（站点级别求和）
                for h in self.params.hydro_stations.get(n, []):
                    if f'MC_hydro_res_{h}' in self.data['operating_costs']:
                        mc = self.data['operating_costs'][f'MC_hydro_res_{h}'].get(n, 0)
                        objective += mc * self.variables['p_hydro_res'][n, h, t]
                    elif 'MC_hydro_res' in self.data['operating_costs']:
                        # 如果没有站点级别的数据，使用通用数据
                        mc = self.data['operating_costs']['MC_hydro_res'].get(n, 0)
                        objective += mc * self.variables['p_hydro_res'][n, h, t]
                
                # 径流式水电运行成本（国家级别）
                if 'MC_hydro_ror' in self.data['operating_costs']:
                    mc = self.data['operating_costs']['MC_hydro_ror'].get(n, 0)
                    objective += mc * self.variables['p_hydro_ror'][n, t]
                
                # 地热运行成本
                if 'MC_geo' in self.data['operating_costs']:
                    mc = self.data['operating_costs']['MC_geo'].get(n, 0)
                    objective += mc * self.variables['p_geo'][n, t]
                
                # 核电运行成本
                if 'MC_nuclear' in self.data['operating_costs']:
                    mc = self.data['operating_costs']['MC_nuclear'].get(n, 0)
                    objective += mc * self.variables['p_nuclear'][n, t]
        
        # 1g: 缺电惩罚成本
        shed_cost = 10000  # 默认缺电成本
        for n in countries:
            for t in T:
                objective += shed_cost * self.variables['shed'][n, t]
        
        self.model.setObjective(objective, GRB.MINIMIZE)
        logger.info("目标函数已设置")
    
    def load_data(self, data_dict: Dict):
        """加载模型数据"""
        self.data.update(data_dict)
        logger.info("模型数据已加载")
    
    def solve(self, time_limit: Optional[int] = None, gap: Optional[float] = None):
        """求解模型"""
        if self.model is None:
            raise ValueError("模型尚未建立")
        
        if time_limit:
            self.model.setParam('TimeLimit', time_limit)
        if gap:
            self.model.setParam('MIPGap', gap)
        
        logger.info("开始求解模型...")
        self.model.optimize()
        
        if self.model.status == GRB.OPTIMAL:
            logger.info(f"模型求解成功！目标函数值: {self.model.objVal:.2e}")
        elif self.model.status == GRB.INFEASIBLE:
            logger.error("模型不可行")
            self.model.computeIIS()
            self.model.write("infeasible.ilp")
        else:
            logger.warning(f"求解状态: {self.model.status}")
    
    def get_solution(self) -> Dict:
        """获取求解结果"""
        if self.model.status != GRB.OPTIMAL:
            return {}
        
        solution = {}
        
        # 提取装机容量结果
        solution['capacity'] = {}
        
        # 火电和可再生能源（国家级别）
        for tech in self.params.thermal_techs + self.params.renewable_techs:
            solution['capacity'][tech] = {
                n: self.variables[f'I_{tech}'][n].x 
                for n in self.params.countries
            }
        
        # 径流式水电（国家级别）
        solution['capacity']['hydro_ror'] = {
            n: self.variables['I_hydro_ror'][n].x 
            for n in self.params.countries
        }
        
        # 水库式水电（站点级别）
        solution['capacity']['hydro_res'] = {}
        for n in self.params.countries:
            solution['capacity']['hydro_res'][n] = {}
            for h in self.params.hydro_stations.get(n, []):
                solution['capacity']['hydro_res'][n][h] = self.variables['I_hydro_res'][n, h].x
        
        # 提取储能容量结果
        solution['storage'] = {}
        for tech in self.params.storage_techs:
            solution['storage'][f'{tech}_P'] = {
                n: self.variables[f'I_{tech}_P'][n].x 
                for n in self.params.countries
            }
            solution['storage'][f'{tech}_E'] = {
                n: self.variables[f'I_{tech}_E'][n].x 
                for n in self.params.countries
            }
        
        return solution
    
    def save_results(self, filename: str):
        """保存结果到文件"""
        solution = self.get_solution()
        
        # 转换为DataFrame并保存
        capacity_df = pd.DataFrame(solution.get('capacity', {}))
        capacity_df.to_csv(f"{filename}_capacity.csv")
        
        storage_df = pd.DataFrame(solution.get('storage', {}))
        storage_df.to_csv(f"{filename}_storage.csv")
        
        logger.info(f"结果已保存到 {filename}_*.csv")


def create_sample_model():
    """创建示例模型"""
    # 示例国家（可以扩展到全球202个国家）
    countries = ['CHN', 'USA', 'IND', 'JPN', 'DEU']
    
    # 创建参数对象
    params = ModelParameters(countries=countries)
    
    # 创建模型
    model = GlobalPowerModel(params)
    
    # 添加变量
    model.add_variables()
    
    # 设置目标函数
    model.set_objective()
    
    return model


if __name__ == "__main__":
    # 创建并测试示例模型
    model = create_sample_model()
    print("全球电力系统扩展规划模型已创建")
    print(f"模型包含 {len(model.params.countries)} 个国家")
    print(f"技术类型: {model.params.thermal_techs + model.params.renewable_techs}") 