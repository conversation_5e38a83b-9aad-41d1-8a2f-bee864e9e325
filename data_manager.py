"""
全球电力系统扩展规划模型数据管理模块
Data Management Module

处理输入数据、验证数据完整性和提供数据接口
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import logging
import json
import os
from pathlib import Path

logger = logging.getLogger(__name__)

class DataManager:
    """数据管理器类"""
    
    def __init__(self, data_path: str = "data"):
        """
        初始化数据管理器
        
        Args:
            data_path: 数据文件夹路径
        """
        self.data_path = Path(data_path)
        self.data_path.mkdir(exist_ok=True)
        
        # 数据存储
        self.countries = []
        self.technologies = {}
        self.time_series_data = {}
        self.static_data = {}
        
        logger.info(f"数据管理器初始化完成，数据路径: {self.data_path}")
    
    def load_countries(self, countries_file: str = None):
        """
        加载国家列表
        
        Args:
            countries_file: 国家列表文件路径
        """
        if countries_file is None:
            # 使用默认的全球主要国家列表
            self.countries = self._get_default_countries()
        else:
            try:
                countries_df = pd.read_csv(self.data_path / countries_file)
                self.countries = countries_df['country_code'].tolist()
            except Exception as e:
                logger.error(f"读取国家列表失败: {e}")
                self.countries = self._get_default_countries()
        
        logger.info(f"已加载 {len(self.countries)} 个国家")
    
    def _get_default_countries(self):
        """获取默认的全球主要国家列表"""
        return [
            'CHN', 'USA', 'IND', 'JPN', 'DEU', 'RUS', 'CAN', 'BRA', 'KOR', 'IRN',
            'SAU', 'AUS', 'FRA', 'GBR', 'IDN', 'MEX', 'ITA', 'TUR', 'ZAF', 'THA',
            'ESP', 'NLD', 'POL', 'ARG', 'EGY', 'UKR', 'PAK', 'NGA', 'VNM', 'BGD',
            'MYS', 'KAZ', 'CHL', 'PHL', 'BEL', 'IRQ', 'FIN', 'VEN', 'CZE', 'PER',
            'NOR', 'ROU', 'NZL', 'ISR', 'DZA', 'AUT', 'COL', 'GRC', 'PRT', 'SWE'
        ]
    
    def load_demand_data(self, demand_file: str = None):
        """
        加载需求数据
        
        Args:
            demand_file: 需求数据文件路径
        """
        if demand_file is None:
            # 生成示例需求数据
            self._generate_sample_demand_data()
        else:
            try:
                demand_df = pd.read_csv(self.data_path / demand_file)
                self.time_series_data['demand'] = self._process_demand_data(demand_df)
            except Exception as e:
                logger.error(f"读取需求数据失败: {e}")
                self._generate_sample_demand_data()
        
        logger.info("需求数据加载完成")
    
    def _generate_sample_demand_data(self):
        """生成示例需求数据"""
        demand_data = {}
        
        for country in self.countries:
            # 基础负荷（按人口和经济水平估算）
            base_load = self._get_country_base_load(country)
            
            for t in range(8760):
                # 简单的日负荷曲线和季节性变化
                hour_of_day = t % 24
                day_of_year = t // 24
                
                # 日负荷曲线（早8点和晚6点为峰值）
                daily_factor = 0.7 + 0.3 * (
                    0.5 * (1 + np.cos(2 * np.pi * (hour_of_day - 8) / 12)) +
                    0.5 * (1 + np.cos(2 * np.pi * (hour_of_day - 18) / 12))
                )
                
                # 季节性因子（夏季和冬季用电量较高）
                seasonal_factor = 0.8 + 0.2 * (1 + np.cos(2 * np.pi * day_of_year / 365))
                
                demand_data[(country, t)] = base_load * daily_factor * seasonal_factor
        
        self.time_series_data['demand'] = demand_data
    
    def _get_country_base_load(self, country: str) -> float:
        """获取国家基础负荷（MW）"""
        # 简化的基础负荷数据
        base_loads = {
            'CHN': 800000, 'USA': 500000, 'IND': 200000, 'JPN': 180000, 'DEU': 80000,
            'RUS': 250000, 'CAN': 150000, 'BRA': 160000, 'KOR': 110000, 'IRN': 70000,
            'SAU': 80000, 'AUS': 50000, 'FRA': 100000, 'GBR': 60000, 'IDN': 60000,
            'MEX': 70000, 'ITA': 60000, 'TUR': 50000, 'ZAF': 45000, 'THA': 40000
        }
        return base_loads.get(country, 20000)  # 默认20GW
    
    def load_capacity_factors(self, cf_file: str = None):
        """
        加载容量因子数据
        
        Args:
            cf_file: 容量因子文件路径
        """
        if cf_file is None:
            self._generate_sample_capacity_factors()
        else:
            try:
                cf_df = pd.read_csv(self.data_path / cf_file)
                self.time_series_data['capacity_factors'] = self._process_capacity_factors(cf_df)
            except Exception as e:
                logger.error(f"读取容量因子数据失败: {e}")
                self._generate_sample_capacity_factors()
        
        logger.info("容量因子数据加载完成")
    
    def _generate_sample_capacity_factors(self):
        """生成示例容量因子数据"""
        cf_data = {}
        
        renewable_techs = ['onw', 'offw', 'solar']
        
        for tech in renewable_techs:
            for country in self.countries:
                for t in range(8760):
                    cf_data[(tech, country, t)] = self._get_sample_capacity_factor(tech, country, t)
        
        self.time_series_data['capacity_factors'] = cf_data
    
    def _get_sample_capacity_factor(self, tech: str, country: str, hour: int) -> float:
        """获取示例容量因子"""
        hour_of_day = hour % 24
        day_of_year = hour // 24
        
        if tech == 'solar':
            # 太阳能：白天高，夜晚为0
            if 6 <= hour_of_day <= 18:
                base_cf = 0.3 + 0.4 * np.sin(np.pi * (hour_of_day - 6) / 12)
                # 季节性调整
                seasonal_factor = 0.8 + 0.2 * np.cos(2 * np.pi * day_of_year / 365)
                return min(1.0, base_cf * seasonal_factor)
            else:
                return 0.0
        
        elif tech == 'onw':
            # 陆上风电：基础容量因子 + 随机波动
            base_cf = self._get_wind_base_cf(country)
            random_factor = 0.8 + 0.4 * np.random.random()
            return min(1.0, base_cf * random_factor)
        
        elif tech == 'offw':
            # 海上风电：通常比陆上风电容量因子更高
            base_cf = self._get_wind_base_cf(country) * 1.3
            random_factor = 0.8 + 0.4 * np.random.random()
            return min(1.0, base_cf * random_factor)
        
        return 0.3  # 默认值
    
    def _get_wind_base_cf(self, country: str) -> float:
        """获取风电基础容量因子"""
        wind_cf = {
            'CHN': 0.25, 'USA': 0.35, 'IND': 0.20, 'DEU': 0.30, 'DNK': 0.45,
            'GBR': 0.40, 'FRA': 0.25, 'ESP': 0.30, 'ITA': 0.25, 'NLD': 0.35
        }
        return wind_cf.get(country, 0.25)
    
    def load_existing_capacity(self, capacity_file: str = None):
        """
        加载现有装机容量数据
        
        Args:
            capacity_file: 装机容量文件路径
        """
        if capacity_file is None:
            self._generate_sample_existing_capacity()
        else:
            try:
                capacity_df = pd.read_csv(self.data_path / capacity_file)
                self.static_data['existing_capacity'] = self._process_existing_capacity(capacity_df)
            except Exception as e:
                logger.error(f"读取现有装机容量失败: {e}")
                self._generate_sample_existing_capacity()
        
        logger.info("现有装机容量数据加载完成")
    
    def _generate_sample_existing_capacity(self):
        """生成示例现有装机容量"""
        capacity_data = {}
        
        all_techs = ['coal', 'gas_oil', 'bio', 'onw', 'offw', 'solar',
                    'hydro_res', 'hydro_ror', 'geo', 'nuclear']  # 将gas和oil合并为gas_oil
        
        for country in self.countries:
            total_demand = max([self.time_series_data['demand'].get((country, t), 0) 
                              for t in range(8760)])
            
            for tech in all_techs:
                # 基于技术类型分配现有装机
                ratio = self._get_tech_share(country, tech)
                capacity_data[f'{tech}_{country}'] = total_demand * ratio
        
        self.static_data['existing_capacity'] = capacity_data
    
    def _get_tech_share(self, country: str, tech: str) -> float:
        """获取技术在国家电力结构中的占比"""
        # 简化的技术占比（基于全球平均值调整）
        base_shares = {
            'coal': 0.3, 'gas_oil': 0.30, 'bio': 0.02,  # gas_oil合并了原gas(0.25)和oil(0.05)
            'onw': 0.08, 'offw': 0.02, 'solar': 0.06,
            'hydro_res': 0.15, 'hydro_ror': 0.05, 'geo': 0.01, 'nuclear': 0.1
        }

        # 根据国家调整（发达国家可再生能源比例较高）
        developed_countries = ['DEU', 'DNK', 'NLD', 'SWE', 'NOR', 'FIN']
        if country in developed_countries:
            if tech in ['onw', 'offw', 'solar']:
                return base_shares[tech] * 2
            elif tech in ['coal', 'gas_oil']:  # 更新为gas_oil
                return base_shares[tech] * 0.5
        
        return base_shares.get(tech, 0.05)
    
    def load_cost_data(self, cost_file: str = None):
        """
        加载成本数据
        
        Args:
            cost_file: 成本数据文件路径
        """
        if cost_file is None:
            self._generate_sample_cost_data()
        else:
            try:
                cost_df = pd.read_csv(self.data_path / cost_file)
                investment_costs, operating_costs = self._process_cost_data(cost_df)
                self.static_data['investment_costs'] = investment_costs
                self.static_data['operating_costs'] = operating_costs
            except Exception as e:
                logger.error(f"读取成本数据失败: {e}")
                self._generate_sample_cost_data()
        
        logger.info("成本数据加载完成")
    
    def _generate_sample_cost_data(self):
        """生成示例成本数据"""
        # 投资成本 ($/kW)
        investment_costs = {
            'IC_coal': {country: 2000 for country in self.countries},
            'IC_gas_oil': {country: 1300 for country in self.countries},  # gas和oil的加权平均值
            'IC_bio': {country: 3000 for country in self.countries},
            'IC_onw': {country: 1500 for country in self.countries},
            'IC_offw': {country: 3000 for country in self.countries},
            'IC_solar': {country: 1000 for country in self.countries},
            'IC_hydro_res': {country: 2500 for country in self.countries},
            'IC_hydro_ror': {country: 2000 for country in self.countries},
            'IC_PSH_P': {country: 1500 for country in self.countries},
            'IC_PSH_E': {country: 50 for country in self.countries},
            'IC_BAT_P': {country: 800 for country in self.countries},
            'IC_BAT_E': {country: 300 for country in self.countries},
        }
        
        # 运行成本 ($/MWh)
        operating_costs = {
            'FC_coal': {country: 30 for country in self.countries},
            'FC_gas_oil': {country: 60 for country in self.countries},  # gas和oil的加权平均
            'FC_bio': {country: 25 for country in self.countries},
            'SC_coal': {country: 100 for country in self.countries},
            'SC_gas_oil': {country: 65 for country in self.countries},  # gas和oil的加权平均
            'SC_bio': {country: 60 for country in self.countries},
            'MC_hydro_res': {country: 5 for country in self.countries},
            'MC_hydro_ror': {country: 3 for country in self.countries},
            'MC_geo': {country: 15 for country in self.countries},
            'MC_nuclear': {country: 20 for country in self.countries},
        }
        
        # 添加水库式水电站点级别成本数据
        investment_costs.update(self._generate_hydro_station_costs())
        operating_costs.update(self._generate_hydro_station_operating_costs())
        
        self.static_data['investment_costs'] = investment_costs
        self.static_data['operating_costs'] = operating_costs
    
    def _generate_hydro_station_costs(self) -> Dict:
        """生成水电站点级别投资成本"""
        station_costs = {}
        
        for country in self.countries:
            # 获取该国家的水电站列表
            stations = self._get_country_hydro_stations(country)
            
            for station_id in stations:
                # 基础投资成本：2500 $/kW，根据站点条件调整
                base_cost = 2500
                
                # 根据站点特征调整成本（简化模拟）
                size_factor = self._get_station_size_factor(station_id)
                terrain_factor = self._get_station_terrain_factor(station_id)
                
                station_costs[f'IC_hydro_res_{station_id}'] = {
                    country: base_cost * size_factor * terrain_factor
                }
        
        return station_costs
        
    def _generate_hydro_station_operating_costs(self) -> Dict:
        """生成水电站点级别运行成本"""
        station_costs = {}
        
        for country in self.countries:
            stations = self._get_country_hydro_stations(country)
            
            for station_id in stations:
                # 基础运行成本：5 $/MWh，根据效率调整
                base_cost = 5
                efficiency_factor = self._get_station_efficiency_factor(station_id)
                
                station_costs[f'MC_hydro_res_{station_id}'] = {
                    country: base_cost / efficiency_factor
                }
        
        return station_costs
        
    def _get_country_hydro_stations(self, country: str) -> List[str]:
        """获取国家的水电站列表"""
        # 简化实现：每个国家生成2-5个虚拟水电站
        hydro_factor = self._get_hydro_factor(country)
        num_stations = min(5, max(1, int(hydro_factor // 2)))
        return [f'{country}_dam_{i:02d}' for i in range(1, num_stations + 1)]
        
    def _get_station_size_factor(self, station_id: str) -> float:
        """获取水电站规模因子（影响投资成本）"""
        # 基于站点ID的简单模拟
        hash_val = hash(station_id) % 100
        if hash_val < 20:
            return 0.8  # 小型水电站，成本较低
        elif hash_val < 70:
            return 1.0  # 中型水电站，标准成本
        else:
            return 1.3  # 大型水电站，成本较高
            
    def _get_station_terrain_factor(self, station_id: str) -> float:
        """获取水电站地形因子（影响建设成本）"""
        # 基于站点ID的简单模拟
        hash_val = hash(station_id + '_terrain') % 100
        if hash_val < 30:
            return 0.9  # 地形条件好，成本较低
        elif hash_val < 80:
            return 1.0  # 标准地形条件
        else:
            return 1.2  # 地形复杂，成本较高
            
    def _get_station_efficiency_factor(self, station_id: str) -> float:
        """获取水电站效率因子"""
        # 基于站点ID的简单模拟
        hash_val = hash(station_id + '_efficiency') % 100
        return 0.8 + 0.2 * (hash_val / 100)  # 0.8-1.0范围
    
    def load_potential_data(self, potential_file: str = None):
        """
        加载技术潜力数据
        
        Args:
            potential_file: 技术潜力文件路径
        """
        if potential_file is None:
            self._generate_sample_potential_data()
        else:
            try:
                potential_df = pd.read_csv(self.data_path / potential_file)
                self.static_data['potential'] = self._process_potential_data(potential_df)
            except Exception as e:
                logger.error(f"读取技术潜力数据失败: {e}")
                self._generate_sample_potential_data()
        
        logger.info("技术潜力数据加载完成")
    
    def _generate_sample_potential_data(self):
        """生成示例技术潜力数据"""
        potential_data = {}
        
        renewable_techs = ['onw', 'offw', 'solar', 'bio', 'hydro_res', 'hydro_ror']
        
        for country in self.countries:
            country_area = self._get_country_area(country)  # km²
            
            for tech in renewable_techs:
                if tech == 'onw':
                    # 陆上风电：可用土地面积的3%
                    potential_data[f'{tech}_{country}'] = country_area * 0.03 * 5  # MW/km²
                elif tech == 'offw':
                    # 海上风电：基于海岸线长度
                    coastline_factor = self._get_coastline_factor(country)
                    potential_data[f'{tech}_{country}'] = coastline_factor * 1000
                elif tech == 'solar':
                    # 太阳能：可用土地面积的5%
                    potential_data[f'{tech}_{country}'] = country_area * 0.05 * 50  # MW/km²
                elif tech == 'bio':
                    # 生物质：基于农业用地
                    ag_factor = self._get_agriculture_factor(country)
                    potential_data[f'{tech}_{country}'] = ag_factor * 100
                else:
                    # 水电：基于地理条件
                    hydro_factor = self._get_hydro_factor(country)
                    potential_data[f'{tech}_{country}'] = hydro_factor * 1000
        
        self.static_data['potential'] = potential_data
    
    def _get_country_area(self, country: str) -> float:
        """获取国家面积（km²）"""
        areas = {
            'CHN': 9596961, 'USA': 9833517, 'RUS': 17098242, 'CAN': 9984670,
            'BRA': 8514877, 'AUS': 7692024, 'IND': 3287263, 'ARG': 2780400,
            'KAZ': 2724900, 'DZA': 2381741, 'COD': 2344858, 'SAU': 2149690,
            'MEX': 1964375, 'IDN': 1904569, 'SDN': 1861484, 'LBY': 1759540
        }
        return areas.get(country, 500000)  # 默认50万km²
    
    def _get_coastline_factor(self, country: str) -> float:
        """获取海岸线因子"""
        coastline_countries = ['NOR', 'GBR', 'JPN', 'CAN', 'AUS', 'CHL', 'NZL', 'DNK']
        return 10.0 if country in coastline_countries else 1.0
    
    def _get_agriculture_factor(self, country: str) -> float:
        """获取农业因子"""
        agricultural_countries = ['BRA', 'USA', 'ARG', 'UKR', 'IND', 'CHN']
        return 5.0 if country in agricultural_countries else 1.0
    
    def _get_hydro_factor(self, country: str) -> float:
        """获取水电因子"""
        hydro_countries = ['CAN', 'BRA', 'NOR', 'CHN', 'RUS', 'VEN', 'IND']
        return 10.0 if country in hydro_countries else 2.0
    
    def get_model_data(self) -> Dict:
        """
        获取模型所需的完整数据
        
        Returns:
            包含所有模型数据的字典
        """
        model_data = {}
        
        # 合并时间序列数据
        model_data.update(self.time_series_data)
        
        # 合并静态数据
        model_data.update(self.static_data)
        
        # 添加可靠性相关数据
        model_data['reliability_factors'] = self._get_reliability_factors()
        model_data['reliability_requirement'] = self._get_reliability_requirements()
        
        # 添加火电技术参数（约束8-12所需）
        model_data.update(self._get_thermal_technical_parameters())
        
        logger.info("模型数据准备完成")
        return model_data
    
    def _get_reliability_factors(self) -> Dict:
        """获取可靠性因子"""
        return {
            f'{tech}_{country}': self._get_tech_reliability(tech)
            for tech in ['coal', 'gas', 'oil', 'bio', 'onw', 'offw', 'solar', 
                        'hydro_res', 'hydro_ror', 'geo', 'nuclear']
            for country in self.countries
        }
    
    def _get_tech_reliability(self, tech: str) -> float:
        """获取技术可靠性因子"""
        reliability = {
            'coal': 0.95, 'gas_oil': 0.91, 'bio': 0.88,  # gas_oil取gas和oil的平均值
            'onw': 0.10, 'offw': 0.12, 'solar': 0.08,
            'hydro_res': 0.85, 'hydro_ror': 0.70, 'geo': 0.95, 'nuclear': 0.90
        }
        return reliability.get(tech, 0.8)
    
    def _get_reliability_requirements(self) -> Dict:
        """获取可靠性需求"""
        return {
            country: max([self.time_series_data['demand'].get((country, t), 0) 
                         for t in range(8760)]) * 1.15  # 15%备用容量
            for country in self.countries
        }
    
    def _get_thermal_technical_parameters(self) -> Dict:
        """获取火电技术参数"""
        
        # 最小运行时间（小时）
        min_up_time = {}
        # 最小停机时间（小时）
        min_down_time = {}
        # 爬坡速率（每小时最大变化率，占装机容量比例）
        ramp_up = {}
        ramp_down = {}
        # 最小出力比例
        min_output = {}
        max_output = {}
        # 热耗率（GJ/MWh）
        heat_rate = {}
        # CO2排放因子（tCO2/MWh）
        co2_emission_factor = {}
        # 年度启动次数限制
        annual_startup_limit = {}
        
        thermal_techs = ['coal', 'gas_oil', 'bio']  # 将gas和oil合并为gas_oil
        
        for tech in thermal_techs:
            for country in self.countries:
                # 根据技术类型设置不同的参数
                if tech == 'coal':
                    min_up_time[f'{tech}_{country}'] = 8  # 燃煤机组最小运行8小时
                    min_down_time[f'{tech}_{country}'] = 4  # 最小停机4小时
                    ramp_up[f'{tech}_{country}'] = 0.3  # 每小时最大上调30%
                    ramp_down[f'{tech}_{country}'] = 0.3
                    min_output[f'{tech}_{country}'] = 0.4  # 最小出力40%
                    max_output[f'{tech}_{country}'] = 1.0
                    heat_rate[f'{tech}_{country}'] = 12.0  # 燃煤热耗率较高
                    co2_emission_factor[f'{tech}_{country}'] = 0.95  # 燃煤CO2排放高
                    annual_startup_limit[f'{tech}_{country}'] = 50  # 年度启动50次
                
                elif tech == 'gas_oil':
                    # 油气机组参数：综合gas和oil的特性，偏向于灵活性较好的特征
                    min_up_time[f'{tech}_{country}'] = 2  # 保持较好的灵活性
                    min_down_time[f'{tech}_{country}'] = 1
                    ramp_up[f'{tech}_{country}'] = 0.7  # 取gas和oil的平均值，偏向更好的调节能力
                    ramp_down[f'{tech}_{country}'] = 0.7
                    min_output[f'{tech}_{country}'] = 0.15  # 取gas和oil的平均值
                    max_output[f'{tech}_{country}'] = 1.0
                    heat_rate[f'{tech}_{country}'] = 9.0  # gas和oil的加权平均(考虑gas占比更大)
                    co2_emission_factor[f'{tech}_{country}'] = 0.55  # gas和oil的加权平均
                    annual_startup_limit[f'{tech}_{country}'] = 250  # 保持较高的启动灵活性
                
                elif tech == 'bio':
                    min_up_time[f'{tech}_{country}'] = 4  # 生物质机组
                    min_down_time[f'{tech}_{country}'] = 2
                    ramp_up[f'{tech}_{country}'] = 0.4
                    ramp_down[f'{tech}_{country}'] = 0.4
                    min_output[f'{tech}_{country}'] = 0.3
                    max_output[f'{tech}_{country}'] = 1.0
                    heat_rate[f'{tech}_{country}'] = 11.0
                    co2_emission_factor[f'{tech}_{country}'] = 0.1  # 生物质近零排放
                    annual_startup_limit[f'{tech}_{country}'] = 100
        
        # 初始在线容量（现有装机的一定比例）
        initial_online = {}
        for tech in thermal_techs:
            for country in self.countries:
                existing_cap = self.static_data.get('existing_capacity', {}).get(f'{tech}_{country}', 0)
                # 假设初始时50%的机组在线
                initial_online[f'{tech}_{country}'] = existing_cap * 0.5
        
        return {
            'min_up_time': min_up_time,
            'min_down_time': min_down_time,
            'ramp_up': ramp_up,
            'ramp_down': ramp_down,
            'min_output': min_output,
            'max_output': max_output,
            'heat_rate': heat_rate,
            'co2_emission_factor': co2_emission_factor,
            'annual_startup_limit': annual_startup_limit,
            'initial_online': initial_online,
            # 可选参数（如果有需要可以添加）
            'annual_fuel_limit': {},  # 年度燃料限制（如果需要）
            'annual_co2_limit': {},   # 年度CO2排放限制（如果需要）
            'monthly_hours_limit': {},  # 月度运行小时限制（如果需要）
            'maintenance_periods': {},  # 维护时段（如果需要）
        }
    
    def save_data_summary(self, filename: str = "data_summary.json"):
        """保存数据摘要"""
        summary = {
            'countries': len(self.countries),
            'time_periods': 8760,
            'technologies': {
                'thermal': ['coal', 'gas', 'oil', 'bio'],
                'renewable': ['onw', 'offw', 'solar'],
                'hydro': ['hydro_res', 'hydro_ror'],
                'other': ['geo', 'nuclear'],
                'storage': ['PSH', 'BAT']
            },
            'data_status': {
                'demand_loaded': 'demand' in self.time_series_data,
                'capacity_factors_loaded': 'capacity_factors' in self.time_series_data,
                'existing_capacity_loaded': 'existing_capacity' in self.static_data,
                'costs_loaded': 'investment_costs' in self.static_data,
                'potential_loaded': 'potential' in self.static_data
            }
        }
        
        with open(self.data_path / filename, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        logger.info(f"数据摘要已保存: {filename}")


def create_data_manager(data_path: str = "data") -> DataManager:
    """创建并初始化数据管理器"""
    dm = DataManager(data_path)
    
    # 加载所有数据
    dm.load_countries()
    dm.load_demand_data()
    dm.load_capacity_factors()
    dm.load_existing_capacity()
    dm.load_cost_data()
    dm.load_potential_data()
    
    # 保存数据摘要
    dm.save_data_summary()
    
    return dm


if __name__ == "__main__":
    # 测试数据管理器
    dm = create_data_manager()
    model_data = dm.get_model_data()
    print(f"数据加载完成，包含 {len(dm.countries)} 个国家的数据") 