#!/bin/bash
# 全球电力系统规划项目环境激活脚本
# 使用方法: source activate_env.sh

echo "正在激活全球电力系统规划项目虚拟环境..."
conda activate global_ps_planning

if [ "$CONDA_DEFAULT_ENV" = "global_ps_planning" ]; then
    echo "✅ 环境激活成功！当前环境: $CONDA_DEFAULT_ENV"
    echo ""
    echo "已安装的主要包："
    echo "- gurobipy: Gurobi优化器 (v12.0.2)"
    echo "- pandas: 数据处理 (v2.3.1)"
    echo "- numpy: 数值计算 (v2.0.2)"
    echo "- matplotlib: 数据可视化 (v3.9.4)"
    echo "- scipy: 科学计算 (v1.13.1)"
    echo ""
    echo "可以使用以下命令运行项目："
    echo "python main.py"
    echo ""
    echo "使用 'conda deactivate' 退出环境"
else
    echo "❌ 环境激活失败"
fi 