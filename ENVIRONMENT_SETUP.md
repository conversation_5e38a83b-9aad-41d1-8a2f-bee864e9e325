# 全球电力系统规划项目环境设置

## 🚀 虚拟环境创建完成！

我们已经为您的全球电力系统规划项目创建并配置了一个名为 `global_ps_planning` 的虚拟环境。

## 📦 已安装的软件包

### 核心优化器
- **gurobipy** (v12.0.2) - Gurobi优化器，用于求解MILP模型
  - 状态：✅ 已安装并配置（限制版许可证）

### 数据处理包
- **pandas** (v2.3.1) - 数据处理和分析
- **numpy** (v2.0.2) - 数值计算
- **scipy** (v1.13.1) - 科学计算

### 数据可视化
- **matplotlib** (v3.9.4) - 基础图表绘制
- **seaborn** (v0.13.2) - 统计图表

### 工具包
- **tqdm** (v4.67.1) - 进度条显示
- **PyYAML** (v6.0.2) - YAML配置文件处理
- **multiprocessing-logging** (v0.3.4) - 并行计算日志

## 🔧 使用方法

### 1. 激活环境

**方法一：使用conda命令**
```bash
conda activate global_ps_planning
```

**方法二：使用提供的脚本**
```bash
source activate_env.sh
```

### 2. 验证环境
```bash
python -c "import gurobipy; print('Gurobi版本:', gurobipy.gurobi.version())"
```

### 3. 运行项目
```bash
python main.py
```

### 4. 退出环境
```bash
conda deactivate
```

## 🔍 环境信息

- **Python版本**: 3.9.23
- **环境位置**: `/opt/anaconda3/envs/global_ps_planning`
- **平台**: macOS (ARM64)

## ⚡ Gurobi 许可证信息

当前使用的是Gurobi限制版许可证：
- 到期时间：2026年11月23日
- 限制：仅用于非生产环境
- 适用于：学术研究、开发和测试

如需完整版许可证，可以：
1. 申请学术许可证（如果您是学生/研究人员）
2. 购买商业许可证
3. 申请免费试用许可证

## 🧪 测试验证

所有主要组件已通过测试：
- ✅ 基础包导入正常
- ✅ Gurobi优化器工作正常
- ✅ 项目模块导入成功

## 📝 注意事项

1. 每次使用项目前需要激活虚拟环境
2. Gurobi限制版适用于开发和测试
3. 如遇到包版本冲突，可以使用 `pip install --upgrade <package>` 更新
4. 建议定期备份环境：`conda env export > environment.yml`

## 🆘 常见问题

**Q: 如何更新包？**
```bash
conda activate global_ps_planning
pip install --upgrade <package_name>
```

**Q: 如何重新创建环境？**
```bash
conda remove -n global_ps_planning --all
conda create -n global_ps_planning python=3.9 -y
conda activate global_ps_planning
pip install -r requirements.txt
```

**Q: Gurobi许可证过期怎么办？**
访问 [Gurobi官网](https://www.gurobi.com/downloads/) 获取新的许可证。

## 🎯 下一步

现在您可以开始运行全球电力系统规划模型了！

```bash
conda activate global_ps_planning
python main.py
``` 