# Citation Key: fujimoriEnergyTransformationCost2019

---

# SUPPLEMENATRY INFORMATION for _Energy Transformation Cost for the Japanese Mid-century Strategy_

Fujimori et al.


1


**1.** **Supplementary figures**


_Supplementary Figure 1. Population (a) and GDP MER (Market Exchange Rates) (b) assumptions._


2


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-2-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-2-1.png)

_Supplementary Figure 2. Primary energy (panels_ _**a**_ _and_ _**e**_ _), power generation (panels_ _**b**_ _and_ _**f**_ _), final_
_energy demand (panels_ _**c**_ _and_ _**g**_ _), CO_ _2_ _emissions (panel_ _**d**_ _), and carbon price (panel_ _**h**_ _) projections for no-_
_nuclear scenarios. Panels_ _**a**_ _,_ _**b**_ _, and_ _**c**_ _are baseline scenarios, whereas panels_ _**e**_ _,_ _**f**_ _, and_ _**g**_ _are mitigation_
_scenarios. The other in panel_ _**a**_ _and_ _**e**_ _includes secondary energy net imports._


3


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-3-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-3-1.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-3-2.png)

_Supplementary Figure 3. Primary energy (panels_ _**a**_ _and_ _**e**_ _), power generation (panels_ _**b**_ _and_ _**f**_ _), final_
_energy demand (panels_ _**c**_ _and_ _**g**_ _), CO_ _2_ _emissions (panel_ _**d**_ _), and carbon price (panel_ _**h**_ _) projections for no-_
_CCS scenarios. Panels_ _**a**_ _,_ _**b**_ _, and_ _**c**_ _are baseline scenarios, whereas panels_ _**e**_ _,_ _**f**_ _, and_ _**g**_ _are mitigation_
_scenarios. The other in panel_ _**a**_ _and_ _**e**_ _includes secondary energy net imports._


4


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-4-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-4-1.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-4-2.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-4-3.png)

_Supplementary Figure 4. The GDP loss rates associated with variation in the substitution elasticity_
_between energy and value-added in the stand-alone model_


5


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-5-1.png)








|(1)|Col2|
|---|---|
|||
|•<br>Power generation share by<br>energy sources<br>•<br>Battery capacity<br>•<br>CCS installation<br>•<br>Final energy consumption by<br>sectors and energy types<br>•<br>Investment of energy end-use<br>sectors<br>•<br>Carbon prices<br>•<br>Transmission losses|•<br>Power generation share by<br>energy sources<br>•<br>Battery capacity<br>•<br>CCS installation<br>•<br>Final energy consumption by<br>sectors and energy types<br>•<br>Investment of energy end-use<br>sectors<br>•<br>Carbon prices<br>•<br>Transmission losses|







|(2)|Col2|
|---|---|
|||
|• GDP changes<br>• Household consumption<br>changes<br>• Industry and service sectors<br>output<br>• Energy Prices|• GDP changes<br>• Household consumption<br>changes<br>• Industry and service sectors<br>output<br>• Energy Prices|


|(3)|Col2|
|---|---|
|||
|• Power capacity by technologies<br>• Electricity demand|• Power capacity by technologies<br>• Electricity demand|


(4)









_Supplementary Figure 5. Model integration strategy._


6


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-6-0.png)

_Supplementary Figure 6. Regional classification of AIM/Enduse, AIM/Power, and the power grid system_
_(Source: Shiraki et al. 2016_ _[1]_ _). For AIM/Power, since a version of the model that classifies Japan into 10_
_regions is used in this study, intraregional transmission lines were not considered._


7


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-7-0.png)



























_Supplementary Figure 7. Household expenditure structure._


8


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-8-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-8-1.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-8-2.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-8-3.png)

_Supplementary Figure 8. Climate mitigation policy costs associated with variations in the elasticity of_
_monetary outputs and physical energy service demand._


9


_Supplementary Figure 9. Main energy, emissions and economic indicators of the baseline scenario in 2030_
_by iteration. Each panel illustrates an individual variable, the codes and units of which are listed in_


10


_Supplementary Figure 10. Main energy, emissions and economic indicators of the mitigation scenario in_
_2030 by iteration. Each panel illustrates an individual variable, the codes and units of which are listed in ._


11


_Supplementary Figure 11. Main energy, emissions and economic indicators of the baseline scenario in_
_2050 by iteration. Each panel illustrates an individual variable, the codes and units of which are listed in ._


12


_Supplementary Figure 12. Main energy, emissions and economic indicators of the mitigation scenario in_
_2050 by iteration. Each panel illustrates an individual variable, the codes and units of which are listed in ._


13


_Supplementary Figure 13. Main energy, emissions and economic indicators of the mitigation scenario_
_without nuclear in 2030 by iteration. Each panel illustrates an individual variable, the codes and units of_
_which are listed in ._


14


_Supplementary Figure 14. Main energy, emissions and economic indicators of the mitigation scenario_
_without CCS in 2030 by iteration. Each panel illustrates an individual variable, the codes and units of_
_which are listed in ._


15


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-15-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-15-1.png)

_Supplementary Figure 15. Change ratios of industrial outputs or household consumption in mitigation_
_scenarios relative to the baseline scenario. Panels_ _**a**_ _,_ _**b**_ _,_ _**c**_ _, and_ _**d**_ _illustrate household, iron and steel, and_
_other manufacturing and service sectors, respectively._


16


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-16-0.png)

_Supplementary Figure 16. Evolution of oil-fired power generation. Left and right panels show the baseline_
_and mitigation cases._


17


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-17-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-17-1.png)

_Supplementary Figure 17. Evolution of residential and commercial final energy consumption and_
_hydropower generation. Left and right panels show the baseline and mitigation cases. Top and bottom_
_panels show residential and commercial final energy consumption and hydropower generation,_
_respectively._


18


_Supplementary Figure 18. Final energy consumption by energy carriers. Top and bottom panels show the_
_baseline and mitigation cases, respectively. From left to right, CGE stand-alone, integrated model (CGE_
_second run), and Enduse stand-alone simulation results are shown._


19


_Supplementary Figure 19. Primary energy supply by energy source. Top and bottom panels show the_
_baseline and mitigation cases, respectively. From left to right, CGE stand-alone, integrated model (CGE_
_second run), and Enduse stand-alone simulation results are shown._


20


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-20-0.png)

_Supplementary Figure 20. Power generation by energy source generated by AIM/Enduse and AIM/Power._
_Top and bottom panels show AIM/Enduse and AIM/Power results, respectively. From left to right, results_
_1 and 2 correspond to the results of each iteration. ‘Default’, ‘No CCS’, and ‘No nuclear’ represent the_
_scenario variations in technological availability._


21


**2.** **Supplementary tables**
_Supplementary Table 1. Regression results of mitigation costs in the IPCC AR5 database_ _[2]_ _._


Estimate Std. Error t value Pr(>|t|)


(Intercept) -73.791 7.594 -9.717 < 2e-16 ***


550ppm -1.238 0.217 -5.717 1.E-08 ***


Year 0.037 0.004 10.076 < 2e-16 ***


Asia 0.352 0.347 1.014 0.311


Latin America -1.527 0.356 -4.287 2.E-05 ***


Middle East and

0.293 0.355 0.825 0.410
Africa


OECD -1.368 0.346 -3.950 8.E-05 ***


Reforming regions 0.546 0.383 1.425 0.154


CGE 4.120 0.386 10.677 < 2e-16 ***


1130 degrees of freedom and adjusted R-squared: 0.199


22


_Supplementary Table 2. Literature review of CGE and energy system integration studies_


Top-down
Literature Region Integration method Bottom-up model
model



Sector
coverage of

energy
integration



Convergence
confirmation



Electricity
intermittency
representation



Soft-linking with Electricity price
Abrell and Rausch, 2016 [3] Europe Static CGE Electricity Electricity LDC
iterative procedure and quantity



Soft-linking with Energy system Energy price and
Andersen et al. 2019 [4] Denmark Static CGE All energy
iterative procedure model (TIMES) quantity



LDC (32 time
slices per
annual)



South Soft-linking with Static CGE Energy system Electricity price
Arndt et al. 2016 [5] Electricity No
Africa iterative procedure (SAGE) model (TIMES) and quantity


Static and
Boeringer et al. 2008 [6] Global Hard-linking Electricity Electricity - No
dynamic CGE

Energy service
Soft-linking with Static CGE Energy system
Fortes et al. 2014 [7] Portugal All energy demand No
iterative procedure (GEM-E3) model (TIMES)
differences



Soft-linking with Static CGE Energy system Commodity price
Helgesen et al. 2018 [8] Norway All energy
iterative procedure (REMES) model (TIMES) and sectoral output


Soft-linking with Electricity price
Hwang and Lee, 2015 [9] Korea Static CGE Electricity Electricity
iterative procedure and quantity


Krook-Riekkola et al. Soft-linking with Static CGE Energy system Energy
Sweden All energy
2017 [10] iterative procedure (EMEC) model (TIMES) consumption



LDC (260
time slices
per annual)


LDC (12 time
slices per
annual)


LDC (12 time
slices per
annual)



Recursive World Energy
Lanzi et al. 2012 [11] Global Parameter calibration All energy None No
dynamic CGE Outlook

Energy system
Soft-linking with
Laurent et al. 2004 [12] Switzerland Static CGE model Houshold Carbon price No
iterative procedure
(MARKAL)

Electricity
Sue Wing et al. 2008 [13] The US Hard-linking Static CGE Electricity - No
disaggregation


23


Tapia-Ahumada et al. Recursive
Global Soft-linking Electricity Electricity              - Hourly
2015 [14] dynamic CGE



Soft-linking with Energy price and
Tuladhar et al. 2009 [15] US Dynamic CGE Electricity Electricity
iterative procedure quantity



LDC (20 time
slices per
annual)



Recursive Energy system
Vandyck et al. 2016 [16] Global One-way Soft-linking All energy None No
dynamic CGE model (POLES)


Recursive Energy system
Waisman et al. 2012 [17] Global Recursive soft-linking All energy None No
dynamic CGE model (POLES)


24


_Supplementary Table 3. Additional annual investment of energy end-use sector in the mitigation scenario_
_relative to the baseline scenario computed by AIM/Enduse (unit is billion 2010US$). Negative values_
_indicate that investment in mitigation scenario is lower than baseline scenario due to energy service_
_demand changes and cross-sectoral effects generated by total system cost optimization._


2010 2015 2020 2025 2030 2035 2040 2045 2050


Industry -0.01 -0.01 -0.07 0.06 0.02 0.16 0.02 0.28 -0.26


Commercial 0.01 0 0.01 0.21 0.42 0.5 0.53 0.83 0.92


Residential 0 0 0 0.11 0.26 0.82 1.26 1.27 1.71


Transport 0 0 0.01 0.29 0.57 1.97 2.8 3.18 6.12


25


_Supplementary Table 4. Full list of diagnostic scenarios and their GDP loss rates in 2050. Column names_
_are sectors, and ‘on’ and ‘off’ refer to whether AIM/Enduse information is incorporated. The red and blue_
_rows indicate the stand-alone and integrated models, respectively. Yellow and green rows indicate_
_scenarios that include and exclude information from a single sector given by AIM/Enduse, respectively._
_Uncoloured rows do not appear in Table1._



Energy


Supply



Industry Service Transport Residential



GDP loss rate (%)


2030 2050



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-25-0.png)

scenario 4 off off off on on 1.0 2.3


scenario 5 off off on off off 0.6 1.7


scenario 6 off off on off on 0.5 1.5


scenario 7 off off on on off 0.7 1.6


scenario 8 off off on on on 0.6 1.5


scenario 9 off on off off off 0.4 0.8


scenario 10 off on off off on 0.3 0.6


scenario 11 off on off on off 0.5 0.7


scenario 12 off on off on on 0.4 0.6


scenario 13 off on on off off 0.1 0.4


scenario 14 off on on off on 0.0 0.3


scenario 15 off on on on off 0.2 0.3


scenario 18 on off off off on 0.9 2.3


scenario 19 on off off on off 1.1 2.4


scenario 20 on off off on on 0.9 2.5


scenario 21 on off on off off 0.5 2.0


scenario 22 on off on off on 0.4 1.8


scenario 23 on off on on off 0.7 2.3


scenario 24 on off on on on 0.5 2.2


scenario 25 on on off off off 0.4 1.2


scenario 26 on on off off on 0.3 1.0


scenario 27 on on off on off 0.6 1.3


scenario 28 on on off on on 0.4 1.2


scenario 29 on on on off off 0.0 0.8

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-25-2.png)


26


_Supplementary Table 5. List of production sectors in AIM/CGE_


Classification Sectors ISIC rev3 code


Coal mining 101, 102


111, 112 (related to oil extraction),
Oil mining
103


Gas mining 111, 112 (related to gas extraction)


Petroleum refinery
231, 232, 233
Coal transformation


Biomass transformation (1st generation)               

Biomass transformation (2nd generation
with energy crop)               

Biomass transformation (2nd generation
with residue)                

Gas manufacture distribution 402, 403


Coal-fired power



Energy supply
(transformation) sectors



Oil-fired power


Gas-fired power


Nuclear power


Hydroelectric power


Geothermal power


Photovoltaic power


Wind power


Waste biomass power


Other renewable energy power generation


Advanced biomass-power generation



401



Hydrogen production by gas 

Hydrogen production by biomass 

27


_Supplementary Table 6 List of production sectors in AIM/CGE_


Classification Sectors ISIC rev3 code


Rice


Wheat


Other grains

01

Oil seed crops


Sugar crops



Agricultural sectors
(Energy end-use sectors)


Other production sectors
(Energy end-use sectors)



Other crops


Ruminant livestock


05
Raw milk


Other livestock and fishery


Forestry 02


Mineral mining and Other quarrying 12, 13, 14


Food products 15, 16


Textiles and Apparel and Leather, and Wood
17, 18, 19, 243, 20
products


Paper, Paper products and Pulp 21, 2211, 2212, 2213, 2219, 222, 223


Chemical, Plastic and Rubber products 241, 242, 25


Mineral products nec 26


Iron and Steel 271, 2731


Non-Ferrous products 272, 2732


Other Manufacturing 28, 29, 31, 33, 30, 32, 34, 35, 36, 37


Construction 45


Transport and communications 60, 61, 62, 63, 64


41, 50,51,52,55, 65, 66, 67, 70, 71,
Other service sectors 72, 73, 74, 75, 80, 85, 90, 91, 92, 93,
94, 95, 99


CCS service 

28


_Supplementary Table 7. List of technologies in AIM/Enduse adopted from Oshiro et al. (2015)_ _[18]_


**Sector** **Technology option**


High performance pulp washing device, High efficient black liquid boiler, Next



Industrial sector


Residential and


Commercial


sector


Transport sector


Power


generation sector



generation coke oven, CCS for steel/cement production, DC electric furnace,


Naphtha catalytic cracker, High efficient industrial boiler, Industrial heat pump, High


efficient motor, High efficient agricultural device


High efficient air conditioner, High efficient water heater (e.g. Heat pump water


heater), Electric heat pump water heater, SOFC, High efficient lighting, High


efficient appliance, High performance building envelope, Building energy


management system


High efficient passenger vehicle, Hybrid vehicle, Plug-in hybrid electric vehicle,


Battery electric vehicle (BEV), Fuel-cell electric vehicle (FCEV), CNG vehicle,


Biofuel, High efficient train, High efficient ship, High efficient aircraft, Eco driving


IGCC w/CCS, IGCC wo/CCS, IGFC w/CCS, IGFC wo/CCS, Advanced gas


combined cycle (ACC) w/CCS, ACC wo/CCS, Fuel cell gas combined cycle w/ or


wo/CCS, Nuclear, Onshore wind power, Offshore wind power, Solar PV,


Geothermal, Bioenergy, Hydropower, Pumped hydro, Reinforcing electricity


interconnection capacity, Hydrogen generation by electrolysis



Reduced fertilization, HFCs leakage reduction and recovery, Reduced municipal
Other sectors

solid waste


29


_Supplementary Table 8. List of technoeconomic information of power sector in AIM./Enduse._

|Variable|Unit|2010|2015|2020|2025|2030|2035|2040|2045|2050|
|---|---|---|---|---|---|---|---|---|---|---|
|Capital Cost|Biomass|w/ CCS|US$2010/kW|7540|8063|8063|8063|8063|8063|8063|8063|8063|
|Capital Cost|Biomass|w/o CCS|US$2010/kW|3813|4336|4336|4336|4336|4336|4336|4336|4336|
|Capital Cost|Coal|w/ CCS|US$2010/kW|4338|4338|4338|4338|4338|4338|4338|4338|4338|
|Capital Cost|Coal|w/o CCS|US$2010/kW|2704|2704|2704|2704|2704|2704|2704|2704|2704|
|Capital Cost|Gas|w/ CCS|US$2010/kW|2174|2174|2174|2174|2174|2174|2174|2174|2174|
|Capital Cost|Gas|w/o CCS|US$2010/kW|1122|1122|1122|1122|1122|1122|1122|1122|1122|
|Capital Cost|Geothermal|US$2010/kW|8716|8607|8607|8607|8607|8607|8607|8607|8607|
|Capital Cost|Hydro|US$2010/kW|8716|8716|8716|8716|8716|8716|8716|8716|8716|
|Capital Cost|Nuclear|US$2010/kW|4506|4721|4721|4721|4721|4721|4721|4721|4721|
|Capital Cost|Solar|PV|US$2010/kW|5704|4031|3035|2588|2141|2065|1990|1914|1838|
|Capital Cost|Wind|Offshore|US$2010/kW|5443|5704|5449|5194|4940|4797|4655|4513|4370|
|Capital Cost|Wind|Onshore|US$2010/kW|3046|3145|2918|2725|2531|2531|2531|2490|2449|
|Lifetime|Biomass|w/ CCS|years|40|40|40|40|40|40|40|40|40|
|Lifetime|Biomass|w/o CCS|years|40|40|40|40|40|40|40|40|40|
|Lifetime|Coal|w/ CCS|years|40|40|40|40|40|40|40|40|40|
|Lifetime|Coal|w/o CCS|years|40|40|40|40|40|40|40|40|40|
|Lifetime|Gas|w/ CCS|years|40|40|40|40|40|40|40|40|40|
|Lifetime|Gas|w/o CCS|years|40|40|40|40|40|40|40|40|40|
|Lifetime|Geothermal|years|40|40|40|40|40|40|40|40|40|
|Lifetime|Hydro|years|80|80|80|80|80|80|80|80|80|
|Lifetime|Nuclear|years|40|40|40|40|40|40|40|40|40|
|Lifetime|Solar|PV|years|15|15|15|15|15|15|15|15|15|
|Lifetime|Wind|Offshore|years|15|15|15|15|15|15|15|15|15|
|Lifetime|Wind|Onshore|years|15|15|15|15|15|15|15|15|15|
|OM Cost|Fixed|Biomass|w/ CCS|US$2010/kW/yr|982|982|982|982|982|982|982|982|982|
|OM Cost|Fixed|Biomass|w/o CCS|US$2010/kW/yr|290|290|290|290|290|290|290|290|290|
|OM Cost|Fixed|Coal|w/ CCS|US$2010/kW/yr|303|303|303|303|303|303|303|303|303|
|OM Cost|Fixed|Coal|w/o CCS|US$2010/kW/yr|106|106|106|106|106|106|106|106|106|
|OM Cost|Fixed|Gas|w/ CCS|US$2010/kW/yr|159|159|159|159|159|159|159|159|159|
|OM Cost|Fixed|Gas|w/o CCS|US$2010/kW/yr|34|34|34|34|34|34|34|34|34|
|OM Cost|Fixed|Geothermal|US$2010/kW/yr|354|354|354|354|354|354|354|354|354|
|OM Cost|Fixed|Hydro|US$2010/kW/yr|379|379|379|379|379|379|379|379|379|
|OM Cost|Fixed|Nuclear|US$2010/kW/yr|200|200|200|200|200|200|200|200|200|
|OM Cost|Fixed|Solar|PV|US$2010/kW/yr|39|39|39|39|39|39|39|39|39|
|OM Cost|Fixed|Wind|Offshore|US$2010/kW/yr|241|241|241|241|241|241|241|241|241|
|OM Cost|Fixed|Wind|Onshore|US$2010/kW/yr|64|64|64|64|64|64|64|64|64|



30


_Supplementary Table 9. Assumed power plant capacity by construction year (Unit is GW)_


Construction year -1970 1971 1976 1981 1986 1991 1996 2001 2006
-75 -80 -85 -90 -95 -00 -05 -10


Coal|w/o CCS 0.2 4.4 2.5 4.1 4.1 8.3 10.1 10.7 2.8


Gas|w/o CCS 0.6 14.5 9.0 6.7 7.9 4.5 12.9 4.3 9.6


Oil|w/o CCS 12.8 15.0 7.8 8.1 3.3 2.3 3.4 2.3 1.5


Hydro 16.1 1.0 1.0 1.1 0.6 0.4 0.2 0.2 0.1


Nuclear 12.1 5.7 3.4 6.5 11.1 4.7 2.2 3.2


Biomass|w/o CCS 0.2


Geothermal 0.0 0.0 0.0 0.1 0.1 0.2 0.1 0.0


Solar|PV 0.0 0.2 0.9 1.9


Wind|Onshore 0.0 0.0 0.1 0.9 1.4


31


_Supplementary Table 10. Assumptions on fuel prices_


Variable Unit 2010 2020 2030 2040 2050


Price|Coal US$2010/GJ 4.1 3.9 3.8 3.4 3.3


Price|Gas US$2010/GJ 9.9 9.3 11.2 11.3 11.4


Price|Oil US$2010/GJ 12.8 14.9 17.3 15.9 14.7


32


_Supplementary Table 11. Mapping of household consumption goods and general goods categories_


Household consumption goods category General goods category


Petroleum products



Car related consumption


Non-car related energy


Cereal



Electricity


Biofuel


Other Manufacturing


Coal


Oil


Gas


Petroleum products


Coal products


Biofuel


Town gas


Electricity


Hydrogen


Other Manufacturing


Rice


Wheat


Other grains



Oil seed crops Oil seed crops


Sugar crops Sugar crops


Other crops Other crops


Ruminant livestock Ruminant livestock


Raw milk Raw milk


Other livestock and fishery Other livestock and fishery


Forestry Forestry


Mineral mining and Other quarrying Mineral mining and Other quarrying


Food products Food products


Textiles and Apparel and Leather Textiles and Apparel and Leather


Wood products Wood products


Paper, Paper products and Pulp Paper, Paper products and Pulp


Chemical, Plastic and Rubber products Chemical, Plastic and Rubber products


Iron and Steel Iron and Steel


Non-Ferrous products Non-Ferrous products


Construction Construction


Transport and communications Transport and communications


Other services Other services


33


_Supplementary Table 12. Error rates (%) between AIM/CGE and AIM/Enduse models. CGE1, CGE2,_


_CGE3, End1, and End2 correspond to the results shown in Supplementary Figure 5 results for each model_


_output. For example, CGE_results1 and Enduse results1 are CGE1 and End1 respectively._

|Col1|Baseline|Col3|Col4|Mitigation|Col6|Col7|
|---|---|---|---|---|---|---|
||End1_CGE1|End1_CGE2|End2_CGE3|End1_CGE1|End1_CGE2|End2_CGE3|
|Final Energy (EJ/yr)|7%|1%|1%|4%|1%|1%|
|Primary Energy (EJ/yr)|8%|1%|1%|13%|1%|1%|
|Primary Energy|Coal (EJ/yr)|11%|2%|2%|26%|6%|4%|
|Primary Energy|Fossil Fuel (EJ/yr)|4%|1%|1%|11%|2%|2%|
|Primary Energy|Gas (EJ/yr)|13%|1%|1%|15%|2%|2%|
|Primary Energy|Nuclear (EJ/yr)|34%|1%|1%|34%|1%|1%|
|Primary Energy|Oil (EJ/yr)|15%|2%|2%|6%|2%|2%|
|Secondary Energy|Electricity (EJ/yr)|20%|2%|2%|25%|1%|1%|
|Secondary Energy|Electricity|Coal (EJ/yr)|11%|1%|1%|43%|5%|1%|
|Secondary Energy|Electricity|Gas (EJ/yr)|21%|2%|2%|18%|1%|1%|
|Secondary Energy|Electricity|Non-Biomass<br>Renewables (EJ/yr)|0%|0%|0%|0%|0%|0%|
|Secondary Energy|Electricity|Nuclear (EJ/yr)|35%|1%|1%|35%|35%|1%|
|Secondary Energy|Electricity|Oil (EJ/yr)|75%|15%|15%|15%|15%|15%|
|Secondary Energy|Liquids (EJ/yr)|13%|0%|0%|3%|3%|4%|
|Final Energy|Electricity (EJ/yr)|19%|0%|0%|25%|0%|0%|
|Final Energy|Gases (EJ/yr)|5%|4%|4%|20%|3%|4%|
|Final Energy|Industry (EJ/yr)|4%|2%|2%|10%|2%|2%|
|Final Energy|Liquids (EJ/yr)|7%|2%|2%|9%|2%|2%|
|Final Energy|Residential and Commercial<br>(EJ/yr)|10%|0%|0%|8%|1%|1%|
|Final Energy|Solids (EJ/yr)|32%|2%|2%|58%|2%|2%|
|Final Energy|Transportation (EJ/yr)|13%|1%|1%|9%|1%|1%|
|Primary Energy|Hydro (EJ/yr)|29%|1%|1%|25%|7%|1%|
|Primary Energy|Solar (EJ/yr)|57%|1%|1%|13%|1%|1%|
|Primary Energy|Wind (EJ/yr)|86%|1%|1%|22%|1%|1%|
|Secondary Energy (EJ/yr)|8%|2%|2%|6%|4%|4%|
|Final Energy|Residential (EJ/yr)|9%|0%|0%|10%|1%|1%|
|Final Energy|Commercial (EJ/yr)|11%|0%|0%|7%|0%|0%|
|Emissions|CO2|Energy and Industrial<br>Processes (Mt CO2/yr)|4%|1%|1%|10%|2%|1%|



34


_Supplementary Table 13. Variable code and unit list_


Code Variable Unit


Fin_Ene Final Energy EJ/yr


Prc_Car Price|Carbon US$2005/t CO2


Prm_Ene Primary Energy EJ/yr


Prm_Ene_Coa Primary Energy|Coal EJ/yr


Prm_Ene_Fos Primary Energy|Fossil Fuel EJ/yr


Prm_Ene_Gas Primary Energy|Gas EJ/yr


Prm_Ene_Nuc Primary Energy|Nuclear EJ/yr


Prm_Ene_Oil Primary Energy|Oil EJ/yr


Sec_Ene_Ele Secondary Energy|Electricity EJ/yr


Sec_Ene_Ele_Bio Secondary Energy|Electricity|Biomass EJ/yr


Sec_Ene_Ele_Coa Secondary Energy|Electricity|Coal EJ/yr


Sec_Ene_Ele_Gas Secondary Energy|Electricity|Gas EJ/yr


Sec_Ene_Ele_Nuc Secondary Energy|Electricity|Nuclear EJ/yr


Sec_Ene_Ele_Oil Secondary Energy|Electricity|Oil EJ/yr


Sec_Ene_Liq Secondary Energy|Liquids EJ/yr


Fin_Ene_Ele Final Energy|Electricity EJ/yr


Fin_Ene_Gas Final Energy|Gases EJ/yr


Fin_Ene_Ind Final Energy|Industry EJ/yr


Fin_Ene_Liq Final Energy|Liquids EJ/yr


Fin_Ene_Res_and_Com Final Energy|Residential and Commercial EJ/yr


Fin_Ene_Solids Final Energy|Solids EJ/yr


Fin_Ene_Tra Final Energy|Transportation EJ/yr


Prm_Ene_Hyd Primary Energy|Hydro EJ/yr


Prm_Ene_Solar Primary Energy|Solar EJ/yr


Prm_Ene_Win Primary Energy|Wind EJ/yr


Sec_Ene Secondary Energy EJ/yr


Fin_Ene_Res Final Energy|Residential EJ/yr


Fin_Ene_Com Final Energy|Commercial EJ/yr


Emi_CO2_Ene Emissions|CO2|Energy Mt CO2/yr


35


**3.** **Supplementary notes**
**Supplementary Note 1. Climate mitigation costs in the IPCC AR5 database**
We investigated the IPCC AR5 database to determine whether climate mitigation costs in multi-sector
CGE models are higher than those in other types of models. The AR5 database includes various scenarios
that cannot be dealt with equally. For example, the scenarios developed under EMF27 (Energy Modeling
Forum) include assumptions about technological variations. Climate targets also differ among scenarios.
Therefore, we first selected scenarios that do not have any restrictions on technological availability from
EMF27. Then, the climate targets were classified into two categories equivalent to 450 and 550 ppm CO 2
at the end of this century. We then applied simple regression analysis, as follows:


𝑀𝐶𝑅 � � 𝑏 � 𝑌 � � 𝑒 �
�
��,��∈��


where _MCR_ _s_ is the climate mitigation cost (%) for scenario _s_ ; _b_ _l_ is a dummy parameter representing a set of
model classifications (multi-sector CGE or not), years, regions (five regions, Asia, Latin America, Middle
East-Africa, OECD, and Reforming region + global), and climate targets (450 or 550ppm); _Y_ _l_ is an
estimated variable; and _e_ _s_ is an error term. Here, we take climate mitigation cost to represent GDP losses as
a standard metric, but for models that do not include GDP losses, additional energy system costs or area
under MAC (marginal abatement cost) are used. The regression results show that the CGE model is a
positive factor with a statistically significant _t_ -value.


**Supplementary Note 2. Mathematical formula of CGE model**
In this section, we describe the mathematical formulas in the AIM/CGE model, which is particularly
relevant to energy consumption and power generation. We use different production functions between
energy end-use and power generation sectors and they are described as below.


**1)** **Energy end-use sectors other than household sector in CGE model**
We begin with the representation of the energy end-use sectors (Supplementary Table 5). As many of
other CGE models, AIM/CGE assumes KL-E type multi-nested CES production function [19] . The valueadded and energy composite inputs are determined by multiplying a coefficient by the output from the
energy end-use sectors (Equation 1) that is calibrated. Then, value-added and energy composite are
combined with the CES function (Equation 2). Labour and capital inputs are further nested in the CES
function, as shown in Equation 3.

𝑄𝑉𝐴𝐸 �� �𝑖𝑣𝑎𝑒 � ∙𝑄𝐴 �� (1)



𝑄𝑉𝐴𝐸 �� �𝛼𝑎𝑒 � �𝛽𝑎𝑒 � ∙𝑄𝑉𝐴 �� [����] [�] ��1 �𝛽𝑎𝑒 � �∙�𝑎𝑡 � ∙𝑄𝐸𝑁𝐸 �� � ���� � �



�
����� (2)



�

𝑄𝑉𝐴 �� �𝑡𝑓𝑝∙𝛼𝑓 � �𝛽𝑓 � ∙𝑄𝐹 "�������",�� ���� � ��1 �𝛽𝑓 � �∙𝑄𝐹 "�����",�� ��� � � ���� (3)

where

𝑎∈𝐴 is a set of production activities,
�
𝑄𝐴 � is an output of sector _a_ in the baseline scenarios (monetary unit),
�
𝑄𝑉𝐴𝐸 � is the composite of value-added and energy of sector _a_ in the baseline scenarios (monetary unit),
�
𝑄𝑉𝐴 � is value-added of sector _a_ in baseline scenarios (monetary unit),
𝑄𝐸𝑁𝐸 �� is the total energy inputs including any energy carriers (e.g. gas, liquids, and electricity) of sector
_a_ in baseline scenarios (physical unit),
�
𝑄𝐹 �,� is the primary factor inputs of factor _f_ and sector _a_ in baseline scenarios ( _f_ = capital or labor)
(monetary unit),
𝑖𝑣𝑎𝑒 � is an input coefficient of the output of sector _a_,
𝛼𝑎𝑒 � is the scale parameter of the CES function for value-added and energy aggregates,
𝛽𝑎𝑒 � is the share parameter of the CES function for value-added and energy aggregates,
𝜌𝑎𝑒 � is the exponent parameter of the CES function for value-added and energy aggregates,
𝛼𝑓 � is the scale parameter of the CES function for primary factor aggregates,
𝛽𝑓 � is the share parameter of the CES function for primary factor aggregates,


36


𝜌𝑓 � is the exponent parameter of the CES function for primary factor aggregates,
_at_ _a_ is an autonomous energy efficiency improvement parameter, and
_tfp_ is a parameter that represents the economy wide total factor productivity that is calibrated in the
baseline scenarios by hitting the target GDP and the calibrated values are adopted in the mitigation
scenarios.
The energy carriers shares in the energy end-use sectors are calculated by using McFadden’s (1981) [20]
logit share equation. The concept underlying the logit is that the decision makers determine the share of
each element under a certain probability distribution function. This function form is applied in some other
IAMs (GCAM [21] and IMAGE [22] ). For further theoretical discussion of the logit sharing mechanism, see
Clarke and Edmonds (1993) [23] _._ _QENE_ shown above is the sum of the individual energy carriers determined
by this equation.



𝑆𝐻𝐸𝑁𝐸 � �� � ∙� ���,� ∙�� �,��� [��]
�,�



∑ ��∈���� �� �� ∙� ��,��� ∙�� ��,��� [��]



𝑐∈𝐶𝐸𝑁𝐸, 𝑎∈𝐴𝐸𝑛𝑑 (5)



��∈����



Where

𝑎∈𝐴𝐸𝑛𝑑 is energy end-use production activities and a subset of the production activities (e.g. industry,
transport and so on),
𝑐∈𝐶𝐸𝑁𝐸 is set of energy commodities (coal, petroleum products and so on), which is a subset of all
commodities,
𝑆𝐻𝐸𝑁𝐸 �,� is the energy consumption share of energy commodity _c_ in production activity _a_,
𝑃𝑄 �,� is the price of commodity _c_ in production activity _a_ (monetary/physical unit),
𝛿 �,��� and 𝛽 �� are parameters for logit selection, and
𝑎𝑒 � is the fuel-wise energy preference change parameter of commodity _c_ .

For the stand-alone model, 𝛽𝑎𝑒 �� and 𝛿 �,� are calibrated by base year information. Autonomous Energy
Efficiency Improvement (AEEI) in the stand-alone model _at_ _a_ is one of the critical parameters determining
energy consumption. We adopted a uniform AEEI across energy end-use sectors for each year, which is
associated with GDP growth. For the years that assumes more than 1% of GDP annual growth, AEEI
change rate is 1%, and half of the annual GDP growth rates is assumed for the other years. The fuel-wise
energy preference change parameter _ae_ _c_ is set annually to 1%, 0.5% and -0.5% for electricity, gas and coal
respectively, which represent fuel shift from conventional solid and liquids to gas and electricity. They are
arbitrarily assumed and their validity should be interpreted within the baseline scenario’s perspective.
With respect to the integrated model, the 𝑆𝐻𝐸𝑁𝐸 �,� and 𝑄𝐸𝑁𝐸 �� are fixed based on the AIM/Enduse
model results and endogenise 𝛿 �,��� and _at_ _a_ .
Then, we can derive the input coefficients of capital in the baseline scenarios after obtaining the
simulation results of the baseline scenarios as shown in Equation (6). For the mitigation scenarios, the
derived input coefficients of the primary factors shown above are used to estimate primary factor inputs.
For the capital inputs of the energy end-use sector, additional investment costs (associated with mitigation
scenarios) relative to baseline scenarios, which are given by the AIM/Enduse model, have been added to
the baseline inputs, as shown in Equation (7).
𝑖𝑓𝑎 �,�� �𝑄𝐹 �,�� /𝑄𝐴 �� 𝑓∈𝐹𝑐𝑎𝑝 (6)
𝑄𝐹 �,�� �𝑖𝑓𝑎 �,�� ∗𝑄𝐴 �� �𝐴𝑑𝑑𝐼𝑛𝑣 �,� 𝑓∈𝐹𝑐𝑎𝑝 (7)
where
𝑓∈𝐹𝑐𝑎𝑝 is a set of capital and a subset of primary factors
�
𝑖𝑓𝑎 � is an input coefficient of primary factor _f_ and sector _a_ in the baseline scenarios.
𝐴𝑑𝑑𝐼𝑛𝑣 �,� is the additional investment cost associated with mitigation costs provided by AIM/Enduse,
�
𝑄𝐹 �,� is the primary factor input of factor _f_ and sector _a_ in the mitigation scenarios ( _f_ = capital or labour)
(monetary unit), and
�
𝑄𝐴 � is an output of sector _a_ in the mitigation scenarios (monetary unit).

**2)** **Power generation in CGE model**
The total consumption of electricity is determined by the demand side representation shown in the
previous subsection ( _QENE_ and _SHEHE_, and the latter for household). Then, the power generation is
determined based on logit sharing which has already been mentioned above, as below.


37


𝑆𝐻𝐴𝐶 � � ���,�� ∙���� �,��� [��]
�,�



∑ ��∈���� � �,���� ∙���� �,���� [��]



𝑐∈𝐶𝐸𝑙𝑦, 𝑎∈𝐴𝐸𝑙𝑦 (8)



��∈����



where 𝑎∈𝐴𝐸𝑙𝑦 is a subset of production activity and electricity production activity (e.g. coal, solar PV
and so on),
𝑐∈𝐶𝐸𝑙𝑦 is a subset of commodity and electricity,
𝑆𝐻𝐴𝐶 �,� is the electricity generation share of production activity _a_,
𝑃𝑋𝐴𝐶 �,� is the price of commodity _c_ produced by production activity _a_ (monetary/physical unit),
𝛿 �,��� and 𝛽 �� are parameters for the logit selection of power general technologies.

For the stand-alone model, 𝛿 �,��� is calibrated using base year information. The integrated model fixes
the 𝑆𝐻𝐴𝐶 �,� as the AIM/Enduse model results and endogenises 𝛿 �,��� . This treatment is the same between
the baseline and the mitigation scenarios. As the absolute amount of power generation is determined by the
demand side, here we specify only the share. The transmission losses are also considered. Battery capacity
is input as an absolute amount.

**3)** **Household consumption in CGE model**
Household consumption is formulated using a LES function and further nested in a CES for energy
and other manufacturing goods. The function is derived from a function originally defining how spending
on individual commodities has a linear relation with total consumption spending, based on the assumption
that each household maximises a Stone–Geary utility function subject to consumption expenditure
constraints. The parameters of the LES function other than food were calibrated based on income elasticity
values (Nganou, 2005) [24] . The income elasticity of food demand for each region and commodity was from
Bruinsma (2010) [25] .
Then, total energy consumption is finally split out into fuel-wise consumption using a logit function


��
𝑄𝐶𝐻 �� �𝜇 �� �𝜃 �� ∙� ��� �� [�∑] ���∈�� 𝜇 ��� �  𝑐ℎ∈𝐶𝐻 (9)



𝑄𝐶𝐻 �� �𝛼ℎ �� �� ∙𝑄𝐻𝐸 ����� �� ��1 �𝛽ℎ �� �∙𝑄𝐻𝑀 ����� ��
�𝛽ℎ �



�

�����
(10)



� �� [�]
𝑆𝐻𝐻𝐸𝑁𝐸 � �� � ∙� �,�� ∙�� �,�
�,��



� �� [�]
∑ ��∈���� �� �� ∙� ��,�� ∙�� ��,�



𝑐∈𝐶𝐸𝑁𝐸, 𝑐ℎ∈𝐶𝐻𝐸 (11)



��∈����



𝑄𝐻 � �



∑ ��∈��� 𝑆𝐻𝐻𝐸𝑁𝐸 �,�� ∙𝑄𝐻𝐸 �� 𝑐∈𝐶𝐸𝑁𝐸

∑ ��,���∈������ 𝑄𝐶𝐻 �� 𝑐∈𝐶𝑁𝐸𝑁𝐸

∑ ��,���∈������ 𝑄𝐻𝑀 �� 𝑐∈𝐶𝑂𝑀𝐹



(12)



where
𝑐ℎ∈𝐶𝐻 is a set of household consumption goods of which mappings with goods _c_ are shown in
Supplementary Table 11
𝑐ℎ∈𝐶𝐻𝐸 is a set of energy related household consumption goods (car usage and other energy related
consumption)
�𝑐, 𝑐ℎ�∈𝑚𝑎𝑝𝐶𝐶𝐻 is a mapping from household consumption goods _ch_ to general goods _c_ . shown in
Supplementary Table 11,
𝑃𝐶𝐻 �� are the quantity and price of household consumption goods _ch_ (relative ratio to base year),
𝑄𝐶𝐻 �� are the quantity and price of household consumption goods _ch_ (monetary unit but energy is
accounted as physical unit),
𝑆𝐻𝐻𝐸𝑁𝐸 �,�� is the share of energy fuel c of household consumption goods _ch_,
𝑄𝐻𝐸 �� and 𝑄𝐻𝑀 �� are the quantity of energy and other manufacturing goods, respectively, for household
consumption goods _ch_ (car usage and other energy related consumption; physical unit),
𝜃 �� and 𝜇 �� are LES function parameters which are calibrated recursively,
𝛼ℎ �� is the scale parameter of the CES function for energy and other manufacturing goods aggregates,
𝛽ℎ �� is the share parameter of the CES function for energy and other manufacturing goods aggregates,
𝜌ℎ �� is the exponent parameter of the CES function for energy and other manufacturing goods aggregates,
𝛿 �,��� and 𝛽 [�] are parameters for logit selection of household energy fuel.


38


Household LES function parameters are updated recursively based on income elasticity. Electricity
and biofuel used in transport, are not accounted in the base year social accounting matrix and, thus, we
introduce the initial parameters for 𝛿 �,��� and 𝛿 �,��� by calibrating 0.1% of the share in each energy
consumption in 2015 and 2020. They are then updated afterwards to one third of the value of petroleum
products in 40 years. They are same as used in the SSPs quantification. For the integrated model, the
parameters 𝛽ℎ �� and 𝜌ℎ �� were determined endogenously based on the energy consumption and
investment needs for other manufacturing goods computed by AIM/Enduse.

**Supplementary Note 3. Consistency check across models**
We confirm how the outputs of two models change over iterations and reach convergence.
Supplementary Table 12 shows the degree of coincidence for each pair of AIM/Enduse and AIM/CGE
datasets. An indicator shown below is adopted as the indicator. i, t and s are sets of variables of model (e.g.
energy demand), years and scenarios respectively. _X_ _i,t,s_ and _Y_ _i,t,s_ are AIM/CGE and AIM/Enduse outputs
respectively.



�
∑ � �𝑋 �,�,� �𝑌 �,�,� �
𝐸𝑟𝑟𝐼 �
�,�
~~�~~ �∑� � [𝑋] [�][,][�][,][�] [�𝑌] 2 [�][,][�][,][�] ~~�~~ �



�



�∑� � [𝑋] [�][,][�][,][�] [�𝑌] 2 [�][,][�][,][�] ~~�~~ �



CGE results drastically changed from the standalone version to the coupled version, and the
discrepancy with AIM/Enduse falls substantially (End1_CGE1 to End1_CGE2 in Supplementary Figure
5). The differences in these improvements are nearly stable in the second iteration run, wherein
AIM/Enduse incorporated energy service demand changes given by AIM/CGE and AIM/CGE further
input revised AIM/Enduse data (End1_CGE2 to End2_CGE3 in Supplementary Figure 5). We run five
iterations and the results indicate that the second iteration sufficiently converges the variables (see
Supplementary Figure 9 to Supplementary Figure 14). This result may be due to energy service demand
changes produced by AIM/CGE, which fed into AIM/Enduse, being small in the second and third
iterations. Here, we can estimate the value-added and household consumption changes (differences
between CGE2 and CGE3 in Supplementary Figure 5). Consequently, the third CGE run showed few
changes from the second run.
Overall, the errors are less than 20%, but there are some exceptions. Final energy in the residential
and service sectors and electricity generation from oil and hydropower in the baseline scenarios exhibit
relatively high discrepancies, which show relatively small improvements in the third iteration. Therefore,
we classified two types of errors. First, some years have very small values in one or both models, causing
the error rates of these two models to be large in some years. Oil-fired power generation is one such
example, as shown in Supplementary Figure 16. The second type of error is mainly due to discrepancies in
the base year information between the two models. The residential and service sectors’ final energy
consumption and hydropower explain the cause of these differences, as shown in Supplementary Figure
17. As indicated in the Methods section, we incorporated the change ratios from AIM/Enduse into
AIM/CGE relative to the base year for final energy consumption, and thus trends after the base year for
AIM/Enduse, which is 2010, are quite similar in the second AIM/CGE run. We further provide figures
comparing energy-related data, Supplementary Figure 18 and Supplementary Figure 19, to explore the
future evolution of energy systems.
Regarding the consistency of the AIM/Power and AIM/Enduse models, power generation appears to
be almost the same at the end of the run (Supplementary Figure 20). Caution is still required, as other
indicators such as capacity factor, may play a role here. That possibility is not necessarily true for the
detailed technological contributions, but would hardly affect the overall trend because the main differences
affect tiny power generation sources (e.g. the coal power capacity factor in mitigation scenarios).


39


**4.** **Supplementary references**

1. Shiraki H, Ashina S, Kameyama Y, Hashimoto S, Fujita T. Analysis of optimal locations for
power stations and their impact on industrial symbiosis planning under transition toward lowcarbon power sector in Japan. _Journal of Cleaner Production_ 2016, **114:** 81-94.

2. International Institute for Applied Systems Analysis, (IIASA). IAMC AR5 scenario database.

                                        2015 [cited]Available from: https://secure.iiasa.ac.at/web
apps/ene/AR5DB/dsd?Action=htmlpage&page=about#intro

3. Abrell J, Rausch S. Cross-country electricity trade, renewable energy and European
transmission infrastructure policy. _Journal of Environmental Economics and Management_
2016, **79:** 87-113.

4. Andersen KS, Termansen LB, Gargiulo M, Ó Gallachóirc BP. Bridging the gap using energy
services: Demonstrating a novel framework for soft linking top-down and bottom-up models.
_Energy_ 2019, **169:** 277-293.

5. Arndt C, Davies R, Gabriel S, Makrelov K, Merven B, Hartley F _, et al._ A sequential approach
to integrated energy modeling in South Africa. _Applied Energy_ 2016, **161:** 591-599.

6. Böhringer C, Rutherford TF. Combining bottom-up and top-down. _Energy Economics_ 2008,
**30** (2) **:** 574-596.

7. Simões S, Seixas J, Van Regemorter D, Ferreira F. Top-down and bottom-up modelling to
support low-carbon scenarios: climate policy implications AU - Fortes, Patricia. _Climate_
_Policy_ 2013, **13** (3) **:** 285-304.

8. Helgesen PI, Tomasgard A. From linking to integration of energy system models and
computational general equilibrium models – Effects on equilibria and convergence. _Energy_
2018, **159:** 1218-1233.

9. Hwang W-S, Lee J-D. A CGE analysis for quantitative evaluation of electricity market
changes. _Energy Policy_ 2015, **83:** 69-81.

10. Krook-Riekkola A, Berg C, Ahlgren EO, Söderholm P. Challenges in top-down and bottomup soft-linking: Lessons from linking a Swedish energy system model with a CGE model.
_Energy_ 2017, **141:** 803-817.

11. Lanzi E, Chateau J, Dellink R. Alternative approaches for levelling carbon prices in a world
with fragmented carbon markets. _Energy Economics_ 2012, **34:** S240-S250.

12. Drouet L, Haurie A, Labriet M, Thalmann P, Vielle M, Viguier L. A Coupled BottomUp/Top-Down Model for GHG Abatement Scenarios in the Swiss Housing Sector. In: Loulou
R, Waaub J-P, Zaccour G (eds). _Energy and Environment_ . Springer US, 2005, pp 27-61.

13. Sue Wing I. The synthesis of bottom-up and top-down approaches to climate policy
modeling: Electric power technology detail in a social accounting framework. _Energy_
_Economics_ 2008, **30** (2) **:** 547-573.

14. Tapia-Ahumada K, Octaviano C, Rausch S, Pérez-Arriaga I. Modeling intermittent renewable
electricity technologies in general equilibrium models. _Economic Modelling_ 2015, **51:** 242262.


40


15. Tuladhar SD, Yuan M, Bernstein P, Montgomery WD, Smith A. A top–down bottom–up
modeling approach to climate change policy analysis. _Energy Economics_ 2009, **31:** S223S234.

16. Vandyck T, Keramidas K, Saveyn B, Kitous A, Vrontisi Z. A global stocktake of the Paris
pledges: Implications for energy systems and economy. _Global Environmental Change_ 2016,
**41** (Supplement C) **:** 46-63.

17. Waisman H, Guivarch C, Grazi F, Hourcade JC. The Imaclim-R model: infrastructures,
technical inertia and the costs of low carbon futures under imperfect foresight. _Climatic_
_Change_ 2012, **114** (1) **:** 101-120.

18. Oshiro K, Masui T. Diffusion of low emission vehicles and their impact on CO2 emission
reduction in Japan. _Energy Policy_ 2015, **81:** 215-225.

19. van der Werf E. Production functions for climate policy modeling: An empirical analysis.
_Energy Economics_ 2008, **30** (6) **:** 2964-2979.

20. McFadden D. Econometric models of probabilistic choice. _Structural analysis of discrete data_
_with econometric applications_ 1981, **198272** .

21. Brenkert AL, Smith SJ, Kim SH, Pitcher HM. Model Documentation for the MiniCAM.
PNNL; 2003.

22. de Vries BJM, van Vuuren DP, den Elzen MGJ, Janssen MA. The Targets IMage Energy
Regional (TIMER) model Technical Documentation. Department of International
Environmental Assessment
National Institute of Public Health and the Environment (RIVM); 2001.

23. Clarke JF, Edmonds JA. Modelling energy technologies in a competitive market. _Energy_
_Economics_ 1993, **15** (2) **:** 123-129.

24. Nganou J-P. Estimation of the parameters of a linear expenditure system (LES) demand;
2005.

25. Bruinsma J. The resource outlook to 2050: by how much do land, water and crop yields need
to increase by 2050?, Expert meeting on how to feed the world in 2050; 2010.


41




---

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-0-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-0-1.png)
### ARTICLE

https://doi.org/10.1038/s41467-019-12730-4 **OPEN**

## Energy transformation cost for the Japanese mid-century strategy


Shinichiro Fujimori 1,2,3 *, Ken Oshiro 1, Hiroto Shiraki 4 & Tomoko Hasegawa 2,3,5


The costs of climate change mitigation policy are one of the main concerns in decarbonizing

the economy. The macroeconomic and sectoral implications of policy interventions are

typically estimated by economic models, which tend be higher than the additional energy

system costs projected by energy system models. Here, we show the extent to which policy

costs can be lower than those from conventional economic models by integrating an energy

system and an economic model, applying Japan’s mid-century climate mitigation target.
The GDP losses estimated with the integrated model were significantly lower than those in

the conventional economic model by more than 50% in 2050. The representation of industry

and service sector energy consumption is the main factor causing these differences. Our
findings suggest that this type of integrated approach would contribute new insights by

providing improved estimates of GDP losses, which can be critical information for setting

national climate policies.


1 Department of Environmental Engineering, Kyoto University, C1-3 361, Kyotodaigaku Katsura, Nishikyoku, Kyoto city, Japan. 2 Center for Social and
Environmental Systems Research, National Institute for Environmental Studies (NIES), 16–2 Onogawa, Tsukuba, Ibaraki 305–8506, Japan. [3] International
Institute for Applied System Analysis (IIASA), Schlossplatz 1, A-2361 Laxenburg, Austria. [4] School of Environmental Science, The University of Shiga
Prefecture, Hikone, Japan. [5] Department of Civil and Environmental Engineering, College of Science and Engineering, Ritsumeikan University, Kyoto, Japan.
[*email: <EMAIL>](mailto:<EMAIL>)


NATURE COMMUNICATIONS | (2019) 10:4737 | https://doi.org/10.1038/s41467-019-12730-4 | www.nature.com/naturecommunications 1


### ARTICLE NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-019-12730-4



limate change mitigation is one of the greatest societal
challenges facing most countries as reduction of energy# C related CO 2 emissions is key to reducing greenhouse gas

(GHG) emissions. In 2015, more than 190 countries reached the
Paris Agreement (PA) [1] and each country submitted their own
Nationally Determined Contribution (NDC). Along with those
targets, countries were also asked to engage in long-term planning, known as a mid-century strategy [2][,][3] . Under the long-term
global goal in the PA of keeping the global mean temperature
increase well below 2 °C compared with the pre-industrial level,
the net CO 2 emissions in this mid-century must be close to
neutral according to numerous studies carried out using Integrated Assessment Models (IAMs) [4] .
Macroeconomic costs of climate change mitigation is a great
concern for climate policy settings [5] . The Intergovernmental Panel
on Climate Change (IPCC) fifth assessment report summarises
climate mitigation costs, and GDP or consumption losses in 2050
are around 2–6% [4] to achieve the abovementioned 2 °C goal.
There are multiple ways to interpret these numbers. It may be too
expensive to pay for climate change prevention that delays GDP
growth for a couple of years or low enough for avoiding widespread climate change impacts and irreversible risks associated
with catastrophic events. To address macroeconomic mitigation
costs, IAMs normally represent GHG emissions reduction costs
either through an energy system model or an economic model,
often termed bottom-up and top-down models, respectively.
Although there are other ways to classify the IAMs, in this
paper, we define economic model as the model that includes
multi-sectoral CGE model within the IAM framework, and
energy system model as the model that does not. Note that a
power-dispatch model is also used in this study although that
is not usually classified as IAMs. There are many global [6][–][8], and
national energy system models [9][,][10] as well as the economic
models [11][,][12], which are based on multi-sectoral CGE models.
Traditionally, CGE models tend to project higher policy costs
than those of energy system models [13] (see also Supplementary
Note 1 and Supplementary Table 1). One possible reason for this
tendency is that parameters in CGE models are calibrated against
a historical period in which it is difficult to decouple economic
growth and CO 2 emissions. Some argue that aggregated energy
system representation is disadvantageous to understanding
drastic energy system changes and their macroeconomic implications. Thus, incorporating energy system model information
into CGE models may lead lower macroeconomic costs than
previously reported.
Integrating CGE and energy system model offers a great
advantage in representing the feedbacks inherent across economic
and energy systems. For the policy makers, macroeconomic
implications including sectoral impacts provided by CGE models
is more meaningful than energy system costs alone. To this end,
several attempts have been made [14][–][16], whereas investigators such
as Bohringer et al. [17][–][20] incorporated disaggregated information
on power sectors. An extended literature list is shown in Supplementary Table 2 and there are more examples if we include
non-multi-sector CGE models [21][,][22] .
At the meantime, drastic energy transformation requires largescale variable renewable energy penetration. The key issue of the
variability in renewable energy is strongly dependent on nationaland local-scale grid systems, availability of solar and wind power,
battery technology, and other energy sources that can be used to
balance demand and supply. Recently, some national modelling
studies have addressed these issues [23][–][25] and integration of a
power-dispatch model with an energy system model has been
attempted [26] . In IAMs, they are represented to some degree [27][,][28],
which are adequate to provide global-scale energy analyses.
However, no studies showed macroeconomic implications of



consistently dealing with energy systems and the stability of
power generation.
Here, we describe the macroeconomic implications of climate
mitigation policy using an integrated modelling framework
wherein an energy system model, Asian-Pacific Integrated Model/
Enduse (AIM/Enduse), and a power-dispatch model, AIM/Power,
are inter-linked with the multi-sector economic model (AIM/
CGE). We call this new soft-linking modelling framework an
integrated model, which allows us to assess the macroeconomic
impacts of climate change mitigation with concrete specification
of detailed energy technologies, ensuring a stable power supply
with consideration of long-term (seasonal and daily) and shortterm (less than hourly) power fluctuations.
The principle of this methodology is based on the concept that
energy simulation from the energy system model is more reliable
than that from the economic model, as energy supply and
demand are technologically represented in detailed in the energy
system model. Similarly, the technological representation of
power supply in the power-dispatch model is more reliable than
that in the energy system model. We overcome the disadvantages
of these models by exchanging information and iterating it
among models. We begin with the AIM/Enduse run, which
provides energy system information to AIM/CGE and AIM/
Power. Then, these two models’ outcomes are further fed into
AIM/Enduse. Finally, we confirm whether the models reach
sufficient convergence for our purposes (see Supplementary
Information for more detailed discussion about reaching convergence). See the Methods for indicators exchanged among
models. Note that for CGE results, we compare the stand-alone
CGE model with the integrated model.
We applied this framework to Japan as a case study. The
Japanese government has declared a long-term GHG emissions
reduction target of 80% by 2050 [29] . As mitigation costs in Japan
estimated in previous studies vary significantly across IAMs [30][–][32],
application of this framework would be beneficial for Japan’s
climate policies to communicate with the stakeholders. We analysed scenarios with and without climate mitigation policy, which
are the mitigation and baseline scenarios, respectively.
As results, we found that the macroeconomic costs are not as
high as previously reported when energy system information is
appropriately reflected in the economic model. The critical
determinants of mitigation costs that changed in the newly
developed integrated model were identified as the representation
of industry and service sectors’ energy consumption, which is
associated with production functions. These findings may change
the general perception of climate change mitigation costs in terms
of macroeconomic losses and provide important policy insights.


Results
Energy system in Japan’s mid-century strategy. An 80%
reduction of GHG emissions requires substantial changes in the
energy system compared to the current system or the baseline
scenario (Fig. 1a). As a result of Japan’s unique socioeconomic
circumstances, with a decreasing population and modest economic growth (Supplementary Fig. 1), the overall energy system
shows little changes in the future under the baseline scenario. The
main changes of the baseline 2050 from the base year are the
higher share of coal relative to other fossil fuels, and the decrease
in the share of nuclear energy, which reflects the current societal
attitude toward nuclear power that limits new construction
(Fig. 1b). Regarding CO 2 emissions, the baseline level is stable or
may even decline over time (Fig. 1d). Meanwhile, the mitigation
scenario exhibits large-scale renewable energy penetration, slight
energy demand reduction, compositional changes characterised
by the use of more carbon-neutral energy sources, and



2 NATURE COMMUNICATIONS | (2019) 10:4737 | https://doi.org/10.1038/s41467-019-12730-4 | www.nature.com/naturecommunications


### NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-019-12730-4 ARTICLE



20



1250



**a** 20 **b** **c** **d**



4



15


10


5


0



3


2


1


0



20



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-2-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-2-1.png)

**e** 20 **f** **g**



15


10


5


0


15



4



15


10


5


0



3


2


1


0



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-2-4.png)

Wind


Biomass|w/o CCS


Geothermal



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-2-2.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-2-5.png)

10


5


0


Other
Solid|Biomass


Solid|Coal


Liquid|Biomass



Liquid|Oil


Gas


Electricity



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-2-6.png)

Coal|w/o CCS


Oil|w/o CCS


Gas|w/o CCS



Nuclear


Hydro


Solar



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-2-3.png)

1000


750


500


250


0


**h**


900


600


300


0


Heat

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-2-7.png)
Baseline Mitigation



Fig. 1 Main energy and emissions related variables. Primary energy source (a, e), power generation (b, f), final energy demand (c, g), CO 2 emissions (d),
and carbon price (h) projections. a, b, and c show the baseline scenario, whereas e, f, and g show mitigation scenarios. The other in a and e includes
secondary energy net imports



electrification (Fig. 1f). The price of carbon in the mitigation
scenario increases over time and reaches ~1000$/tCO 2 in 2050,
which is due to the low-carbon technological availability in the
AIM/Enduse model.
The power system relying heavily on variable renewable energy
requires measures to stabilise the power supply system and
demand responses. Curtailment in onshore wind increases,
particularly after 2020 when variable renewables start to expand
(Fig. 2a). Furthermore, when coal-fired power is completely
phased out around 2040, offshore wind also exhibits a clear
curtailment increase. The battery requirements for short-term
fluctuations also increase sharply after 2020, whereas the capacity
factor of thermal power plants declines (Fig. 2b, c). We also show
the daily electricity supply and demand profiles for selected days
in 2050 (Fig. 2d).


Mitigation costs. Mitigation costs, as measured by GDP loss rates
(hereafter GDP is accounted by the total final consumption),
increase over time as emissions reductions become deeper, as
illustrated in Fig. 3a. The CGE stand-alone results reach more
than 2.5% after 2030, whereas the integrated model is lower,
around 1.2% in 2050 (Fig. 3a). The equivalent variation also
shows similar trend as GDP losses (Fig. 3b). The additional
energy system costs in the AIM/Enduse stand-alone are plotted in
the same figure, and are notably similar to the integrated model
results (blue lines in Fig. 3a). The mitigation costs under such
deep emissions reductions from CGE studies are usually not as
low as our estimates (2–6% of GDP losses in 2050) [4] . Once the
energy system model’s results are reflected in the economic
model, the integrated model would be able to estimate similar
mitigation costs to those from energy system models.
We further implemented sensitivity scenarios with varying
technological availability, which may lead to non-linear energy
system responses, to investigate the robustness of our findings.
For this purpose, we selected two technological variation
scenarios wherein more power stability measures are needed;



namely, without nuclear and without carbon capture and storage
(CCS). These results can be interpreted as a simple uncertainty
analysis, but they have more meaningful policy implications
because the perception of nuclear power in Japan has changed
drastically since the Fukushima Daiichi accident, and there is
limited geologically appropriate space for CCS on Japanese
territory. Figure 3c illustrates the relationship of mitigation costs
in the CGE stand-alone and integrated models for this sensitivity
analysis. Here, we again see systematically higher costs in the
stand-alone model than in the integrated model. Comparison of
these integrated model’s GDP losses and additional energy system
costs derived from AIM/Enduse shows a similar trend to that
in Fig. 3d. The overall energy and emissions trends for
this sensitivity cases are provided in Supplementary Fig. 2 and
Supplementary Fig. 3.


Mechanism causing the differences in macroeconomic costs.
The central mechanisms for changing the macroeconomic
implications are changes in the productivity of primary factors
(labour and capital) constituting value-added, which is the GDP
measure in production side. This is because the primary factor
inputs are constrained exogenously for each year in our economic
model [33][,][34] while the capital and labour inputs change dynamically with population development, and GDP growth. The
straight-forward reason that GDP losses are lower in the integrated model than in the stand-alone model is that the parameter
assumptions in CGE models differ between the stand-alone
model and integrated model. The former relies on the existing
literature and the latter on the energy system model outputs.
Consequently, the primary factor productivity is higher in the
integrated model than in the stand-alone model. Then, the differences in productivity are mainly driven by two things. One is
the productivity decreases associated with emissions reductions in
energy end-use sectors, such as industry, transport and service
sectors (e.g. capital replacement by expensive but energy-efficient
ones). The other is sectoral allocation changes in primary factors.



NATURE COMMUNICATIONS | (2019) 10:4737 | https://doi.org/10.1038/s41467-019-12730-4 | www.nature.com/naturecommunications 3


### ARTICLE NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-019-12730-4



**a** **b**


8


6


Solar | PV



40


30



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-3-1.png)

Year


Intermediate

weekend

PV_H

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-3-4.png)


Hour



Pumped hydro


Battery for long-term


Battery for short-term



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-3-0.png)

Year

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-3-2.png)


Year



20


10


0


**d** Summer
weekday
PV_H


200


150


100


50


0


–50



Charge


Pumping


Discharge


Pumped hydro


PV


Wind


Demand_w/_HP&EV


Demand_w/o_HP&EV



Wind | Offshore


Wind | Onshore


Thermal power total


Biomass|w/o CCS


Coal|w/ CCS


Coal|w/o CCS


Gas|w/ CCS


Gas|w/o CCS



**c**



4


2


0


80


60


40


20


0



Summer
weekday
PV_L



Gas


Biomass


Coal


Hydro


Nuclear


Loss



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-3-3.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-3-5.png)

Fig. 2 Power system reactions to large-scale renewable energy penetration. a Wind and solar curtailment rate, which is the unused energy divided by
total power generation for each energy source, b the installed capacity of technologies to stabilise fluctuations in the electricity supply, c the capacity
factor of thermal power, and d the profiles of electricity demand and supply on selected three typical days (PV_H and PV_L indicate sunny and cloudy
days, respectively)



Regarding the first factor, Fig. 4a illustrates the capital
input efficiency of major industrial sectors (top 10 industries,
which account for 95% of GDP in the base year) in the
mitigation scenario compared to the baseline scenario for
stand-alone and integrated models in 2050. Here, we define the
capital input efficiency as capital input per output for each
sector, which is a model outcome. Higher values indicate
that additional capital inputs are needed in the mitigation
scenario compared to the baseline scenario. In general, the
stand-alone model requires larger capital inputs than the
integrated model in the mitigation scenario. We can roughly
compute the value-added losses associated with these capital
productivity losses by multiplying the value-added of each
sector Fig. 4b, c). These eventually account for 1.3 percentage
points of the total value-added (GDP). Then, the productivity
differences between the stand-alone and integrated model are
mainly caused by differences in the functional form and
parameters particular to the value-added and energy bundle.
Here, we use a CES function in which the substitution elasticity,
share parameters and future autonomous energy efficiency are
defined in the stand-alone model. The integrated model
uses a function of the same form, but the additional investment
and energy inputs are exogenously given by AIM/Enduse,
whereas the CES shift parameters are determined endogenously
(sector-wise additional investments are shown in Supplementary Table 3).



The second factor, namely the effect of sectoral primary factor
allocation changes, is mainly driven by the power generation sector.
The electricity generation in the mitigation scenario compared to
the baseline scenario is about 20% higher in the stand-alone model,
but almost the same in the integrated model (Fig. 4). There are
certainly differences in technological shares between the stand-alone
and integrated models, but, in summary, it seems that the difference
in total electricity generation between the models is the dominant
factor, where the stand-alone model requires additional capital and
labour inputs, accounting for 0.4 percentage points of GDP, relative
to the integrated model, which relies on the AIM/Enduse outputs
(Fig. 4d). With respect to the representation of electricity demand,
the total electricity demand is determined by energy consumption
in the energy end-use sectors, which are represented by a CES
function, as mentioned above. The fuel-wise share is determined
using a logit function in both the stand-alone and integrated
models. A parameter representing the preferences or technological
choices in the logit function is determined endogenously in the
integrated model, based on the AIM/Enduse results, whereas they
are exogenous parameters in the stand-alone model. We describe
the detailed mathematical formation and assumptions in the
Supporting Information.
In addition to the two main mechanisms mentioned above, the
productivity changes and sectoral shifts in other sectors certainly
occur, but are relatively minor. In summary, the differences in
GDP changes between the stand-alone and integrated models are



4 NATURE COMMUNICATIONS | (2019) 10:4737 | https://doi.org/10.1038/s41467-019-12730-4 | www.nature.com/naturecommunications


### NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-019-12730-4 ARTICLE

**a** **b**



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-4-0.png)

4


3


2


1


0


Year


CGE stand-alone


Integrated model


Enduse stand-alone


**c** **d**

4


3


2


1


0

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-4-6.png)


CGE stand-alone (%/year)


Default


No CCS


No nuclear



4


3


2


1


0


1.5


1.0


0.5


0.0



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-4-1.png)

Year


CGE stand-alone


Integrated model

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-4-7.png)


Energy system model (%/year)


Default


No CCS


No nuclear



Fig. 3 Climate change mitigation cost. a, b Time-series mitigation cost AIM/CGE results are represented as GDP loss rates and equivalent variation change
rates relative to baseline scenarios. AIM/Enduse results are expressed as additional energy system costs of GDP relative to baseline scenarios. c, d 5-year
mitigation costs with varying technological availability; c illustrates the relationship of GDP losses in the CGE stand-alone and integrated models, and
d shows GDP losses in the integrated model and additional energy system costs in AIM/Enduse. The energy system model results shown here correspond
to Enduse_results1 in Supplementary Fig. 5



explained above, but, generally speaking, many interactions
simultaneously occur in the CGE model and sometimes the
cause and consequences are not clear.


Decomposition of mitigation costs and sectoral contributions.
To identify which sectors contribute to GDP losses, the valueadded by each sector, as estimated by the economic model, is
decomposed into three factors of output changes, value-added
productivity (output per value-added), and residuals. Moreover,
we compared the outputs of stand-alone CGE and integrated



model runs in Fig. 5. The stand-alone CGE model shows
remarkable value-added decreases in the industry (IND) and
service sectors (SER) in 2030, whereas the integrated model does
not. These trends remained consistent for the year 2050, with the
CGE stand-alone model showing large changes in the service
sector. This result is consistent with those described in the previous section, wherein the industry and service sector’s energy
system information, i.e. the representation of production functions in those sectors, are critical factors for differentiating overall
GDP losses between the two models. The output decrease in the
service sector is the largest element to change the GDP in the



NATURE COMMUNICATIONS | (2019) 10:4737 | https://doi.org/10.1038/s41467-019-12730-4 | www.nature.com/naturecommunications 5


### ARTICLE NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-019-12730-4

**a** **b**


1.2

0.6


1.1



1.0



CGE stand-alone


Integrated model



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-5-0.png)

0.9


**c** Sectors **d**


02 Other manufacturing



0.4


0.2


0.0


2


1


0



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-5-1.png)

Geothermal


Biomass


Wind


Solar


Hydro


Nuclear


Gas


Oil


Coal



Sectors



Stand-alone model


Integrated model



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-5-3.png)

03 Transport and communications


05 Chemical, plastic and rubber products


07 Paper, paper products and pulp


09 Non-ferrous products



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-5-2.png)

Contribution of capital productivity change
to value added changes (% of GDP)


Fig. 4 Valued-added differences between baseline and mitigation scenarios. a Sectoral capital input efficiency for the top 10 industrial activities in 2050.
Capital efficiencies in baseline and mitigation scenarios are computed and then the capital efficiency in the mitigation scenario relative to the baseline
scenario is shown. b Sectoral value-added share in the baseline scenario for the top 10 industrial activities. c The capital productivity value-added effects
compared to the total value-added for the top 10 industrial activities. Dots means net total changes. Negative and positive values mean capital productivity
gain and losses compared to baseline scenarios respectively. d The value-added share of power sectors in terms of total economy-wide value-added



CGE stand-alone model. This result may be driven by changes in
household expenditures for services, which were around 3.4 and
0.0% in the CGE stand-alone and integrated models, respectively,
in 2050. These differences may be due to changes in total income.
We ran further diagnostic scenarios with and without
incorporating energy system information by sectors (see Methods
for more details) to investigate the extent to which the energy
system model’s output information for each sector contributes to
mitigation cost differences compared to the stand-alone CGE.
Comparing scenarios that include a single sector’s information
from AIM/Enduse and the stand-alone model (Row 1–6
in Table 1, respectively), the inclusion of the industry and service
sector information from AIM/Enduse makes a remarkable
difference in the GDP loss rate (Row 5 and 4 in Table 1,
respectively). From the opposite side, the scenarios taking out the
AIM/Enduse information for each sector (Row 7–11 in Table 1)
show that excluding the industry and service sectors consistently
generates GDP loss differences compared to the integrated model
(Row 12 in Table 1). Conversely, the incorporation of residential,
transport and energy supply sector information given by AIM/
Enduse has a small impact on GDP losses, or even has the
opposite effect in some cases. Finally, we can see cross-sectional
effects in other scenarios in Supplementary Table 4, which
indicates the complexity of the results and shows that the
influence of each sectoral impact is not additive. However, the



overall insights are clear, that the industry and service sectors are
key in determining macroeconomic implications.


Discussion
Our newly proposed integrated model approach implicitly
assumes that the energy productivity in the CGE model is
endogenized by using the energy system model information. This
treatment is somewhat different from the conventional approach,

                                     in which CGE models use the same Autonomous Energy Effi
ciency Improvement (AEEI) and constant elasticity substitution
parameters, with and without mitigation policies. Based on the
results showing that the macroeconomic costs associated with
climate change mitigation policies are lower than estimated using
conventional approaches, we can interpret the energy productivities in the mitigation scenarios as being higher than in the
conventional approach. This would imply that the AIM/Enduse
model incorporates higher productivity technological information
than the conventional CES approach.
Overall, as long as an energy system model is more reliable than
the CGE model in terms of energy-related variables, the energy
representation in the conventional CGE should be replaced by the
energy system model outputs. The contributions of the industry and
service sectors to GDP loss differences are caused by the production
function form and its parameters. Basically, for most conventional



6 NATURE COMMUNICATIONS | (2019) 10:4737 | https://doi.org/10.1038/s41467-019-12730-4 | www.nature.com/naturecommunications


### NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-019-12730-4 ARTICLE

2030 2050


0


–1


–2


0


–1


–2

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-6-0.png)


Sectors


Output change Value-added_Output ratio Residual


Fig. 5 Decomposition analysis of GDP changes across sectors. Value-added changes relative to baseline scenarios are expressed as percentages of GDP.
Legend entries Output change, Value-added_output ratio, and Residual refer to output changes, value-added productivity changes, and residuals,
respectively. The top and bottom panels show CGE stand-alone and integrated model results, respectively. Sectors are BIO Bioenergy industry, SER service
sector, CCS CCS industries, TRS Transportation, IND manufacturing and construction, PWR power, OEN other energy supply, AGR agriculture, and FFE

fossil fuel extraction



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/fujimoriEnergyTransformationCost2019/fujimoriEnergyTransformationCost2019.pdf-6-1.png)





CGE models, the substitution elasticity of energy and value-added
in these sectors use values referenced from the literature [34] . This
representation has two possible disadvantages. First, historical priceinduced energy and capital substitutability data are based on past
events and limited to developed countries. Future technological
availability, which is represented by the energy system model in this
study, may change drastically. Second, the elasticity parameter is
sometimes assumed to be uniform, but it should differ among
sectors, and probably regions (this study uses the global model’s
uniform value for the stand-alone model).



There can be a discussion on the parameter choices in the
conventional CGE models and a question whether our results are
robust to the key parameter assumptions. To this end, we conducted a sensitivity analysis, varying the elasticity substitution
between energy and value-added from 0.2 to 0.8, taking the range
from the literature [35] . The results showed that the cost differences
associated with variation in the substitution elasticity parameter
are much smaller than the differences between the integrated and
stand-alone models (see Supplementary Fig. 4). This implies that
even if the wide range of values for the substitution elasticity



NATURE COMMUNICATIONS | (2019) 10:4737 | https://doi.org/10.1038/s41467-019-12730-4 | www.nature.com/naturecommunications 7


### ARTICLE NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-019-12730-4



parameter (as seen historically in the literature) is considered,
future technological changes represented by the energy system
model cannot be expressed.
To represent the production functions, an alternative approach
to CES-type methods already exists in the econometric method [36] .
In contrast to this approach, our method relies on realistic
representation of technological availability. Therefore, we can
identify explicit technological changes that are consistent with the
general equilibrium framework. Note that this process implicitly
assumes that currently non-existent technologies are excluded,
whereas the conventional approach using possible substitution
could implicitly assume an infinite possibility to decrease energy
consumption in response to energy price signals.
GDP loss differences associated with the household sector’s
representation in the conventional and integrated models were
small, but we need to consider the disadvantages of measuring the
mitigation cost as GDP loss. Household expenditure is a major
component of GDP in the expenditure accounting system, and
increases in household expenditure directly boost GDP. Hence,
purchasing relatively expensive energy devices such as electric
vehicles and heat pumps will not directly decrease GDP, but
rather may offset the negative impacts of climate change mitigation costs. Notably, this GDP increase is attributed to the
additional expenditure, which may not contribute to an increase
in actual welfare. This finding may show one of the limitations of
accounting for climate mitigation costs using this type of model.
An energy system model simply represents the reduction
potential of energy-consuming devices, but numerous other
possibilities exist to change the energy service itself. Artificial
intelligence may maintain energy devices more efficiently, or
transport demand could be reduced. Material consumption can
also change through sharing of goods and services. From that
perspective, the mitigation potential and associated cost may be
underestimated. Meanwhile, these societal changes could have
indirect effects in the opposite direction in terms of energy
consumption, as information technology would require additional electricity. The monetary savings realised by decreasing
energy usage could be spent on other things, and if it were spent
on energy-intensive activities (e.g. tourism using air travel),
energy consumption and emissions could increase.
The energy system model’s representation of technological
diffusion is based on linear programming with some constraints.
Thus, this model may be interpreted as the extreme case where a
single technology is selected at some point under certain price
conditions, such as only electric vehicles being sold in a private
car market. Meanwhile, the CES or logit formulations that are
typically used in economic models allow multiple possibilities,
implicitly assuming heterogeneity in goods and consumers,
whose real behaviour should be represented by a utility function
that accounts for non-monetary value [37] . This notion is important
when interpreting household results derived from integrated
model results, where some may select economically irrational
technologies and non-monetary factors are present. However,
according to our results, industrial activities have more influence
over mitigation cost and our conclusions would hold true if we
included such heterogeneity.
We achieved relatively fast convergence compared with existing
studies. There are two possible reasons for the rapid convergence.
First, on AIM/CGE side, the energy consumption is forced to be
AIM/Enduse by endogenising parameters that are exogenous in the
conventional CGE formula. Second, the major information provided by AIM/CGE to AIM/Enduse that changes the AIM/Enduse
response is the energy service changes (output of sectors and total
household consumption), but the difference from the previous
iteration is less than 1%, which would not change AIM/Enduse
results in terms of carbon price or power generation.



For now, this study’s approach and the implications
thereof are applicable only to Japan, within the context of our
modelling framework. Application to other fields by different
modelling teams is needed to demonstrate that our findings can
be generalised.
For future researches, as reported in the results section, some
variables show discrepancies between the two models in the base
year. Although we think that this discrepancy does not affect our
main conclusion, a more consistent understanding of this type of
modelling framework is needed. This understanding may be
accomplished by calibrating both models, but such calibration
will require substantial additional efforts to fully harmonise the
base year data. Although this calibration is not expected to change
our conclusions, it is a worthwhile endeavour for future research.
Another future potential research based on this modelling is that
hard-linkage among the models and in particular, electricity
market is now highly demanded to investigate in terms of
intermittent supply of solar and wind power generation.


Methods
Overview of the method. Here, we developed an integrated modelling framework
that incorporates energy system, power-dispatch, and CGE models, as illustrated inSupplementary Fig. 5. Each model’s output is exchanged with the others. We
executed five model iterations and assessed the second iteration because the discrepancy improvements were sufficiently small at the second iteration. The calculation begins with an AIM/Enduse run and then uses AIM/CGE and AIM/
Power. AIM/Enduse is run again, considering the AIM/CGE and AIM/Power
outputs. The electricity demand and supply system under stringent emissions
reduction targets would be highly dependent on fluctuations in the electricity
supply and demand patterns, which requires operation on an hourly basis.
Therefore, we used AIM/Power in this model. We conducted scenario-based
simulations through 2050. The individual models were solved from 2010 to 2050,
then the results from each were input to the other models. If models interact each
other for each year, the convergence could be much faster since current approach
can remain the gaps among the models each year, which can be amplified particularly latter period. However, fortunately we have already had good convergences
with less iterations. The energy system and related CO 2 emissions are the scope of
this study, as Japanese GHG emissions are associated with these factors. In this
study, we excluded the effect of climate change damage on the economy to avoid
complexity (e.g. isolating mitigation effects from the mixture of climate changemitigation and damage impact, and additional assumptions on other countries’
emissions situations). The baseline socioeconomic assumptions are based on
Shared Socioeconomic Pathways 2 described in Fujimori et al. [38] .


A computable general equilibrium model. The CGE model used in this study is a
recursive dynamic general equilibrium model that covers all regions of the world
and is widely used in climate mitigation and impact studies [39][–][43] . The main inputs
for the model are socioeconomic assumptions of the drivers of GHG emissions
such as population, total factor productivity (TFP), which should reproduce the
GDP assumptions in baseline scenarios, energy technology, and consumer preferences on diet. The production and consumption of all goods and GHG emissions are the main outputs based on price equilibrium. The base year is the
year 2005.
One characteristic of our industrial classification is that energy sectors,
including power sectors, are disaggregated in detail, because energy systems and
their technological descriptions are crucial for the purposes of this study.
Moreover, to appropriately assess bioenergy and land-use competition, agricultural
sectors are highly disaggregated [44] . Details of the model structure and its
mathematical formulas were provided by Fujimori et al. and wiki page [45] .
Production sectors are assumed to maximise profits under multi-nested
constant elasticity substitution (CES) functions at each input price. Energy
transformation sectors (Supplementary Table 5) input energy and are valueadded based on a fixed coefficient, whereas all energy end-use sectors
(Supplementary Table 6) have elasticities between energy and the value-added
(CES aggregation of capital and labor) amount. These sectors are treated in this
manner to account for energy conversion efficiency in the energy transformation
sectors. Power generation from several energy sources is combined using a logit
function [46], although a CES function is often used in other CGE models. We
chose this method to represent energy balance because the CES function does
not guarantee a physical balance [47] . As discussed by Fujimori, Hasegawa [44], an
energy or physical balance violation in the CES would not be critical if the power
generation shares of each technology in total power generation were similar to
the calibrated information. The hydrogen production sectors have similar
structure as power generation. In this study, climate mitigation changes the
power generation mix when compared to that of the base year, and therefore is a
key treatment. The variable renewable energy cost assumption is shown in SI



8 NATURE COMMUNICATIONS | (2019) 10:4737 | https://doi.org/10.1038/s41467-019-12730-4 | www.nature.com/naturecommunications


### NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-019-12730-4 ARTICLE



section 2. Household expenditures on each commodity are described with a
linear expenditure system (LES) function. The savings ratio is endogenously
determined to balance savings and investment, and capital formation for each
item is determined using a fixed coefficient. The Armington assumption, which
assumes imperfect substitutability between domestically produced and traded
goods [48], is used for trade, and the current account is assumed to be balanced.
To construct energy supply cost curves, we implemented multiple sources of
information. Solar and wind supply curves are from a study considering urban
distance [49] . Biomass potential and supply curve data is from a land-use allocation
model [50] .


An energy system model. The energy system model used in this study is a
recursive dynamic partial equilibrium model based on detailed descriptions of
energy technologies in the end-use and supply sectors. In this study, we used the
multi-region version of AIM/Enduse [Japan] [51], which divides Japan into 10 regions
(see Supplementary Fig. 6) based on the power grid system. The model covers
energy-related GHG emissions from both energy end-use and energy supply sectors. The end-use sectors are composed of industry, buildings and transportation
sectors, and they are disaggregated into several subsectors with respect to types of
products, buildings, and transportation mode based on the IEA energy balances.
The CO 2 emissions constraint is assumed for every simulation year of the AIM/
Enduse model under the mitigation scenario. Within this study, the carbon price
trajectory is almost exponential as a consequence. Therefore, even if we adopt an
inter-temporal optimisation scheme, it would not markedly affect the results.
However, this might not be the case for other carbon constraints. Mitigation
options are selected based on linear programming to minimize total energy system
costs that include investments for mitigation options and energy costs subject to
exogenous parameters such as cost and efficiency of technology, primary energy
prices, energy service demands, and emission constraints. Detailed information on
the model structure and parameter settings are provided in Kainuma et al. [52] and a
list of technologies is given in Supplementary Table 7. As the models used in this
study were recursive dynamic, we did not consider discounting the energy system
costs. Nevertheless, the AIM/Enduse model annualises the capital costs of energy
technologies using a discount rate in the range 5–33% (Oshiro et al.) [53] . The sectoral discount rate is 5% for power and industry, 10% for transportation, and 33%
for other sectors. These individual discount rates are only applied to simulate
technology selection in the energy system model. Consequently, the energy
investment data fed into the economic model are not discounted by these rates.
The power sector is modelled in detail, considering the balances of electricity
supply and demand in 3-h steps to assess the impacts of variable renewable
energies (VREs). This sector also includes measures to integrate VREs into the
grid, such as electricity storage, demand response (DR) using battery-powered
electric vehicles and heat-pump devices, and interconnections. The total capacity
was calculated based on the capacity of newly installed power plants, which was
determined endogenously, as well as that of existing plants. In the AIM/Enduse
model, the residual capacities of the existing power plants in operation in 2010
were calculated based on individual powerplant information, such as year
constructed, capacity of each plant, and expected lifetime.
In the industry, building and transportation sectors, wide mitigation options are
included, such as energy-efficient devices and fuel switching. The industrial sector
also includes innovative technologies such as carbon capture and storage (CCS).
However, the AIM/Enduse stand-alone model does not account for some
mitigation options that contribute to reduction in energy service demands. The key
power generation technoeconomic information is shown in Supplementary
Table 8. The cost information is based on METI data (2015)consistent with the assumptions in Japan’s NDC. Note that the estimated [54], as they are
mitigation cost may become much lower under more optimistic assumptions
regarding future cost reductions, especially for renewable energies. Moreover,
powerplant information in 2010 and fuel assumptions are shown in Supplementary
Table 9 and Supplementary Table 10.


A power-dispatch model. The power-dispatch model used in this study is a
recursive dynamic partial equilibrium model focused on generation planning for
the power sector. In other words, unlike the AIM/Enduse model covering all
energy-related sectors, the AIM/Power model only covers the power generation
sector. This model can simulate hourly or annual electricity generation, generation
capacity, plant locations, and multiple flexible resources, and includes interregional
transmission, dispatchable power, storage, and demand responses. These variables
were selected based on linear programming while minimising the total system
costs, including capital costs, operation and maintenance costs, and fuel costs
under several constraints, including satisfying electricity demand and CO 2 emission reduction targets. In this study, we used a version of the model that classifies
Japan into 10 regions (see Supplementary Fig. 6). Detailed information about this
model can be found in Shiraki et al. [55] . Note that as AIM/Enduse provides power
generation installed capacity for AIM/Power, AIM/Power does not make investment decisions, except for making additional investments in storage and power
plants aimed at hourly and within hourly power demand-supply management
AIM/Power can explicitly simulate the hourly demand-supply balance of
electricity, with consideration of daily variations in photovoltaic output caused by
weather conditions as well as seasonal and weekday/weekend variations in demand.



In addition, the demand-supply balance of electricity within an hour is modelled
using the fluctuations and flexible range of each generator. Although generators
and flexible resources are modelled in detail, electricity demands are provided
exogenously. Thus, the power-dispatch stand-alone model does not determine the
total electricity consumption and installed capacity by technology, which are given
parameters. Note that there are buffers to deal with seasonal fluctuations, such as
fossil fuel CCS thermal plants, in the mitigation scenarios, and thus, even if we
consider battery storage for seasonal fluctuations, it would remain unused due to
the cost competitiveness. The no CCS scenario also uses gas thermal plants to
adjust for seasonal differences.


From the energy system model to the economic model. The following information is given to AIM/CGE from AIM/Enduse outputs. First, Change ratio of
final energy consumption by sector and energy type; second, power generation
share by energy source; third, battery capacity for stabilising fluctuations of the
power supply and its capacity factor, which is taken from AIM/Power (this capacity
factor means the total hours that the battery used divided by a year); forth, CCS
installation; fifth, investment in energy end-use sectors; sixth, carbon prices;
seventh transmission losses.
Final energy consumption is classified into four sectors (industry, transport,
service and residential) and fed into the CGE model. We exogenously represent these
sectors, while autonomous energy efficiency improvement (AEEI) parameters are
endogenised. This treatment maintains the same number of equations and variables
as in the conventional CGE approach. To integrate household energy consumption
and energy device purchase activities in the household, we divided the household
expenditure into four categories, such as car-use activities and other energy
consumption activities, as illustrated in the Supplementary Fig. 7 (see more detailed
information in Supplementary Note 2 and Supplementary Table 11). Because the
absolute value of energy consumption is not fully harmonised between these two
models, we compare the change ratios of energy consumption with 2010 levels, which
is the base year of the AIM/Enduse model, for final energy consumption
determination. If the corresponding energy consumption was zero or very low in 2010
(less than 1 ktoe), the change ratio can lead to unrealistic projections; therefore, we
use absolute values. The investment in energy end-use sectors is input as an
incremental capital cost compared to the baseline case, where investment costs in the
baseline is modelled by CES substitution. Moreover, the capital input coefficients are
fixed at baseline levels so that additional energy investment is represented by AIM/
Enduse information rather than CES substitution elasticity in the mitigation scenarios.


From the economic model to the energy system model. Because the sectoral
disaggregation of AIM/Enduse basically complies with the IEA energy balance,
there are inconsistencies in the AIM/CGE, which is based on an input-output table.
Thus, in terms of data exchange from AIM/CGE to AIM/Enduse, the subsectors
are aggregated so that the granularity of the sectors is in agreement. Nevertheless,given the large share of industrial GHG emissions in Japan’s long-term low-carbon
scenarios, iron, chemical, paper, non-metallic minerals, and non-ferrous metals are
exempted from the sector aggregation. AIM/Enduse uses the following information
generated by AIM/CGE: first, GDP changes; second, household consumption
changes; third, industry and service sector outputs; fourth energy price changes
Economic information from AIM/CGE is input into AIM/Enduse as changes in
energy service demand for each sector. Transport demand is associated with GDP
projection in AIM/Enduse and we proportionally change the transport demand based
on changes in GDP. The energy service demand in the industrial sectors, such as steel
and cement production, and outputs of other industrial sectors, is altered by the
outputs from AIM/CGE. Energy service demand in the household and industrial
sectors could have low or high elasticities to relevant economic activity variables, such
as household consumption and outputs of service sectors, but remains an uncertain
factor. According to the Swedish econometric analysis [48], elasticity between monetary
and physical units of energy services can be assumed to be ~1.0. This elasticity
accounts for the percent change in physical energy services caused by a 1% change in
monetary outputs. Furthermore, the GDP losses indicated in this study are relatively
small, less than 3%, in the CGE stand-alone model, as shown in Fig. 4a. Thus, we
tentatively applied an elasticity value of 1.0. Meanwhile, we varied the elasticity from
0.5 to 2.0 and observed that the policy costs change slightly, but the qualitative
conclusion still holds (Supplementary Fig. 8).


From the energy system model to the power-dispatch model. AIM/Power’s role
is to present the feasibility of power-dispatch given an electricity demand and
installed power capacity. Thus, AIM/Enduse provides the following items to AIM/
Power: first, electricity demand; second, power generation installed capacity; third,
demand response technological availability, such as heat-pump water heaters and
electric vehicles


From the power-dispatch model to the economic model. AIM/Power provides
more realism in terms of technologies to stabilise short-term fluctuations in the
power system than the other two models used in this study. Moreover, the power
system would respond to large-scale renewable energy installations by adjusting the
capacity factor for conventional power generation systems (e.g. coal-fired power) in
addition to curtailing the output from variable renewables. These measures for



NATURE COMMUNICATIONS | (2019) 10:4737 | https://doi.org/10.1038/s41467-019-12730-4 | www.nature.com/naturecommunications 9


### ARTICLE NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-019-12730-4

**f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f**



balancing short-term fluctuations reduce the electricity output per installed capacity, and thus affect investment decisions. It is necessary to consider this feedback
from AIM/Power to AIM/Enduse. In summary, the following AIM/Power information is given to AIM/Enduse: first, battery capacity needed to stabilise shortterm electricity fluctuations; second, Curtailment ratio; third, capacity factors.
Note that generation capacity, although not directly related to balancing shortterm fluctuations, is decided by AIM/Enduse. Thus, the battery capacity is
determined by AIM/Enduse when considering long-term electricity fluctuations,
whereas that for short-term fluctuations is provided by AIM/Power.


Convergence of iterations. We confirm fast convergence between the models.
Detailed discussion related to the convergence is made by Supplementary Note 3

–
where Supplementary Figs. 9 20, Supplementary Table 12 and Supplementary
Table 13 shows actual convergence situation. As stated, the discrepancy between
the model almost reaches convergences in the second step.


Scenario assumptions. There are two basic assumptions for future scenarios,
namely, baseline and mitigation, which are carried out with and without carbon
pricing to reduce GHG emissions by 80% in 2050. Basic assumptions on technological conditions, such as nuclear scenarios and CCS capacities, are taken from
previous studiesdiffers from that in the stand-alone AIM/CGE run to identify each sector [56] . In the results section, we describe how the mitigation cost’s contribution to the changing mitigation costs.


**f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f**



Analytical method for the diagnostic scenario runs. To investigate the extent to
which AIM/Enduse output information for each sector contributes to mitigation
cost adjustment compared to the conventional CGE approach, we ran diagnostic
scenarios with and without incorporation of AIM/Enduse data by sector, as noted
in the supplementary information. Ultimately, we conducted 32 scenarios with
various combinations of AIM/Enduse information for energy supply, industry,
service, transport, and residential sectors taken into account or excluded. The
indicator shown below is adopted. i, t, and s are sets of variables (e.g. energy
demand), years and scenarios, respectively. X i,t,s and Y i,t,s are AIM/CGE and AIM/
Enduse outputs, respectively.

**f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f**



**f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f**


ErrInd i;s ¼



v **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f**
u
u
u
u
~~t~~



f **f** i f **f** i f **f** i f **f** i f **f** i f **f** i f **f** i f **f** i f **f** i f **f** i f **f** i f **f** i f **f** i f **f** i f **f** i f **f** i f **f** i f **f** iffi
2
P t � [X] t;i;s [�] [Y] t;i;s �



**f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f**


~~hP~~ t ~~�~~ X t;i;s þ2 Y t;i;s ~~�i~~ 2



**f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f**


Reporting summary. Further information on research design is available in
the Nature Research Reporting Summary linked to this article.


Data availability
[Scenario data are accessible online via HARVARD Dataverse (https://doi.org/10.7910/](https://doi.org/10.7910/DVN/QE6ERU)
[DVN/QE6ERU). The data which are derived from the original scenario database shown](https://doi.org/10.7910/DVN/QE6ERU)
as figures but not in the above database is available upon requests. The source data
underlying Figs. 1–5 and Supplementary Figs 1–20 are provided as a Source Data file.


Code availability
The source code for generating figures used in the main text and supplementary
[information is available in HARVARD Dataverse (https://doi.org/10.7910/DVN/](https://doi.org/10.7910/DVN/QE6ERU)
[QE6ERU), which name is the Sourcecode.zip. The current code base of the AIM/Enduse,](https://doi.org/10.7910/DVN/QE6ERU)
AIM/CGE and AIM/Power developed over more than two decades at Kyoto University,
Shiga Prefecture University and National Institute for Environmental Studies. AIM/
[Enduse is available at AIM website (http://www-iam.nies.go.jp/aim/data_tools/index.](http://www-iam.nies.go.jp/aim/data_tools/index.html#enduse)
[html#enduse) and others are not available in a publicly shareable version. The code will](http://www-iam.nies.go.jp/aim/data_tools/index.html#enduse)
continue to be developed and hosted by Kyoto University, Department of Environmental
[Engineering (http://www.athehost.env.kyoto-u.ac.jp/). Requests for code should be](http://www.athehost.env.kyoto-u.ac.jp/)
addressed to Shinichiro Fujimori.


Received: 7 November 2018; Accepted: 27 September 2019;


References
1. UNFCCC. FCCC/CP/2015/L.9/Rev.1: Adoption of the Paris Agreement.
(UNFCCC, Paris, 2015).
2. The White House. United States mid-century strategy for deep decarbonization.
(United Nations Framework Convention on Climate Change, Washington,
DC, 2016).
3. Iyer, G. et al. Measuring progress from nationally determined contributions to
mid-century strategies. Nat. Clim. Change 7, 871–874 (2017).



4. Clarke, L. et al. Assessing Transformation Pathways. Climate Change 2014:
Mitigation of Climate Change. Contribution of Working Group III to the Fifth
Assessment Report of the Intergovernmental Panel on Climate Change.
(Cambridge University Press, Cambridge, United Kingdom and New York,
NY, USA, 2014).
5. McCollum, D. L. et al. Energy investment needs for fulfilling the Paris
Agreement and achieving the Sustainable Development Goals. Nat. Energy 3,
589–599 (2018).
6. Calvin, K. et al. The SSP4: a world of deepening inequality. Glob. Environ.
Change 42, 284–296 (2017).
7. Fricko, O. et al. The marker quantification of the Shared Socioeconomic
Pathway 2: a middle-of-the-road scenario for the 21st century. Glob. Environ.
Change 42(Supplement C), 251–267 (2017).
8. van Vuuren, D. P. et al. Energy, land-use and greenhouse gas emissions
trajectories under a green growth paradigm. Glob. Environ. Change 42,
237–250 (2017).
9. Strachan, N. & Kannan, R. Hybrid modelling of long-term carbon reduction
scenarios for the UK. Energy Econ. 30, 2947–2963 (2008).
10. Turton, H. ECLIPSE: An integrated energy-economy model for climate policy
and scenario analysis. Energy 33, 1754–1769 (2008).
11. Jacoby, H. D., Reilly, J. M., McFarland, J. R. & Paltsev, S. Technology and
technical change in the MIT EPPA model. Energy Econ. 28, 610–631 (2006).
12. Fujimori, S. et al. SSP3: AIM implementation of Shared Socioeconomic
Pathways. Glob. Environ. Change 42, 268–283 (2017).
13. Markandya, A., Halsnaes, K. Costing methodologies. Guidance Papers on the
Cross Cutting Issues of the Third Assessment Report of the IPCC. 15–31 (Global
Industrial and Social Progress Research Institute (GISPRI), 2000).
14. Waisman, H., Guivarch, C., Grazi, F. & Hourcade, J. C. The Imaclim-R model:
infrastructures, technical inertia and the costs of low carbon futures under
imperfect foresight. Climatic Change 114, 101–120 (2012).
15. Lanzi, E., Chateau, J. & Dellink, R. Alternative approaches for levelling carbon
prices in a world with fragmented carbon markets. Energy Econ. 34,
S240–S250 (2012).
16. Vandyck, T., Keramidas, K., Saveyn, B., Kitous, A. & Vrontisi, Z. A global

**f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** stocktake of the Paris pledges: implications for energy systems and economy.

Glob. Environ. Change 41(Supplement C), 46–63 (2016).
17. Tuladhar, S. D., Yuan, M., Bernstein, P., Montgomery, W. D. & Smith, A. A
top–down bottom–up modeling approach to climate change policy analysis.
Energy Econ. 31, S223–S234 (2009).
18. Arndt, C. et al. A sequential approach to integrated energy modeling in South
Africa. Appl. Energy 161, 591–599 (2016).
19. Böhringer, C. & Rutherford, T. F. Combining bottom-up and top-down.
Energy Econ. 30, 574–596 (2008).
20. Böhringer, C. & Rutherford, T. F. Integrated assessment of energy policies:
decomposing top-down and bottom-up. J. Economic Dyn. Control 33,
1648–1661 (2009).
21. Hartwig, J., Kockat, J., Schade, W. & Braungardt, S. The macroeconomic
effects of ambitious energy efficiency policy in Germany—Combining bottomup energy modelling with a non-equilibrium macroeconomic model. Energy
124, 510–520 (2017).
22. Kriegler, E. et al. Fossil-fueled development (SSP5): an energy and resource
intensive scenario for the 21st century. Glob. Environ. Change-Hum. Policy
Dimens. 42, 297–315 (2017).
23. Jacobson, M. Z., Delucchi, M. A., Cameron, M. A. & Frew, B. A. Low-cost
solution to the grid reliability problem with 100% penetration of intermittent
wind, water, and solar for all purposes. Proc. Natl. Acad. Sci. USA 112,
15060–15065 (2015).
24. Zerrahn, A. & Schill, W.-P. Long-run power storage requirements for high
shares of renewables: review and a new model. Renew. Sustain. Energy Rev. 79,
1518–1534 (2017).
25. Santos-Alamillos, F. J., Archer, C. L., Noel, L., Budischak, C. & Facciolo, W.
Assessing the economic feasibility of the gradual decarbonization of a large
electric power system. J. Clean. Prod. 147, 130–141 (2017).
26. Pye, S., Li, F. G. N., Price, J. & Fais, B. Achieving net-zero emissions through
the reframing of UK national targets in the post-Paris Agreement era. Nat.
Energy 2, 17024 (2017).
27. Luderer, G. et al. Assessment of wind and solar power in global low-carbon
energy scenarios: an introduction. Energy Econ. 64, 542–551 (2017).
28. Pietzcker, R. C. et al. System integration of wind and solar power in integrated
assessment models: a cross-model evaluation of new approaches. Energy Econ.
64, 583–599 (2017).
29. Japan MoE. Overview of the Plan for Global Warming Countermeasures
[-Cabinet decision on May 13, 2016. 2016 https://www.env.go.jp/press/files/en/](https://www.env.go.jp/press/files/en/676.pdf)
[676.pdf (2016).](https://www.env.go.jp/press/files/en/676.pdf)
30. Aldy, J. et al. Economic tools to promote transparency and comparability in
the Paris Agreement. Nat. Clim. Change 6, 1000 (2016).
31. Oshiro, K., et al. Mid-century emission pathways in Japan associated with the
global 2 °C goal: national and global models’ assessments based on carbon



**f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f** **f**


10 NATURE COMMUNICATIONS | (2019) 10:4737 | https://doi.org/10.1038/s41467-019-12730-4 | www.nature.com/naturecommunications


### NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-019-12730-4 ARTICLE



[budgets. Climatic Change 1–15 https://link.springer.com/article/10.1007/](https://link.springer.com/article/10.1007/s10584-019-02490-x)
[s10584-019-02490-x. (2019).](https://link.springer.com/article/10.1007/s10584-019-02490-x)
32. Sugiyama, M. et al. Japan’s long-term climate mitigation policy: multi-model
assessment and sectoral challenges. Energy 167, 1120–1131 (2019).
33. van der Mensbrugghe, D. The environmental impact and sustainability
applied general equilibrium (ENVISAGE) model. The World Bank, January
[2008 http://siteresources.worldbank.org/INTPROSPECTS/Resources/334934-](http://siteresources.worldbank.org/INTPROSPECTS/Resources/334934-*************/Envisage7b.pdf)
[*************/Envisage7b.pdf (2008).](http://siteresources.worldbank.org/INTPROSPECTS/Resources/334934-*************/Envisage7b.pdf)
34. Babiker, M. H., et al. The MIT Emissions Prediction and Policy Analysis
(EPPA) Model; Revisions, Sensitivities, and Comparisons of Results.
(Cambridge, MA, 2001).
35. van der Werf, E. Production functions for climate policy modeling: an
empirical analysis. Energy Econ. 30, 2964–2979 (2008).
36. Jorgenson, D. W. Econometric methods for modeling producer behavior.
Handb. Econ. 3, 1841–1915 (1986).
37. McCollum, D. L. et al. Improving the behavioral realism of global integrated
assessment models: an application to consumers’ vehicle choices.
Transportation Res. Part D Transp. Environ. 55, 322–342 (2017).
38. Fujimori, S. et al. SSP3: AIM implementation of Shared Socioeconomic
Pathways. Glob. Environ. Change-Hum. Policy Dimens. 42, 268–283 (2017).
39. Hasegawa, T. et al. Climate change impact and adaptation assessment on food
consumption utilizing a new scenario framework. Environ. Sci. Technol. 48,
438–445 (2014).
40. Hasegawa, T. et al. Consequence of climate mitigation on the risk of hunger.
Environ. Sci. Technol. 49, 7245–7253 (2015).
41. Mittal S., Dai H., Fujimori S., Masui T. Bridging greenhouse gas emissions and
renewable energy deployment target: comparative assessment of China and
India. Appl. Energy 166, 301–313 (2016).
42. Hasegawa, T., Fujimori, S., Takahashi, K., Yokohata, T. & Masui, T. Economic
implications of climate change impacts on human health through
undernourishment. Climatic Change 136, 1–14 (2016).
43. Fujimori, S., Kainuma, M., Masui, T., Hasegawa, T. & Dai, H. The
effectiveness of energy service demand reduction: a scenario analysis of global
climate change mitigation. Energy Policy 75(Supplement C), 379–391 (2014).
44. Fujimori, S., Hasegawa, T., Masui, T. & Takahashi, K. Land use representation
in a global CGE model for long-term simulation: CET vs. logit functions. Food
Sec 6, 685–699 (2014).
45. IAMC. The common Integrated Assessment Model (IAM) documentation.

[https://www.iamcdocumentation.eu/index.php/IAMC_wiki (2018).](https://www.iamcdocumentation.eu/index.php/IAMC_wiki)
46. Clarke, J. F. & Edmonds, J. A. Modelling energy technologies in a competitive
market. Energy Econ. 15, 123–129 (1993).
47. Schumacher, K. & Sands, R. D. Innovative energy technologies and climate
policy in Germany. Energy Policy 34, 3929–3941 (2006).
48. Armington, S. P. A theory of demand for products distinguished by place of
production. Staff Pap. 16, 159–178 (1969).
49. Herran, D. S., Dai, H., Fujimori, S. & Masui, T. Global assessment of onshore
wind power resources considering the distance to urban areas. Energy Policy
91, 75–86 (2016).
50. Hasegawa, T., Fujimori, S., Ito, A., Takahashi, K. & Masui, T. Global land-use
allocation model linked to an integrated assessment model. Sci. Total Environ.
580, 787–796 (2017).
51. Oshiro, K. & Masui, T. Diffusion of low emission vehicles and their impact on
CO2 emission reduction in Japan. Energy Policy 81, 215–225 (2015).
52. Kainuma, M. M. Y. & Morita, T. Climate policy assessment: Asia-Pacific
integrated modeling. (Springer, Japan, 2003).
53. Oshiro, K., Kainuma, M. & Masui, T. Assessing decarbonization pathways and
their implications for energy security policies in Japan. Clim. Policy 16(sup1),
S63–S77 (2016).
54. METI. Report on analysis of generation costs, etc. for subcommittee on longterm energy supply and demand outlook. (Ministry of Economy, Trade and
Industry, 2015).



55. Shiraki, H., Ashina, S., Kameyama, Y., Hashimoto, S. & Fujita, T. Analysis of
optimal locations for power stations and their impact on industrial symbiosis
planning under transition toward low-carbon power sector in Japan. J. Clean.
Prod. 114, 81–94 (2016).
56. Oshiro, K., Kainuma, M. & Masui, T. Implications of Japan’s 2030 target for
long-term low emission pathways. Energy Policy 110, 581–587 (2017).


Acknowledgements
S.F., K.O., and T.H. are supported by the Environment Research and Technology
Development Fund (2-1702, 2-1908) of the Environmental Restoration and Conservation
Agency of Japan and JSPS KAKENHI Grant (No. 19H02273). S.F. is supported by
COMMIT (Climate pOlicy assessment and Mitigation Modeling to Integrate national
and global Transition pathways) financed by Directorate General Climate Action (DG
CLIMA) and EuropeAid under grant agreement No. 21020701/2017/770447/SER/
CLIMA.C.1 EuropeAid/138417/DH/SER/MulitOC. H.S. is supported by the Murata
Science Foundation.


Author contributions

S.F. designed the research; S.F carried out analysis of the modelling results; S.F. and H.S.
created figures; S.F. wrote the draft of the paper; S.F., K.O. and H.S. provided economic,
energy system, and power model data; and S.F., K.O., H.S., and T.H. contributed to the
discussion and interpretation of the results.


Competing interests
The authors declare no competing interests.


Additional information

[Supplementary information is available for this paper at https://doi.org/10.1038/s41467-](https://doi.org/10.1038/s41467-019-12730-4)

[019-12730-4.](https://doi.org/10.1038/s41467-019-12730-4)


Correspondence and requests for materials should be addressed to S.F.


Peer review information Nature Communications thanks Per Ivar Helgesen, Ronald
Sands and the other, anonymous, reviewer(s) for their contribution to the peer review of
this work. Peer reviewer reports are available.


[Reprints and permission information is available at http://www.nature.com/reprints](http://www.nature.com/reprints)


Publisher’s note Springer Nature remains neutral with regard to jurisdictional claims in
published maps and institutional affiliations.


Open Access This article is licensed under a Creative Commons
Attribution 4.0 International License, which permits use, sharing,
adaptation, distribution and reproduction in any medium or format, as long as you give
appropriate credit to the original author(s) and the source, provide a link to the Creative
Commons license, and indicate if changes were made. The images or other third party
material in this article are included in the article’s Creative Commons license, unless

indicated otherwise in a credit line to the material. If material is not included in the

article’s Creative Commons license and your intended use is not permitted by statutory
regulation or exceeds the permitted use, you will need to obtain permission directly from
[the copyright holder. To view a copy of this license, visit http://creativecommons.org/](http://creativecommons.org/licenses/by/4.0/)
[licenses/by/4.0/.](http://creativecommons.org/licenses/by/4.0/)


© The Author(s) 2019



NATURE COMMUNICATIONS | (2019) 10:4737 | https://doi.org/10.1038/s41467-019-12730-4 | www.nature.com/naturecommunications 11


