# Citation Key: lombardiPolicyDecisionSupport2020

---

#### **ll**

### Article
# Policy Decision Support for Renewables Deployment through Spatially Explicit Practically Optimal Alternatives

Francesco <PERSON>i, Bryn



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lombardiPolicyDecisionSupport2020/lombardiPolicyDecisionSupport2020.pdf-0-3.png)











We develop a computational method, which shows that there is a flexibility of
choice to manage contested decisions arising when planning highly renewable
power systems, such as where to locate wind capacity. Within this decision space,
problematic technologies, such as bioenergy, are difficult and costly to replace
and are not absolute must-haves. Expansion of PV is a must-have, coupled with
battery when designed to cope with low-wind conditions. Carbon-neutral gas
turbines can contribute to balancing but with a minor role compared with today’s

use.



<PERSON><PERSON>, <PERSON>a <PERSON>,


<PERSON>


[<EMAIL>](mailto:<EMAIL>)


HIGHLIGHTS

We reveal trade-offs like where to

site wind farms, hidden in

conventional methods


PV is a must-have in all examined

systems, often complemented by
batteries


Replacing firm capacity is costly,
requiring large renewable and
storage capacities


Carbon-neutral gas turbines
contribute to balancing but
minorly compared with today


<PERSON><PERSON> et al., Joule 4, 2185–2207


October 14, 2020 ª 2020 Elsevier Inc.


[https://doi.org/10.1016/j.joule.2020.08.002](https://doi.org/10.1016/j.joule.2020.08.002)


#### **ll**


##### Article
## Policy Decision Support for Renewables Deployment through Spatially Explicit Practically Optimal Alternatives

Francesco Lombardi, [1][,][3][,] - Bryn Pickering, [2] Emanuela Colombo, [1] and Stefan Pfenninger [2]



SUMMARY

Designing highly renewable power systems involves a number of
contested decisions, such as where to locate generation and transmission capacity. Yet, it is common to use a single result from a
cost-minimizing energy system model to inform planning. This neglects many more alternative results, which might, for example,
avoid problematic concentrations of technology capacity in any
one region. To explore such alternatives, we develop a method to
generate spatially explicit, practically optimal results (SPORES).
Applying SPORES to Italy, we find that only photovoltaic and storage technologies are vital components for decarbonizing the power
system by 2050; other decisions, such as locating wind power, allow
flexibility of choice. Most alternative configurations are insensitive
to cost and demand uncertainty, while dealing with adverse weather
requires excess renewable generation and storage capacities. For
policymakers, the approach can provide spatially detailed power
system transformation options that enable decisions that are socially and politically acceptable.


INTRODUCTION


Transitioning to decarbonized electricity supply is urgent, [1] and increasingly, the
large-scale deployment of wind and solar power are seen as the key to this
transition. [2][,][3] Policy and planning decisions toward that goal are not straightforward,
given the many possible technological options and the different social and political
barriers they face. [4][,][5] Energy system optimization models can inform decisions by
identifying system configurations to reach a target such as zero emissions with minimum cost. [6][,][7] However, cost optimality alone ignores the social and environmental
dimensions that are more important for real-world political feasibility [8][,][9] and that
models have difficulties depicting. [10] Indeed, many stakeholders with differing
influence and motivations are involved in energy planning, often including local communities when it comes to renewable capacity deployment decisions. [11] Although
linearized methods to consider multiple objectives do exist, it is virtually impossible
to parametrize the indefinite number of stakeholder objectives into a single multiobjective optimization problem. [12] Further, these techniques fail to acknowledge
the limitation of looking for a single optimal configuration. [8] Focusing on a single,
optimal solution means that a range of equally feasible but perhaps radically different
system configurations remain hidden from view. [4] Explicitly modelling and comparing
these alternatives lets energy modellers more effectively support decision making. [12]


Methods to generate near-optimal alternative solutions have been applied in energy
system optimization models [13][,][14] in both country-wide [15][,][16] and continental [17][,][18]



Context & Scale


The planning of highly renewable
power systems at any scale
involves compromise across
diverse stakeholders. We develop
a method that generates a variety
of spatially explicit, alternative
system configurations that can be

used to balance techno-economic

feasibility with social and political
goals.


The application of our method to
Italy reveals flexibility of choice for

decisions like where to locate

wind power and whether to invest
in particular technologies.
Technology substitution and
complementarity is evident: solar
photovoltaic and battery
capacities expand together, as do
wind and synthetic gas turbine
capacities, all of which must
notably increase to replace
bioenergy’s firm capacity. We also
see that highly renewable systems
rely on regional interconnectivity
but that gas infrastructure is only

useful at a fraction of current

capacity. Our approach can be
similarly applied to examine
trade-offs in other national

systems, as well as those at district

and continental scales.



Joule 4, 2185–2207, October 14, 2020 ª 2020 Elsevier Inc. 2185


#### **ll**

studies. These past studies have focused on identifying ‘‘must-have’’ technologies,
such as electricity storage, that are always or frequently chosen by the model across
many near-optimal solutions. [18] However, all of these studies modelled entire countries as single regions with average renewable generation potentials for a single
weather year. Yet, for the renewable transition, an entirely new set of relevant questions is of critical importance, such as: the variability of renewable generation as a
function of the siting of wind and solar capacity; [3] the interdependency of such siting
and of the resulting distance from point of consumption with transmission capacity
expansion plans [19][,][20] ; and the effects of technology deployment, both renewable
generation and transmission, on landscape protection, land use conflicts, and social
acceptance of infrastructure. [21] These questions all play out on a more local scale [11]

and thus require modelling with spatial detail.


Here, we introduce a method to address these shortcomings, to generate spatially
explicit practically optimal results (SPORES). To do so, we expand the algorithm
for the generation of near-optimal solutions from DeCarolis [12] to generate solutions
that differ in their spatial configuration of technology deployment. We modify the
model’s objective function to minimize the appearance of location-technology combinations already explored in previous solutions, assigning weights to these preexplored combinations proportional to their utilization of available expansion
potential (see Experimental Procedures). Using our approach, we investigate potential configurations for the full decarbonization of the Italian energy system by 2050.
Currently without an energy system plan beyond 2030, Italy is in need of planning in
the context of a fully decarbonized Europe. The country also exhibits spatial variation in demand, resource availability, and legislative power, allowing us to demonstrate the scientific use of the SPORES approach while providing timely policy
insights. We model Italy using 20 regions corresponding to the political units that
hold legislative power on local renewable energy regulation, further grouped into
the 6 electricity market bidding zones. In doing so, we harmonize the spatial context
of our model with real decision structures. The model is solved using hourly data for a
specific weather year in the range 1981–2016, determining the capacity and location
of electricity generation, storage, and transmission technologies to deploy. The
model is implemented in the open-source Calliope framework [22] which we extend
with a general implementation of the SPORES approach. As a basis for comparison,
we first start with a discussion of model results from the cost-minimizing optimal
solution.


RESULTS


Cost Optimality Makes Implicit Trade-Offs on Critical Real-World Decision
Variables

Decisions on technology mix happen along three dimensions: the specific technologies to deploy, the location of their installed capacity, and the extent to which that
capacity is used in operating the system. As shown in Figure 1, the cost-optimal
configuration from the first, conventional run of the model (see Experimental Procedures) indicates a major PV capacity expansion (+144.5 GW), followed by onshore
and offshore wind (+59.6 and +17.6 GW, respectively). Non-negligible investments
in electrolysis (7.0 GW H2 ) and methanation with direct air capture (5.6 GW CH4 ) allow
for the injection and long-term storage of carbon-neutral, synthetic methane in the
existing gas infrastructure. This allows 11.7 GW of existing combined-cycle gas turbines to be kept in operation. All of the limited expansion potential for international
connections, pumped hydro, and biomass plants is fully utilized, whereas inter-zone
connections are substantially but not fully reinforced. Utility-scale batteries are
mainly installed in the bidding zone NORD (for zone definitions, see Figure S11),


2186 Joule 4, 2185–2207, October 14, 2020


##### Article

1 Politecnico di Milano, Department of Energy,
20156 Milan, Italy


2 ETH Zu¨ rich, Department of Environmental
Systems Science, 8092 Zu¨ rich, Switzerland


3 Lead Contact


[*Correspondence: <EMAIL>](mailto:<EMAIL>)


[https://doi.org/10.1016/j.joule.2020.08.002](https://doi.org/10.1016/j.joule.2020.08.002)


#### **ll**


##### Article



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lombardiPolicyDecisionSupport2020/lombardiPolicyDecisionSupport2020.pdf-3-3.png)




































































|Col1|Col2|Col3|
|---|---|---|
||||
||||


|Col1|Col2|Col3|Col4|
|---|---|---|---|
|||||
|||||
|||||



Figure 1. Cost-Optimal Model Results Compared with Capacity Expansion Potentials
Capacity expansion potentials, or potential capacity, represent the maximum theoretically possible expansion for the capacity of a technology in a
specific region or bidding zone. The methodology adopted to compute such values is reported in detail in the Experimental Procedures.
(A) Cost-optimal and potential PV and wind capacity in each political region.
(B) Cost-optimal utilization of available potential transmission line capacity expansion between national bidding zones and to neighboring countries.
(C and D) Nationally aggregated cost-optimal and potential capacity, for all modeled electricity generation and storage technologies, split into smaller
scale (C) and larger scale (D) technologies. Storage technology capacity refers to charge/discharge capacity, not stored energy capacity. Electrolysis
and methanation capacity are expressed in GW relative to the output product (hydrogen and methane, respectively). The potential expansion capacity
of all biofuel, hydro, and geothermal technologies is fully utilized, as well as all international transmission. There is such a large scope to increase
electrolysis, methanation, and gas turbine capacity that, for clarity, we do not visualize the exact limit (reported in Supplemental Experimental
Procedures). The same occurs for battery, PV, and wind capacity, but most of the optimal capacity expansion takes place in specific regions, some of
which are fully utilized, leaving others with little to no deployed capacity. For bidding zone and region definitions, see Supplemental Experimental

Procedures.


Joule 4, 2185–2207, October 14, 2020 2187


#### **ll**

which has the highest peak demand. The cost-optimal configuration for 2050 would
require, starting from the 2015 reference year, average annual renewable deployment rates of + 4.1 and +2.2 GW for PV and wind, respectively. This is comparable
to annual expansion in the period 2008–2016. Driven by strong political support, PV
expansion in this time period averaged +2.4 GW/year, peaking at +9.5 GW in 2010,
whereas wind expansion averaged +0.7 GW/year, peaking at +1.4 GW in 2009. [23]

Transmission line expansion would similarly be in line with current national plans.
The grid operator expects to reach the international transmission expansion suggested by the cost-optimal solution by 2030. [24] Italian inter-zone transmission expansion is equivalent to +0.44 GW/year, slightly higher than the +0.29 GW/year foreseen in current plans. [24]


Although capacities and deployment rates in the cost-optimal result are thus
feasible, four features of the optimal solution stand out as highly relevant and
potentially problematic for policy makers. These features represent implicit
trade-offs made in the cost-optimal solution, which may be undesirable or warrant
modification. First, there is an uneven distribution of wind and solar generation capacity across the country. A small number of regions would need to sustain
unprecedented shares of the national deployment rate. This is particularly problematic for wind farms, increasingly encountering local opposition worldwide. [25][,][26]

For example, Sardinia would go from containing 11.0% to 19.5% of Italian wind
generation capacity, while comprising only 2.7% of its population. From a technical perspective, such concentration of capacity also entails increased need for
transmission to points of consumption located further away, with system reliability
risks in case of line congestion or damage, compared with a more distributed
configuration. [27]


Second, large transmission capacity expansion leads to underutilized connections.
The SICI-SUD connection would only have a 26% average hourly capacity factor.
Such low utilisation [28][,][29] may be cost-optimal on a national level but may make justifying the investment more difficult from a local perspective, given the high landscape impact in proportion to the low utilization. In addition, the line is mostly
used unidirectionally to transfer local excess generation in SICI to the neighboring
SUD region, which may further lead to local opposition through a perception of unevenly shared benefits. A low-capacity factor can thus be seen as a proxy for potential broader social barriers. [30] Third, system design is based on a specific weather
year, which does not cover the full range of climatological conditions, such as particularly windless days. Additional renewable generation and storage capacity may
mitigate the effect of overfitting renewable generation to a single weather year,
on the condition that firm, dispatchable capacity remains fixed. Despite leading to
greater curtailment in typical weather conditions, this ‘‘overcapacity’’ could be beneficial in adverse weather years.


Fourth and finally, there is deployment of technologies against which public opposition may arise, are technically unproven, or which may have undesired trade-offs.
These technologies include bioenergy, which competes with other land uses, and,
which may have unacceptably high lifecycle emissions; [31] batteries, the large-scale
integration of which into power systems has not yet taken place; methanation with
direct air capture, which has so far only been demonstrated at the pilot scale; [32]

and offshore wind, which currently lacks political support in Italy, similarly to other
international contexts. [33–35] Using our approach, we will investigate alternative but
equally feasible solutions, generating a decision space to help navigate around
the possibly problematic features highlighted above. While we apply the method


2188 Joule 4, 2185–2207, October 14, 2020


##### Article


##### Article

to the Italian case, the problems outlined above exist across most industrialized
countries, [31][,][34][,][36] and our approach to dealing with them are equally applicable
elsewhere.


Must-Haves and Real Choices

Given that some features of the model results may be undesirable or impractical, for
reasons exogenous to the model configuration, we want to investigate whether
there are key features that hold across a range of model solutions (must-haves),
where one deployment choice could be substituted with another (real choices),
and what trade-offs such substitutions would entail. To do so, we generate SPORES.
These SPORES are feasible solutions that are maximally different from the costoptimal solution while total system cost remains within 5%, 10%, or 20% of the
cost-optimal case. 178 SPORES are generated in each scenario; see Experimental
Procedures for a detailed description of the method.


Figure 2 shows the frequency distribution of the extent to which potential expansion
capacity is utilized by each technology, across a set of SPORES in each cost relaxation scenario. For a 10% cost relaxation, most technological options do not appear
consistently across all alternative configurations, with the exception of PV and international transmission. In particular, PV has a narrow distribution that peaks around
45% capacity utilization and never falls below 15%. Moreover, while the expansion
of international transmission can be completely avoided if accepting a larger
(20%) cost relaxation compared to the cost-optimal case, PV can never be entirely
disregarded. If the accepted cost relaxation is instead limited to 5%, technologies,
such as inter-zonal transmission, onshore wind, pumped hydro storage, and bioenergy, are present to some extent in all configurations. All other technologies
can be entirely substituted by functionally equivalent alternatives in at least one
configuration, irrespective of the accepted cost relaxation. These technologies (power-to-gas, batteries, and offshore wind) therefore represent the real choices. International and inter-zonal transmission, onshore wind, pumped hydro storage, and
bioenergy are costly to replace but not absolute must-haves. Only PV remains as
an absolute must-have technology, which places it in a position of key importance
in Italy’s energy transition.


Although firm capacity options, such as bioenergy, can be reduced, this always entails significantly larger increases in capacity elsewhere. For instance, assuming a
10% cost relaxation, avoiding 4 GW of additional bioenergy capacity requires 36
to 45 GW additional renewable capacity and approximately 6 GW of additional storage capacity. This trade-off and the importance of firm capacity has also been shown
to exist in the US. [19] In our case, if firm capacity is predominantly based on harvested
biomass, the land use implications of replacing bioenergy with renewables are not
clear. Power-to-gas options are an alternative for firm capacity. However, because
they are coupled to renewable generation through electrolysis, they can help to substitute bioenergy only when the cost relaxation permits a large deployment of excess
renewable capacity (see Figure 2C). PV with battery storage thus remains the most
cost-effective alternative to firm capacity.


Figure 3 shows that the available decision space is limited by the choice of technology and where those technologies are sited (see also Figure S1). We can see that the
deployment of wind capacity in southern and islanded zones, far from the largest
point of consumption (NORD), requires increased transmission capacity on all lines
leading to the NORD bidding zone. This trend is less marked for PV capacity deployment, since there is a high tendency for PV deployment to be coupled with battery


#### **ll**

Joule 4, 2185–2207, October 14, 2020 2189


#### **ll**


##### Article

**C** 20% cost relaxation



**A** 5% cost relaxation



Bioenergy minimised Cost-optimal solution


**B** 10% cost relaxation



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lombardiPolicyDecisionSupport2020/lombardiPolicyDecisionSupport2020.pdf-6-3.png)

1.0


0.5


0.0


Figure 2. Frequency Distribution of Capacity Expansion Potential Utilization across the Whole Set of SPORES (178 for Each Box), for Each Technology
Option and for Different Cost Relaxations
Utilization is defined as the deployed technology capacity relative to the maximum potential capacity across the entire model region. Zero utilization
only occurs in specific SPORES in which we explicitly minimize utilization of a technology (see Experimental Procedures). Technologies with no
occurrences of zero utilization (PV, international transmission) are those that can never entirely be avoided, not even when explicitly minimized and
hence represent a must have. Gas turbine capacity is used to represent the full power-to-gas technology chain (comprising electrolysis, methanation
with direct air capture, and gas turbines), since it is the rate limiter for electricity production from synthetic methane. The frequency distribution is shown
by a strip (categorical scatter) plot in red, with the kernel density estimation of the underlying distribution in gray underneath. The vertical spread in the
strip plot points (i.e., the ‘‘jitter’’) improves legibility but has no statistical interpretation. The frequency distribution is statistically equivalent to a
sample, not a full dataset, since the generated alternatives represent only a subset—namely the subset of maximally different alternatives—of all
possible feasible configurations. Colored diamonds highlight particular cases in which bioenergy (as a key provide of firm capacity) is minimized,
leading to proportionally larger increments of renewables and storage capacity, as further discussed in the main text.


storage, which favors intra-day, local consumption. Indeed, the synergy between
daily PV fluctuations and short-term battery storage frequently reduces the need
for gas turbine capacity for intra-day balancing. Gas turbine capacity correlates
instead with wind capacity, without the need for geographic proximity. This highlights two competing strategies: one more oriented toward local consumption,
based on PV and batteries, and another more oriented toward consumption far
from production, based on wind turbines and a combination of increased transmission expansion and power-to-gas deployment. When a higher investment margin is
made available, such as with a 20% cost relaxation, the coexistence of multiple strategies becomes possible, and competition effects weaken overall, possibly reducing
the risks associated with the reliance on a single strategy—such as one grounded on
long-distance transmission.


We have seen that bioenergy is structurally hard to replace. However, such behavior
changes for bidding zones located between several other zones, and hence heavily
interconnected, such as CNOR. These zones can take advantage of multiple, diversified weather patterns to displace bioenergy via a combination of (1) local or nearby
PV and gas turbine capacity (with gas turbines emerging particularly for cost relaxations larger than 5%) and (2) wind farms located further away and connected via
transmission lines and gas pipelines. This buffering role could be similarly played


2190 Joule 4, 2185–2207, October 14, 2020


#### **ll**


##### Article

Offshore wind

Onshore wind

Transmission

Bioenergy
Gas turbines

Storage
PV

Offshore wind

Onshore wind

Transmission

Bioenergy
Gas turbines

Storage
PV

Offshore wind

Onshore wind

Transmission

Bioenergy
Gas turbines

Storage
PV

Offshore wind

Onshore wind

Transmission

Bioenergy
Gas turbines

Storage
PV

Offshore wind

Onshore wind

Transmission

Bioenergy
Gas turbines

Storage
PV

Offshore wind

Onshore wind

Transmission

Bioenergy
Gas turbines

Storage
PV



1


0


−1



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lombardiPolicyDecisionSupport2020/lombardiPolicyDecisionSupport2020.pdf-7-3.png)

**NORD**



**CNOR**



**CSUD**



**SUD**



**SARD**



**SICI**



Figure 3. Correlation of Technology Capacity Utilization across all 178 SPORES for the Reference 10% Cost Relaxation
The same figure is repeated for cost relaxations of 5% and 20% as Figure S1. A high correlation coefficient, either positive (e.g., between battery storage
and PV in SICI) or negative (e.g., between onshore and offshore wind in SICI and SUD), indicates a strong coupling between capacity deployment of two
technologies across the entire SPORES set. A positive coupling indicates that both technologies are deployed together, whereas a negative coupling
indicates that one technology is deployed at the expense of the other. There are many instances in which correlation is either low or zero, indicating no
coupling between deployment patterns. Correlation is calculated using the Spearman correlation function; self-correlation, which is always 1, is not
shown. Some technologies in the figure represent groups of model technologies: ‘‘PV’’ covers rooftop and open-field PV; ‘‘transmission’’ covers interzonal and international transmission; ‘‘storage’’ covers pumped hydro and battery, but not gas storage, since it is equally available across all SPORES as
part of the existing infrastructure, provided that power-to-gas technologies are installed. ‘‘Gas turbines’’ shows the dispatchable capacity of the whole
power-to-gas supply chain. Some technologies such as waste-to-energy are not included, as they have no expansion potential.


by central zones in other geographic contexts. For example, capitalizing on the anticorrelation of wind patterns across Europe requires balancing wind turbine generation across the continent’s north-south divide. [37] Based on this initial examination of

the range of choice that exists across all solutions, we move on to defining more

formal criteria to select solutions of interest.


Joule 4, 2185–2207, October 14, 2020 2191


#### **ll**

Table 1. Best-Ranking SPORES


##### Article















|Accepted<br>Cost<br>Relaxation|SPORE Name and<br>Number|Overcapacity<br>(GW)|Maximum Wind<br>Capacity Share in a<br>Region (%)|Minimum Inter-<br>zonal Transmission<br>Capacity Factor (%)|
|---|---|---|---|---|
|20%|high overcapacity<br>(100)|48.5a|13.9|46.1|
|20%|low wind<br>concentration (23)|21.6|11.4a|46.1|
|20%|high transmission<br>use (101)|10.4|15.1|59.3a|
|10%|high overcapacity<br>(59)|32.1a|13.2|33.2|
|10%|low wind<br>concentration (101)|11.5|12.1a|35.9|
|10%|high transmission<br>use (24)|9.6|13.4|42.4a|
|5%|high overcapacity/<br>low wind<br>concentration (62)|6.4a|10.2a|31.6|
|5%|high transmission<br>use (9)|0|14.3|33.9a|
||cost-optimal<br>solution|0|19.5|26.1|


Best-ranking SPORES within the subset of 54, 22, and 11 SPORES (for, respectively, 20%, 10%, and 5%
accepted cost relaxations) with a regional concentration of wind deployment lower than the cost-optimal
solution and transmission line capacity factors no lower than 30%. For a 10% cost relaxation, SPORE 59
features the highest overcapacity (estimated by renewable and storage discharge capacity in excess
compared to cost optimal, for the same firm capacity). Discharge capacity is given as the maximum
rate at which electricity can be dispatched by direct electrical storage (pumped hydro and batteries) or
synthetic methane gas turbines (supplied via electrolysis and methanation). SPORE 101 has the lowest
maximum wind capacity share in any one region, whereas SPORE 24 has the highest minimum inter-zonal
transmission capacity factor. For the case of a 5% cost relaxation, the ‘‘high-overcapacity’’ SPORE is simultaneously the highest-ranking for ‘‘low wind concentration.’’ Notably, throughout all cost relaxation
values, although each of these three SPORES were selected for their performance on one metric, they
all outperform the cost-optimal solution in terms of wind capacity concentration and transmission line utilization, while overcapacity shows a nonlinear trend.‘
a Highest scoring solution on each metric.


Alternatives with Spatial and Technological Diversity
We now examine three of the problematic features we identified in the optimal solution: the concentration of wind farm deployment in specific regions, the low-capacity factor of expanded transmission lines, and the overfitting of renewable
generation and storage capacity to a specific set of weather conditions, quantified
by the presence or absence of overcapacity compared to the cost-optimal solution.
We compare all SPORES on key metrics associated with each feature (Table 1). We
find that only a subset of SPORES both reduce the concentration of wind deployment and avoid transmission lines with capacity factors below 30% (Table S1). Table 1 compares the three key metrics for those SPORES with the greatest improvement relative to the cost-optimal solution on each metric, across cost relaxations.
Depending on our choice of SPORE and on the cost relaxation, we see that it is
possible to invest in overcapacity in the range 6.4–48.5 GW, reduce the spatial concentration of wind deployment by 40%–48%, or increase the minimum capacity factor across all inter-zonal transmission lines by 30%–127%. As expected, variations in
the accepted cost relaxation affect the extent to which excess capacity can be
installed, but improvements on other metrics are still possible.


2192 Joule 4, 2185–2207, October 14, 2020


#### **ll**


##### Article



**A**


**B**


**C**



**Cost-optimal solution**

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lombardiPolicyDecisionSupport2020/lombardiPolicyDecisionSupport2020.pdf-9-3.png)


**Low wind concentration**

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lombardiPolicyDecisionSupport2020/lombardiPolicyDecisionSupport2020.pdf-9-7.png)


**High overcapacity**



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lombardiPolicyDecisionSupport2020/lombardiPolicyDecisionSupport2020.pdf-9-4.png)



FR


FR


FR



GR


GR


GR



AT


AT

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lombardiPolicyDecisionSupport2020/lombardiPolicyDecisionSupport2020.pdf-9-8.png)


AT



350


300


250


200


150


100


50


0


350


300


250


200


150


100


50


0


350


300


250


200


150


100



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lombardiPolicyDecisionSupport2020/lombardiPolicyDecisionSupport2020.pdf-9-5.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lombardiPolicyDecisionSupport2020/lombardiPolicyDecisionSupport2020.pdf-9-9.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lombardiPolicyDecisionSupport2020/lombardiPolicyDecisionSupport2020.pdf-9-11.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lombardiPolicyDecisionSupport2020/lombardiPolicyDecisionSupport2020.pdf-9-12.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lombardiPolicyDecisionSupport2020/lombardiPolicyDecisionSupport2020.pdf-9-13.png)

50


0


**Wind and PV** **Electricity curtailment and** **Methane and Hydrogen production and**
**annual production** **energy flow along transmission lines** **methane flow along transmission lines** **Installed capacity**



Pumped hydro
Battery
Gas turbine
Methanation
Electrolysis
Offshore wind
Onshore wind
Rooftop PV
Farm-scale PV
Hydro
Bioenergy



Liquified gas transport


0.6


0 2.2

Annual methane
flow (TWh)



50


0



Curtailed energy (TWh)


0.1 6.4


8.5


0 34.1

Annual electricity
flow (TWh)



5


0



Joule 4, 2185–2207, October 14, 2020 2193


#### **ll**
##### Article

Figure 4. Spatial and Technological Configuration of Three Alternative Results
(A–C) (A) The cost-optimal configuration, (B) the SPORE with the lowest concentration of wind capacity deployed in any one region, and (C) the SPORE
with the highest overcapacity. In each case, we show region-specific total annual PV and wind production (left); electricity flows across transmission lines
connecting zones and the required renewable generation curtailment in those zones (center-left); hydrogen and carbon-neutral methane production in
each zone and the associated methane flows across existing pipelines or maritime transport pathways (center-right); and nationally aggregated
installed technology capacity, including both existing and new plants (right). At the bottom of each column are the legend and scale. Methanation and
electrolysis information is given in units of the output product (synthetic methane and hydrogen, respectively).


Figure 4 gives an overview of the model results for the high overcapacity and low
wind concentration SPORES from Table 1 (for the reference 10% cost relaxation),
compared with the cost-optimal solution. In the cost-optimal result, 19.5% of the total national onshore wind deployment was in the Sardinia region; this decreases to
9.1% in the low wind concentration SPORE; the share at which it already is today.
In this configuration, overall, the highest share for a single region is 12.1%. Part of
this comes from using 86% more offshore wind capacity than in the optimal result
and from deploying more total wind capacity (Figure 4B). This configuration also requires less than one-third of the overall transmission line expansion that the costoptimal solution needs, which is an additional benefit of spreading capacity more
evenly across the country. Similarly, electrolysis and methanation capacity are less
concentrated, reducing the risk of hydrogen production fluctuating due to the
weather pattern of a specific region. Domestic hydrogen production also increases
by 56% without an increase in demand for synthetic methane, leading to additional
available hydrogen for sectors other than power-to-gas.


It is not strictly necessary to depend on additional offshore capacity to avoid large
wind deployment in specific regions. The highest overcapacity SPORE (Figure 4C)
shows a configuration with a similarly low maximum wind capacity share, caused

�
by entirely avoided offshore wind deployment ( 100%) in favor of additional
onshore wind capacity (+24%). This SPORE has higher PV (+18%) and battery
(+85%) capacity expansion, counterbalanced by low transmission line capacity

�
expansion ( 55%) and slightly higher, but more evenly distributed, methanation capacity. Despite increased storage capacity, such a high overall variable renewable
capacity does lead to a non-negligible increase in curtailment (+8%), i.e., peak production exceeding demand, in the reference weather conditions. Curtailment may
be seen as a problem, since it is lost revenue from the perspective of the operators,
but it also signifies overcapacity. Under unfavorable weather conditions, the combination of such overcapacity with a large storage capacity may contribute to
providing the required redundancy for stable system operation (see also Sensitivity
to Cost Projections, Weather, and Demand), given that firm capacity does not
reduce below that given in the cost-optimal solution.


Alternatives to Potentially Problematic Technologies
To entirely avoid unmodeled risks associated with certain potentially problematic
technologies, we ensure that the overall SPORES set includes several system configurations that reduce or entirely eliminate the need for their capacity expansion
(see Experimental Procedures). In particular, additional offshore wind power plants,
utility-scale batteries, and power-to-gas technologies can be entirely excluded from
the system configuration for any cost relaxation in the range 5%–20%, while bioenergy can be fully avoided for cost relaxations larger than 5%, or otherwise
restrained to a marginal increase of only 1 GW. For the reference 10% cost relaxation, Figure 5 shows the effect of minimizing potentially problematic technologies
on PV, wind, power-to-gas, and transmission capacity expansion. It plots the capacity utilization of these technologies against their respective maximum concentration


2194 Joule 4, 2185–2207, October 14, 2020


##### Article

**A**


0.40


0.35


0.30


0.25


0.20


0.15


0.10


**C**


0.5


0.4


0.3


0.2


0.1


0.0

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lombardiPolicyDecisionSupport2020/lombardiPolicyDecisionSupport2020.pdf-11-4.png)


0.0 0.2 0.4 0.6 0.8 1.0

Power-to-gas (turbines) potential utilisation



**B**





0.0 0.2 0.4 0.6 0.8 1.0

Transmission potential utilisation



Cost-optimal solution


Power-to-gas minimised


Bioenergy minimised


Offshore wind minimised


Batteries minimised


Low wind concentration


#### **ll**

Joule 4, 2185–2207, October 14, 2020 2195



Figure 5. Impact of Minimizing the Deployment of Potentially Problematic Technologies
(A–D) On the deployment of PV (A), wind (B), transmission (C), and power-to-gas (D) technologies. All 178
SPORES are plotted on each panel; colored SPORES are those for which the deployment of a potentially
problematic technology is explicitly minimized. Panels compare the maximum share of a technology in
any one region or the minimum capacity factor for a single line for transmission (y axis) against the total
utilized potential expansion capacity (x axis). Gas turbine capacity is used to represent the full power-togas technology chain (comprising electrolysis, methanation with direct air capture, and gas turbines),
since it is the rate limiter for electricity production from synthetic gases. However, since gas turbine
capacity is calculated at the bidding zone level, methanation plant capacity is used to compute regional
concentration. The cost-optimal solution and the ‘‘low wind concentration’’ SPORE (Table 1) are
highlighted with additional markers. The ‘‘low wind concentration’’ SPORE, chosen as a representative
example, features one-third the overall transmission capacity expansion compared with the cost-optimal
case, and a reduced deployment of power-to-gas, but at the expense of an increased deployment of

batteries and offshore wind.


in any specific region (Figures 5A, 5B, and 5D) and for transmission, against the minimum capacity factor for a single transmission line (Figure 5C).


Dynamics of coupled capacity previously seen in Figures 2 and 3 are made more
evident when minimizing specific technology deployment. For instance, eliminating
battery capacity requires a reduction in PV deployment, and zero power-to-gas
infrastructure decreases the possible deployment of wind. Hence, as we have
already seen, some dispatchable technologies are better suited to the generation
profiles of non-dispatchable technologies. Indeed, zero battery capacity actually
leads to an increase in wind power deployment; wind power is balanced instead
by the longer-term storage provided by power-to-gas infrastructure. Overall, a
decrease in any one dispatchable problematic technology requires an increase in
the capacity of others: minimizing battery capacity requires a notable increase in power-to-gas infrastructure. As previously highlighted in Must-Haves and Real
Choices, the minimization of bioenergy, the sole firm capacity option in our model
with margin for capacity expansion, requires proportionally larger deployment of
all other technologies. Finally, the minimization of any potentially problematic technology leads to increased use of transmission lines, which are relatively


#### **ll**

Table 2. Capacity Expansion Potential Utilization of Problematic Technologies


##### Article







|Col1|Capacity Expansion Potential Utilization (%)|Col3|Col4|Col5|
|---|---|---|---|---|
|Subsets with High Transmission<br>Use and Low Wind Concentration<br>for Different Cost Relaxations<br>20% cost relaxation|Bioenergy<br>0–100|Offshore<br>Wind<br>0–95.0|Batteries<br>0–39.7|Power-to-Gas<br>(Turbines)<br>0–64.7|
|10% cost relaxation|100|0–63.3|22.0–<br>36.5|0–17.9|
|5% cost relaxation|100|35.0–44.7|21.1–<br>22.3|18.0–20.9|


For subsets of SPORES that meet the criteria of high transmission use and low wind concentration (as
defined in Table 1). The potential utilization within the subset is given as a range, for increasing accepted

cost relaxations.


underutilized. Similar patterns, though more or less marked depending on the available margin, hold for different accepted cost relaxations (Figure S2). It is clear that
trade-offs between different potentially problematic technologies emerge: to eliminate one, we must rely more on another.


When considering only the SPORES with high transmission utilization and low wind
concentration, bioenergy and battery capacity deployment cannot be avoided
under a 10% cost relaxation (Table 2). If the cost relaxation is limited to 5%, all problematic technologies are components of these SPORES. Accepting a 20% cost relaxation, however, allows us to find SPORES that eliminate all problematic technologies
while keeping wind farm regional concentration low and transmission line use high.
All these problematic technologies can therefore be added to a ‘‘costly to replace’’
category. Cost relaxation can be interpreted as the willingness to pay: when it is high
enough, these technologies can still be avoided. Presenting the trade-offs allows decision-makers to make explicit decisions on them. For example, some may find minimizing imported or harvested bioenergy crop more important than preventing
economically inefficient underutilization use of transmission lines.


Sensitivity to Cost Projections, Weather, and Demand
The SPORES we have considered so far were generated with a cost relaxation in the
range 5%–20% from the cost-optimal solution, for a reference set of technology cost
projections and a single reference year for demand and weather. Here, we investigate the sensitivity of results to cost projections (separated into power-to-gas and
renewables/battery costs), as well as to future demand and weather year (see Experimental Procedures for details).


The SPORES are relatively insensitive to technology costs, particularly to those of
power-to-gas (Figures 6, S7B, S7C, S8B, and S8C). This indicates that the role of power-to-gas infrastructure is somewhat fixed, despite the high uncertainty in the cost
of utility-scale electrolysis and methanation plants resulting from their early stage
of commercialization. The sensitivity to renewable and battery costs is also comparatively low, but there is some movement of the SPORES cluster to higher renewable
capacity utilization when renewable and battery costs are low (Figures 6, S7D, and
S8D). Lower renewable and battery costs also lead to bioenergy becoming avoidable even with a 5% cost relaxation (Table S2), since the large increase in renewable
capacity required to displace bioenergy is now affordable.


Sensitivity to future demand is somewhat greater than that to cost fluctuations (Figures 6, S7F, S7G, S8F, and S8G). The SPORES under demand sensitivity move in a


2196 Joule 4, 2185–2207, October 14, 2020


##### Article

**A**


0.60


0.55


0.50


0.45


0.40


0.35


0.30


0.25



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lombardiPolicyDecisionSupport2020/lombardiPolicyDecisionSupport2020.pdf-13-5.png)

0.0 0.2 0.4 0.6 0.8
Transmission capacity utilisation


#### **ll**

**High power-to-gas cost**

0.60


0.55


0.50


0.0 0.2 0.4 0.6 0.8
Transmission capacity utilisation



**C**



**B**



**Low power-to-gas cost**

0.60


0.55


0.50



Figure 6. Solutions Found across Sensitivity Scenarios
Here, focusing on the reference 10% cost relaxation, we represent the reference set of SPORES (A) in terms of renewable capacity potential utilization
(y axis), transmission potential utilization (x axis), and direct electricity storage capacity potential utilization (color). Further panels show how the set of
SPORES reacts, in terms of these indicators, to changes in the cost projections related to power-to-gas (B and C) or renewables and battery (D and E)


Joule 4, 2185–2207, October 14, 2020 2197


#### **ll**
##### Article

Figure 6. Continued
technologies; the estimated electricity demand in 2050 (F and G); and the reference weather year (H and I). The reference scenario results (A) are
repeated, for comparison, in (B–G) as lightly gray outlined circles. ‘‘low’’ and ‘‘high’’ cost scenarios for power-to-gas (electrolysis, methanation with
direct air capture) and renewables and batteries refer to, respectively, more or less optimistic cost projections for all of the technologies comprised in
each group. ‘‘Low demand’’ refers to a lower projected 2050 demand to that used in the reference scenario, while ‘‘high demand’’ refers to a greater
projected 2050 demand. The reference weather year is 2007, the best weather year is 2010, and the worst weather year is 1989. For details on the
definition of cost projection ranges, generation of demand scenarios, and selection of weather years, see Experimental Procedures. The ‘‘highovercapacity’’ SPORE identified for the reference scenario is marked in (I) for comparison with the set of SPORES generated for unfavorable weather
conditions. The same figure is repeated as Figure S3, substituting color coding for electricity storage with power-to-gas capacity potential utilization,
showing how the former is complementary to the latter: all SPORES minimizing battery are characterized by higher-than-average power-to-gas
deployment, and vice versa, with the exception of the low-demand scenario, which features low deployment of both. The figure is also repeated for 20%
and 5% cost (Figures S7 and S8) showing that similar trends hold.


similar, if not more exaggerated direction to that seen when changing renewables
and battery costs. Lower demand leads to lower overall storage utilization (and similarly low power-to-gas deployment, see Figure S3). Higher demand calls for a visibly
stronger reliance on both renewables and storage capacity deployment. Yet,
although the cost-optimal solution expects a 5% increase in total renewables capacity under high demand, SPORES still exist with renewables deployment on the scale

�
of the reference scenario cost-optimal solution ( 40% total renewables capacity
utilization).


Weather year choice has the strongest effect on the entire SPORES set, strongly and
visibly shifting all results, particularly along the renewable capacity and storage capacity utilization axes (Figures 6, S7H, S7I, S8H, and S8I). These are the same features sought by the ‘‘high overcapacity’’ configuration; we see an overlap in system
configurations between the reference and bad weather scenarios, as long as cost
relaxation is greater than 5% (Figures 6I, S7I, and S8I). For a system to be able to
deal with adverse weather conditions, additional renewable and battery capacity
is essential—with a preference for configurations relying more on local PV over
long-distance wind production. The additional capacity of renewables is tightly
coupled to electrical storage, with power-to-gas capacity, actually reducing across
all SPORES in the low-wind, worst weather year case (Figure S3).


We see in Figure 6 that there is a degree of overlap in the renewable and transmission capacity utilization between the reference scenario and the sensitivity scenarios.
This overlap increases with cost relaxation (Figures S7 and S8); an increased willingness to pay increases the likelihood of choosing a system configuration, which is
valid on different realizations of uncertain parameters. Otherwise, the choice of
cost relaxation does not qualitatively change the aforementioned results (Figures
S4–S8). The previously studied low wind concentration SPORE (see Table 1) is
among those with similarities to some sensitivity scenario SPORES. They are characterized by similar spatial and technological capacity expansion strategies, only with
slightly different absolute per-region capacities (Figure S9). For a 10% cost relaxation, the largest oversizing relative to the low wind concentration SPORE from the
reference scenario is up to 21 GW (+28%) of additional PV capacity and +6 GW
(+40%) of additional battery capacity in the NORD bidding zone. This is complemented by a reduction in total wind capacity and no change in firm capacity. All of
these systems are still close to optimal cost; their higher cost compared with the
optimal solution could simply be interpreted as the cost of incorporating uncertainty
in decision making. This is an additional benefit of the SPORES approach: the same
analysis undertaken with only the conventional, cost-optimal solution would suggest
greater sensitivity in many scenarios, failing to communicate the existence of configurations that can adapt to unfavorable conditions with only minor changes in the system configuration, albeit at a marginally higher system cost.


2198 Joule 4, 2185–2207, October 14, 2020


##### Article

DISCUSSION


We have argued that by relying on a cost-optimal energy system configuration, a
range of implicit trade-offs are made leading the solution to suffer from problematic
features: spatially unbalanced distribution of infrastructure deployment; an overfitting of variable renewable generation and storage capacity to a specific weather
year; high transmission expansion, resulting in low and economically unattractive
utilization of transmission lines; and reliance on potentially problematic technologies. All of these are issues not just for the Italian case we examine here but generally
for cost-optimal model results across Europe and globally. [31][,][34][,][36] We demonstrate
that the SPORES approach is able to address these issues by generating and systematically analyzing a wide range of spatially explicit, practically optimal model results.
With a marginally increased willingness to pay (5%–20%) above a globally minimized
cost, it is possible for any stakeholder to find system configurations that meet their
otherwise unmodeled objectives.


Using SPORES, it is possible to identify technologies that are must-haves: they are present in all generated alternative energy system pathways. It is also possible to identify
those technologies, which are costly to replace; a higher cost relaxation, which can be interpreted as a higher willingness to pay, is required for system configurations without
thesetechnologies.Inthe contextofItaly,PVisfoundtobeamust-have,whilebioenergy,
batteries,and international transmission emerge as the costliest to replace. Furthermore,
it is possible to identify complementary and competitive technologies: local generation
viaPVandbatteryoftencompeteswithlong-distancewindgenerationcoupledtopowerto-gas,butthetwobecomecomplementaryforhigh(upto20%)willingnesstopay.Thisis
likely to apply similarly to other sunny countries, but more work is needed to verify
whether such dynamics and potentially problematic technologies are similar elsewhere,
e.g., in windier and less sunny regions. The important general implication is that our
approach can quantify the monetary trade-offs in deciding between such technologies,
allowingdecision-makerstomakeexplicitdecisionsonthem.Forinstance,removing bioenergy from the Italian system requires a 10-fold increase in renewable capacity. This
trade-off between firm capacity and renewables has also been identified in the context
of the US. 19 Nevertheless, it would be premature to conclude that highly renewable energy systems should therefore always include bioenergy. Bioenergy deployment could
face greater public opposition than wind turbines, [25] require at least two orders of magnitude more dedicated land use per installed MW [38] and where biomass is imported clash
with demands for energy independence. [39] Thus, again, a range of trade-offs influence
the degree to which a specific decision-maker may wish to rely on bioenergy. Thus, the
full decision space of feasible energy pathways should be presented. The SPORES
method exposes this decision space in a way that can be used by decision-makers to better balance engineering criteria with social and political considerations.


Notably, for regions that have many interconnections and can take advantage of
multiple and diverse weather patterns, bioenergy’s firm capacity is often substituted
(even for limited willingness to pay) by a combination of geographically close PV and
gas turbine capacity and wind farms located further away and connected via transmission lines and gas pipelines. This buffering role could be played by topologically
central zones in other geographic contexts. For example, capitalizing on the anticorrelation of wind patterns across Europe requires balancing wind turbine generation
across the continent’s north-south divide. [37]


The feasible space of technology deployment is not only restricted to a study of
must-have technologies and technologies that are costly to replace but also to an


#### **ll**

Joule 4, 2185–2207, October 14, 2020 2199


#### **ll**

analysis of the capacity ranges. We find that some of Italy’s existing gas turbine capacity is likely to play a role in a decarbonized energy system. However, their prevalence across most SPORES is low, rarely going above about 20%, and never above
60% utilization of today’s capacity. This exposes the technical limits associated with
supporting a high penetration of variable renewables with synthetic fuels and legacy
infrastructure, a finding which is pertinent across Europe, where natural gas still plays
a large role. [40] Irrespective of one’s expectations for synthetic fuels, any additional
investment into gas infrastructure to support power generation now seems
misguided.


By undertaking a sensitivity analysis, we show that the limited dependence on power-to-gas infrastructure is insensitive to technology cost projections. We also
demonstrate that by performing such an analysis across a set of SPORES, rather
than only for a cost-optimal solution, overlaps can be found in which system configurations remain valid under different sensitivity scenarios, particularly for increasing
willingness to pay. Indeed, our results seem to suggest that, for systems with limited
potential for the expansion of firm capacity, an overcapacity of renewables and storage, with a preference for local over long-distance generation, may help bridge the
gap with adverse weather years.


Although an expanded understanding of energy system configurations is possible
with the SPORES approach, it has scope for further enhancement. Additional
flexibility mechanisms, such as endogenous demand side management—here,
considered exogenously and only to a limited extent (see Experimental Procedures)—could be investigated in more detail, which may reveal technology mixes
that can partially mitigate the overall necessity of storage. Our current results with
respect to the need for batteries and power-to-gas could be considered a conservative scenario, in which demand side and sectoral integration flexibility options are
accounted for in the generation of realistic load profiles but without assuming the
possibility of system-wide real-time control and optimization of pools of energy
users by an aggregator. Additionally, our sensitivity analyses show that, although
the cost-optimal solution can be heavily sensitive to variations in uncertain parameters like cost projections, or choice of weather year and future demand, the existing
SPORES approach alludes to feasible deployment patterns which are similar across
all sensitivity sets. Identifying these SPORES could be improved with techniques that
embed input parameter uncertainty into the problem formulation, such as stochastic
optimization. The resulting ‘‘resilient SPORES’’ would consider unmodeled objectives and uncertain parameters simultaneously.


Even in its current state, the SPORES approach creates a rich set of alternatives for
consideration in the policy-making process, enhancing the relevance of energy system models as tools in the transformation to full decarbonization. To help with the urgent task of planning socially and politically acceptable energy system decarbonization strategies, our implementation of SPORES in the open-source energy systems
modeling framework Calliope makes it accessible to a wide range of potential users.


EXPERIMENTAL PROCEDURES


Resource Availability
Lead Contact

Further information and requests for resources and materials should be directed to
[and will be fulfilled by the Lead Contact, Francesco Lombardi (francesco.lombardi@](mailto:<EMAIL>)
[polimi.it).](mailto:<EMAIL>)


2200 Joule 4, 2185–2207, October 14, 2020


##### Article


##### Article

Materials Availability
This study did not generate new unique materials.


Data and Code Availability
All the code and input data required to reproduce this study have been deposited to
[https://doi.org/10.5281/zenodo.3903089 and also available at GitHub (https://](https://doi.org/10.5281/zenodo.3903089)
[github.com/FLomb/Calliope-Italy).](https://github.com/FLomb/Calliope-Italy)


SPORES Approach
The core of the SPORES approach consists of a spatially explicit extension to the
‘‘modeling to generate alternatives’’ (MGA) method [12] with the specific goal of being embedded in an energy system model with high spatial resolution. To do so,
we work with the open-source energy modeling framework Calliope, [22] designed to
model systems with high shares of variable renewable generation using high
spatial and temporal resolution. An exhaustive discussion of Calliope’s mathematical formulation is provided by Pfenninger and Keirstead, [41] and the complete code
[is available online at https://github.com/calliope-project/calliope. We use Calliope](https://github.com/calliope-project/calliope)
to build a model of the Italian power system, as described further below in the
Experimental Procedures. The implementation of the SPORES algorithm is freely
and openly available from the Calliope-Italy model repository. [42] We now discuss
the algorithm for the computation of SPORES (which is also summarized in
Figure S10).


Finding the Global Optimum
The global optimum of the optimization problem is sought by minimizing the discounted financial system cost, as reported in Equation 1.



(Equation 1)



!



min : cost = XX



c fix;ij x ijcap + Xc var;ij x tprod;ij


t



j



i


#### **ll**

Joule 4, 2185–2207, October 14, 2020 2201



s:t: Ax%b


xR0;



where i and j indicate the ith technology type and the jth sub-location within the
considered spatial domain; x ij [cap] is the decision variable related to the installed capacity of the ij-th location-technology combination (hereafter abbreviated as
loc::tech, following Calliope’s nomenclature); x ij [prod] ;t is the decision variable related
to the power production of the ij-th loc::tech as a function of time; c fix;ij, c var,ij are,
respectively, the discounted financial fixed and variable costs per each loc::tech;
A, b are a matrix and a vector of coefficients used to build all the physical constraints
in combination with the vector x of all decision variables.


Assigning Weights
A strictly positive weight ðw ij [n] [Þ][ is assigned to those decision variables which assume]
non-zero values in the cost-optimal solution, namely loc::techs for which a non-zero
capacity is installed. The scoring on capacity (rather than production) decision variables allows us to define an ad-hoc scoring logic that assigns a weight to each
loc::tech that is equivalent to the ratio between the installed and the maximum theoretically installable capacity. This score is then combined with the weight obtained in
the preceding iteration ðw ij [n][�][1] Þ, for any iteration different from the initial one (Equation 2). This approach differs from the original MGA formulation, which adopts an
integer weighting logic (+1 for each non-zero decision variable).


#### **ll**


##### Article



w ij [n] [=][ w] ij [n][�][1] +



xx ij ~~cap~~ icapj;max;n (Equation 2)



In fact, in the framework of the 2050 fully renewable horizon of interest, most of the
available loc::techs are expected to experience some investments in capacity
since the initial model run, and to thus have non-zero weights. Accordingly, a
classic weighting scheme based on integer scoring would likely produce very
similar weights across multiple loc::techs, complicating (though not precluding)
the search for maximally different alternatives. On the contrary, assigning the
weight based on the maximum theoretical potential for each loc::tech is expected
to penalize more those locations that are fully utilized in previous iterations, favoring an earlier unlocking of poorly utilized locations. This choice follows that indicated by previous work, [16] that the specific needs and purposes of the study and
the related model formulation should guide the identification of a weighting logic
that is most effective in providing meaningful alternatives. In fact, the code implementation allows the modeler to customize the weighting logic via a dedicated

function.


Generating SPORES
A SPORE is obtained by minimizing the sum of location-specific weighted capacity
decision variables in each loc::tech, while constraining the cost of the current model
run (cost n ) to remain in the accepted neighborhood of the optimal cost (cost t ), as reported in Equation 3.



min Y =
X



w ij x ijcap
i



X



j



s:t: cost n %ð1 + sÞ,cost 0


Ax%b


xR0;



(Equation 3)



where s is the accepted relaxation or slack. Previous work [8] has shown that policy
makers are willing to accept cost increases up to 30%. The benefit of a more
expensive alternative configuration is that a range of results can be produced,
some of which may meet other, unmodeled objectives. Here, we choose a more
conservative slack value of 10% and test sensitivity to larger (20%) or smaller
(5%) values. New weights are computed based on the new configuration obtained
as per Equation 2, and an arbitrary number of SPORES can be generated (50 in this
study).


The formulation in Equation 3 characterizes the problem as an ε-constrained multiobjective optimization, with minimization of already explored decision variables as
a primary objective and cost as a secondary one. As noted elsewhere, [16] this may
complicate the search for configurations which fully exclude pivotal cost-lowering
technological options. Further, the inclusion of the spatial dimension leads to a
multiplication of the possible maximally different system configurations, compared
with existing conventional near-optimal solutions methods, thus, exacerbating the
problem. To overcome this potential bottleneck, we generate a further set of
SPORES that explicitly seek the minimization of capacity for each considered
loc::tech. Up to 3 alternative configurations (a number that is increased to 20 for
the special case of ‘‘potentially problematic technologies,’’ as defined in the
main text) are explored in each case, adopting the formulation reported in
Equation 4.


2202 Joule 4, 2185–2207, October 14, 2020


##### Article



ijcap + b,X



Xw ij x ijcap

i



min Y 2 = a,x cap



j



s:t: cost n %ð1 + sÞ,cost 0


Ax%b


xR0;



(Equation 4)


#### **ll**

Joule 4, 2185–2207, October 14, 2020 2203



where x [cap] isthe capacityinvestmentdecisionvariableassociatedwiththe loc::tech under

ij
minimization, and a and b are the weights associated to the different components of the
objective function, in this case 10 and 0.1, respectively. The approach produces (for the
modelconfigurationadoptedinthisstudy)atotalof178SPORESforeachscenario.Inthis
study, comprising 27 independent scenarios (considering the different cost relaxations
and the sensitivity analysis),we generate a total of 4,806 independent solutions.The generation of such a wide range of feasible options represents an unprecedented effort for a
model of high spatial and temporal detail [19] and aims at significantly reducing the ‘‘structural uncertainty’’ of energy modeling, i.e., the gap between what can be modeled and
what is in reality irreducibly uncertain and non-modellable. [13] In fact, SPORES avoid the
risk of incorrectly deciding what is of interest to stakeholders, and what is not, rather
providing a full range of maximally different feasible spatial configurations of technology
deployment for evaluation (including sensitivity scenarios).


The 20-Region Italian Power System Model
The Italian power sector is organized into six geographical bidding zones: NORD,
CNOR, CSUD, SUD, SARD, and SICI. Bidding zones are used to regulate exchanges
on the electricity market, and inter-zonal connections between them constitute the
core of the national transmission network, as well as the most critical transmission capacity bottlenecks. The national transmission system operator, Terna, only provides
electricity demand data for these bidding zones, so this is the most feasible scale on
which to model the Italian power system. [43][,][44]


However, some of these bidding zones comprise a large number of political regions
(e.g., NORD consists of eight regions), associated with significantly different renewable electricity generation potential and different local political conditions, which
might encourage or hinder wind or solar power deployment. Therefore, we propose
a double-scale spatial representation of the Italian power sector, as shown in Figure S11, in which electricity demand profiles, dispatchable power production/storage plants, and inter-zone transmission lines are characterized at the bidding zone
level, but renewable electricity generation capacity (i.e., rooftop photovoltaic,
farm-scale photovoltaic, onshore wind, and offshore wind) and pumped hydroelectric storage (PHS) are characterized at NUTS2 (i.e., regional) administrative level. The
administrative regions contained within a bidding zone are connected to a central
bidding zone demand node by virtual unconstrained transmission lines.


Electricity Demand in 2050
Significant changes in the electricity demand are expected in coming decades,as a result
of an increasing and substantial electrification of sectors, such as heat and transport. Estimating the resulting additional electricity demand is difficult since it will depend on the
degree of electrification and on the absolute evolution of national energy consumption
as a function of efficiency improvements and economic growth. To address this, we
rely on the partial-decomposition methodology by Boßman and Staffel, [45] implemented
in the open-source demand model DESSTinEE. First, it characterizes all energy sectors of
a given country, disaggregated into residential and industrial heat, in addition to several
formsoftransport.Itdoessowithspecificdemandprofiles,insomecasesresultingfroma
weighted average of several real-life relevant behaviors (e.g., smart and conventional


#### **ll**

charging strategies of electric vehicles), subsequently re-aggregating them into a future
electricity demand curve by applying selected degrees of electrification and power-to-X
conversion factors.The assumptionsusedtodosocanbearbitrarily manipulatedorgathered from a set of pre-built scenarios based on outlook by international organizations.
Here, we generate a possible evolution of the aggregated Italian electricity demand profile, based on the pre-built ‘‘GEA-Efficiency’’ (GEA-Eff) scenario, which assumes large degrees of electrification of other energy sectors coupled with energy efficiency measures,
andanoveralldecarbonizationoftheeconomyconsistentwiththeParisAgreementgoals
(although energy uses that are not-electrified by 2050 are left outside the domain of analysis). We transpose the hour-by-hour relative increase in country-aggregated electricity
demand profiles between DESSTinEE’s 2050 projections and baseline (2010) synthetic
profile onto the actual electricity demand profile gathered from ENTSO-E, for the reference year 2015, as shown in Figure S12. Finally, we re-allocate the country-aggregated
relative increase in hour-by-hour demand to each bidding zone’s electricity profile, as
based on the Terna zonal demand data, following a proportionality criterion.


Considering the high uncertainty associated with estimating demand, we include
demand in our sensitivity analysis. We consider as an upper bound for sensitivity
the International Energy Agency’s 2 [�] scenario (high demand)—based on similar assumptions to GEA-Eff but with less focus on efficiency measures—and, as a lower
bound, the ‘‘low-growth’’ (low demand) pre-defined DESSTinEE scenario, in which
the demand increase associated with economic expansion is limited. Further details
about load profile variations across scenarios are reported in Figure S13.


Power Generation in 2050

The characterization of existing power plants and inter-zone or international transmission
lines with the required level of spatial disaggregation is realized on the basis of datasets
provided by the Terna [46] and ‘‘Gestore dei Servizi Energetici’’ (GSE) [23] for the reference
year 2015, as well as building on the data from Lombardi et al. [47] for a single-zone Italian
power system model based on Calliope. We only consider technologies that are relevant
for a decarbonized 2050 system, which includes hydroelectric (disaggregated into reservoir/basin, run of river, and pumped hydro storage types), geothermal, biomass (solid,
biogas, biofuel, and waste-to-energy) and variable renewable (onshore wind, rooftop
PV, and farm-scale PV) power plants. In addition, we consider the existing large fleet of
combined-cycle gas turbine plants and the gas network infrastructure, which includes
intra- and inter-zone connections and a significant capacity for long-term gas storage,
with the restriction that these can only be supplied by carbon-neutral methane produced
via electrolysis and methanation with direct air capture. In agreement with the most
recent literature, [40][,][48] this is preferred to the usage of such turbines with fossil fuels and
carbon capture and storage, which we do not consider. We consider all of these technologies to be emissions free and do not explicitly model embedded or lifecycle emissions.
This assumption is uncertain for bioenergy in particular, which is why we include it among
the potentially problematic technologies. Existing capacities of such technologies are
considered to be kept in operation or repowered at the same sites and economically
paid off. The corresponding location-specific capacities for generation technologies
and for transmission lines are reported in Tables S3–S5, as well as openly available at
the online repository containing the model used in this study.


Furthermore, capacity expansion of existing plants or new installations are allowed
for those technologies, which are part of already planned projects, planned in the
most recent national energy strategy (SEN), [49] or identified as pivotal in most recent
decarbonization studies. [36] Those include four new technology types, namely
offshore wind, battery storage, electrolysis, and methanation with direct air capture,


2204 Joule 4, 2185–2207, October 14, 2020


##### Article


##### Article

as listed in Table S6 with their respective fixed and variable costs and lifetime estimates. Variable renewable and PHS reference costs are gathered from a country
study, [50] while, for battery costs, we assume lithium-ion batteries and costs from
Schmidt et al. [51] All such costs are nonetheless made subject to sensitivity based
on most recent literature cost ranges [19][,][20][,][52][,][53] (Table S7). A specific, in depth literature analysis is dedicated to electrolysis and methanation with direct air capture
costs (Figure S14) and related sensitivity ranges [32][,][54–60] (Table S7). Costs for transmission lines depend on the type and length of connection. We use per-length costs
come from the real unitary costs declared by the transmission system operator for
existing or planned transmission capacity expansion projects for location-specific inter-zone or international connections. [24] Based on this, Table S6 reports specific
costs for the expansion of each transmission line.


The spatially explicit characterization of the generation potential for variable renewable generation consists of per-region capacity factor time series and per-region
maximum capacity limits. For the capacity factors, we rely on Pfenninger and
Staffell [61][,][62] whose bias-corrected wind and PV simulations for all European NUTS2
[regions are openly available through www.renewables.ninja. The spatial disaggre-](http://www.renewables.ninja)
gation into the 20 Italian NUTS2 regions allows to clearly account for significantly
different renewable source availability profiles in each location (Figure S15), hence,
enabling a spatially explicit optimization of investments in additional capacity. An
exception is represented by offshore wind potentials, only available at NUTS1 resolution and thus uniformly assigned to all coastal regions which have non-zero
offshore wind theoretical potentials. Finally, it is worth noting that all variable renewable generation potentials are significantly influenced by choice of weather year, as
shown in Figure S16. By running the model for each weather year and calculating
how the latter affects the minimum cost configuration, we select the year 2016 as
the most-typical reference weather year for Italy, while we identify 2010 and 1989
as the best and the worst cases, respectively, for sensitivity analysis purposes.


Maximum theoretical potentials for renewable capacity installation in each region are
derived from the open renewable potential datasets from Tro¨ ndle et al., [63] again for all
EU countries and with NUTS2 resolution, based on a GIS analysis of suitable installation
sites for rooftop PV, farm-scale (open-field) PV, onshore wind, and offshore wind. In
particular, the study distinguishes between ‘‘technical’’ and ‘‘technical-social’’ potentials,
with farmlands and protected areas excluded from the count in the second case. For this
study, we rely on the more conservative technical-social potentials, considering also that
the Italian legislation forbids renewable capacity deployment in protected areas. Overall
renewables potentials constrain the maximum installable electrolysis and, in turns,
methanation capacity in each location. Potentials for additional PHS capacity from
Terna; [24] to which we apply a ratio of 100 kWh of PHS storage per kW of PHS power production capacity(correspondingtothe average ratio for the currentPHS fleet).The resulting potentials, expressed as MW of additional capacity, are reported in Table S8. Finally,
maximum potentials for the capacity expansion of each individual international or interzone transmission line are conservatively set, respectively, to +1 and +5 GW, in line with
the current plans of Terna. Biomass potential is estimated as an additional system-wide 4
GW, and limited to biogas power plants, following the plans reported in the national energy strategy. [49]


SUPPLEMENTAL INFORMATION


[Supplemental Information can be found online at https://doi.org/10.1016/j.joule.](https://doi.org/10.1016/j.joule.2020.08.002)

[2020.08.002.](https://doi.org/10.1016/j.joule.2020.08.002)


#### **ll**

Joule 4, 2185–2207, October 14, 2020 2205


#### **ll**

ACKNOWLEDGMENTS


Numerical calculations were performed on the ETH Euler cluster.


AUTHOR CONTRIBUTIONS


Conceptualization, F.L., B.P., and S.P.; Methodology, F.L., B.P., and S.P.; Software,
F.L. and B.P.; Investigation, F.L. and B.P.; Writing – Original Draft, F.L.; Writing – Review & Editing, F.L., B.P., E.C., and S.P.; Visualization, F.L., B.P., and S.P.


DECLARATION OF INTERESTS


The authors declare no competing interests.


Received: January 9, 2020
Revised: April 28, 2020
Accepted: July 31, 2020
Published: August 24, 2020


REFERENCES


##### Article

[system model. arXiv, arXiv:1910.01891](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref18)

[[physics].](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref18)


[19. Sepulveda, N.A., Jenkins, J.D., de Sisternes,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref19)
[F.J., and Lester, R.K. (2018). The role of firm](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref19)
[low-carbon electricity resources in deep](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref19)
[decarbonization of power generation. Joule 2,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref19)
[2403–2420.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref19)


[20. Brown, T., Schlachtberger, D., Kies, A.,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref20)
[Schramm, S., and Greiner, M. (2018). Synergies](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref20)
[of sector coupling and transmission](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref20)
[reinforcement in a cost-optimised, highly](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref20)
[renewable European energy system. Energy](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref20)
[160, 720–739.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref20)


[21. Drechsler, M., Egerer, J., Lange, M.,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref21)
[Masurowski, F., Meyerhoff, J., and Oehlmann,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref21)
[M. (2017). Efficient and equitable spatial](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref21)
[allocation of renewable power plants at the](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref21)
[country scale. Nat. Energy 2, 17124.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref21)


[22. Pfenninger, S., and Pickering, B. (2018).](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref22)
[Calliope: a multi-scale energy systems](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref22)
[modelling framework. J. Open Source Softw. 3,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref22)
[825.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref22)


23. Gestore dei Servizi. (2018). Energetici (GSE)
Rapporto statistico - Energia da fonti
[rinnovabili in Italia. https://www.gse.it/](https://www.gse.it/documenti_site/Documenti%20GSE/Rapporti%20statistici/GSE%20-%20Rapporto%20Statistico%20FER%202018.pdf)
[documenti_site/Documenti%20GSE/Rapporti](https://www.gse.it/documenti_site/Documenti%20GSE/Rapporti%20statistici/GSE%20-%20Rapporto%20Statistico%20FER%202018.pdf)
[%20statistici/GSE%20-%20Rapporto%](https://www.gse.it/documenti_site/Documenti%20GSE/Rapporti%20statistici/GSE%20-%20Rapporto%20Statistico%20FER%202018.pdf)
[20Statistico%20FER%202018.pdf.](https://www.gse.it/documenti_site/Documenti%20GSE/Rapporti%20statistici/GSE%20-%20Rapporto%20Statistico%20FER%202018.pdf)


24. Terna (Italian TSO). (2019). Piano di sviluppo
della rete di trasmissione nazionale, 2019
[Edizione. https://download.terna.it/terna/](https://download.terna.it/terna/0000/1188/36.PDF)
[0000/1188/36.PDF.](https://download.terna.it/terna/0000/1188/36.PDF)


[25. Liebe, U., and Dobers, G.M. (2019).](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref25)
[Decomposing public support for energy](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref25)
[policy: what drives acceptance of and](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref25)
[intentions to protest against renewable energy](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref25)
[expansion in Germany? Energy Res. Soc. Sci.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref25)
[47, 247–260.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref25)


[26. Giordono, L.S., Boudet, H.S., Karmazina, A.,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref26)
[Taylor, C.L., and Steel, B.S. (2018). Opposition](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref26)
[‘‘overblown’’? Community response to wind](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref26)
[energy siting in the Western United States.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref26)
[Energy Res. Soc. Sci. 43, 119–131.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref26)



1. Masson-Delmotte, V., Zhai, P., Po¨ rtner, H.,
Roberts, D., Skea, J., Shukla, P., Pirani, A.,
Moufouma-Okia, W., Pe´ an, C., Pidcock, R.,
et al. (2018). Global warming of 1.5 [�] C. An IPCC
Special Report on the impacts of global
warming of 1.5 [�] C above pre-industrial levels
and related global greenhouse gas emission
pathways, in the context of strengthening the
global response to the threat of climate
change, sustainable development, and efforts
[to eradicate poverty. https://www.ipcc.ch/site/](https://www.ipcc.ch/site/assets/uploads/sites/2/2019/06/SR15_Full_Report_Low_Res.pdf)
[assets/uploads/sites/2/2019/06/](https://www.ipcc.ch/site/assets/uploads/sites/2/2019/06/SR15_Full_Report_Low_Res.pdf)
[SR15_Full_Report_Low_Res.pdf.](https://www.ipcc.ch/site/assets/uploads/sites/2/2019/06/SR15_Full_Report_Low_Res.pdf)


[2. Jacobson, M.Z., Delucchi, M.A., Cameron,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref2)
[M.A., and Frew, B.A. (2015). Low-cost solution](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref2)
[to the grid reliability problem with 100%](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref2)
[penetration of intermittent wind, water, and](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref2)
[solar for all purposes. Proc. Natl. Acad. Sci.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref2)
[USA 112, 15060–15065.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref2)


[3. Collins, S., Deane, P., O](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref3) [´ ] Gallacho´ ir, B.O [´ ] .,
[Pfenninger, S., and Staffell, I. (2018). Impacts](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref3)
[of inter-annual wind and solar variations on](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref3)
[the European power system. Joule 2, 2076–](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref3)
[2090.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref3)


[4. Brown, T.W., Bischof-Niemz, T., Blok, K.,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref4)
[Breyer, C., Lund, H., and Mathiesen, B.V.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref4)
[(2018). Response to ‘burden of proof: a](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref4)
[comprehensive review of the feasibility of 100%](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref4)
[renewable-electricity systems’. Renew. Sustain.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref4)
[Energy Rev. 92, 834–847.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref4)


[5. Ellenbeck, S., and Lilliestam, J. (2019). How](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref5)
[modelers construct energy costs: discursive](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref5)
[elements in energy system and integrated](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref5)
[assessment models. Energy Res. Soc. Sci. 47,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref5)
[69–77.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref5)


[6. Pfenninger, S., Hawkes, A., and Keirstead, J.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref6)
[(2014). Energy systems modeling for twenty-](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref6)
[first century energy challenges. Renew.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref6)
[Sustain. Energy Rev. 33, 74–86.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref6)


[7. Levi, P.J., Kurland, S.D., Carbajales-Dale, M.,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref7)
[Weyant, J.P., Brandt, A.R., and Benson, S.M.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref7)
[(2019). Macro-energy systems: toward a new](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref7)
[discipline. Joule 3, 2282–2286.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref7)


[8. Trutnevyte, E. (2016). Does cost optimization](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref8)
[approximate the real-world energy transition?](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref8)
[Energy 106, 182–193.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref8)


2206 Joule 4, 2185–2207, October 14, 2020



[9. Heuberger, C.F., Staffell, I., Shah, N., and Mac](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref9)
[Dowell, N.M. (2018). Impact of myopic](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref9)
[decision-making and disruptive events in](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref9)
[power systems planning. Nat. Energy 3,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref9)
[634–640.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref9)


[10. Saltelli, A., and Giampietro, M. (2017). What is](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref10)
[wrong with evidence based policy, and how](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref10)
[can it be improved? Futures 91, 62–71.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref10)


[11. Carrozza, C. (2015). Democratizing expertise](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref11)
[and environmental governance: different](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref11)
[approaches to the politics of science and their](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref11)
[relevance for policy analysis. J. Environ. Policy](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref11)
[Plan. 17, 108–126.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref11)


[12. DeCarolis, J.F. (2011). Using modeling to](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref12)
[generate alternatives (MGA) to expand our](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref12)
[thinking on energy futures. Energy Econ. 33,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref12)
[145–152.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref12)


[13. Yue, X., Pye, S., DeCarolis, J., Li, F.G.N., Rogan,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref13)
[F., and Gallacho´ ir, B.O](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref13) [´ ] . (2018). A review of
[approaches to uncertainty assessment in](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref13)
[energy system optimization models. Energy](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref13)
[Strategy Rev. 21, 204–217.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref13)


[14. DeCarolis, J., Daly, H., Dodds, P., Keppo, I., Li,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref14)
[F., McDowall, W., Pye, S., Strachan, N.,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref14)
[Trutnevyte, E., Usher, W., et al. (2017).](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref14)
[Formalizing best practice for energy system](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref14)
[optimization modelling. Appl. Energy 194,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref14)
[184–198.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref14)


[15. Berntsen, P.B., and Trutnevyte, E. (2017).](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref15)
[Ensuring diversity of national energy scenarios:](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref15)
[bottom-up energy system model with](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref15)
[modeling to generate alternatives. Energy 126,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref15)
[886–898.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref15)


[16. DeCarolis, J.F., Babaee, S., Li, B., and Kanungo,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref16)
[S. (2016). Modelling to generate alternatives](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref16)
[with an energy system optimization model.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref16)
[Environ. Modell. Softw. 79, 300–310.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref16)


[17. Price, J., and Keppo, I. (2017). Modelling to](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref17)
[generate alternatives: a technique to explore](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref17)
[uncertainty in energy-environment-economy](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref17)
[models. Appl. Energy 195, 356–369.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref17)


[18. Neumann, F., and Brown, T. (2019). The near-](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref18)
[optimal feasible space of a renewable power](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref18)


##### Article

[27. Burger, S.P., Jenkins, J.D., Huntington, S.C.,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref27)
[and Perez-Arriaga, I.J. (2019). Why distributed?:](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref27)
[A critical review of the tradeoffs between](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref27)
[centralized and decentralized resources. IEEE](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref27)
[Power and Energy Mag 17, 16–24.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref27)


28. Stigler, H., Fickert, L., Muhr, H.M., Renner, H.,
Nischler, G., Brandauer, W., Wakolbinger, C.,
Bachhiesl, U., Nacht, T., Hu¨ tter, D., et al. (2012).
Gutachten zur Ermittlung des erforderlichen
Netzausbaus im deutschen U [¨ ] bertragungsnetz
[2012. https://graz.pure.elsevier.com/en/](https://graz.pure.elsevier.com/en/publications/gutachten-zur-ermittlung-des-erforderlichen-netzausbaus-im-deutsc)
[publications/gutachten-zur-ermittlung-des-](https://graz.pure.elsevier.com/en/publications/gutachten-zur-ermittlung-des-erforderlichen-netzausbaus-im-deutsc)
[erforderlichen-netzausbaus-im-deutsc.](https://graz.pure.elsevier.com/en/publications/gutachten-zur-ermittlung-des-erforderlichen-netzausbaus-im-deutsc)


[29. Thomas, H., Marian, A., Chervyakov, A.,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref29)
[Stu¨ ckrad, S., and Rubbia, C. (2016). Efficiency of](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref29)
[superconducting transmission lines: an analysis](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref29)
[with respect to the load factor and capacity](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref29)
[rating. Electr. Power Syst. Res. 141, 381–391.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref29)


[30. Rivier, M., Pe´ rez-Arriaga, I.J., and Olmos, L.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref30)
[(2013). Electricity transmission. In Regulation of](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref30)
[the Power Sector Power Systems and I, J.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref30)
[Pe´ rez-Arriaga, ed. (Springer), pp. 251–340.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref30)


[31. Davis, S.J., Lewis, N.S., Shaner, M., Aggarwal,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref31)
[S., Arent, D., Azevedo, I.L., Benson, S.M.,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref31)
[Bradley, T., Brouwer, J., Chiang, Y.-M., et al.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref31)
[(2018). Net-zero emissions energy systems.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref31)
[Science 360, eaas9793.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref31)


[32. Fasihi, M., Efimova, O., and Breyer, C. (2019).](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref32)
[Techno-economic assessment of CO2 direct](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref32)
[air capture plants. J. Clean. Prod. 224, 957–980.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref32)


[33. Reichardt, K., Negro, S.O., Rogge, K.S., and](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref33)
[Hekkert, M.P. (2016). Analyzing](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref33)
[interdependencies between policy mixes and](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref33)
[technological innovation systems: the case of](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref33)
[offshore wind in Germany. Technol. Forecast.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref33)
[Soc. Change 106, 11–21.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref33)


[34. Sokoloski, R., Markowitz, E.M., and Bidwell, D.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref34)
[(2018). Public estimates of support for offshore](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref34)
[wind energy: false consensus, pluralistic](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref34)
[ignorance, and partisan effects. Energy Policy](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref34)
[112, 45–55.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref34)


[35. Wu, Y., Hu, Y., Lin, X., Li, L., and Ke, Y. (2018).](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref35)
[Identifying and analyzing barriers to offshore](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref35)
[windpowerdevelopmentinChinausingthegrey](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref35)
[decision-making trial and evaluation laboratory](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref35)
[approach. J. Clean. Prod. 189, 853–863.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref35)


[36. Jenkins, J.D., Luke, M., and Thernstrom, S.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref36)
[(2018). Getting to zero carbon emissions in the](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref36)
[electric power sector. Joule 2, 2498–2510.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref36)


[37. Grams, C.M., Beerli, R., Pfenninger, S., Staffell,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref37)
[I., and Wernli, H. (2017). Balancing Europe’s](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref37)
[wind power output through spatial](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref37)
[deployment informed by weather regimes.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref37)
[Nat. Clim. Change 7, 557–562.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref37)


[38. Luderer, G., Pehl, M., Arvesen, A., Gibon, T.,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref38)
[Bodirsky, B.L., deBoer,H.S.,Fricko, O., Hejazi, M.,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref38)
[Humpeno¨ der, F., Iyer, G., et al. (2019).](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref38)
[Environmental co-benefits and adverse side-](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref38)
[effects of alternative power sector](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref38)
[decarbonization strategies. Nat. Commun. 10,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref38)
[5229.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref38)


[39. Jewell, J., Vinichenko, V., McCollum, D., Bauer,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref39)
[N., Riahi, K., Aboumahboub, T., Fricko, O.,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref39)



[Harmsen, M., Kober, T., Krey, V., et al. (2016).](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref39)
[Comparison and interactions between the](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref39)
[long-term pursuit of energy independence and](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref39)
[climate policies. Nat. Energy 1, 16073.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref39)


[40. Lo¨ ffler, K., Burandt, T., Hainsch, K., and Oei,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref40)
[P.-Y. (2019). Modeling the low-carbon](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref40)
[transition of the European energy system - A](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref40)
[quantitative assessment of the stranded assets](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref40)
[problem. Energy Strategy Rev 26, 100422.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref40)


[41. Pfenninger, S., and Keirstead, J. (2015).](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref41)
[Renewables, nuclear, or fossil fuels? Scenarios](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref41)
[for Great Britain’s power system considering](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref41)
[costs, emissions and energy security. Appl.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref41)
[Energy 152, 83–93.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref41)


42. Lombardi, F. (2019). Calliope-Italy Repository
[(Zenodo). https://doi.org/10.5281/zenodo.](https://doi.org/10.5281/zenodo.3903089)
[3903089.](https://doi.org/10.5281/zenodo.3903089)


43. Sparber, W., Moser, D., Manzolini, G., and
Prina, M.G. (2017). Renewable energy high
penetration scenarios using multi-nodes
approach: analysis for the Italian case. 33rd
European Photovoltaic Solar Energy
Conference and Exhibition, pp. 2164–2170.


[44. Colbertaldo, P., Guandalini, G., and](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref44)
[Campanari, S. (2018). Modelling the integrated](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref44)
[power and transport energy system: the role of](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref44)
[power-to-gas and hydrogen in long-term](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref44)
[scenarios for Italy. Energy 154, 592–601.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref44)


[45. Boßmann, T., and Staffell, I. (2015). The shape](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref45)
[of future electricity demand: exploring load](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref45)
[curves in 2050s Germany and Britain. Energy](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref45)
[90, 1317–1333.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref45)


46. Terna (Italian TSO). (2020). Terna Transparency
[Report. https://www.terna.it/en/electric-](https://www.terna.it/en/electric-system/transparency-report)
[system/transparency-report.](https://www.terna.it/en/electric-system/transparency-report)


[47. Lombardi, F., Rocco, M.V., and Colombo, E.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref47)
[(2019). A multi-layer energy modelling](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref47)
[methodology to assess the impact of heat-](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref47)
[electricity integration strategies: the case of the](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref47)
[residential cooking sector in Italy. Energy 170,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref47)
[1249–1260.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref47)


[48. Child, M., Koskinen, O., Linnanen, L., and Breyer,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref48)
[C. (2018). Sustainability guardrails for energy](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref48)
[scenarios of the global energy transition. Renew.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref48)
[Sustain. Energy Rev. 91, 321–334.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref48)


49. Ministero dello Sviluppo Economico (2017).
Strategia Energetica Nazionale (SEN) 2017.
[https://www.mise.gov.it/images/stories/](https://www.mise.gov.it/images/stories/documenti/Testo-integrale-SEN-2017.pdf)
[documenti/Testo-integrale-SEN-2017.pdf.](https://www.mise.gov.it/images/stories/documenti/Testo-integrale-SEN-2017.pdf)


50. Virdis, M.R., Gaeta, M., and Martini, C. (2017).
Decarbonizzazione dell’economia italiana.
Scenari di sviluppo del sistema
[energetico nazionale (Editrice ALKES). https://](https://www.minambiente.it/sites/default/files/archivio/allegati/rse_decarbonizzazione_web.pdf)
[www.minambiente.it/sites/default/files/](https://www.minambiente.it/sites/default/files/archivio/allegati/rse_decarbonizzazione_web.pdf)
[archivio/allegati/rse_decarbonizzazione_web.](https://www.minambiente.it/sites/default/files/archivio/allegati/rse_decarbonizzazione_web.pdf)
[pdf.](https://www.minambiente.it/sites/default/files/archivio/allegati/rse_decarbonizzazione_web.pdf)


[51. Schmidt, O., Hawkes, A., Gambhir, A., and](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref51)
[Staffell, I. (2017). The future cost of electrical](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref51)
[energy storage based on experience rates.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref51)
[Nat. Energy 2, 17110.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref51)


52. IRENA (2019). Future of solar photovoltaic:
deployment, investment, technology, grid


#### **ll**

integration and socio-economic aspects
(a global energy transformation: paper).
[https://www.irena.org/-/media/Files/IRENA/](https://www.irena.org/-/media/Files/IRENA/Agency/Publication/2019/Nov/IRENA_Future_of_Solar_PV_2019.pdf)
[Agency/Publication/2019/Nov/](https://www.irena.org/-/media/Files/IRENA/Agency/Publication/2019/Nov/IRENA_Future_of_Solar_PV_2019.pdf)
[IRENA_Future_of_Solar_PV_2019.pdf.](https://www.irena.org/-/media/Files/IRENA/Agency/Publication/2019/Nov/IRENA_Future_of_Solar_PV_2019.pdf)


53. IRENA (2019). Future of wind: deployment,
investment, technology, grid integration and
socio-economic aspects (a global energy
[transformation paper). https://irena.org/-/](https://irena.org/-/media/Files/IRENA/Agency/Publication/2019/Oct/IRENA_Future_of_wind_2019.pdf)
[media/Files/IRENA/Agency/Publication/2019/](https://irena.org/-/media/Files/IRENA/Agency/Publication/2019/Oct/IRENA_Future_of_wind_2019.pdf)
[Oct/IRENA_Future_of_wind_2019.pdf.](https://irena.org/-/media/Files/IRENA/Agency/Publication/2019/Oct/IRENA_Future_of_wind_2019.pdf)


54. IRENA (2019). Hydrogen: a renewable energy
[perspective. https://www.irena.org/](https://www.irena.org/publications/2019/Sep/Hydrogen-A-renewable-energy-perspective#:%7E:text=Hydrogen%20has%20emerged%20as%20an,to%20ensure%20a%20sustainable%20future.%26text=Important%20synergies%20exist%20between%20hydrogen,the%20reach%20of%20renewable%20soluti)
[publications/2019/Sep/Hydrogen-](https://www.irena.org/publications/2019/Sep/Hydrogen-A-renewable-energy-perspective#:%7E:text=Hydrogen%20has%20emerged%20as%20an,to%20ensure%20a%20sustainable%20future.%26text=Important%20synergies%20exist%20between%20hydrogen,the%20reach%20of%20renewable%20soluti)
[A-renewable-energy-](https://www.irena.org/publications/2019/Sep/Hydrogen-A-renewable-energy-perspective#:%7E:text=Hydrogen%20has%20emerged%20as%20an,to%20ensure%20a%20sustainable%20future.%26text=Important%20synergies%20exist%20between%20hydrogen,the%20reach%20of%20renewable%20soluti)
[perspective#:�:text=Hydrogen%20has%](https://www.irena.org/publications/2019/Sep/Hydrogen-A-renewable-energy-perspective#:%7E:text=Hydrogen%20has%20emerged%20as%20an,to%20ensure%20a%20sustainable%20future.%26text=Important%20synergies%20exist%20between%20hydrogen,the%20reach%20of%20renewable%20soluti)
[20emerged%20as%20an,to%20ensure%20a%](https://www.irena.org/publications/2019/Sep/Hydrogen-A-renewable-energy-perspective#:%7E:text=Hydrogen%20has%20emerged%20as%20an,to%20ensure%20a%20sustainable%20future.%26text=Important%20synergies%20exist%20between%20hydrogen,the%20reach%20of%20renewable%20soluti)
[20sustainable%20future.&text=Important%](https://www.irena.org/publications/2019/Sep/Hydrogen-A-renewable-energy-perspective#:%7E:text=Hydrogen%20has%20emerged%20as%20an,to%20ensure%20a%20sustainable%20future.%26text=Important%20synergies%20exist%20between%20hydrogen,the%20reach%20of%20renewable%20soluti)
[20synergies%20exist%20between%](https://www.irena.org/publications/2019/Sep/Hydrogen-A-renewable-energy-perspective#:%7E:text=Hydrogen%20has%20emerged%20as%20an,to%20ensure%20a%20sustainable%20future.%26text=Important%20synergies%20exist%20between%20hydrogen,the%20reach%20of%20renewable%20soluti)
[20hydrogen,the%20reach%20of%20renewable](https://www.irena.org/publications/2019/Sep/Hydrogen-A-renewable-energy-perspective#:%7E:text=Hydrogen%20has%20emerged%20as%20an,to%20ensure%20a%20sustainable%20future.%26text=Important%20synergies%20exist%20between%20hydrogen,the%20reach%20of%20renewable%20soluti)
[%20solutions.](https://www.irena.org/publications/2019/Sep/Hydrogen-A-renewable-energy-perspective#:%7E:text=Hydrogen%20has%20emerged%20as%20an,to%20ensure%20a%20sustainable%20future.%26text=Important%20synergies%20exist%20between%20hydrogen,the%20reach%20of%20renewable%20soluti)


[55. Go¨ tz, M., Lefebvre, J., Mo¨ rs, F., McDaniel Koch,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref55)
[A., Graf, F., Bajohr, S., Reimert, R., and Kolb, T.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref55)
[(2016). Renewable Power-to-Gas: a](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref55)
[technological and economic review. Renew.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref55)
[Energy 85, 1371–1390.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref55)


[56. Keith, D.W., Holmes, G., St. Angelo, D., and](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref56)
[Heidel, K. (2018). A process for capturing CO2](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref56)
[from the atmosphere. Joule 2, 1573–1594.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref56)


[57. Bo¨ hm, H., Zauner, A., Rosenfeld, D.C., and](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref57)
[Tichler, R. (2020). Projecting cost development](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref57)
[for future large-scale power-to-gas](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref57)
[implementations by scaling effects. Appl.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref57)
[Energy 264, 114780.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref57)


[58. Thema, M., Bauer, F., and Sterner, M. (2019).](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref58)
[Power-to-gas: electrolysis and methanation](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref58)
[status review. Renew. Sustain. Energy Rev. 112,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref58)
[775–787.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref58)


[59. Gorre, J., Ortloff, F., and van Leeuwen, C.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref59)
[(2019). Production costs for synthetic methane](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref59)
[in 2030 and 2050 of an optimized power-to-gas](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref59)
[plant with intermediate hydrogen storage.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref59)
[Appl. Energy 253, 113594.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref59)


[60. Buttler, A., and Spliethoff, H. (2018). Current](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref60)
[status of water electrolysis for energy storage,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref60)
[grid balancing and sector coupling via power-](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref60)
[to-gas and power-to-liquids: a review. Renew.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref60)
[Sustain. Energy Rev. 82, 2440–2454.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref60)


[61. Pfenninger, S., and Staffell, I. (2016). Long-term](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref61)
[patterns of European PV output using 30 years](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref61)
[of validated hourly reanalysis and satellite data.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref61)
[Energy 114, 1251–1265.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref61)


[62. Staffell, I., and Pfenninger, S. (2016). Using bias-](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref62)
[corrected reanalysis to simulate current and](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref62)
[future wind power output. Energy 114, 1224–](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref62)
[1239.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref62)


[63. Tro¨ ndle, T., Pfenninger, S., and Lilliestam, J.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref63)
[(2019). Home-made or imported: on the](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref63)
[possibility for renewable electricity autarky on](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref63)
[all scales in Europe. Energy Strategy Rev 26,](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref63)
[100388.](http://refhub.elsevier.com/S2542-4351(20)30348-2/sref63)


Joule 4, 2185–2207, October 14, 2020 2207


