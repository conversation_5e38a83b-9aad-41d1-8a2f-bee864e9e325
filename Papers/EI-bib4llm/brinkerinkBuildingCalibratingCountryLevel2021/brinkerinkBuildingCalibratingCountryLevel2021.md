# Citation Key: brinkerinkBuildingCalibratingCountryLevel2021

---

[Energy Strategy Reviews 33 (2021) 100592](https://doi.org/10.1016/j.esr.2020.100592)


Contents lists available at ScienceDirect

# Energy Strategy Reviews


[journal homepage: http://www.elsevier.com/locate/esr](https://http://www.elsevier.com/locate/esr)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/brinkerinkBuildingCalibratingCountryLevel2021/brinkerinkBuildingCalibratingCountryLevel2021.pdf-0-1.png)

## Building and Calibrating a Country-Level Detailed Global Electricity Model Based on Public Data


Maarten Brinkerink [a] [,] [b] [,] [*], <PERSON> [´] oir´ [a] [,] [b], <PERSON> [a] [,] [b ]


a _MaREI Centre, Environmental Research Institute, University College Cork, Cork, Ireland_
b _School of Engineering, University College Cork, Cork, Ireland_



A R T I C L E I N F O


_Keywords:_
Open data

Power system
Power system modelling

Emissions

Global


**1. Introduction**



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/brinkerinkBuildingCalibratingCountryLevel2021/brinkerinkBuildingCalibratingCountryLevel2021.pdf-0-0.png)

A B S T R A C T


Deep decarbonization of the global electricity sector is required to meet ambitious climate change targets. This
underlines the need for improved models to facilitate an understanding of the global challenges ahead, partic­
ularly on the concept of large-scale interconnection of power systems. Developments in recent years regarding
availability of open data as well as improvements in hardware and software has stimulated the use of more
advanced and detailed electricity system models. In this paper we explain the process of developing a first-of-itskind reference global electricity system model with over 30,000 individual power plants representing 164
countries spread out over 265 nodes. We describe the steps in the model development, assess the limitations and
existing data gaps and we furthermore showcase the robustness of the model by benchmarking calibrated hourly
simulation results with historical emission and generation data on a country level. The model can be used to
evaluate the operation of today’s power systems or can be applied for scenario studies assessing a range of global
decarbonization pathways. Comprehensive global power system datasets are provided as part of the model input
data, with all data being openly available under the FAIR Guiding Principles for scientific data management and
stewardship allowing users to modify or recreate the model in other simulation environments. The software used
for this study (PLEXOS) is freely available for academic use.



In energy systems literature, modelled global pathways limiting
global warming to 1.5 [◦] C generally meet energy service demand with
lower energy use and significant electrification of energy end use [1,2].
These requirements signal a potential system transition in global elec­
tricity generation and the role of increased interconnection becomes an
important question. Large scale modelling of continental power systems
can facilitate a better understanding of potential pathways towards a
zero-carbon supply of our future energy needs, yet to date research in
this area is limited by a lack of detailed global electricity models [3].
Due to limitations in either computational complexity or data
availability, electricity system modelling studies tend to make a tradeoff between the spatial scale of the study area and technical represen­
tation of power plant characteristics and transmission components. In
modelling studies on a multi-country scale, a single node per country
copperplate approach is generally applied [4–7] and technical proper­
ties such as turbine unit sizes, heat rates, and start-up costs [4,8,9] are
usually represented in a standardized manner with uniform



characteristics for every individual power plant of a certain type. This
approach is acceptable for long term scenario studies where develop­
ment of power plants and its technological characteristics are uncertain,
yet for realistic assessments of today’s electricity system a finer repre­
sentation of the diversity in power plant- and electricity system char­
acteristics is preferable.
There are a limited number of modelling studies assessing electricity
systems from a global perspective. This can partly be explained because
of the aforementioned issues, yet an additional factor is that generally
the use of a global electricity model is seen as unnecessary and even
impractical. Different to most other energy carriers, electricity to-date is
produced and consumed domestically or exchanged between several
countries within a region or continent. That said, the interest in the
concept of long-distance electricity transmission and the potential evo­
lution towards an interconnected global grid has gained significant
traction in the last few years [3,10,11], resulting in a range of modelling
studies on this topic [7,12–17]. Other research utilizing global elec­
tricity models focuses on feasibility assessments of possible 100%
renewable energy systems, without the utilization of low-carbon




 - Corresponding author. MaREI Centre, Environmental Research Institute, University College Cork, Cork, Ireland
_E-mail address:_ [<EMAIL> (M. Brinkerink).](mailto:<EMAIL>)


[https://doi.org/10.1016/j.esr.2020.100592](https://doi.org/10.1016/j.esr.2020.100592)
Received 25 February 2020; Received in revised form 17 July 2020; Accepted 24 November 2020

Available online 10 December 2020
[2211-467X/© 2020 The Author(s). Published by Elsevier Ltd. This is an open access article under the CC BY license (http://creativecommons.org/licenses/by/4.0/).](http://creativecommons.org/licenses/by/4.0/)


_M. Brinkerink et al._ _Energy Strategy Reviews 33 (2021) 100592_



technologies such as nuclear energy, carbon capture and storage (CCS)

[18,19] or even bioenergy [18].
In order to provide improved insights in the diversity of the worlds
electricity system we developed ‘PLEXOS-World’, a detailed global
electricity model capable of simulating over 30,000 existing power
plants using public data. Although the issues of computational intensity
and data access are still relevant, developments in recent years
regarding faster computers, improved solvers and solving techniques

[20], as well as relevant open electricity system data initiatives [21–23]
have made this project possible. An assessment by Pfenninger and col­
leagues of the use of open data and software within energy policy
research indicates that it generally lags behind other fields of research

[24]. Extended efforts are being made for this study regarding this gap
by means of showing the potential of open power system data as well as
openness of model. The PLEXOS-World model is openly accessible for
any PLEXOS user, with the software being freely available for academic
use. The model in raw data format and all model input data is openly
available and can be retrieved from the supplementary datasets [25],
allowing users to modify or recreate the model in other simulation

environments.

In this paper we describe the process of building a detailed global
electricity model at plant- and country level. Section 2 includes the
methodology, full overview of the data inputs and any made assump­
tions. A benchmarking exercise of calibrated simulation results with
historical emission and generation data to secure accurate model per­
formance is included in section 3. The paper concludes in section 4 with
a discussion of the findings, the existing limitations and data gaps and an
outlook on possible future work based on the developed model.


**2. Data input and methodology**


This section introduces the software used to simulate the global
electricity model, describes the main methods and assumptions and
gives a full overview of the input data.


_2.1. Unit Commitment & Economic Dispatch model_


The software used in this study to solve the Unit Commitment &
Economic Dispatch (UCED) problem in the global electricity model is
PLEXOS. PLEXOS is a transparent electricity system modelling tool used
for electricity market modelling and planning. Detailed linear equations
can be queried, modified and viewed by the user to facilitate a deeper
understanding of model dynamics. The equations as applied for this
study can be found in section 1 (S1) of the supplementary material [25].
All data input is fully customizable and the tool facilitates use of a range
of open source (GLPK, SCIP) and commercial (CPLEX, Gurobi, MOSEK,
Xpress-MP) solvers depending on preference and accessibility to licen­
ses. PLEXOS comes with a fully build-in user interface enabling data
management, model building and simulation all to be done within, yet
also supports automation of data flows and model simulation from
outside the user interface by means of COM or .NET. The software
package comes with detailed documentation of all features. Modelling
can be carried out using Mixed Integer Linear Programming (MILP) that
aims to minimize an objective function subject to the expected cost of
thermal and renewable electricity dispatch and a range of technical
constraints. It is also possible to select Linear Programming (LP) for the
model simulation to limit the computational complexity, albeit with
lower detail in technical parameters. In the default setup of the software
each time step is modelled in sequence and is linked to the previous for
initial conditions. PLEXOS also provides the option to perform model
simulations in a parallel fashion, meaning that otherwise chronological
time steps can be simulated at once while spread out over multiple cores
after which results are ‘stitched’ back together. This approach has the
advantage of optimized utilization of computational resources with the
trade-off being reduced accuracy considering cross-period parameters
(e.g. number of online generator units) are not being tracked between



**Table 1**

Runtime performance of the PLEXOS-World model under different unit
commitment optimalities and step link modes. The model simulations have been
performed on a Dell Intel(R) Core (TM) i7-8700K CPU @ 3.70 GHz with 63.83
GB Memory with Xpress-MP 35.01.01 as solver.


Unit Commitment Step Link Interval Time step Runtime
Optimality Mode


MILP Linked Hourly Daily +6 Hour 30 h

Look-ahead



MILP Parallel Hourly Daily +6 Hour
Look-ahead,

12 steps in

Parallel



7 h



steps. A comparison in the runtime performance between both ap­
proaches in context of PLEXOS-World can be found in Table 1. For the
simulations in this study we applied MILP with linked time steps for
optimal accuracy.
The objective function of the model includes operational costs,
consisting of fuel costs, start-up costs consisting of a fuel offtake at startup of a unit and a fixed unit start-up cost. Penalty costs for unserved
energy and a penalty cost for not meeting reserve requirements can also
be included in the objective function. Fuel consumption is calculated
using piecewise linear functions based on the generator heat rate. Sys­
tem level constraints consist of an energy balance equation ensuring
supply meets the regional demand at each simulation period. Water
balance equations ensure water flow within pumped storage units is
conserved and tracked. Constraints on unit operation include minimumand maximum generation, minimum- and maximum up and down time
and ramp-up and ramp-down rates. A zonal pricing methodology is
applied with an assumed perfect market across the globe without
consideration of market power or competitive bidding practices. A large
number of open energy models are available covering different energy
sectors and varying geographical regions. [1 ] PLEXOS-World’s configura­
tion is similar in set-up to other UCED models (for example Dispa-SET),
but has a simplified representation of cross border transmissions by
making use of Net Transfer Capacities (NTC).


_2.2. Spatial and temporal representation_


PLEXOS-World covers the electricity systems of 164 countries, sub­
divided into a total of 265 nodes. Larger countries, both in terms of size
as well as relative electricity demand, are spread out over multiple nodes
allowing for the integration of regional diversity as well as time-zone
differences. This is the case for Australia (7 nodes), Brazil (10 nodes),
Canada (9 nodes), China (34 nodes), India (5 nodes), Japan (6 nodes),
Russia (7 nodes) and the United States (24 nodes). Subdivision of nodes
is generally based on geographical borders, operating areas of different
authorities or following the availability of data. See Fig. 1 for an over­
view of the nodal representation in PLEXOS-World and S4 of the sup­
plementary material [25] for a full list of nodes. S2 of the supplementary
material can be consulted for more details on the approach of
sub-country division of nodes and data.
The model is setup to run for the 2015 calendar year, with custom­
izable timesteps adjustable for the aim of the study and the size of the
simulated model. Typically, two-hourly, hourly or 5-min intervals are
used. 2015 has been chosen as simulation year due to restrictions on
data availability for more recent years. Continents and nodes can be
manually selected or deselected based on the user’s preferences, keeping
in mind that changing the spatial or temporal resolution can signifi­
cantly affect the computational intensity of the simulation. Hourly
simulations are generally sufficient to get a basic understanding of the
optimal UCED, yet to incorporate ramping constraints of generator units


1
[https://en.wikipedia.org/wiki/Open_energy_system_models.](https://en.wikipedia.org/wiki/Open_energy_system_models)



2


_M. Brinkerink et al._ _Energy Strategy Reviews 33 (2021) 100592_

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/brinkerinkBuildingCalibratingCountryLevel2021/brinkerinkBuildingCalibratingCountryLevel2021.pdf-2-0.png)


**Fig. 1.** Nodal representation of PLEXOS-World. Every copperplated area of an individual colour represents a node with a total of 164 countries and 265 nodes.
Australia (7 nodes), Brazil (10 nodes), Canada (9 nodes), China (34 nodes), India (5 nodes), Japan (6 nodes), Russia (7 nodes) and the United States (24 nodes) are
subdivided into multiple nodes. Note that besides a range of smaller islands, certain land-based countries are also not incorporated in the model due to absence of
data in the WRI Power Plant Database.



or to assess aspects such as system inertia sub-hourly modelling is
advisable [26]. The input data for demand- and variable renewables
(VRES) time-series are based on hourly patterns, yet the software line­
arly interpolates data values in case sub-hourly modelling is required.
Hourly intervals are used for the simulations in this study based on daily
time steps with a 6 h look-ahead.


_2.3. Technical representation and input data_


The model draws solely on public sources of information for input
data. The sources and accompanying assumptions for this study are
introduced in the next sections. Fig. 2 gives an overview of the different
steps within the modelling process as well as for the different sources
and their interrelationships with the data inputs. The steps and data as
used for the calibration exercise are also shown. Note that the data in the

model is from best available public sources, but users of the model have
freedom to change and edit any data if more advanced local or sitespecific data is at hand.


_2.3.1. Power plant portfolios_
The World Resources Institute (WRI), in collaboration with the
Global Energy Observatory, Google, KTH Royal Institute of Technology
in Stockholm and Enipedia, has made extended efforts to create the first
open access Global Power Plant Database covering more than 85% of
global capacity [21]. The WRI database differentiates power plants per
fuel type and has integrated geolocations. It has been used as the pri­
mary source for power plant capacity data for PLEXOS-World. Approx­
imately 55% of power plants in the WRI database have a commissioning
year attached. For the remaining 45% it is unclear whether these power
plants were already operational as of 2015. Power plants for which it is
known that they became operational after 2015 are incorporated in the
model yet are ‘turned off’ (units are set to zero) for simulations of the
2015 calendar year. The geolocations were used to allocate power plants
to the relevant nodes. Fig. 3 shows a visualization of the power plant



data with the height of the bar indicating the relative capacity size. This
visualization does not only reflect the differences in density of power
plants between regions, but also highlights the data gap of the missing
15% of global power plant capacity. The coverage in developing regions,
as well as countries such as China, India and Russia is not fully
exhaustive. Furthermore, wind and solar coverage is limited due to the
more decentralized nature of these technologies. The remaining power
plant capacity not accounted for in the WRI database has been incor­
porated using standardized generators per country and per technology
based on a number of quality sources such as the EIA [27], ENTSO-E

[28], IEA [29,30], IRENA [31] and India’s Central Electricity Author­
ity [32]. For smaller countries where no diversified fossil capacity data
exists within the above sources, it is assumed that the relative share of
coal, gas and oil capacity per country within the WRI database can be
used to scale up to the reported aggregate fossil capacity as indicated by
the EIA [27]. Due to a lack of sub-country capacity data for especially
China, Japan and Russia, it is assumed that missing capacity in these
larger countries can be spread out relative to the share of existing ca­
pacity per technology per sub-country node in the WRI database.
Power plant capacity data in the WRI database is supplied in an
aggregate format without differentiating individual turbine unit sets per
power plant. To be able to incorporate generator characteristics such as
minimum stable levels, ramp rates and to assess system inertia contri­
butions it is important to disaggregate the power plant capacity data into
individual units. This is done by utilizing a standard unit size method­
ology per fuel type as applied in earlier studies [5,13,33], both for the
WRI database data as well as for the missing capacities, with the stan­
dard turbine unit sizes per generator type indicated in Table 2. Other
renewable power plants such as solar and wind power plants, as well as
all other storage technologies other than Pumped-Storage Hydro (PSH),
use the capacities as given by the different databases. Note that
Concentrated Solar Power (CSP) to-date is not included as a separate
power plant type because the WRI database does not differentiate be­
tween different solar technologies.



3


_M. Brinkerink et al._ _Energy Strategy Reviews 33 (2021) 100592_

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/brinkerinkBuildingCalibratingCountryLevel2021/brinkerinkBuildingCalibratingCountryLevel2021.pdf-3-0.png)


**Fig. 2.** Flow chart visualizing the different steps for the modelling within this study. The left side indicates the main sources used for the data input of the model and
their interrelationships. Hourly model simulations in PLEXOS occur based on the model input, from which among others generation and emission values per power
plant are the main simulation output. These values are benchmarked on a country-level with historical data for 2015 retrieved from IEA and IRENA datasets as
indicated on the right side of the chart. Through an iterative process, a range of fuel and generator properties are calibrated (indicated with the red connections) to
replicate the 2015 context. These aspects are further explained in the next sections.



It has been assumed that gas power plants in the WRI database with a
capacity _<_ 130 MW represent open cycle gas turbines (OCGT) and vice
versa _>_ 130 MW combined cycle gas turbines (CCGT). The number of
units per power plant _U_ (rounded upwards) can be calculated with (1),
with _MWt_ being the total nameplate capacity of the power plant and
_MWst_ the standard unit size of the relevant technology. Consequently,
the MW capacity per unit _C_ equals (2).


U = [MWt _/_ MWst] (1)


C = MWst _/_ U (2)


Generic relationships have been derived based on historical power
plant data to calculate generator specific heat rates and start costs
depending on the capacity per turbine unit. By using the constants _SCa_
and _SCb_ as included in Table 2, the specific start cost _SC_ per unit _C_ can be
calculated with (3). These characteristics are modifiable by users and
available as part of the model input data.


SC = (C ∗ SCa) + SCb (3)

Similarly, the generator specific heat rate can be calculated with (4),
by using the constants _HRd, HRe, and HRf_ .


HR = ( ( C [2] [)] ∗HRd) + (C ∗ HRe) + HRf (4)


In unconstrained model runs, baseload power plants such as coal



(2015 context with higher gas prices), nuclear, biomass and geothermal
are over utilized compared to historical data. In real life, generators can
be limited in their operation due to a variety of factors such as outages,
maintenance, limitations in fuel supply or through policy-based con­
straints. Data regarding restrictions in operation at power plant level are
not available within the public domain, hence for these baseload tech­
nologies we incorporated operational constraints specified per country
and technology which forces generator units to be ‘turned off’ for part of
the simulation horizon. IEA’s ‘Electricity Information’ [30] provides
insights in generation values for 2015 per country and fuel type. The
difference between these values and the combined power output of all
power plants per country and fuel type in the unconstrained model run
can be used as indicator for the initial size of the required operational
constraints. Through an iterative process with model simulations, these
initial values have been calibrated up or down until further change
negatively impacted the match with reported historical generation.


_2.3.2. Renewable profiles_
The supply of electricity from hydro, solar and wind is determined
using location specific capacity factors (CF). The Renewables Ninja
database [23] has been used to extract hourly CF profiles for every onand offshore wind (5187 in total) and solar (5929 in total) power plant
location in the WRI database by making use of the geolocations. The
profiles are developed by making use of NASA’s MERRA-2 global



4


_M. Brinkerink et al._ _Energy Strategy Reviews 33 (2021) 100592_

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/brinkerinkBuildingCalibratingCountryLevel2021/brinkerinkBuildingCalibratingCountryLevel2021.pdf-4-0.png)


**Fig. 3.** Visualization of the power plant data of the WRI database [21]. Relative height of the bar is an indicator for the capacity of the specific power plant.


**Table 2**

Standard generator characteristics and variables as applied for this study. SCa, SCb, HRd, HRe, HRf represent constants in derived relationships based on historical
power plant data to calculate generator specific Heat Rates (HR) and Start Costs (SC) with the capacity per generator unit as variable.


Generator Type MWst (MW) SCa SCb HRd HRe HRf Minimum Stable Level (%)


Biomass 200 246.51 1412.6 6E-05 − 0.0392 14.432 30

Coal 300 6.2646 1166.7 − 2E-07 − 0.0016 10.892 30

CCGT 400 251.5 − 9875 2E-06 0.0025 8.307 40

OCGT 130 91.525 − 186.44 8E-05 − 0.0235 11.516 20

Hydro (non-PSH) 400 – – – – – –

Nuclear 600 134.55 87 091 5E-08 − 0.0004 4.0717 60

Oil 300 91.525 − 186.44 8E-05 − 0.0235 11.516 50

PHS 200 – – – – – –



reanalysis data [34]. The current set of profiles are based on the 2015
meteorological year, future updates of the model will include a wider
range of data years considering that weather patterns can have signifi­
cant impact on the operation of electricity systems, especially with
increasing VRES integration [35]. Standardized solar- and wind power
plants integrated to scale up missing capacities within the different
nodes make use of an averaged profile based on all CF profiles from
within that node. For nodes where no wind or solar power plants exist
within the WRI database, a sample of between 4 and 8 patterns per node
spread out over its respective geographical area have been manually
extracted from the Renewables Ninja database.
Initial model simulations indicated that the overall generation of
solar and wind per node as a result of the integrated CF profiles was in
some cases significantly overestimated compared to historical genera­
tion data for 2015 as reported by IRENA [36]. As shown by the authors
of the Renewables Ninja database [37], use of the database in particular
for regions outside the EU requires bias correction. For this reason,

we’
ve applied country-level multipliers to the hourly profiles to cali­
brate overall generation from solar and wind in the model with historic
2015 data.



Due to the size of the model, hydro other than PSH is modelled in a
simplified manner without actively simulating the use of (cascaded)
reservoirs. Location specific monthly CFs for every hydro power plant
(7155 in total) are developed by making use of the Global Reservoir and
Dam Database (GRAND) [38] and a study by Gernaat and colleagues

[39]. In this latter study, the authors identified over 60,000 potential
new locations for hydro power plants and developed monthly water
discharge profiles for every new location, as well as for every existing
location as identified in the GRAND database based on 30-years of runoff
data. The geolocations of the hydro power plants from the WRI database
are matched with the nearest dam from the GRAND database, with every
plant above 1 GW matched manually to secure accuracy. The coverage
of the GRAND database for dams above 58 latitude is limited, hence for
hydro power plants in the Scandinavian countries of Iceland, Finland,
Norway and Sweden we use country average profiles as used for earlier
studies assessing the European electricity system [5,13,35]. For the
northern parts of Canada and Russia we use a country average fully
based on GRAND data. The profiles for the standardized hydro power
plants used to fill gaps in power plant capacities within the WRI database
are based on an average of all profiles of the specific node. Countries



5


_M. Brinkerink et al._ _Energy Strategy Reviews 33 (2021) 100592_



without hydro power plants in the WRI database, yet with mentioned
capacity following EIA data, are assigned an average profile from a
neighboring country.
Following [39], the design discharge of hydro turbines is assumed to
be based on the 4th highest discharge month in the discharge profiles
meaning that during at least three months of the year spillage of water

_CFt_
occurs. Base profiles for the month specific maximum capacity factor
per GRAND location can be calculated with (5), with _Qd_ being the design
discharge and _Qt_ being the discharge of month _t_ . Following on to that, to
secure accuracy on the macro level, the individual profiles from (5) are
scaled by comparing the calculated capacity weighted average CF per
country with a country-level 15-year average CF based on historical
capacity and generation data from IRENA [36].


CF t = Qt _/_ Qd ∗ 100 (5)


Hydro power plants within the WRI database do not differentiate
between types of hydro, being Run-of-River or reservoir-based systems.
Early stage model simulations indicated that the generation potential for
a large share of hydro power plants in months with high CFs was not
fully utilized, whereas the occurrence of significant unserved energy in
hydro dominated regions (e.g. Canada) in months with lower CFs indi­
cated the importance of seasonal storage of water for these regions. To
mimic the possibility of having a certain flexibility in cross-monthly
storage of water for more dispersed generation of electricity, the orig­
inal profiles were rescaled with (6) to fit within a narrower range of
monthly values by calibrating the original min (min old ) and max
(max old ) of the distribution of _CF_ _t_ s of the specific hydro power plant. The
adjusted min (min new ) and max (max new ) values were determined based
on an iterative process of model simulations with a hard upper limit set
at 80% of the highest _Qt_ of every individual profile. At all times, the
capacity weighted average of the profiles within a country equal the 15year average country CF as identified with the IRENA data. As a last step
specifically for this study, scalers have been applied in the calibration
exercise to slightly in- or decrease the profiles for 2015 conditions again
following reported country-level generation data from IRENA. All CF
profiles as used for this study can be found in [25]. Hydro plants are
constrained at a monthly level with the above profiles but are free to
provide flexibility and balancing at hourly level.



with a ratio above 200. This average ratio has been applied to all PSH
projects where storage size data was missing. Altogether, the model
incorporates over 1100 operational electricity storage projects, of which

323 PSH.


_2.3.4. Hourly demand data_
Availability of hourly public demand data for countries outside
Europe and North America is limited. A common approach in electricity
system modelling studies for regions outside these areas is therefore to
use standardized profiles from other countries (mostly European) and
adapt the profiles based on locational characteristics [12,15,43].
Extended efforts have been made to integrate a more detailed spatial
representation within the demand data for this study. To-date, the model
includes load profiles based on actual historical hourly data for
approximately 50 countries and regional specific historical load profiles
for 55 sub-regions. This includes data from geographically dispersed
load centres around the globe such as Canada, the United States (US),
Mexico, Brazil, Russia, South-Africa, Japan, South-Korea and Australia.
The data portal of the European Network of Transmission System Op­
erators (ENTSO-E) includes historical hourly load data for all EU
member states, as well as for most non-EU countries connected to the
European synchronous grid [44,45]. Data for Ukraine has been retrieved
through direct communication with the national system operator (SE
NPC Ukrenergo, 29-10-2018). A range of system operators or governing
entities provide historical hourly load data on an individual (sub-)
country level. A full overview of the existing publicly accessible hourly
load data can be found in S5 of the supplementary material [25] with all
global demand profiles as used for this study to be retrieved as a separate
file also from [25]. Details on availability and development of hourly
load profiles for all sub-country nodes can be found in S2 of the sup­
plementary material.
Within the available historic data, differences exist that need to be
overcome to retain uniformity in the input data for the 2015 model. Not
all profiles cover the full electricity system of a country. As a best esti­
mate for hourly demand in the respective country, we scaled the avail­
able profiles to 100% of 2015 electricity demand. Furthermore, not all
available profiles are based on the 2015 calendar year, hence these
profiles have been scaled and shifted to 2015 values. Shifting profiles is
required to retain balance in weekdays and weekends while scaling
profiles from year to year. Scaling of the hourly profiles occurs linearly
with the difference in final demand between the reference year of the
data and 2015 as proxy. It has been assumed that there are no changes in
relative peak demand. Final electricity demand per country has been
determined by multiplying consumption per capita data from the World
Bank with the total population, combined with integrating country-level
Transmission & Distribution (T&D) losses [46]. All in all, 28 countries
did not have a value for electricity consumption per capita. These
countries were assigned a value from the nearest neighboring country
with similar GDP per capita. This was done manually to verify the
consistency of data.
Countries without available historic hourly demand profiles have
been assigned country specific synthetic profiles as developed by Tok­
tarova and colleagues [47]. The authors constructed a calibrated
method to generate demand profiles for future years based on locational
economic, technical and climatic characteristics. Profiles as developed
for 2020 are scaled and shifted to the 2015 calendar year. For a number
of smaller countries for which no historical or synthetic profiles were
available we assigned profiles from the nearest node with similar GDP
per capita.



CF t ( _new_ ) = (CF t − min old



max
)/( old



old [−] [min] old



old



 - max
) ( new



max new [−] [min] new



new



+ min (6)
) new



Yearly CFs for Ocean, Tidal and Wave based power plants have been
integrated based on [30]. No seasonality or variability has been included
for these technologies to-date.



_2.3.3. Storage_
Large scale electricity storage to-date is mostly based on PSH, albeit
integration of other storage technologies for balancing of VRES or other
ancillary services is becoming more prominent. The US Department of
Energy (DOE) Global Energy Storage Database [2 ] is a regularly updated
database of operational and commissioned electricity storage projects.
The DOE database provides rated power per project yet does not
consistently include storage size (MWh) or charge and discharge effi­
ciencies. Technology specific full cycle efficiencies are incorporated
based on mean values from reported data in [40]. Similarly, indicative
hours of storage values from the same study are used to calculate project
specific storage sizes for all technologies apart from PSH. For approxi­
mately 130 of the PSH projects, mostly in Europe and the US, actual data
on storage size has been retrieved through [41,42] as well as through
individual Wikipedia pages as best indication. Based on this project data,
a calculated average ratio (MWh/MW) between storage size and power
rating for PSH of 18.9 has been determined after exclusion of outliers



a calculated average ratio (MWh/MW) between storage size and power _2.3.5. Net Transfer Capacities_
rating for PSH of 18.9 has been determined after exclusion of outliers Significant developments in the availability of open data regarding

existing high voltage power transmission infrastructure around the
globe has occurred in recent years [48,49]. Yet, no complete global
dataset exists incorporating cross-border Net Transfer Capacities

2
[https://www.sandia.gov/ess-ssl/global-energy-storage-database/.](https://www.sandia.gov/ess-ssl/global-energy-storage-database/) (NTCs). Hence, for the 2015 global electricity system model NTCs were



6


_M. Brinkerink et al._ _Energy Strategy Reviews 33 (2021) 100592_



retrieved through a variety of sources to fill this data gap. NTCs have
been applied rather than modelling transmission infrastructure line by
line due to restrictions on the availability of data as well as to set a limit
on computational complexity of the model simulations. The values
represent the technical potential for power flow and do not take into
account possible geopolitical or market restrictions on utilization.
As part of a study on indicative scenarios of power plant investments
based on potential for electricity trade in the African continent, Taliotis
and colleagues [50] composed a dataset with all existing and planned
NTCs between adjacent African countries. For the 2015 model we only
incorporated the existing lines. The ‘Comision de Integracion Energetica
Regional’ (CIER) published a report in 2016 on the current state of the
energy systems within Central- and South-America, including an over­
view of the interconnectivity between countries with existing and
planned power transmission projects [51]. Similarly, The World Bank
analyzed the current power market structure and design of the elec­
tricity networks in the Middle East and Northern-Africa [52], and an
overview of existing grid infrastructure for South-East Asia can be found
in [53,54]. For reference NTCs between countries covered by the
ENTSO-E we used the 2016 Ten Year Network Development Plan
(TYNDP) as background [55]. Given 2020 values per border in [56]
were taken while capacities from projects finished after 2015 have been
excluded. Furthermore, the transparency platform of the ENTSO-E
provides NTCs [57,58] and hourly exchange values [59] for the ma­
jority of pathways within Europe not directly covered by the TYNDP.
Finally, a wide range of additional journal papers, reports and other
sources contribute to a global dataset of existing cross-border and
cross-regional NTCs as of 2015. This is included in S6 of the supple­
mentary material [25], with table S6.1 showcasing NTCs per adjacent
pathway as well as the references behind the values. S2 of the supple­
mentary material includes a more detailed description of the approaches
used regarding NTCs between sub-country nodes. Fig. 4 highlights the
global cross-border transmission pathways with the highest existing
NTCs as of 2015.

To-date, pathways with the highest NTCs are mostly used to facilitate
supply of surplus electricity from hydro power plants to the power



systems of neighboring countries. Examples are the Paraguayan part of
the Itaipu plant mostly used to supply Southern Brazil and a range of
hydro power plants in Mozambique which are being used to supply
power hungry South-Africa. Looking passed these mostly unilateral
flows, Europe is on the forefront of power system integration to a
combined market reflected by the generally high cross-border trans­
mission capacities.


_2.3.6. Fuels and emissions_

Fuel prices for standard commodities such as coal and gas were taken
from BP Statistical Review as simplified annual prices at continental
level [60]. These can be modified by users if more granular information
is available. Oil as a fuel for power generation is most dominant in re­
gions where there is high supply of the raw fuel, e.g. in the middle east
and in countries such as Venezuela. As a result of the local availability,
standard commodity prices for oil do not always reflect a realistic fuel
price for the power sector in these regions. Multiple iterations in PLEXOS
were used to calibrate country-level oil prices that resulted in power
plant utilization close to 2015 reported generation values. Carbon
pricing is currently not included to retain uniformity in the model for the
different continents. To-date, a range of different carbon pricing
mechanisms are applied in a number of regions around the world [61].
Power plants based on fossil fuels have limited differentiation in specific
fuel types within the WRI database. To reflect the use of specific
sub-categories of fuel groups within the different continents (e.g. bitu­
minous coal or lignite) on the overall CO 2 emissions, continent specific
ratios of CO 2 emission per unit of raw fuel (being coal, natural gas or oil)
have been incorporated. These were calculated by matching 2015 gen­
eration and emission data per larger fuel group as reported by the IEA

[29,30,62].


_2.4. Model calibration and benchmarking_


As described in earlier sections of this paper, part of the model input
data such as renewable capacity factors, operational constraints of
thermal power plants and fuel prices have been calibrated to secure



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/brinkerinkBuildingCalibratingCountryLevel2021/brinkerinkBuildingCalibratingCountryLevel2021.pdf-6-0.png)

**Fig. 4.** Global top 25 cross-border transmission pathways with highest NTCs as of 2015. Max Flow represents flow for direction node A - node B and Min Flow vice
versa. Pathways between sub-country nodes are not included. For a full list see table S6.1 of the supplementary material [25].


7


_M. Brinkerink et al._ _Energy Strategy Reviews 33 (2021) 100592_



model accuracy. This has been done through an iterative process of
comparing model simulation output with 2015 benchmark data and
calibrating the input data accordingly. Model calibration is important as
it allows users to judge the quality of the results against international
benchmarks such as the IEA. Note that users of the model can ignore the
calibration by turning off the specific calibration scenario and dialing
back to the raw model input. However, we believe it is a helpful asset
and gives a more realistic representation of the global power system.
The sources used for the benchmark and calibration are as follows.

Annex A of the World Energy Outlook (WEO) [29] provides historical
CO2 emissions from power generation for the different continents. Dif­
ferences in geographical coverage per continent compared to
PLEXOS-World (e.g. Turkey is part of ‘Europe’ within the WEO whereas
in PLEXOS-World it is part of ‘Asia’) have been adjusted by removing or
adding calculated country-level power sector CO2 emissions from or to
the continental totals. These country-level values were calculated based
on IEA’s ‘CO 2 emissions from fuel combustion’ [62] which provides
historical CO2 emissions per generated kWh per fuel type for a range of
countries, multiplied with country-level generation data per fuel type
from IEA’s ‘Electricity Information’ [30]. [30] has also been used to
calibrate generation values for most fuel types. Unfortunately, the report
does not differentiate generation values for solar and wind and does not
include data for all countries around the world. Hence for solar and wind

as well as for other renewable technologies where country-level gener­
ation data is missing we used an additional dataset from IRENA [36].
Comparison of the benchmark data with simulation results based on the
calibrated model input can be found in section 3.


_2.5. Model availability_


The full model (and its future updates) in raw data format as well as
the input datasets for PLEXOS-World are available at [25] and we use
the ‘FAIR Guiding Principles for scientific data management and stew­
ardship’ for dissemination [63], allowing users to modify or recreate the
model in other simulation environments. FAIR encourages the find­
ability, accessibility, interoperability, and reuse of digital assets. The
principles emphasize machine-actionability, in essence the capacity of
computational systems to find, access, interoperate, and reuse data with
none or minimal human intervention because of the increasing reliance
on computational support to deal with data as a result of the increase in
volume, complexity, and creation speed of data.



**3. Results**


This section includes a benchmarking exercise in which the cali­
brated model simulation results of the over 30,000 simulated power
plants are being compared to historical data with 2015 as base year.
Benchmarking is undertaken at an aggregated continental and country
level and not at plant level as this model is intended to allow users to
examine large scale and continental power systems. Users have the op­
tion to downscale the spatial size of the model simulations yet would
have to undertake their own calibration.


Fig. 5 showcases a comparison between the overall generation and
CO2 emissions on a continental and global level from the PLEXOS-World
simulations with historically reported data. Main observations based on
the graphs are that both the generation as well as the emissions are
generally in line with reported data. Small deviations exist with the
reported generation values, predominantly in Asia and Europe, which
can be the result of a combination of factors.

First, the use of different datasets for input and calibration can lead
to small yet insuperable differences. The overall demand for every
country within the model, determining the required generation, has
been based on World Bank data, whereas the reported 2015 generation
values are based on IEA and IRENA datasets. Furthermore, although
load shedding in mostly developing countries is not uncommon, limited
occurrence of unserved energy (global total of 92.4 TWh on 24,000 TWh
demand) in especially sub-country nodes indicates a possible limitation
of the assumption of relative distribution of missing power plant ca­
pacities based on the existing share of capacity per sub-country node
within the WRI database. It is likely that as a result of said assumption
slight underestimation of power plant capacity in a specific sub-country
node can occur in favor of another and vice versa. Yet, due to a lack of
openly available robust datasets including sub-country level power plant
capacities the current approach is near optimal.
Finally, besides the technical potential for power flow, to-date there
are no restrictions implemented in the model regarding trade of elec­
tricity between nodes which can lead to overestimation of flows and
consequently underestimation of domestic generation. Current model
results indicate a significant flow from European nodes to Asia (mostly
Russia) contributing to the slight differences with historically reported
data in both continents. Comparison of the overall continental emissions
with reported data as shown in Fig. 5 indicates a similar story, values are
generally in line, with small differences mostly as a result of the
described differences in required generation.



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/brinkerinkBuildingCalibratingCountryLevel2021/brinkerinkBuildingCalibratingCountryLevel2021.pdf-7-0.png)

**Fig. 5.** Comparison of the overall generation values and CO2 emissions from the calibrated PLEXOS-World simulations with historically reported data for 2015.


8


_M. Brinkerink et al._ _Energy Strategy Reviews 33 (2021) 100592_

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/brinkerinkBuildingCalibratingCountryLevel2021/brinkerinkBuildingCalibratingCountryLevel2021.pdf-8-0.png)


**Fig. 6.** Comparison of the difference in generation- and emission values per fuel type for 2015 between the benchmark- and calibrated simulation values. Total global
generation in 2015 was 24,267 TWh. A value of 0 indicates that the benchmark and simulation values are exactly equal, negative values indicate that simulation
values from PLEXOS-World are lower compared to the benchmark values and positive values vice versa.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/brinkerinkBuildingCalibratingCountryLevel2021/brinkerinkBuildingCalibratingCountryLevel2021.pdf-8-1.png)


**Fig. 7.** Comparison of normalized generation values per fuel type for the top 10 countries with highest 2015 electricity demand. Score of 1 indicates that the
calibrated simulation value is equal to the reported benchmark value, _<_ 1 is shortage, _>_ 1 is surplus. Total generation within the PLEXOS-World simulations per fuel
type and country is indicated on the X-axis with a logarithmic scale.


9


_M. Brinkerink et al._ _Energy Strategy Reviews 33 (2021) 100592_



Fig. 6 shows a more detailed view on both aspects by comparing the
historical and simulated generation and emission values per fuel type.
More detailed graphs that include comparisons with total emission- and
generation values per fuel type and continent can be found in S3 of the
supplementary material [25]. The generation output of operationally
low-cost technologies such as coal, hydro, nuclear, solar and wind has
been calibrated at country level through an iterative process to come as
close as possible to reported 2015 generation values. This has generally
been successful, yet the earlier indicated differences in total generation
leads in certain cases to a mismatch in the overall use of peaking power
plants based on gas and oil compared to historically reported data. These
power plants are generally at the end of the merit order (2015 context
with higher gas prices), and hence dispatched last or switched off first
making it most susceptible of all power plant types to changes in
demand.

Next to an overall deviation in use of peaking power plants, there is
also a slight mismatch in the relative use of oil versus gas in countries
where both fuel types compete. The main reason for this mismatch is the
approach used to scale missing power plant capacities based on relative
influence of coal, gas and oil in the WRI database for countries where no
capacity data is available in the IEA datasets. It is possible that the
country-level power plant capacity of a specific fossil fuel is under­
estimated, meaning that the theoretical generation potential is insuffi­
cient to reach the benchmark values. The reason that this is especially
visible in Africa is that relatively speaking Africa is underrepresented in
the WRI database compared to other continents. Furthermore, to-date
secondary fuels for thermal power plants are not incorporated in the
model which affects the use of oil and gas.
These aspects are also visible on a country-level as indicated in Fig. 7.
Utilization of gas and oil-based power plants is controlled by means of its
fuel price, with oil prices calibrated at country-level to optimize the
balance in use of both fuel types compared to historical data. Despite
this, in certain cases oil is slightly underutilized in favor of gas and vice
versa. Yet, it is important to realize that in absolute terms the role of oil
for the purpose of power generation is very limited (see S3 of the sup­
plementary material [25]). Overall deviations in the use of gas
compared to the benchmark values are mostly as a result of lower or
higher required generation in the model. The underutilization of oil in
India results from data discrepancies in the different datasets. The IEA
reports a gross electricity production from oil in 2015 of almost 23 TWh

[30], whereas the diesel-based installed capacity according to India’s
Central Electricity Authority in March 2015 was 1.2 GW [32] and in
March 2016 only 0.99 GW [64]. Even at full utilization this would lead

–
to a maximum generation potential of 8.7 10.5 TWh. The relatively low
usage of gas in China is a direct result from the earlier described limi­
tations in sub-country allocation of generator capacities as well as a
slightly lower total demand compared to benchmark generation values.
That said, the role of gas for power generation in China is limited
compared to other fuel types. Beyond gas and oil, the graph shows that
country-level total generation as well as generation from baseload- and
other low-cost technologies is generally in line with historical genera­
tion values.


**4. Discussion**


This paper describes the model development of a first-of-its-kind
reference detailed hourly global power system model at plant and
country level. The model – dubbed PLEXOS-World after the simulation
software used – can simulate the dispatch of over 30,000 individual
power plants representing 164 countries spread out over 265 nodes.
Alongside the existing storage facilities around the world as well as the



globally existing cross-border transmission capacities, the model opti­
mizes the supply of electricity to match the system demand by mini­
mizing the overall operational system cost.
We’ve shown that the model can be a useful tool for the simulation of

the global power system through a benchmarking exercise of calibrated
simulation results with historical data for 2015. That said, the model is
as strong as its input data and the underlying model assumptions. Sig­
nificant improvements can still be made, for example regarding the
representation of existing power plant portfolios, the level of spatial
detail in aspects such as fuel- and carbon prices and by incorporating a
wider range of data years for demand- and variable renewable profiles

[35]. The main strength of the model is therefore not in its absolute
accuracy but in its openness, adaptability and flexibility for other users.
All model input is available as supplementary material [25] to allow
other users to modify the model in PLEXOS or recreate the model in
other simulation environments. This includes a full global dataset of
cross-border transmission capacities, hourly demand profiles, and
plant-specific capacity factor profiles for existing hydro, solar and wind
power plants. The model can be used for assessments on the global scale,
but it is as easy to zoom in on a specific country or area in the world
allowing it to be used for a wide range of research. The model is setup in
a straight-forward fashion that makes it easy for users to switch to more
accurate and detailed data for specific regions while modelling other
areas with base data (or exclude completely).
The study has given us some valuable insights in the availability,
importance- and strength of open data initiatives [24]. Nonetheless, it
has also highlighted the still existing data gaps in especially areas
outside Europe and North-America as well as the general difficulty of
dealing with data discrepancies while using multiple large datasets. The
study also showcased the clear differences in power plant portfolios and
overall power system characteristics in different parts of the world. This
latter aspect highlights once again that there is no single uniform
pathway in the energy transition and decarbonization of the global
power system, fueling the importance of modelling tools like
PLEXOS-World to support research in this area.
In future research, the model will be used as a reference model based
on which a range of global decarbonization pathways will be assessed.
For example, advanced analyses of the concept of a globally inter­
connected power grid [3,13,17] will be conducted as well as the appli­
cation of known soft-linking techniques [65] to investigate the technical
feasibility of projected power systems in global scenarios as constructed
by integrated assessment models.


**CRedit**


**Maarten Brinkerink:** Conceptualization, Methodology, Validation,
Formal analysis, Data Curation, Writing - Original Draft, Writing - Re­
view & Editing, Visualization. **Brian O Gallach** [´] **oir:** ´ Supervision, Fund­
ing acquisition. **Paul Deane:** Conceptualization, Methodology, Writing Review & Editing, Supervision, Project administration, Funding
acquisition.


**Declaration of competing interest**


The authors declare that they have no known competing financial
interests or personal relationships that could have appeared to influence
the work reported in this paper.


**Acknowledgements**


The authors acknowledge the support provided by Energy Exemplar



10


_M. Brinkerink et al._ _Energy Strategy Reviews 33 (2021) 100592_



and Science Foundation Ireland (SFI) MaREI centre (12/RC/2302).
Furthermore, we would like to express our gratitude towards Bruce
Owen (Manitoba Hydro), David Gernaat (PBL Netherlands Environ­
mental Assessment Agency), Gang He (Stony Brook University), Lynn StLaurent (Hydro Quebec), Raman Mall (SaskPower) and UKNEGRO for
providing operational electricity system data in context of this study.
Finally, we thank Alparslan Zehir (University College Cork) for peerreviewing draft versions of this paper.


**References**


[1] D.L. McCollum, W. Zhou, C. Bertram, et al., Energy investment needs for fulfilling
the paris agreement and achieving the sustainable development goals, Nature
[Energy 3 (2018) 589–599, https://doi.org/10.1038/s41560-018-0179-z.](https://doi.org/10.1038/s41560-018-0179-z)

[[2] J. Rogelj, D. Shindell, K. Jiang, et al., Mitigation pathways compatible with 1.5](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref2) [◦] C
[in the context of sustainable development, in: Global Warming of 1.5](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref2) [◦] C. An IPCC
[Special Report on the Impacts of Global Warming of 1.5](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref2) [◦] C above Pre-industrial
[Levels and Related Global Greenhouse Gas Emission Pathways, in the Context of](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref2)
[Strengthening the Global Response to the Threat of Climate Change, 2018,](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref2)

[[3] M. Brinkerink, B.pp. 93–174.](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref2) O. Gallach [´] oir, P. Deane, A comprehensive review on the benefits ´
and challenges of global power grids and intercontinental interconnectors, Renew.

[4] J.P. Deane, G. Drayton, B.P. Sustain. Energy Rev. 107 (2019), O Gallach [´] [https://doi.org/10.1016/j.rser.2019.03.003oir, The impact of sub-hourly modelling in ´](https://doi.org/10.1016/j.rser.2019.03.003) .
power systems with significant levels of renewable generation, Appl. Energy 113

[[5] S. Collins, J.P. Deane, B. (2014) 152–158, https://doi.org/10.1016/j.apenergy.2013.07.027O Gallach](https://doi.org/10.1016/j.apenergy.2013.07.027) [´] oir, Adding value to EU energy policy analysis ´ .
using a multi-model approach with an EU-28 electricity dispatch model, Energy
[130 (2017) 433–447, https://doi.org/10.1016/j.energy.2017.05.010.](https://doi.org/10.1016/j.energy.2017.05.010)

[6] W. Zappa, M. Junginger, M. van den Broek, Is a 100% renewable European power
[system feasible by 2050? Appl. Energy 233–234 (2019) 1027–1050, https://doi.](https://doi.org/10.1016/j.apenergy.2018.08.109)
[org/10.1016/j.apenergy.2018.08.109.](https://doi.org/10.1016/j.apenergy.2018.08.109)

[7] A. Purvins, L. Sereno, M. Ardelean, et al., Submarine power cable between Europe
[and North America: a techno-economic analysis, J. Clean. Prod. (2018), https://](https://doi.org/10.1016/j.jclepro.2018.03.095)
[doi.org/10.1016/j.jclepro.2018.03.095.](https://doi.org/10.1016/j.jclepro.2018.03.095)

[8] A.S. Brouwer, M. van den Broek, W. Zappa, et al., Least-cost options for integrating
intermittent renewables in low-carbon power systems, Appl. Energy 161 (2016)
[48–74, https://doi.org/10.1016/j.apenergy.2015.09.090.](https://doi.org/10.1016/j.apenergy.2015.09.090)

[9] D. Hess, The empirical probability of integrating CSP and its cost optimal
configuration in a low carbon energy system of EUMENA, Sol. Energy 166 (2018)
[267–307, https://doi.org/10.1016/j.solener.2018.03.034.](https://doi.org/10.1016/j.solener.2018.03.034)

[10] S. Chatzivasileiadis, D. Ernst, G. Andersson, The global grid, Renew. Energy 57
[(2013) 372–383, https://doi.org/10.1016/j.renene.2013.01.032.](https://doi.org/10.1016/j.renene.2013.01.032)

[11] E. Bompard, D. Grosso, T. Huang, et al., World decarbonization through global
[electricity interconnections, Energies 11 (2018) 1–29, https://doi.org/10.3390/](https://doi.org/10.3390/en11071746)
[en11071746.](https://doi.org/10.3390/en11071746)

[[12] M. Biberacher, Modelling and Optimisation of Future Energy System Using Spatial](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref12)
[and Temporal Methods, Dissertation. University of Augsburg, 2004.](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref12)

[13] M. Brinkerink, P. Deane, S. Collins, et al., Developing a global interconnected
[power system model, Global Energy Interconnection 1 (2018) 330–343, https://](https://doi.org/10.14171/j.2096-5117.gei.2018.03.004)
[doi.org/10.14171/j.2096-5117.gei.2018.03.004.](https://doi.org/10.14171/j.2096-5117.gei.2018.03.004)

[[14] T. Aboumahboub, K. Schaber, P. Tzscheutschler, et al., Optimization of the](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref14)
[utilization of renewable energy sources in the electricity sector. EE’10 Proceedings](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref14)
[of the 5th IASME/WSEAS International Conference on Energy & Environment,](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref14)
[World Scientific and Engineering Academy and Society (WSEAS), Wisconsin, 2010,](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref14)
[pp. 196–204.](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref14)

[[15] K. Ummel, Global Prospects for Utility-Scale Solar Power : toward Spatially Explicit](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref15)
[Modeling of Renewable Energy Systems Working, 2010. Paper 235 December](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref15)
[2010.](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref15)

[16] E. Halawa, G. James, X. Shi, et al., The prospect for an Australian–asian power grid:
[a critical appraisal, Energies 11 (2018) 200, https://doi.org/10.3390/](https://doi.org/10.3390/en11010200)
[en11010200.](https://doi.org/10.3390/en11010200)

[[17] J. Yu, K. Bakic, A. Kumar, et al., TB 775 - Global Electricity Network - Feasibility](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref17)
[Study, 2019, ISBN 9782858734771.](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref17)

[18] M.Z. Jacobson, M.A. Delucchi, Z.A.F. Bauer, et al., 100% clean and renewable
wind, water, and sunlight all-sector energy roadmaps for 139 countries of the
[world, Joule 1 (2017) 108–121, https://doi.org/10.1016/j.joule.2017.07.005.](https://doi.org/10.1016/j.joule.2017.07.005)

[19] D. Bogdanov, J. Farfan, K. Sadovskaia, et al., Radical transformation pathway
towards sustainable electricity via evolutionary steps, Nat. Commun. 10 (2019)
[1–16, https://doi.org/10.1038/s41467-019-08855-1.](https://doi.org/10.1038/s41467-019-08855-1)

[[20] C. Barrows, M. Hummon, W. Jones, et al., Time Domain Partitioning of Electricity](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref20)
[Production Cost Simulations, 2014. NREL/TP-6A20-60969.](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref20)

[[21] L. Byers, J. Friedrich, R. Hennig, et al., A Global Database of Power Plants.](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref21)
[Washington DC, 2018.](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref21)

[22] W. Medjroubi, U.P. Müller, M. Scharf, et al., Open data in power grid modelling:
new approaches towards transparent grid models, Energy Rep. 3 (2017) 14–21,
[https://doi.org/10.1016/j.egyr.2016.12.001.](https://doi.org/10.1016/j.egyr.2016.12.001)

[23] S. Pfenninger, I. Staffell, Long-term patterns of European PV output using 30 years
of validated hourly reanalysis and satellite data, Energy 114 (2016) 1251–1265,
[https://doi.org/10.1016/j.energy.2016.08.060.](https://doi.org/10.1016/j.energy.2016.08.060)

[24] S. Pfenninger, J. DeCarolis, L. Hirth, et al., The importance of open data and
software: is energy research lagging behind? Energy Pol. 101 (2017) 211–215,
[https://doi.org/10.1016/j.enpol.2016.11.046.](https://doi.org/10.1016/j.enpol.2016.11.046)




[[25] M. Brinkerink, P. Deane, PLEXOS World 2015, 2020, https://doi.org/10.7910/](https://doi.org/10.7910/DVN/CBYXBY)
[DVN/CBYXBY. Harvard Dataverse, V3. UNF6+rCz+q2zqJyLE0VRQCABZA==](https://doi.org/10.7910/DVN/CBYXBY)

[fileUNF].

[[26] H. Holttinen, A. Orths, H. Abildgaard, et al., IEA Wind Expert Group Report on](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref26)
[Recommended Practices: 16, 2013. Wind Integration Studies. 89.](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref26)

[[27] EIA, International, 2018. https://www.eia.gov/international/data/world/electrici](https://www.eia.gov/international/data/world/electricity/electricity-capacity/)
[ty/electricity-capacity/. (Accessed 24 August 2018).](https://www.eia.gov/international/data/world/electricity/electricity-capacity/)

[[28] ENTSO-E, Installed Capacity Per Production Type, 2019. https://transparency.ent](https://transparency.entsoe.eu/generation/r2/installedGenerationCapacityAggregation/show)
[soe.eu/generation/r2/installedGenerationCapacityAggregation/show. (Accessed](https://transparency.entsoe.eu/generation/r2/installedGenerationCapacityAggregation/show)
18 October 2019).

[[29] IEA, World Energy Outlook 2017. Paris, 2017, ISBN 9789264243668, https://doi.](https://doi.org/10.1016/0301-4215(73)90024-4)
[org/10.1016/0301-4215(73)90024-4.](https://doi.org/10.1016/0301-4215(73)90024-4)

[[30] IEA, Electricity Information 2017. Paris, 2017, ISBN 978-92-64-27813-4.](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref30)

[[31] IRENA, REmap 2030 Renewable Energy Prospects for Russian Federation, Working](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref31)
[Paper. Abu Dhabi, 2017, ISBN 978-92-9260-022-8.](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref31)

[32] CEA, All India Installed Capacity (In MW) of Power Stations, 2015. As on
[31.03.2015, http://cea.nic.in/reports/monthly/installedcapacity/2015/installed](http://cea.nic.in/reports/monthly/installedcapacity/2015/installed_capacity-03.pdf)

[[33] J.P. Deane, M. _capacity-03.pdfO Ciar](http://cea.nic.in/reports/monthly/installedcapacity/2015/installed_capacity-03.pdf) [´] . (Accessed 21 April 2019). ain, B.P. ´ O Gallach [´] oir, An integrated gas and electricity model ´
of the EU energy system to examine supply interruptions, Appl. Energy 193 (2017)
[479–490, https://doi.org/10.1016/j.apenergy.2017.02.039.](https://doi.org/10.1016/j.apenergy.2017.02.039)

[34] A. Molod, L. Takacs, M. Suarez, et al., Development of the GEOS-5 atmospheric
general circulation model: evolution from MERRA to MERRA2, Geosci. Model Dev.

[35] S. Collins, P. Deane, B. (GMD) 8 (2015) 1339–O Gallach1356, [´] [https://doi.org/10.5194/gmd-8-1339-2015oir, et al., Impacts of inter-annual wind and solar ´](https://doi.org/10.5194/gmd-8-1339-2015) .
[variations on the European power system, Joule 2 (2018) 2076–2090, https://doi.](https://doi.org/10.1016/j.joule.2018.06.020)
[org/10.1016/j.joule.2018.06.020.](https://doi.org/10.1016/j.joule.2018.06.020)

[[36] IRENA, Renewable Energy Statistics 2019. Abu Dhabi, 2019, ISBN 978-92-9260-](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref36)
[033-4.](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref36)

[37] I. Staffell, S. Pfenninger, Using bias-corrected reanalysis to simulate current and
[future wind power output, Energy 114 (2016) 1224–1239, https://doi.org/](https://doi.org/10.1016/j.energy.2016.08.068)
[10.1016/j.energy.2016.08.068.](https://doi.org/10.1016/j.energy.2016.08.068)

[38] B. Lehner, C.R. Liermann, C. Revenga, et al., High-resolution mapping of the
world’s reservoirs and dams for sustainable river-flow management, Front. Ecol.
[Environ. 9 (2011) 494–502, https://doi.org/10.1890/100125.](https://doi.org/10.1890/100125)

[39] D.E.H.J. Gernaat, P.W. Bogaart, D.P.V. Vuuren, et al., High-resolution assessment
of global technical and economic hydropower potential, Nature Energy 2 (2017)
[821–828, https://doi.org/10.1038/s41560-017-0006-y.](https://doi.org/10.1038/s41560-017-0006-y)

[40] X. Luo, J. Wang, M. Dooner, et al., Overview of current development in electrical
energy storage technologies and the application potential in power system
[operation, Appl. Energy 137 (2015) 511–536, https://doi.org/10.1016/j.](https://doi.org/10.1016/j.apenergy.2014.09.081)
[apenergy.2014.09.081.](https://doi.org/10.1016/j.apenergy.2014.09.081)

[41] F. Geth, T. Brijs, J. Kathan, et al., An overview of large-scale stationary electricity
storage plants in Europe: current status and new developments, Renew. Sustain.
[Energy Rev. 52 (2015) 1212–1227, https://doi.org/10.1016/j.rser.2015.07.145.](https://doi.org/10.1016/j.rser.2015.07.145)

[[42] M.W.H. Americas, Technical analysis of pumped storage and integration with wind](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref42)
[power in the pacific northwest final report, 2009.](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref42)

[43] D. Bogdanov, C. Breyer, North-East Asian Super Grid for 100% renewable energy
supply: optimal mix of energy technologies for electricity, gas and heat supply
[options, Energy Convers. Manag. 112 (2016) 176–190, https://doi.org/10.1016/j.](https://doi.org/10.1016/j.enconman.2016.01.019)
[enconman.2016.01.019.](https://doi.org/10.1016/j.enconman.2016.01.019)

[[44] ENTSO-E, Consumption Data: Hourly Load Values 2006-2015, 2019. https://doc](https://docstore.entsoe.eu/Documents/Publications/Statistics/Monthly-hourly-load-values_2006-2015.xlsx)
[store.entsoe.eu/Documents/Publications/Statistics/Monthly-hourly-load-value](https://docstore.entsoe.eu/Documents/Publications/Statistics/Monthly-hourly-load-values_2006-2015.xlsx)
[s_2006-2015.xlsx. (Accessed 11 November 2018).](https://docstore.entsoe.eu/Documents/Publications/Statistics/Monthly-hourly-load-values_2006-2015.xlsx)

[[45] ENTSO-E, Total Load - Day Ahead/Actual, 2020. https://transparency.entsoe.eu/lo](https://transparency.entsoe.eu/load-domain/r2/totalLoadR2/show)
[ad-domain/r2/totalLoadR2/show. (Accessed 11 December 2018).](https://transparency.entsoe.eu/load-domain/r2/totalLoadR2/show)

[[46] The World Bank, DataBank, 2019. https://databank.worldbank.org/home.aspx.](https://databank.worldbank.org/home.aspx)
(Accessed 4 September 2019).

[47] A. Toktarova, L. Gruber, M. Hlusiak, et al., Long term load projection in high
resolution for all countries globally, Int. J. Electr. Power Energy Syst. 111 (2019)
[160–181, https://doi.org/10.1016/j.ijepes.2019.03.055.](https://doi.org/10.1016/j.ijepes.2019.03.055)

[[48] Openmod Transmission network datasets. https://wiki.openmod-initiative.org/](https://wiki.openmod-initiative.org/wiki/Transmission_network_datasets)
[wiki/Transmission_network_datasets. (Accessed 13 March 2019).](https://wiki.openmod-initiative.org/wiki/Transmission_network_datasets)

[49] C. Arderne, C. Zorn, C. Nicolas, et al., Predictive mapping of the global power
[system using open data, Scientific Data 7 (2020) 19, https://doi.org/10.1038/](https://doi.org/10.1038/s41597-019-0347-4)
[s41597-019-0347-4.](https://doi.org/10.1038/s41597-019-0347-4)

[50] C. Taliotis, A. Shivakumar, E. Ramos, et al., An indicative analysis of investment
opportunities in the African electricity supply sector - using TEMBA (The
Electricity Model Base for Africa), Energy for Sustainable Development 31 (2016)

[[51] CIER, Síntesis Informativa Energ50sector energ–66, https://doi.org/10.1016/j.esd.2015.12.001´etico en países de America del Sur, America Central y El Caribe, ´etica de los Países de la CIER; Informaci.](https://doi.org/10.1016/j.esd.2015.12.001) on del ´
[Montevideo, 2016.](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref51)

[[52] The World Bank, Middle East and North Africa; Integration of Electricity Networks](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref52)
[in the Arab World; Regional Market Structure and Design, 2013. Report No:](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref52)
[ACS7124.](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref52)

[53] T. Sumranwanich, Development of Cross – Border Trade between Thailand and
[Neighboring Countries, 2014. https://dokumen.tips/documents/development-o](https://dokumen.tips/documents/development-of-cross-border-trade-between-thailand-and-4-bangkokdevelopmedevelopment.html)
[f-cross-border-trade-between-thailand-and-4-bangkokdevelopmedevelopment.](https://dokumen.tips/documents/development-of-cross-border-trade-between-thailand-and-4-bangkokdevelopmedevelopment.html)
[html. (Accessed 11 November 2018).](https://dokumen.tips/documents/development-of-cross-border-trade-between-thailand-and-4-bangkokdevelopmedevelopment.html)

[54] E. Toh, Interconnections in Southeast Asia : Implications for Singapore and the
[Region, 2016. https://www.ceer.eu/documents/104400/-/-/4738276b-5fe6-88f3-](https://www.ceer.eu/documents/104400/-/-/4738276b-5fe6-88f3-48f7-47eab474ed8c%0A)
[48f7-47eab474ed8c%0A. (Accessed 11 November 2018).](https://www.ceer.eu/documents/104400/-/-/4738276b-5fe6-88f3-48f7-47eab474ed8c%0A)

[[55] ENTSO-E, TYNDP 2016 Scenario Development Report. Brussels, 2015.](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref55)



11


_M. Brinkerink et al._ _Energy Strategy Reviews 33 (2021) 100592_




[[56] ENTSO-E, Project List TYNDP2016 Assessments, 2016. https://www.entsoe.](https://www.entsoe.eu/Documents/TYNDP%20documents/TYNDP%202016/rgips/Project%20list%20TYNDP2016%20assessments.xlsx)
[eu/Documents/TYNDP documents/TYNDP 2016/rgips/Project list TYNDP2016](https://www.entsoe.eu/Documents/TYNDP%20documents/TYNDP%202016/rgips/Project%20list%20TYNDP2016%20assessments.xlsx)
[assessments.xlsx. (Accessed 12 March 2019).](https://www.entsoe.eu/Documents/TYNDP%20documents/TYNDP%202016/rgips/Project%20list%20TYNDP2016%20assessments.xlsx)

[[57] ENTSO-E, Forecasted Transfer Capacities - Month Ahead, 2019. https://transparen](https://transparency.entsoe.eu/transmission-domain/r2/forecastedTransferCapacitiesMonthAhead/show)
[cy.entsoe.eu/transmission-domain/r2/forecastedTransferCapacitiesMonthAhead/](https://transparency.entsoe.eu/transmission-domain/r2/forecastedTransferCapacitiesMonthAhead/show)
[show. (Accessed 25 February 2019).](https://transparency.entsoe.eu/transmission-domain/r2/forecastedTransferCapacitiesMonthAhead/show)

[[58] ENTSO-E, Forecasted Transfer Capacities - Year Ahead, 2019. https://transparenc](https://transparency.entsoe.eu/transmission-domain/ntcYear/show)
[y.entsoe.eu/transmission-domain/ntcYear/show. (Accessed 25 February 2019).](https://transparency.entsoe.eu/transmission-domain/ntcYear/show)

[[59] ENTSO-E, Cross-Border Physical Flow, 2019. https://transparency.entsoe.eu/trans](https://transparency.entsoe.eu/transmission-domain/physicalFlow/show)
[mission-domain/physicalFlow/show. (Accessed 25 February 2019).](https://transparency.entsoe.eu/transmission-domain/physicalFlow/show)

[[60] BP, BP Statistical Review of World Energy 2019, 2019.](http://refhub.elsevier.com/S2211-467X(20)30145-0/sref60)

[[61] The World Bank, Carbon Pricing Dashboard, 2019. https://carbonpricingdashb](https://carbonpricingdashboard.worldbank.org/)
[oard.worldbank.org/. (Accessed 26 September 2019).](https://carbonpricingdashboard.worldbank.org/)




[[62] IEA, CO2 Emissions from Fuel Combustion 2017. Paris, 2017, https://doi.org/](https://doi.org/10.1787/co2_fuel-2007-en-fr)
[10.1787/co2_fuel-2007-en-fr.](https://doi.org/10.1787/co2_fuel-2007-en-fr)

[63] M.D. Wilkinson, M. Dumontier, IjJ. Aalbersberg, et al., Comment: the FAIR Guiding
Principles for scientific data management and stewardship, Scientific Data 3 (2016)
[1–9, https://doi.org/10.1038/sdata.2016.18.](https://doi.org/10.1038/sdata.2016.18)

[64] CEA, All India Installed Capacity (In MW) of Power Stations, 2016. As on
[31.03.2016, http://cea.nic.in/reports/monthly/installedcapacity/2016/installed](http://cea.nic.in/reports/monthly/installedcapacity/2016/installed_capacity-03.pdf)
[_capacity-03.pdf. (Accessed 19 February 2020).](http://cea.nic.in/reports/monthly/installedcapacity/2016/installed_capacity-03.pdf)

[65] J.P. Deane, A. Chiodi, M. Gargiulo, et al., Soft-linking of a power systems model to
[an energy systems model, Energy 42 (2012) 303–312, https://doi.org/10.1016/j.](https://doi.org/10.1016/j.energy.2012.03.052)
[energy.2012.03.052. https://energyexemplar.com/plexos-world/. https://energye](https://doi.org/10.1016/j.energy.2012.03.052)
[xemplar.com/plexos-world.](https://energyexemplar.com/plexos-world)



12


