# Citation Key: horschPyPSAEurOpenOptimisation2018

---

## PyPSA-Eur: An Open Optimisation Model of the European Transmission System [$]

<PERSON> [a,b,], <PERSON> [b], <PERSON> [b], <PERSON> [a,b]


_a_ _Institute for Automation and Applied Informatics, Karlsruhe Institute of Technology,_
_76344 Eggenstein-Leopoldshafen, Germany_
_b_ _Frankfurt Institute for Advanced Studies, 60438 Frankfurt am Main, Germany_


**Abstract**


PyPSA-Eur, the first open model dataset of the European power system at
the transmission network level to cover the full ENTSO-E area, is presented.
It contains 6001 lines (alternating current lines at and above 220 kV voltage
level and all high voltage direct current lines), 3657 substations, a new open
database of conventional power plants, time series for electrical demand and
variable renewable generator availability, and geographic potentials for the expansion of wind and solar power. The model is suitable both for operational
studies and generation and transmission expansion planning studies. The continental scope and highly resolved spatial scale enables a proper description of the
long-range smoothing effects for renewable power generation and their varying
resource availability. The restriction to freely available and open data encourages the open exchange of model data developments and eases the comparison
of model results. A further novelty of the dataset is the publication of the full,
automated software pipeline to assemble the load-flow-ready model from the
original datasets, which enables easy replacement and improvement of the individual parts. This paper focuses on the description of the network topology,
the compilation of a European power plant database and a top-down load timeseries regionalisation. It summarises the derivation of renewable wind and solar
availability time-series from re-analysis weather datasets and the estimation of
renewable capacity potentials restricted by land-use. Finally, validations of the
dataset are presented, including a new methodology to compare geo-referenced
network datasets to one another.


_Keywords:_ Electricity system model, renewable power generation,
transmission network, power plant dataset


**1. Introduction**


The energy system in Europe is undergoing a far-reaching transformation
on multiple fronts: generation from variable renewable energy sources, such
as wind and solar power, is growing due to the imperative of tackling climate
change; electricity provision has been unbundled and liberalised, raising complex


$ c _⃝_ [2018, CC BY-NC-ND 4.0, eprint of doi:10.1016/j.esr.2018.08.012](https://creativecommons.org/licenses/by-nc-nd/4.0/)

_∗_ Corresponding author
_Email address:_ `<EMAIL>` (Jonas H¨orsch)


_Preprint submitted to Energy Strategy Reviews_ _October 3, 2018_


challenges for the efficient design and regulation of electricity markets; the need
to decarbonise heating and transport is driving electrification of these sectors;
and finally energy markets are being integrated across the continent [1].
To study this transformation, accurate modelling of the transmission grid is
required. The need to take account of international electricity trading and the
possibility of smoothing variable renewable feed-in over large distances (wind
generation has a typical correlation length of around 600 km [2]) mean that
models should have a continental scope. At the same time, high spatial detail
is required, since national grid bottlenecks are already hindering the uptake
of renewable energy today [3], and given persistent public acceptance problems
facing new transmission projects [4], severe grid bottlenecks will remain a feature
of the energy system for decades to come.
Currently there is no openly-available model of the full European transmission network with which researchers can investigate and compare different approaches to the energy transformation. The transmission grid dataset provided
by the European Network of Transmission System Operators for Electricity
(ENTSO-E) for the 2016 Ten Year Network Development Plan (TYNDP) [5] is
rendered unusable by restrictive licensing, the exclusion of Finland, Norway and
Sweden, and a lack of geographical localisation of the represented substations.
The lack of geo-data means that the crucial weather system correlations and dynamics cannot be mapped onto the network. In 2005 in [6] an openly-available
model of the continental European transmission network (i.e. excluding the
UK, Ireland, Scandinavia and the Baltic states) was presented using a manual
matching of buses and lines to the raster graphic of the ENTSO-E map, along
with an open power plant database based on the Global Energy Observatory

[7]; this model was updated to the network of 2009 in [8]. Apart from not
covering the full ENTSO-E area, this dataset has the problem that much of
the data was extracted manually, which is potentially error-prone and hard to
repeat as new data becomes available, the buses are missing geo-coordinates
and the power plant database is incomplete. In [9, 10] geo-coordinates and data
for wind and solar plants were added to the dataset. Open datasets based on
OpenStreetMap [11], such as the SciGRID network [12] and the osmTGmod [13]
network, are of high quality in Germany, where data is well organised, but are
not yet accurate for the rest of Europe. Similarly the open electricity model provided by the German Institute for Economic Research (DIW), ELMOD-DE [14],
only covers Germany.
In this paper we present a model of the European power system at the
transmission network level which remedies these many deficiencies: it is not
only open but also contains a high level of detail for the full ENTSO-E area.
Grid data is provided by an automatic extraction of the ENTSO-E grid map; a
power plant database is presented using a sophisticated algorithm that matches
records from a wide range of available sources and includes geo-data; other
data, such as time series for electrical load and wind, solar and hydro-electric
availability, and geographic potentials for the expansion of wind and solar power,
are also described. A new technique for comparing network datasets is presented
and used to validate the grid data. The dataset and all code used to generate
it from the raw data are available online [15, 16], as a model for the Python for
Power System Analysis (PyPSA) framework version 0.13.1 [17, 18].
In Section 2 the data sources and processing methods are presented; the
data is validated in Section 3; limitations of the dataset are discussed in Section


2


Volt. Wires Series Series ind. Shunt Current App. power
level resist. reactance capacit. therm. limit therm. limit
(kV) (Ω/km) (Ω/km) (nF/km) (A) (MVA)


220 2 0 _._ 06 0 _._ 301 12 _._ 5 1290 492

300 3 0 _._ 04 0 _._ 265 13 _._ 2 1935 1005

380 4 0 _._ 03 0 _._ 246 13 _._ 8 2580 1698


Table 1: Standard line types for overhead AC lines [23]


4; conclusions are drawn in Section 5.


**2. Data sources and methods**


_2.1. Network topology_

The network topology and geography of substations and transmission lines
have been extracted from the geographical vector data of the online ENTSO-E
Interactive Map [19] by the GridKit toolkit [20]. We did not use the published
extract at [21], since several errors like inadvertently duplicated alternating
current (AC) lines and missing transformers and lines between substations of
short distances below 1 km have been identified.
Instead we extended the GridKit toolkit [1] :


1. A python script was added to stitch the vector tiles from the ENTSO-E
map according to the identifier attribute ‘oid’ before importing the line
structures into GridKit, since the toolkit previously often mislabeled the
overlapping parts of the same line as two separate circuits.


2. The tolerance for connecting dangling high-voltage direct current (HVDC)
lines and their converter stations with the next high-voltage alternating
current (HVAC) substations has been increased and the new connections
have been manually verified.


3. AC lines carrying circuits of several voltage levels had to be split by inspecting the descriptive text tag and are split into several lines.


4. The columns and format of the extracted CSV lines have been aligned
closer with PyPSA to simplify the subsequent import.


The electrical parameters are derived by assuming the standard AC line
types in Table 1 for the length and number of circuits. The DC line capacities
are assigned from the table in [22]. No transformer information is contained
in the map, so a single transformer of capacity 2 GW (i.e. equivalent to four
500 MW transformers) is placed between buses of different voltage levels at
the same location, with a reactance of 0.1 per unit. The transformer capacity
assumption is on the high side to avoid introducing constraints where none exist
in reality.
The restriction to buses and transmission lines of the voltage levels 220 kV,
300 kV and 380 kV in the landmass or exclusive economic zones of the European


1 The modified toolkit has been published at `[https://github.com/PyPSA/GridKit/](https://github.com/PyPSA/GridKit/)` .


3


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/horschPyPSAEurOpenOptimisation2018/horschPyPSAEurOpenOptimisation2018.pdf-3-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/horschPyPSAEurOpenOptimisation2018/horschPyPSAEurOpenOptimisation2018.pdf-3-1.png)

Figure 1: Transmission network model (includes optional lines that are planned
and under construction).


countries and the removal of 23 disconnected stub sub-networks (of less than 4
buses) produces the transmission network in Figure 1 of all current transmission
lines plus several ones which are already under or close to construction (these
are marked in the dataset). In total the model contains 6001 HVAC lines with
a volume of 345 _._ 7 TW km (of which 17 TW km are still under construction), 46
HVDC lines with a volume of 6 _._ 2 TW km (of which 2 _._ 3 TW km are still under
construction). The buses are composed of 3657 substations and 1320 auxiliary
buses, like joints and power plants.
The countries are partitioned into Voronoi cells as catchment areas, each of
which is assumed to be connected to the substation by lower voltage network
layers. These Voronoi cells are used to link power plant capacities and determine
feed-in by potential renewable energy generation, as well as the share of demand
drawn at the substation.


_2.2. Conventional power plants_


Official sources often only report on country-wide capacity totals keyed
by fuel-type and year like the Eurostat nrg ~~1~~ 13a database [24], the ENTSOE net generation capacity [25] or the ENTSO-E Scenario Outlook and Adequacy Forecast (SO&AF) [26, 27], while only seven countries [2] have official
power plant lists collected and standardised by the Open Power System Data
(OPSD) project [28].
This gap has been gradually closing since ENTSO-E started maintaining a
power plant list (ENTSO-E PPL) on their Transparency Platform [29]. Unfortunately, it is still far from complete, for instance even after excluding solar
and wind generators, the total capacity represented in Germany amounts only
to about 57 GW, while the SO&AF reports 111 GW, 107 GW of which are also
covered as operational in the German BNetzA Kraftwerksliste [30].
The powerplantmatching (PPM) tool and database [31] we present in this
section achieves good coverage by (1) standardising the records of several freely


2 BE, DE, FR, HU, IE, IT, LT as listed by the Open Power System Data project at `[http:](http://open-power-system-data.org/data-sources#23_National_sources)`
```
//open-power-system-data.org/data-sources#23_National_sources

```

4


Column Argument


Name Power plant name
Fueltype _{_ Bioenergy, Geothermal, Hard Coal, Hydro, Lignite,
Nuclear, Natural Gas, Oil, Solar, Wind, Other _}_
Technology _{_ CCGT, OCGT, Steam Turbine, Combustion Engine,
Run-Of-River, Pumped Storage, Reservoir _}_
Set _{_ PP, CHP _}_
Capacity Generation capacity in MW
lat/lon Latitude and Longitude
Country _{_ EU-27 + CH + NO (+ UK) minus Cyprus and Malta _}_
YearCommissioned Commissioning year
File Source file of the data record
projectID Identifier of the power plant in the original source file


Table 2: Standardised data structure for the power plant databases.


available databases, (2) linking them using a deduplication and record linkage
application and (3) reducing the connected claims about fuel type, technology,
capacity and location to the most likely ones.
PPM incorporates several power plant databases that are either published
under free licenses allowing redistribution and reuse or are at least freely accessible. In the order of approximate reliability, there are OPSD [28], ENTSOE PPL [29], DOE Energy Storage Exchange [32], Global Energy Observatory
(GEO) [7], Carbon Monitoring for Action (CARMA) from 2009 [33, 34], DOE
Energy Storage Exchange [32] (ESE) and the Global Power Plant Database by
the World Resource Institute (GPD) [35]. All of them are brought into the
standardised tabular structure outlined in Table 2 by explicit maps between the
various naming schemes and additional heuristics identifying common fuel-type
or technology keywords like _lignite_ or _CHP_ in the _Name_ column. Furthermore,
the _Name_ column is cleaned by removing frequently occurring tokens, _power_
_plant_ or block numbers, for instance.
Since OPSD, ENTSO-E PPL and ESE report individual power plant units
for at least some power plants, in a first step we use the deduplication mode of
the java application Duke to determine units of the same power plant. Duke [36]
is a free software extension of the search engine library Lucene that determines
probabilities whether pairs of records (of the same or different tables) refer to the
same entity. It computes conditional probabilities _p_ _[i,j]_ _c_ := _P_ ( _M_ _[i,j]_ _|x_ _[i,j]_ _c_ [) for the]
event _M_ _i,j_ := “records i and j match” given the data _x_ _[i,j]_ _c_ in column _c_ of these
records from mostly character-based similarity metrics like the _Jaro distance_ or
the _Q-gram distance_ [37] skewed into a configurable interval and combines them
into an overall matching probability as



� _c_ _[p]_ _c_ _[i,j]_

~~�~~ _c_ _[p]_ _c_ _[i,j]_ + ~~[�]~~ _c_



�
_p_ _i,j_ := _P_ ( _M_ _[i,j]_ _| ∩_ _c_ _x_ _[i,j]_ _c_ [) =]



_c_

_._ (1)
_c_ [(1] _[ −]_ _[p]_ _c_ _[i,j]_ [)]



_c_ _[p]_ _c_ _[i,j]_ + ~~[�]~~



This formula, a simplified variant of Naive Bayesian Classification, can be derived from the Bayes Theorem under the assumptions of pairwise conditional
independence of the _x_ _[i,j]_ _c_ and unbiased prior probabilities whether two records
match or do not, i.e. _P_ ( _M_ _[i,j]_ ) = _P_ (( _M_ _[i,j]_ ) _[C]_ ) = 0 _._ 5. The former assumption


5


Column Deduplication Record linkage
Comparator low h igh Comparator low h igh


Name JaroToken 0 _._ 09 0 _._ 99 JaroToken 0 _._ 09 0 _._ 99

Fueltype QGram 0 _._ 09 0 _._ 65 QGram 0 _._ 09 0 _._ 7
Country QGram 0 _._ 01 0 _._ 51 QGram 0 _._ 0 0 _._ 53
Capacity Numeric 0 _._ 49 0 _._ 51 Numeric 0 _._ 1 0 _._ 75
Geoposition Geo 0 _._ 05 0 _._ 55 Geo 0 _._ 1 0 _._ 8


Table 3: Duke comparison metrics and intervals for aggregation of power plant units (deduplication) and linking different power plant tables (record linkage). JaroToken breaks the full
string into several tokens, evaluates the Jaro Winkler distance metric for each and returns the
compound Jaccard index [37]. These parameters have been chosen by hand and plausibility,
while instead they should be tuned for a representative subset to an ideal match by Duke’s
Genetic algorithm.


underlies all naive bayesian classifiers and ignores for instance the correlation
between technology and capacity (run-of-the-river turbines are typical small
(20 MW), whereas nuclear power plants are typically large, with a median capacity of 2 GW). The latter assumption means literally that any two power plant
entries from two different datasets have a prior probability of 50% to refer to the
same power plant, while the real probability is less than _NN_ [2] [=] _N_ [1] [, seen from the]

comparison of two identical datasets of length _N_ . To include such a more realistic prior assumption into the matching process would require changing several
internals of the Duke library and is out of the scope of this work. Nevertheless,
the model has already been successfully applied in practice [38, 39].
For the aggregation of power plant units, Duke is configured to use the
metrics and intervals described in Table 3 to return the probabilities _p_ _i,j_ _>_ 0 _._ 985
between likely pairs _i_ and _j_ . In the power plant matching tool of the authors,
these are used as edges in a directed graph of records and the cliques of this
graph [3] are aggregated as power plants. Note the low end of the interval for
measuring the similarity of the fuel-type chosen to prevent merging units with
different fuel-types into the same power plant.
For linking the six databases, PPM runs Duke in Record linkage mode on
every pair of databases and determines the most likely links above the threshold
of 0 _._ 985. These links are joined to chains by collecting the records across all
databases that match to the same plant in any database. The chains are reduced
by keeping only the longest chains, until they are consistent, i.e. each power
plant appears only in at most one chain. This could likely be improved by
joining chains recursively, while keeping track of the chain probability based on
a variant of Eq. (1) at the expense of not being able to rely on the fast pandas
routines any more.
For the remaining chains the power plant information is aggregated by taking
the most frequent _Fueltype_, a comma separated list of the _Technology_ (-ies), the
mean _lat/lon_ and the median _Capacity_ . The latter ensures that the shutdown
or addition of a block of a power plant which is not yet reflected in a minority
of databases does not distort the final capacity.


3 A clique in a directed graph is a subset of the nodes such that every two distinct nodes
are adjacent.


6


The compound dataset, at the time of writing, contains 3501 power plants
with a total capacity of 663 GW. Less than a third of these are represented in
3 or more sources, but still account for about two third of the capacity. 2584
small power plants with an average capacity of about 83 MW appear in only two
databases. There are a further 2788 power plants with 18 _._ 2 GW capacity in the
OPSD dataset unmatched by the other free datasets and exclusively compiled
from official sources. After including these power plants the mean absolute error
from the SO&AF country-wise capacity is at 12% of the average capacity and
below a 27% deviation in each single country except for Bulgaria and Lithuania.
Refer to the companion paper for a more detailed comparison of the free dataset
with the proprietary World Electric Power Plants dataset [40].


_2.3. Hydro-electric generation_


Existing hydroelectric capacities ensue from the same matching process as
the conventional power plants, particularly based on the sources ESE and ENTSOE. The capacities are categorised into run-of-river, reservoir and pumped storage. Reservoir and pumped storage have energy storage capacities that are
estimated by distributing the country-aggregated energy storage capacities reported by [41, 42] in proportion to power capacity. Run-of-river as well as reservoir hydro capacities receive an hourly-resolved in-flow of energy. Extensions to
the current hydro capacities are not considered.
Renewable generation time series like hydro-electric in-flow, wind are derived
from the re-analysis weather dataset ERA5 by the European Centre for MediumRange Weather Forecasts (ECMWF) [43]. It provides wind speeds, irradiation,
surface-roughness, temperature and run-off in hourly resolution since 2008 on a
on a 0 _._ 28 _[◦]_ _×_ 0 _._ 28 _[◦]_ spatial raster ( _x ∈X_ ).
The simplified in-flow time series is generated as in [41, 44] by aggregating
the total potential energy at height _h_ _x_ relative to ocean level of the run-off data
_R_ _x_ in each country _c_ by


_G_ _[H]_ _c_ [(] _[t]_ [) =] _[ N]_ � _h_ _x_ _R_ _x_ ( _t_ ) (2)

_x∈X_ ( _c_ )


where _N_ is chosen so that � _t_ _[G]_ _c_ _[H]_ [(] _[t]_ [) d] _[t]_ [ matches the EIA annual hydroelectric-]

ity generation [45]. The in-flow is distributed to all run-of-river and reservoir
capacities in proportion to their power capacity.


_2.4. Wind generation_


Adapting the methodology in [46] to the newer dataset, the wind speeds at
100 m above ground _u_ [100 m] _x_ ( _t_ ) are extrapolated to turbine hub-height _h_ using
the surface roughness _z_ _x_ [0] [with the logarithmic law]


ln � _h/z_ _x_ [0] �
_u_ _[h]_ _x_ [(] _[t]_ [) =] _[ u]_ [100 m] _x_ ( _t_ ) ln (100 m _/z_ _x_ [0] ) _[.]_ (3)


The capacity factor of each raster cell _x_ for a wind turbine with powercurve
_P_ _w_ ( _u_ ) and generator capacity _P_ _w_ _[max]_ is determined as



_c_ _x,w_ =



� _P_ _w_ ( _u_ _[h]_ _x_ [(] _[t]_ [))] � _t_ (4)

_P_ _w_ _[max]_


7


and together with the usable area _A_ _x,w_ the maximally installable wind generation capacity _G_ _[max]_ _x,w_ [= 0] _[.]_ [3] _[ ·]_ [ 10 MW] _[/]_ [km] [2] _[ ·][ A]_ _[x,w]_ [is calculated, where 10 MW] _[/]_ [km] [2]

is the technical potential density [47] and 0 _._ 3 arises out of considering competing
land use and issues of public acceptance.
The usable area is restricted by the following constraints: Onshore wind can
only be built in land use types of the CORINE Land Cover database [48] associated to _Agricultural areas_ and _Forest and semi natural areas_ and furthermore
a minimum distance of 1000 m from _Urban fabric_ and _Industrial, commercial_
_and transport units_ must be respected. Offshore wind can only be constructed
in water depths up to 50 m. Additionally, all nature reserves and restricted
areas listed in the Natura2000 database [49] are excluded. The wind generation
potential in Germany is shown in Figure 2.
Each Voronoi cell _V_ of a substation covers multiple cells of the re-analysis
weather grid, as described by the indicatormatrix _I_ _V,x_ = area( _V ∩_ _x_ ) _/_ area _x_,
and we distribute the wind turbine capacity according to a normed capacity
layout

_G_ _[p.u.]_ _V,x,w_ [=] _[ I]_ _[V,x]_ ~~�~~ _[c]_ _[x,w]_ _x_ [(] _[G][·]_ [)] _x_ _[max]_ _,w_ (5)


which prefers cells _x_ with high capacity factor _c_ _x,w_ and high maximally installable capacity _G_ _[max]_ _x,w_ [. The wind generation availability time-series at a substation]
with Voronoi cell _V_ is, thus,



_,_ (6)

_P_ _w_ _[max]_



_g_ ¯ _V,w_ ( _t_ ) = _G_ _V,w_



�



_x_ _[G]_ _[p.u.]_ _V,x,w_ _[P]_ _[w]_ [(] _[u]_ _x_ _[h]_ [(] _[t]_ [))]



for an installed capacity _G_ _V,w_ . This capacity is expandable until reaching _G_ _[max]_ _x,w_
in any grid cell up to


_G_ _[max]_ _V,w_ [=] min _I_ _V,x_ _G_ _[max]_ _x,w_ _._ (7)
_{x|I_ _V,x_ _>_ 0 _}_ _G_ ~~_[p.u.]_~~ _V,x,w_


The power curve of the turbine Vestas V112 with a turbine capacity of 3 MW
and a hub height 80 m is used to generate the onshore wind time-series and the
NREL Reference Turbine with 5 MW at 90 m is used for the offshore wind timeseries. The accuracy of the wind generation time-series are improved to account
for effects of spatial wind speed variations within a grid cell by smoothing the
power curves with a Gaussian kernel as



_∞_
_P_ _w_ ( _u_ ) = _η_
� 0



1 _−_ [(] _[u][−][u][′]_ [+][∆] _[u]_ [)][2]

e 2 _σ_ 0 ~~[2]~~
2 _πσ_ 0 [2]



1
_P_ 0 ( _u_ _[′′]_ )
0 ~~�~~ 2



2 _σ_ 0 ~~[2]~~ d _u_ _[′]_ _,_ (8)



where _η_ = 0 _._ 95, ∆ _u_ = 1 _._ 27 m _/_ s and _σ_ 0 = 2 _._ 29 m _/_ s are the optimal parameters
minimising the error between the re-analysis-based time-series and a year of
Danish wind feed-in [46]. A study comparing the wind generation time-series
based on the re-analysis MERRA-2 dataset for a 20 year period to the percountry wind feed-in and several wind park generation measurements found
non-negligible discrepancies of the optimal bias correction parameters between
different countries [50]. They will be incorporated in a future version of the
presented model.


8


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/horschPyPSAEurOpenOptimisation2018/horschPyPSAEurOpenOptimisation2018.pdf-8-7.png)



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/horschPyPSAEurOpenOptimisation2018/horschPyPSAEurOpenOptimisation2018.pdf-8-8.png)







![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/horschPyPSAEurOpenOptimisation2018/horschPyPSAEurOpenOptimisation2018.pdf-8-4.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/horschPyPSAEurOpenOptimisation2018/horschPyPSAEurOpenOptimisation2018.pdf-8-5.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/horschPyPSAEurOpenOptimisation2018/horschPyPSAEurOpenOptimisation2018.pdf-8-6.png)

Figure 2: Wind (l.) and solar (r.) potential power generation after landuse restrictions for
weather grid cells in Germany. The generation of all grid cells in a Voronoi cell (also shown
in black) is fed into the central substation.


_2.5. Photovoltaic generation_


The solar availability time-series and maximally installable capacity per substation are based on the direct and diffuse surface solar irradiance in Surface
Solar Radiation Data Set - Heliosat (SARAH-2), but except for that is similar
to the generation time-series of wind turbines.
The photovoltaic generation _P_ _x,s_ ( _t_ ) for a panel of nominal capacity _P_ _s_ _[max]_
of a point in time _t_ and space resp. grid cell _x_ is calculated from the surface
solar irradiance. The total panel irradiation is derived from the solar azimuth
and altitude [51] using geometric relations of the trajectory of the sun and the
tilted panel surface [52]. All solar panels are facing South at an angle of 35 deg.
The electric model by Huld et al. [53] determines the active power output from
the total irradiation and the ambient temperature. Implementation details are
found in the `pv` sub-package of the atlite package [54].
For each raster cell _x ∈X_ the capacity factor _c_ _x,s_ and the maximally installable capacity _G_ _[max]_ _x,s_ = 0 _._ 01 _·_ 145 MW _/_ km [2] _·_ _A_ _x,s_ is determined as for wind, with
the difference that the high technical potential of 145 MW _/_ km [2] corresponds to
an unrealistic full surface of solar cells, which is offset by allowing only up to 1%.
The permitted CORINE land use types are _Artificial surfaces_, most _Agricultural_
_areas_ except for those with forests and then including only few sub-categories of
_Forest and semi natural areas_ : _Scrub and/or herbaceous vegetation associations_,
_Bare rocks_ and _Sparsely vegetated areas_ . Figure 2 shows the solar generation
potentials.
Equations (5)-(7) are applied analogously to generate the solar availability
time-series ¯ _g_ _V,s_ ( _t_ ) and to find the solar expansion potential _G_ _[max]_ _V,s_ [. The reference]
solar panel is the crystalline sillicon panel fitted in [53].


_2.6. Demand_


The hourly electricity demand profiles for each country from 2011 to 2016
are taken from the European Network of Transmission System Operators for
Electricity (ENTSO-E) website [55]. The load time-series is distributed to the
substations in each country by 60% according to the gross domestic product
(GDP) as a proxy for industrial demand and by 40% as residential demand
according to population in a Voronoi cell. The 60-40% split is based on a linear
regression analysis of the per-country data and agrees with values used in [14].
The two statistics are mapped from the Eurostat Regional Economic Accounts


9


Circuit length DE EU
in 1000 km 220 kV 300 kV 380 kV 220 kV 300 kV 380 kV


ENTSO-E 13 _._ 70 0 _._ 0 20 _._ 92 117 _._ 25 9 _._ 96 146 _._ 82

PyPSA-Eur 10 _._ 49 0 _._ 0 24 _._ 97 116 _._ 69 9 _._ 23 154 _._ 31


Table 4: AC lines circuit lengths of the whole of Europe and Germany as an example


database (nama ~~1~~ 0-reg) for NUTS3 regions to the Voronoi cells in proportion
to their geographic overlap.


**3. Validation**


_3.1. Network total line lengths_


In this subsection, total line circuit lengths at different voltage levels in the
model are compared with official statistics from ENTSO-E. The lengths of AC
circuits [56] per voltage level and country are compared to aggregations of line
lengths times circuits from PyPSA-Eur, so that cross-border lines are equally
attributed to both adjacent countries. In Table 4 the total line lengths for
the whole of Europe and Germany are presented as examples. Considering the
data for all countries, the lines in the PyPSA-Eur dataset deviate from the
ENTSO-E lengths of circuits by a mean absolute error of 15% for 220 kV, 7%
for 300 kV and 9% for 380 kV lines. These deviations are accounted for by the
fact that the ENTSO-E map [19] from which the PyPSA-Eur network is derived
is only an artistic representation and does not follow the exact contours of each
transmission line. Some differences may also be due to incorrect classification
of 220 kV lines as 380 kV lines, or due to the fact that the ENTSO-E map on
which PyPSA-Eur is based is more up-to-date with regard to recent upgrades
to the transmission network.


_3.2. Network topology_


While the total circuit lengths might agree, it does not necessarily mean
that the lines are in the right places with the right topology. The _ENTSO-E_
_Interconnected network map_ [19] is the source of the network topology in PyPSAEur, so it naturally agrees well in visual examination. A comparison with the
network topology published with the TYNDP [5] is hindered by shortened substation names and missing geo-locations in that dataset. Instead, in this section
the network topology of PyPSA-Eur is compared to the open network datasets
available for Germany, which are derived using a different methodology. New,
experimental algorithms are presented to compare network topologies, since few
appropriate algorithms exist in the literature. This is a difficult problem because
neither the locations nor the number of the buses and lines in the different models necessarily agree. Our methodology works by first establishing a common
set of aggregated buses for the different networks, then comparing the networks
once all lines and other elements have been reattached to the aggregated buses.
More precisely, we present a new technique of applying _k_ -means clustering
to measure the similarity of several geo-located network models, specifically the


10


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/horschPyPSAEurOpenOptimisation2018/horschPyPSAEurOpenOptimisation2018.pdf-10-5.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/horschPyPSAEurOpenOptimisation2018/horschPyPSAEurOpenOptimisation2018.pdf-10-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/horschPyPSAEurOpenOptimisation2018/horschPyPSAEurOpenOptimisation2018.pdf-10-1.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/horschPyPSAEurOpenOptimisation2018/horschPyPSAEurOpenOptimisation2018.pdf-10-2.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/horschPyPSAEurOpenOptimisation2018/horschPyPSAEurOpenOptimisation2018.pdf-10-3.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/horschPyPSAEurOpenOptimisation2018/horschPyPSAEurOpenOptimisation2018.pdf-10-4.png)

Figure 3: 80 clusters jointly identified by colour in the network topologies of the models
PyPSA-Eur, osmTGmod and ELMOD-DE.











![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/horschPyPSAEurOpenOptimisation2018/horschPyPSAEurOpenOptimisation2018.pdf-10-9.png)



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/horschPyPSAEurOpenOptimisation2018/horschPyPSAEurOpenOptimisation2018.pdf-10-10.png)


|Col1|Col2|
|---|---|
|0<br>5<br>|10<br>|



Figure 4: Capacity connecting the same clusters in GW


German 220 kV and 380 kV voltage layers represented in PyPSA-Eur, osmTGmod and ELMOD-DE. The buses of all networks are jointly clustered together
into _k_ clusters by minimizing the distances in each cluster _π_ _j_



� _w_ ( _a_ ) _||a −_ _m_ _j_ _||_ [2] (9)

_a∈π_ _j_



_D_ ( _π_ _j_ ) =



_k_
�

_j_ =1



from its buses _a_ to the center _m_ _j_ = [�]



_b∈π_ _j_ _[w]_ [(] _[b]_ [)] _[ b/]_ [ �]



from its buses _a_ to the center _m_ _j_ = [�] _b∈π_ _j_ _[w]_ [(] _[b]_ [)] _[ b/]_ [ �] _b∈π_ _j_ _[w]_ [(] _[b]_ [) with chosen bus-]

weights _w_ ( _b_ ). Since Eq. (9) expands to a polynomial in the scalar products
_⟨_ _a, b ⟩_ between buses, kernel k-means allows the use of general scalar products
by evaluating them on every pair of buses [57]. In the kernel of the scalar
product we propose
_K_ _a,b_ = _e_ _[−||][a][−][b][||]_ 2 [2] _[/N]_ + _νB_ _a,b_ _[†]_ (10)


spatial cohesion comes from the first term, a radial basis function based on the
Euclidean distance _|| · ||_ 2 over the number of buses _N_, which favours short geometric distances and connectedness over spatial convexity, while the pseudo
inverse of the admittance matrix _B_ in each network induces an electrical reactance distance, shown to lead to electrically cohesive clusters [58]. With the
weights _w_ ( _a ∈_ PyPSA-Eur _/_ ELMOD-DE) = 5 and _w_ ( _a ∈_ osmTGmod) = 1
balancing the five times as many buses in osmTGmod and the relative weight
_ν_ = 200, the clustering algorithm is able to distribute 80 clusters across the
three networks, by starting from the labels found by regular _k_ -means and by
picking the best result from 20 runs. The networks and the associated buses are
shown in Figure 3.
As the clusters are the same in each network, the aggregate capacity between
every two clusters is now comparable for different networks. Unfortunately,



11


|25|Col2|Col3|
|---|---|---|
|75<br>00<br>|75<br>00<br>|75<br>00<br>|
|25<br>50<br>|25<br>50<br>|25<br>50<br>|
|25<br>50<br>|25<br>50<br>|1.0<br>|


|25|Col2|Col3|
|---|---|---|
|75<br>00<br>|75<br>00<br>|75<br>00<br>|
|25<br>50<br>|25<br>50<br>|25<br>50<br>|
|25<br>50<br>|25<br>50<br>|1.0<br>|


|25|Col2|Col3|
|---|---|---|
|75<br>00<br>|75<br>00<br>|75<br>00<br>|
|25<br>50<br>|25<br>50<br>|25<br>50<br>|
|25<br>50<br>|25<br>50<br>|.0|



Figure 5: Line volume at buses in the same cluster in TWkm


PyPSA-Eur osmTGmod ELMOD-DE


PyPSA-Eur 1.000 0.856 0.741
osmTGmod 1.000 0.868

ELMOD-DE 1.000


Table 5: Pearson correlation coefficients between line volume at different buses


Figure 4 reveals bad agreement for the capacity between pairs of clusters, due to
its high sensitivity to errors arising from clustering topologically distinct buses;
i.e. buses lying on distinct lines are inadvertently joined together. Increasing the
weight of the electrical distance _ν_ dampens the appearance of these associations,
but worsens convergence and increasingly finds solutions in which clusters in the
electrically well connected areas as the Ruhrpott detach from one of the three
networks. With a lower number of clusters between 20 to 40, _ν_ can be increased
by an order of magnitude and the topological errors are less important, then
the aggregate capacities between large network zones can be compared and
deviations identified.
Another approach is simply to aggregate line volumes within and attached
to each cluster of buses. These are compared in Figure 5 and turn out to be
quite robust against topologically problematic associations and show a high
correlation across networks in Table 5. ELMOD-DE has proportionally less line
volume than osmTGmod and PyPSA-Eur, but with approximately the same
spatial distribution. osmTGmod and PyPSA-Eur agree well.


_3.3. Potentials for expansion of renewables_


Geographic potentials for the expansion of wind and solar power depend
strongly on technical, environmental, social and political constraints. Different
organisations offer different assessments of acceptable potentials, which involve
a complex balance between land availability, landscape impact and species protection. In this section we compare aggregated total potentials for Germany in
the PyPSA-Eur model derived using the methodologies described in Sections
2.4 and 2.5 with other studies.

For onshore wind, there is an installable potential of 441 GW in Germany
in the model. Assessments in the literature range from 198 GW [59] (based
on a ‘realistic’ restriction to 2% of total land area, although 8% is available
when excluding forests and protected areas) up to 1190 GW [60] (using 13.8%
of the total land area, ignoring species protection and whether locations are
economically exploitable).


12


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/horschPyPSAEurOpenOptimisation2018/horschPyPSAEurOpenOptimisation2018.pdf-12-1.png)





Figure 6: Load shedding
and line loading at peak demand.



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/horschPyPSAEurOpenOptimisation2018/horschPyPSAEurOpenOptimisation2018.pdf-12-0.png)

For offshore wind, 87 GW of fixed-foundation capacity is installable in PyPSAEur in Germany. Estimates in the literature range from 38 GW [61] to 85 GW [62].
350 GW of solar photovoltaics is installable in Germany in PyPSA-Eur. The
potential depends strongly on what land areas are permitted, but typical values range from 360 GW [61] to 400 GW [62] (including roofs, facades and railway/motorway sidings, but excluding free space).


_3.4. Model validation: Linear optimal power flow_


As a validation of the model at large, it is formulated within the Python
for Power System Analysis (PyPSA) framework [17, 18] and the linear optimal
power flow of the European peak-load hour is considered to check the feasibility
of the combined network, generation and demand data for supplying the most
extreme demand, so that line-loading is below 70% to approximate the _N −_ 1
stability constraint.
Solar- and wind feed-in are not allowed to reduce the load, while hydroelectric installations may be discharged at their full power capacity. European
overall peak-load of 0 _._ 51 TW in the dataset happens at 17:00 on 17-01-2013
and leads to 20 _._ 4 GW of load shedding in the vicinity of large agglomerations,
primarily in Paris (6 _._ 2 GW) and London (3 _._ 8 GW) as shown in Fig. 6. Since
there is sufficient generation capacity to cover the peak load, this load-shedding
is due to grid bottlenecks which appear in the model (but not in reality, since
grid bottlenecks do not cause load-shedding in today’s European network). The
amount of shedding decreases considerably by lifting capacity constraints on
short lines, for example if lines shorter than 25 km are not limited in power
capacity, only about 6 GW, 1%, of load has to be shed. Similarly easing local
restrictions by clustering the network using a k-means algorithm as detailed
in [63] to 1500 buses reduces shedding to 1% of peak load, while clustering to 362
buses allows most of the demand to be supplied, except for 650 MW on Mallorca,
where the power plant dataset is missing 1 _._ 4 GW of coal and gas capacities, and
at the Northern tip in Norway. This is indicative of local assignment errors of
load and supply, when the Voronoi cells used to assign load and generators to
transmission substations do not represent the true distribution grid topology
at each transmission substation, and/or an underrepresentation of inner-city
underground cabling, which is not always shown on the map. Clustering the
network, so that each bus represents a larger area, smooths out local assignment


13


errors. Since there are several heuristic remedies from expanding the loaded
lines over rearranging the load to using clustered topologies as done manually
by [6, 8], we decided not to perform any corrections, but publish the dataset as
is.


**4. Limitations**


While the benefit of an openly available, functional and partially validated
model of the European transmission system is high, many approximations have
been made due to missing data. In this section we summarise the limitations of
the dataset, both as a warning to the user and as an encouragement to assist in
improving the approximations.
The grid data is based on a map of the ENTSO-E area [19] that is known
to contain small distortions to improve readability. Since the exact impedances
of the lines are unknown, approximations based on line lengths and standard
line parameters were made that ignore specific conductoring choices for particular lines. There is no openly available data on busbar configurations, switch
locations, transformers or reactive power compensation assets.
Using Voronoi cells to aggregate load and generator data to transmission
network substations ignores the topology of the underlying distribution network,
meaning that assets may be connected to the wrong substation. Assumptions
have been made about the distribution of load in each country proportional to
population and GDP that may not reflect local circumstances. Openly available
data on load time series may not correspond to the true vertical load [64] and is
not spatially disaggregated; assuming, as we have done, that the load time series
shape is the same at each node within each country ignores local differences.
Information on existing wind, solar and small hydro, geothermal, marine and
biomass power plants are excluded from the dataset because of a lack of data
availability in many countries. Approximate distributions of wind and solar
plants in each country can be generated that are proportional to the capacity
factor at each location.

The database of hydro-electric power plants does not include plant-specific
energy storage information, so that blanket values based on country storage
totals have been used. Inflow time series are based on country-wide approximations, ignoring local topography and basin drainage; in principle a full hydrological model should be used. Border connections and power flows to Russia,
Belarus, Ukraine, Turkey and Morocco have not been taken into account; islands which are not connected to the main European system, such as Malta,
Crete and Cyprus, are also excluded from the model.


**5. Conclusions**


In this paper a dataset PyPSA-Eur has been presented of the full European
transmission system, including a high resolution grid model, load data, a new
geo-referenced database of conventional power plants, potentials for the expansion of wind and solar, and time series for the load and variable renewable power
availability. The model is only based on publicly available and open datasets,
and all code and data has been made available [15, 16], making it the first open
model of the full European system at such high spatial resolution.


14


To validate the model, total circuit lengths were compared with official statistics for Europe, renewable expansion potentials were checked against literature
values, an optimal power flow study was performed, and a new technique was developed to compare the network topology with other network models. Together
these validation steps demonstrate that the model is a plausible approximation of the European power system. Further validation is desirable to increase
confidence in the model.
Since PyPSA-Eur is open, it can be further improved by any research group
as and when better data or new methodologies become available. It is also
hoped that the existence of unofficial open datasets such as PyPSA-Eur will
encourage data holders to release their own official datasets, in the interests of
improving modelling by third parties.
Given the pressing need to understand how to adapt the electricity system
to rising shares of variable renewable generators, new market structures, the
electrification of other energy sectors such as transport and heating, and rising public concerns about the landscape impacts of overhead lines, there is a
clear imperative for detailed, rigorous and reproducible modelling of the transmission system. The dataset PyPSA-Eur has been designed primarily for the
optimisation of future investment in generation and transmission, but can also
be adapted to studies of the operation of the current power system. We hope
that it will contribute towards a transparent discussion of the future needs of
the European energy system.


**Acknowledgements**


All authors acknowledge funding from the German Federal Ministry of Education and Research under grant no. 03SF0472C. T.B. and J.H. acknowledge
funding from the Helmholtz Association under grant no. VH-NG-1352.


**References**


[[1] European Commission, A Framework Strategy for a Resilient Energy](http://eur-lex.europa.eu/legal-content/EN/TXT/?uri=COM:2015:80:FIN)
[Union with a Forward-Looking Climate Change Policy, Tech. rep. (2015).](http://eur-lex.europa.eu/legal-content/EN/TXT/?uri=COM:2015:80:FIN)
URL `[http://eur-lex.europa.eu/legal-content/EN/TXT/?uri=COM:](http://eur-lex.europa.eu/legal-content/EN/TXT/?uri=COM:2015:80:FIN)`

```
  2015:80:FIN

```

[2] C. M. St. Martin, J. K. Lundquist, M. A. Handschy, Variability of interconnected wind plants: correlation length and its dependence on variability time scale, Environmental Research Letters 10 (4) (2015) 044004.
`[doi:10.1088/1748-9326/10/4/044004](http://dx.doi.org/10.1088/1748-9326/10/4/044004)` .


[[3] BNetzA, BKartA, Monitoringbericht 2016, Tech. rep. (2016).](http://tinyurl.com/bnetza-monitoringbericht-2016)
URL `[http://tinyurl.com/bnetza-monitoringbericht-2016](http://tinyurl.com/bnetza-monitoringbericht-2016)`


[4] A. Battaglini, N. Komendantova, P. Brtnik, A. Patt, Perception of barriers
for expansion of electricity grids in the European Union, Energy Policy 47
(2012) 254–259. `[doi:10.1016/j.enpol.2012.04.065](http://dx.doi.org/10.1016/j.enpol.2012.04.065)` .


[5] ENTSO-E Pan-European Power Transmission Grid Datasets, `[https://](https://www.entsoe.eu/stum/)`
`[www.entsoe.eu/stum/](https://www.entsoe.eu/stum/)` (2016).


15


[6] Q. Zhou, J. Bialek, Approximate model of european interconnected system
as a benchmark system to study effects of cross-border trades, Power Systems, IEEE Transactions on 20 (2) (2005) 782–788. `[doi:10.1109/TPWRS.](http://dx.doi.org/10.1109/TPWRS.2005.846178)`

`[2005.846178](http://dx.doi.org/10.1109/TPWRS.2005.846178)` .


[7] R. Gupta, H. Shankar, et. al., Global Energy Observatory, `[http://](http://globalenergyobservatory.org/)`
`[globalenergyobservatory.org/](http://globalenergyobservatory.org/)` (2017).


[8] N. Hutcheon, J. Bialek, Updated and validated power flow model of the
main continental european transmission network, in: IEEE PowerTech 2013
Grenoble, 2013. `[doi:10.1109/PTC.2013.6652178](http://dx.doi.org/10.1109/PTC.2013.6652178)` .


[9] T. V. Jensen, P. Pinson, RE-Europe, a large-scale dataset for modeling
a highly renewable European electricity system, Scientific Data 4 (2017)
170175. `[doi:10.1038/sdata.2017.175](http://dx.doi.org/10.1038/sdata.2017.175)` .


[10] T. V. Jensen, H. de Sevin, M. Greiner, P. Pinson, The RE-Europe data set
(2015). `[doi:10.5281/zenodo.620228](http://dx.doi.org/10.5281/zenodo.620228)` .


[[11] OpenStreet Map (2017).](http://www.openstreetmap.org/)
URL `[http://www.openstreetmap.org/](http://www.openstreetmap.org/)`


[12] C. Matke, W. Medjroubi, D. Kleinhans, SciGRID - An Open Source
Reference Model for the European Transmission Network (v0.2), `[http:](http://www.scigrid.de)`
`[//www.scigrid.de](http://www.scigrid.de)` (2016).


[[13] M. Scharf, A. Nebel, osmTGmod: Open source German transmission grid](https://github.com/wupperinst/osmTGmod)
[model based on OpenStreetMap v0.1.3 (2017).](https://github.com/wupperinst/osmTGmod)
URL `[https://github.com/wupperinst/osmTGmod](https://github.com/wupperinst/osmTGmod)`


[[14] J. Egerer, Open Source Electricity Model for Germany (ELMOD-DE), DIW](https://www.diw.de/sixcms/detail.php?id=diw_01.c.528929.de)
Data Documentation.

URL `[https://www.diw.de/sixcms/detail.php?id=diw_01.c.528929.](https://www.diw.de/sixcms/detail.php?id=diw_01.c.528929.de)`

```
  de

```

[15] J. H¨orsch, F. Hofmann, D. Schlachtberger, T. Brown, PyPSA-Eur: An
Open Optimisation Model of the European Transmission System (dataset
version 0.2) (2018). `[doi:10.5281/zenodo.1265323](http://dx.doi.org/10.5281/zenodo.1265323)` .


[[16] J. H¨orsch, PyPSA-Eur: An Open Optimization Model of the European](https://github.com/FRESNA/pypsa-eur)
[Transmission System (dataset) (2017).](https://github.com/FRESNA/pypsa-eur)
URL `[https://github.com/FRESNA/pypsa-eur](https://github.com/FRESNA/pypsa-eur)`


[17] T. Brown, J. H¨orsch, D. Schlachtberger, PyPSA: Python for Power System
Analysis, Journal of Open Research Software 6 (4). `[arXiv:1707.09913](http://arxiv.org/abs/1707.09913)`,
`[doi:10.5334/jors.188](http://dx.doi.org/10.5334/jors.188)` .


[[18] T. Brown, J. H¨orsch, D. Schlachtberger, PyPSA: Python for Power System](http://pypsa.org/)
[Analysis version 0.13.1 (2018).](http://pypsa.org/) `[doi:10.5281/zenodo.1208706](http://dx.doi.org/10.5281/zenodo.1208706)` .
URL `[http://pypsa.org/](http://pypsa.org/)`


[19] ENTSO-E Interactive Transmission System Map, `[https://www.entsoe.](https://www.entsoe.eu/map/)`
`[eu/map/](https://www.entsoe.eu/map/)` (2017).


16


[20] B. Wiegmans, GridKit: GridKit 1.0 ’for Scientists’ (2016). `[doi:10.5281/](http://dx.doi.org/10.5281/zenodo.47263)`

`[zenodo.47263](http://dx.doi.org/10.5281/zenodo.47263)` .


[21] B. Wiegmans, GridKit extract of ENTSO-E interactive map (2016). `[doi:](http://dx.doi.org/10.5281/zenodo.55853)`
`[10.5281/zenodo.55853](http://dx.doi.org/10.5281/zenodo.55853)` .


[22] Wikipedia, List of HVDC projects — Wikipedia, the free encyclopedia, `[https://en.wikipedia.org/w/index.php?title=List_of_HVDC_](https://en.wikipedia.org/w/index.php?title=List_of_HVDC_projects&oldid=782297059)`
`[projects&oldid=782297059](https://en.wikipedia.org/w/index.php?title=List_of_HVDC_projects&oldid=782297059)` (2016).


[23] D. Oeding, B. Oswald, Elektrische Kraftwerke und Netze, 7th Edition,
Springer, 2011. `[doi:10.1007/978-3-662-52703-0](http://dx.doi.org/10.1007/978-3-662-52703-0)` .


[24] Eurostat, Infrastructure - electricity - annual data, dataset nrg ~~1~~ 13a, `[http:](http://appsso.eurostat.ec.europa.eu/nui/show.do?dataset=nrg_113a)`
```
  //appsso.eurostat.ec.europa.eu/nui/show.do?dataset=nrg_113a
```

(2017).


[25] ENTSO-E, Net generating capacity, `[https://www.entsoe.eu/db-query/](https://www.entsoe.eu/db-query/miscellaneous/net-generating-capacity)`
`[miscellaneous/net-generating-capacity](https://www.entsoe.eu/db-query/miscellaneous/net-generating-capacity)` (2015).


[26] ENTSO-E, Scenario Outlook & Adequacy Forecast, `[https://www.entsoe.](https://www.entsoe.eu/outlooks/maf/)`
`[eu/outlooks/maf/](https://www.entsoe.eu/outlooks/maf/)` (2015).


[27] Open Power System Data, Data Package National generation capacity. Version 2017-07-07, `[https://data.open-power-system-data.org/](https://data.open-power-system-data.org/national_generation_capacity/2017-07-07/)`
`[national_generation_capacity/2017-07-07/](https://data.open-power-system-data.org/national_generation_capacity/2017-07-07/)` (2017).


[28] Open Power System Data, Data Package Conventional power plants.
Version 2017-07-07, `[https://data.open-power-system-data.org/](https://data.open-power-system-data.org/conventional_power_plants/2017-07-07/)`
`[conventional_power_plants/2017-07-07/](https://data.open-power-system-data.org/conventional_power_plants/2017-07-07/)` (2017).


[29] ENTSO-E Transparency Platform, Installed Capacity Per Production Unit, `[https://transparency.entsoe.eu/generation/r2/](https://transparency.entsoe.eu/generation/r2/installedCapacityPerProductionUnit/show)`
`[installedCapacityPerProductionUnit/show](https://transparency.entsoe.eu/generation/r2/installedCapacityPerProductionUnit/show)` (2017).


[30] Kraftwerksliste der Bundesnetzagentur, `[http://tinyurl.com/](http://tinyurl.com/bnetza-kraftwerksliste)`
`[bnetza-kraftwerksliste](http://tinyurl.com/bnetza-kraftwerksliste)` (2017).


[31] J. H¨orsch, F. Hofmann, F. Gotzens, Powerplantmatching: Set of tools to
combine multiple power plant databases version 0.3, `[https://github.com/](https://github.com/FRESNA/powerplantmatching)`
`[FRESNA/powerplantmatching](https://github.com/FRESNA/powerplantmatching)` (2017). `[doi:10.5281/zenodo.1403221](http://dx.doi.org/10.5281/zenodo.1403221)` .


[32] Sandia National Laboratories, DOE Global Energy Storage Database,

`[http://www.energystorageexchange.org/projects](http://www.energystorageexchange.org/projects)` (2017).


[33] D. Wheeler, K. Ummel, Calculating Carma: Global Estimation of CO2
Emissions from the Power Sector, SSRN Electronic Journal. `[doi:10.2139/](http://dx.doi.org/10.2139/ssrn.1138690)`

`[ssrn.1138690](http://dx.doi.org/10.2139/ssrn.1138690)` .


[34] K. Ummel, Carma revisited: An updated database of carbon dioxide
emissions from power plants worldwide, SSRN Electronic Journal (304).
`[doi:10.2139/ssrn.2226505](http://dx.doi.org/10.2139/ssrn.2226505)` .


[35] World Resource Institute, Climate Data and Tools Project, Global
Power Plant Database 1.1.0, `[http://datasets.wri.org/dataset/](http://datasets.wri.org/dataset/globalpowerplantdatabase)`
`[globalpowerplantdatabase](http://datasets.wri.org/dataset/globalpowerplantdatabase)` (2018).


17


[36] L. M. Garshol, Duke: a fast and flexible deduplication engine, version 1.2,

`[https://github.com/larsga/Duke/](https://github.com/larsga/Duke/)` (2014).


[37] A. K. Elmagarmid, P. G. Ipeirotis, V. S. Verykios, Duplicate record detection: A survey, IEEE Transactions on Knowledge and Data Engineering
19 (1) (2007) 1–16. `[doi:10.1109/TKDE.2007.250581](http://dx.doi.org/10.1109/TKDE.2007.250581)` .


[38] P. Domingos, M. Pazzani, On the optimality of the simple bayesian classifier
under zero-one loss, Machine Learning 29 (2) (1997) 103–130. `[doi:10.](http://dx.doi.org/10.1023/A:1007413511361)`
`[1023/A:1007413511361](http://dx.doi.org/10.1023/A:1007413511361)` .


[39] I. Androutsopoulos, J. Koutsias, K. V. Chandrinos, C. D. Spyropoulos, An
Experimental Comparison of Naive Bayesian and Keyword-Based AntiSpam Filtering with Personal E-mail Messages, Proceedings of the 23rd
Annual International ACM SIGIR Conference on Research and Development in Information Retrieval (2000) 160–167. `[arXiv:cs/0008019v1](http://arxiv.org/abs/cs/0008019v1)` .


[40] F. Gotzens, H. Heinrichs, J. H¨orsch, F. Hofmann, Performing energy modelling exercises in a transparent way - the issue of data quality in power
plant databases, Energy Strategy Reviews, same issue.


[41] A. Kies, K. Chattopadhyay, L. von Bremen, E. Lorenz, D. Heinemann,
RESTORE 2050 Work Package Report D12: Simulation of renewable feedin for power system studies., Tech. rep., RESTORE 2050 (2016).


[[42] B. Pfluger, F. Sensfuß, G. Schubert, J. Leisentritt, Tangible ways towards](http://www.1aufbau.de/isi-wAssets/docs/x/de/publikationen/Final_Report_EU-Long-term-scenarios-2050_FINAL.pdf)
[climate protection in the European Union (EU Long-term scenarios 2050),](http://www.1aufbau.de/isi-wAssets/docs/x/de/publikationen/Final_Report_EU-Long-term-scenarios-2050_FINAL.pdf)
Fraunhofer ISI.

URL `[http://www.1aufbau.de/isi-wAssets/docs/x/de/](http://www.1aufbau.de/isi-wAssets/docs/x/de/publikationen/Final_Report_EU-Long-term-scenarios-2050_FINAL.pdf)`
```
  publikationen/Final_Report_EU-Long-term-scenarios-2050_FINAL.
  pdf

```

[43] Copernicus ECMWF, ERA5 dataset, `[https://software.ecmwf.int/](https://software.ecmwf.int/wiki/display/CKB/ERA5+data+documentation)`
`[wiki/display/CKB/ERA5+data+documentation](https://software.ecmwf.int/wiki/display/CKB/ERA5+data+documentation)` (2017).


[44] D. Schlachtberger, T. Brown, S. Schramm, M. Greiner, The benefits of
cooperation in a highly renewable european electricity network, Energy
134 (Supplement C) (2017) 469 – 481. `[doi:10.1016/j.energy.2017.06.](http://dx.doi.org/10.1016/j.energy.2017.06.004)`

`[004](http://dx.doi.org/10.1016/j.energy.2017.06.004)` .


[45] U.S. Energy Information Administration, Hydroelectricity net generation
europe 2000-2014, `[http://tinyurl.com/EIA-hydro-gen-EU-2000-2014](http://tinyurl.com/EIA-hydro-gen-EU-2000-2014)`
(2017).


[46] G. B. Andresen, A. A. Søndergaard, M. Greiner, Validation of Danish wind
time series from a new global renewable energy atlas for energy system
analysis, Energy 93, Part 1 (2015) 1074 – 1088. `[doi:10.1016/j.energy.](http://dx.doi.org/10.1016/j.energy.2015.09.071)`

`[2015.09.071](http://dx.doi.org/10.1016/j.energy.2015.09.071)` .


[47] Y. Scholz, Renewable energy based electricity supply at low costs - Development of the REMix model and application for Europe, Ph.D. thesis,
Universit¨at Stuttgart (2012). `[doi:10.18419/opus-2015](http://dx.doi.org/10.18419/opus-2015)` .


18


[48] EEA, Corine Land Cover (CLC) 2012, version 18.5.1, `[https://land.](https://land.copernicus.eu/pan-european/corine-land-cover/clc-2012)`
`[copernicus.eu/pan-european/corine-land-cover/clc-2012](https://land.copernicus.eu/pan-european/corine-land-cover/clc-2012)` (2012).


[49] EEA, Natura 2000 data - the European network of protected sites, `[http:](http://www.eea.europa.eu/data-and-maps/data/natura-7)`
`[//www.eea.europa.eu/data-and-maps/data/natura-7](http://www.eea.europa.eu/data-and-maps/data/natura-7)` (2016).


[50] I. Staffell, S. Pfenninger, Using bias-corrected reanalysis to simulate current
and future wind power output, Energy 114 (2016) 1224 – 1239. `[doi:10.](http://dx.doi.org/10.1016/j.energy.2016.08.068)`
`[1016/j.energy.2016.08.068](http://dx.doi.org/10.1016/j.energy.2016.08.068)` .


[51] J. J. Michalsky, The astronomical almanac’s algorithm for approximate
solar position (1950–2050), Solar Energy 40 (3) (1988) 227–235. `[doi:10.](http://dx.doi.org/10.1016/0038-092x(88)90045-x)`
`[1016/0038-092x(88)90045-x](http://dx.doi.org/10.1016/0038-092x(88)90045-x)` .


[52] A. B. Sproul, Derivation of the solar geometric relationships using vector
analysis, Renewable Energy 32 (7) (2007) 1187–1205. `[doi:10.1016/j.](http://dx.doi.org/10.1016/j.renene.2006.05.001)`

`[renene.2006.05.001](http://dx.doi.org/10.1016/j.renene.2006.05.001)` .


[53] T. Huld, R. Gottschalg, H. G. Beyer, M. Topiˇc, Mapping the performance
of pv modules, effects of module type and data averaging, Solar Energy
84 (2) (2010) 324 – 338. `[doi:10.1016/j.solener.2009.12.002](http://dx.doi.org/10.1016/j.solener.2009.12.002)` .


[54] G. Andresen, J. H¨orsch, T. Brown, Atlite: Light-weight version of Aarhus
RE Atlas for converting weather data to power systems data, `[https://](https://github.com/FRESNA/atlite)`
`[github.com/FRESNA/atlite](https://github.com/FRESNA/atlite)` (2017).


[55] Country-specific hourly load data, `[https://www.entsoe.eu/data/](https://www.entsoe.eu/data/data-portal/consumption/)`
`[data-portal/consumption/](https://www.entsoe.eu/data/data-portal/consumption/)` (2011).


[56] ENTSO-E, Lengths of circuits, `[https://www.entsoe.eu/db-query/](https://www.entsoe.eu/db-query/miscellaneous/lengths-of-circuits)`
`[miscellaneous/lengths-of-circuits](https://www.entsoe.eu/db-query/miscellaneous/lengths-of-circuits)` (2015).


[57] I. S. Dhillon, Y. Guan, B. Kulis, Kernel k-means, Proceedings of the 2004
ACM SIGKDD international conference on Knowledge discovery and data
mining - KDD ’04. `[doi:10.1145/1014052.1014118](http://dx.doi.org/10.1145/1014052.1014118)` .


[58] E. Cotilla-Sanchez, P. D. H. Hines, C. Barrows, S. Blumsack, M. Patel,
Multi-attribute partitioning of power networks based on electrical distance,
IEEE Transactions on Power Systems 28 (4) (2013) 4979–4987. `[doi:10.](http://dx.doi.org/10.1109/TPWRS.2013.2263886)`
`[1109/TPWRS.2013.2263886](http://dx.doi.org/10.1109/TPWRS.2013.2263886)` .


[[59] Potenzial der Windenergienutzung an Land, Tech. rep., Bundesverband](https://www.wind-energie.de/shop-potenzial-der-windenergienutzung-land)
WindEnergie (2012).
URL `[https://www.wind-energie.de/shop-potenzial-der-windenergienutzung-land](https://www.wind-energie.de/shop-potenzial-der-windenergienutzung-land)`


[[60] Potenzial der Windenergie an Land, Tech. rep., Umweltbundesamt (2013).](https://www.umweltbundesamt.de/sites/default/files/medien/378/publikationen/potenzial_der_windenergie.pdf)
URL `[https://www.umweltbundesamt.de/sites/default/files/](https://www.umweltbundesamt.de/sites/default/files/medien/378/publikationen/potenzial_der_windenergie.pdf)`
```
  medien/378/publikationen/potenzial_der_windenergie.pdf

```

[[61] N. G. et al., Interaktion EE-Strom, W¨arme und Verkehr, Tech. rep.,](http://www.energiesystemtechnik.iwes.fraunhofer.de/de/projekte/suche/laufende/interaktion_strom_waerme_verkehr.html)
Fraunhofer IWES (2015).
URL `[http://www.energiesystemtechnik.iwes.fraunhofer.de/de/](http://www.energiesystemtechnik.iwes.fraunhofer.de/de/projekte/suche/laufende/interaktion_strom_waerme_verkehr.html)`
```
  projekte/suche/laufende/interaktion_strom_waerme_verkehr.html

```

19


[[62] H.-M. Henning, A. Palzer, 100% erneuerbare Energien f¨ur Strom und](https://www.ise.fraunhofer.de/de/veroeffentlichungen/veroeffentlichungen-pdf-dateien/studien-und-konzeptpapiere/studie-100-erneuerbare-energien-in-deutschland.pdf)
[W¨arme in Deutschland, Tech. rep., Fraunhofer ISE (2012).](https://www.ise.fraunhofer.de/de/veroeffentlichungen/veroeffentlichungen-pdf-dateien/studien-und-konzeptpapiere/studie-100-erneuerbare-energien-in-deutschland.pdf)
URL `[https://www.ise.fraunhofer.de/de/veroeffentlichungen/](https://www.ise.fraunhofer.de/de/veroeffentlichungen/veroeffentlichungen-pdf-dateien/studien-und-konzeptpapiere/studie-100-erneuerbare-energien-in-deutschland.pdf)`
```
  veroeffentlichungen-pdf-dateien/studien-und-konzeptpapiere/
  studie-100-erneuerbare-energien-in-deutschland.pdf

```

[63] J. H¨orsch, T. Brown, The role of spatial scale in joint optimisations of
generation and transmission for european highly renewable scenarios, in:
14th International Conference on the European Energy Market (EEM),
2017, pp. 1–7. `[arXiv:1705.07617](http://arxiv.org/abs/1705.07617)`, `[doi:10.1109/EEM.2017.7982024](http://dx.doi.org/10.1109/EEM.2017.7982024)` .


[64] M. Schumacher, L. Hirth, How much electricity do we consume? a guide to
german and european electricity consumption and generation data, FEEM
Working Paper No. 88.215. `[doi:10.2139/ssrn.2715986](http://dx.doi.org/10.2139/ssrn.2715986)` .


20


