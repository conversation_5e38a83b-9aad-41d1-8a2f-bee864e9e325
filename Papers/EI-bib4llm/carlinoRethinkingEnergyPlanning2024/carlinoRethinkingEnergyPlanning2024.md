# Citation Key: carlinoRethinkingEnergyPlanning2024

---

#### **nature sustainability**

**Article** [https://doi.org/10.1038/s41893-024-01367-x](https://doi.org/10.1038/s41893-024-01367-x)
# **Rethinking energy planning to mitigate the** **impacts of African hydropower**



Received: 11 October 2023


Accepted: 6 May 2024


Published online: 4 June 2024


Check for updates



**<PERSON>** **[1,2,5]** **, <PERSON>** **[2,3,4]** **, <PERSON>** **[2]** **&**
**<PERSON>** **[1]**


Around 100 GW of new hydropower projects have been proposed in
continental Africa to contribute to meeting future energy demand. Yet,
the future expansion of hydropower on the continent faces obstacles
due to the impacts of dams on rivers, greenhouse gas emissions from
reservoirs and increasingly competitive alternative renewable electricity
technologies. Here we propose an integrated approach to include these
considerations in energy planning. Compared with planning for least-cost
energy systems, capacity expansion strategies balancing environmental and
techno-economic objectives increase electricity prices and total discounted
costs by at most 1.4% and 0.2%, respectively, while reducing impacts on
annual hydropower emissions and river fragmentation by at least 50%. Our
results demonstrate that refining techno-economic analysis in light of global
and local environmental objectives can help policymakers reduce the river
fragmentation and greenhouse gas emissions associated with hydropower
development at marginal increases in energy costs.



Developing countries are implementing new energy infrastructure to
improve their living standards [1] and meet the needs of their growing
populations [2] . In this context, the deployment of low-carbon technologies is paramount to reducing greenhouse gas emissions and mitigating climate change [3] . From a levelized-cost-of-electricity perspective,
hydroelectricity has traditionally been an inexpensive source of power
that does not rely on fossil fuels [4] and has the potential to expand substantially [5], especially in the Global South, where most of its remaining
potential lies [6] . In Africa, more than 300 hydropower projects have been
proposed, which could provide an additional power capacity of around
100 GW for the continent [7] . This expansion would more than triple the
existing hydropower capacity of 40 GW, which currently represents
15% of the total installed capacity [3] .
Exploiting the continent’s hydropower potential would have many
social, environmental and climatic consequences [8] . Worldwide, only
23% of rivers flow uninterrupted to the ocean, of which the majority are
in the Arctic, with the remainder found in Southeast Asia and the Congo
Basin [9] . In particular, of the 543 rivers longer than 500 km flowing uninterrupted to the ocean globally, 156 (29%) are in the African continent [9] .



Flow patterns and blocked ecological corridors result in fragmentation
of the fluvial ecosystem [10] . Consequently, species habitats might be
negatively affected [11][,][12], leading to biodiversity loss [13][,][14] . Dams also have a
substantial impact on physical processes such as sediment delivery [15][,][16],
promoting coastal erosion and subsidence [17][,][18] . Hydropower development has to strike an adequate compromise between safeguarding
fluvial ecosystem services and building enough capacity to meet the
energy system needs [19][,][20] .
In addition, a growing body of research has documented the climate effects of reservoir hydropower. As organic matter decays in
reservoirs, these artificial water bodies can become non-negligible
sources of carbon dioxide and methane [21][,][22] . The main emissions pathways in reservoirs are methane bubbling, methane and carbon dioxide
diffusion, and degassing when water is released from the turbines [23][,][24] .
Since these processes depend on the abundance of biomass and the
speed of biological processes regulated by temperature, emissions are
higher for reservoirs at tropical latitudes, as confirmed by field measurements [25] . While carbon dioxide emissions from reservoirs decline

exponentially over time due to faster aerobic digestion, methane



1 Department of Electronics, Information, and Bioengineering, Politecnico di Milano, Milan, Italy. 2 Natural Capital Project, Stanford University, Stanford, CA,
USA. [3] Woods Institute for the Environment, Stanford University, Stanford, CA, USA. [4] Doerr School of Sustainability, Stanford University, Stanford, CA, USA.

5 Present address: Department of Global Ecology, Carnegie Institution for Science, Stanford, CA, USA. [e-mail: <EMAIL>](mailto:<EMAIL>)


[Nature Sustainability | Volume 7 | July](http://www.nature.com/natsustain) 2024 | 879–890 **879**


**Article** https://doi.org/10.1038/s41893-024-01367-x


**a** **b** **c**



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/carlinoRethinkingEnergyPlanning2024/carlinoRethinkingEnergyPlanning2024.pdf-1-1.png)



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/carlinoRethinkingEnergyPlanning2024/carlinoRethinkingEnergyPlanning2024.pdf-1-0.png)

Hydropower emissions (MtCO 2 e yr [–1] )


|SSP1-2.6<br>SSP4-6.0<br>SSP5-8.5<br>0 25 50 75 100 125 0 10 20 30 40<br>Installed hydropower capacity (GW) River fragmentation index (%)|Col2|Col3|
|---|---|---|
|SSP1-2.6<br>SSP4-6.0<br>SSP5-8.5<br>0<br>0<br>10<br>20<br>30<br>40<br>25<br>50<br>75<br>100<br>Installed hydropower capacity (GW)<br>River fragmentation index (%)<br><br><br><br>125|||



**Fig. 1 | Impacts of cost-optimal hydropower expansion.** **a**, Additional
hydropower capacity installed according to energy system planning. **b**, River
fragmentation index. **b**, Annual carbon-dioxide-equivalent hydropower


emissions increase and stabilize due to the long timescale of ebullition and degassing processes [26] . This implies that in a few decades, when
emissions from other sectors such as energy, industry and agriculture
have decreased, addressing reservoir emissions (particularly methane)
will become essential to achieving net zero emissions [26][,][27] .
Besides ecological and climatic concerns, there are also economic
challenges. Hydropower projects compete for capacity expansion with
technologies whose cost efficiency is rapidly increasing, especially
solar photovoltaics (PV) [28] and wind power [29] . Traditional hydropower
is a mature technology whose costs are not expected to change substantially in the future. In contrast, the costs of solar PV and wind are
expected to continue their present declining trend [30] . For this reason,
at least a third of the proposed hydroelectric projects in Africa are not
expected to be cost-optimal [31][,][32] .
Strategic hydropower planning can be one approach to solving
challenging techno-ecological trade-offs. It relies on multi-objective
optimization to navigate the trade-offs between environmental
impacts and hydropower expansion [33] . This method provides decision makers with a set of Pareto-optimal dam portfolios minimizing
environmental impacts for any given level of additional hydropower
generation or capacity [15][,][16][,][34][–][36] . However, few studies have examined the
role and cost-effectiveness of new hydropower in the context of future
energy systems [17][,][32][,][37] . In contrast, hydropower infrastructure sequencing within capacity expansion in energy and power system models is
driven by techno-economic objectives. Environmental impacts have
been examined a posteriori [38] or considered in the optimization phase
in the case of country-level power systems modelling [39] . There thus
remains a fundamental disconnect between the two approaches.
In the African continent, this disconnect may encourage risky
infrastructure development, ultimately hindering progress towards a
sustainable and secure energy future. The ability of new hydropower to
meet growing energy demand could have a substantial impact on rivers
and associated ecosystem services and greenhouse gas emissions. To
address these challenges, our research explores two questions. First,
what are the emissions and impacts on ecosystems associated with the
development of hundreds of hydropower projects planned until 2050
for the African continent? Second, we explore how the African energy
system can benefit from strategic dam planning approaches to reduce
these impacts. What would the ensuing economic consequences be
for the energy sector?



emissions. The hatched areas and the dashed lines indicate the capacity,
river fragmentation index and hydropower emissions for the existing and full
expansion scenarios, respectively.


Here we couple energy system modelling and strategic dam planning to assess the potential evolution of the hydropower sector on
the African continent from 2020 to 2050 under different climate and

socio-economic scenarios. By integrating these approaches, we resolve
their inconsistencies and reduce their respective drawbacks. As we consider the energy system and internalize previously overlooked climatic
and environmental externalities, we design cost-efficient pathways
for continental hydropower expansion that minimize environmental
impacts while meeting the demands of the energy system.
Our study demonstrates the value of integrating the technoeconomic perspective with the strategic dam planning approach to
select cost-effective, low-impact hydropower projects. These results
also underscore the importance of discovering near-optimal solutions [40] in energy system models to reduce non-monetizable risks
associated with infrastructure planning.


**Integrating environmental objectives in energy**
**planning**
We combined an energy system model with a hydropower development simulation model to examine techno-economic, global climatic
and local environmental objectives. In particular, we relied on the
OSeMOSYS-TEMBA open-source energy system model for the African
continent [41][–][44], modified to include specific existing and proposed hydropower plant data from the African Hydropower Atlas [7] . We considered
each of the 628 existing and future hydropower projects using capacity
factors obtained from a distributed hydrological model [32][,][45] using the
full expansion configuration under three climate and land-use change
scenarios, and we discuss the importance of this approximation in Supplementary Note 1. The model derives the development of cost-optimal
energy systems from 2020 to 2050, sequencing more than 300 proposed hydropower projects. We considered climate impacts on water
availability, land-use change and socio-economic projections for final
energy demands using the Inter-Sectoral Impact Model Comparison
Project 2b [46], a subset of the Shared Socioeconomic Pathways (SSPs) [47] .
These scenarios—SSP1-2.6, SSP4-6.0 and SSP5-8.5—represent future
narratives associated with ambitious sustainability targets, increasing
inequalities and development based on fossil fuels, respectively. While
we modelled the full energy system including non-electricity sectors, we
report the results for the power capacity expansions and the generation
trajectories, mostly focusing on hydropower.



[Nature Sustainability | Volume 7 | July](http://www.nature.com/natsustain) 2024 | 879–890 **880**


**Article** https://doi.org/10.1038/s41893-024-01367-x


42.5



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/carlinoRethinkingEnergyPlanning2024/carlinoRethinkingEnergyPlanning2024.pdf-2-0.png)



70


65


60


55









37.5


35.0


32.5


30.0


27.5


25.0













200 250 300 350



400 450 500 550



Hydropower generation (TWh yr [–1] )



**Fig. 2 | Multi-objective integration of energy system optimization and the**
**strategic dam planning approach.** The values of average annual generation,
annual carbon-dioxide-equivalent hydropower emissions and river fragmentation
index are reported for the dam portfolios analysed. First, we analysed emissions,
hydropower generation and river fragmentation for three portfolios of dams
generated by the energy system model, which does not consider those broader
environmental impacts of dams. The results are shown for three different SSPs


To address the impacts of hydropower development, in line with
strategic dam planning, we introduce the ecosystems–emissions–generation (EEG) simulation model. Given a set of existing and future dams,
the EEG simulation model computes the value of average annual hydropower generation, fluvial ecosystem fragmentation using the river
fragmentation index (RFI) [38][,][48][,][49] and annual carbon-dioxide-equivalent
emissions using a per-mass 100-year warming potential for methane
equal to 34 times that of carbon dioxide [25][,][26] . The emissions are computed using the surface area of each reservoir and the emission factors
based on the climatic zone [26] . The EEG simulation model can be coupled
with multi-objective optimization algorithms to sequence hydropower
projects, effectively balancing climatic and environmental considerations [15][,][16] . For more details about the models, see Methods.


**Impacts on river connectivity and hydropower**
**emissions**

Using the EEG simulation model, we examined the impacts of costoptimal hydropower sequencing obtained from energy system modelling in terms of hydropower greenhouse gas emissions and average
river fragmentation across African river basins where dams are present
or under consideration. Figure 1a illustrates that at least 32 GW (that
is, 32% of the proposed capacity) is not cost-optimal due to competition from other sources, particularly solar PV and wind power. The
competitiveness of other power sources is therefore responsible for
a reduction in the impacts associated with hydropower development
compared with the full expansion scenarios, resulting in a decrease of
at least 4% (−16% of the additional impact) in river fragmentation and
4.5 Mt of CO 2 -equivalent emissions (MtCO 2 e) per year (−19% of the
additional impact) in hydropower emissions.
However, the impacts of cost-optimal hydropower development
considering other renewable sources are still substantial compared
with present conditions. The current average river fragmentation
index across the African river basins is around 26%, as shown in Fig. 1b.
Under the cost-optimal energy system expansion scenarios, it would
increase to at least 33% and up to 38% in the worst case. For reference,
under the full expansion scenario, the river fragmentation index
could reach 42%, almost reaching the global average (that is, 43%) [49] .



(box (i)). Second, we performed an independent optimization to derive dam
portfolios that minimize conflicts between generation and environmental
objectives, without considering energy systems aspects. We then selected a
subset of these dam portfolios (box (ii)) and constrained the energy system
optimization to dams included in those scenarios. As a result, we found dam
portfolios (box (iii)) that optimize environmental objectives and are compatible
with the needs of the continental energy system by 2050.


Both fluvial ecosystem and hydropower emission impacts are larger
under the SSP1-2.6 scenario, intermediate under SSP5-8.5 and lowest
under SSP4-6.0, as the former requires more hydropower to meet more
ambitious emission reduction targets.
Because the energy expansion model does not consider greenhouse gas emissions from reservoirs, the dam portfolio for SSP1-2.6
results in the highest emissions from the hydropower sector reported
in Fig. 1c. When considering only hydropower, SSP4-6.0 and SSP5-8.5
have lower reservoir emissions, because less hydropower is installed.
Of course, the total energy system emissions of the latter two scenarios
are higher than the one observed in SSP1-2.6. As the energy system
considers only fossil fuel emissions, it deploys more hydropower to
reach ambitious emission reduction targets.
Under our modelling framework, the emissions associated with the
hydropower portfolio of SSP1-2.6 would not align with the achievement
of the 2.0 °C emission reduction target. Indeed, when the emission constraints become stringent, any additional emissions in the power sector
conflict with the demand and emissions from other sectors still relying
on fossil fuels, as shown in Supplementary Fig. 1. At present, emissions
from existing hydropower, on the order of 50 MtCO 2 e yr [−1], if accounted
for, would result in a 3% increase in the total African energy system
emissions. However, as the use of fossil fuels declines under SSP1-2.6,
the full hydropower expansion scenario would result in an increase by
7% of the total energy system emissions in 2050, if they were considered
using CO 2 e units based on the global warming potentials of 100 years [26] .
In this sense, the higher the reduction in fossil fuels and the following
reduction in emissions, the higher the relevance of hydropower emissions for achieving net zero emissions. Yet, large uncertainty remains in
the value of emissions [50] and in the fraction attributable to hydropower
generation rather than to other water uses.


**Reducing impacts via strategic dam planning**
To reduce the impacts of cost-optimal hydropower expansion, we
adopted a multi-objective perspective typical of strategic dam planning perspectives and examined the trade-offs between average
annual hydropower generation, river fragmentation and reservoir
emissions.



[Nature Sustainability | Volume 7 | July](http://www.nature.com/natsustain) 2024 | 879–890 **881**


**Article** https://doi.org/10.1038/s41893-024-01367-x



**a**


Energy system planning



**b**



Strategic dam planning



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/carlinoRethinkingEnergyPlanning2024/carlinoRethinkingEnergyPlanning2024.pdf-3-0.png)











Plant capacity (MW)


0.09


11,050



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/carlinoRethinkingEnergyPlanning2024/carlinoRethinkingEnergyPlanning2024.pdf-3-1.png)

Selected with energy system planning

Selected with strategic dam planning

Selected with energy system and strategic dam planning



**Fig. 3 | Analysis of foregone hydropower projects.** **a**, The sets of projects
selected under the three approaches are shown using a Venn diagram to examine
intersecting and complementary sets. For each set, we report the number of


We compared the objectives of cost-optimal hydropower expansion using the energy system model with those of hydropower expansion portfolios minimizing conflicts between environmental, climate
and generation objectives obtained from the EEG simulation model
via multi-objective optimization in Fig. 2. The energy system scenarios
remain suboptimal in this framing, as their performance is always
Pareto-dominated by the strategic dam planning approach. Indeed,
with the same level of emissions and river ecosystem fragmentation,
it would be possible to provide between 75 and 100 TWh of additional
generation using Pareto-optimal portfolios discovered via this planning approach. Similarly, 10 MtCO 2 e yr [−1] fewer emissions could be
produced for the same generation while reducing average river fragmentation by at least 5%.
However, the potential for impact reduction promised by strategic
dam planning does not yet consider the economic realities of energy
system constraints and the competition from other power sources.
For this reason, we integrated the two approaches. First, we selected
the subset of Pareto-optimal dam portfolios that outperform the best
case achieved using energy system planning in all three objectives:
emissions, ecosystem impacts and generation. Using this criterion,
the selected dam portfolios provide at least the same mean annual
generation of the SSP1-2.6 expansion scenario while reducing the values
of greenhouse gas emissions and fluvial ecosystems fragmentation
by at least 10% (in both cases associated with SSP4-6.0). Second, we
ran the energy system model to find the cost-optimal hydropower
expansion constrained to develop only dams belonging to the selected
Pareto-optimal portfolios.
Integrating environmental and climatic objectives into energy
system planning substantially reduces the impacts of cost-optimal
hydropower expansion. When the hydropower expansion in the energy
system model is constrained to the most efficient dams, the average
river fragmentation index is only slightly higher than current levels,
with an increase of at most 1%—that is, −94% of the additional impacts
of the full expansion scenario. At the same time, the increase in hydropower emissions is limited to no more than 5 MtCO 2 e yr [−1], resulting in
−80% of additional impacts compared with the full expansion scenario.
We also ran the energy system model constrained to develop dams that



projects and their total capacity (in GW) in parentheses. **b**, The locations of
these projects are reported on the map. The colour classification indicates the
approach that would select them.


are among the ones reported in any selected Pareto-optimal portfolio.
In this case (reported in Supplementary Fig. 2), compared with the
present conditions, the river fragmentation index and hydropower
emissions can increase by at most 3% and 8.5 MtCO 2 e yr [−1], respectively.
This would translate to −82% and −66% additional impacts compared
with the full expansion scenario for river fragmentation and hydropower emissions, respectively.


**Exploring alternative dam portfolios**
The hydropower projects selected by each approach (energy system
planning, strategic dam planning and integration) are reported in
Extended Data Fig. 1. For completeness, we report results when considering all the dams in selected Pareto-optimal dam portfolios in
Supplementary Fig. 3. Energy system planning consistently selects
62 projects, and 31 more are selected in at least one scenario. However, among the selected Pareto-optimal portfolios, 146 projects are
always selected, and 52 projects are selected in at least one scenario.
This shows an important difference between the approaches. Strategic
dam planning only considers installed hydropower capacity without
consideration of energy system needs. In contrast, energy system planning requires lower hydropower expansion than what was previously
anticipated by water resources planners, as it takes advantage of alternative cost-competitive power sources.
In addition, energy system planning tends to spread hydropower
expansion across a larger area, while strategic dam planning concentrates dams on particular rivers. For example, more projects tend to be
selected in the Congo, Zambezi and upper Nile River Basin according to
the strategic dam planning approach. This is because the energy system
model considers transmission constraints and costs. The strategic
dam planning approach instead prioritizes areas where adding more
dams does not substantially increase existing river fragmentation
(notably upstream of existing dams) and greenhouse gas emissions
(for example, in East Africa).
In Fig. 3a, we report the number and total capacity of projects
using a Venn diagram to examine relationships between the different
approaches. The selection of projects resulting after integration overlaps more with that of energy system planning (63 of 93 projects, or 45



[Nature Sustainability | Volume 7 | July](http://www.nature.com/natsustain) 2024 | 879–890 **882**


**Article** https://doi.org/10.1038/s41893-024-01367-x



**a**



**b** **c**





Oil


Gas


Coal


Biomass


Nuclear


Geothermal


Wind


Solar


Hydro



|15<br>0<br>5<br>0|Col2|Col3|Col4|Col5|Col6|Col7|Col8|Col9|
|---|---|---|---|---|---|---|---|---|
|15<br>0<br>5<br>~~0~~|||||||||
|15<br>0<br>5<br>~~0~~|||||||||
|–5<br>0<br>15|||||||||


**Fig. 4 | Change in installed capacity after the integration of environmental and climatic objectives.** **a** – **c**, Differences in installed capacity between energy system
planning and energy system planning constrained to always Pareto-optimal dams under SSP1-2.6 ( **a** ), SSP4-6.0 ( **b** ) and SSP5-8.5 ( **c** ).



of 60 GW) and less with selected Pareto-optimal portfolios (70 of 146
projects, or 47 of 65 GW). Focusing on the foregone projects, shown
in Fig. 3b, it can be seen that constraints associated with the energy
system substantially reduce the number of projects selected by the
strategic dam planning approach. Similar results are observed when
the integration is performed constraining the energy system model
to all the dams belonging to selected Pareto-optimal portfolios, as
reported in Supplementary Fig. 4. This underscores the importance of
considering alternative power sources to provide electricity to meet
the demand. While the projects selected by strategic dam planning and
then foregone after integration are distributed all over the continent,
16 of the 28 projects foregone after being selected by the energy system planning approach are located in West Africa. These few projects
are of particular interest because, when they are avoided, impacts on
greenhouse gas emissions and river fragmentation can be drastically
reduced, mitigating the concerns associated with unintended consequences of hydropower expansion.


**The costs of including environmental and**
**climatic objectives**
In Fig. 4, we report the change in installed capacity when constraining the energy system model to develop only dams among the ones
belonging to all selected Pareto-optimal portfolios. We also examined the capacity changes when considering all dams in the selected
Pareto-optimal portfolios (Supplementary Fig. 5), and we report the
differences in generation in Supplementary Fig. 6. As hydropower’s
role declines in terms of capacity and generation, other sources fill the
gaps. For SSP4-6.0, in the short term and in the long term, fossil fuels
are important to guarantee demand satisfaction, but wind and solar
are important as well. Yet, especially for SSP1-2.6, most of the power
capacity additions are solar and wind, while in terms of generation,
nuclear and geothermal also substantially contribute to closing the
gap. Considering the changes in each river basin reported in Fig. 5, after
integrating environmental and climatic objectives in energy system
planning, the capacity selected in smaller river basins is reduced by
around 50% in all scenarios. To a lesser extent, this also occurs in the
Nile, Niger and Zambezi river basins. Yet, the cost-optimal capacity
proposed by the energy system planning approach in the Congo River
basin (including the construction of the Inga 3 project) is not sensitive
to environmental and climatic objectives. When considering a less
stringent constraint for integration, changes in additional hydropower
capacity per basin are smaller, as reported in Supplementary Fig. 7.



Niger, Zambezi and in particular smaller river basins see the largest
reductions.

Given the changes in installed capacity and hydropower projects’
selection, we estimated the economic impacts on total discounted
costs and electricity prices. If the changes in costs or prices are not
substantial, the integration of energy system modelling and strategic
dam planning might provide a more desirable infrastructure plan
for policymakers. Indeed, near-optimal solutions in energy system
modelling can unveil promising system design alternatives [40][,][51] . We
report the percentage increase in mean electricity prices and total
discounted costs in Table 1. Supplementary Table 1 provides the change
in absolute values, also reported in Supplementary Figs. 8 and 9. The
increase in mean electricity prices is at most 1.4%, while the increase in
total discounted costs is always below 0.2%, and we can consider them
to be not substantial given the uncertainties involved with our modelling frameworks. The maximum difference of total discounted costs,
obtained under SSP1-2.6 constrained to projects always part of the
selected Pareto-optimal portfolios, is around US$5 billion in net present
value. At current costs [4][,][43], this is equivalent to the capital investment
required to build a 3 GW solar project, a negligible amount given the
scale of the transformation required for the transition to a low-carbon
energy system. Similarly, compared with the annual US$190 billion
investment in clean energy in Africa suggested by the International
Energy Agency [3], the additional cost of developing hydropower sustainably and strategically seems negligible.


**Discussion**

Energy system models consider hydropower as a cheap and carbonneutral power source that should be developed where it is technically
and economically feasible [52] . For this reason, more than 300 hydropower projects are under consideration in the African continent.
Yet, environmental and climatic concerns coupled with increased
cost-competitiveness of other power sources require a multi-objective
planning approach. In particular, we examine two research questions:
(1) What are the impacts of African hydropower expansion in terms of
emissions and fluvial ecosystem fragmentation? (2) What is the cost
of reducing these impacts? We show that cost-optimal hydropower
development increases hydropower emissions by 15–20 MtCO 2 yr [−1]
and river fragmentation across the continent by 5–10%. By integrating
energy system modelling and strategic dam planning, these impacts
(which already represent a reduction of 20–40% in hydropower emissions and 44–75% in river fragmentation impacts associated with the



[Nature Sustainability | Volume 7 | July](http://www.nature.com/natsustain) 2024 | 879–890 **883**


**Article** https://doi.org/10.1038/s41893-024-01367-x



**a**



**b** **c**

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/carlinoRethinkingEnergyPlanning2024/carlinoRethinkingEnergyPlanning2024.pdf-5-1.png)


Energy system planning Integration



16


14


12


10


8


6


4


2


0



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/carlinoRethinkingEnergyPlanning2024/carlinoRethinkingEnergyPlanning2024.pdf-5-0.png)

**Fig. 5 | Changes in installed hydropower capacity by basin for the integration**
**of environmental and climatic objectives.** **a** – **c**, New installed hydropower
capacity in the four major river basins and aggregated for smaller river basins


**Table 1 | Increases in electricity prices and total discounted**
**costs associated with integrating environmental and**
**climatic objectives**





Increase in mean electricity 1.4 (0.7) 0.5 (0.8) 0.4 (0.1)
prices (%)


The percentage increase of mean electricity prices and total discounted costs when moving
from cost-optimal to constrained energy system development is shown. The values reported
are for energy system development constrained to projects that are always in the selected
subset of Pareto-optimal portfolios. The values in parentheses are for the projects in at least
one of the selected Pareto-optimal portfolios.


full expansion of hydropower) can be reduced by at least 50% and up
to 75% and 86%, for hydropower emissions and river ecosystem fragmentation, respectively. These climate and environmental benefits
come with little impact on the economic performance of the energy
system, as we report a maximum increase of 1.3% in electricity prices
and 0.2% in total discounted costs.

In this work, we connect energy system planning with strategic
dam planning to integrate techno-economic constraints and competition with other power sources with environmental and climatic
consequences of hydropower using multi-objective optimization.
Notwithstanding the uncertainty associated with the estimation of
greenhouse gas emissions from reservoirs, these emissions can be
substantial and would comprise part of the carbon budget associated
with a particular warming target. Additional ecological constraints
might be imposed by, for example, targets for riparian conservation
and biodiversity. It is thus a major limitation that energy system models are not aware of local, regional or global impacts of hydropower
generation, while approaches to reduce the environmental impacts of
hydropower do not, or only rudimentarily [35], integrate energy system
considerations [53] .

With our work, we reveal the substantial potential of integrating
energy system modelling and strategic dam planning to reconcile
energy, climate and river function. We also show that these benefits
come with only a minor penalty in terms of costs. This allows us to
maintain a balanced approach that guarantees meeting energy demand
at low cost and developing hydropower where new infrastructure does
not substantially degrade the value of ecosystem services or produce
a long-term source of greenhouse gas emissions.



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/carlinoRethinkingEnergyPlanning2024/carlinoRethinkingEnergyPlanning2024.pdf-5-2.png)

for SSP1-2.6 ( **a** ), SSP4-6.0 ( **b** ) and SSP5-8.5 ( **c** ) under the energy system planning
approach and after the integration of energy system planning and strategic dam
planning for each scenario considered.


While our results encourage the integration of strategic dam
planning with energy system modelling, many of the assumptions
made for this study should be further explored and refined. First,
regarding the emissions from reservoirs, more data could reduce the
uncertainty in greenhouse gas flux estimates. Second, energy system
models including multiple greenhouse gases in the style of integrated
assessment models could be used to avoid the need to convert emis
sions into carbon dioxide equivalent and to allow emissions from the
hydropower sector to feed back into the other energy sectors for a full
readjustment of the energy system. Third, we adopted river fragmentation across the continent as a proxy of ecosystem services, but a large
body of literature is available to explicitly model the impacts of dams
on many domains of river ecosystems—for example, on fish habitat [12]
or sediment connectivity [15][–][17][,][35][,][53] . Even though river fragmentation has
been shown to correlate strongly with some of these objectives (notably, sediment transport [35] ), we recommend the use of more detailed
indicators, ideally agreed upon after consulting with local stakeholders, to accurately target objectives of local interest such as minimum
environmental flow [54], sediment transport [15][,][16] and fish biodiversity [12] .
Additionally, refining the estimates of channel width and depth along
the river network using remote sensing data [55] and in situ measurements could be instrumental in improving estimates of reach volumes,
which is important for fluvial connectivity. Measuring impacts across
multiple basins is also difficult, as it requires assuming a particular
aggregation function, resulting in the loss of information at a finer
scale. The above considerations could be extended to the assessment

of negative effects on the local displaced populations and their cultural heritage [56], as well as the potential to limit exposure to political
conflicts [57] . We also acknowledge the potential to substantially increase
the resolution of our results using more detailed reservoir operation
modelling and a dam network simulation model to better account
for cascading effects [39][,][58][–][61] . Combining simulation-based optimization with model emulation for integrated water and energy systems
would lend itself to the implementation of bottom-up approaches for
robust infrastructure design [17][,][59][,][62], which could be used to examine
more in detail the impacts on other uses of reservoirs such as irrigation [63] . Finally, we left out considerations regarding spatial correlations
of climatic variability and its impacts on power supply [64], land-use
impacts associated with solar PV and wind power stations [65][,][66], and the
importance of cost overruns for large infrastructural projects [67][,][68] . We
suggest including these factors in studies and analyses refining our
approach at a more local level.



[Nature Sustainability | Volume 7 | July](http://www.nature.com/natsustain) 2024 | 879–890 **884**


**Article** https://doi.org/10.1038/s41893-024-01367-x



Notwithstanding the limitations and potential improvements
to the modelling framework adopted in this analysis, we show the
large potential of integrated multi-sector modelling supported by
multi-objective optimization. By reconciling and unifying the perspectives adopted to examine the dam planning problem, we can identify
poorly performing dams and foster hydropower expansion strategies that reduce both local environmental impacts and global climate
impacts with only modest cost increases.


**Methods**
**Energy system model**
The OSeMOSYS-TEMBA energy system model [43][,][44] is an open-source
implementation of the OSeMOSYS modelling framework [41][,][69] for the
African continent. It requires demands for all energy carriers—that is,
electricity and fuels. Resource extraction and transformation technologies are provided for the different energy carriers to describe
the full path from primary to final energy. We used a modelling horizon covering from 2020 to 2050, with a country-level spatial resolution and a seasonal step divided into day and night time-slices. The
model minimizes the total discounted costs to satisfy the demand.
The energy demand of the original OSeMOSYS-TEMBA model is based
on the observed consumption of the different fuels and projections
of gross domestic product (GDP) and electrification so that future
consumption includes new demand due to increasing population and
living standards as well as increased levels of energy access over time [43] .
Regarding the power sector, the model makes decisions for capacity
expansion and generation from each power source and considers
constraints with the other energy sectors to minimize total discounted
costs. In particular, we used the OSeMOSYS-TEMBA model version
developed to represent each hydropower plant (existing and proposed) in the energy system model. We adopted a mixed integer linear
programming problem formulation to include binary decision variables used to optimize the timing of the construction of the proposed
hydropower projects [32] . The African Hydropower Atlas [7] collects information about more than 600 hydropower projects, of which more than
300 are proposed. It also reports the generation after climate impacts
on water availability and changes in land use coherently with the SSP
scenarios [47] . These scenarios provide narratives to examine potential
future socio-economic evolution. In our model, we relied on the scenarios that overlap with the ones used in the Inter-Sectoral Impact
Model Intercomparison Project 2b [46] —that is, we used SSP1-2.6, SSP46.0 and SSP5-8.5. These scenarios represent future socio-economic
evolution with sustainable development, increasing inequality and
fossil-fuelled development, respectively. They are the same as the
scenarios adopted in the energy system model. Accordingly, the final
energy demands in the energy system model are obtained by downscaling the SSP energy projections for the region using the GDP of
the country considered. In particular, the final energy demands are
obtained by assuming that the existing final energy demand in the
energy system model converges to the value obtained via downscaling over time as discussed in ref. 32. We used GDP as an external variable for downscaling as it is a measure of the total economic activity
and energy use of a country. Indeed, on the basis of data from 2015
for the 48 mainland African countries considered in our study, GDP
holds a higher correlation with electricity consumption than GDP
per capita and population, as reported in Supplementary Fig. 10. By
including each hydropower project in the energy system model, we
can analyse the cost-optimality of each project and better compare
it with the strategic dam planning approach. The water availability
for each hydropower project is based on simulations of the SWAT+
model [45], a distributed hydrological model, forced with bias-corrected
daily weather data obtained from four general circulation models
(GFDL-ESM2M, HadGEM2-ES, ISPL-CM5A-LR and MIROC5) under three
Representative Concentration Pathway (RCP) and SSP combinations
(SSP1–RCP2.6, SSP4–RCP6.0 and SSP5–RCP8.5), thus resulting in 12



projections of discharge using the same approach reported in ref. 7
but for the temporal horizon 2020–2050. The water availability has
been computed for each general circulation model in the ensemble,
and the average relative difference in monthly availability between
the projection and control periods across the four general circulation
models has been used to obtain future water availability on the basis
of historical water availability obtained using the EWEMBI dataset
for each SSP–RCP combination considered [7] . The capacity factors of
each hydropower project were computed on the basis of the average
water availability across the four models for each SSP–RCP combination considered.


**Dam portfolio simulation model**
We built the EEG simulation model following the strategic dam planning approach. The model allows us to evaluate different hydropower
expansion alternatives on the basis of their impact on fluvial ecosystem
connectivity, annual greenhouse gas emissions from hydropower
and annual mean generation. We used this model as a simulation
tool coupled with multi-objective evolutionary algorithms to derive
Pareto-optimal dam portfolios. Since this model does not consider
the energy system, the Pareto-optimal dam portfolios were used to
constrain the energy system model, implementing a soft model integration approach.


**Fluvial ecosystem connectivity model**
To evaluate the impact on fluvial ecosystem connectivity, we used the
RFI [38][,][48][,][49], which is formulated as follows:



RFI = 100 1 −
× (



_N_ r
∑

_i_ =1



_v_ [2]

_V_ _i_ [2] [)] (1)



where _N_ r is the number of river reaches in the basin examined, _v_ _i_ is
the volume of each fragmented river reach and _V_ is the total volume
of the river. We computed the RFI for each river and then averaged it
over all the rivers on which we have infrastructural development. To
compute river reaches’ volume and to minimize the computational
cost of computing the RFI over a large network, we combined information from multiple datasets to build a compact river network
representation.
This study used two datasets: (1) the African Hydropower Atlas [7],
storing all existing and proposed dams, along with such attributes
as coordinates (°), capacity (MW), discharge (m [3] s [−1] ) and reservoir
volume (m [3] ); and (2) the HydroRIVERS [70] dataset, a network of _O_ (10 [5] )
reaches, with each reach having such attributes as coordinates (°),
reach length (m), discharge (m [3] s [−1] ) and Strahler order. The volume
of each river reach is computed using the reach length and the discharge from the HydroRIVERS dataset with power laws calibrated
to empirical data to obtain the river channel depth and width [71] . This
compact approach allows the estimation of channel characteristics,
also in data-scarce environments, and it is commonly adopted in
these settings [38] .
Because these two datasets’ coordinate systems were not calibrated, the locations given by their respective coordinates were not
aligned, resulting in dams that were not on the river network when the
two datasets were combined. Thus, we first implemented an algorithm
wherein we snapped each dam’s location to the geographically closest
river reach in the dataset-merging process. To validate the accuracy of
this approach, we visualized the ratio of each dam’s discharge to that
of the river reach to which it was snapped against the dam’s power
capacity, all on a log–log plot. An accurate snapping algorithm would
have resulted in a dam-to-reach discharge ratio of 1 for every dam, but
this was not the case solely using geographic proximity as the heuristic. Specifically, this approach snapped dams to small reaches, with
the magnitude of the discharge discrepancy increasing for dams with
greater capacity (Supplementary Fig. 11).



[Nature Sustainability | Volume 7 | July](http://www.nature.com/natsustain) 2024 | 879–890 **885**


**Article** https://doi.org/10.1038/s41893-024-01367-x



We therefore developed a more accurate snapping algorithm to
prevent dams from being placed on river reaches that were too small.
The algorithm is described as follows:



(2)
}



River reach minimum Strahler order
= {



2, if log(capacity) ≤ 1


3, if log(capacity) > 1



The closest river reach meeting a particular dam’s minimum
Strahler order was assigned to that dam.
This algorithm resulted in 96.8% of the 559 dams being matched
to river reaches with a discharge of the same order of magnitude as
that of the dam. The log–log error plot resulting from the application
of this snapping algorithm can be visualized in Supplementary Fig. 12.
The remaining 18 dams that could not be placed on the appropriate
river reach were assigned to their respective reaches manually in QGIS
3.27 (Firenze) [72] .
Because the optimization requires many iterations, in our case
_O_ (10 [6] ), and the optimization is _O_ ( _N_ ) in network size, running the optimization over the entire HydroRIVERS network to determine the Pareto
front of dam portfolios is computationally intractable. However, to
calculate the connectivity of river basins, it is only necessary to differentiate between river reaches that are separated by a dam, as the
optimization only selects locations corresponding to dams in the
African Hydropower Atlas dataset, so the fuller resolution afforded
by the full _O_ (10 [5] )-reach network is not beneficial.
Specifically, to prepare the river network to facilitate computational efficiency, we aggregated the reaches so that only one reach,
with aggregated attributes, lay between each dam, as exemplified
in Supplementary Fig. 13 using a small river basin in Northern Africa
(Medjerda).
To achieve this, we represented the merged river-and-dam network
as a graph network, using the Python package NetworkX [73] . The river
reaches became graph nodes, with such attributes as length, volume,
discharge and (if applicable) associated dam name, as shown in Supplementary Fig. 14. The directed edges of the network represented
the water flow in the river network from reach to reach. To simplify the
river network and create new, aggregated river reaches, the following
algorithm was used:


 - Traverse through each node that has an associated dam. For
each node, cut the edges of the node connecting it to its neighbours. This creates clusters of weakly connected components,
corresponding to reaches that are not separated by a dam or
topology.

 - Traverse through each weakly connected component. Within
each weakly connected component, traverse through each node
to aggregate attribute values (for example, sum length).

 - Re-insert cut-directed edges between the newly aggregated
nodes.


With this simplification algorithm, we reduced the river network
from one comprising 880,975 nodes and 880,398 edges to one comprising 577 nodes and 489 edges, corresponding to a >1,500× simplification. This facilitated the optimization, as it allowed us to run the
required number of function evaluations in only minutes.


**Greenhouse gas emissions from hydropower plants**
To estimate the emissions for each hydropower project, we first used
the carbon intensity from the NREL Life Cycle Assessment Harmonization project [74], 20.5 gCO 2 kWh [−1], which is mostly associated with
the hydropower project construction but also includes operations
and decommissioning [75], and is consistent with other estimates in the
literature [76] . To obtain the value of emissions, we multiplied this value
of carbon intensity by the generation produced over the year by each
project, for which we used the average annual generation across the
three SSP–RCP scenarios considered.



For reservoir hydropower, we included reservoir emissions by
multiplying the surface area of the reservoir by the areal flux of greenhouse gas emissions.
We used data for African reservoirs from the GRAND database [77]

to estimate the reservoir areas. When data on the surface area of a

reservoir were not available, we used multiple log–log regression. The
regressors were the reservoir volume from the African Hydropower
Atlas and the reservoir’s catchment area, estimated using the flow direction raster from HydroRIVERS [70] and the Python package pysheds [78] . We
calibrated this functional relationship using reservoirs for which we
had data from the GRAND dataset. The calibrated function improves
existing methods by achieving an _R_ [2] of 0.95 in the log space when tested
over available data in the GRAND database for African reservoirs, as
reported in Supplementary Fig. 15. We then used this relationship to
quantify reservoir areas (1) for current dams without tabulated surface
area and (2) for all future dams from the African Hydropower Atlas.
Once the area for each reservoir had been obtained, we could
compute the emissions from each reservoir by multiplying the surface
area by the areal flux associated with the climatic zone observed in the
reservoir location.

The fluxes are available under two different parameterizations
obtained via the G-res model [79] . In Supplementary Fig. 1, we report the
emissions obtained by parametrizing the fluxes on the basis of latitude
(left) [25] and climatic zone (right) [26] . Examining Supplementary Fig. 1
shows that substantial uncertainty remains in the absolute values of
reservoirs’ greenhouse emissions [50] . These emissions will also compete
with emissions from other sectors, as shown by the black and red lines
reporting the total emissions limit in 2050 and 2070 for the 2.0 °C and
1.5 °C temperature increase scenarios. This shows the importance of
tackling and reducing these sources of emissions to avoid conflicts
with other sectors (for example, heat and transport) when aiming for
very low carbon intensities of the energy system. Yet, as reported in
ref. 25, only a few points are available for Africa, and most of the estimates at these latitudes are from Southeast Asia or Latin America.

For this reason, our results are based on the greenhouse gas emission
estimations based on the climatic zone as in ref. 26. More points are
available to compute averages for different climatic zones, and local
climatic conditions are more informative than latitude to describe

the biogeochemical processes of interest to obtain the magnitude
of greenhouse gas fluxes. To consider net greenhouse gas fluxes, we
referred to the coefficients used in ref. 34 to reduce the carbon dioxide

emissions by 75% and the methane emissions by 10%.


**Multi-objective dam portfolio optimization**
We can now define the multi-objective optimization problem as follows:


min **u** **J** ( **u** ) (3)


**J** ( **u** ) = [RFI avg ( **u** ), GHG( **u** ), −GEN( **u** )] (4)


**u** = [ _u_ 1, _u_ 2, …, _u_ _N_ p ] (5)


_u_ _i_ ∈{0, 1}, _i_ = 1, 2, …, _N_ p (6)



2

( _v_ _i_, _b_ ( **u** ))


_V_ [2]

_b_



1
RFI avg ( **u** ) = _N_ b



×



_N_ b
∑

_b_ =1


_N_ ∑ r, _b_

_i_ =1



RFI _b_ ( **u** ) (7)



(8)
)



RFI _b_ ( **u** ) = 100 1 −
× (



GHG( **u** ) =



_N_ p
∑

_i_ =1



_u_ _i_ × GHG _i_ (9)



[Nature Sustainability | Volume 7 | July](http://www.nature.com/natsustain) 2024 | 879–890 **886**


**Article** https://doi.org/10.1038/s41893-024-01367-x



GEN( **u** ) =



_N_ p
∑

_i_ =1



_u_ _i_ × GEN _i_ (10)



1
GEN _i_ = _N_ SSP × SSP∈{ ∑ SSPs}



CAP _i_ × 8,760 × 12 [1] [×]



12
∑

_m_ =1



CF SSP, _m_, _i_ (11)



SSPs = { SSP1-2.6, SSP4-6.0, SSP5-8.5 } (12)


where **J** is a vector containing the objectives considered in the optimization. These objectives are RFI avg ( **u** ), or the average RFI; GHG( **u** ), or the
annual emissions associated with existing and future hydropower; and
GEN( **u** ), or the average annual generation provided by existing and future
hydropower. To unify the notation adopted in the formulation of the
minimization problem, we considered the negative value of hydropower
generation because we wanted to maximize this objective. The vector **u**
contains all the binary decision variables that describe the construction
(or not) of all the _N_ p hydropower projects considered, 330 in this case.
_N_ b is the number of river basins affected by new hydropower
projects and over which the RFI is averaged, and _N_ r, _b_ is the number of
river reaches in basin _b_ . In each river basin _b_, the length of fragmented
reaches depends on the realization of hydropower projects—that is,
on the decision vector **u** .

Regarding the greenhouse gas emissions objective, we summed
the emissions for each project _i_, GHG _i_, depending on its construction
decision _u_ _i_ . The emissions for each project _i_ are described in detail in
the section above.

Depending on the decision variable of each project _u_ _i_, the values of average annual generation GEN _i_ are summed to compute the
value of the generation objective. To compute the value of the average
generation, we computed the annual generation under the three SSP
scenarios considered. For each scenario, we computed the average of
the monthly capacity factors CF SSP, _m_, _i_ obtained under the full expansion from the African Hydropower Atlas and then multiplied it by the
nominal capacity of project _i_ (CAP _i_ ) and the number of hours in a year
to pass from capacity to generation units. Finally, we found the average annual generation as the average of the annual generations under
the number of SSP scenarios considered, _N_ SSP . Our approach neglects
the cascading effects resulting from the different dam configurations
and the importance of reservoir operating policies—that is, we do not
consider the effects that upstream dams might have on the generation at downstream dam sites. We adopted this approach to keep the
optimization problem tractable given the large number of hydropower
projects considered (628). However, we estimated the errors stemming from this assumption using a network simulation model of the
Zambezi River basin, one of the four largest African rivers. We found
that the mean error estimation in the capacity factors due to network
configurations is 4%, while alternative dam operations could influence
capacity factors by 5.5%. These experiments are described in detail in
Supplementary Information (Supplementary Note 1).
We solved the multi-objective optimization problem using
Borg-MOEA [80], an auto-adaptive many-objective evolutionary algorithm
widely adopted in water resources engineering. Notwithstanding the
full number of potential combinations (2 [330] ~ 10 [100] ), the optimization
converges in 10 [6] function evaluations as shown in Supplementary
Fig. 16. For our results, we used the output of a single seed. However, we
provide evidence that the approximate set of the seed we used covers a
fraction equal to 0.99 of the hypervolume of the reference set obtained
running five seeds for the same number of iterations. We compare the
two sets of solutions in Supplementary Fig. 17.


**Constraining capacity expansion with selected dam portfolios**
To derive the dam portfolios for integration with the energy system model, we selected the subset of Pareto-optimal solutions that
(1) provide more generation than all the hydropower expansion plans



generated by the energy system model, (2) reduce greenhouse gas emissions by 10% compared with the least-emitting capacity expansion plan
derived from the energy system and (3) reduce the RFI by 10% compared
with the lowest value observed among the hydropower capacity expansion plans optimized in the energy system. The selected dam portfolios
were used to constrain the energy system model in two ways. We ran the
three scenarios constrained to the hydropower projects that are part
of the selected Pareto-optimal dam portfolios or to all dams belonging
to the Pareto-optimal dam portfolios. By doing so, we examined the
potential for impact reduction at different costs associated with the
integration of strategic dam planning and energy system planning.


**Reporting summary**
Further information on research design is available in the Nature
Portfolio Reporting Summary linked to this article.


**Data availability**
The OSeMOSYS-TEMBA energy system model data are available via
[Zenodo at https://doi.org/10.5281/zenodo.3521841 (ref. 81) in their modi-](https://doi.org/10.5281/zenodo.3521841)
fied version, including power plant data for more than 600 hydropower
projects from the African Hydropower Atlas, which are available via
[Zenodo at https://doi.org/10.5281/zenodo.7931050 (ref. 82). The African](https://doi.org/10.5281/zenodo.7931050)
Hydropower Atlas contains technical information about the existing
[and future hydropower projects and is available online (https://www.](https://www.hydroshare.org/resource/5e8ebdc3bfd24207852539ecf219d915/)
[hydroshare.org/resource/5e8ebdc3bfd24207852539ecf219d915/). The](https://www.hydroshare.org/resource/5e8ebdc3bfd24207852539ecf219d915/)
GRAND database containing information about existing dams worldwide
[is available thanks to the Global Dam Watch project (https://www.global-](https://www.globaldamwatch.org/grand)
[damwatch.org/grand). Emission factors are available from the National](https://www.globaldamwatch.org/grand)
[Renewable Energy Laboratory (https://data.nrel.gov/submissions/171).](https://data.nrel.gov/submissions/171)
The SSPs report socio-economic projections using Integrated Assess[ment Models and are available online (https://tntcat.iiasa.ac.at/SspDb/](https://tntcat.iiasa.ac.at/SspDb/dsd?Action=htmlpage&page=welcome)
[dsd?Action=htmlpage&page=welcome). Figure 3, Supplementary Figs. 3](https://tntcat.iiasa.ac.at/SspDb/dsd?Action=htmlpage&page=welcome)
and 4, and Extended Data Fig. 1 incorporate data from the HydroSHEDS
v.1 database, which is © World Wildlife Fund, Inc. (2006–2022) and has
been used herein under licence. The World Wildlife Fund has not evalu
ated the data as altered and incorporated in these figures, and therefore
gives no warranty regarding its accuracy, completeness, currency or
suitability for any particular purpose. Portions of the HydroSHEDS v.1
database incorporate data that are the intellectual property rights of
© USGS (2006–2008), NASA (2000–2005), ESRI (1992–1998), CIAT
(2004–2006), UNEP-WCMC (1993), the World Wildlife Fund (2004), the
Commonwealth of Australia (2007), and His Royal Majesty and the British
Crown, and are used under licence. The HydroSHEDS v.1 database and
[more information are available at https://www.hydrosheds.org (ref. 70).](https://www.hydrosheds.org)
All the data to reproduce the results and figures are available via Zenodo
[at https://doi.org/10.5281/zenodo.8360437 (ref. 83).](https://doi.org/10.5281/zenodo.8360437)


**Code availability**
All the processing scripts to reproduce the results and figures are avail[able via Zenodo at https://doi.org/10.5281/zenodo.8360437 (ref. 83).](https://doi.org/10.5281/zenodo.8360437)


**References**

1. Kikstra, J. S., Mastrucci, A., Min, J., Riahi, K. & Rao, N. D. Decent
living gaps and energy needs around the world. _Environ. Res. Lett._
**16**, 095006 (2021).
2. Akintande, O. J., Olubusoye, O. E., Adenikinju, A. F. & Olanrewaju,
B. T. Modeling the determinants of renewable energy
consumption: evidence from the five most populous nations in
Africa. _Energy_ **206**, 117992 (2020).
3. _Africa Energy Outlook 2022: World Energy Outlook Special Report_
(IEA, 2022).
4. _Renewable Power Generation Costs in 2022_ (IRENA, 2023).
5. Zarfl, C., Lumsdon, A. E., Berlekamp, J., Tydecks, L. & Tockner, K.
A global boom in hydropower dam construction. _Aquat. Sci._ **77**,
161–170 (2015).



[Nature Sustainability | Volume 7 | July](http://www.nature.com/natsustain) 2024 | 879–890 **887**


**Article** https://doi.org/10.1038/s41893-024-01367-x



6. Gernaat, D. E., Bogaart, P. W., Vuuren, D. P. V., Biemans, H. &
Niessink, R. High-resolution assessment of global technical and
economic hydropower potential. _Nat. Energy_ **2**, 821–828 (2017).
7. Sterl, S. et al. A spatiotemporal atlas of hydropower in Africa for
energy modelling purposes. _Open Res. Eur._ **1**, 29 (2022).
8. Llamosas, C. & Sovacool, B. K. The future of hydropower? A
systematic review of the drivers, benefits and governance
dynamics of transboundary dams. _Renew. Sustain. Energy Rev._
**137**, 110495 (2021).
9. Grill, G. et al. Mapping the world’s free-flowing rivers. _Nature_ **569**,
215–221 (2019).
10. Anderson, E. P. et al. Fragmentation of Andes-to-Amazon
connectivity by hydropower dams. _Sci. Adv._ **4**, eaao1642 (2018).
11. Zarfl, C. et al. Future large hydropower dams impact global
freshwater megafauna. _Sci. Rep._ **9**, 18531 (2019).
12. Barbarossa, V. et al. Impacts of current and future large dams on
the geographic range connectivity of freshwater fish worldwide.
_Proc. Natl Acad. Sci. USA_ **117**, 3648–3655 (2020).
13. Dias, M. S. et al. Anthropogenic stressors and riverine fish
extinctions. _Ecol. Indic._ **79**, 37–46 (2017).
14. Tickner, D. et al. Bending the curve of global freshwater
biodiversity loss: an emergency recovery plan. _BioScience_ **70**,
330–342 (2020).
15. Schmitt, R. J., Bizzi, S., Castelletti, A. & Kondolf, G. Improved
trade-offs of hydropower and sand connectivity by strategic dam
planning in the Mekong. _Nat. Sustain._ **1**, 96–104 (2018).
16. Schmitt, R. J. P., Bizzi, S., Castelletti, A., Opperman, J. & Kondolf,
G. M. Planning dam portfolios for low sediment trapping shows
limits for sustainable hydropower in the Mekong. _Sci. Adv._ **5**
(2019).
17. Schmitt, R. J. et al. Strategic basin and delta planning increases
the resilience of the Mekong Delta under future uncertainty. _Proc._
_Natl Acad. Sci. USA_ **118**, e2026127118 (2021).
18. Kondolf, G. et al. Save the Mekong Delta from drowning. _Science_
**376**, 583–585 (2022).
19. Winemiller, K. O. et al. Balancing hydropower and biodiversity in
the Amazon, Congo, and Mekong. _Science_ **351**, 128–129 (2016).
20. Chowdhury, A. K. et al. Hydropower expansion in eco-sensitive
river basins under global energy–economic change. _Nat. Sustain._
**7**, 213–222 (2024).
21. Hertwich, E. G. Addressing biogenic greenhouse gas emissions
from hydropower in LCA. _Environ. Sci. Technol._ **47**, 9604–9611
(2013).
22. Deemer, B. R. et al. Greenhouse gas emissions from reservoir water
surfaces: a new global synthesis. _BioScience_ **66**, 949–964 (2016).
23. Prairie, Y. T. et al. Greenhouse gas emissions from freshwater
reservoirs: what does the atmosphere see? _Ecosystems_ **21**,
1058–1071 (2018).
24. Calamita, E. et al. Unaccounted CO 2 leaks downstream of a large
tropical hydroelectric reservoir. _Proc. Natl Acad. Sci. USA_ **118**,
e2026004118 (2021).
25. Harrison, J. A., Prairie, Y. T., Mercier-Blais, S. & Soued, C. Year2020 global distribution and pathways of reservoir methane
and carbon dioxide emissions according to the greenhouse gas
from reservoirs (G-res) model. _Glob. Biogeochem. Cycles_ **35**,
e2020GB006888 (2021).
26. Soued, C., Harrison, J. A., Mercier-Blais, S. & Prairie, Y. T. Reservoir
CO 2 and CH 4 emissions and their climate impact over the period
1900–2060. _Nat. Geosci._ **15**, 700–705 (2022).
27. Ou, Y. et al. Role of non-CO 2 greenhouse gas emissions in limiting
global warming. _One Earth_ **5**, 1312–1315 (2022).
28. Haegel, N. M. et al. Terawatt-scale photovoltaics: transform global
energy. _Science_ **364**, 836–838 (2019).
29. Veers, P. et al. Grand challenges in the science of wind energy.
_Science_ **366**, eaau2027 (2019).



30. Meng, J., Way, R., Verdolini, E. & Diaz Anadon, L. Comparing
expert elicitation and model-based probabilistic technology cost
forecasts for the energy transition. _Proc. Natl Acad. Sci. USA_ **118**,
e1917165118 (2021).
31. Chowdhury, A. K. et al. Enabling a low-carbon electricity system
for Southern Africa. _Joule_ **6**, 1826–1844 (2022).
32. Carlino, A. et al. Declining cost of renewables and climate change
curb the need for African hydropower expansion. _Science_ **381**,
eadf5848 (2023).
33. Almeida, R. M. et al. Strategic planning of hydropower
development: balancing benefits and socioenvironmental costs.
_Curr. Opin. Environ. Sustain._ **56**, 101175 (2022).
34. Almeida, R. M. et al. Reducing greenhouse gas emissions of
Amazon hydropower with strategic dam planning. _Nat. Commun._
**10**, 4281 (2019).
35. Schmitt, R. J., Kittner, N., Kondolf, G. M. & Kammen, D. M. Joint
strategic energy and river basin planning to reduce dam impacts
on rivers in Myanmar. _Environ. Res. Lett._ **16**, 054054 (2021).
36. Flecker, A. S. et al. Reducing adverse impacts of Amazon
hydropower expansion. _Science_ **375**, 753–760 (2022).
37. Opperman, J. J. et al. Balancing renewable energy and river
resources by moving from individual assessments of hydropower
projects to energy system planning. _Front. Environ. Sci._ **10**, 2410
(2023).
38. Siala, K., Chowdhury, A. K., Dang, T. D. & Galelli, S. Solar energy
and regional coordination as a feasible alternative to large
hydropower in Southeast Asia. _Nat. Commun._ **12**, 4159 (2021).
39. Gonzalez, J. M. et al. Designing diversified renewable energy
systems to balance multisector performance. _Nat. Sustain._ **6**,
415–427 (2023).
40. Neumann, F. & Brown, T. The near-optimal feasible space of a
renewable power system model. _Electr. Power Syst. Res._ **190**,
106690 (2021).
41. Howells, M. et al. OSeMOSYS: the Open Source Energy Modeling
System: an introduction to its ethos, structure and development.
_Energy Policy_ **39**, 5850–5870 (2011).
42. Taliotis, C. et al. An indicative analysis of investment opportunities
in the African electricity supply sector—using TEMBA (The
Electricity Model Base for Africa). _Energy Sustain. Dev._ **31**, 50–66
(2016).
43. Pappis, I. et al. _Energy Projections for African Countries_ (JRC,
2019).
44. Pappis, I. et al. The effects of climate change mitigation strategies
on the energy system of Africa and its associated water footprint.
_Environ. Res. Lett._ **17**, 044048 (2022).
45. Chawanda, C. J., Nkwasa, A., Thiery, W. & van Griensven, A.
Combined impacts of climate and land-use change on future
water resources in Africa. _Hydrol. Earth Syst. Sci._ **28**, 117–138
(2024).
46. Frieler, K. et al. Assessing the impacts of 1.5 C global warming—
simulation protocol of the Inter-Sectoral Impact Model
Intercomparison Project (ISIMIP2b). _Geosci. Model Dev._ **10**,
4321–4345 (2017).
47. Riahi, K. et al. The Shared Socioeconomic Pathways and their
energy, land use, and greenhouse gas emissions implications: an
overview. _Glob. Environ. Change_ **42**, 153–168 (2017).
48. Grill, G., Dallaire, C. O., Chouinard, E. F., Sindorf, N. & Lehner, B.
Development of new indicators to evaluate river fragmentation
and flow regulation at large scales: a case study for the Mekong
River Basin. _Ecol. Indic._ **45**, 148–159 (2014).
49. Grill, G. et al. An index-based framework for assessing patterns
and trends in river fragmentation and flow regulation by global
dams at multiple scales. _Environ. Res. Lett._ **10**, 015001 (2015).
50. Jager, H. I. et al. Getting lost tracking the carbon footprint of
hydropower. _Renew. Sustain. Energy Rev._ **162**, 112408 (2022).



[Nature Sustainability | Volume 7 | July](http://www.nature.com/natsustain) 2024 | 879–890 **888**


**Article** https://doi.org/10.1038/s41893-024-01367-x



51. Grochowicz, A., van Greevenbroek, K., Benth, F. E. & Zeyringer,
M. Intersecting near-optimal spaces: European power systems
with more resilience to weather variability. _Energy Econ._ **106496**,
106496 (2023).
52. Rheinheimer, D. E., Tarroja, B., Rallings, A. M., Willis, A. D. & Viers,
J. H. Hydropower representation in water and energy system
models: a review of divergences and call for reconciliation.
_Environ. Res. Infrastruct. Sustain._ **3**, 012001 (2023).
53. Schmitt, R. J., Kittner, N., Kondolf, G. M. & Kammen, D. M. Deploy
diverse renewables to save tropical rivers. _Nature_ **569**, 330–332
(2019).
54. Hatchard, S., Schmitt, R. J., Pianosi, F., Savage, J. & Bates, P.
Strategic siting and design of dams minimizes impacts on seasonal
floodplain inundation. _Environ. Res. Lett._ **18**, 084011 (2023).
55. Allen, G. H. & Pavelsky, T. M. Global extent of rivers and streams.
_Science_ **361**, 585–588 (2018).
56. Mayer, A., Castro-Diaz, L., Lopez, M. C., Leturcq, G. & Moran, E.
F. Is hydropower worth it? Exploring Amazonian resettlement,
human development and environmental costs with the Belo
Monte project in Brazil. _Energy Res. Soc. Sci._ **78**, 102129 (2021).
57. Trotter, P. A., Maconachie, R. & McManus, M. C. Solar energy’s
potential to mitigate political risks: the case of an optimised
Africa-wide network. _Energy Policy_ **117**, 108–126 (2018).
58. Sterl, S. et al. Smart renewable electricity portfolios in West
Africa. _Nat. Sustain._ **3**, 710–719 (2020).
59. Basheer, M. et al. Cooperative adaptive management of the Nile
River with climate and socio-economic uncertainties. _Nat. Clim._

_Change_ **13**, 48–57 (2023).
60. Arnold, W., Salazar, J. Z., Carlino, A., Giuliani, M. & Castelletti, A.
Operations eclipse sequencing in multipurpose dam planning.
_Earth’s Future_ **11**, e2022EF003186 (2023).
61. Liu, Z. & He, X. Balancing-oriented hydropower operation makes
the clean energy transition more affordable and simultaneously
boosts water security. _Nat. Water_ **1**, 778–789 (2023).
62. Brown, C., Ghile, Y., Laverty, M. & Li, K. Decision scaling: linking
bottom-up vulnerability analysis with climate projections in the
water sector. _Water Resour. Res._ **48** (2012).
63. Schmitt, R. J., Rosa, L. & Daily, G. C. Global expansion of
sustainable irrigation limited by water storage. _Proc. Natl Acad._
_Sci. USA_ **119**, e2214291119 (2022).
64. Conway, D., Dalin, C., Landman, W. A. & Osborn, T. J. Hydropower
plans in Eastern and Southern Africa increase risk of concurrent
climate-related electricity supply disruption. _Nat. Energy_ **2**,
946–953 (2017).
65. Wu, G. C. et al. Strategic siting and regional grid interconnections
key to low-carbon futures in African countries. _Proc. Natl Acad._
_Sci. USA_ **114**, E3004–E3012 (2017).
66. Wu, G. C. et al. Avoiding ecosystem and social impacts of
hydropower, wind, and solar in Southern Africa’s low-carbon
electricity system. _Nat. Commun._ **15**, 1083 (2024).
67. Sovacool, B. K., Gilbert, A. & Nugent, D. An international
comparative assessment of construction cost overruns for
electricity infrastructure. _Energy Res. Soc. Sci._ **3**, 152–160 (2014).
68. Deshmukh, R., Mileva, A. & Wu, G. Renewable energy alternatives
to mega hydropower: a case study of Inga 3 for Southern Africa.
_Environ. Res. Lett._ **13**, 064020 (2018).
69. Barnes, T., Shivakumar, A., Brinkerink, M. & Niet, T. OSeMOSYS
Global, an open-source, open data global electricity system
model generator. _Sci. Data_ **9**, 623 (2022).
70. Lehner, B. & Grill, G. Global river hydrography and network
routing: baseline data and new approaches to study the world’s
large river systems. _Hydrol. Process._ **27**, 2171–2186 (2013).
71. Allen, P. M., Arnold, J. C. & Byars, B. W. Downstream channel
geometry for use in planning-level models 1. _J. Am. Water Resour._
_Assoc._ **30**, 663–671 (1994).



72. _QGIS Geographic Information System_ (QGIS Association, 2024).
73. Hagberg, A., Swart, P. & Schult, D. _Exploring Network Structure,_
_Dynamics, and Function Using NetworkX_ (OSTI, 2008).
74. _Life Cycle Assessment Harmonization_ [(NREL, 2021); https://www.](https://www.nrel.gov/analysis/life-cycle-assessment.html)
[nrel.gov/analysis/life-cycle-assessment.html](https://www.nrel.gov/analysis/life-cycle-assessment.html)
75. O’Connor, P. et al. _Hydropower Vision: A New Chapter for_
_America’s 1st Renewable Electricity Source_ (US Department of
Energy, 2016).
76. Pehl, M. et al. Understanding future emissions from low-carbon
power systems by integration of life-cycle assessment and
integrated energy modelling. _Nat. Energy_ **2**, 939–945 (2017).
77. Lehner, B. et al. High-resolution mapping of the world’s reservoirs
and dams for sustainable river-flow management. _Front. Ecol._
_Environ._ **9**, 494–502 (2011).
78. Bartos, M. pysheds: simple and fast watershed delineation in
Python. _GitHub_ [https://github.com/mdbartos/pysheds (2020).](https://github.com/mdbartos/pysheds)
79. Prairie, Y. T. et al. A new modelling framework to assess biogenic
GHG emissions from reservoirs: the G-res tool. _Environ. Model._

_Softw._ **143**, 105117 (2021).
80. Hadka, D. & Reed, P. Borg: an auto-adaptive many-objective
evolutionary computing framework. _Evol. Comput._ **21**, 231–259
(2013).
81. Pappis, I., Sridharan, V., Usher, W. & Howells, M. JRC-TEMBA—
African decarbonisation pathways. _Zenodo_ [https://doi.org/](https://doi.org/10.5281/zenodo.3521841)
[10.5281/zenodo.3521841 (2019).](https://doi.org/10.5281/zenodo.3521841)
82. Carlino, A. Data in support of ‘Declining cost of renewables and
climate change curb the need for African hydropower expansion’.
_Zenodo_ [https://doi.org/10.5281/zenodo.7931050 (2022).](https://doi.org/10.5281/zenodo.7931050)
83. Carlino, A., Schmitt, R., Clark, A. & Castelletti, A. Data and code in
support of ‘Rethinking energy planning to mitigate environmental
and climatic impacts of future African hydropower’. _Zenodo_
[https://doi.org/10.5281/zenodo.8360437 (2023).](https://doi.org/10.5281/zenodo.8360437)


**Acknowledgements**
A. Carlino acknowledges support from the European Union’s Horizon
2020 Research and Innovation programme GEOCEP under Marie
Sklodowska-Curie grant agreement no. 870245. A. Carlino was
partially funded by the European Union’s Horizon 2020 Research and
Innovation Actions programme under the project SOS-WATER (grant
agreement no. 101059264). A. Castelletti was partially funded by the
European Union’s Horizon 2020 Research and Innovation programme
under the GoNEXUS project (grant agreement no. 101003722).


**Author contributions**

A. Carlino, R.S. and A. Castelletti conceptualized the project.
A. Castelletti acquired the funding. A. Carlino and A. Clark curated
the data. A. Carlino and A. Clark conducted the formal analyses.
A. Carlino, R.S., A. Clark and A. Castelletti devised the methodology.
A. Carlino, R.S. and A. Clark visualized the data. A. Carlino prepared
the original paper draft. A. Carlino, R.S., A. Clark and A. Castelletti
reviewed and edited the paper.


**Competing interests**
The authors declare no competing interests.


**Additional information**

**Extended data** [is available for this paper at https://doi.org/10.1038/](https://doi.org/10.1038/s41893-024-01367-x)

[s41893-024-01367-x.](https://doi.org/10.1038/s41893-024-01367-x)


**Supplementary information** The online version contains
[supplementary material available at https://doi.org/10.1038/s41893-](https://doi.org/10.1038/s41893-024-01367-x)

[024-01367-x.](https://doi.org/10.1038/s41893-024-01367-x)


**Correspondence and requests for materials** should be addressed to

Andrea Castelletti.



[Nature Sustainability | Volume 7 | July](http://www.nature.com/natsustain) 2024 | 879–890 **889**


**Article** https://doi.org/10.1038/s41893-024-01367-x



**Peer review information** _Nature Sustainability_ thanks Mohammed
Basheer and the other, anonymous, reviewer(s) for their contribution
to the peer review of this work.


**Reprints and permissions information** is available at
[www.nature.com/reprints.](http://www.nature.com/reprints)


**Publisher’s note** Springer Nature remains neutral with regard to
jurisdictional claims in published maps and institutional affiliations.



Springer Nature or its licensor (e.g. a society or other partner)
holds exclusive rights to this article under a publishing agreement
with the author(s) or other rightsholder(s); author self-archiving
of the accepted manuscript version of this article is solely
governed by the terms of such publishing agreement and
applicable law.


© The Author(s), under exclusive licence to Springer Nature Limited

2024



[Nature Sustainability | Volume 7 | July](http://www.nature.com/natsustain) 2024 | 879–890 **890**


**Article** https://doi.org/10.1038/s41893-024-01367-x

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/carlinoRethinkingEnergyPlanning2024/carlinoRethinkingEnergyPlanning2024.pdf-12-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/carlinoRethinkingEnergyPlanning2024/carlinoRethinkingEnergyPlanning2024.pdf-12-1.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/carlinoRethinkingEnergyPlanning2024/carlinoRethinkingEnergyPlanning2024.pdf-12-2.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/carlinoRethinkingEnergyPlanning2024/carlinoRethinkingEnergyPlanning2024.pdf-12-3.png)


**Extended Data Fig. 1 | Classification of proposed hydropower projects under the approaches considered.** We report the classification of individual hydropower
projects (always selected, selected at least once, and never selected) under the energy system planning ( **a** ), strategic dam planning ( **b** ), and after the integration ( **c** ).


[Nature Sustainability](http://www.nature.com/natsustain)


Corresponding author(s): Andrea Castelletti


Last updated by author(s): Apr 23, 2024

## Reporting Summary


Nature Portfolio wishes to improve the reproducibility of the work that we publish. This form provides structure for consistency and transparency
in reporting. For further information on Nature Portfolio policies, see our Editorial Policies and the Editorial Policy Checklist.

##### Statistics


For all statistical analyses, confirm that the following items are present in the figure legend, table legend, main text, or Methods section.


n/a Confirmed


The exact sample size ( _n_ ) for each experimental group/condition, given as a discrete number and unit of measurement


A statement on whether measurements were taken from distinct samples or whether the same sample was measured repeatedly


The statistical test(s) used AND whether they are one- or two-sided
_Only common tests should be described solely by name; describe more complex techniques in the Methods section._


A description of all covariates tested


A description of any assumptions or corrections, such as tests of normality and adjustment for multiple comparisons


A full description of the statistical parameters including central tendency (e.g. means) or other basic estimates (e.g. regression coefficient)
AND variation (e.g. standard deviation) or associated estimates of uncertainty (e.g. confidence intervals)


For null hypothesis testing, the test statistic (e.g. _F_, _t_, _r_ ) with confidence intervals, effect sizes, degrees of freedom and _P_ value noted

_Give P values as exact values whenever suitable._


For Bayesian analysis, information on the choice of priors and Markov chain Monte Carlo settings


For hierarchical and complex designs, identification of the appropriate level for tests and full reporting of outcomes


Estimates of effect sizes (e.g. Cohen's _d_, Pearson's _r_ ), indicating how they were calculated


_Our web collection on_ _statistics for biologists contains articles on many of the points above._

|/a|Co|
|---|---|
|||
|||
|||
|||
|||
|||
|||
|||
|||
|||
|||
|||
|||
|||
|||
|||
|||
|||
|||
|||


##### Software and code


Policy information about availability of computer code


Data collection All the data and processing scripts to reproduce the results and figures are available on Zenodo (https://doi.org/10.5281/zenodo.8360437).


Data analysis All the data and processing scripts to reproduce the results and figures are available on Zenodo (https://doi.org/10.5281/zenodo.8360437). To
solve the mixed integer linear program, we use IBM ILOG CPLEX Optimization Studio 12.10.0. To solve the multi-objective optimization
problem, we use the multi-objective evolutionary algorithm Borg (http://borgmoea.org/).


For manuscripts utilizing custom algorithms or software that are central to the research but not yet described in published literature, software must be made available to editors and
reviewers. We strongly encourage code deposition in a community repository (e.g. GitHub). See the Nature Portfolio guidelines for submitting code & software for further information.

##### Data


Policy information about availability of data


All manuscripts must include a data availability statement. This statement should provide the following information, where applicable:


   - Accession codes, unique identifiers, or web links for publicly available datasets

   - A description of any restrictions on data availability

   - For clinical datasets or third party data, please ensure that the statement adheres to our policy


The OSeMOSYS-TEMBA energy system model data is available on Zenodo (https://zenodo.org/record/3521841) as its modified version including power plant data
for more than 600 hydropower projects from the African Hydropower Atlas (https://zenodo.org/record/7931050). The African Hydropower Atlas contains technical



1


~~information about the existing and future hydropower projects and is available online (https://www.hydroshare.org/~~
resource/5e8ebdc3bfd24207852539ecf219d915/). The GRAND database containing information about existing dams worldwide is available thanks to the Global
Dam Watch project (https://www.globaldamwatch.org/grand). Emission Factors are available from the National Renewable Energy Laboratory (https://
data.nrel.gov/submissions/171). The Shared Socioeconomic Pathways report socioeconomic projections using Integrated Assessment Models and are available
online (https://tntcat.iiasa.ac.at/SspDb/dsd?Action=htmlpage&page=welcome). Figs. 3, S3, S4, and Extended Data Fig. 1 incorporate data from the HydroSHEDS
version 1 database which is © World Wildlife Fund, Inc. (2006-2022) and has been used herein under license. WWF has not evaluated the data as altered and
incorporated within these figures, and therefore gives no warranty regarding its accuracy, completeness, currency or suitability for any particular purpose. Portions
of the HydroSHEDS v1 database incorporate data which are the intellectual property rights of © USGS (2006-2008), NASA (2000-2005), ESRI (1992-1998), CIAT
(2004-2006), UNEP-WCMC (1993), WWF (2004), Commonwealth of Australia (2007), and Her Royal Majesty and the British Crown and are used under license. The
HydroSHEDS v1 database and more information are available (https://www.hydrosheds.org).

##### Human research participants


Policy information about studies involving human research participants and Sex and Gender in Research.


Reporting on sex and gender No human research participants were involved in this study.


Population characteristics n/a


Recruitment n/a


Ethics oversight n/a


Note that full information on the approval of the study protocol must also be provided in the manuscript.

### - Field specific reporting


Please select the one below that is the best fit for your research. If you are not sure, read the appropriate sections before making your selection.


Life sciences Behavioural & social sciences Ecological, evolutionary & environmental sciences


For a reference copy of the document with all sections, see nature.com/documents/nr-reporting-summary-flat.pdf

### Ecological, evolutionary & environmental sciences study design


All studies must disclose on these points even when the disclosure is negative.


Study description The study couples energy and environmental system modelling with multi-objective optimization to examine how different
hydropower expansion strategies in Africa tradeoff impacts on river fragmentation, greenhouse gas emissions and technoeconomic

metrics.


Research sample The energy system model data is based on the OSeMOSYS-TEMBA model. The data for the hydropower projects is obtained from the
African Hydropower Atlas. The river network is obtained from the HydroRIVERS dataset. Socioeconomic projections are obtained
from the SSP scenarios.


Sampling strategy No sample strategy was used as all the data available online were used.


Data collection Data were collected from the different databases available online.


Timing and spatial scale We model the energy system model at a seasonal scale dividing time slices into day and night for the period 2020-2050. The energy
system model considers country-level energy system. Additionally, the hydropower projects are considered individually. In the multiobjective problem, we model each river basin on which a hydropower project is currently located or proposed and we model the
impact of each hydropower project in terms of river fragmentation and reservoir greenhouse gas emissions.


Data exclusions No data were excluded.


Reproducibility The results are reproducible using the code available on Zenodo. We have reproduced results and showed their robustness during
peer review.


Randomization The multi-objective problem is solved using an evolutionary algorithm. For this reason, we examine the results for five different

random seeds.


Blinding Blinding was not relevant to our study as we focus on the quantification of technoeconomic and environmental impacts.


Did the study involve field work? Yes No



2


### Reporting for specific materials, systems and methods

We require information from authors about some types of materials, experimental systems and methods used in many studies. Here, indicate whether each material,
system or method listed is relevant to your study. If you are not sure if a list item applies to your research, read the appropriate section before selecting a response.



Materials & experimental systems



Methods


|n/a|Col2|Involved in the study<br>ChIP-seq<br>Flow cytometry<br>MRI-based neuroimaging|
|---|---|---|
|n/a|||


|n/a|Col2|Involved in the study<br>Antibodies<br>Eukaryotic cell lines<br>Palaeontology and archaeology<br>Animals and other organisms<br>Clinical data<br>Dual use research of concern|
|---|---|---|
|n/a|||



3


