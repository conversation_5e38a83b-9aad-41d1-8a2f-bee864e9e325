# Citation Key: mcdonaldSequentialDecisionmakingUncertainty2024

---

# iScience Article

#### Sequential decision-making under uncertainty for long-term energy transition planning



Graphical abstract

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/mcdonaldSequentialDecisionmakingUncertainty2024/mcdonaldSequentialDecisionmakingUncertainty2024.pdf-0-0.png)


Highlights


d We develop a stochastic programming energy system model

to account for uncertainty


d We evaluate the quality of transition pathways obtained with 4

different methods


d Accounting for sequential decisions is important and leads to

lower average costs


d Accounting for uncertainty leads to a more diverse

technology mix



Authors


<PERSON>,

<PERSON><PERSON>


Correspondence

[<EMAIL>](mailto:<EMAIL>)


In brief


Computer science; Engineering; Energy

systems



McDonald & Maravelias, 2024, iScience 27, 111288
December 20, 2024 ª 2024 The Authors. Published by Elsevier Inc.
###### https://doi.org/10.1016/j.isci.2024.111288 ll


## **iScience**

Article
### Sequential decision-making under uncertainty for long-term energy transition planning


<PERSON> [1] and <PERSON><PERSON> [1,2,3,] 1 Chemical and Biological Engineering, Princeton University, 50 Olden St, Princeton, NJ 08540, USA
2 Andlinger Center for Energy and the Environment, Princeton University, 86 Olden St, Princeton, NJ 08540, USA
3 Lead contact

[*Correspondence: <EMAIL>](mailto:<EMAIL>)
[https://doi.org/10.1016/j.isci.2024.111288](https://doi.org/10.1016/j.isci.2024.111288)


SUMMARY


###### **ll**

OPEN ACCESS



Global warming concerns have led to emission regulations and various incentives for low-carbon technologies. Energy system models, which are used to examine how investments affect our ability to meet energy
demand, are typically based on two assumptions: key parameters are assumed to be known deterministically
and a multi-period energy transition plan is determined at one point in time. We argue that for a systematic
generation and analysis of energy transition pathways, these assumptions should be relaxed and, accordingly, we propose methods to achieve that. First, we use stochastic programming (SP) to account for uncertainty in key parameters. Second, we pair SP with a sequential decision-making approach that represents
how decisions can be updated as uncertainties unfold. Third, we use simulation-based methods to evaluate
the quality of energy transitions. Importantly, we find that accounting for uncertainty, proactively and through
feedback, yields pathways with diverse technology portfolios that are resilient to uncertainty.



INTRODUCTION


Growing concern for the effects of greenhouse gas (GHG)
emissions has led governments to incentivize emission reduction around the world. [1][,][2] For example, the United States (US)
announced a target to reduce GHG emissions by 50–52% by
2030. [3] While emission reduction incentives have created an

opportunity to further develop low-carbon technologies, there
are many open questions regarding the type and timing of investments as well as the technologies that should be phased
out. Energy system models, which generate multi-period investment plans to meet a set of goals while satisfying given
constraints, have been used to address some of the aforementioned questions and help policy makers understand
how different regulations and incentives might affect the
development of the energy system. [4–7] Energy system models
can also be used to study how renewable or low-carbon technologies can efficiently be integrated into the existing infrastructure, which is vital to reduce carbon emissions. [8–12] Net
Zero America (NZA) is a comprehensive study that lays out
5 different pathways for the US to reach net-zero carbon
emissions by 2050. Each pathway shows how energy demands will be met while phasing out fossil fuels from all energy sectors. [13] Pickering et al. studied the European energy
system and showed that there are many different energy transition pathways that can be followed to eliminate fossil fuels
while remaining within 10% of the lowest cost solution. [14]

Both these studies emphasize that the results from energy
system models are important in guiding decisions, but there
are multiple pathways to reach the same goals.



Formulating energy system models accounting for uncertainty
can be challenging and often leads to computationally expensive
models. Thus, the most common way to study the impact of uncertainty is through sensitivity or scenario analysis, where a
model is solved multiple times with different parameters. [13][,][15–17]

This method offers insights into how decisions change in the
presence of uncertainty, but it does not permit making a unique
set of decisions today, which is something that has to happen in
a realistic setting. In terms of timing of decision-making, many
energy system models generate energy transition pathways
based on a single model run, that is, the transition, over decades,
is predicted at the beginning of the planning horizon considered.

These solutions are not used as the exact investments that need

to be made, but show projections of investments under different
assumptions. However, this approach does not represent how
decisions are made in practice, where the actors can re-evaluate
past choices and make new decisions based on new information. The decision-making approach can affect the investment
decisions made under different assumptions. While there has
been some research into how sequential decision-making can
be applied to energy system modeling, these approaches focus
on the effect of shortened foresight and are deterministic. [18–22]

These studies discuss how shortened foresight from the application of a sequential decision-making approach can lead to
higher costs and emissions since the full horizon is not observed.
While it is true that having longer foresight leads to a lower
cost solution, the findings of these studies assume that the
information about the future is deterministically known. If key parameters are uncertain (e.g., emission reduction incentives), then
assuming perfect foresight can have a significant impact on the



iScience 27, 111288, December 20, 2024 ª 2024 The Authors. Published by Elsevier Inc. 1
[This is an open access article under the CC BY-NC-ND license (http://creativecommons.org/licenses/by-nc-nd/4.0/).](http://creativecommons.org/licenses/by-nc-nd/4.0/)


###### **ll iScience**

OPEN ACCESS Article



cost of the system. In that setting, having the ability to reoptimize
can help to reduce costs.
Accordingly, the goal of this paper is to develop a stochastic
programming (SP) model, which accounts for uncertainty, and
apply it in a sequential manner, which better represents realworld decision-making, to study the US energy system. We
also use a simulation-based method to systematically assess
the quality of solutions we obtain using different methods,
providing what the authors believe to be the first comparison
of these different solution methods as well as critical insights
into the quality and applicability of results obtained using energy
system models. Our results show that adopting sequential decision-making while explicitly accounting for uncertainty leads to
lower average costs and energy transition pathways that include
a wider range of technologies.


Methods

System under review
We study the US energy sector over a horizon divided into 5-year
time periods using real data. The system includes 7 regions, 21
technologies, and 21 components. A capacity expansion model
is developed to meet energy demand over the time horizon.
We develop a deterministic (perfect foresight) model and a
multi-stage stochastic programming (MSSP) model that considers uncertainty in demand, capital cost of select renewable
technologies, and policy related to carbon emissions. Uncertainty in demand is considered through the fraction of demand
met by liquid fuels or electricity, that is, the total time-varying energy demand does not change across scenarios. The STAR
Methods describe the considered system in detail.
Stochastic programming
In an MSSP model, uncertainty is observed successively through
multiple stages, [23][,][24] where stages can be distinct from time periods, and decisions are also partitioned into stages. An MSSP
model consists of first stage decisions, which are made before
any uncertainty is observed, followed by decisions made in subsequent stages (i.e., second-, third-, etc. stage decisions) as uncertainty is observed. Importantly, the first-stage decisions are
identical across all scenarios which means that, unlike the results
of sensitivity analysis studies, they can be readily implemented.
It is assumed that the probability distribution of the uncertain parameters is known and that a continuous probability distribution
can be approximated by a discrete distribution. A scenario tree is
used to represent all outcomes of the uncertain parameters,
where a scenario corresponds to a path from the root node to
a leaf node. We use the value of stochastic solution (VSS) as a
metric of the benefit of applying SP.
Sequential decision-making
To better represent realistic decision-making which occurs
sequentially, we adopt a rolling horizon approach, where (1) an
optimization (deterministic or stochastic) model is solved to
determine a solution for the entire horizon; (2) the decisions of
the first time period are implemented (fixed); (3) uncertainty in
the relevant parameters is observed, defining the current system
state (which can be different from the predicted state); and (4) a
new optimization model is formulated and solved, at the second
time point. The process is repeated until an implemented (closed
loop) solution is generated for the entire horizon of interest. [25][,][26] A


2 iScience 27, 111288, December 20, 2024



more detailed description of the approach can be found in the
STAR Methods. A simulation-based method is then paired with
both the forward looking and rolling horizon approaches to
show how the generated pathways fare in the face of uncertainty.
When the simulation-based method is applied with the rolling
horizon approach, randomly generated parameter values are
used to represent uncertainty observation between iterations.
The parameters values are sampled from the distributions that
were used to generate the mean values used in the deterministic

model and the outcomes for the scenarios in the stochastic

model.

Notation

The following notation is used when referencing the different
models, approaches, solutions, and optimal objective function
values. We use D and S to refer to the deterministic and

stochastic model, respectively. The forward-looking and rolling
horizon approaches are referenced with F and R respectively.
Combining a model and an approach defines a method, which
is represented using the corresponding model and approach
letters; e.g., DR refers to the application of the deterministic
model solved following the rolling horizon approach. A solution
to a method is represented by x [method] and the optimal objective
function value (total cost of the system) is represented by f [method] ;
e.g., the solution of DR is x [DR] and the optimal objective function
value is f [DR] . The average solution and optimal objective function
value across multiple simulations are denoted with a hat; e.g.,
the average solution of DR is bx [DR] and the average optimal
objective function value is f [b] [DR] . The average solution refers to
the weighted decisions, made using the different methods,
across 100 different uncertainty realizations.


RESULTS


Forward looking solutions
Figure 1A shows, at high level, the energy transition pathway
generated using DF, which is the approach followed in most
similar studies in the literature. The pathways corresponding to
three scenarios used in SF are shown in Figure 1 panels C–E.
The three scenarios are different only in the outcomes of demand, that is, the outcomes in all other parameters are the
same. The electricity demand levels in the three scenarios are
the highest (C)/mid-level (D)/lowest (E), as shown in Figure S4.
Note that the solutions for all scenarios used in S have the

same first stage capacity decisions. Also, given that there is a
one period delay between the decision to invest and the
establishment of new capacity and that there is no uncertainty
observation through period three, the impact of the first stage
decisions manifests between 2025 and 2035, which are the
two periods included in the black box in Figure 1.
First, we observe that x [SF] has different initial capacity decisions than x [DF], with the ‘‘difference’’ between the two highlighted
in Figure 1B. The difference shown is the difference in investment
decisions, therefore the y axis is at a smaller scale for Figure 1B
compared to Figures 1A, 1C–1E, which show the total installed
capacity. Notably, the investments into renewable and low
carbon technologies in the x [SF] solution are 11% higher than
the corresponding investments in x [DF] . Specifically, x [SF] includes
higher investments into Fischer Tropsch biofuels, natural gas


###### **iScience**

Article


###### **ll**

OPEN ACCESS



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/mcdonaldSequentialDecisionmakingUncertainty2024/mcdonaldSequentialDecisionmakingUncertainty2024.pdf-3-2.png)

Figure 1. Energy transition pathways generated by DF and SF
(A) Energy transition pathway of DF; total cost = $12.20 Trillion.
(B) Difference in decisions between x [DF] and first stage decisions of x [SF] .
(C–E) Energy transition pathway of the scenario with the highest (C)/mid-level (D)/lowest (E) level of electricity demand; total cost = $18.24 (C)/$12.83 (D)/$12.23
(E) Trillion. The expected cost across all scenarios of SF is $13.76 Trillion. Each technology type (intermediate, power, liquid fuel) are separated with a black line.
Within each technology type the technologies are organized from least carbon emissions to most carbon emissions. Abbreviations used in all figures: NG: natural
gas; w/CC: with carbon capture; RWGSFTS: reverse water-gas shift combined with Fischer Tropsch; BECCS: bioethanol with carbon capture and storage; FT:
Fischer Tropsch. The data for all figures can be found in the supplementary information.



(with and without carbon capture), hydroelectric, petroleum, and
biomass power plants. This is because the stochastic model
sees scenarios with higher electricity and liquid fuel demands
and prepares for them via early investments in renewables
and gradual reduction in the capacities of high carbon technologies (e.g., oil refineries and natural gas combined cycle power
plants).
Second, as expected, the scenario with the highest electricity
demand has the most electricity generating technologies at the
end of the time horizon (panel C), while the scenario with the
lowest electricity demand (or highest liquid fuel demand), has
more liquid fuel technologies. However, importantly, there is still
more investment into electricity-generating technologies in the
low electricity demand scenario (Figure 1E) compared to the investments in x [DF] in which electricity demand was higher. This is
because the first stage decisions in the MSSP model account for
the outcome of all scenarios, thereby leading to first stage (early)
decisions that make the solution robust to a range of uncertain
futures.

To test the hypothesis that it is beneficial to use MSSP, we
calculate the VSS, which is a measure typically used in the literature, using f [SF] and the expected result of using the solution of D.
The VSS is found to be $504 billion, suggesting that there is
indeed a benefit to accounting for uncertainty.



Sequential decision-making solutions
When we apply a rolling horizon approach, to represent the
more realistic process of sequential decision-making, we
observe small differences in capacity investments compared
to the forward-looking approach. The energy transition pathways generated using DF and DR are, in general, similar, as
shown in Figure 2. The x [DR] pathway (Figure 2C) is similar to
the x [DF] pathway (Figure 2A) because the data used to obtain
the former are the same as in the forward-looking approach.
However, there are small differences, seen in Figure 2B,
because the investments made toward the end of the horizon

are the result of models that account for new information
(which was not considered in x [DR] ). There is less than a 2% difference in the total investment decisions in each time period.
f [DR] is 1% higher than f [DF] due to the slight increase in investments that occurs at the end of the time horizon. This agrees
with previous research that shows that shortened foresight
can lead to higher costs. [18–22] Figure 2E shows x [SR] which, as
a reminder, was generated by sequentially solving an MSSP
model. Interestingly, the pathway lies within the range of pathways corresponding to the three scenarios of SF shown in
Figures 1C–1E. This is because SR generates a single energy
transition pathway, even though an MSSP model is considered in each iteration, using the mean values of the uncertain


iScience 27, 111288, December 20, 2024 3


###### **ll iScience**

OPEN ACCESS Article


Figure 2. Energy transition pathways generated using the DF, DR, and SR
(A) Energy transition pathway of DF; total cost =
$12.19 Trillion.
(B) Difference between x [DF] and x [DR] .
(C) Energy transition pathway of DR; total cost =
$12.32 Trillion.
(D) Difference between x [SR] and x [DR] .
(E) Energy transition pathway of SR; total cost =
$12.83 Trillion. Assumptions used to generate these
results are given in the STAR Methods section. Each
technology type (intermediate, power, liquid fuel) are
separated with a black line. Within each technology
type the technologies are organized from least carbon

emissions to most carbon emissions.


the first stage decisions, which are used to
generate the pathway shown in Figure 2E.
As a result, a more diverse technology
mixture is obtained in x [SR] .



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/mcdonaldSequentialDecisionmakingUncertainty2024/mcdonaldSequentialDecisionmakingUncertainty2024.pdf-4-2.png)

parameters in each iteration, while SF generates an energy
transition pathway for each scenario.
Overall, SR yields a pathway that is more conservative
than the pathways corresponding to the high or low electricity
demand scenarios of SF and is similar, in total installed
capacity, to the pathway obtained by DR, though some investment choices are different since uncertainty is considered
in each iteration. Figure 2D shows the differences in investment decisions between x [SR] and x [DR] . The most notable differ
ence is that there is increased investment into electricity
generating technologies and oil refineries in the later time
periods of x [SR] . Again, this is because the solutions generated
using a stochastic model are more robust. This is true even for


4 iScience 27, 111288, December 20, 2024



Energy transition pathway analysis
under uncertainty
The results using the rolling horizon
approach were obtained assuming that the
expected values used in the deterministic

model will indeed be observed in each

iteration. This assumption, which is predominantly made in the literature, leads to a
DR-generated energy transition pathway,
which is similar to the DF-generated
pathway with very similar costs (f [DF] is 1%
lower than f [DR] ). This observation is in agreement with the results of papers that have
studied the effect of sequential decision
making. [18–22] However, this is a strong
assumption and so, we argue, that a more
interesting and relevant question is how
the pathways generated by the various

methods would fare in the face of

uncertainty. To address this question, we
implement the rolling horizon approach
using the simulation-based method described in the Methods section. To get statistically significant results, we apply the same
approach 100 times and calculate average costs, denoted using

a hat.

Next, we compare the quality of solutions obtained using the
four different methods we have discussed, evaluated using a
single future (uncertainty realization); and the set of 100 different
uncertainty realizations. The first four yield a single total cost,
whereas the latter four yield cost distributions, as shown in
Figure 3. The left panel of Figure 3 shows the costs obtained
by the approaches that yield a single (forward looking) solution;
and the right panel shows the costs obtained using the rolling
horizon approach.
We start our discussion with the results based on a single
solution. We observe the following.


###### **iScience**

Article

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/mcdonaldSequentialDecisionmakingUncertainty2024/mcdonaldSequentialDecisionmakingUncertainty2024.pdf-5-2.png)


(1) Unsurprisingly, when the parameters we observe are the
same as the ones used in the deterministic model, then
the cost is the lowest (f [DF] ).
(2) The cost of the SF method (f [SF] ) is higher because the SF
solution has to account for multiple uncertainty realizations, which leads to larger early investments into a wider
set of technologies.
(3) The picture reverses when the obtained solutions are
evaluated under multiple future outcomes. The average
cost of the DF method (f [b] [DF] ) increases dramatically while
the cost of the SF method (f [b] [SF] ) stays almost the same;
because the uncertainty represented via the 100 realizations, was already accounted for via scenarios.
(4) Importantly, f [b] [SF] < f [b] [DF] and f [b] [SF] has a smaller standard deviation than f [b] [DF] .


These results suggest that the insights obtained using solutions
generated at the beginning of the planning horizon have limited
use. The costs will change significantly when the realization of uncertainty differs from the expected values used to calculate the
initial solution. On the contrary, accounting for uncertainty, even
proactively, has important benefits because meeting demand (at
higher probability) outweighs the cost of additional investments.
Next, we turn our attention to the more realistic setting where
the decision maker reacts to uncertainties (see costs in the right
panel of Figure 3). We observe the following.


(1) When DR is evaluated using the average values of the uncertain parameters, the calculated cost (f [DR] ) is the lowest,
and very similar to f [DF] .
(2) When reality is not exactly as predicted, the average cost
(f [b] [DR] ) of the pathways generated using DR increases,
again, significantly. However, we note that f [b] [DR] < f [SF] which
suggests that having the ability to react to uncertainty, as
opposed to account for it, is advantageous.
(3) Employing a stochastic model in each iteration leads to
higher cost (f [DR] < f [SR] ) because larger investments are
made, as seen in Figure 2. Note that f [SR] is significantly
smaller than f [SF] which, again, highlights the importance
of sequential decision-making.
(4) Accounting for uncertainty and reacting to it, leads to the
lowest average cost: f [b] [SR] is 3% lower than f [b] [DR] and has a

smaller standard deviation.


###### **ll**

OPEN ACCESS


Figure 3. Total costs of DF, SF, DR, and SR
Costs when the methods are evaluated under a single
future are shown as single points. The dash and error
bars represent the mean ± SD when we consider 100
different uncertainty realizations. Costs are shown in
red (green) for the deterministic (stochastic) models.


The previous analysis suggests that being
able to reoptimize as uncertainty is observed
and new information becomes available is

critical. Overall, the best method to obtain
the lowest cost solution is to pair MSSP
with the rolling horizon approach, demonstrated by f [b] [SR] having the lowest average
and most consistent costs. Our analysis also shows that the
evaluation of the pathways should be performed considering
different uncertainty realizations. Most notably, methods that
appear to lead to high-cost solutions, when evaluated under a
single outcome, are, in fact, the methods that generate not
only more realistic but also better, on average, energy transition
pathways.


DISCUSSION


The proposed methods can be used to address numerous interesting questions. In this section, we present, briefly due to space
limitations, how net zero emission constraints impact energy
transition pathways as well as how to address deep uncertainty.


Net zero emissions

Given the findings in the previous section, we focus on the
insights obtained when methods are evaluated under multiple
uncertainty realizations. The analysis so far considers credits
for producing renewable electricity or capturing carbon but

does not consider carbon emission constraints. We discuss

how the investment choices change once we add a constraint
enforcing net zero emissions by 2050. Interestingly, the insights

we derive are similar to those based on credits. As seen in

Figure 4A, the average costs of the methods employing S are
lower and more consistent than the costs generated using their
deterministic counterparts (f [b] [SF] < f [b] [DF], f [b] [SR] < f [b] [DR] ). Also, f [b] [SR] is
comparable to f [b] [SF], but the standard deviation is still less than
all other methods because SR leads to larger and more diverse
investments. Also, f [b] [DR] is smaller than f [b] [DF] by $344 billion, emphasizing that having the ability to reoptimize can have a significant
benefit. As expected, the average carbon emissions, denoted byEb [method], by the end of 2050 are 0 MMT CO 2 . When a rolling
horizon approach is used, the standard deviation in emissions
is smaller than when the forward-looking approach is used,
most clearly seen when we compare E [b] [SF] with E [b] [SR] in Figure 4B.
The average emissions of DF (E [b] [DF] ) are lower than the average
emissions of SF (E [b] [SF] ) because the emissions for the least cost
scenario for each of the 100 different uncertainty realizations
does not mean the emissions prior to 2050 will be lower.
Although the average cost is less than 1% lower for SF, SR

has more consistent costs and emissions.


iScience 27, 111288, December 20, 2024 5


###### **ll iScience**

OPEN ACCESS Article

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/mcdonaldSequentialDecisionmakingUncertainty2024/mcdonaldSequentialDecisionmakingUncertainty2024.pdf-6-2.png)


Figure 4. Total costs and carbon emissions of pathways generated by different methods under net zero constraints based on 100 realizations

(A) The mean ± SD of total cost.
(B) The mean ± SD of total emissions; E [b] [method] denotes the average carbon emissions for a given method (e.g., E [b] [DF] is the average carbon emissions when model D
is solved with the forward-looking approach). Costs and emissions are shown in red (green) for the deterministic (stochastic) models.



Figure 5 shows the average energy transition pathway for DR
and SR with and without emissions constraints. As expected, in
Figures 5B and 5E, we see (1) more renewable and low carbon
technologies are chosen when net zero is enforced, (2) direct
air capture is necessary to reach net zero emissions by 2050
for both DR and SR, and (3) a reduction in capacity of high carbon emitting technologies such as oil refineries and natural gas
power plants without carbon capture because carbon emissions
must be reduced when the net zero constraint is added. Also,
when re-optimization is performed, both with and without emission constraints, there are larger investments into low carbon
and renewable technologies. Detailed results of average investment decisions for the various methods are given in the STAR

Methods.


Deep uncertainty
The probabilities of the outcomes of some parameters can be
difficult to predict; for example, the outcome of incentives
related to carbon emissions is one such parameter since it is
highly influenced by politics. The results presented so far are
based on the assumption that the probability for the two outcomes (high/low credits) was the same (50%). Using the SR
methods, we test probabilities of 25/75 and 75/25 to see if the
probability of having high credits has a significant effect on the
generated transition pathway. When the probability of higher
credits is increased to 75%, the cost of the solution increased
by less than 0.1% and the total investments vary by less than
2.5%. The differences in costs and investments are even smaller

when the probability of high credits decreases to 25%. This simple analysis suggests that, under sequential decision-making,
the impact of the probability of the different credit-related outcomes does not significantly impact investment decisions and,
therefore, assuming a 50% probability for the two outcomes is
reasonable. More broadly, this finding again suggests that the
ability to react to uncertainty realization and new information
arrival leads to high-quality transition pathways. The difference
in costs and investment decisions can be seen in Figure 6.


6 iScience 27, 111288, December 20, 2024



Conclusions

Energy system models, based on multi-period capacity planning
concepts, allow us to study outcomes of different energy transition
pathways while avoiding myopic decisions due to the consideration of short planning horizons and goals. In this paper, we first
expand the scope and utility of such models, and then derive
numerous critical insights into energy transition features and key
outcomes. First, we outline how stochastic optimization can be
used to account for future uncertainty while allowing us to make
unique here-and-now decisions. Second, we argue that, to mimic
reality, energy transitions plans should be generated via an iterative simulation-like process. Most importantly, we elucidate why
transition pathways, and the method used to generate them,
should be assessed under multiple realizations of uncertainty.
Using the proposed methods, we show that accounting for
uncertainty leads, in general, to larger investments as well as investments in a wider set of technologies, which ultimately results
in, counterintuitively, lower average total costs. We show that the
ability to reoptimize, even in a sector with significant time delays,
leads to substantial cost savings. Finally, we briefly discuss the
impact of emission constraints on the energy transition cost
and outline how to approach questions in the presence of
deep uncertainty.


Limitations of the study
The goal of this paper is to propose methods to improve the
assessment of energy system models and use these methods
to study energy transitions. We develop a new model that accounts for uncertainty, propose a sequential decision-making
approach to generate energy transitions, and use a simulationbased framework to assess the quality of the generated solutions. To achieve all of the aforementioned we had to develop
a simplified model and focus on a high-level analysis.
The developed model does not consider operation detail at an
hourly or daily level due to the computational burden that the
simultaneous consideration of uncertainty, through scenarios,
and operational detail would entail. Considering granular time


###### **iScience**

Article


###### **ll**

OPEN ACCESS



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/mcdonaldSequentialDecisionmakingUncertainty2024/mcdonaldSequentialDecisionmakingUncertainty2024.pdf-7-2.png)

Figure 5. Energy transition pathway of DR and SR methods with and without net zero constraints
(A) Average energy transition pathway of DR.
(B) Difference between the average energy transition pathway of DR when emissions are constrained and when only credits are available.
(C) Average energy transition pathway of DR with net zero constraints.
(D) Average energy transition pathway of SR.
(E) Difference between the average energy transition pathway of SR when emissions are constrained and when only credits are available.
(F) Average energy transition pathway of SR with net zero constraints. The average carbon emissions are shown with a blue line. Each technology type
(intermediate, power, liquid fuel) are separated with a black line. Within each technology type the technologies are organized from least carbon emissions to most

carbon emissions.



resolution would potentially affect the solutions obtained and
could be something to consider in future work. Nevertheless,
the presented assessment still shows how other methods
compare against the traditional DF method, and how investment
decisions can be affected by the method one uses to consider
uncertainty.
In this study, technology learning in considered only for
biomass technologies, while other renewable technologies are
considered to have a decrease in cost over time, rather than a
decrease based on installed capacity. Learning rates based on
capacity could be considered for any emerging technologies, if
desired, to better capture how technologies costs evolve.
The optimization model has a cost minimization objective
function, which can be limiting since there are many solutions
that can be within 10% of the minimum cost and can better

meet other goals, such as reducing carbon emissions. This
limitation can be addressed using other objective functions, a
multi-objective optimization model, or modeling and solution
techniques for generating alternatives. The proposed methods
can be extended and applied to these other methods.
Another limitation of this study is the assumption that the total
energy demand is constant and the uncertainty is considered



through what portion of the demand is met by either liquid fuels
or electricity. This assumption could be limiting because electrification can lead to efficiency improvements, leading to a lower
total demand. It would be interesting to consider uncertainty in

the overall demand in future work.

Finally, it was assumed that the probability distribution of
the uncertain parameters was known. In general, obtaining the
probability of representative outcomes of some of these parameters is challenging (e.g., policy decisions). The paper outlined
one approach to partially address this challenge, by looking at
how the solution changed as the probabilities of different policies
related to carbon were varied, but more systematic and rigorous
approaches are needed, which can be the topic of future work.


RESOURCE AVAILABILITY


Lead contact

Further information and requests for resources should be directed to the lead
[contact, Christos T. Maravelias (<EMAIL>).](mailto:<EMAIL>)


Material availability

The study did not generate new materials.


iScience 27, 111288, December 20, 2024 7


###### **ll iScience**

OPEN ACCESS Article

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/mcdonaldSequentialDecisionmakingUncertainty2024/mcdonaldSequentialDecisionmakingUncertainty2024.pdf-8-2.png)


Figure 6. Differences in energy transition pathways when different probabilities for carbon emission reduction credits are used
(A) Energy transition pathway of SR when the probability of the high (low) carbon credits is 50% (50%).
(B) Difference between x 25 [SR] =75 [and][ x] 50 [SR] =50 [, where the subscripts represent the probabilities of high/low credits.]
(C) Difference between x 75 [SR] =25 [and][ x] 50 [SR] =50 [. Each technology type (intermediate, power, liquid fuel) are separated with a black line. Within each technology type the]
technologies are organized from least carbon emissions to most carbon emissions.



Data and code availability


d This paper analyzes existing, publicly available data, accessible in this
papers supplemental information.

d All original code is available in this paper’s supplemental information.

d Any additional information required to reanalyze the data reported in this
paper is available from the lead contact upon request.


ACKNOWLEDGMENTS


This material is based upon work supported in part by the Great Lakes
Bioenergy Research Center, U.S. Department of Energy, Office of Science,
Biological and Environmental Research Program under Award Number
DE-********* and by the Innovation Research Grant from the Andlinger Center for Energy and the Environment at Princeton University.


AUTHOR CONTRIBUTIONS


M.A.M.: conceptualization, data curation, formal analysis, investigation,
methodology, resources, software, validation, visualization, writing—original
draft, writing—review and editing. C.T.M.: conceptualization, formal analysis,
funding acquisition, investigation, methodology, project administration, supervision, validation, writing—review and editing.


DECLARATION OF INTERESTS


The authors declare no competing interests.


STAR+METHODS


Detailed methods are provided in the online version of this paper and include
the following:


d KEY RESOURCES TABLE


d METHOD DETAILS


B Energy system


d METHODS


8 iScience 27, 111288, December 20, 2024



B Stochastic programming

B Uncertain parameters and scenarios

B Rolling horizon approach


B Parameter observation


B Value of stochastic solution


B Scenario reduction properties


d ADDITIONAL RESULTS


B Rolling horizon approach


B Additional results at the national level


B Additional results at the regional level


d MODEL FORMULATION


B Model size


B Model objective


B Notation


B Full model formulation


d QUANTIFICATION AND STATISTICAL ANALYSIS


SUPPLEMENTAL INFORMATION


[Supplemental information can be found online at https://doi.org/10.1016/j.isci.](https://doi.org/10.1016/j.isci.2024.111288)

[2024.111288.](https://doi.org/10.1016/j.isci.2024.111288)


Received: May 3, 2024
Revised: September 5, 2024
Accepted: October 28, 2024
Published: October 30, 2024


REFERENCES


1. Lee, H., andRomero, J. (2023).Climate Change. In Synthesis Report(IPCC),
[pp. 1–34. https://doi.org/10.59327/IPCC/AR6-9789291691647.001.](https://doi.org/10.59327/IPCC/AR6-9789291691647.001)


[2. United Nations. Net Zero Coalition. https://www.un.org/en/climatechange/](https://www.un.org/en/climatechange/net-zero-coalition)

[net-zero-coalition.](https://www.un.org/en/climatechange/net-zero-coalition)


3. The White House (2021). FACT SHEET: President Biden Sets 2030 Greenhouse Gas Pollution Reduction Target Aimed at Creating Good-Paying


###### **iScience**

Article


Union Jobs and Securing U.S. Leadership on Clean Energy Technologies
(The White House Washington, DC). [https://www.whitehouse.gov/](https://www.whitehouse.gov/briefing-room/statements-releases/2021/04/22/fact-sheet-president-biden-sets-2030-greenhouse-gas-pollution-reduction-target-aimed-at-creating-good-paying-union-jobs-and-securing-u-s-leadership-on-clean-energy-technologies/)
[briefing-room/statements-releases/2021/04/22/fact-sheet-president-biden-](https://www.whitehouse.gov/briefing-room/statements-releases/2021/04/22/fact-sheet-president-biden-sets-2030-greenhouse-gas-pollution-reduction-target-aimed-at-creating-good-paying-union-jobs-and-securing-u-s-leadership-on-clean-energy-technologies/)
[sets-2030-greenhouse-gas-pollution-reduction-target-aimed-at-creating-](https://www.whitehouse.gov/briefing-room/statements-releases/2021/04/22/fact-sheet-president-biden-sets-2030-greenhouse-gas-pollution-reduction-target-aimed-at-creating-good-paying-union-jobs-and-securing-u-s-leadership-on-clean-energy-technologies/)
[good-paying-union-jobs-and-securing-u-s-leadership-on-clean-energy-](https://www.whitehouse.gov/briefing-room/statements-releases/2021/04/22/fact-sheet-president-biden-sets-2030-greenhouse-gas-pollution-reduction-target-aimed-at-creating-good-paying-union-jobs-and-securing-u-s-leadership-on-clean-energy-technologies/)
[technologies/.](https://www.whitehouse.gov/briefing-room/statements-releases/2021/04/22/fact-sheet-president-biden-sets-2030-greenhouse-gas-pollution-reduction-target-aimed-at-creating-good-paying-union-jobs-and-securing-u-s-leadership-on-clean-energy-technologies/)


4. Victoria, M., Zeyen, E., and Brown, T. (2022). Speed of technological transformations required in Europe to achieve different climate goals. Joule 6,
[1066–1086. https://doi.org/10.1016/j.joule.2022.04.016.](https://doi.org/10.1016/j.joule.2022.04.016)


5. Way, R., Ives, M.C., Mealy, P., and Farmer, J.D. (2022). Empirically
grounded technology forecasts and the energy transition. Joule 6, 2057–
[2082. https://doi.org/10.1016/j.joule.2022.08.009.](https://doi.org/10.1016/j.joule.2022.08.009)


6. Kydes, A.S. (1980). The Brookhaven Energy System Optimization Model:
Its Variants and Uses. In Energy Policy Modeling: United States and Canadian Experiences: Volume II Integrative Energy Policy Models, W.T.
Ziemba and S.L. Schwartz, eds. (Springer Netherlands), pp. 110–136.
[https://doi.org/10.1007/978-94-009-8751-7_7.](https://doi.org/10.1007/978-94-009-8751-7_7)


7. Lopion, P., Markewitz, P., Robinius, M., and Stolten, D. (2018). A review of
current challenges and trends in energy systems modeling. Renew. Sus[tain. Energy Rev. 96, 156–166. https://doi.org/10.1016/j.rser.2018.07.045.](https://doi.org/10.1016/j.rser.2018.07.045)


8. Krey, V., Luderer, G., Clarke, L., and Kriegler, E. (2014). Getting from here
to there – energy technology transformation pathways in the EMF27
scenarios. Climatic Change 123, 369–382. [https://doi.org/10.1007/](https://doi.org/10.1007/s10584-013-0947-5)

[s10584-013-0947-5.](https://doi.org/10.1007/s10584-013-0947-5)


9. Luderer, G., Bosetti, V., Jakob, M., Leimbach, M., Steckel, J.C., Waisman,

H., and Edenhofer, O. (2012). The economics of decarbonizing the energy
system—results and insights from the RECIPE model intercomparison.
[Climatic Change 114, 9–37. https://doi.org/10.1007/s10584-011-0105-x.](https://doi.org/10.1007/s10584-011-0105-x)


10. Pfenninger, S., Hawkes, A., and Keirstead, J. (2014). Energy systems
modeling for twenty-first century energy challenges. Renew. Sustain. En[ergy Rev. 33, 74–86. https://doi.org/10.1016/j.rser.2014.02.003.](https://doi.org/10.1016/j.rser.2014.02.003)


11. Rathi, T., and Zhang, Q. (2022). Capacity planning with uncertain endoge[nous technology learning. Comput. Chem. Eng. 164, 107868. https://doi.](https://doi.org/10.1016/j.compchemeng.2022.107868)
[org/10.1016/j.compchemeng.2022.107868.](https://doi.org/10.1016/j.compchemeng.2022.107868)


12. Fishbone, L.G., and Abilock, H. (1981). Markal, a linear-programming
model for energy systems analysis: Technical description of the bnl
[version. Int. J. Energy Res. 5, 353–375. https://doi.org/10.1002/er.444](https://doi.org/10.1002/er.444<?show [?tjl=20mm]&tjlpc;[?tjl]?>0050406)

[0050406.](https://doi.org/10.1002/er.444<?show [?tjl=20mm]&tjlpc;[?tjl]?>0050406)


[13. Larson, E., Greig, C., Jenkins, J., Mayfield, E., Pascale, A., Zhang, C.,](http://refhub.elsevier.com/S2589-0042(24)02513-6/sref13)
[Drossman, J., Williams, R., Pacala, S., Socolow, R., et al. (2021). Net-](http://refhub.elsevier.com/S2589-0042(24)02513-6/sref13)
[Zero America: Potential Pathways, Infrastructure, and Impacts.](http://refhub.elsevier.com/S2589-0042(24)02513-6/sref13)


14. Pickering, B., Lombardi, F., and Pfenninger, S. (2022). Diversity of options
to eliminate fossil fuels and reach carbon neutrality across the entire Euro[pean energy system. Joule 6, 1253–1276. https://doi.org/10.1016/j.joule.](https://doi.org/10.1016/j.joule.2022.05.009)

[2022.05.009.](https://doi.org/10.1016/j.joule.2022.05.009)


15. Reinert, C., Nolzen, N., Frohmann, J., Tillmanns, D., and Bardow, A.

(2023). Design of low-carbon multi-energy systems in the SecMOD framework by combining MILP optimization and life-cycle assessment. Comput.
[Chem. Eng. 172, 108176. https://doi.org/10.1016/j.compchemeng.2023.](https://doi.org/10.1016/j.compchemeng.2023.108176)

[108176.](https://doi.org/10.1016/j.compchemeng.2023.108176)


16. Dowling, J.A., Rinaldi, K.Z., Ruggles, T.H., Davis, S.J., Yuan, M., Tong, F.,
Lewis, N.S., and Caldeira, K. (2020). Role of Long-Duration Energy Storage in Variable Renewable Electricity Systems. Joule 4, 1907–1928.
[https://doi.org/10.1016/j.joule.2020.07.007.](https://doi.org/10.1016/j.joule.2020.07.007)


17. Pedersen, T.T., Gøtske, E.K., Dvorak, A., Andresen, G.B., and Victoria, M.

(2022). Long-term implications of reduced gas imports on the decarbon[ization of the European energy system. Joule 6, 1566–1580. https://doi.](https://doi.org/10.1016/j.joule.2022.06.023)
[org/10.1016/j.joule.2022.06.023.](https://doi.org/10.1016/j.joule.2022.06.023)


18. Cuisinier, E [´ ] ., Lemaire, P., Penz, B., Ruby, A., and Bourasseau, C. (2022).
New rolling horizon optimization approaches to balance short-term and
long-term decisions: An application to energy planning. Energy 245,
[122773. https://doi.org/10.1016/j.energy.2021.122773.](https://doi.org/10.1016/j.energy.2021.122773)


###### **ll**

OPEN ACCESS


19. Heuberger, C.F., Staffell, I., Shah, N., and Mac Dowell, N. (2018). Impact of
myopic decision-making and disruptive events in power systems
[planning. Nat. Energy 3, 634–640. https://doi.org/10.1038/s41560-018-](https://doi.org/10.1038/s41560-018-0159-3)

[0159-3.](https://doi.org/10.1038/s41560-018-0159-3)


20. Johnson, N., Krey, V., McCollum, D.L., Rao, S., Riahi, K., and Rogelj, J.
(2015). Stranded on a low-carbon planet: Implications of climate policy
for the phase-out of coal-based power plants. Technol. Forecast. Soc.
[Change 90, 89–102. https://doi.org/10.1016/j.techfore.2014.02.028.](https://doi.org/10.1016/j.techfore.2014.02.028)


21. Keppo, I., and Strubegger, M. (2010). Short term decisions for long
term problems – The effect of foresight on model based energy systems
[analysis. Energy 35, 2033–2042. https://doi.org/10.1016/j.energy.2010.](https://doi.org/10.1016/j.energy.2010.01.019)

[01.019.](https://doi.org/10.1016/j.energy.2010.01.019)


22. Poncelet, K., Delarue, E., Six, D., and D’haeseleer, W. (2016). Myopic
optimization models for simulation of investment decisions in the electric
power sector. In 2016 13th International Conference on the European En[ergy Market (EEM), pp. 1–9. https://doi.org/10.1109/EEM.2016.7521261.](https://doi.org/10.1109/EEM.2016.7521261)


[23. Birge, J.R., and Louveaux, F. (2011). Introduction to Stochastic Program-](http://refhub.elsevier.com/S2589-0042(24)02513-6/sref23)
[ming (Springer Science & Business Media).](http://refhub.elsevier.com/S2589-0042(24)02513-6/sref23)


[24. Kall, P., and Wallace, S.W. (1994). Stochastic Programming (Wiley)).](http://refhub.elsevier.com/S2589-0042(24)02513-6/sref24)


[25. Rawlings, J.B., and Mayne, D.Q. (2009). Model Predictive Control Theory](http://refhub.elsevier.com/S2589-0042(24)02513-6/sref25)
[and Design (Nob Hill Pub, Llc).](http://refhub.elsevier.com/S2589-0042(24)02513-6/sref25)


26. Roijers, D.M., Vamplew, P., Whiteson, S., and Dazeley, R. (2013). A Survey
of Multi-Objective Sequential Decision-Making. J. Artif. Intell. Res. 48,
[67–113. https://doi.org/10.1613/jair.3987.](https://doi.org/10.1613/jair.3987)


[27. U.S. Energy Information Administration. Refining crude oil. https://www.eia.](https://www.eia.gov/energyexplained/oil-and-petroleum-products/refining-crude-oil.php)
[gov/energyexplained/oil-and-petroleum-products/refining-crude-oil.php.](https://www.eia.gov/energyexplained/oil-and-petroleum-products/refining-crude-oil.php)


28. Brinkman, N., Wang, M., Weber, T., and Darlington, T. (2005). Well-toWheels Analysis of Advanced Fuel/Vehicle Systems: A North American
Study of Energy Use, Greenhouse Gas Emissions. Criteria Pollutant Emis[sions (EERE Publication and Product Library). https://doi.org/10.2172/](https://doi.org/10.2172/1218344)

[1218344.](https://doi.org/10.2172/1218344)


29. Pradhan, A., Shrestha, D., Gerpen, J., and Duffield, J. (2008). The Energy
Balance of Soybean Oil Biodiesel Production: A Review of Past Studies.
[Transactions of the ASABE 51, 185–195. https://doi.org/10.13031/2013.](https://doi.org/10.13031/2013.24203)

[24203.](https://doi.org/10.13031/2013.24203)


30. Killingtveit, A [˚ ] . (2019). 8 - Hydropower. In Managing Global Warming, T.M.
[Letcher, ed. (Academic Press), pp. 265–315. https://doi.org/10.1016/](https://doi.org/10.1016/B978-0-12-814104-5.00008-9)

[B978-0-12-814104-5.00008-9.](https://doi.org/10.1016/B978-0-12-814104-5.00008-9)


31. Masnadi, M.S., El-Houjeiri, H.M., Schunack, D., Li, Y., Englander, J.G., Badahdah, A., Monfort, J.C., Anderson, J.E., Wallington, T.J., Bergerson,
J.A., et al. (2018). Global carbon intensity of crude oil production. Science
[361, 851–853. https://doi.org/10.1126/science.aar6859.](https://doi.org/10.1126/science.aar6859)


32. Lee, U., Kwon, H., Wu, M., and Wang, M. (2021). Retrospective analysis of
the U.S. corn ethanol industry for 2005–2019: implications for greenhouse
gas emission reductions. Biofuel. Bioprod. Biorefin. 15, 1318–1331.
[https://doi.org/10.1002/bbb.2225.](https://doi.org/10.1002/bbb.2225)


33. Chen, R., Qin, Z., Han, J., Wang, M., Taheripour, F., Tyner, W., O’Connor,
D., and Duffield, J. (2018). Life cycle energy and greenhouse gas emission
effects of biodiesel in the United States with induced land use change impacts. Bioresour. Technol. 251, 249–258. [https://doi.org/10.1016/j.](https://doi.org/10.1016/j.<?show [?tjl=20mm]&tjlpc;[?tjl]?>biortech.2017.12.031)

[biortech.2017.12.031.](https://doi.org/10.1016/j.<?show [?tjl=20mm]&tjlpc;[?tjl]?>biortech.2017.12.031)


34. Geissler, C.H., and Maravelias, C.T. (2022). Analysis of alternative bioenergy with carbon capture strategies: present and future. Energy Environ.
[Sci. 15, 2679–2689. https://doi.org/10.1039/D2EE00625A.](https://doi.org/10.1039/D2EE00625A)


35. U.S. Energy Information Administration. Capital Cost and Performance
Characteristics for Utility-Scale Electric Power Generating Technologies.
[https://www.eia.gov/analysis/studies/powerplants/capitalcost/.](https://www.eia.gov/analysis/studies/powerplants/capitalcost/)


36. U.S. Energy Information Administration. Carbon Dioxide Emissions Coeffi[cients..https://www.eia.gov/environment/emissions/co2_vol_mass.php..](https://www.eia.gov/environment/emissions/co2_vol_mass.php)


37. U.S. Energy Information Administration. PADD regions enable regional
[analysis of petroleum product supply and movements .https://www.eia.](https://www.eia.gov/todayinenergy/detail.php?id=4890)
[gov/todayinenergy/detail.php?id=4890.](https://www.eia.gov/todayinenergy/detail.php?id=4890)


iScience 27, 111288, December 20, 2024 9


###### **ll iScience**

OPEN ACCESS Article



38. Frittelli, J., Parfomak, P.W., Ramseur, J.L., Andrews, A., Pirog, R., and Ratner, M. (2014). U.S. Rail Transportation of Crude Oil: Background and Is[sues for Congress (UNT Digital Library). https://digital.library.unt.edu/ark:/](https://digital.library.unt.edu/ark:/67531/metadc276861/)

[67531/metadc276861/.](https://digital.library.unt.edu/ark:/67531/metadc276861/)


39. Kocoloski, M., Michael Griffin, W., and Scott Matthews, H. (2011). Impacts
of facility size and location decisions on ethanol production cost. Energy
[Pol. 39, 47–56. https://doi.org/10.1016/j.enpol.2010.09.003.](https://doi.org/10.1016/j.enpol.2010.09.003)


40. Laughlin, M., and Burnham, A. (2016). Case Study: Natural Gas Regional
[Transport Trucks (Department of Energy). https://www.osti.gov/biblio/](https://www.osti.gov/biblio/1336552)

[1336552.](https://www.osti.gov/biblio/1336552)


41. U.S. Energy Information Administration. Major U.S. utilities spending more
[on electricity delivery, less on power production. https://www.eia.gov/](https://www.eia.gov/todayinenergy/detail.php?id=50456)
[todayinenergy/detail.php?id=50456..](https://www.eia.gov/todayinenergy/detail.php?id=50456)


42. U.S. Energy Information Administration. Rail Coal Transportation Rates.

[https://www.eia.gov/coal/transportationrates/..](https://www.eia.gov/coal/transportationrates/)


43. U.S. Department of Transportation. Cost of Transportation: Costs Faced by
[Businesses Purchasing Transportation Services https://data.bts.gov/stories/](https://data.bts.gov/stories/s/Transportation-Economic-Trends-Transportation-Cost/2yqq-baqd/)
[s/Transportation-Economic-Trends-Transportation-Cost/2yqq-baqd/..](https://data.bts.gov/stories/s/Transportation-Economic-Trends-Transportation-Cost/2yqq-baqd/)


44. U.S. Energy Information Administration. Uranium Marketing Annual
[Report. https://www.eia.gov//uranium/marketing/index.php..](https://www.eia.gov//uranium/marketing/index.php)


45. Marufuzzaman, M., Eksxioglu, S.D., and Hernandez, R. (2015). Truck versus�
pipeline transportation cost analysis of wastewater sludge. Transport.
[Res. Pol. Pract. 74, 14–30. https://doi.org/10.1016/j.tra.2015.02.001.](https://doi.org/10.1016/j.tra.2015.02.001)


46. Hadjerioua, B., Wei, Y., and Kao, S.-C. (2012). US Hydropower Potential from
Existing Non-powered Dams (Greater than 1MW) (Oak Ridge National Labo[ratory). https://doi.org/10.21951/HYDROPOTENTIAL_NPD_V1/1493305.](https://doi.org/10.21951/HYDROPOTENTIAL_NPD_V1/1493305)


[47. U.S. Energy Information Administration. Annual Coal Reports. https://](https://www.eia.gov/coal/annual/index.php)
[www.eia.gov/coal/annual/index.php..](https://www.eia.gov/coal/annual/index.php)


48. U.S. Energy Information Administration. Biodiesel Imports by Area of Entry.

[https://www.eia.gov/dnav/pet/pet_move_imp_a_EPOORDB_IM0_mbbl_a.htm..](https://www.eia.gov/dnav/pet/pet_move_imp_a_EPOORDB_IM0_mbbl_a.htm)


[49. U.S. Energy Information Administration. Crude Oil Production. https://](https://www.eia.gov/dnav/pet/pet_crd_crpdn_adc_mbbl_a.htm)
[www.eia.gov/dnav/pet/pet_crd_crpdn_adc_mbbl_a.htm..](https://www.eia.gov/dnav/pet/pet_crd_crpdn_adc_mbbl_a.htm)


50. U.S. Energy Information Administration. Domestic Uranium Production Report

[- Annual. https://www.eia.gov//uranium/production/annual/uisl.php..](https://www.eia.gov//uranium/production/annual/uisl.php)


51. U.S. Energy Information Administration. East Coast (PADD 1) Crude Oil
[Imports. https://www.eia.gov/dnav/pet/pet_move_impcp_a2_r10_epc0_](https://www.eia.gov/dnav/pet/pet_move_impcp_a2_r10_epc0_IP0_mbbl_a.htm)

[IP0_mbbl_a.htm..](https://www.eia.gov/dnav/pet/pet_move_impcp_a2_r10_epc0_IP0_mbbl_a.htm)


[52. U.S. Energy Information Administration. International. https://www.eia.](https://www.eia.gov/international/data/world/coal-and-coke/coal-and-coke-imports?pd=1&amp;p=0000000000000000000000000000000000000007g420000000004&amp;u=0&amp;f=A&amp;v=mapbubble&amp;a=-&amp;i=none&amp;vo=value&amp;t=C&amp;g=00000000000000000000000000000000000000000000000001&amp;l=249-ruvvvvvfvtvnvv1vrvvvvfvvvvvvfvvvou20evvvvvvvvvvnvvvs0008&amp;s=315532800000&amp;e=1609459200000&amp;)
[gov/international/data/world/coal-and-coke/coal-and-coke-imports?pd=1&p=000](https://www.eia.gov/international/data/world/coal-and-coke/coal-and-coke-imports?pd=1&amp;p=0000000000000000000000000000000000000007g420000000004&amp;u=0&amp;f=A&amp;v=mapbubble&amp;a=-&amp;i=none&amp;vo=value&amp;t=C&amp;g=00000000000000000000000000000000000000000000000001&amp;l=249-ruvvvvvfvtvnvv1vrvvvvfvvvvvvfvvvou20evvvvvvvvvvnvvvs0008&amp;s=315532800000&amp;e=1609459200000&amp;)
[0000000000000000000000000000000000007g420000000004&u=0&f=A&v=](https://www.eia.gov/international/data/world/coal-and-coke/coal-and-coke-imports?pd=1&amp;p=0000000000000000000000000000000000000007g420000000004&amp;u=0&amp;f=A&amp;v=mapbubble&amp;a=-&amp;i=none&amp;vo=value&amp;t=C&amp;g=00000000000000000000000000000000000000000000000001&amp;l=249-ruvvvvvfvtvnvv1vrvvvvfvvvvvvfvvvou20evvvvvvvvvvnvvvs0008&amp;s=315532800000&amp;e=1609459200000&amp;)
[mapbubble&a=-&i=none&vo=value&t=C&g=000000000000000000000000](https://www.eia.gov/international/data/world/coal-and-coke/coal-and-coke-imports?pd=1&amp;p=0000000000000000000000000000000000000007g420000000004&amp;u=0&amp;f=A&amp;v=mapbubble&amp;a=-&amp;i=none&amp;vo=value&amp;t=C&amp;g=00000000000000000000000000000000000000000000000001&amp;l=249-ruvvvvvfvtvnvv1vrvvvvfvvvvvvfvvvou20evvvvvvvvvvnvvvs0008&amp;s=315532800000&amp;e=1609459200000&amp;)

[00000000000000000000000001&l=249-ruvvvvvfvtvnvv1vrvvvvfvvvvvvfvvv](https://www.eia.gov/international/data/world/coal-and-coke/coal-and-coke-imports?pd=1&amp;p=0000000000000000000000000000000000000007g420000000004&amp;u=0&amp;f=A&amp;v=mapbubble&amp;a=-&amp;i=none&amp;vo=value&amp;t=C&amp;g=00000000000000000000000000000000000000000000000001&amp;l=249-ruvvvvvfvtvnvv1vrvvvvfvvvvvvfvvvou20evvvvvvvvvvnvvvs0008&amp;s=315532800000&amp;e=1609459200000&amp;)

[ou20evvvvvvvvvvnvvvs0008&s=315532800000&e=1609459200000&.](https://www.eia.gov/international/data/world/coal-and-coke/coal-and-coke-imports?pd=1&amp;p=0000000000000000000000000000000000000007g420000000004&amp;u=0&amp;f=A&amp;v=mapbubble&amp;a=-&amp;i=none&amp;vo=value&amp;t=C&amp;g=00000000000000000000000000000000000000000000000001&amp;l=249-ruvvvvvfvtvnvv1vrvvvvfvvvvvvfvvvou20evvvvvvvvvvnvvvs0008&amp;s=315532800000&amp;e=1609459200000&amp;)


[53. U.S. Energy Information Administration. Natural Gas Dry Production. https://](https://www.eia.gov/dnav/ng/ng_prod_sum_a_EPG0_FPD_mmcf_a.htm)
[www.eia.gov/dnav/ng/ng_prod_sum_a_EPG0_FPD_mmcf_a.htm..](https://www.eia.gov/dnav/ng/ng_prod_sum_a_EPG0_FPD_mmcf_a.htm)


54. U.S. Energy Information Administration. Electric Power Industry - U.S.
[Electricity Imports from and Electricity Exports to Canada. https://www.](https://www.eia.gov/electricity/annual/html/epa_02_14.html)
[eia.gov/electricity/annual/html/epa_02_14.html..](https://www.eia.gov/electricity/annual/html/epa_02_14.html)


55. U.S. Energy Information Administration. U.S. Natural Gas Imports by
[Country. https://www.eia.gov/dnav/ng/ng_move_impc_s1_a.htm..](https://www.eia.gov/dnav/ng/ng_move_impc_s1_a.htm)


56. U.S. Energy Information Administration. Where our uranium comes from.

[https://www.eia.gov/energyexplained/nuclear/where-our-uranium-comes-](https://www.eia.gov/energyexplained/nuclear/where-our-uranium-comes-from.php)
[from.php..](https://www.eia.gov/energyexplained/nuclear/where-our-uranium-comes-from.php)


10 iScience 27, 111288, December 20, 2024



57. US EPA. Geographic Perspective on the Current Biomass Resource Avail[ability in the United States. https://archive.epa.gov/epa/statelocalclimate/](https://archive.epa.gov/epa/statelocalclimate/geographic-perspective-current-biomass-resource-availability-united-states.html)
[geographic-perspective-current-biomass-resource-availability-united-](https://archive.epa.gov/epa/statelocalclimate/geographic-perspective-current-biomass-resource-availability-united-states.html)

[states.html..](https://archive.epa.gov/epa/statelocalclimate/geographic-perspective-current-biomass-resource-availability-united-states.html)


58. U.S. Energy Information Administration. Atmospheric Crude Oil Distillation
[Operable Capacity. https://www.eia.gov/dnav/pet/pet_pnp_cap1_a_(na)](https://www.eia.gov/dnav/pet/pet_pnp_cap1_a_(na)_8D0_BpCD_a.htm)
[_8D0_BpCD_a.htm..](https://www.eia.gov/dnav/pet/pet_pnp_cap1_a_(na)_8D0_BpCD_a.htm)


[59. U.S. Energy Information Administration. Historical State Data. https://](https://www.eia.gov/electricity/data/state/)
[www.eia.gov/electricity/data/state/..](https://www.eia.gov/electricity/data/state/)


[60. SEIA. State-By-State Map. https://www.seia.org/states-map.](https://www.seia.org/states-map)


61. U.S. Energy Information Administration. U.S. Biodiesel Plant Production
[Capacity. https://www.eia.gov/biofuels/biodiesel/capacity/..](https://www.eia.gov/biofuels/biodiesel/capacity/)


62. U.S. Energy Information Administration. U.S. Fuel Ethanol Plant Production
[Capacity. https://www.eia.gov/petroleum/ethanolcapacity/index.php..](https://www.eia.gov/petroleum/ethanolcapacity/index.php)


63. WINDExchange. U.S. Installed and Potential Wind Power Capacity and
[Generation. https://windexchange.energy.gov/maps-data/321..](https://windexchange.energy.gov/maps-data/321)


64. Ferioli, F., and van der Zwaan, B.C.C. (2009). Learning in Times of Change:
A Dynamic Explanation for Technological Progress. Environ. Sci. Technol.
[43, 4002–4008. https://doi.org/10.1021/es900254m.](https://doi.org/10.1021/es900254m)


65. Daugaard, T., Mutti, L.A., Wright, M.M., Brown, R.C., and Componation, P.
(2015). Learning rates and their impacts on the optimal capacities and pro[duction costs of biorefineries. Biofuel. Bioprod. Biorefin. 9, 82–94. https://](https://doi.org/10.1002/bbb.1513)
[doi.org/10.1002/bbb.1513.](https://doi.org/10.1002/bbb.1513)


66. U.S. Energy Information Administration. Primary Energy Consumption Es[timates, 2021. https://www.eia.gov/state/seds/sep_sum/html/sum_btu_](https://www.eia.gov/state/seds/sep_sum/html/sum_btu_totcb.html)

[totcb.html..](https://www.eia.gov/state/seds/sep_sum/html/sum_btu_totcb.html)


67. U.S. Energy Information Administration. West Coast (PADD 5) Product
[Supplied for Crude Oil and Petroleum Products. https://www.eia.gov/](https://www.eia.gov/dnav/pet/pet_cons_psup_dc_r50_mbbl_a.htm)
[dnav/pet/pet_cons_psup_dc_r50_mbbl_a.htm..](https://www.eia.gov/dnav/pet/pet_cons_psup_dc_r50_mbbl_a.htm)


68. U.S. Energy Information Administration. US Electricity Profile 2021.

[https://www.eia.gov/electricity/state/index.php..](https://www.eia.gov/electricity/state/index.php)


69. Jenkins, J.D., Farbes, J., Jones, R., and Mayfield, E.N. (2022). REPEAT
Project Section-By-Section Summary of Energy and Climate Policies in
[the 117th Congress. REPEAT Project. Zenodo. http://bit.ly/REPEAT-](http://bit.ly/REPEAT-Policies)

[Policies.](http://bit.ly/REPEAT-Policies)


70. U.S. Energy Information Administration. Annual Energy Outlook 2022.

[https://www.eia.gov/outlooks/aeo/data/browser/#/?id=2-AEO2022&region=1-](https://www.eia.gov/outlooks/aeo/data/browser/#/?id=2-AEO2022&amp;region=1-0&amp;cases=ref2022&amp;start=2020&amp;end=2050&amp;f=A&amp;linechart=%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7Eref2022-d011222a.149-2-AEO2022.1-0&amp;map=ref2022-d011222a.4-2-AEO2022.1-0&amp;ctype=linechart&amp;sourcekey=0)

[0&cases=ref2022&start=2020&end=2050&f=A&linechart=�����������](https://www.eia.gov/outlooks/aeo/data/browser/#/?id=2-AEO2022&amp;region=1-0&amp;cases=ref2022&amp;start=2020&amp;end=2050&amp;f=A&amp;linechart=%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7Eref2022-d011222a.149-2-AEO2022.1-0&amp;map=ref2022-d011222a.4-2-AEO2022.1-0&amp;ctype=linechart&amp;sourcekey=0)

[������������������ref2022-d011222a.149-2-AEO2022.1-0&map=](https://www.eia.gov/outlooks/aeo/data/browser/#/?id=2-AEO2022&amp;region=1-0&amp;cases=ref2022&amp;start=2020&amp;end=2050&amp;f=A&amp;linechart=%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7Eref2022-d011222a.149-2-AEO2022.1-0&amp;map=ref2022-d011222a.4-2-AEO2022.1-0&amp;ctype=linechart&amp;sourcekey=0)
[ref2022-d011222a.4-2-AEO2022.1-0&ctype=linechart&sourcekey=0.](https://www.eia.gov/outlooks/aeo/data/browser/#/?id=2-AEO2022&amp;region=1-0&amp;cases=ref2022&amp;start=2020&amp;end=2050&amp;f=A&amp;linechart=%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7E%7Eref2022-d011222a.149-2-AEO2022.1-0&amp;map=ref2022-d011222a.4-2-AEO2022.1-0&amp;ctype=linechart&amp;sourcekey=0)


71. Birge, J.R. (1982). The value of the stochastic solution in stochastic linear
[programs with fixed recourse. Math. Program. 24, 314–325. https://doi.](https://doi.org/10.1007/BF01585113)
[org/10.1007/BF01585113.](https://doi.org/10.1007/BF01585113)


72. Escudero, L.F., Garı´n, A., Merino, M., and Pe´ rez, G. (2007). The value of the
[stochastic solution in multistage problems. TOP 15, 48–64. https://doi.](https://doi.org/10.1007/s11750-007-0005-4)
[org/10.1007/s11750-007-0005-4.](https://doi.org/10.1007/s11750-007-0005-4)


73. Colvin, M., and Maravelias, C.T. (2010). Modeling methods and a branch
and cut algorithm for pharmaceutical clinical trial planning using stochas[tic programming. Eur. J. Oper. Res. 203, 205–215. https://doi.org/10.](https://doi.org/10.1016/j.ejor.2009.07.022)
[1016/j.ejor.2009.07.022.](https://doi.org/10.1016/j.ejor.2009.07.022)


74. Apap, R.M., and Grossmann, I.E. (2017). Models and computational strategies for multistage stochastic programming under endogenous and
[exogenous uncertainties. Comput. Chem. Eng. 103, 233–274. https://](https://doi.org/10.1016/j.compchemeng.2016.11.011)
[doi.org/10.1016/j.compchemeng.2016.11.011.](https://doi.org/10.1016/j.compchemeng.2016.11.011)


###### **iScience**

Article


STAR+METHODS


KEY RESOURCES TABLE


REAGENT or RESOURCE SOURCE IDENTIFIER


Deposited data


###### **ll**

OPEN ACCESS



Capital costs This paper Excel file ‘‘Table S1. Capital Cost’’


Purchasing costs This paper Excel file ‘‘Table S2. Purchase Cost’’


Operating costs This paper Excel file ‘‘Table S3. Operating Cost’’


Transportation costs This paper Excel file ‘‘Table S4. Transportation Cost’’


Demand for products This paper Excel file ‘‘Table S5. Demand’’


Supply of components This paper Excel file ‘‘Table S6. Supply’’


Existing capacity of technologies This paper Excel file ‘‘Table S7. Existing Capacity’’


Maximum capacity and production This paper Excel file ‘‘Table S8. Maximum Capacity

and Production’’


Learning curve This paper Excel file ‘‘Table S9. Learning Curve
Expansion Points’’


Carbon credits This paper Excel file ‘‘Table S10. Carbon Credits’’


Availability factor for technologies This paper Excel file ‘‘Table S11. Availability Factor’’


Conversion factor for technologies This paper Excel file ‘‘Table S12. Conversion’’


Lifetime and loss of capacity for This paper Excel file ‘‘Table S13. Lifetime and Loss of
technologies Capacity’’


Radius of regions This paper Excel file ‘‘Table S14. Radius’’


Midpoint of regions This paper Excel file ‘‘Table S15. Midpoint of Region

and Distance’’


Probability of scenarios This paper Excel file ‘‘Table S16. Probabilities’’


Penalties for unmet demand, interest rate, This paper Excel file ‘‘Table S17. Scalar Values’’
and tortuosity factor


Software and algorithms


GAMS 38.3.0 General Algebraic Modeling System [https://www.gams.com/](https://www.gams.com/)


Gurobi 9.5.1 Gurobi Optimization [https://www.gurobi.com/](https://www.gurobi.com/)


Model used to generate results This paper Supplementary Information


METHOD DETAILS


Energy system
Technology and component overview
Figure S1 shows an overview of all the technologies and components and how they are connected. There are 21 total technologies:
10 producing liquid fuels, 9 generating electricity, and two intermediate technologies. There are 21 components: 11/10 consumed/
produced by technologies; among the 10 produced, five can also be consumed in a different technology. A table that shows the
conversion of a component in a technology can be found in Table S12. [13][,][27–36] There are 6 final products that can be used to
meet the final demand in each region. Some of the final products can only be produced by a single component while others can
be produced by multiple components. For example, the aviation fuel product can only be produced by jet fuel while the motor gasoline product can be produced by gasoline and ethanol.
Spatial and temporal modeling
The US is represented in terms of seven regions: Alaska, Hawaii, and five regions for the continental US that follow the five Petroleum
Administration for Defense Districts (PADDs) defined by EIA and can be seen in Figure S2. [37] Alaska and Hawaii are the only states
considered as individual regions since they are disconnected from the continental US. The model is solved over a 45-year time
horizon divided into nine 5-year time periods. All results are only shown through 2050.
The transportation distance between regions is calculated from the midpoints of each region. Transportation within a region is
calculated based on the average distance from the center of a circle, 2=3t � r, where r is the radius of the circle and t is the tortuosity


iScience 27, 111288, December 20, 2024 e1


###### **ll iScience**

OPEN ACCESS Article


factor, which is used to calculate the actual distance from the straight-line distance. The radius of each region is calculated by
assuming that the total area of each region is the area of a circle. Spatially, the system is coarser than systems used in other models
because of the increase in model size due to the inclusion of scenarios. Nevertheless, the system we consider allow us to maintain all
the key model characteristics (i.e., decision-making over a multi-period horizon considering multiple regions, products, and technologies) and thus can be used to derive key insights into the impacts of (1) sequential decision making and (2) accounting for
uncertainty.
Regions are connected by pipeline, truck, rail, boat, and transmission lines. Components can only be transported by certain types
of transportation modes, as seen in Table S1. The cost of different transportation modes and components can be found in Table
S4. [38–45]


Liquid fuel products consist of gasoline, ethanol, diesel, biodiesel, jet fuel, residual oil, and other oils. Boats are only used to connect ports to regions as well as connect Alaska and Hawaii to the continental US. Figure S2 shows how one region is connected to the
remaining regions. Green font indicates regions in the US, yellow font indicates federal offshore land where natural gas and crude oil
are drilled, and red font indicates ports where imports from outside of the US are accepted.
Supply data
For hydroelectric power plants, the available supply is assumed to be the potential hydropower from existing non-powered dams
within the US that have a greater potential capacity than 1 MW. [46] All other supply data can be found in Table S6. [47–57]

Capacity
All data on existing capacity and bounds for expansion can be found in Tables S7 and S8. [58–63]

We consider retirement of capacity and a delay in the installation of new capacity. The retirement of capacity happens in two
different ways. First, the capacity that is available at the beginning of the horizon (2020) will be retired gradually within the planning
horizon, depending on the lifetime of the different technologies. This gradual retirement represents the fact that existing capacity
was installed at different times, thus retirements happen at different time points in the horizon. Second, the newly installed capacity,
predicted by the model, will be retired after the lifetime of the technology, which ranges between 20 and 60 years. The delay in the
installation of new capacity is implemented because of the significant time needed to build a new facility. We assume that it takes 5
years, from the time the decision is made, to install the corresponding capacity.
The capacity is reported as the yearly production level of a given technology. In order to include liquid fuel producing technologies
and electricity generating technologies in the same figures, all capacity is reported in Gigawatts. The capacity is reported in terms of
power because the capacity must be sufficient to meet the total energy demand per year.
Learning curve
Many studies using energy system models have shown that investments are going to have to be made into emerging and developing
technologies that are currently expensive to build and operate. [13] As investments are made into developing technologies, the costs
can decrease. This decrease in cost with increased capacity can be described by a learning curve. [64] We consider learning curves for
all biomass technologies since these are new technologies that have not been implemented commercially and have a high potential
for learning. We assume that we will learn from all biomass technologies, that is, if there is investment into one type of biomass technology, then there will be a decrease in cost across all biomass technologies. Other technologies, like wind and solar, are considered
to have a decrease capital costs over time rather than a decrease based on a learning rate.
We use discretized points to model the learning curve to maintain the linearity of the model. There are specific expansion levels,
d, that describe the level of capacity required to see a decrease in costs. The expansion levels are based on the total installed
capacity of all biomass technologies, but the decrease in cost is specific to each technology and their learning rate. [65]

Figure S3 shows a learning curve for corn ethanol refineries where the x axis is the total newly invested capacity of all biomass
technologies and the y axis is the capital cost of corn ethanol refineries. The figure illustrates the relationship between the capacity
expansion variables QE d and [d] QE d 0 . These variables describe the capacity expansion investment that is made into technologies to
increase the capacity from level d or increase the capacity to level d [0] respectively, given that d < d [0] . The variables are further defined
in model formulation. In this example, the total newly invested capacity of all biomass technologies begins at level d = 0, which relates to no new investments; the capital cost of corn ethanol is at 2:73 $/Gal. Investments can be made into multiple biomass technologies or a single technology that will increase the capacity from level d = 0 to level d = 1 (31 � 10 [9] Gal/year). In this example, there
is only investment into corn ethanol refineries to increase to d = 1. When the investments are made, QE d = 0 = [d] QE d = 1 = 31 � 10 [9]

Gal/year. [d] QE d = 1 shows that the investment of 31 � 10 [9] Gal/year will increase the total newly invested capacity to expansion level
d = 1 once all capacity has been installed and will relate to a new capital cost of 2:10 $/Gal. QE d = 0 shows that the investment of
31 � 10 [9] Gal/year is made while the total newly invested capacity is still at the initial expansion point, d = 0, and relates to the capital
cost that will need to be invested to make the 31 � 10 [9] Gal/year expansion.


METHODS


Stochastic programming
A two-stage SP model can be expressed as follows: [23][,][24]


min c [T] x + E x Qðx; x s Þ (Equation 1)


e2 iScience 27, 111288, December 20, 2024


###### **iScience**

Article


###### **ll**

OPEN ACCESS


s:t: Ax = b (Equation 2)


x R 0; y s R 0 (Equation 3)



where x is a vector of first stage decision variables, c is the first stage cost vector, b is the first stage right hand side vector, and A is the
first stage matrix. The second term in the objective function is the expectation of the recourse function, which is defined as:


Qðx; x s Þ = min�q [T] s [y] [s] ��W s y s = h s � T s x; x; y R 0� (Equation 4)
y


where y s is a vector of second stage decision variables; x s is a vector that contains the realizations of the random variables in scenario
s; q s is the second stage cost vector, W s is a recourse matrix, h s is the right-hand side, and T s is a technology matrix that is used to
relate second stage decisions, y s, to first stage decisions, x. The two-stage program can be expanded to a multistage program by the
addition of a recourse function for each stage. For example, the objective function in a 3-stage model would be as follows: [23]


min c [T][;][1] x [1] + E x 2 Q [2] [�] x [1] ; x [2] s � + E x 3 Q [3] [�] x [2] ; x [3] s � (Equation 5)


In Equation 5, superscript numbers refer to stages. It is seen that the recourse function depends on the realization of uncertainty in
the current stage (x [H] s [) and the decisions made in the previous stage (][x] [H][ �] [1] [), where H refers to the current stage. The previous stage]
decisions are denoted as x, but they no longer only refer to first stage decisions as in the two-stage problem defined in Equations 1, 2,
and 3. Now, x [1] ; x [2] ; x [3] refer to the first, second, third stage decisions.
As described in the main text, a scenario tree is used in SP to represent all outcomes of the uncertain parameters, where a scenario
corresponds to a path from the root node to a leaf node. Each scenario has a probability that is equal to the product of the probabilities of the outcomes of the uncertain parameters observed along the path. [24] In MSSP, uncertainty is observed successively
through each stage, meaning that the distinction across all scenarios is not fully known until the final stage. [24] At the first stage,
all decision variables have to be the same across scenarios because no uncertainty has been observed, that is, the scenarios are
indistinguishable. In the following stages, some uncertainty is observed, which makes some subsets of scenarios distinguishable.
Nonanticipativity constraints enforce decisions in scenarios that are indistinguishable from each other to be equal.
The orange path shown in Figure S4 represents the path of a scenario through a scenario tree where a high value for the uncertain
parameter is observed at each stage. Also, each group of indistinguishable scenarios are grouped together at each time point. At the
last time point, when all uncertainty has been revealed, all scenarios are distinguishable.


Uncertain parameters and scenarios
We consider uncertainty in (1) the fraction of energy demand that will be met by either electricity or liquid fuels, (2) the level of credits
related to carbon emission reduction (either a credit to capture carbon or a credit to produce electricity from renewable sources), and
(3) the capital costs of onshore wind and photovoltaic solar technologies. Scenario trees depicting when the random parameters are
observed are shown in Figure S5. The data for the random parameters can be found in Tables S1, S5, and S10. [35][,][66–69]

The total energy demand is assumed to be known and follow the projections in the annual energy outlook 2022. [70] The uncertainty is
considered through what proportion of the demand is met by liquid fuels or electricity. The demand for liquid fuel products (gasoline,
diesel, jet fuel, residual oil, and other oils) in each region changes in time by the same relative amount. The six demand outcomes
range from high electrification to low electrification. The outcomes of electricity demand can be seen in Figure S5A.
In terms of credits related to carbon emission reduction, we consider two credits. The first is a credit to capture carbon and the
second is a credit to produce electricity from renewable sources. There are two outcomes, representing one high and one low credit
level. The outcomes of the carbon capture credit can be seen in Figure S5B.
The third source of uncertainty is the capital costs of wind and solar since it is expected that these technologies will receive large
investments over the next 30 years. There are two outcomes: a steep decrease in costs due to ongoing large investments; and a less
steep decrease where it is assumed that the large investments already made have reduced the capital costs significantly and the
costs are approaching a plateau. The outcomes of onshore wind capital costs can be seen in Figure S5C.
The outcomes of the above discussed random parameters are combined to form a set of 24 scenarios shown in the scenario tree
in Figure S6. To the right of each scenario in the tree, the outcome of each uncertain parameter is defined using the reference
abbreviations (OD, OC, OI) used in Figure S5.


Rolling horizon approach
The implementation of the rolling horizon approach, which is similar to simulation approaches used to analyze different control strategies for dynamic systems, [25] is illustrated in Figure S7. In general, it is assumed that data (e.g., demand forecast) for the next 10 years
is well known, but there is uncertainty beyond 10 years.
In iteration 1, the original model, either D or S, is solved considering a horizon from 2020 through 2050 (iteration 1). The decisions
made in the first time period (2020–2025) are fixed and thus become part of the implemented energy transition pathway. In iteration 2,
the planning horizon is rolled forward by one period, that is, the decision maker will make decisions over the 2025–2055 period,
knowing the state of the system in 2025. Rolling the horizon forward also means that uncertainty for period 2030–2035, which is


iScience 27, 111288, December 20, 2024 e3


###### **ll iScience**

OPEN ACCESS Article


now within 10 years from the start of the horizon in iteration 2, is observed and that information about the newly added time period
(2050–2055) is considered. The model is resolved and the process is repeated until an implemented solution has been generated for
the horizon of interest (2020–2050). Note that as the horizon is moved forward, the planning horizon, at some point, is shortened,
though we continue to solve a planning model with a horizon that exceeds beyond the period we are interested in so that we do
not obtain any myopic solutions.
When D is paired with this approach, we use the mean values of the uncertain parameters. When an MSSP model is used in
each iteration, the scenario tree that describes the uncertain parameters ‘‘moves’’ with the horizon, that is, the scenario tree
structure remains the same (i.e., uncertainty starts to be observed at the third time period of any given iteration), but the values of
the parameters are updated, as shown in Figure S8 using the scenario tree depicting electricity demand.


Parameter observation

A simulation-based method is used to show how the generated pathways of the different approaches fare when the parameters
differ from their expected values. The values for the demand, credits related to carbon emission reduction, and capital cost of select
renewables are sampled from the distribution used to generate the mean values used in the deterministic model. In the rolling horizon
approach, the value of a parameter is observed in the second time period of the model solved in a given iteration, while the remaining
values of the parameter are the predicted values. We observe the parameters that are considered as random parameters in the stochastic model. The observation of the parameters is illustrated in Figure S9 using electricity demand as an example. Panel A/D shows
how electricity demand changes over time in the deterministic/stochastic model solved in the first iteration. In the second iteration
(panels B and E), the value of the demand is observed in the second time period. In this example the demand is higher than what was
expected (shown in red). The demands after the second time period are the predicted values. In the third iteration (panels C and F), the
demand in the first period is equal to the value that was observed. The demand is observed again, in the second time period of the
current iteration, and is higher than expected. The value of the observed demand in all iterations is from a triangular distribution

around the mean demand used in the deterministic model.


Value of stochastic solution

The value of stochastic solution (VSS) is calculated to measure the benefit of using SP coupled with the forward-looking approach.
When MSSP is used, the VSS is the difference between the expected result of using the solution of the deterministic model (EEV) and
the optimal objective function value of the MSSP model. [71]


VSS = EEV � f [SF] (Equation 6)


Since the deterministic model does not include scenarios, the EEV is calculated to allow the deterministic model to be exposed to
the same uncertainty as the stochastic model, allowing for a fairer comparison of the costs. The following procedure is used to calculate the EEV when uncertainty is observed throughout multiple stages [72] .


(1) Solve the deterministic model at the first node of the scenario tree, that is, at the beginning of the horizon assuming mean
(expected) parameter values (see red parameter values in Figure S10A). At the first node, the value of the parameter is the
expected value across the 8 scenarios at each time period. Fix the decisions for the first time period, which is denoted
with a red box around the first node of the scenario tree in Figure S10B.
(2) Move forward by one time period and solve a deterministic model at each node (i.e., for each subset of indistinguishable
scenarios) assuming that the value of the uncertain parameter is equal to the corresponding values used in the MSSP counterpart. The remaining parameter value, beyond period 2, is equal to the mean value of the parameter in the scenarios that are
indistinguishable in period 2 (see Figure S10B). Fix the decisions in the current time period at each node.
(3) Continue to move forward by one time period and solve a deterministic model at each node in the scenario tree
and fix the decisions of each node in each time period. See Figure S10C for the continuation of this process to the third
time period.
(4) After the deterministic model has been solved in the last time period, the expected cost is calculated using the optimal
objective function value of the deterministic model at each node in the final time period of the scenario tree using the same
probabilities as the MSSP model.


The VSS quantifies if it is beneficial to consider uncertainty using SP instead of considering mean parameter values. [23] For a minimization model, as is the case for this study, we would expect to see a positive VSS since the optimal objective function value of the
MSSP model should be lower than the EEV.


Scenario reduction properties
The nonanticipativity constraints are originally written for every scenario pair ðs;s [0] Þ, which can cause a stochastic program to quickly
become computationally intractable. There are a few properties that we use to decrease the number of redundant scenario pairs to
help keep computation time within a reasonable range. The proofs for these properties are beyond the scope of this paper. We refer
the reader to Colvin et al. [73] and Apap et al. [74] for proofs of scenario reduction properties. [,]


e4 iScience 27, 111288, December 20, 2024


###### **iScience**

Article


Property 1. ðs; s [0] Þ = ðs [0] ; sÞ, therefore, it is sufficient to enforce NACs on scenario pairs ðs; s [0] Þ given s < s [0] .
Property 2. It is sufficient to enforce NACs for scenario pairs ðs; s [0] Þ where s and s [0] are adjacent.


ADDITIONAL RESULTS


###### **ll**

OPEN ACCESS



Rolling horizon approach
The average energy transition pathway is calculated with the investments that are made, using the different methods, across
the 100 different uncertainty realizations. When we compare bx [DR] / bx [SR] to x [DR] / x [SR], it is seen that x [DR] and x [SR] have more carbon
emitting technologies (e.g., oil refineries, natural gas power plants without carbon capture, coal power plants) by the end of the
time horizon, shown in Figure S11. bx [DR] and bx [SR] have more renewable and low carbon technologies, such as RWGSFTS, solar,
onshore wind, hydroelectric power plants, and direct air capture. This shows that on average, when the decision maker is able
to reoptimize, there are larger investments into low carbon and renewable technologies which can help to meet net zero emission
goals.


Additional results at the national level

Figures S12 through S14 are additional results at the national level for method SR. Note, for Figure S13, that the previously installed
capacity does not match the exact total capacity (previous installed + newly installed) of the previous time period because retirement
of capacity is considered.


Additional results at the regional level
The solutions of the proposed methods provide detailed information, both spatially and temporally, that cannot be presented. To give
an idea of the type of information the methods yield, we present some, still aggregated, results for a single region (PADD 3) in
Figures S15–S18 using method SR.


MODEL FORMULATION


Model size

The optimization model has equations describing material and energy balances through technologies, material transfers between
regions, demand satisfaction, capacity constraints and expansions; and a cost minimization objective function. The developed
MSSP model has 61,992 binary and 3.8 million continuous variables, and 990,559 constraints. Each pathway generated using the
RH approach requires the solution of 6 models, which means that the evaluation of the SR method, using 100 uncertainty realizations,
requires the execution of 600 runs.


Model objective
An optimization model for capacity expansion has been developed similar to Rathi et al. with the addition of constraints on transportation and trade between regions and learning of technologies based on cumulative installed capacity. [11] The objective of the model is
to minimize the expected costs of expanding liquid fuel producing and electricity generation facilities to meet demand over a 30-year
time horizon given that the demand for electricity and liquid fuels, carbon related policies, and capital costs of wind and solar technologies are uncertain.


Notation

We use lower case italicized roman characters as indices. Uppercase bold Roman characters are sets and subsets. Italicized Greek
characters are parameters. Uppercase italicized Roman characters are variables. The model is shown for both the deterministic
model and the stochastic model. The stochastic model is distinguished by having the parameters, variables, and equations that
are indexed by scenario shown in red.


Full model formulation

For the full model formulation, please refer to section SI 2. Both the deterministic and stochastic model are shown and
the stochastic model is distinguished by having the parameters, variables, and equations that are indexed by scenario shown

in red.


QUANTIFICATION AND STATISTICAL ANALYSIS


To complete the simulation-based method, random values were sampled from a triangular distribution centered around the expected
value for the energy demand, the credits related to carbon emission reduction, and the capital costs of onshore wind and photovoltaic
solar technologies. The average cost +/� SD of these simulations are shown in Figures 3 and 4.


iScience 27, 111288, December 20, 2024 e5


