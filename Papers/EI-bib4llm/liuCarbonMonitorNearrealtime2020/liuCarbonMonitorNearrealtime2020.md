# Citation Key: liuCarbonMonitorNearrealtime2020

---

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/liuCarbonMonitorNearrealtime2020/liuCarbonMonitorNearrealtime2020.pdf-0-0.png)
## **OPEN**

##### **Data Descriptor**



www.nature.com/scientificdata

# **Carbon Monitor, a near-real-time** **emission** **daily dataset of global CO 2** **from fossil fuel and cement** **production**


**[<PERSON>](http://orcid.org/0000-0002-8968-7050)** **[1,13]** [ ✉] **[, <PERSON>](http://orcid.org/0000-0001-8560-4943)** **[2,13]** [ ✉] **[, <PERSON>](http://orcid.org/0000-0002-6409-9578)** **[1,13]** **[, <PERSON>](http://orcid.org/0000-0002-9338-0844)** **[3]** [ ✉] **[, <PERSON>](http://orcid.org/0000-0001-8344-3445)** **[2]** **,**
**Yilong <PERSON>** **[4]** **, Duo Cui** **[1]** **, Biqing Zhu** **[1]** **[, Xinyu Dou](http://orcid.org/0000-0001-7783-6971)** **[1]** **[, Piyu Ke](http://orcid.org/0000-0003-0353-6313)** **[1]** **[, Taochun Sun](http://orcid.org/0000-0003-3640-6005)** **[1]** **[, Rui Guo](http://orcid.org/0000-0001-5536-0669)** **[1]** **,**
**Haiwang Zhong** **[5]** **, Olivier Boucher** **[6]** **[, François-Marie Bréon](http://orcid.org/0000-0003-2128-739X)** **[2]** **, Chenxi Lu** **[1]** **, Runtao Guo** **[7]** **,**
**Jinjun Xue** **[8,9,10]** **[, Eulalie Boucher](http://orcid.org/0000-0002-6070-2544)** **[11]** **[, Katsumasa Tanaka](http://orcid.org/0000-0001-9601-6442)** **[2,12]** **[& Frédéric Chevallier](http://orcid.org/0000-0002-4327-3813)** **[2]**


**We constructed a near-real-time daily CO** **2** **emission dataset, the Carbon Monitor, to monitor the**
**variations in CO** **2** **emissions from fossil fuel combustion and cement production since January 1, 2019,**
**at the national level, with near-global coverage on a daily basis and the potential to be frequently**
**updated. Daily CO** **2** **emissions are estimated from a diverse range of activity data, including the hourly**
**to daily electrical power generation data of 31 countries, monthly production data and production**
**indices of industry processes of 62 countries/regions, and daily mobility data and mobility indices**
**for the ground transportation of 416 cities worldwide. Individual flight location data and monthly**
**data were utilized for aviation and maritime transportation sector estimates. In addition, monthly**
**fuel consumption data corrected for the daily air temperature of 206 countries were used to estimate**
**the emissions from commercial and residential buildings. This Carbon Monitor dataset manifests**
**the dynamic nature of CO** **2** **emissions through daily, weekly and seasonal variations as influenced by**
**workdays and holidays, as well as by the unfolding impacts of the COVID-19 pandemic. The Carbon**
**Monitor near-real-time CO** **2** **emission dataset shows a 8.8% decline in CO** **2** **emissions globally from**
**January 1** **[st]** **to June 30** **[th]** **in 2020 when compared with the same period in 2019 and detects a regrowth of**
**CO** **2** **emissions by late April, which is mainly attributed to the recovery of economic activities in China**
**and a partial easing of lockdowns in other countries. This daily updated CO** **2** **emission dataset could offer**
**a range of opportunities for related scientific research and policy making.**


**Background & Summary**
The main cause of global climate change is the excessive anthropogenic emission of CO 2 to the atmosphere from
geological carbon reservoirs, the combustion of fossil fuel and cement production. Dynamic information on fossil
fuel-related CO 2 emissions is critical for understanding the impacts of different human activities and their variability on driving climate change. Emissions with high-temporal resolution help monitoring changes in emissions


1 Department of Earth System Science, Tsinghua University, Beijing, 100084, China. 2 Laboratoire des Sciences
du Climat et de l’Environnement (LSCE/IPSL), CEA-CNRS-UVSQ, Univ Paris-Saclay, Gif-sur-Yvette, France.
3 Department of Earth System Science, University of California, Irvine, 3232 Croul Hall, Irvine, CA, 92697-3100, USA.
4 Key Laboratory of Land Surface Pattern and Simulation, Institute of Geographical Sciences and Natural Resources
Research, Chinese Academy of Sciences, Beijing, China. [5] Department of Electrical Engineering, Tsinghua University,
Beijing, 100084, China. [6] Institute Pierre-Simon Laplace, Sorbonne Université/CNRS, Paris, France. [7] School of
Mathematical School, Tsinghua University, Beijing, 100084, China. [8] Center of Hubei Cooperative Innovation for
Emissions Trading System, Wuhan, China. [9] Faculty of Management and Economics, Kunming University of Science
and Technology, Kunming, China. [10] Economic Research Centre of Nagoya University, Furo-cho, Chikusa-ku, Nagoya,
Japan. [11] Université Paris-Dauphine, PSL, Paris, France. [12] Center for Global Environmental Research, National
Institute for Environmental Studies, Tsukuba, Japan. [13] These authors contributed equally: Zhu Liu, Philippe Ciais,
Zhu Deng. [✉] [e-mail: <EMAIL>; <EMAIL>; <EMAIL>](mailto:<EMAIL>)



Scientific **Data** | _(2020) 7:392_ [| https://doi.org/10.1038/s41597-020-00708-7](https://doi.org/10.1038/s41597-020-00708-7) 1


www.nature.com/scientificdata/ www.nature.com/scientificdata


from activity data, during the COVID-19 and for likely weaker signals thereafter. Furthermore, the combustion
processes of fossil fuel also emit short-lived pollutants such as SO 2, NO 2 and CO, and capturing these data would
also allow a more accurate quantification and better understanding of air quality changes [1][,][2] . Estimates of CO 2
emissions from fossil fuel combustion and cement production [2][–][7] are based on both activity data (e.g., the amount
of fuel burnt or energy produced) and emissions factors (see Methods) [8] . The sources of these data are mainly
national energy statistics, although a number of databases, such as CDIAC, BP, EDGAR, IEA and GCP, also
produce and compile estimates for different groups of countries or for all countries [1][,][9][–][11] . Fossil fuel-related CO 2
emissions are usually reported on an annual basis but the data lag by at least one year.
The uncertainty associated with CO 2 emissions from burning fossil fuel and producing cement is small when
considering large emitters or the global totals: smaller than that of co-emitted combustion-related pollutants,
for which uncertain technological factors influence the ratio of emitted amounts to fossil fuel burnt [12][–][14] . The
uncertainty of global carbon emissions from fossil fuel burning and cement production varies between ±6% and
±10% [2][,][6][,][15][,][16] (±2σ), and it is attributed to both the activity data and the emissions factors. For the activity data,
the amount of fuel burnt is captured by energy production and consumption statistics; hence, the uncertainties
are introduced by errors and inconsistencies in the reported figures from different sources. For the emissions
factors, the different fuel types, quality and combustion efficiency together contribute to the overall uncertainty.
For example, coal used in China is of variable quality, and its emission factors, both before cleaning (raw coal) and
after (cleaned coal), vary significantly, which was found to cause a 15% uncertainty range for CO 2 emissions [17] . On
the other hand, there is very limited temporal change in emission factors. For example, the annual difference in
emission factors for coal consumption was within 2% globally [17][–][20], while the variation in emission factors for oil
and gas was found to be much smaller.
Given that the uncertainty of CO 2 emissions from fossil fuel burning and cement production is generally
below ±10% [9][,][21][,][22] and the annual difference in emission factors is less than 2% [17], the CO 2 emissions can be estimated directly by estimating the absolute amount of and the relative change in activity over time. This method
has been widely used for scientific products that update recent changes in CO 2 emissions estimates [1][,][23][–][25], given
that official and comprehensive CO 2 national inventories reported by countries to the UNFCCC become available with a lag of two years for Annex-I countries and several years for non-Annex-I countries [4] . As such, a higher
spatial, temporal and sectoral resolution of CO 2 emission inventories beyond annual and national levels can be
obtained by using spatial, temporal and sectoral data to disaggregate annual national emissions [8][,][13][,][25][,][26] . The level
of granularity in spatially explicit dynamic emission inventories depends on the available data, such as the location and operations of sources [25] (i.e., power generation for a certain plant), regional statistics of energy use (i.e.,
monthly fuel consumption) [8][,][26], and knowledge about proxies for the distribution of emissions such as gridded
population density, night lights, urban forms and GDP data [8][,][13][,][25][,][26] .
Gaining from past experiences in constructing annual inventories and newly compiled activity data, we present in this study a novel daily dataset of CO 2 emissions from fossil fuel burning and cement production at the
national level. The countries/regions include China, India, the US, EU27 & UK, Russia, Japan, Brazil, and the rest
of the world (ROW), as well as the emissions from international bunkers. This dataset, known as Carbon Monitor
[(data available at https://carbonmonitor.org/), is separated into several key emission sectors: power sector (39%](https://carbonmonitor.org/)
of total emissions), industrial production (28%), ground transport (19%), air transport (3%), ship transport (2%),
and residential consumption (10%). For the first time, daily emissions estimates are produced for these six sectors
based on dynamically and regularly updated activity data. This is made possible by the availability of recent activity data such as hourly electrical power generation, traffic indices, airplane locations and natural gas distribution,
with the assumption that the daily variation in emissions is driven by the activity data and that the contribution
from emission factors is negligible, as they evolve at longer time scales, e.g., from policy implementation and
technology shifts.
The framework of this study is illustrated in Fig. 1. We calculated national CO 2 emissions and international
aviation and shipping emissions since January 1, 2019, drawing on hourly datasets of electric power production
and the associated CO 2 emissions in 31 countries (thus including the substantial variations in carbon intensity
associated with the variable mix of electricity production), daily vehicle traffic indices in 416 cities worldwide,
monthly production data for cement, steel and other energy intensive industrial products in 62 countries/regions,
daily maritime and aircraft transportation activity data, and previous-year fuel use data corrected for air temperature for residential and commercial buildings, covering over 70% of global power and industry emissions, 85%
of ground transportation emissions, 100% of residential and international bunker emissions, respectively. We
also inferred the emissions from ROW (see Methods) where data are not directly available thus covering 100% of
global CO 2 emissions. Together, these data cover almost all fossil fuels and industry sources of global CO 2 emissions, except for emissions from land use change (up to 10% of global CO 2 emissions), and the non-fossil fuel CO 2
emissions of industrial products (up to 2% of global CO 2 emissions) [27] and of cement and clinker (e.g., plate glass,
ammonia, urea, calcium carbide, soda ash, ethylene, ferroalloys, alumina, lead and zinc).
While daily emissions can be directly calculated using near-real-time activity data and emission factors for
the electricity power sector, such an approach is difficult to apply to all sectors. For the industrial sector, emissions can be estimated monthly in some countries. For the other sectors, we used proxy data instead of daily real
activity data to dynamically downscale the annual or monthly CO 2 emissions totals to a daily basis. For instance,
traffic indices in cities representative of each country were used instead of actual vehicle counts and categories,
combined with annual national total sectoral emissions, to produce daily road transportation emissions. As such,
for the use of fuels in the road transportation, air transportation and residential sectors in most countries, we
downscaled monthly or annual total emissions data in 2019 to calculate the daily CO 2 emissions in that year.
Subsequently, we scaled monthly totals of 2019 by daily proxies of activities to obtain daily CO 2 emissions data
for the first four months of 2020 during the unprecedented disruption of the COVID-19 pandemic. The Carbon
Monitor near-real-time CO 2 emission dataset shows a 8.8% decline in CO 2 emissions globally from January 1 [st]


Scientific **Data** | _(2020) 7:392_ [| https://doi.org/10.1038/s41597-020-00708-7](https://doi.org/10.1038/s41597-020-00708-7) 2


www.nature.com/scientificdata/ www.nature.com/scientificdata

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/liuCarbonMonitorNearrealtime2020/liuCarbonMonitorNearrealtime2020.pdf-2-1.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/liuCarbonMonitorNearrealtime2020/liuCarbonMonitorNearrealtime2020.pdf-2-2.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/liuCarbonMonitorNearrealtime2020/liuCarbonMonitorNearrealtime2020.pdf-2-9.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/liuCarbonMonitorNearrealtime2020/liuCarbonMonitorNearrealtime2020.pdf-2-11.png)


**Fig. 1** Framework for data processing.


120



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/liuCarbonMonitorNearrealtime2020/liuCarbonMonitorNearrealtime2020.pdf-2-12.png)



110


100


90


80


70















![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/liuCarbonMonitorNearrealtime2020/liuCarbonMonitorNearrealtime2020.pdf-2-10.png)

**Date**


**Fig. 2** Daily CO 2 emissions data from January 1 [st], 2019 to June 30 [th], 2020.


to June 30 [th] in 2020 when compared with the same period in 2019 (Fig. 2), and detects a regrowth of CO 2 emissions by late April, which is mainly attributed to the recovery of economic activities in China and partial easing
of lockdowns in other countries (for a more in-depth analysis of this topic, please see our recent related paper [28] ).


**Methods**
**Annual total and sectoral emissions per country in the baseline year 2019.** According to the
IPCC guidelines for emissions reporting [4], the CO 2 emissions _Emis_ should be calculated by multiplying the activity data _AD_ by the corresponding emissions factors _EF_

#### Emis = ∑∑∑ AD i j k,, ⋅ EF i j k,, (1)


where _i_, _j_, _k_ are indices for regions, sectors and fuel types, respectively. _EF_ can be further separated into the net
heating values _v_ for each fuel type (the energy obtained per unit of fuel), the carbon content _c_ per energy output
(t C/TJ) and the oxidization rate _o_ (the fraction (in %) of fuel oxidized during combustion):

#### Emis = ∑∑∑ AD i j k,, ⋅ ( v i j k,, ⋅ c i j k,, ⋅ o i j k,, ) (2)


Due to the lag of more than two years in the publishing governmental energy statistics, we started from the
most recent CO 2 emissions estimates up to 2018 from current CO 2 databases [1][,][9][–][11] . For 2019, we completed this
information by obtaining annual total emissions based on data in the literature and disaggregated the annual total


Scientific **Data** | _(2020) 7:392_ [| https://doi.org/10.1038/s41597-020-00708-7](https://doi.org/10.1038/s41597-020-00708-7) 3


www.nature.com/scientificdata/ www.nature.com/scientificdata

|Countries/Regions|Scaling Factor (%)|Source|
|---|---|---|
|China|2.8%|Estimated in this study|
|India|1.8%|Global Carbon Budget 201924|
|US|2.4%|Carbon Brief, 202033|
|EU27&UK|−3.9%|Carbon Brief, 202033|
|Russia|0.5%|=ROW|
|Japan|0.5%|=ROW|
|Brazil|0.5%|=ROW|
|ROW|0.5%|Global Carbon Budget 201924|



**Table 1.** Scaling factor for the emission growth in 2019 compared to 2018.


into daily emissions (see below). For 2020, we estimated daily CO 2 emissions by using daily changes in activity
data in 2020 compared to 2019. The CO 2 emissions and sectoral structure in 2018 for countries and regions were
extracted from EDGAR V4.3.2 [1] and V5.0 [29] for each country, and national emissions were scaled to 2019 based
on our own estimate (for China) and data from the Global Carbon Budget 2019 [24] (for other countries) (Table 1):


_Emis_ _r_,2019 = _α_ _r_ ⋅ _Emis_ _r_,2018 (3)

For China, we first calculated CO 2 emissions in 2018 based on the energy consumption by fuel type and for
cement production in 2018 from the China Energy Statistical Yearbook [30] and the National Bureau Statistics [31],
following Eq. 1. We projected the energy consumption in 2019 from the annual growth rates of coal, oil and gas
reported by the Statistical Communiqué [31] and applied China-specific emission factors [17] to obtain the annual
growth rate of emissions in 2019. We projected China’s CO 2 emissions based on our previous studies on the
country-specific emission factor data in China [17] and past trends of China’s CO 2 emissions [17][,][32] . Our projection
(2.6%) is similar to the GCP revised projection [33] (2.0%), and the slight difference falls into the uncertainty range
of the both estimations. For the US and EU27&UK, we used updated emissions growth rates in 2019 reported
by CarbonBrief [33] . For countries with no estimates for emissions growth rates in 2019, such as Russia, Japan and
Brazil, we assumed that their growth rate was 0.5% based on the emission growth rate of the rest of the world [24] .
As the result, our daily average CO 2 estimates in 2019 (98.2 Mt CO 2 per day) are lower than the daily average CO 2
estimates in 2019 from GCP (around 100 Mt CO 2 per day). The discrepancies mainly came from the difference
between EDGAR and GCP databases, showing a 2.1% global difference between these two databases in 2018.
In this study, the EDGAR detailed sectors were aggregated into several larger sectors ( _s_ ): power sector, industrial sector, transport sector (ground transport, aviation and shipping), and residential sector, and the percentage
estimates for the various sectors are derived from the EDGAR database. This is consistent with the new activity
data we used below to compute daily variations. We used the sectoral distribution in 2018 from EDGAR to infer
the sectoral emissions in 2019 for each country/region (Eq. 4), assuming that the sectoral distribution remained
unchanged in these two years.



_Emis_
_Emis_ = _Emis_ ⋅



,,2019 = _Emis_ _r_,2019 ⋅ _r s_,,2018

_Emis_ _r_,2018



_r s_,,2019 = _Emis_ _r_,2019 ⋅ _r s_,,
_Emis_ _r_,2018 (4)



,2018



**Data acquisition and processing of carbon monitor daily CO** **2** **emissions.** According to IPCC
Guidelines [4], the CO 2 emissions for each sector can be calculated by multiplying sectoral activity data by their
corresponding emission factors following Eq. 5:


_Emis_ _s_ = _AD_ _s_ ⋅ _EF_ _s_ (5)

The emissions were calculated following this equation separately for the power sector, the industrial sector, the
transport sector, and the residential sector.


**Power sector.** The CO 2 emissions from the power sector can be calculated by adapting Eq. 5 with
sector-specific activity data (i.e., electricity production in Russia and thermal electricity production in other
countries) and the corresponding emission factors (Eq. 6):


_Emis_ _power_ = _AD_ _power_ ⋅ _EF_ _power_ (6)


Normally, the emission factors change slightly over time but can be assumed to remain constant over the
two-year period considered in this study compared to the large changes in activity data. We present uncertainties
due to the changes of fuel mix in thermal production (see Technical Validation). Thus, we assumed that emission
factors remained unchanged in 2019 and 2020 and calculated the daily emissions as follows:



_AD_



= _Emis_ ⋅ _daily_

_daily_ _yearly_
_AD_ _yearly_ (7)



_Emis_ = _Emis_ ⋅



_yearly_



For China, we estimate the daily thermal production _AD_ _daily_ from the daily disaggregation of monthly thermal
emissions _AD_ _monthly_ by using daily coal consumption by six power companies in China _C_ _daily_ as follow:


Scientific **Data** | _(2020) 7:392_ [| https://doi.org/10.1038/s41597-020-00708-7](https://doi.org/10.1038/s41597-020-00708-7) 4


www.nature.com/scientificdata/ www.nature.com/scientificdata











|Country/<br>Region|Data source|Sectors included|Resolution|
|---|---|---|---|
|China|National Bureau of Statistics (https://data.stats.gov.<br>cn/) / WIND (https://www.wind.com.cn/)|Termal production /Daily coal consumption for<br>6 power companies|Monthly/daily|
|India|Power System Operation Corporation Limited<br>(https://posoco.in/reports/daily-reports/)|Termal production (summarizing the<br>production of_Coal_, _Lignite_, and_Gas, Naphtha_<br>_& Diesel_)|Daily|
|US|Energy Information Administration’s (EIA) Hourly<br>Electric Grid Monitor (https://www.eia.gov/beta/<br>electricity/gridmonitor/)|Termal production (summarizing the<br>production of_Coal, Petroleum_, and_Natural Gas_)|Hourly|
|EU27 & UK|ENTSO-E Transparent platform (https://<br>transparency.entsoe.eu/dashboard/show)|Termal production (summarizing the<br>production of_Fossil.Brown. coal.Lignite, Fossil._<br>_Coal.derived.gas,Fossil.Gas, Fossil.Hard.coal,_<br>_Fossil.Oil, Fossil.Oil.shale_, and_Fossil.Peat_)|Hourly|
|Russia|United Power System of Russia (http://www.so-ups.<br>ru/index.php)|Total generation|Hourly|
|Japan|Organization for Cross-regional Coordination of<br>Transmission Operators (OCCTO) (https://www.<br>occto.or.jp/en/)|Termal generation|Hourly|
|Brazil|Operator of the National Electricity System (http://<br>www.ons.org.br/Paginas/)|Termal production|Hourly|


**Table 2.** Data sources of activity data in the power sector.


_AD_ _daily_ = _AD_ _monthly_ ⋅ ( _C_ _daily_ / _C_ _monthly_ ) (8)


2
The data sources of daily activity data in the power sector are described in Table . The countries/regions listed
in Table 2 account for more than 74% of the total CO 2 emissions in the power sector. For emissions from other
countries (ROW), which are not listed in Table 2, we estimated the power sector emission changes in 2020 based
on periods of the national lockdown. For daily emission changes for the ROW in 2019, we first assumed a linear
relationship between daily global emissions and daily total emissions for the countries listed in Table 2. Then,
we classified each country according to whether it adopted lockdown measures based on official reports. Based
on daily emissions data for the power sector of the countries listed in Table 2, we calculated the average percent
change _α_ of power sector emissions across those countries during their lockdown periods, and used it to estimate
the emission reduction of each country in the rest of the world during their specific national lockdowns (Eq. 9,
where c denotes a country in ROW and d denotes day of 2020), and aggregated them into daily emissions for each
ROW country.


_Emis_ _c d c_, ( ) = _Daily Mean Emis_ _c_,2019 × (1 − _α_ ), ( ) _d c_ ∈ _lockdown period inc_ (9)


**Industrial sector: Industrial and cement production.** While daily production data are not directly
available for industrial and cement production, the monthly CO 2 emissions from the industrial and cement production sectors can be calculated by using monthly statistics of industrial production and daily data of electricity
generation to disaggregate the monthly CO 2 emissions into daily values. This calculation assumes a linear relationship between daily electricity generation for industry and daily industry production data to compute daily
industry production.
The emissions from industrial production during fossil fuel combustion were calculated by multiplying the
activity data (i.e., fossil fuel consumption data in the industrial sector) by the corresponding emissions factors
by the type of fuel. Due to limited data availability, we assumed a linear relationship between daily industrial
production and industrial fossil fuel use, and the emission factors remained unchanged. Therefore, the monthly
emissions in 2019 for a country/region can be calculated by the following equation:


_Emis_ _montly_,2019, _r_ = _Emis_ _yearly_,2019, _r_ ⋅ ( _P_ _monthly_,2019, _r_ / _P_ _yearly_,2019,, _i r_ ) (10)

The emissions from cement production during the chemical process of calcination of calcite were also calculated with Eq. (10).
Specifically, for China, the emissions from the industrial sector were further divided into those for the steel,
cement, chemical, and other industries (indicated by index _i_ ):



_E_



_Emis_ _montly_,2019, _China_ = ∑ _mis_ _yearly_,2019, _i_ ⋅ ( _P_ _monthly_,2019, _i_ / _P_ _yearly_,2019, _i_ ) (11)



,2019, _China_ _yearly_,2019, _i_ _monthly_,2019, _i_ _yearly_,2019, _i_



For the monthly emissions in 2020 for a country/region, we used the following equation:


_Emis_ _montly_,2020, _r_ = _Emis_ _monthly_,2019, _r_ ⋅ ( _P_ _monthly_,2020, _r_ / _P_ _monthly_,2019, _r_ ) (12)

where _P_ is the industrial production for different industrial sectors (in China) or the total industrial production
index (in other countries), as listed in Table 3. In China’s case, the January and February estimates were combined,
as individual monthly data were not reported by the sources listed in Table 3 for these two months. The monthly
industrial emissions were disaggregated to daily emissions using daily electricity data, as explained above.


Scientific **Data** | _(2020) 7:392_ [| https://doi.org/10.1038/s41597-020-00708-7](https://doi.org/10.1038/s41597-020-00708-7) 5


www.nature.com/scientificdata/ www.nature.com/scientificdata


Lacking the latest Industrial Production Index for June 2020 for the EU27 & UK and India, we adopted
[monthly growth rates of industrial output from Trading Economics (https://tradingeconomics.com) based on](https://tradingeconomics.com)
preliminary survey data. CO 2 emissions from countries listed in Table 3 accounts for more than 71% of the global
industrial emissions. For other countries not listed in Table 3, we used the same method described for the power
sector to calculate the daily industry emissions from the ROW.
To allocate monthly emissions into daily emissions, we assume the linear relationship between daily industry
activity and daily electricity production, and use the weight of daily electricity production to monthly electricity
production:


_Emis_ _daily_ = _Emis_ _monthly_ ⋅ ( _Elec_ _daily_ / _Elec_ _monthly_ ) (13)


**Transport sector.** _Ground transportation_ . We collected hourly congestion level data from the TomTom
[website (https://www.tomtom.com/en_gb/traffic-index/). The congestion level (hereafter called](https://www.tomtom.com/en_gb/traffic-index/) _X_ ) represents the
extra time spent on a trip, as a percentage, compared to under uncongested conditions. TomTom congestion level
data were obtained for 416 cities across 57 countries (Only-online Table 1) at a temporal resolution of one hour.
Of note, a zero-congestion level means that the traffic is fluid or ‘normal’ but does not mean there are no vehicles
and zero emissions. It is thus important to identify the lower threshold of emissions when the congestion level is
zero. To do so, we compared the time series of daily mean TomTom congestion level _X_ with the daily mean car flux
(in vehicles per day) from publicly available real-time _Q_ data from an average of 60 roads in the megacity area of
[Paris. The daily mean car counts were reported by the city’s service (https://opendata.paris.fr/pages/home/<USER>//opendata.paris.fr/pages/home/<USER>
used a sigmoid function to fit the relationship between _X_ and _Q_ :



_c_



_bX_ _c_
_Q_ = _a_ + _d_ _c_ + _X_ _c_ (14)



_c_ _c_



where a, b, c and d are the regression parameters (Table 4). We verified that the empirical fit from Eq. 14 can
reproduce the observed large drop in _Q_ due to the lockdown in Paris and the recovery afterwards. We assume
that relative changes in daily emissions were proportional to the relative change in the function _Q_ ( _X_ ) from Eq. 14.
Then, we applied the function _Q_ ( _X_ ) established for Paris to other cities included in the TomTom dataset, assuming
that the relative magnitude of car counts (and thus emissions) follows a similar relationship with TomTom. The
emission changes were first calculated for individual cities and then weighted by city emissions for aggregation
_i_ with _n_
to national changes. For a specific country cities reported by TomTom, the national daily vehicle flux for
day _j_ was given by:



= [∑] _ni_ =1 _Q_ _i da_, _yj_ _E_ _i_

_country dayj_, _n_

∑ _i_ =1 _E_ _i_ (15)



= [∑]



= _i_ =1 _i da_,

, ∑ _ni_ =1 _E_ _i_



=



_Q_ _E_



_i da_, _yj_ _i_



_Q_ _country dayj_, = _n_

∑ =



_n_
_i_



∑



1



where _E_ _i_ is the annual road transportation emissions of city _n_ taken at the grid point of each TomTom city from
[the annual gridded EDGARv4.3.2 emissions map for the “road transportation” sector (1A3b) (https://edgar.jrc.](https://edgar.jrc.ec.europa.eu/)
[ec.europa.eu/) for the year 2010, assuming that the spatial distribution of ground transport did not change signif-](https://edgar.jrc.ec.europa.eu/)
icantly within a country between 2010 and the period of this study. Then, the daily road transportation emissions
in 2019 and 2020 ( _E_ _country dayj_, ) for a country were scaled such that the total road transportation emissions in the
first half year of 2019 equaled 182/365 times the annual emissions of this sector in 2019 ( _E_ _country_,2019 ) estimated in
this study:



182/365



×



_E_



, = _Q_ _country dayj_, 182 _country_,2019

∑ _j_ =1 _Q_ _country dayj_, (2019)



_country dayj_, = _Q_ _country dayj_, 182 _country_,2019

∑ _j_ =1 _Q_ _country dayj_, (2019) (16)



_E_ _country dayj_, = _Q_ _country dayj_, 182
∑ =



=



182 _j_ =1 _Q_ _country dayj_, (2019)



_Q_



[The TomTom GPS products include devices for car (https://www.tomtom.com/en_us/drive/car/), motorcycle](https://www.tomtom.com/en_us/drive/car/)
[(https://www.tomtom.com/en_us/drive/motorcycle/) and large vehicles (https://www.tomtom.com/en_us/drive/](https://www.tomtom.com/en_us/drive/motorcycle/)
[truck/). Although we did not find more information about the details how the congestion index is calculated,](https://www.tomtom.com/en_us/drive/truck/)
we believe that the calculation of congestion level includes the data from private and commercial cars, light and
heavy vehicles. In this study, we did not compute the emissions separately for light and heavy vehicles separately, because 1) the EDGAR emission product for “road transport” (sector 1A3b), which we used as a reference
emission product, did not separate these two different types of vehicles; and 2) the TomTom congestion level is
not reported for light and heavy vehicles separately. So we implicitly assume that they similarly scale with the
TomTom congestion level.
For countries not included in the TomTom dataset, we assumed that the emissions changes follow the mean
changes of other countries. For example, Cyprus, as an EU member country, had no city reported in the TomTom
dataset, so its relative emissions change was assumed to follow the same pattern for total emissions from other
EU countries included in the TomTom dataset (which covers 98% of total EU emissions). Similarly, the relative
changes in emissions for countries in the ROW but not reported by TomTom were assumed to follow the same
pattern as the total emissions from all TomTom reported countries (which cover 85% of global total emissions).


_Aviation._ CO 2 emissions from commercial aviation are usually reconstructed from bottom-up emission inventories based on knowledge of the parameters of individual flights [34][,][35] . We also calculated the CO 2 emissions from
[commercial aviation following this approach. Individual commercial flights are tracked by Flightradar24 (https://](https://www.flightradar24.com)
[www.flightradar24.com) based on ADS-B signals emitted by aircraft and received by their network of ADS-B](https://www.flightradar24.com)


Scientific **Data** | _(2020) 7:392_ [| https://doi.org/10.1038/s41597-020-00708-7](https://doi.org/10.1038/s41597-020-00708-7) 6


www.nature.com/scientificdata/ www.nature.com/scientificdata















|Country/<br>Region|Sector|Data|Data source|
|---|---|---|---|
|China|Steel industry|Crude steel production|World Steel Association website (https://www.<br>worldsteel.org/)|
|China|Cement industry|Cement and clinker production|National Bureau of Statistics (http://www.stats.<br>gov.cn/english/)|
|China|Chemical industry|Sulfuric acid, caustic soda, soda ash, ethylene, chemical<br>fertilizer, chemical pesticide, primary plastic and<br>synthetic rubber|National Bureau of Statistics (http://www.stats.<br>gov.cn/english/)|
|China|Other industry|Crude iron ore, phosphate ore, salt, feed, refned edible<br>vegetable oil, fresh and frozen meat, milk products,<br>liquor, sof drinks, wine, beer, tobaccos, yarn, cloth,<br>silk and woven fabric, machine-made paper and<br>paperboards, plain glass, ten kinds of nonferrous metals,<br>refned copper, lead, zinc, electrolyzed aluminum,<br>industrial boilers, metal smelting equipment, and cement<br>equipment|National Bureau of Statistics (http://www.stats.<br>gov.cn/english/)|
|India|/|Industrial Production Index (IPI)|Ministry of Statistics and Programme<br>Implementation (http://www.mospi.<br>nic.in) Trading Economics (https://<br>tradingeconomics.com)|
|US|/|Industrial Production Index (IPI)|Federal Reserve Board (https://www.<br>federalreserve.gov)|
|EU & UK|/|Industrial Production Index (IPI)|Eurostat (https://ec.europa.eu/eurostat/<br>home) Trading Economics (https://<br>tradingeconomics.com)|
|Russia|/|Industrial Production Index (IPI)|Federal State Statistics Service (https://eng.<br>gks.ru)|
|Japan|/|Industrial Production Index (IPI)|Ministry of Economy, Trade and Industry<br>(https://www.meti.go.jp)|
|Brazil|/|Industrial Production Index (IPI)|Brazilian Institute of Geography and Statistics<br>(https://www.ibge.gov.br/en/institutional/<br>the-ibge.htm)|


**Table 3.** Data sources for indust^prial production.


receptors. As we do not yet have the capability to convert the FlightRadar24 database into CO 2 emissions on
a flight-by-flight basis, we compute CO 2 emissions by assuming a constant _EF_ _aviation_ (CO 2 emission factor per
km flown) across the whole fleet of aircraft (regional, narrowbody passenger, widebody passenger and freight
operations). This assumption is reasonable if the flight mix between these categories has not changed significantly between 2019 and 2020.The International Council on Clean Transportation (ICCT) published that CO 2
emissions from commercial freight and passenger aviation resulted in 918 Mt CO 2 in 2018 [36] based on the OAG
flight database and emission factors from the PIANO database. IATA estimated a 3.4% increase between 2018 and
2019 in terms of available seat kilometers [37] . In the absence of further information, we consider this increase to be
representative of freight aviation as well and use a slightly smaller growth rate of 3% for CO 2 emissions between
2018 and 2019 to account for a small increase in fuel efficiency. The kilometers flown are computed assuming
great circle distance between the take-off, cruising, descent and landing points for each flight and are cumulated
over all flights. The FlightRadar24 database has incomplete data for some flights and may completely miss a small
fraction of actual flights, so we scale the ICCT estimate of CO 2 emissions (inflated by 3% for 2019) with the total
estimated number of kilometers flown for 2019 (67.91 million km) and apply this scaling factor to 2020 data.
Again, this assumes that the fraction of missed flights is the same in 2019 and 2020. As the departure and landing
airports are known for each flight, we can classify the km flown (and hence the CO 2 emissions) per country and
for each country between domestic or international traffic. The daily CO 2 emissions were computed as the product of distance flown by a CO 2 emission factor per km flown, according to:


_Daily Emis_ _aviation_ = _Daily Kilometers Flown_ _aviation_ 2020 × _EF_ _aviation_ 2019 (17)


_Ships._ We collected international CO 2 shipping emissions for 2016–2018 based on EDGAR’s international emissions. We also collected global shipping emissions during the period of 2007–2015 from IMO [38] and ICCT [39] .
According to the Third IMO GHG Study [38], CO 2 emissions from international shipping accounted for 88% of
global shipping emissions, and domestic and fishing emissions accounted for 8% and 4%, respectively. We calculated international CO 2 shipping emissions from 2007–2015 from global shipping emissions and the ratio of
international to global shipping emissions. We extrapolated emissions from linear fits from 2007–2018 to estimate
the emissions in 2019. The data sources of shipping emissions are listed in Table 5. We obtained emissions for the
first half year of 2019 byassuming the equal distribution of monthly shipping CO 2 emissions. The equations are
as follows:


_Monthly Emis_ _international shipping_,2019 = _α_ × _Yearly Emis_ _international shipping_,2019 × _R_ _month_ (18)


_α_ is the increasing rate of international shipping emissions in 2019 based on the linear extrapolation of data
from the period 2007–2018, estimated to be 3.01%. _R_ _month_ represents the ratio of the months to be calculated in


Scientific **Data** | _(2020) 7:392_ [| https://doi.org/10.1038/s41597-020-00708-7](https://doi.org/10.1038/s41597-020-00708-7) 7


www.nature.com/scientificdata/ www.nature.com/scientificdata

|Parameter|Value|
|---|---|
|_a_|100.87|
|_b_|671.06|
|_c_|1.98|
|_d_|6.49|



**Table 4.** Regression parameters of the sigmoid function of Eq. 14 that describes the relationship between car
counts ( _Q_ ) and TomTom congestion level ( _X_ ).


the whole year. Given this, we estimated the shipping emissions for the first half year of 2019 using _R_ _month_ equal to
181/365.
We assumed that the change in shipping emissions was linearly related to the change in ship traffic volume.
The change in international shipping emissions for the first half year of 2020 was calculated according to the
following equation:


_Emis_ _period_,2020 = _Emis_ _period_,2019 × _C_ _index_ (19)


where _c_ _index_ represents the ratio of the change in shipping emissions, estimated to the end of April as −25% compared to the same period last year according to the news report [40] .


**Residential sector: residential and commercial buildings.** Fuel consumption daily data from this
sector are not available. Several studies [41][,][42] showed that the main source of daily and monthly variability in this
sector is climate, namely, heating emissions increase when temperature falls below a threshold that depends on
the region, building type and people’s habits. We calculated emissions by assuming annual totals unchanged from
2019 and using daily climate information in three steps: 1) estimate population-weighted heating degree days
for each country and for each day based on the ERA5 [43] reanalysis of 2-meter air temperature, 2) split residential
emissions into two parts: cooking emissions and heating emissions based on the EDGAR database [29] and using the
EDGAR estimates of 2018 residential emissions as the baseline. Emissions from cooking were assumed to remain
independent of temperature, and those from heating were assumed to be a function of the heating demand. Based
on the change in population-weighted heating degree days in each country in 2019 and 2020, we downscaled
annual EDGAR 2018 residential emissions to daily values for 2019 and 2020 as described by Eqs. 20–22:



∑
= _Emis_ ×



_Emis_ _c m_, = _Emis_ _c m_,,2018 × ∑ m _HDD_
∑



_c m_, = _Emis_ _c m_,,2018 × m _c d_,

∑ m,2018 _HDD_ _c d_, (20)



, = _Emis_ _c m_,,2018 × m,

∑ _HDD_ _c d_



m,2018,



_Emis_ _c d_, = _Emis_ _c m_, × _Ratio_ _heating c m_,, × _HDD_ _c d_, + _Emis_ _c m_, × (1 − _Ratio_ _heating c m_,, ) ×
∑ _HDD_ _c d_



_c d_, + _Emis_ _c m_, × (1 − _Ratio_ _heating c m_,, ) × 1

_HDD_ _N_



_c d_, = _Emis_ _c m_, × _Ratio_ _heating c m_,, × _c d_, + _Emis_ _c m_, × (1 − _Ratio_ _heating c m_,, ) ×

∑ m _HDD_ _c d_, _N_ _m_ (21)



, = _Emis_ _c m_, × _Ratio_ _heating c m_,, ×, + _Emis_ _c m_, × (1 − _Ratio_ _heating c m_,, ) ×

∑ m _HDD_ _c d_ _N_ _m_



_c m_, _heating c m_,,



,,,



_c d_



m,


###### HDD c d, = ∑ ( Pop grid ∑ ×( Pop ( T grid c d,, ) − 18 ) )



( _Pop_ × _T_ − 18 )



_c d_, = _grid_ ∑ ( _Pop_ _grid c dgrid_,, ) (22)



= _g_,,

, ∑ ( _Pop_ _grid_ )



_Pop_



where _c_ is country, _d_ is day, _m_ is month, _Emis_ _c m_, [ is the residential emissions of country ] _[c]_ [ in month ] _[m]_ [ of year 2019 ]
or 2020, _Emis_ _c m_,,2018 [is the emissions of country ] _[c]_ [ in month ] _[m]_ [ of year 2018, ] _[HDD]_ _c d_, [ is the population-weighted ]
heating degree day in country _c_ in day _d_, _Emis_ _c d_, is the residential emissions of country _c_ in day _d_ of year 2019 or
2020, _Ratio_ _heating c m_,, [ is the percentage of residential emissions from heating demand in country ] _[c]_ [ in month ] _[m]_ [, ] _[N]_ _[m]_
is the number of days in month _m_, _Pop_ _grid_ is gridded population data derived from Gridded Population of the
World, Version 4 [44], _T_ is the daily average air temperature at 2 meters derived from ERA5 [43], and 18 is a HDD reference temperature [13] of 18 °C.
The main assumption in this approach is that residential emissions did not change based on factors other than
heating degree day variations in 2020, although people’s time at home dramatically increased during the lockdown period. To test the validity of this assumption, we compiled natural gas daily consumption data by residen[tial and commercial buildings for France (https://www.smart.grtgaz.com/fr/consommation) (unfortunately, such](https://www.smart.grtgaz.com/fr/consommation)
data could not be collected in many countries) during 2019 and 2020 (Fig. 3). Natural gas consumption in kWh
per day was transformed to CO 2 emissions using an emission factor of 10.55 kWh per m [3] and a molar volume of
22.4 10 [−][3] m [3] per mole.
First, we verified that the temporal variation in those ‘true’ residential CO 2 emissions was similar to that given
by Eqs. 20–22. Second, after fitting a piecewise model to those natural gas residential emission data using ERA5
air temperature data, we removed the effect of temperature to obtain emissions corrected for temperature effects.
Even if the lockdown was very strict in France, we found no significant emissions anomaly, meaning that although
nearly the entire population was confined at home, it did not increase or decrease emissions. This complementary
analysis tentatively suggests that residential emissions can be well approximated in other countries by Eqs. 20–22
based only on temperature during the lockdown period.


Scientific **Data** | _(2020) 7:392_ [| https://doi.org/10.1038/s41597-020-00708-7](https://doi.org/10.1038/s41597-020-00708-7) 8


www.nature.com/scientificdata/ www.nature.com/scientificdata

|Shipping Emissions|Sources|
|---|---|
|Global shipping emissions (2007–2012)|IMO38|
|Global shipping emissions (2013–2015)|ICCT39|
|International shipping emissions (2016–2018)|EDGAR v5.013|



**Table 5.** Data sources used to estimate ship emissions.


**Data Records**
[Currently, there are 36,177 data records provided in this dataset, which can be downloaded at our website (https://](https://carbonmonitor.org)
[carbonmonitor.org) and Figshare](https://carbonmonitor.org) [45] :


                     - A total of 270 records are daily mean CO 2 emissions (from fossil fuel combustion and cement production
processes) 1751–2020.

                     - A total of 4,923 records are the daily emissions for 9 countries/regions (i.e. China, India, US, EU27 & UK,
Russia, Japan, Brazil, ROW and World) and 547 days (from January 1, 2019 to June 30 [th], 2020).

                     - A total of 3,829 records are the daily emissions for 6 sectors and the global total (i.e. power, industry, residential, ground transportation, aviation (including domestic aviation and international aviation), international
shipping and Globe) and 547 days (from January 1, 2019 to June 30 [th], 2020).

                     - A total of 27,155 records are daily emissions of 9 countries/regions (i.e. China, India, US, EU27 & UK, Russia,
Japan, Brazil, ROW and World) in the power sector and industrial sector and the global total in the international shipping sector for 547 days (from January 1, 2019, to June 30 [th], 2020), and ground transport sector,
residential sector and aviation sector (China, India, US, EU27 & UK, Russia, Japan, Brazil, ROW, domestic
aviation total, international aviation, and total aviation) for 578 days (from January 1, 2019, to July 31 [st], 2020).


**Technical Validation**
**Uncertainty estimates.** We calculate daily emissions directly from daily activity data in most sectors, thus
the daily uncertainties are explained by the specific uncertainty of daily sectoral activity data itself and uncertainties in the (empirical) models used to convert activity to emissions. Here we should distinguish between _error_
and _uncertainty_ . Errors being defined the difference to the truth cannot be estimated because we do not know the
true values. Uncertainty could be estimated if we had different activity datasets, by looking at the spread of these
different datasets, but this is also not possible as most of our activity data are unique. The main issue is that we do
not know if the uncertainty of activity data would be a systematic uncertainty (e.g. all days are biased low using
our activity dataset compared to another activity dataset) or a random error (in which case, there could be positive bias one day compensated by negative bias another day and the uncertainty would be small e.g. on monthly
values derived from daily values). We do acknowledge that we cannot estimate daily uncertainties. In the future,
this could be done by trying to collect different activity data. This uncertainty analysis was also presented in our
related paper recently published at _Nature Communications_ [28] .
Thus, we followed the 2006 IPCC Guidelines for National Greenhouse Gas Inventories to conduct an uncertainty analysis of the data. First, the uncertainties were calculated for each sector (See Table 6 for uncertainty
ranges of each sector):


                     - Power sector: the uncertainty is mainly from inter-annual variability of coal emission factors and changes in
mix of generation fuel in thermal production. The uncertainty of power emission from fossil fuel is within
(±14%) with the consideration of both inter-annual variability of fossil fuel based on the UN statistics and
the variability of the mix of generation fuel (the ratio of electricity produced by coal to thermal production).

                     - Industrial sector: The uncertainty of CO 2 from industry and cement production comes from monthly production data. CO 2 from industry and cement production in China accounts for more than 60% of world total
industrial CO 2, and the uncertainty of emissions in China is 20%. Uncertainty from monthly statistics was
derived from 10,000 Monte Carlo simulations to estimate a 68% confidence interval (1 sigma) for China. We
calculated the 68% prediction interval of the linear regression models between emissions estimated from
monthly statistics and official emissions obtained from annual statistics at the end of each year to deduce
the one-sigma uncertainty involved when using monthly data to represent the change for the whole year.
The squared correlation coefficients are within the range of 0.88 (e.g., coal production) and 0.98 (e.g., energy
import and export data), which indicates that only using the monthly data can explain 88% to 98% of the
whole year’s variation [32] ; the remaining variation is not covered but reflects the uncertainty caused by the
frequent revisions of China’s statistical data after they are first published.

                     Ground Transportation: The emissions from the ground transportation sector are estimated by assuming that
the relative magnitude in car counts (and thus emissions) follow a similar relationship with TomTom congestion index in Paris. This model calibrated in Paris was cross checked to match traffic fluxes of two other cities.
Future work will focus on additional cross-validation and calibration with more traffic data from other cities.

                     Aviation: The uncertainty in the aviation sector comes from the difference in daily emission data estimated
based on the two methods. We calculate the average difference between the daily emission results estimated
based on the flight route distance and the number of flights and then divide the average difference by the average daily emissions estimated by the two methods to obtain the uncertainty in CO 2 from the aviation sector.

                     - Shipping: We used the uncertainty analysis from IMO as our uncertainty estimate for shipping emissions.
According to the Third IMO Greenhouse Gas study 2014 [38], the uncertainty in shipping emissions was 13%
based on bottom-up estimates.


Scientific **Data** | _(2020) 7:392_ [| https://doi.org/10.1038/s41597-020-00708-7](https://doi.org/10.1038/s41597-020-00708-7) 9


www.nature.com/scientificdata/ www.nature.com/scientificdata

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/liuCarbonMonitorNearrealtime2020/liuCarbonMonitorNearrealtime2020.pdf-9-0.png)


**Fig. 3** Residential and commercial building daily natural gas consumption (linearly related to CO 2 emissions
from this sector) in France for the last 5 years. Temperature effects have been removed from emissions using
a linear piecewise model fitted to daily data. When the effect of variable winter temperature was removed, no
significant change is seen in 2020 during the very strict lockdown period except for a small dip by end of March.



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/liuCarbonMonitorNearrealtime2020/liuCarbonMonitorNearrealtime2020.pdf-9-1.png)



**Table 6.** Percentage uncertainty of all items.


Residential: The 2-sigma uncertainty in daily emissions is estimated as 40%, which is calculated based on a
comparison with daily residential emissions derived from real fuel consumption in several European countries, including France, Great Britain, Italy, Belgium, and Spain.


The uncertainty in the emission projection for 2019 is estimated as 2.2% by combining the reported uncertainty of the projected growth rates and the EDGAR estimates in 2018.
Then, we combine all the uncertainties by following the error propagation equation from the IPCC.
Equation 23 is used to derive the uncertainty of the sum and could be used to combine the uncertainties of all
sectors:





_U_ _total_ =





_total_ =

∑ _µ_ _s_ (23)







where respectively. _U_ _s_ and _µ_ _s_ are the percentage and quantity (daily mean emissions) of the uncertainty of sector _s_,
Equation 24 is used to derive the uncertainty of the multiplication, which in turn is used to combine the
uncertainties of all sectors and of the projected emissions in 2019:

#### U overall = ∑ U i 2 (24)


**Code availability**
[The generated datasets are available from https://doi.org/10.6084/m9.figshare.12685937.v4 and https://github.](https://doi.org/10.6084/m9.figshare.12685937.v4)
[com/zhudeng94/dailyCO2. Codes for industrial emission calculation and summary table generation are available](https://github.com/zhudeng94/dailyCO2)
on the GitHub repository presented as worksheets. Also the raw data of power generation in U.S., EU27 & UK,
India, Russia, Japan and Brazil are available on the GitHub repository. Other raw data and codes for emission
estimation in other sectors are only available upon reasonable requests.


Received: 10 June 2020; Accepted: 17 September 2020;
Published: xx xx xxxx


**References**
1. Janssens-Maenhout, G. _et al_ . EDGAR v4.3.2 Global Atlas of the three major Greenhouse Gas Emissions for the period 1970–2012.
_Earth Syst. Sci. Data_ **11**, 959–1002 (2019).
2. Marland, G. & Rotty, R. M. Carbon dioxide emissions from fossil fuels: a procedure for estimation and results for 1950–1982. _Tellus_
_Ser. B-Chem. Phys. Meteorol._ **36**, 232–261 (1984).


Scientific **Data** | _(2020) 7:392_ [| https://doi.org/10.1038/s41597-020-00708-7](https://doi.org/10.1038/s41597-020-00708-7) 10


www.nature.com/scientificdata/ www.nature.com/scientificdata


3. Intergovernmental Panel on Climate Change (IPCC). Revised 1996 IPCC Guidelines for National Greenhouse Gas Inventories.
(Intergovernmental Panel on Climate Change, 1997).
4. Eggleston, S., Buendia, L., Miwa, K., Ngara, T. & Tanabe, K. 2006 IPCC guidelines for national greenhouse gas inventories. Vol. 5
(Institute for Global Environmental Strategies Hayama, Japan, 2006).
5. Gregg, J. S., Andres, R. J. & Marland, G. China: Emissions pattern of the world leader in CO 2 emissions from fossil fuel consumption
and cement production. _Geophys. Res. Lett._ **35**, GL032887 (2008).
6. Andres, R. J., Boden, T. A. & Higdon, D. A new evaluation of the uncertainty associated with CDIAC estimates of fossil fuel carbon
dioxide emission. _Tellus Ser. B-Chem. Phys. Meteorol_ . **66** (2014).
7. Fridley, D., Zheng, N. & Qin, Y. _Inventory of China’s Energy-Related CO_ _2_ _Emissions in 2008_ . (Lawrence Berkeley National Laboratory,
2011).
8. Andres, R. J., Gregg, J. S., Losey, L., Marland, G. & Boden, T. A. Monthly, global emissions of carbon dioxide from fossil fuel
consumption. _Tellus Ser. B-Chem. Phys. Meteorol._ **63**, 309–327 (2011).
9. Le Quéré, C. _et al_ . Global carbon budget 2018. _Earth Syst. Sci. Data_ **10**, 2141–2194 (2018).
10. BP. _Statistical Review of World Energy_ [, https://www.bp.com/en/global/corporate/energy-economics/statistical-review-of-world-](https://www.bp.com/en/global/corporate/energy-economics/statistical-review-of-world-energy.html)
[energy.html (2020).](https://www.bp.com/en/global/corporate/energy-economics/statistical-review-of-world-energy.html)
11. International Energy Agency (IEA). _CO_ _2_ _Emissions from Fuel Combustion 2019_ [, https://www.iea.org/reports/co2-emissions-from-](https://www.iea.org/reports/co2-emissions-from-fuel-combustion-2019)
[fuel-combustion-2019 (2019).](https://www.iea.org/reports/co2-emissions-from-fuel-combustion-2019)
12. Hong, C. _et al_ . Variations of China’s emission estimates: response to uncertainties in energy statistics. _Atmos. Chem. Phys._ **17**,
1227–1239 (2017).
13. Crippa, M. _et al_ . High resolution temporal profiles in the Emissions Database for Global Atmospheric Research. _Sci. Data_ **7**, 121
(2020).
14. Zhao, Y., Zhou, Y., Qiu, L. & Zhang, J. Quantifying the uncertainties of China’s emission inventory for industrial sources: From
national to provincial and city scales. _Atmos. Environ._ **165**, 207–221 (2017).
15. Andres, R. J. _et al_ . A synthesis of carbon dioxide emissions from fossil-fuel combustion. _Biogeosciences_ **9**, 1845–1871 (2012).
16. Olivier, J. G., Janssens-Maenhout, G. & Peters, J. A. _Trends in global CO_ _2_ _emissions: 2013 report_ . Report No. 9279253816, (PBL
Netherlands Environmental Assessment Agency, 2013).
17. Liu, Z. _et al_ . Reduced carbon emission estimates from fossil fuel combustion and cement production in China. _Nature_ **524**, 335–338
(2015).
18. Choudhury, A., Roy, J., Biswas, S., Chakraborty, C. & Sen, K. Determination of carbon dioxide emission factors from coal
combustion. In _Climate Change and India: Uncertainty Reduction in Greenhouse Gas Inventory Estimates_ (Universities Press, 2004).
19. Roy, J., Sarkar, P., Biswas, S. & Choudhury, A. Predictive equations for CO 2 emission factors for coal combustion, their applicability
in a thermal power plant and subsequent assessment of uncertainty in CO 2 estimation. _Fuel_ **88**, 792–798 (2009).
20. Sarkar, P. _et al_ . Revision of country specific NCVs and CEFs for all coal categories in Indian context and its impact on estimation of
CO 2 emission from coal combustion activities. _Fuel_ **236**, 461–467 (2019).
21. OlivierJ. G. J. & Peters, J. A. H. W. _Trends in global CO_ _2_ _and total greenhouse gas emissions; 2019 Report_ . (PBL Netherlands
Environmental Assessment Agency, The Hague, 2019).
22. Andres, R. J., Boden, T. A. & Higdon, D. M. Gridded uncertainty in fossil fuel carbon dioxide emission maps, a CDIAC example.
_Atmos. Chem. Phys_ . **16** (2016).
23. Asefi‐Najafabady, S. _et al_ . A multiyear, global gridded fossil fuel CO 2 emission data product: Evaluation and analysis of results. _J._
_Geophys. Res.: Atmos._ **119**, 10,213–210,231 (2014).
24. Friedlingstein, P. _et al_ . Global carbon budget 2019. _Earth Syst. Sci. Data_ **11**, 1783–1838 (2019).
25. Oda, T., Maksyutov, S. & Andres, R. J. The Open-source Data Inventory for Anthropogenic CO 2, version 2016 (ODIAC2016): a
global monthly fossil fuel CO 2 gridded emissions data product for tracer transport simulations and surface flux inversions. _Earth_
_Syst. Sci. Data_ **10**, 87–107 (2018).
26. Gregg, J. S. & Andres, R. J. A method for estimating the temporal and spatial patterns of carbon dioxide emissions from national
fossil-fuel consumption. _Tellus Ser. B-Chem. Phys. Meteorol._ **60**, 1–10 (2008).
27. Cui, D., Deng, Z. & Liu, Z. China’s non-fossil fuel CO 2 emissions from industrial processes. _Appl. Energy_ **254**, 113537 (2019).
28. Liu, Z., Ciais, P., Deng, Z. _et al_ . Near-real-time monitoring of global CO 2 emissions reveals the effects of the COVID-19
pandemic. _Nat Commun_ **11**, 5172 (2020).
29. Crippa, M. _et al_ . _Fossil CO_ _2_ _and GHG emissions of all world countries - 2019 Report_ . (Publications Office of the European Union,
2019).
30. National Bureau of Statistics. _China Energy Statistical Yearbook_ . (China Statistical Press, 2019).
31. National Bureau of Statistics. _Statistical Communiqué of the People’s Republic of China on the National Economic and Social_
_Development_ [, http://www.stats.gov.cn/tjsj/tjgb/ndtjgb/ (2020).](http://www.stats.gov.cn/tjsj/tjgb/ndtjgb/)
32. Liu, Z., Zheng, B. & Zhang, Q. _New dynamics of energy use and CO_ _2_ _emissions in China_ [. Preprint at https://arxiv.org/abs/1811.09475](https://arxiv.org/abs/1811.09475)
(2018).
33. Korsbakken, J. I., Andrew, R. & Peters, G. _Guest post: Why China’s CO_ _2_ _emissions grew less than feared in 2019_ [, https://www.](https://www.carbonbrief.org/guest-post-why-chinas-co2-emissions-grew-less-than-feared-in-2019)
[carbonbrief.org/guest-post-why-chinas-co2-emissions-grew-less-than-feared-in-2019 (2020).](https://www.carbonbrief.org/guest-post-why-chinas-co2-emissions-grew-less-than-feared-in-2019)
34. Ji-Cheng, H. & Yu-Qing, X. Estimation of the Aircraft CO 2 Emissions of China’s Civil Aviation during 1960–2009. _Adv. Clim. Chang._
_Res._ **3**, 99–105 (2012).
35. Turgut, E. T., Usanmaz, O. & Cavcar, M. The effect of flight distance on fuel mileage and CO 2 per passenger kilometer. _Inter. J._
_Sustain. Transp._ **13**, 224–234 (2019).
36. Graver, B., Zhang, K. & Rutherford, D. _CO_ _2_ _emissions from commercial aviation, 2018_ . (International Council on Clean
Transportation, 2019).
37. International Air Transport Association (IATA). _Slower but Steady Growth in 2019_ [, https://www.iata.org/en/pressroom/pr/2020-02-](https://www.iata.org/en/pressroom/pr/2020-02-06-01/)
[06-01/ (2020).](https://www.iata.org/en/pressroom/pr/2020-02-06-01/)
38. Smith, T. W. P. _et al_ . _Third IMO Greenhouse Gas Study 2014._ (International Maritime Organization, London, UK, 2015).
39. Olmer, N., Comer, B., Roy, B., Mao, X. & Rutherford, D. _Greenhouse Gas Emissions From Global Shipping, 2013–2015_ . (International
Council on Clean Transportation, 2017).
40. Kinsey, A. _Coronavirus Intensifies Global Shipping Risks_ [, https://www.maritime-executive.com/editorials/coronavirus-intensifies-](https://www.maritime-executive.com/editorials/coronavirus-intensifies-global-shipping-risks)
[global-shipping-risks (2020).](https://www.maritime-executive.com/editorials/coronavirus-intensifies-global-shipping-risks)
41. Spoladore, A., Borelli, D., Devia, F., Mora, F. & Schenone, C. Model for forecasting residential heat demand based on natural gas
consumption and energy performance indicators. _Appl. Energy_ **182**, 488–499 (2016).
42. VDI. _Berechnung der Kosten von Wärmeversorgungsanlagen (Economy calculation of heat consuming installations)_ (VID-Gesellschaft
Bauen und Gebäudetechnik. 1988).
43. Copernicus Climate Change Service (C3S). ERA5: Fifth generation of ECMWF atmospheric reanalyses of the global climate.
_Copernicus Climate Change Service Climate Data Store_ [https://cds.climate.copernicus.eu (2019).](https://cds.climate.copernicus.eu)
44. Doxsey-Whitfield, E. _et al_ . Taking advantage of the improved availability of census data: a first look at the gridded population of the
world, version 4. _Appl. Geogr._ **1**, 226–234 (2015).
45. Deng, Z. _et al_ . Daily updated dataset of national and global CO 2 emissions from fossil fuel and cement production. _figshare_ [https://](https://doi.org/10.6084/m9.figshare.12685937.v4)
[doi.org/10.6084/m9.figshare.12685937.v4 (2020).](https://doi.org/10.6084/m9.figshare.12685937.v4)


Scientific **Data** | _(2020) 7:392_ [| https://doi.org/10.1038/s41597-020-00708-7](https://doi.org/10.1038/s41597-020-00708-7) 11


www.nature.com/scientificdata/ www.nature.com/scientificdata


**Acknowledgements**
Authors acknowledged reviews comment to improving the manuscript. Zhu Liu acknowledges funding from
Qiushi Foundation, the Resnick Sustainability Institute at California Institute of Technology, Natural Science
Foundation of Beijing (JQ19032) and the National Natural Science Foundation of China (grant 71874097 and
41921005).


**Author contributions**
Zhu Liu and Philippe Ciais designed the research, and Zhu Deng coordinated the data processing. Zhu Liu,
Philippe Ciais and Zhu Deng contributed equally to this research, and all authors contributed to data collection,
analysis and paper writing.


**Competing interests**
The authors declare no competing interests.


**Additional information**
**Correspondence** and requests for materials should be addressed to Z.L., P.C. or S.J.D.


**Reprints and permissions information** [is available at www.nature.com/reprints.](http://www.nature.com/reprints)


**Publisher’s note** Springer Nature remains neutral with regard to jurisdictional claims in published maps and
institutional affiliations.


**Open Access** This article is licensed under a Creative Commons Attribution 4.0 International
License, which permits use, sharing, adaptation, distribution and reproduction in any medium or
format, as long as you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license, and indicate if changes were made. The images or other third party material in this
article are included in the article’s Creative Commons license, unless indicated otherwise in a credit line to the
material. If material is not included in the article’s Creative Commons license and your intended use is not permitted by statutory regulation or exceeds the permitted use, you will need to obtain permission directly from the
[copyright holder. To view a copy of this license, visit http://creativecommons.org/licenses/by/4.0/.](http://creativecommons.org/licenses/by/4.0/)


[The Creative Commons Public Domain Dedication waiver http://creativecommons.org/publicdomain/zero/1.0/](http://creativecommons.org/publicdomain/zero/1.0/)
applies to the metadata files associated with this article.

© The Author(s) 2020


Scientific **Data** | _(2020) 7:392_ [| https://doi.org/10.1038/s41597-020-00708-7](https://doi.org/10.1038/s41597-020-00708-7) 12


