# Citation Key: chenBlockchainConsensusMechanism2022

---

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenBlockchainConsensusMechanism2022/chenBlockchainConsensusMechanism2022.pdf-0-0.png)
## **A blockchain consensus mechanism that uses** **Proof of Solution to optimize energy dispatch** **and trading**

**[<PERSON><PERSON><PERSON>](http://orcid.org/0000-0001-6553-7915)** **[1]** [ ✉] **[, Hanning <PERSON>](http://orcid.org/0000-0002-2149-7445)** **[1]** **[, <PERSON><PERSON>](http://orcid.org/0000-0002-4300-7822)** **[1]** **, <PERSON> Yan** **[1]** **, <PERSON><PERSON><PERSON>** **[1]** **, <PERSON><PERSON><PERSON>** **[1]** **[, <PERSON><PERSON>](http://orcid.org/0000-0003-0366-4657)** **[2]** **,**
**<PERSON>a** **[2]** **[and <PERSON><PERSON><PERSON>](http://orcid.org/0000-0003-2296-8250)** **[2]**


**Traditional centralized optimization and management schemes may be incompatible with a changing energy system whose**
**structure is becoming increasingly distributed. This challenge can hopefully be addressed by blockchain. However, existing**
**blockchains have not been well prepared to integrate mathematical optimization, which plays a key role in many energy system**
**applications. Here we propose a blockchain consensus mechanism tailored to support mathematical optimization problems,**
**called Proof of Solution (PoSo). PoSo mimics Proof of Work (PoW) by replacing the meaningless mathematical puzzle in PoW**
**with a meaningful optimization problem. This is inspired by the fact that both the solutions to the puzzle and to an optimization**
**problem are hard to find but easy to verify. We show the security and necessity of PoSo by using PoSo to enable energy dispatch**
**and trading for two integrated energy systems. The results show that compared with existing optimization schemes, PoSo**
**ensures that only the optimal solution is accepted and executed by participants. Further, compared with existing blockchains,**
**PoSo can seamlessly incorporate mathematical optimization and minimize the workload associated with searching and verify-**
**ing the optimum.**



he complexity of energy systems is forecast to increase with
increasing penetration of distributed energy resources [1][,][2],
# T increasing synergy across multiple energy carriers [3] and

increasing inter-area energy exchange [4] . Such transition results in a
more distributed physical structure of an energy system [5], where multiple interest parties need to collaborate and coordinate with each
other. It will be challenging for traditional centralized management
schemes to work because a central authority may not exist or may
not have the ability to command and coordinate all interest parties [6][,][7] .
Blockchain is promising in enabling trusted collaboration in the
absence of a trusted central authority. Instead of letting a central
authority dictate and dominate a collaboration network, blockchain
lets stakeholders jointly run and oversee the collaboration network
in a democratic way [8] . The states and rules of the collaboration network, as a result, are resistant to manipulation by any single party.
A consensus mechanism is one of the key components of blockchain. It defines how different parties can reach agreement on the
states and actions in a blockchain network [8][,][9] . The most famous and
widely used blockchain consensus mechanism is perhaps Proof of
Work (PoW) [10] . PoW is used by Bitcoin and Ethereum [11] (around
50% of projects working in energy and blockchain are developed on
Ethereum [12] ). However, PoW is also famous for wasting substantial
time and energy on solving a difficult but meaningless mathematical puzzle [13] . Criticism to PoW has led to other consensus mechanisms, such as Proof of Stake [14], Delegated Proof of Stake [15], Proof of
Authority [16], Proof of Capacity [17], Practical Byzantine Fault Tolerance [18]
and Delegated Byzantine Fault Tolerance [19] . Detailed explanations of
these mechanisms are available in studies [6][–][8] or Supplementary Note
1. These blockchain consensus mechanisms have enabled a variety
of applications in finance, insurance, supply chain management
and energy [20][–][22] . In the energy sector, the most popular blockchain



application is peer-to-peer energy trading [12], which treats electricity
energy in a similar way to other commodities.
However, the energy sector has some unique characteristics different from other industries. Control, dispatch and trading in power
energy systems are usually subject to strict physical constraints and
rely on solving constrained optimization problems [23] . It is usually
complex and requires specialized solvers to solve an optimization
problem. This poses challenges on existing blockchains that are not
specialized for mathematical optimization.
To process optimization tasks on existing blockchains, optimization solvers probably need to be coded in the smart contract layers (not in the consensus mechanism layers). Smart contracts are
the agreed rules for collaboration in the form of executable programmes [4] . Specifically, a number of nodes all need to solve an optimization problem by running a blockchain smart contract [24] . This
is to prevent any single party from dominating and manipulating
the optimization result. Other than the above method, Shibata [25]
proposes an alternative blockchain mechanism capable of searching for good approximate solutions to an optimization problem.
Specifically, many nodes compete to evaluate a large number of
solution candidates, and among them, the best approximate solution is accepted. These two methods are inefficient and even
inexact and have limited the types of blockchain application. For
example, in most studies that involve blockchain and optimization in energy [26][–][29], blockchain is used only for communicating and
recording critical information, while optimization problems are still
solved by a central entity.
Here we propose a blockchain consensus mechanism tailored
to support mathematical optimization problems in energy systems.
The blockchain consensus mechanism is named Proof of Solution
(PoSo). The basic idea of PoSo mimics that of PoW. Specifically,



1 Key Laboratory of Control of Power Transmission and Conversion, Ministry of Education, Shanghai Jiao Tong University, Shanghai, China. 2 State Key
Laboratory of Power Systems, Department of Electrical Engineering, Tsinghua University, Beijing, China. [✉] [e-mail: <EMAIL>](mailto:<EMAIL>)


**Nature Energy** [| VOL 7 | June 2022 | 495–502 | www.nature.com/natureenergy](http://www.nature.com/natureenergy) **495**


#### Articles NaTurE EnErgy



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenBlockchainConsensusMechanism2022/chenBlockchainConsensusMechanism2022.pdf-1-0.png)





9





8



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenBlockchainConsensusMechanism2022/chenBlockchainConsensusMechanism2022.pdf-1-12.png)











the received solutions







![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenBlockchainConsensusMechanism2022/chenBlockchainConsensusMechanism2022.pdf-1-11.png)



d 2 d k



optimality of the
received solutions



**Fig. 1 |** **Flow chart of PoSo.** Under the PoSo scheme, a delegation comprising participants interested in ruling the collaboration network is selected by and
out of all participants. A temporary leader, selected from and by the delegates, is responsible for yielding an optimal solution. The rest of the delegates
are followers, who verify and exchange the received solutions. If the delegates agree on the solution, they will broadcast the solution to every participant.
A participant trusts a received solution if the solution is endorsed by the majority of the delegates. Any leader identified as dishonest or incompetent is
replaced by one of the rest of the delegates and removed from the delegation. d 1, d 2, ..., d k represent the delegates.



PoSo replaces the meaningless mathematical puzzle by a meaningful optimization problem. Similar to the solution to the mathematical puzzle in PoW, the solution to an optimization problem
in PoSo is also _hard to find but easy to verify_ . Unlike PoW, PoSo
avoids spending massive computing power on solving meaningless
problems. Such features unlock the use of blockchain in a variety of
applications where optimization is inevitable but no central trusted
authority exists to run optimization algorithms. To demonstrate
the robustness and necessity of PoSo, we present two case studies
that use PoSo to enable dispatch and trading for two integrated
energy systems.


**Philosophy of PoSo**
In history, a multi-party collaboration network is usually run and
dominated by a central authority. This central authority has supreme
power and control over other participating parties and owns all



information and data. Other participating parties, on the other
hand, usually lack means to counterbalance the central authority.
For example, if the central authority openly or secretly manipulates
collaboration rules for its own interest, others are usually unable to
disable or even detect such manipulation.
Instead of letting a central authority dominate a collaboration
network, an alternative is that participants jointly run the collaboration network and mutually oversee and balance each other. Examples
of such regimes in the real world include parliaments or congresses
in politics, boards of directors in business or committees in various
organizations. However, until recently, such democracy is practiced
offline but not online. Blockchain, an emerging information technology, is an enabler of online democracy. A blockchain-based collaboration network is not led by one central authority but enables
everyone to have a chance to be a temporary leader. A temporary
leader has restricted power and must not violate the rules of the



**496** **Nature Energy** [| VOL 7 | June 2022 | 495–502 | www.nature.com/natureenergy](http://www.nature.com/natureenergy)


#### NaTurE EnErgy Articles



**a** **b** **c**

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenBlockchainConsensusMechanism2022/chenBlockchainConsensusMechanism2022.pdf-2-0.png)


**d** **e**

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenBlockchainConsensusMechanism2022/chenBlockchainConsensusMechanism2022.pdf-2-1.png)


Blockchain Blockchain



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenBlockchainConsensusMechanism2022/chenBlockchainConsensusMechanism2022.pdf-2-2.png)

Coordinator


Participant


Information flow



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenBlockchainConsensusMechanism2022/chenBlockchainConsensusMechanism2022.pdf-2-5.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenBlockchainConsensusMechanism2022/chenBlockchainConsensusMechanism2022.pdf-2-6.png)

**Fig. 2 |** **Schematics of different optimization structures.** **a**, Centralized.
**b**, Hierarchical. **c**, Fully distributed. **d**, Blockchain as coordinator. **e**, PoSo.
In **a** or **e**, the central operator ( **a** ) or delegation ( **e** ) determines an optimal
solution for each participant with no iterations. In **b** or **d**, the coordinator ( **b** )
or delegation ( **d** ) exchanges information with participants iteratively to find
an optimal solution. In **c**, participants exchange information with neighbours
iteratively to find an optimal solution.


collaboration network, otherwise its actions are rejected by other
parties and its role is replaced by other parties.
In a PoW-based blockchain, participants interested in being
temporary leaders (that is, miners) compete to solve a difficult but
meaningless mathematical puzzle. The winner becomes a temporary leader and announces a block that defines the latest states of
the network. The block is accepted by the rest of participants if and
only if the block is deemed valid. A block is valid if the puzzle is
solved correctly and the latest states of the network are generated by
strictly enforcing the rules of the network (that is, strictly executing
smart contracts). Notably, the mathematical puzzle is hard to solve
but easy to verify, so the rest of participants do not have to make
considerable efforts on verification.
PoSo is inspired by the PoW mechanism. Similar to the mathematical puzzle in PoW, the optimal solution to a continuously differentiable optimization problem is also hard to find but easy to verify.
Specifically, if the optimization problem is convex, a solution _x_ _[*]_ is
optimal if and only if the Karush–Kuhn–Tucker (KKT) condition
holds at _x_ _[*]_ . If the optimization problem is non-convex, _x_ _[*]_ is a local
optimum if and only if the KKT condition and the second-order
sufficient condition hold at _x_ _[*]_ (Supplementary Note 2). Apparently,
verifying whether a given _x_ _[*]_ satisfies the optimality conditions is
much easier than finding _x_ _[*]_ .
The time- and energy-consuming puzzle-solving process and
smart contract-executing process in a PoW-based blockchain are
replaced by one process, that is, an optimization problem solving process in PoSo (Table 1 and Methods). Briefly speaking, a
temporary leader is responsible for finding an optimal solution,
whereas others verify the solution to determine whether to accept
it (Fig. 1). Given that there are usually numerous verifiers, the
hard-to-solve but easy-to-verify feature of PoSo can minimize the
total workload in the collaboration network. This feature becomes
increasingly useful as the difficulty of solving an optimization
problem increases.
Compared with existing blockchains, PoSo pioneers a blockchain paradigm. In this paradigm, the blockchain consensus layer
not only enables participants to reach agreement but also processes
some tasks that otherwise have to be processed in the smart contract
layer. The specific task that PoSo can process is to find and verify
the solution to an optimization problem. By doing so, PoSo can
seamlessly incorporate mathematical optimization and minimize
the workload associated with searching and verifying the optimum.



Compared with existing centralized [23][,][30], hierarchical [31][,][32] or
fully distributed [33][,][34] optimization schemes, PoSo enables checks
and balances among participants. This ensures that only the
optimal solution is accepted and executed by participants, even
if some of the participants act dishonestly. Compared with
blockchain-as-coordinator schemes [35][–][38], PoSo is more computationally efficient by avoiding numerous rounds of iterations (Fig. 2).
PoSo also differs from existing optimization schemes in the types
of applicable optimization problem. Existing centralized schemes
are applicable to any optimization problems. Hierarchical, fully distributed or blockchain-as-coordinator schemes are applicable only
to optimization problems with decomposable structures [39][,][40] . PoSo
works as long as the objective function and constraints in an optimization problem are continuously differentiable (Table 2).
The PoSo mechanism is useful in scenarios where the rules of
the collaboration network take the form of mathematical optimization and the state of each participant is determined by optimization. In the energy sector, examples of such collaboration include
energy management, economic dispatch and pool-market trading
among energy producers and consumers. For instance, in economic dispatch, the leader is supposed to determine the dispatched
energy production or consumption volume of each participant to
minimize the total dispatch cost. The state of each participant is its
dispatched MWh production/consumption. The rule of the collaboration network is that participants should be dispatched in a
cost-minimization way.


**Apply PoSo to economic dispatch**
We instantiate the PoSo mechanism with an example of dispatching an electricity–heat–gas integrated energy system (IES). The
electricity, heat and gas sub-systems are coupled with energy hubs,
which consist of energy conversion and storage devices. Because an
IES usually spans several energy sectors and multiple energy hubs, a
centralized operation authority usually does not exist. Even if such
an operation centre exists, it has the ability to distort the optimal
dispatch pattern and may not be trusted by all participating energy
hubs. The proposed PoSo scheme is promising to address this challenge by allowing participating energy hubs to jointly dispatch the
IES and oversee the dispatch pattern.
This example is based on the University of Manchester campus, an
electricity–heat–gas IES [3] . The structure and location of each energy
hub in the IES is given in Supplementary Fig. 1 and Supplementary
Table 1. We consider eight energy hubs in this case, each having a
combined heat and power (CHP) unit, a gas boiler, electricity storage and heat storage. The electricity network is connected to a bulk
power system, and the gas network is connected to a gas source.
The IES optimal dispatch model is given in Supplementary Note 3.
The optimal dispatch pattern of the IES is the one that minimizes
the operational cost while satisfying all operational constraints.
One can refer to studies [3][,][41][,][42] for further explanation of the model.
Detailed system parameters and optimization results of the IES are
available at [https://github.com/kelpman05/DataUofManchester.](https://github.com/kelpman05/DataUofManchester)
The structure of the PoSo code is shown in Supplementary Fig. 2.


**Economic dispatch results under existing optimization**
**schemes**
Under a centralized dispatch scheme, it is possible that the central
operation authority colludes with some energy hubs. Accordingly,
this authority is motivated and able to act in the interests of these
energy hubs instead of the interests of the entire IES by manipulating the dispatch pattern. For example, if the operation centre
colludes with energy hub 3, it can add an extra term that maximizes the power output of energy hub 3 into the objective function (Supplementary Note 3). Figure 3 shows the dispatch patterns
before and after manipulation, which are referred to as optimal and
non-optimal solutions in the following texts.



**Nature Energy** [| VOL 7 | June 2022 | 495–502 | www.nature.com/natureenergy](http://www.nature.com/natureenergy) **497**


#### Articles NaTurE EnErgy



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenBlockchainConsensusMechanism2022/chenBlockchainConsensusMechanism2022.pdf-3-0.png)



2,500



2,000


1,500


1,000



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenBlockchainConsensusMechanism2022/chenBlockchainConsensusMechanism2022.pdf-3-1.png)



500


0

1 2 3 4 5 6 7 8 9 10 11 12



13 14 15 16 17 18 19 20 21 22 23 24



Time (h)


**Fig. 3 |** **Electricity outputs under the optimal and non-optimal dispatch patterns.** The electricity production pattern covers 24 hours. For each hour, there
are two bars representing two dispatch patterns. The left bar represents the dispatch pattern before manipulation (optimal dispatch pattern). The right bar
represents the dispatch pattern after manipulation (non-optimal dispatch pattern).



The non-optimal solution increases the CHP electricity output
of energy hub 3 substantially while reducing the output of the bulk
power system. Notably, among other energy hubs, the CHP output of
energy hub 6 is also increased. Because letting the CHP of energy hub
3 increase its output is not economic, the IES operation costs under
the non-optimal and optimal solutions are US$904.17 and US$744.09,
with the former being 21.5% higher than the latter. This result shows
that untrusted parties can distort existing centralized schemes by
tampering with optimization algorithms or input parameters.
The economic dispatch model in this example does not have a
decomposable structure (Supplementary Note 3). Therefore, existing hierarchical, fully distributed and blockchain-as-coordinator
optimization schemes are not applicable.


**Economic dispatch results under the PoSo scheme**
In the University of Manchester example, energy hubs 3, 4, 5, 6 and
7 are selected as delegates to constitute a delegation and are called
delegates C, D, E, F and G, respectively. These five delegates are
responsible for announcing an optimal dispatch pattern. Assume
that two of them, C and F, are dishonest and are able to collude.
They can act in all possible ways when being leaders. Such actions
include sending a solution to no follower or part of followers, sending unified or heterogeneous solutions to followers and sending
optimal or non-optimal solutions to followers. Here we let delegates
C and F disobey algorithm (1) of Methods in four ways that mimic
various possible actions they can take. In scenarios 1 to 3, delegate
C is the current leader and the other four delegates are followers.



In scenario 1, leader C manipulates the objective function in
the same way as described in the previous subsection and derives
a non-optimal solution. It sends the non-optimal solution only to
delegate F but not others. Delegate F does not send any result to
other followers.
In scenario 2, leader C derives an optimal solution and a
manipulated non-optimal solution. It sends the optimal solution to
delegate D, sends the non-optimal solution to delegates E and F
and does not send any result to delegate G. Follower F forwards the
non-optimal solution to delegate D.
In scenario 3, leader C sends a manipulated non-optimal
solution to delegates D, E and F and does not send any result to
delegate G. Follower F does not send any result to other followers.
In scenario 4, delegate D is the leader. It derives an optimal solution and sends it to all followers. Followers C and F do not forward

the result to other followers.
In these four scenarios, delegates C and F send a non-optimal
solution or do not send any solution to participating energy hubs.
The solution exchanged among delegates contains the values of
all decision variables, all Lagrange multipliers and the total operational cost. To visualize the message-exchange process, we represent
a solution by the associated operational cost, that is, representing
the optimal/non-optimal solutions by US$744 and US$904. The
detailed message-exchange processes in these four scenarios are
shown in Table 3. Notably, the IES dispatch model is a non-convex
optimization model. Because all honest delegates are required to use
the same solver CONOPT4 (a non-convex optimization problem



**498** **Nature Energy** [| VOL 7 | June 2022 | 495–502 | www.nature.com/natureenergy](http://www.nature.com/natureenergy)


#### NaTurE EnErgy Articles



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenBlockchainConsensusMechanism2022/chenBlockchainConsensusMechanism2022.pdf-4-0.png)



solver) when being leaders, the local optimum they find are identical, that is, US$744.
In scenario 1, because all honest followers receive nothing during the phase for delegates, they will deem leader C as offline or
dishonest and let the next delegate be the leader. In an additional
phase for delegates, leader D lets honest followers E and G see the
optimal solution.
In scenario 2, even if leader C or follower F sends an optimal
solution to only one honest follower, this optimal solution will eventually be seen and accepted by all honest followers. No matter how
C or F sends out non-optimal solutions and attempts to create disagreement, non-optimal solutions are abandoned by honest followers and cannot influence their decisions.
In scenario 3, because all honest followers receive nothing or a
non-optimal solution, they will also let a new leader (delegate D)
re-run the phase for delegates, similar to scenario 1.



In scenario 4, however the dishonest followers C and F act, the
rest of the honest followers will see and use the optimal solution sent
from leader D.
In the first phases of scenarios 1 and 3, none of the delegates D, E
and G broadcast any result to participating energy hubs. No energy
hub sees a solution endorsed by more than 50% of the delegates, so
the energy hubs do not act but keep waiting.
In scenarios 2 and 4 and the second phases of scenarios 1 and 3, delegates D, E and G together broadcast the optimal solution to all energy
hubs. Each energy hub sees an optimal solution sent from more than
50% of the delegates, thus trusting and executing this solution.
Therefore, no matter how dishonest delegates C and F act, they
are unable to distort the solution but end up being blacklisted and
removed from the delegation.
However, if dishonest delegates take majority, PoSo will
fail. Specifically, if all dishonest delegates broadcast the same



**Nature Energy** [| VOL 7 | June 2022 | 495–502 | www.nature.com/natureenergy](http://www.nature.com/natureenergy) **499**


#### Articles NaTurE EnErgy



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenBlockchainConsensusMechanism2022/chenBlockchainConsensusMechanism2022.pdf-5-0.png)









![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenBlockchainConsensusMechanism2022/chenBlockchainConsensusMechanism2022.pdf-5-1.png)













non-optimal solution, all energy hubs will trust and implement this
dominant but incorrect solution.


**Economic dispatch results under some variants of PoSo**
This subsection attempts to simplify or modify some procedures in
the proposed PoSo scheme. Specifically, the following four variants
are tested. We still assume that delegates C and F are dishonest.
In variant 1, followers do not verify the optimality of the solution
sent from the leader, that is, skipping step 7 when running algorithm (1) in Methods.
Obviously, if leader C sends a non-optimal solution to all followers, then all delegates will broadcast this incorrect solution to energy
hubs. All energy hubs then implement the incorrect dispatch pattern.
In variant 2, followers do not forward the received solutions to
other followers, that is, skipping step 9 when running algorithm (1).
In this variant, leader C can send an optimal solution to follower D and a non-optimal solution to followers E, F and G
(Supplementary Table 2). Delegate D sends the optimal solution to all energy hubs while delegates E and G send nothing to
the energy hubs. E and G are waiting for the next delegate on the
waiting list, D, to be the leader. However, delegate D is unaware
of what happens to E and G and regards the optimal dispatch pattern found. Consequently, delegate D will be blacklisted by E and
G while the energy hubs will not be able to see and implement a
dominant solution.
In variant 3, followers find the optimal solution by themselves to
verify whether the leader sends the correct solution, that is, using
step 3 instead of step 7 when running algorithm (1). This variant
mimics the verification processes in most existing blockchains.
That is, validating nodes verify a received message by re-running
the smart contract to see if they can derive the same result as the
one they receive.
Under the PoSo scheme, the leading delegate spends 220 s on
finding an optimum while a following delegate spends less than 1 s
on verifying the optimum. Under variant 3, however, each delegate
spends around 220 s on finding an optimum, occupying computational resources around five times as much as the PoSo scheme.
In variant 4, delegates solve the optimal dispatch problem on a
PoW-based blockchain, such as Ethereum (Methods).
On a PoW-based blockchain, it takes each delegate around 220 s
to find or verify an optimal solution and additional time to find or
verify the solution to a mathematical puzzle. The time to solve a
mathematical puzzle varies by PoW-based blockchain techniques.
For example, the time to solve a mathematical puzzle is around 15 s
in Ethereum and 10 min in Bitcoin. Therefore, the computational
workload in a PoW-based blockchain is higher than five times the
workload in PoSo (Table 4).


**Apply PoSo to energy trading**
To further compare the performances of PoSo and existing optimization schemes, we apply PoSo to enable trading in an electricity–gas–heat–cooling IES in Wuzhong district in the city of Suzhou,



China. The IES topology, trading optimization model, parameters
and optimization results are available in our previous work [35] and at
[https://github.com/kelpman05/DataWuzhong. The trading pattern,](https://github.com/kelpman05/DataWuzhong)
that is, the trading volume and price of each participant, is determined in a way that minimizes the overall energy purchase cost of
the IES. The optimization model features a decomposable structure,
hence existing distributed optimization schemes (such as hierarchical and blockchain-as-coordinator schemes) are applicable.
Table 5 compares the computational time of different optimization schemes. (1) Compared with a centralized scheme, it
takes delegates 7 s longer to communicate and verify the optimum
in PoSo. (2) A hierarchical scheme involves numerous rounds
of iterations between a central coordinator and participants,
hence is less efficient than a centralized scheme or PoSo. (3) The
blockchain-as-coordinator framework [35] replaces the central coordinator in a hierarchical scheme by blockchain. Although this framework avoids integrating optimization solvers into blockchain, it has
the lowest efficiency. The reason is that it has the same number of
iterations as a hierarchical scheme, and in each iteration, delegates
need to spend time on exchanging messages to reach consensus.
Table 5 also compares the dishonesty tolerance of different optimization schemes. First, to construct a benchmark, we let everyone be
honest and find that the minimum energy purchase cost of the IES is
1.69 million CNY. Then, similar to the previous example, we let some
parties act dishonestly (Extended Data Fig. 1 and Supplementary
Note 4 provide details). Owing to the use of blockchain, the trading
patterns under PoSo and the blockchain-as-coordinator framework
are immune to dishonesty and are identical to the benchmark. In
the centralized scheme and hierarchical scheme, however, dishonest
parties succeed in cheating. Such cheating benefits themselves but
increases the overall energy purchase cost of the IES.


**Conclusion**
Mathematical optimization plays a key role in control, dispatch and
trading in a power energy system. Such optimization tasks are traditionally handled by a central authority, which may be problematic as
the energy system structure becomes increasingly distributed. The
proposed PoSo, a blockchain consensus mechanism tailored for
optimization tasks, can efficiently deliver correct and trusted optimization results in the absence of a central authority.
This paper applies PoSo to dispatch and trading for two integrated energy systems. The two examples support three findings.
First, PoSo is capable of delivering the correct dispatch and trading results in the presence of dishonest parties, whereas traditional
optimization schemes are not. Second, PoSo is superior to existing
blockchains in terms of computational workload and efficiency.
Third, attempts to change or simplify PoSo can undermine its security or efficiency.
Essentially, PoSo enables trusted optimization that involves multiple interest parties. By enabling checks and balances among participants, PoSo ensures that only the optimal solution is accepted and



**500** **Nature Energy** [| VOL 7 | June 2022 | 495–502 | www.nature.com/natureenergy](http://www.nature.com/natureenergy)


#### NaTurE EnErgy Articles



executed, even in the presence of dishonest participants. In energy
systems, PoSo can be applied to optimization-based tasks, such as
dispatch, operation and trading. PoSo can also be applied to other
collaboration networks whose rules are depicted by mathematical
optimization problems.
PoSo pioneers a blockchain paradigm. In this paradigm, the
blockchain consensus layer not only enables participants to reach
agreement but also processes some tasks that otherwise have to be
processed in the smart contract layer. This can hopefully improve
the efficiency of blockchain, which is usually considered as a key
bottleneck that limits its use.
PoSo has some limitations. First, PoSo is applicable only to
optimization problems whose objective functions and constraints
are continuously differentiable. For example, it does not work for
integer programming problems, because the optimum of such
problems cannot be verified using the KKT condition and the
second-order sufficient condition. Second, PoSo is applicable
only when complete information of an optimization problem is
available. Notably, this requirement also exists when processing
optimization tasks with centralized optimization schemes or on
existing blockchains. Future work is needed to extend the applicability of PoSo.


**Methods**
**Algorithm of PoSo.** A flow chart of the PoSo mechanism is depicted in Fig. 1.
Before running the optimum-searching process, some participants are selected as
delegates to constitute a delegation, and others are candidates in case they need to
replace any dishonest delegates. The delegates jointly run the collaboration network
and counterbalance each other. Methods to select delegates, such as voting,
rotation or lottery, have been presented in a number of studies [43][,][44] and will not be
detailed in this article.
At any time, there is a leader among all delegates, and the rest are followers.
The delegates take turns to serve as the leader according to a waiting list, which
comprises all members in the delegation. The first delegate in the waiting list
is deemed as the current leader by the delegation (step 1 in algorithm (1)).
The leader is responsible for solving the optimization problem and sending
an optimal solution to the followers and all participants within a time interval
(step 3 in algorithm (1)). Upon receipt of a solution before the end of the time
interval, a follower verifies the solution. If the solution is deemed optimal and
has not been stored, the follower sends the solution to other followers and
stores this solution (steps 6–11 in algorithm (1)). At the end of the given time
interval, each follower checks whether it has seen an optimal solution. If yes,
this follower deems the current leader as honest, updates the waiting list by
moving the current leader to the end of the list and sends the optimal solution
to all participants (steps 13–14 in algorithm (1)). If no, this follower deems the
current leader as dishonest or incompetent, removes it from the waiting list
and waits for a new leader to re-run the optimization process (steps 16–18
in algorithm (1)).
Once the current leader is deemed dishonest by the rest of the delegates,
including the second delegate on the waiting list, the second delegate takes the role
of the leader immediately. This new leader re-runs the above steps and should let
the delegation see an optimal solution within a new round.


**Algorithm 1** Phase for each delegate _d_ .
1: regard the first delegate in _D_ as the leader _l_
2: **If** _d_ = _l_ **then**
3: find and broadcast ⟨ **x** [∗] ⟩ l to other delegates and all participants
4: **end if**

5: **If** _d_ ≠ _l_ **then**
7: 6: **If** receive **If** _**x**_ _[*]_ is verified as optimal ⟨ **x** [∗] ⟩ ld 1 d 2 ...d k before **then** _T_ & **x** [∗] ∈ / S [r] d **[then]**
8: S [r] d [←] [S] [r] d [∪{] **[x]** [∗] [}]
9:  forward ⟨ **x** [∗] ⟩ ld 1 d 2 ...d k d to other delegates except _l_, _d_ 1, . . ., _d_ _k_
10: **end if**

11: **end if**
12: **If** | S [r] d [|] [ >][ 0] **[then]**
13: broadcast ⟨ **x** [∗] ⟩ d to all participants
14: move _l_ to the end of _D_

15: **else**
16: S [b] d [←] [S] [b] d [∪{] [l] [}]
17: remove _l_ from _D_

18: go to step 1
19: **end if**

20: **end if**



**Algorithm 2** Phase of each participant
1: **If** receive _**x**_ _**[*]**_ from more than half of delegates **then**
2: use _**x**_ _[*]_ as the optimal solution
3: **end if**

where
_D_  Set of delegates, constituting a waiting list where the first delegate
serves as the leader and the rest serve as followers
S [b] d [  Set of blacklisted delegates by delegate ] _[d]_ [, initialized as an empty set]
S [r] d [  Set of received solutions by delegate ] _[d]_ [, initialized as an empty set]
_l_ Leader of the delegation
_d_ _D_ elegate
⟨ **x** [∗] ⟩ ld 1 d 2  - ·· Optimal solution and corresponding Lagrange multipliers signed by delegates _l_, _d_ 1, _d_ 2 and so on, successively
_T_  Time interval after which a delegate stops receiving potential
solutions


Once a participant receives a same solution from more than 50% of the
delegates, this participant will deem this solution as the finalized optimal solution
and implement this solution (algorithm (2)).
After participants have found and agreed on the result to the current
optimization task, they will wait for the next optimization task. For instance, in the
energy trading example, each trading pattern covers one hour and is updated on
an hourly basis. Therefore, participants process a trading-pattern optimization task
using PoSo once an hour.
Complete information about the optimization problem is needed when
performing optimization tasks with PoSo. Here we assume that such information is
available to every delegate in PoSo.
Similar to other blockchain consensus mechanisms, the PoSo mechanism
can function if and only if honest delegates outnumber dishonest delegates. A
functioning PoSo mechanism means that all participants act based on the correct
and same optimal solution. The rationale is as follows. The message-exchange
protocol [45] among followers (step 6 and step 9 in algorithm (1)) ensures that if
one honest follower receives an optimal solution, every other honest follower can
also see this optimal solution. By the end of the phase for delegates, all honest
delegates can reach consensus on whether the current leader is honest and whether
an optimal solution has been found. Hence, all honest delegates will take the
same actions, either waiting for a new leader to re-run the optimization process
or sending out the optimal solution to participants. As long as there are honest
delegates on the waiting list, eventually all honest followers will see an optimal
solution, thus sending it out to all participants. Accordingly, all participants can
always receive a same solution from more than 50% of the delegates if honest
delegates take majority. Upon receipt of a same solution from more than 50% of
the delegates, a participant also trusts this solution because a non-optimal solution
cannot be endorsed by more than 50% of the delegates.
However, if dishonest delegates dominate the delegation, they can also
dominate the solutions seen by participants. Consequently, participants may follow
a non-optimal solution.


**Process of handling an optimization task on a PoW-based blockchain.** On a
PoW-based blockchain such as Ethereum, optimization algorithms need to be
coded in the smart contract layer. All delegates first solve the optimization problem
in the smart contract layer. They then compete to solve a difficult but meaningless
mathematical puzzle in the consensus mechanism layer. The winner becomes the
leader and announces a block including the solutions to the optimization problem and
the mathematical puzzle. Other delegates, as followers, accept the block only if they
deem both solutions as valid. That is, a follower itself has found the same optimum as
the leader, and the solution to the mathematical puzzle passes a validity test.


**Differences in the purpose and design between PoW and PoSo.** A PoW-based
blockchain is theoretically able to process any tasks. In contrast, PoSo is tailored
to process optimization tasks. In the blockchain consensus mechanism layer, PoSo
replaces the mathematical puzzle in PoW by an optimization problem. Therefore,
PoSo does not need a smart contract layer to process optimization problems.
Such customization makes PoSo more efficient than existing blockchains when
processing optimization tasks.


**Multiple local optima issue.** There can be multiple local optima to a non-convex
optimization problem. However, both PoW-based and PoSo-based blockchains
can ensure that participants reach consensus on the same local optimum. On
a PoW-based blockchain, there are multiple nodes competing to find a local
optimum at the same time. The smart contract layer specifies an initial value and
solver for optimization; therefore, these nodes will find the same local optimum. In
PoSo, at any time, there is only one leader that solves the optimization problem and
proposes a local optimum. Any leader also uses the same initial value and solver
specified by the PoSo algorithm. Therefore, in PoSo, when a delegate receives
a local optimum, it is sure that every other delegate also accepts this same local
optimum after the consensus period.
It is usually difficult to find the global optimum for a non-convex optimization
problem. Hence, the optimal solution-searching process in PoSo is deemed
completed as long as a local optimum is found. In this article, the term ‘optimum’



**Nature Energy** [| VOL 7 | June 2022 | 495–502 | www.nature.com/natureenergy](http://www.nature.com/natureenergy) **501**


#### Articles NaTurE EnErgy



refers to the global optimum to a convex optimization problem or a local optimum
to a non-convex optimization problem.


**Data availability**
The data relevant to system parameters and optimization results of the two IESs are
[available at https://github.com/kelpman05/DataUofManchester and https://github.](https://github.com/kelpman05/DataUofManchester)
[com/kelpman05/DataWuzhong.](https://github.com/kelpman05/DataWuzhong)


**Code availability**
[The computational code is available at https://github.com/kelpman05/CodePoSoNE.](https://github.com/kelpman05/CodePoSoNE)


Received: 23 June 2021; Accepted: 7 April 2022;
Published online: 2 June 2022


**References**
1. Morstyn, T., Farrell, N., Darby, S. J. & McCulloch, M. D. Using peer-to-peer
energy-trading platforms to incentivize prosumers to form federated power
plants. _Nat. Energy_ **3**, 94–101 (2018).
2. Parag, Y. & Sovacool, B. K. Electricity market design for the prosumer era.
_Nat. Energy_ **1**, 16032 (2016).
3. Liu, X. & Mancarella, P. Modelling, assessment and sankey diagrams of
integrated electricity–heat–gas networks in multi-vector district energy
systems. _Appl. Energy_ **167**, 336–352 (2016).
4. Thomas, L., Zhou, Y., Long, C., Wu, J. & Jenkins, N. A general form of smart
contract for decentralized energy systems management. _Nat. Energy_ **4**,
140–149 (2019).
5. Kargarian, A. et al. Toward distributed/decentralized DC optimal power flow
implementation in future electric power systems. _IEEE Trans. Smart Grid_ **9**,
2574–2594 (2018).
6. Andoni, M. et al. Blockchain technology in the energy sector: a systematic
review of challenges and opportunities. _Renew. Sust. Energy Rev._ **100**,
143–174 (2019).
7. Di Silvestre, M. L. et al. Blockchain for power systems: current trends and
future applications. _Renew. Sust. Energy Rev._ **119**, 109585 (2020).
8. Belotti, M., Božić, N., Pujolle, G. & Secci, S. A vademecum on blockchain
technologies: when, which, and how. _IEEE Commun. Surv. Tutor._ **21**,
3796–3838 (2019).
9. Hassan, N. U., Yuen, C. & Niyato, D. Blockchain technologies for smart
energy systems: fundamentals, challenges, and solutions. _IEEE Ind. Electron._
_Mag._ **13**, 106–118 (2019).
10. Nakamoto, S. _Bitcoin: A Peer-to-Peer Electronic Cash System_ (Bitcoin, 2008).
[11. Etherscan (Etherscan, 2015); https://etherscan.io/](https://etherscan.io/)
12. Comprehensive Guide to Companies Involved in Blockchain & Energy
(SolarPlaza, 2018).
13. Li, J., Li, N., Peng, J., Cui, H. & Wu, Z. Energy consumption of
cryptocurrency mining: a study of electricity consumption in mining
cryptocurrencies. _Energy_ **168**, 160–168 (2019).
14. Kiayias, A., Russell, A., David, B. & Oliynykov, R. A Provably Secure
Proof-of-Stake Blockchain Protocol. _In Annual International Cryptology_
_Conference_ (eds Katz, J. et al.) 357–388 (Springer, 2017).
15. Fan, X. & Chai, Q. Roll-DPoS: a randomized delegated proof of stake scheme
for scalable blockchain-based Internet of things systems. In _Proc. 15th EAI_
_International Conference on Mobile and Ubiquitous Systems: Computing,_
_Networking and Services_ (eds Schulzrinne, H. et al.) 482–484 (ACM, 2018).
16. Rouhani, S. & Deters, R. Performance analysis of Ethereum transactions in
private blockchain. In _8th IEEE International Conference on Software_
_Engineering and Service Science_ (eds Li, W. et al.) 70–74 (IEEE, 2017).
17. Miller, A., Juels, A., Shi, E., Parno, B. & Katz, J. Permacoin: repurposing
Bitcoin work for data preservation. In _2014 IEEE Symposium on Security and_
_Privacy_ (eds Shannon, G.) 475–490 (IEEE, 2014).
18. Castro, M. & Liskov, B. Practical Byzantine fault tolerance. _OSDI '99: Proc._
_3rd symposium on Operating systems design and implementation_ . (eds Seltzer,
M. et al.) 173–186 (USENIX Association, 1999).
19. Su, Z. et al. A secure charging scheme for electric vehicles with smart
communities in energy blockchain. _IEEE Internet Things J._ **6**, 4601–4613 (2019).
20. Tushar, W., Saha, T. K., Yuen, C., Smith, D. & Poor, H. V. Peer-to-peer
trading in electricity networks: an overview. _IEEE Trans. Smart Grid_ **11**,
3185–3200 (2020).
21. Tushar, W. et al. Challenges and prospects for negawatt trading in light of
recent technological developments. _Nat. Energy_ **5**, 834–841 (2020).
22. Tushar, W. et al. Peer-to-peer energy systems for connected communities:
a review of recent advances and emerging challenges. _Appl. Energy_ **282**,
116131 (2021).
23. Molzahn, D. K. et al. A survey of distributed optimization and control
algorithms for electric power systems. _IEEE Trans. Smart Grid_ **8**,
2941 – 2962 (2017).
24. AlAshery, M. K. et al. A blockchain-enabled multi-settlement quasi-ideal
peer-to-peer trading framework. _IEEE Trans. Smart Grid_ **12**, 885–896 (2020).



25. Shibata, N. Proof-of-search: combining blockchain consensus formation with
solving optimization problems. _IEEE Access_ **7**, 172994–173006 (2019).
26. Luo, F., Dong, Z. Y., Liang, G., Murata, J. & Xu, Z. A distributed electricity
trading system in active distribution networks based on multi-agent coalition
and blockchain. _IEEE Trans. Power Syst._ **34**, 4097–4108 (2019).
27. Li, Y., Yang, W., He, P., Chen, C. & Wang, X. Design and management of a
distributed hybrid energy system through smart contract and blockchain.
_Appl. Energy_ **248**, 390–405 (2019).
28. Wang, S., Taha, A. F., Wang, J., Kvaternik, K. & Hahn, A. Energy
crowdsourcing and peer-to-peer energy trading in blockchain-enabled smart
grids. _IEEE Trans. Syst. Man Cybern._ **49**, 1612–1623 (2019).
29. Di Silvestre, M. L. et al. Ancillary services in the energy blockchain for
microgrids. _IEEE Trans. Ind. Appl._ **55**, 7310–7319 (2019).
30. Ott, A. L. Experience with PJM market operation, system design, and
implementation. _IEEE Trans. Power Syst._ **18**, 528–534 (2003).
31. Erseghe, T. Distributed optimal power flow using ADMM. _IEEE Trans. Power_
_Syst._ **29**, 2370–2380 (2014).
32. Aguado, J. & Quintana, V. Inter-utilities power-exchange coordination: a
market-oriented approach. _IEEE Trans. Power Syst._ **16**, 513–519 (2001).
33. Le, X., Chen, S., Yan, Z. & Xi, J. A neurodynamic approach to distributed
optimization with globally coupled constraints. _IEEE Trans. Cybern._ **48**,
3149–3158 (2018).
34. Chen, S., Xu, C., Yan, Z., Guan, X. & Le, X. Accommodating strategic players
in distributed algorithms for power dispatch problems. _IEEE Trans. Cybern_ .
[https://doi.org/10.1109/TCYB.2021.3085400 (2021).](https://doi.org/10.1109/TCYB.2021.3085400)
35. Chen, S. et al. A trusted energy trading framework by marrying blockchain
and optimization. _Adv. Appl. Energy_ **2**, 100029 (2021).
36. Chen, S., Zhang, L., Yan, Z. & Shen, Z. A distributed and robust
security-constrained economic dispatch algorithm based on blockchain. _IEEE_
_Trans. Power Syst._ **37**, 691–700 (2022).
37. Ping, J., Yan, Z., Chen, S., Yao, L. & Qian, M. Coordinating EV charging via
blockchain. _J. Mod. Power Syst. Clean Energy_ **8**, 573–581 (2020).
38. Ping, J., Yan, Z. & Chen, S. A two-stage autonomous EV charging
coordination method enabled by blockchain. _J. Mod. Power Syst. Clean Energy_
**9**, 104–113 (2021).
39. Conejo, A., Castillo, E., Minguez, R. & Garcia-Bertrand, R. in _Decomposition_
_Techniques in Mathematical Programming_ (eds Conejo, A. et al.) Ch. 1
(Springer, 2006).
40. Palomar, D. P. & Chiang, M. A tutorial on decomposition methods for network
utility maximization. _IEEE J. Sel. Areas Commun._ **24**, 1439–1451 (2006).
41. Cao, Y. et al. Decentralized operation of interdependent power distribution
network and district heating network: a market-driven approach. _IEEE Trans._
_Smart Grid_ **10**, 5374–5385 (2019).
42. Liu, X., Yan, Z. & Wu, J. Optimal coordinated operation of a multi-energy
community considering interactions between energy storage and conversion
devices. _Appl. Energy_ **248**, 256–273 (2019).
43. Gilad, Y., Hemo, R., Micali, S., Vlachos, G. & Zeldovich, N. Algorand: scaling
Byzantine agreements for cryptocurrencies. In _Proc. 26th ACM Symposium on_
_Operating Systems_ _Principles_ (eds Chen, H. et al.) 51–68 (ACM, 2017).
44. Milutinovic, M., He, W., Wu, H. & Kanwal, M. Proof of luck: an efficient
blockchain consensus protocol. In _Proc. 1st Workshop on System Software for_
_Trusted Execution_ (eds Felber, P. et al.) 1–6 (ACM, 2016).
45. Lamport, L., Shostark, R. & Pease, M. The Byzantine generals problem. _ACM_
_Trans. Program. Lang. Syst._ **4**, 382–401 (1982).


**Acknowledgements**
The study is funded by National Natural Science Foundation of China (52077138).


**Author contributions**

S.C., J.P., Z.S. and Q.X. conceived the idea. S.C., J.P. and Z.S. developed the methods.
H.M. and Z.S. developed the codes. X.L. and N.Z. provided the data. S.C., Z.Y. and
C.K. provided funding. S.C. and H.M. wrote the manuscript. All authors revised the
manuscript and responded to reviewer comments.


**Competing interests**
The authors declare no competing interests.


**Additional information**

**Extended data** [is available for this paper at https://doi.org/10.1038/s41560-022-01027-4.](https://doi.org/10.1038/s41560-022-01027-4)


**Supplementary information** The online version contains supplementary material
[available at https://doi.org/10.1038/s41560-022-01027-4.](https://doi.org/10.1038/s41560-022-01027-4)


**Correspondence and requests for materials** should be addressed to Sijie Chen.


**Peer review information** _Nature Energy_ thanks Mahmoud Nabil Mahmoud and the
other, anonymous, reviewer(s) for their contribution to the peer review of this work.


**Reprints and permissions information** [is available at www.nature.com/reprints.](http://www.nature.com/reprints)


**Publisher’s note** Springer Nature remains neutral with regard to jurisdictional claims in
published maps and institutional affiliations.


© The Author(s), under exclusive licence to Springer Nature Limited 2022



**502** **Nature Energy** [| VOL 7 | June 2022 | 495–502 | www.nature.com/natureenergy](http://www.nature.com/natureenergy)


#### **NaTurE EnErgy Articles**

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenBlockchainConsensusMechanism2022/chenBlockchainConsensusMechanism2022.pdf-8-7.png)

**Extended Data Fig. 1 |** **Exchanged messages under different optimization schemes considering dishonesty.** **a**, Centralized. **b**, Hierarchical. **c**,
Blockchain-as-coordinator. **d**, PoSo. An honest market operator or delegate lets all communities see a same trading price 639.9 CNY/MWh. A dishonest
market operator or delegate, colluding with _m_ 1 and _m_ 6, sends _m_ 5 an electricity price of 292.4 CNY/MWh, 60% lower than that to other communities. By
letting _m_ 5 see a lowered price and produce less MWh, _m_ 1 and _m_ 6 can raise the market price (from 639.9 CNY/MWh to 731.0 CNY/MWh) and their sales
(see Supplementary Note 4). The supply and demand curves and the finalized trading prices and volumes of _m_ 5, _m_ 1 & _m_ 6, and the other nine communities
are shown at the bottom of each sub-figure. Per the supply curves, _m_ 5 will sell 334.8 MWh at a price of 292.4 CNY/MWh or 713.2 MWh at a price of
639.9 CNY/MWh. Similarly, the finalized trading price and volume of each other community, whether manipulated or not, are also aligned with its supply
or demand curve in each optimization scheme.


**Nature Energy** [| www.nature.com/natureenergy](http://www.nature.com/natureenergy)


