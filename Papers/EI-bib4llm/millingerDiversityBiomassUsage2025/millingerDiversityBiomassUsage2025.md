# Citation Key: millingerDiversityBiomassUsage2025

---

## **nature energy**

**Article** [https://doi.org/10.1038/s41560-024-01693-6](https://doi.org/10.1038/s41560-024-01693-6)
# **Diversity of biomass usage pathways to** **achieve emissions targets in the European** **energy system**



Received: 22 June 2023


Accepted: 10 December 2024


Published online: 23 January 2025


Check for updates



**<PERSON><PERSON>** **[1,2]** **, F. <PERSON>** **[1]** **, E. <PERSON>** **[3]** **, <PERSON><PERSON>** **[3]** **, <PERSON><PERSON>** **[1]** **&**
**G. <PERSON>** **[1]**


Biomass is a versatile renewable energy source with applications across the
energy system, but it is a limited resource and its usage needs prioritization.
We use a sector-coupled European energy system model to explore
near-optimal solutions for achieving emissions targets. We find that
provision of biogenic carbon has higher value than bioenergy provision.
Energy system costs increase by 20% if biomass is excluded at a net-negative
(−110%) emissions target and by 14% at a net-zero target. Dispatchable
bioelectricity covering ~1% of total electricity generation strengthens
supply reliability. Otherwise, it is not crucial in which sector biomass is
used, if combined with carbon capture to enable negative emissions and
feedstock for e-fuel production. A shortage of renewable electricity or
hydrogen supply primarily increases the value of using biomass for fuel
production. Results are sensitive to upstream emissions of biomass, carbon
sequestration capacity and costs of direct air capture.



Biomass is a diverse and versatile renewable energy source that can be
used for various purposes [1][–][3] . In the electricity system, it can complement the variable renewable energy (VRE) sources of solar and wind
power [4][–][7] and provide dispatchable (firm) generation to meet demand
even in periods of supply shortage in a VRE-based energy system [8][,][9] .
If used for combined heat and power (CHP), it can provide flexible
energy, which may be especially important during so called cold–dark
doldrums, when space heat demand is high and electricity supply from
wind and solar is low [10][,][11] . Biomass can also supply hydrocarbons to sectors that are challenging to electrify and where renewable alternatives
are scarce, such as aviation and marine transport [12][–][14], or plastics and
high-value chemicals [15][–][17] . Also, it can be used to provide process heat for
industry [18][,][19] . All of these options can to some extent be combined with
carbon capture (BECC) to provide carbon for further usage (BECCU),
or negative emissions through geological sequestration (BECCS) [4][,][6][,][18][,][19] .
In contrast to direct air capture (DAC), which requires a substantial
electricity and heat input to extract CO 2 from the atmosphere [20], BECC



captures more concentrated CO 2 in exhaust and waste streams and
provides net energy output along with the carbon capture.
The European Union and United Kingdom have adopted targets
of net-zero greenhouse gas (GHG) emissions for all sectors to comply
with the Paris Agreement targets [21][,][22] . To achieve such targets, residual
emissions, such as methane emissions in agriculture, need to be offset
by carbon dioxide removal (CDR) from the atmosphere, where BECCS
and DACCS emerge as key options for technical CDR [23][–][25] .
Biomass is a limited resource and its use for energy can be associated with a range of positive and negative environmental, social and
economic effects that are context specific and depend on land type
and climatic region, prior land use and how bioenergy feedstock and
management regimes are shaped [26][–][33] . Due to concerns about possible
environmental impacts, insufficient emissions reductions and competition with the food sector, EU policy has capped biofuels from food
and feed crops and increasingly emphasizes lignocellulosic biomass,
especially residues and waste [34][,][35], and prioritizes the biomass usage to



1 Department of Space Earth and Environment, Chalmers University of Technology, Göteborg, Sweden. 2 Built Environment: System Transition: Energy
Systems Analysis, RISE Research Institutes of Sweden, Göteborg, Sweden. [3] Department of Digital Transformation in Energy Systems, Technische
Universität Berlin, Berlin, Germany. [e-mail: <EMAIL>](mailto:<EMAIL>)


[Nature Energy | Volume 10 | February](http://www.nature.com/natureenergy) 2025 | 226–242 **226**


**Article** https://doi.org/10.1038/s41560-024-01693-6



energy applications where other alternatives are currently difficult to
find or considered to be too costly.
All of the possible biomass usage options face competition from
electricity-derived energy carriers and fossil fuels (Extended Data
Fig. 1). A full systems analysis of biomass allocation to different energy
uses therefore requires broad coverage of options and sectors. Such
analyses have been carried out with global integrated assessment
models (IAMs), with a large variety in results, but with biomass in the
longer term generally ending up being used for electricity and/or liquid
fuels production [36], coupled with carbon capture and storage (CCS) [37][,][38] .
The potential value of negative emissions from BECCS has been found
to be very high, enabling the achievement of more ambitious climate
targets [37][,][39][–][41] or delayed phase-out of fossil fuels if temperature overshoot is permitted [40][,][41] . The latter raises concerns over risks in relying
on future technology deployment to compensate for earlier emissions
and over intergenerational equity [42][–][45] .
However, IAMs lack the spatio-temporal detail needed to capture
the variability of, for instance, VRE and electrolysis, and the interplay
of these technologies with biomass options, such as dispatchable bioelectricity. In addition, the costs of VRE have often been overestimated
in IAM-based analyses [46][,][47] and carbon sequestration capacities may be
more limited than what has been assumed [48][,][49] ; both of these factors risk
exaggerating the role of CCS for meeting climate targets [48][,][50] . Moreover, IAMs have until now not included carbon capture and utilization
(CCU) or electrofuels [11][,][47], leaving biofuels as the only non-fossil-fuel
option, and until recently also not included DAC as an alternative CDR
option [47][,][51][,][52] . Most or all of these limitations apply also to previous IAM
analyses focusing specifically on biomass and/or BECCS [36][,][37][,][39][–][41][,][53][–][59],
leading to potential biases in the cost effectiveness of biomass usage,
BECCS and different biomass utilization pathways.
Energy system models (ESMs) on the other hand commonly represent VRE explicitly, with a high spatial and temporal resolution, and
some ESMs have recently been enhanced to encompass all energy
sectors simultaneously [60][,][61] . This enables a sector-coupled analysis of
biomass usage for energy across all sectors and of interactions with
competing fuel options that can be produced from VRE sources, such
as hydrogen and electrofuels. However these models usually include
a restricted selection of biomass applications and, in contrast to IAMs,
only a few studies based on sector-coupled ESMs have focused explicitly on biomass, bioenergy and/or BECC [11][,][62], and a thorough assessment
of biomass usage including BECCUS across usage options is still lacking.
Further, combining bioenergy processes with conventional carbon
capture results in higher costs for the additional capture and heat
infrastructure and energy penalties to provide the substantial process
heat needed to regenerate solvents. Such details have, to date, not been
included in analyses with sector-coupled ESMs but are also lacking in
many IAMs (Extended Data Table 1).
IAM and ESM studies commonly focus on the single cost-optimal
solution, complemented with some sensitivity analyses. However,
social planning projects are subject to a plurality of economic and
socio-political objectives [63][,][64], and uncertainties and objectively irreconcilable trade-offs at different levels regarding future energy systems [65]
and biomass use [66][–][68] are so-called wicked facets of their planning [69] .
The sector-coupled energy system involves diverse stakeholders with
conflicting non-economic objectives and risk perceptions, and past
energy transitions have been found not to follow cost-optimal paths
in hindsight [70] . There is therefore a value in exploring the diversity of
near-optimal solutions for the energy system in general and for biomass
usage in particular, to provide insights for policy about the flexibility
of solutions [71] . Recent analyses have shown that the technology mix
variety of near-optimal solutions, when allowing a small system cost
increase, can be distinctly different from the single least-cost solution,
in heat supply [72], the power system [73][–][76], in integrated assessments [77]
and in a sector-coupled European energy system [61][,][64] . The available
amount of biomass has been found to affect the manoeuvring space



substantially [61], calling for a thorough assessment of near-optimal
solution spaces for biomass usage in the energy system. Also, whereas
net-negative emissions trajectories have been assessed widely with
IAMs, few net-negative analyses have been performed with ESMs [11][,][78] .
This study addresses this gap by using a sector-coupled ESM,
with a more comprehensive coverage of bioenergy technologies and
BECC than in similar modelling studies (Extended Data Table 1). The
overarching goal of the study is to analyse effects on the system cost of
broad ranges of biomass supply and biomass technology deployment
in a European energy system adhering to stringent emissions targets.
This is done through a detailed exploration of the near-optimal solution
space for biomass usage options, within the sector-coupled European
energy system optimization model PyPSA-Eur-Sec. The effects of different deployment levels for wind power, solar PV and electrolysers on
biomass usage, and vice versa, are also assessed. More specifically, we
analyse the solution space around the least-cost optimum, for system
cost increases of 1%, 5%, 10%, through to 25%.
Scenarios with net-negative (−110%) and net-zero CO 2 emissions
compared with 1990 levels are assessed. No explicit target year is modelled, as focus is on how these targets can be met cost effectively, rather
than on pathways leading there. European policies prioritize emissions
reductions over compensation of emissions through CDR [22][,][44][,][79][–][82] . Also,
although theoretical geological storage capacities are vast, investable potentials and CO 2 injection rates as indicated by historical fossil extraction may be limited [48] . Reflecting these aspects, in the main
scenarios, we assess an energy system where only very little compensation of concurrent fossil fuel usage through negative emissions is
permissible by setting carbon sequestration capacities near the limit
for what is necessary to achieve the respective emissions targets, while
also offsetting process emissions considered to be unavoidable, for
instance, from cement production (Extended Data Fig. 2; 600 Mt CO 2
per year for net-negative (−110%) and 140 Mt CO 2 per year for net-zero,
thus allowing the same slack for fossil fuel usage in both cases). The
effect of higher assumed carbon sequestration capacities is analysed
and discussed in a sensitivity analysis.
Reflecting current EU policy direction, bioenergy feedstock is
assumed to consist of residues, with a domestic supply potential corresponding to the medium level in the JRC ENSPRESO database [83] .
Import of non-digestible biomass represents a complementary, but
more expensive, feedstock source [11] (Extended Data Fig. 3). The effect
of variations to these assumptions is assessed in sensitivity analyses.
We find that provision of biogenic carbon for negative emissions
and utilization has a higher value than bioenergy provision. Energy
system costs increase by 20% if biomass is excluded at a net-negative
(−110%) emissions target. Dispatchable bioelectricity covering ~1% of
total electricity generation strengthens supply reliability. Otherwise,
it matters less whether biomass is used for combined heat and power,
liquid fuel production or industrial process heat, as long as the carbon content is utilized to a high extent, as facilitated through carbon
capture to provide renewable carbon for negative emissions or for
production of fuels for further use in the energy system.


**The cost of varying biomass use in the energy system**
In the cost-optimal solution for the net-negative scenario, wind (54%),
solar photovoltaics (PV) (40%) and hydropower (5%) supply 99% of the
whole electricity demand at 9,250 TWh (Extended Data Fig. 4), which is
almost three times the electricity demand in 2021 [84][,][85] . Biomass is mainly
used to complement the supply of fuels and chemicals for industry, aviation and shipping, but a small share is also used to supply dispatchable
electricity (Extended Data Fig. 4). Some 637 TWh biogas and 2,896 TWh
solid biomass (2,172 TWh imported) are used, corresponding to 29%
of the annual primary energy consumption at 13 PWh. Solid biomass
usage amounts to about two times the 1,290 TWh used in 2021, when
overall bioenergy usage was at 1,937 TWh (refs. 84,85). Around 87% of
biomass usage is combined with carbon capture, with the exception



[Nature Energy | Volume 10 | February](http://www.nature.com/natureenergy) 2025 | 226–242 **227**


**Article** https://doi.org/10.1038/s41560-024-01693-6



**a** All biomass excluding MSW **b** CHP **c** Process
heat



**d** Biofuel



6,000


5,000


4,000


3,000


2,000


1,000


0



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-2-1.png)

0 1 3 5


|Col1|Maximum biomass demand (net)|
|---|---|
|||


|c  Process    heat|Col2|
|---|---|
|Maximum<br>biomass demand<br>(excluding DAC)|Maximum<br>biomass demand<br>(excluding DAC)|
|||



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-2-0.png)

0


0





10 15 20



6,000


5,000


4,000


3,000


2,000


1,000


0


**e** BECC


2,000


1,500


1,000


500


0



1 3 5



0 1 3 5 0 1 3 5 10



5



_ε_ (%) _ε_ (%) _ε_ (%)



_ε_ (%)


**Fig. 1 | Solution spaces for different biomass usages in the net-negative**
**emissions scenario with an allowed carbon sequestration potential close to**
**what is necessary to achieve the target.** The respective options are minimized
and maximized to map out the space of feasible solutions (shaded area) for a
given allowed system cost increase _ε_ (percent deviation from the total system
cost). **a**, **e**, The solution space when varying the amount of biomass (except
municipal solid waste) ( **a** ) and the solution space when varying bioenergy with
carbon capture deployment ( **e** ). In **a**, solid biomass usage in 2021 [84][,][85] is shown and


being dispatchable biomethane applications. The total annual system
cost amounts to €822 billion.

If overall biomass usage is restricted to current usage levels, the
system cost ends up ~5% higher than without restrictions. If all biomass
(except mandatory incineration of municipal solid waste) is excluded,
it leads to a 20% higher system cost (Fig. 1a), or an additional cost of
€169 billion annually, roughly corresponding to European defence
expenses [86] . This is twice as much as the cost of excluding solar power
and similar to the cost of excluding wind power, despite both of them
cost optimally providing more primary energy (Fig. 2d,e). Excluding
any of these primary energy sources thus leads to much higher costs.
Wind and solar power are more readily interchangeable whereas the
substitution of biomass is much more expensive because biomass provides non-fossil carbon in addition to energy, for which the substitute,
DAC, coupled with a necessary expansion of additional energy provision, ends up much more expensive. Wind and solar power cannot be
excluded simultaneously even at a 25% system cost increase (Fig. 2f).
The assumed biomass availability determines both relative and
absolute costs of excluding biomass. If biomass imports are not
included as an option, excluding biomass altogether results in a 9%
higher system cost. If the high domestic residue potential estimate
from JRC ENSPRESO is used instead of the medium potential, and
biomass imports are included as an option, the system cost increases
29% when excluding biomass altogether. Excluding biomass imports
results in substantially higher solid biomass and CO 2 prices (Table 1),
indicating a high system pressure for increasing biomass supply.
Substantial flexible dispatchable methane-based power capacities
emerge in the net-negative scenario (521 GW el open-cycle gas turbines
and gas CHPs), on par with the inflexible electricity demand peak of
526 GW (base-load household, commercial and industrial electricity
demand, excluding heat). This flexible power capacity is seldom used,
with capacity factors of 28% (Fig. 3), which renders the addition of
costly carbon capture to these power plants prohibitively expensive.
For carbon capture to be cost effective for a particular technology,
high utilization rates are needed due to the high investment cost of
the additional infrastructure.



_ε_ (%)


is similar to the assumed medium domestic residue potential, in contrast to the
high potential, both from JRC ENSPRESO [83] . Total bioenergy in 2021 [84][,][85] includes
biomass residues and agricultural crops. **b** – **d**, Horizontal lines show biomass
demands if the full demand for district heat ( **b** ), industrial process heat ( **c** ) and
liquid fuels ( **d** ) would be fulfilled by solid biomass. The heat demand for CHP can
be expanded to supply thermal storage and the industrial process heat demand
can be used to supply DAC heat demand. MSW, incineration of municipal solid
waste, which is set to be compulsory.


Although only 225 TWh (bio)methane is used to flexibly supplement variable renewable electricity supply (covering 1% of total generation), this option is the most costly to replace and remains longest when
biomass usage is minimized (Fig. 2a). Different to studies limited to the
power system only, which indicate a substantially larger firm generation
energy requirement [8], or IAM studies, which often obtain substantial
biomass use for electricity production [36], this study finds lower levels
of bioelectricity use because the sector-coupled model entails large
flexible demand capacities such as electrolysers, heat storage, batteries
and electric vehicles, which handle most of the variability in the power
system and thus support high VRE shares (Fig. 3).
System flexibility from sector coupling, energy storage and transmission reduces the dependency on biomass. With lower assumed
flexibility, the system cost of excluding biomass increases from 20%
to 23% (Table 1). Similar amounts of biomass are used for flexible bioelectricity, but least-cost biomass usage shifts from fuel production
to heat generation.
Excluding biomass in the net-zero scenario increases system costs
by 14%, substantially less than the 20% increase in the net-negative
scenario. The cost-optimal biomass use (Fig. 4) is 36% lower than in the
net-negative scenario and within the range of the European Commission net-zero scenarios (2,200–2,900 TWh) (ref. 87). Biomass usage is
still cost optimally coupled with carbon capture, and solution spaces
for individual options are rather similar to the net-negative scenario.


**Biomass carbon is more valuable than bioenergy**
For the net-negative scenario, 87% of biomass use is cost optimally
combined with carbon capture, providing 0.84 Gt biogenic CO 2 annually, corresponding to ~21% of total regional GHG emissions in 2021, at
4 Gt CO 2 -equivalent (ref. 88). The captured amount falls within projected
feasible CCS growth already for 2040, of 1–4.3 Gt per year globally [49], but
would require a ramp-up of BECC from currently near-zero commercial
capacity to covering almost all biomass conversion.
Renewable carbon provision is the key system service of biomass, more so than the energy provided. The only other alternative
for non-fossil carbon provision is DAC, which is substantially less



10 15



[Nature Energy | Volume 10 | February](http://www.nature.com/natureenergy) 2025 | 226–242 **228**


**Article** https://doi.org/10.1038/s41560-024-01693-6



8,000


6,000


4,000


2,000


0


8,000


6,000


4,000


2,000


0


1,000


500


0



**a** All biomass excluding **b** BECC **c** Electrolysis **d** Wind **e** Solar **f** VRE



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-3-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-3-1.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-3-2.png)

0 1 3 5 10 15 20 0 5 10 15 0 5 10 15 20 0 5 10 15 20 0 5 10 0 5 10 15 20 25


_ε_ (%) _ε_ (%) _ε_ (%) _ε_ (%) _ε_ (%) _ε_ (%)



Biofuel + electrobiofuel



BioSNG Biomass CHP Biogas Biomass process heat



Wind Solar Electrolysis Electrofuel Nuclear



BECC



DAC



**Fig. 2 | Effect on the rest of the energy system when decreasing different**
**options from cost-optimal levels.** Upper panels show biomass usage (TWh
biomass), the middle panels show other energy technologies (TWh) and the
lower panels show carbon capture (Mt CO 2 for BECC (bioenergy with carbon
capture) and DAC (direct air carbon capture)). **a**, For example, total biomass
(excluding mandatory waste incineration) is the objective function, which is
minimized. In the upper panel the effect on biomass usage can be observed


cost competitive than the available alternative energy provision
options. The value of biogenic carbon is estimated to be up to over
three times higher than the value of the primary energy provision at
low biomass usage levels in a renewable energy system, in both the
net-negative (Fig. 5a) and net-zero scenarios (Fig. 5c). When a higher
carbon sequestration allowance permits a larger amount of fossil
fuels to be offset by the sequestration of biogenic CO 2, the value of
the biogenic carbon is up to two times higher (Fig. 5b). With varying
amounts of biomass in the system, CO 2 and solid biomass prices are
strongly and similarly affected, whereas the hydrogen price is substantially less affected.
BECC can be excluded at a 13% system cost increase (Fig. 1e), with
mainly biofuel production decreasing whereas biogas and biomass
usage for process heat and flexible bioelectricity remain cost effective also without BECC (Fig. 2b). If BECC is removed, biomass can be
excluded within a 6% cost increase (Table 1), substantially less than with
BECC. Capturing biogenic CO 2 emissions enhances carbon utilization,
enabling scarce renewable carbon to be used multiple times and to
provide negative emissions. As BECC is decreased, biomass usage also
decreases, and DAC increases to provide both the necessary negative
emissions and carbon for the production of electrofuels, resulting in
higher total carbon capture deployment (Fig. 2b).
In the least-cost case, the shadow price (marginal price of an additional MWh) of solid biomass amounts to €54 MWh [−1] (as determined
by the import biomass price) and €135 MWh [−1] if biomass is excluded
(Fig. 5a and Table 1). This is substantially higher than the cost of domestic residue supply (Extended Data Fig. 3 and Extended Data Table 2) or
2020 wood chip prices at €20–25 MWh [−1] (ref. 89). The resulting CO 2
price (marginal cost of CO 2 emissions) amounts to ~€260 t [−1] CO 2 in the
cost-optimal case but increases to €591 t [−1] CO 2 if biomass is excluded



as total biomass use decreases. In the middle panel the effect on other energy
technologies such as wind power can be observed as total biomass use decreases,
and in the lower panel the effect on carbon capture is shown. **b** – **e**, BECC is
minimized as shown in the lower panel ( **b** ), whereas for electrolysis ( **c** ), wind
power ( **d** ) and solar PV ( **e** ), the minimized technology is shown as a solid line in
the middle panels. **f**, The results when minimizing variable renewables (solar PV
and wind power). BioSNG, biogenic synthetic natural gas.


(Fig. 5a and Table 1), indicating a high value of biomass resources to
achieve emissions targets.


**Biomass allocation is not crucial if carbon is**
**captured**
Solid biomass is cost optimally used for biofuel production and process
heat (Extended Data Fig. 4), but a large range of near-optimal solutions
for different usage options exist (Fig. 1b–d). Thus, even though it is
costly to exclude overall biomass usage, it is not so important in which
sectors biomass is used.

District heat is cost optimally covered by a mix of excess heat
from biofuel production and electrolysers, waste incineration, electric
boilers and some (bio)methane-fuelled CHP (Extended Data Fig. 4).
Whereas absent in the cost-optimal solution, solid biomass CHP can
cover up to 50% of district heat demand within a 1% system cost increase
(corresponding to 16% of district heat cost and thus not increasing the
sectoral costs substantially; Fig. 1b). These CHP plants are invariably
equipped with carbon capture, which increases capital cost substantially (Extended Data Table 1), and they are therefore run with high
capacity factors (>90%), coupled with heat storage, highlighting the
priority of carbon capture over additional variation management supporting wind and solar feed-in.
Solid biomass competes with hydrogen and (bio)methane for
process heat supply in the medium-temperature range and mainly with
electricity for process steam production. It can cover a span of 0–100%
in these sub-sectors within the range of a 0.5% system cost increase (7%
of costs in these sectors; Fig. 1c). Thus, a very diverse set of alternative
options exists within a small cost span.
Biofuels cover a span of 20–61% of liquid fuel demand for aviation,
shipping and chemicals already within the range of a 1% system cost



[Nature Energy | Volume 10 | February](http://www.nature.com/natureenergy) 2025 | 226–242 **229**


**Article** https://doi.org/10.1038/s41560-024-01693-6



**Table 1 | Sensitivity of near-optimal system cost and other**
**metrics**



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-4-0.png)









High + 768 3,561 262 54 223 29
import



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-4-1.png)







Assumed biomass amounts and other variations for achieving an −110% net-negative
emissions (and in the last case for net zero). High- and medium-biomass scenarios use the
corresponding potentials from ref. 146 shown in Fig. 1, with (+) and without (−) biomass
imports. The details of the lower flexibility scenario are elaborated in Methods.


increase (Fig. 1d). A wide near-optimal range for fuel supply appears:
direct biomass usage for liquid fuel production can be excluded at a 3%
system cost increase (8% of liquid fuel supply cost), while covering the
full liquid fuel demand with only biofuels can be done at an 8% system
cost increase (resulting in more than a doubling of biomass imports).
When biomass use is decreased from the cost-optimal amount,
the primary change occurs in the production of liquids used as fuels in
aviation and shipping and as feedstock for chemicals, where bioliquids
are replaced by electrofuels. Electricity generation from wind and solar
power increases more than the usage of bioenergy decreases due to
the increasing electricity demand for electrolysis and DAC to supply
hydrogen and carbon for electrofuel production (Fig. 2a).


**Role of biomass if VRE or electrolyser capacity is**
**limited**

The cost-optimal results depend on a large expansion of solar and
onshore wind power and even more so if biomass is excluded (Fig. 2a
and Extended Data Table 3). Achieving these VRE capacities requires an
unprecedented capacity growth at the European scale, which might be
hindered, for instance, by industry scale-up inertia and local opposition
where wind and solar projects are planned [5][,][90][–][94] . When VRE is restricted
to a level below the least-cost case, a decrease in total electricity generation and hydrogen electrolysis to supply electrofuel production
is observed, whereas biomass use increases to supply more biofuel
production and process heat (Fig. 2f).
Results also depend on a large capacity expansion of hydrogen
electrolysis, which similarly requires an unprecedented scale-up
(Extended Data Table 3). The cost-optimal electrolysis capacity is
more than two times projected feasible capacity growth by 2050 for the
European Union and covering a substantial share of projected global
capacities [95] and again even more so if biomass is excluded (Extended
Data Table 3). Decreasing electrolysis from cost-optimal levels leads to
a corresponding reduction in electricity consumption and a decrease



in electrofuel production, which is again balanced by an expansion of
biomass use to supply biofuel production. Electrolysis can be excluded
at an ~20% system cost increase, at which point biomass use is almost
doubled compared to cost-optimal levels (Fig. 2c). A similar magnitude
of biomass usage emerges when VRE is minimized within the same
system cost increase (Fig. 2f).
Thus VRE or electrolyser expansion inertia primarily affects liquid
fuel supply and leads to a much higher demand for biomass if emission
targets are to be achieved. Vice versa, a shortage of biomass increases
the value of electrofuels.


**Sensitivity to upstream emissions and**
**sequestration capacity**
Domestic biomass resources are limited to residues, which have a
lower risk of indirect emissions compared to dedicated energy crops,
but residue extraction can cause soil carbon losses and impact soil
health [96][–][100], which in turn can impact yield levels, potentially leading to
indirect emissions if production is expanded elsewhere to compensate
for declining harvest levels. In addition, biomass imported into Europe
may be associated with GHG emissions along the supply chain.
European policies can restrict imports of high GHG biomass and
require domestic residue extraction to follow best-management practices
to minimize soil impacts. Supply chain emissions can be expected to
decrease if other regions also adopt stringent emissions targets, and residual emissions can be offset through CDR. However, whereas innovation
and changes in land-management practices can lead to dramatic emissions reductions, implementation may be a multi-decadal process [23][,][101][–][104]
and biomass supply may still be associated with emissions due to weak
compliance and leakage effects [105][,][106] . Furthermore, CDR implementation
could offset emissions associated with other activities if it is not needed

to offset residual emissions from the biomass supply chain.
Assuming biomass imports to entail upstream emissions can have
a strong influence on biomass usage in the energy system, depending on the assumed allowed carbon sequestration potential (Fig 6).
A higher allowed annual carbon sequestration potential beyond the
restrictive 600 Mt CO 2 per year limit results in similar but somewhat
wider solution spaces for all biomass usage options (Fig. 4), as there is
room for using more fossil fuels if emissions are captured and stored
or counterbalanced by negative emissions.
If biomass imports are assumed to be carbon neutral (that is,
without upstream emissions), least-cost biomass usage amounts are
stable across a wide range of carbon sequestration allowances (Fig. 6a).
However, the cost to exclude biomass decreases substantially with
increasing allowed carbon sequestration potential (Fig. 6h), as it opens
up for using fossil fuels (Fig. 6i) combined with CCS, which decreases
the cost effectiveness of CCU (Fig. 6f,g).
However, already when assuming upstream emissions for biomass
imports of 10.3 g CO 2 MJ [−1], results depend heavily on the allowed carbon
sequestration potential. At a low carbon sequestration potential, only
marginal amounts of additional upstream emissions from biomass
usage can be accommodated (Fig. 6c), and the system cost of excluding
biomass altogether decreases substantially from 20% to 11% (Fig. 6e).
In this case, especially, biofuel production decreases (Fig. 6b), and DAC
is preferred to supply carbon for electrofuel production (Fig. 6d). At
allowed carbon sequestration potentials of 800–1,600 Mt CO 2, there
is room for more upstream emissions as they can be compensated for
by using more biomass (Fig. 6a) and BECC (Fig. 6d). Higher carbon
sequestration potentials allow for increasing use of fossil CCS (Fig. 6f,i)
combined with negative emissions, which are then provided by DAC
rather than BECC if biomass entails upstream emissions (Fig. 6e).


**Sensitivity to biomass and DAC cost and carbon**
**capture rate**
DAC can decrease system reliance on biomass, but BECC is more
competitive than DAC for delivering renewable carbon and negative



[Nature Energy | Volume 10 | February](http://www.nature.com/natureenergy) 2025 | 226–242 **230**


**Article** https://doi.org/10.1038/s41560-024-01693-6


OCGT



2,500


2,000


1,000


500


250


0


250


500


1,000


2,000


2,500



Gas CHP


Waste incineration


On-wind

Off-wind


PHS discharge


Solar


Hydro


V2G


Battery discharge


Base electricity load


Commercial electricity load


PHS charge


Process steam electricity


Heat pump


Electric heating


Battery charge


BEV charger


Electrolysis



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-5-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-5-1.png)

Full year 07 14 21 28
January



**Fig. 3 | Electricity generation and consumption.** Electricity generation and
consumption for achieving net-negative (−110%) emissions over the full year and for
January. 521 GW el dispatchable firm generation capacity emerges (open-cycle gas
turbines (OCGT) and gas CHPs), on par with the inflexible electricity demand peak



of 526 GW (base-load household, commercial and industrial electricity demand,
excluding heat), but capacity factors are low, at 2% and 8%, respectively. Electricity
generation and consumption are shown above and below the zero line, respectively.
BEV, battery electric vehicle; PHS, pumped hydro storage; V2G, vehicle-to-grid.



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-5-3.png)







6,000


5,000


4,000


3,000


2,000


1,000


0


6,000


5,000


4,000


3,000


2,000


1,000


0



0

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-5-2.png)


0 5 10 15


|c  Process    heat|Col2|
|---|---|
|Maximum biomass<br>demand<br>(excluding DAC)|Maximum biomass<br>demand<br>(excluding DAC)|
|||















0



0 1 3 5 10 15 20


_ε_ (%)



0 1 3 5 0 1 3 5 0 1 3 5 10


_ε_ (%) _ε_ (%) _ε_ (%)



0 5 10 15


_ε_ (%)



**Fig. 4 | Solution spaces for different biomass usages.** **a** – **e**, In a net-zero scenario
with carbon sequestration capacity set to 140 Mt CO 2 per year, close to what
is necessary to achieve the target ( **a** – **e** ), and a net-negative (−110%) emissions
scenarios with an allowed carbon sequestration potential of 1,500 Mt CO 2 per year
(900 Mt CO 2 per year more than in the base case) ( **f** – **i** ). The respective options are
minimized and maximized to map out the space of feasible solutions (shaded area)
for a given allowed system cost increase _ε_ (percent deviation from the least-cost
objective value). **a**, **f**, Solution spaces when varying the amount of biomass (except
municipal solid waste) in the net-zero scenario ( **a** ) and in the net-negative scenario
with higher carbon sequestration potential ( **f** ). **b**, **g**, Solution spaces when varying



solid biomass use for combined heat and power in the net-zero ( **b** ) and net-negative
( **g** ) scenarios. **c**, **h**, Solution spaces when varying solid biomass use for industrial
process heat in the net-zero ( **c** ) and net-negative ( **h** ) scenarios. **d**, **i**, Solution spaces
when varying solid biomass use for liquid fuel and chemical production in the netzero ( **d** ) and net-negative ( **i** ) scenarios. **e**, **j**, Solution spaces when varying bioenergy
with carbon capture deployment in the net-zero ( **e** ) and net-negative ( **j** ) scenarios.
**b** – **d**, **g** – **i**, Horizontal lines show biomass demands if the full demand for district heat
( **b**, **g** ), industrial process heat ( **c**, **h** ) and liquid fuels ( **d**, **i** ) would be fulfilled by solid
biomass. The heat demand for CHP can be expanded to supply thermal storage,
and the industrial process heat demand can be used to supply DAC heat demand.



[Nature Energy | Volume 10 | February](http://www.nature.com/natureenergy) 2025 | 226–242 **231**


**Article** https://doi.org/10.1038/s41560-024-01693-6



**a** Net-negative (–110%) 600 Mt CO 2 **b** Net-negative (–110%) 1,500 Mt CO 2 **c** Net-zero 140 Mt CO 2
600



4


3


2



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-6-0.png)



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-6-2.png)



450


300


150


60


0


1.0


0.9


0.8


0.7


0.6


0.5


0.4



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-6-1.png)



1


+141% +139%


+11% +14% +7%
0



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-6-4.png)

0 1,000 2,000 3,000



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-6-3.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-6-5.png)

0 1,000 2,000 3,000 0 1,000



2,000 3,000



Biomass used (excluding MSW) (TWh)


C/H value ratio Solid biomass price (€ MWh [−1] )

H 2 price (€ MWh [−1] ) CO 2 price (€ t [−1] CO 2 )



**Fig. 5 | Approximation of the carbon and energy value ratio of solid biomass**
**between the least-cost biomass usage and when biomass, except for MSW,**
**is excluded, as calculated through the resulting shadow prices for CO** **2** **and**
**H** **2** **.** H 2 is assumed as the reference for biomass energy, with prices consumption
weighted over all regions and time steps. Upper panels show resulting shadow
prices (left _y_ axis) and the carbon/hydrogen value ratio of solid biomass (right


emissions across a large span of biomass import prices (Fig. 7) and capture rates (Extended Data Fig. 5). Only if DAC capital costs are assumed
to be below about €200 t [−1] CO 2 per year does DAC become competitive
at an import price of €72 MWh [−1] (3–3.5 times 2020 wood chip prices)
or at carbon capture rates of 75% and below. However, there is a strong
cost incentive for achieving high capture rates to reduce expensive
primary carbon input (Extended Data Fig. 5a,b) and as long as costs for
biomass residue collection and transport and so on are covered, biomass can remain profitable at substantially lower biomass prices. DAC
then serves rather as a backstop technology to prevent high scarcity
prices for biomass and renewable carbon as raw material and enables
the achievement of emissions targets if biomass is too scarce [107] .
The system cost of excluding biomass varies between 13–25%
(€104–205 billion) at DAC capital costs corresponding to €171–
800 t [−1] CO 2 per year and a baseline biomass import price of €54 MWh [−1] .
Thus, BECC is substantially more cost competitive even with optimistic
DAC investment costs. This is likely to inhibit the scale-up of DAC and
therefore its cost progression through technological learning, which is
subject to large uncertainties even in the case of gigaton-scale deployment [108] (Extended Data Table 4). To achieve high capacity factors
and thereby keep costs down, DAC benefits greatly from a stable and
substantial supply of electricity and heat [108], and the energy source can
only cause small or zero emissions to enable net-CO 2 removal [109] . The
absence of these conditions in the short to medium term on a European
scale prevents a large scale-up of DAC (and thereby cost progression).



_y_ axis). Lower panels show the prices normalized to the value when biomass
is excluded. **a**, **b**, Net-negative (−110%) scenarios with carbon sequestration
capacity limited to 600 ( **a** ) and 1,500 Mt CO 2 ( **b** ). **c**, A net-zero scenario with
carbon sequestration capacity limited to 140 Mt CO 2 . Solid biomass is assumed to
generate 0.3667 t CO 2 MWh [−1] at combustion.


In contrast, BECC can be scaled up already in the short term, provided
that sufficient biomass is available.


**Discussion and conclusions**

Excluding biomass use in a fossil-free energy system adhering to a negative (−110%) emissions target results in a 20% higher system cost and a
substantially larger and more challenging expansion of VRE, electrolysers, electrofuels and DAC compared to if biomass is available. The cost
increase is similar to the cost of excluding wind power, and the main
reason is the high value of renewable carbon rather than of the energy
provision of biomass. It matters less whether biomass is used for combined heat and power, liquid fuel production or industrial process heat,
as long as the carbon content is utilized to a high extent, as facilitated
through carbon capture to provide renewable carbon for negative emissions or for production of fuels for further use in the energy system.
There is large potential for carbon capture also in Fischer–Tropsch
biofuel production, where ~70% of the biogenic carbon ends up in the
waste stream unless additional hydrogen is added to utilize more of the
carbon directly (electrobiofuels). Similar results emerge for a net-zero
emissions target, but biomass can then be excluded at a 14% system cost
increase due to a lower negative emissions requirement. DAC costs are
highly uncertain [108] and substantially affect the system cost of excluding
biomass, which explains some of the diversity of results in the literature
(Extended Data Table 4), but BECC was found to remain more cost effective even at low DAC costs across a range of assumptions.



[Nature Energy | Volume 10 | February](http://www.nature.com/natureenergy) 2025 | 226–242 **232**


**Article** https://doi.org/10.1038/s41560-024-01693-6



**a** **b** **c**

PWh PWh PWh



Biomass usage for biofuels Solid biomass import usage



PWh



Total biomass usage



PWh



4



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-7-2.png)



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-7-0.png)



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-7-1.png)











2,400


2,200


2,000


1,800


1,600


1,400


1,200


1,000


800


600


2,400


2,200


2,000


1,800


1,600


1,400


1,200


1,000


800


600


2,400


2,200


2,000


1,800


1,600


1,400


1,200


1,000


800


600



























3













4


3


2

















2


1















1

















4


3


2


1


0



0.8 0.7 0.6 2.2 0.9



0 0

0 5.1 10.3 15.4 20.6 0 5.1 10.3 15.4 20.6



0 5.1 10.3 15.4 20.6



**d** **e** **f**



BECC Mt CO DAC Mt CO Fossil and cement CC



Mt CO 2



Mt CO 2 Fossil and cement CC Mt CO 2



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-7-5.png)



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-7-4.png)





![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-7-3.png)





1,000


800


600


400


200


0









0

















1,000


800


600


400


200













1,000


800


600


400


200


0



























0 5.1 10.3 15.4 20.6



0 5.1 10.3 15.4 20.6



0 5.1 10.3 15.4 20.6



**g** **h** **i**



Total CC and DAC Mt CO System cost increase of excluding biomass % Fossil fuel usage



Mt CO 2



%



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-7-6.png)



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-7-7.png)



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-7-8.png)

4.6 4.6 20 4.1































PWh


6



































2,000


1,500


1,000


500







4


2


0

































0


0 5.1 10.3 15.4 20.6 0 5.1 10.3 15.4 20.6

Import biomass upstream emissions (g CO 2 MJ [−1] )



15


10


5


0



0



5.1 10.3 15.4 20.6



**Fig. 6 | Heat maps of cost-optimal configurations.** Heat maps show cost-optimal
configurations for achieving a net-negative (−110%) emissions target when
varying upstream emissions of biomass imports ( _x_ axis) and allowed annual
carbon sequestration potentials ( _y_ axis). Upstream emissions equivalent to 10%
of the biomass CO 2 emissions at full combustion corresponds to 10.3 g CO 2 MJ [−1] .
For reference, estimated nitrous oxide emissions from fertilization correspond
to 0.4–14 g CO 2 equivalent MJ [−1] for poplar or willow depending on yields and soil
conditions, and, for example, substantially more for rape seed [147] . Fossil fuels in


A shortage of either carbon-neutral biomass, VRE or hydrogen
primarily affects renewable liquid fuel production, which entails the
most conversion losses in the energy system and thereby is the marginal
emissions abatement option [11] . Ramping up sufficient resources for
liquid fuel production is therefore particularly challenging. Limiting
fuel demand through, for instance, electrification and modal shifts
would make energy supply to achieve emissions targets easier, whereas
developing a portfolio of different fuel supply options appears as a
sensible strategy to hedge against the considerable resource and technology uncertainties. Consideration of higher CO 2 sequestration levels
would allow for more fossil fuels combined with carbon capture, which
would increase the manoeuvring space and decrease the importance
of biomass and of any renewable technology but would also rely on a
stronger ramp-up of (BE)CCS (Fig. 6d).



this analysis are not assumed to entail upstream emissions, which presents an
optimistic case for their performance. **a**, **c**, Total ( **a** ) and imported ( **c** ) biomass
usage. **b**, Biomass usage for biofuels. **d** – **g**, Least-cost deployment of BECC ( **d** ),
DAC ( **e** ), fossil and cement carbon capture (CC) ( **f** ) and total carbon capture and
DAC ( **g** ). **h**, The system cost increase of excluding biomass (except municipal
solid waste) compared to the least-cost solution for all combinations of carbon
sequestration capacity and upstream emissions of biomass imports. **i**, Fossil
fuel usage.


In previous studies, biomass use has varied considerably but has
often focused on bioelectricity and liquid fuels [36], whereas power system studies on firm generation have indicated a value of biomass for
handling variability [8] . With a high spatio-temporal resolution and broad
cross-sectoral coverage, our study shows that even though substantial
dispatchable generation capacities are deployed, they are only rarely
used, and thus only a small amount of biomass is allocated for providing
flexible bioelectricity. However, although substantial amounts of bioelectricity and CHP appear as low-priority options for biomass usage in
this study (similarly to Williams et al. [78] ), many near-optimal solutions
for biomass usage were found to exist, with similar results for both
restrictive and higher carbon sequestration capacities. Thus, small
deviations in settings may lead to large differences in biomass usage,
and care must be taken when deriving general conclusions.



[Nature Energy | Volume 10 | February](http://www.nature.com/natureenergy) 2025 | 226–242 **233**


**Article** https://doi.org/10.1038/s41560-024-01693-6


**a** **b** **c**
System cost increase of
Total biomass usage PWh Solid biomass import usage PWh excluding biomass %



1,500


2,000


3,000


4,000


5,000


7,000


1,500


2,000


3,000


4,000


5,000


7,000



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-8-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-8-3.png)





![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-8-1.png)

3.6 2.5 1.4 4.2 2.2 1.1 0.0



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-8-2.png)

5.2 3.6 2.5 1.4 4.2 2.2 1.1 0.0 21 13







4


2















4


2


0





0


36 54 72 90 36 54 72 90





30


20



10


0





























36 54 72 90



**d** **e** **f**
BECC Mt CO 2 DAC Mt CO 2 Total CC and DAC Mt CO 2



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-8-4.png)



250



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-8-5.png)



1,000

























750





















500















1,000


750


500


250















0



0 0


36 54 72 90 36 54 72 90



36 54 72 90



Import biomass price (€ MWh [−1] )



**Fig. 7 | Heat maps of cost-optimal configurations.** Heat maps of cost-optimal
configurations for achieving a net-negative (−110%) emissions target when
varying the price of biomass imports ( _x_ axis) and DAC capital expenditure
( _y_ axis). For comparison between different used metrics in literature: DAC capital
costs (CAPEX) of €1,500–7,000 kg [−1] CO 2 h [−1] correspond to €171–800 t [−1] CO 2
per year at a full utilization rate or a capital cost of €16–75 t [−1] CO 2 (not including
operational expenditure) with a 7% discount rate and a 20-year lifetime.


Given the many near-optimal solutions for biomass usage, local
differences in terms of availability of biomass and non-fossil electricity
and transmission bottlenecks and carbon infrastructure, the possibility
to utilize excess heat in district heating and regional system adequacy
considerations lead to local variations in cost-competitiveness of different options (as evidenced already by the diversity of solutions on the
country-level resolution, as seen in Extended Data Fig. 6). This probably
results in a diversified portfolio of locally optimal biomass usages and
spatially resolved trade-offs between using captured carbon for fuel
production or for sequestration.
Nevertheless, as also found in previous studies with lower operational resolution [37][,][39][,][41], combining biomass usage with carbon capture
was found to be a robust strategy. Whereas these earlier studies only
include CCS, this study also includes CCU and finds both very valuable to enable high carbon efficiencies in a renewable energy system.
Allowing more fossil fuel usage compensated through CDR did not
substantially affect near-optimal biomass usage (in contrast to Grant
et al. [48] ), but it was found to reduce the competitiveness of using captured carbon to produce electrofuels (CCU), in favour of sequestering
it (CCS). In fact, failing to apply carbon capture resulted in a considerably reduced value of using biomass in the energy system. Owing to its
high investment cost, carbon capture was found to be cost effective in
processes running with high utilization rates and not in applications
managing the integration of variable renewable electricity.
A net-negative target for the European energy system is probably
needed to reach territorial net-zero emissions, considering that residual
emissions in other sectors need to be compensated; this might exert a
substantial demand pull for biomass, especially if VRE and electrolyser
deployment falls behind expectations. The resulting level of biomass
usage may even exceed the lower end of estimated global biomass residue potentials, which spans a wide range of 3–21 PWh per year (ref. 110).



The assumed capital cost of the carbon capture unit for BECC is here assumed
at a constant €2,400 kg [−1] CO 2 h [−1], but it is likely that cost progressions for DAC
would spill over also to BECC. **a**, Total biomass usage. **b**, Biomass imports. **c**, The
system cost increase of excluding biomass compared to the least-cost solution
for all combinations of carbon sequestration capacity and upstream emissions of
biomass imports. **d** – **f**, BECC ( **d** ), DAC ( **e** ) and total CC and DAC ( **f** ).


Estimated production costs for primary non-residue biomass (for
example, Millinger et al. [111] ) fall within competitive cost ranges in this
study, especially if biomass residues are limited. Thus, high biomass
demand and prices could provide an incentive for the forest and agriculture sectors to produce more primary non-residue biomass for the
energy system. The land carbon consequences in such a scenario are
uncertain; studies find that biomass demand can induce changes in
land use affecting land carbon stocks positively or negatively, depending on climate and soil conditions, historic land use, character of biomass production system being established, governance and other
geographically varying factors [104][,][112][–][116] .
The technical BECCS potential associated with domestic biomass
residues in Europe (excluding forest residues) has been estimated at
200 Mt CO 2 (ref. 117), which would not suffice to achieve net-negative
emissions targets. For the new EU Renewable Energy Directive III, energetic usage of primary forest residues was proposed to be excluded
as an option for meeting renewable targets [118], which alone have been
estimated to amount to up to 1.6 PWh per year in Europe [83], or up to
600 Mt biogenic CO 2 that could potentially be captured (Extended Data
Table 2). Excluding comparably easy-to-monitor domestic resources
might lead to a substantially higher cost of the energy system and to
a higher demand for imported biomass and dedicated crops, with
harder-to-foresee environmental consequences. As has been shown
here, biomass usage, combined with carbon capture, is cost effective as
long as net upstream emissions are relatively small or if negative emissions counteract limited upstream emissions. Exclusions of biomass
sources such as primary forest residues thus need to be weighed against
the targets in the energy sector and the potential to achieve negative
emissions and gauged towards achieved capacity expansion speeds
for VRE and electrolysis, which require an unprecedented ramp-up to
achieve the results presented here already if biomass is not restricted.



[Nature Energy | Volume 10 | February](http://www.nature.com/natureenergy) 2025 | 226–242 **234**


**Article** https://doi.org/10.1038/s41560-024-01693-6



**Methods**
**PyPSA-Eur-Sec model**
PyPSA-Eur-Sec [60][,][119] is an open-source, sector-coupled full European
energy system optimization (linear programming) model including
the power sector, transport (including also international shipping and
aviation), space and water heating, industry and industrial feedstocks.
The model minimizes total system costs by co-optimizing capacity
expansion and operation of all energy generation and conversion and
of storage and transmission of electricity, hydrogen and gas. The model
is based on the Python software toolbox PyPSA (Python for Power
Systems Analysis) [120] . A comprehensive description of the model can be
found in Neumann et al. [121] . A version with an extended set of biomass
resource technology portfolio is used [11], with added details for carbon capture energy penalties and additional competition introduced
for industry heat supply and added domestic biomass pellet boilers,
hydrogen CHPs, waste incineration and electrobiofuels (biofuels with
extra hydrogen added to the process to utilize more of the biomass
carbon directly).
A 37-node spatial resolution and a 5-h temporal resolution over a
full year in overnight greenfield scenarios was used, based on a trade-off
between the difference in results compared to with a 1-h resolution
(Supplementary Fig. 1) and computing time (Supplementary Fig. 2). A
country-level spatial resolution was chosen for computational reasons.
A lossy transport model for electricity transmission was used, which is
suitable at this resolution [122], and transmission is constrained to expand
to at most double the total line volume in 2022.

Final energy demands for the different sectors are calculated
based on the JRC IDEES database [123] with additions for non-EU countries
(refs. 10,60 provide further elaboration) and need to be met (that is,
demand is perfectly inelastic). However, energy carrier production
including electricity, hydrogen, methane and liquid fuels is determined
endogenously. Fossil fuels (coal, natural gas and oil) and uranium are
included, as are solid biomass imports as outlined below. Technology costs and efficiencies are elaborated on in the Supplementary
Information, with technology values for 2040 (given in € 2015 ) used
from the PyPSA energy system technology data set v0.6.0 (ref. 124).
The discount rate is uniform across countries and set to 7%, except
for rooftop solar PV and decentral space/water heating technologies,
for which it is set to 4%.


**Mathematical formulation.** The objective is to minimize the total
annual energy system costs of the energy system that comprises both
investment costs and operational expenditures of generation, storage, transmission and conversion infrastructure. To express both as
annual costs, we use the annuity factor (1 − (1 + _τ_ ) [−] _[n]_ ) / _τ_ that converts
the upfront overnight investment of an asset to annual payments considering its lifetime _n_ and cost of capital _τ_ . Thus, the objective includes
on one hand the annualized capital costs _c_ - for investments at bus _i_
in generator capacity _G_ _i_, _r_ ∈ _R_ [+] of technology _r_, storage energy capacity _E_ _i_, _s_ ∈ _R_ [+] of technology _s_, electricity transmission line capacities
_P_ _ℓ_ ∈ _R_ [+] and energy conversion and transport capacities _F_ _k_ ∈ _R_ [+] (links)
and the variable operating costs _o_ - for generator dispatch _g_ _i_, _r_, _t_ ∈ _R_ [+] and
link dispatch _f_ _k_, _t_ ∈ _R_ [+] on the other:

_G_, min _E_, _P_, _F_, _g_ [[∑] _i_, _r_ _c_ _i_, _r_ × _G_ _i_, _r_ + ∑ _i_, _s_ _c_ _i_, _s_ × _E_ _i_, _s_ + ∑ ℓ _c_ ℓ × _P_ ℓ + ∑ _k_ _c_ _k_ × _F_ _k_ (1)



+ ∑

_t_



_w_ _t_
× (∑ _i_, _r_



_o_ _i_, _r_ × _g_ _i_, _r_, _t_ + ∑ _k_



_o_ _k_ × _f_ _k_, _t_
)]



Thereby, the representative time snapshots _t_ are weighted by
the time span w _t_ such that their total duration adds up to one year;
∑ _t_ ∈ _T_ _w_ _t_ = 365 × 24 h = 8,760 h. A bus _i_ represents both a regional scope
and an energy carrier. In addition to the cost-minimizing objective
function, as exhaustively described in ref. 125, we further impose a set
of linear constraints that define limits on the capacities of generation,



storage, conversion and transmission infrastructure from geographical and technical potentials and the availability of variable renewable
energy sources for each location and point in time. Further, the limit
for CO 2 emissions or transmission expansion is defined, along with
storage consistency equations, and a multi-period linearized optimal
power flow formulation. Overall, this results in a large linear problem.
The modelled system represents a long-term equilibrium where
the zero-profit rule applies and the revenue that each generator
receives from the market exactly covers their costs [126][,][127] . By way of annualization of capital costs (assuming that the modelled year represents
an average revenue year for each asset over their economical lifetime)
and weighting of asset operation to the interannual temporal resolution, there is therefore full cost recovery of all assets built. Prices form
endogenously in the model based on renewable supply conditions,
storage and demand flexibility. Regional electricity price time series
are retrieved from the dual value of the energy balance equations for
each region and hour. CO 2 emissions are considered in the model,
whereas other GHG emissions are not, and a CO 2 price is calculated as
the shadow price of the least-cost objective function for achieving the
set emissions target.


**Near-optimal analysis**
The Modelling to Generate Alternatives (MGA) method [70][,][72][–][74][,][128] was
implemented for the sector-coupled model. With this method, first
a cost-optimal result for achieving emissions targets is calculated,
which gives a minimum system cost _C_ . For notational brevity, let _c_ _[T]_ _x_
denote the linear objective function equation (1) and _Ax_ ≤ _b_ the set of
additional linear constraints in a space of continuous variables, such
that the minimized system cost can be represented by


_C_ = min _x_ { _c_ _[T]_ _x_ | _Ax_ ≤ _b_ } . (2)


In the next step, this cost is increased by _ε_ ∈ {0.01, 0.02, . . .} and
set as a constraint, while minimizing or maximizing a set of variables,
such as energy carriers or technologies, for example, total biomass
usage, total biofuel production or total wind power generation, with


_x_ _s_ = min _x_ _s_ {1 _[T]_ _x_ _s_ | _Ax_ ≤ _b_, _c_ _[T]_ _x_ ≤( _i_ + ε) × _C_ } (3)


_x_ _s_ = max _x_ _s_ {1 _[T]_ _x_ _s_ | _Ax_ ≤ _b_, _c_ _[T]_ _x_ ≤( _i_ + ε) × _C_ } (4)


By exploring the extremes, the Pareto frontiers for a given parameter–cost combination are mapped out. The system cost of excluding
a particular technology or resource was validated in runs where the
option in question was excluded. To obtain shadow prices related to
system cost for Fig. 5, biomass use was set as an additional constraint
in cost-minimizing runs.
The model runs were performed on the Chalmers Centre for Computational Science and Engineering (C3SE) computing cluster.


**Biomass and bioenergy**
A variety of biomass categories and conversion technologies are introduced in the model. Different biomass residue types are clustered into
the categories solid biomass and digestible biomass (Extended Data
Fig. 4). Solid biomass can be used for a variety of applications in heat,
power and fuel production and can optionally be combined with carbon
capture (Extended Data Fig. 1). Digestible biomass can be used for biogas
production via anaerobic digestion, which is upgraded to pure biomethane with the option to capture the waste CO 2 stream. Methane can also be
produced via gasification of solid biomass (BioSNG), or supplied by fossil
methane. These routes result in the same end product, methane (CH 4 ).
Medium domestic (country-level) biomass residue potentials for
2050 from the JRC ENSPRESO database were used [83] nodally explicitly,
with a weighted average of country-level biomass costs including



[Nature Energy | Volume 10 | February](http://www.nature.com/natureenergy) 2025 | 226–242 **235**


**Article** https://doi.org/10.1038/s41560-024-01693-6



harvesting, collection and transport from the reference biomass scenario [129] . Additionally, more expensive solid biomass imports can be
used [11] (Extended Data Fig. 3 and Extended Data Table 2). Pre-treatment
of biomass, where needed, is considered implicitly through moderate
conversion efficiencies and spare waste heat that could be utilized.
Small-scale heating includes a pelletization cost of €9 MWh [−1] biomass.
Reflecting current EU policy direction, bioenergy feedstock is assumed
to consist of residues, for which the likelihood of direct and indirect
land-use change and land carbon changes is smaller than for dedicated
feedstock production. The use of residues and waste as bioenergy
feedstock is assumed not to influence the land carbon stock; that is,
the global _net_ flow of CO 2 between the atmosphere and the biosphere,
which is driven by photosynthesis, respiration, decay and combustion
of organic matter, is assumed to not be affected. In a renewable energy
system, processing, conversion and transport do not cause fossil carbon emissions, and residual GHG emissions associated with land use
can be considered to be offset through CDR if exporting countries
adhere to net-zero or net-negative targets.
For biomass imports, as described in ref. 11, we assume that 175 EJ
per year of biomass can be supplied globally at a price of US$15 GJ [−1],
using the average of an IAM comparison [54] . Using regional data on
biomass use per capita and population estimates [130], we assume that up
to 20 EJ biomass (subtracting domestic potentials) may be imported
to Europe at a price of €15 GJ [−1] (€54 MWh [−1] ). For each additional EJ to
be imported, the price is assumed to increase by €0.25 GJ [−1], based
on the slope of the low-cost scenarios (Extended Data Fig. 3). The
assumed import prices are substantially higher than 2020 wood chip
prices at €20–25 MWh [−1] (ref. 89) (Extended Data Fig. 3). This reflects an
increased demand for biomass in scenarios complying with stringent
GHG emission targets. We test the effect of this assumption on results
in a sensitivity analysis (Fig. 7). Direct biofuel imports (and hydrogen
derivatives) from outside Europe are not considered.


**Carbon balances of biomass**

Solid biomass carbon dioxide uptake from atmosphere, with a carbon
share %C sb = 50%, specific energy e sb = 18 GJ t [−1] and molality
m CO 2 /m _C_ = 44/12 (equation (5)):



ε at [sb] [= −%] _[C]_ [sb] [ ×] [3] _e_ sb [.][6]



×



m CO 2 (5)

m C



Liquid fuel carbon dioxide emissions (t CO 2 MWh [−1] ) at full combustion for diesel and methane based on -CH 2 - simplification and specific
energy _e_ CH 2 = 44 GJ t [−1] LHV for diesel and _e_ CH 4 = 50 GJ t [−1] LHV for methane
(equation (6)):



ε fu = [3][.][6]

_e_ CH _x_



×



m CO 2 (6)
m CH _x_



The carbon efficiency _η_ c of the conversion is estimated by
equation (7).


_η_ c = _η_ × ε [ε] sb [fu] (7)


The rest is assumed to end up as CO 2, of which a part _ε_ s is separated
and possibly captured with an efficiency _η_ ε, with the remainder _ε_ v being
vented as CO 2 to the atmosphere in the exhaust gas.
The biogas produced from digestible biomass is assumed to contain
60 vol% CH 4 (specific energy _e_ = 50 GJ t [−1], density _ρ_ = 0.657 kg m _n_ −3 ) and
40 vol% CO 2 ( _ρ_ = 1.98 kg m _n_ −3 ), which calculates to 0.0868 t CO 2 MWh CH−1 4 [. ]
The feedstock input potentials and costs for biogas are given for
MWh MWh CH 4 and thus MWh in = MWh out for the carbon balance calculations. Thereby the C content in the slush can be omitted, thus avoiding
system boundary issues with the agricultural sector.



**Carbon capture**
Process emissions are assumed to be captured post-combustion
through solvents, which is the standard method with highest technological readiness level in 2021 [131] . For carbon capture in biomass
applications, part of the biomass input is used to meet the additional
heat demand for regenerating the solvents used for CO 2 capture. Heat is
assumed to be met by a steam boiler of the type suitable for the process
(gas for biogas, otherwise solid biomass), with capital and operational
costs added accordingly. Capture rates of 95% are assumed for these
processes except for biogas, where 90% is assumed. For biofuel production, acid gas removal (including CO 2, amounting to 71% of the carbon
in the biomass feedstock; Supplementary Table 1) from the syngas
is assumed to be performed with the Rectisol [132] (methanol-based)
process, with a 90% capture rate [133][–][135] and electricity demand to cover
for this is assumed to be included in the base process. The effect of
capture rates (which can be both higher and lower than assumed here)
is assessed in a sensitivity analysis (Extended Data Fig. 5). As a result of
energy penalties of the BECC processes, the efficiency of the conversion
to the main product is decreased, and the capital cost is increased to
cover for the additional heat demand and carbon capture infrastructure, as summarized in Extended Data Table 1. In contrast, heat demand
for DAC can be met by several competing process steam options as
described below.

A scaling factor _α_ for the additional biomass needed to supply
the steam heat demand of carbon capture is calculated as a function
(equation (8)) of the amount of CO 2 in the output stream _ε_ _s_ (Supplementary Table 2), the required heat input for carbon capture _e_ th,cc
(here assumed as 0.66 MWh t [−1] CO 2 at 100 °C (ref. 136)) and the boiler
efficiency _η_ th (Supplementary Table 2).


1
_α_ = (8)
1 + ε _s_ × _e_ th,cc / _η_ th


The steam efficiency _η_ steam (equation (9)) and main product efficiency _η_ new (equation (10)) are derived as a function of the scaling
factor _α_ .


_η_ steam = (1 − _α_ ) × _η_ th (9)


_η_ new = _α_ × _η_ old (10)


The new investment cost _C_ I,new is derived as a function (equation (11))
of the investment cost of the base plant configuration _C_ I,old scaled
by the updated efficiency _η_ old / _η_ new, with added investment costs for
the steam boiler _C_ I,th scaled by the steam produced _η_ steam / _η_ new and
additional investment costs for the carbon capture unit _C_ I,cc (here
assumed as €2,400 kg [−1] CO 2 h [−1] (ref. 136)) scaled by the process CO 2

output stream _ε_ s .


_C_ I,new = _C_ I,old × _η_ old / _η_ new + _C_ I,th × _η_ steam / _η_ new + _C_ I,cc × ε s (11)


For CHP units, the heat demand is scaled as the main product,
with added district heat output from the carbon capture process (here
assumed as 0.79 MWh th t [−1] CO 2 at district heat temperature [136] ).
These calculations result in the costs and efficiencies for the processes with carbon capture shown in Extended Data Table 1.


**Sector-specific assumptions**
Steel production is assumed to be fully performed with hydrogen as a
reduction agent (direct reduced iron, DRI) and electric arc furnaces,
and the share of scrap steel increases from 40% in 2023 to 70% in the
target year. Cement production entails unavoidable emissions from
calcination, which can be captured.
District heating is assumed to cover 30% of urban demand, whereas
space heating demand is assumed to decrease by 29% due to efficiency



[Nature Energy | Volume 10 | February](http://www.nature.com/natureenergy) 2025 | 226–242 **236**


**Article** https://doi.org/10.1038/s41560-024-01693-6



(∑ _j_, _i_



_C_ [el] ∑ _i_, _t_ _δ_ _i_ [el], _k_
_j_, _i_ _j_, _i_ ∑ _i_ _t_ _π_ [el]



∑ _i_, _t_ _π_ _i_ [el]




[el]

_i_, _k_, _t_ _[p]_ _i_ [ el], _t_


[el]

_i_, _j_, _t_ _[p]_ [ el] _i_, _t_



gains through new buildings and building renovation. District heat can
be fulfilled by numerous options, including excess heat from electrolysers and fuel production (of which 50% is assumed possible to use
considering that processes are not always near district heat grids) and
mandatory incineration of municipal solid waste.
Industrial heat is divided into three segments: low (process steam,
<200 °C), medium (~200–500 °C) and high temperature (>500 °C). In
the low- and medium-temperature segments, biomass is an option,
whereas methane and hydrogen are an option in all three. Direct electrification is an option in the low-temperature (process steam) segment,
whereas heat pumps for process steam are not considered. Thus, solid
biomass competes for producing industrial process steam with electric,
hydrogen and methane boilers and for producing medium-temperature
process heat with hydrogen and methane.
Base electricity demand for households and industry is the same
as in 2011 (except for a subtraction of electricity used for heating, the
supply of which is endogenously determined), with a temporal variation as depicted in Fig. 3.
It is assumed that a strong increase in the use of electric vehicles
reduces liquid fuel demand in land transport to zero, hence reducing
the need for biomass and/or electricity for meeting renewable fuel
targets (land transport demand overall is assumed to increase by 20%).
A liquid fuel demand is however retained in aviation (total fuel demand
increases by 70% compared to 2011), shipping (+50% compared to 2011,
with half of the fuel demand supplied by hydrogen) and in the chemical
industry (same demand as 2011), which can be supplied through solid
biomass-based liquid fuels (biofuels), electrofuels, electrobiofuels and
fossil fuels. Transport and chemical demand is assumed as for the year
2060 in Millinger et al. [11], and recycling of plastics is not considered.
For a sensitivity analysis with less sector coupling, the following
options were turned off completely: battery electric vehicle (BEV)
charging demand-side management; vehicle to grid; thermal energy
storage; waste heat usage from biofuels, electrolysis, DAC and BioSNG;
H 2 networks; H 2 underground storage in salt caverns. Further, no expansion of the electricity transmission or district heat grids from 2022
levels was allowed.


**Cost estimations**

Costs of energy provision to different applications are estimated as
follows and used for comparing cost increases to sectoral costs. The
cost of, for instance, fuels is estimated by allocating the cost of feedstocks used by the share of total feedstocks used for fuel production
and adding investments and operational costs. For processes with
several outputs, the Carnot method is used for allocating costs to
different products [34] .
Assuming 60 °C for heat output and 20 °C for the sink, a Carnot
factor for heat is derived by equation (12) [34] :



_i_ _C_ _i_ _[F]_ [e] + ∑ _i_ ∑, _j_ ∈ _F_ e, _t_ _δ_ [H] [2] _i_ [H], _j_ [2], _t_



+ ∑ _C_ _j_ ∈H 2 (14)

_j_ )



_C_ tot _[F]_ [e] [= ∑]



∑ _i_, _t_ _π_ _i_ [H] _t_ [2]



_i_, _t_

[ el] _i_, _t_



_i_, _t_



The cost of solid biomass _b_ s used to produce biofuels is assigned
to the biofuels by the amount of solid biomass used for biofuels _δ_ _j_ _[b]_ ∈ [s] _F_ b, _i_, _t_ [, ]

with is added to the capital cost of biomass to liquid _j_ ∈ _F_ b divided by the total amount of solid biomass used _C_ _i_ _[F]_ [b] [ (equation (][15] _π_ _i_ _[b]_, [)). The ] _t_ [s] [. This ]
cost is allocated to fuels and heat by equation (13).



_i_ _C_ _i_ _[F]_ [b] + ∑ _i_, _j_ ∈ _F_ b, _t_ _δ_ _[b]_ [s] _i_ _[b]_, _j_ [s], _t_



∑ _i_, _t_ _C_ _i_ _[b]_, _t_ [s] [)] (15)



_C_ biofuel = (1 − _a_ th


_i_

) (∑



∑ _i_, _t_ _π_ _i_ _[b]_ _t_ [s]



_i_, _t_



Costs for industry heat are straightforward, as there is only one
product, and they are calculated as above.


**Technology growth rates**
Historical technology growth rates are used to ex-post assess feasibility
of future growth expectations and model results [137], but not to restrict
model results. Technology growth typically follows an S curve and
is often estimated by a Gompertz curve. The maximum growth rate
G gmp at the inflection point of the S curve serves as an indicator for
comparison to historically observed growth rates and is calculated
by equation (16) [90] .


_G_ gmp = _[Lk]_ _e_ (16)


where _L_ is the asymptote (set to the obtained cost-optimal result
for individual technologies), _k_ is the growth constant and _e_ is Euler’s
number.

Δ _t_ is the time (in years) it takes to grow from 10 to 90% of the
asymptote and can be estimated by equation (17) [90] .



ln
(
Δ _t_ gmp =



ln(0.1)

ln(0.9) [)]



. (17)

_k_



ln(0.1)



_η_ c = 1 − _T_ _[T]_ H [L]



= 1 − 273 [273] + [ +] 60 [ 20] [≈] [0][.][12] (12)



Setting the growth constant to _k_ = 0.09 gives Δ _t_ gmp = 34 years
(equation (17)), that is, if starting at 10% in 2023, 90% of the asymptote is achieved in year 2057. This setting is used for estimation here.
_G_ gmp is normalized to the electricity demand at the inflection
point, located at 37% of the asymptote _L_ . Demand in the base year
_δ_ 0 = 3,448 TWh (ref. 84) and demand _δ_ T in the target year is set to the
resulting electricity generation of the respective scenario.


**Limitations**

A limitation compared to IAM studies is the lack of an explicit representation of agriculture and forestry, and the lack of a global trade
model (including economy dynamics and equilibrium modelling). An
expansion of the system boundaries to encompass the land-use system
would more accurately capture emissions flows related to biomass and
capture the competition between BECCUS and land-based CDR measures such as Afforestation/reforestation [23] . Afforestation/reforestation
has a substantial CDR potential [24], but there are uncertainties regarding
permanency and additionality compared to geological sequestration [23] .
Combined with other CDR measures such as enhanced weathering,
biochar and direct ocean capture [24], the necessity of achieving negative emissions in the energy sector and thus the role of biomass may
be reduced.

A chemical demand is included, but demand may deviate from the
assumed levels, and other uses such as construction and biochar may
also compete for biomass residues (which stem from forestry, from
which the main product is mainly used for construction), which is not
considered in this work.



The allocation factor a th is derived by considering conversion
efficiencies for all products (equation (13)): heat _η_ th, electricity _η_ el, fuels
_η_ fu and multiplying them with their Carnot factors. Carnot factors for
electricity, hydrogen and fuels are set to one.


_η_ c _η_ th
_a_ th = (13)
_η_ el + _η_ fu + _η_ c _η_ th


The share of H 2 used for electrofuel production is calculated by
dividing the electrofuel H 2 demand _[δ]_ [ H] _i_, _j_ [2], _t_ [ for all electrofuel technologies ]

_j_ electrolyser capital costs and H ∈ _F_ e, by the total H 2 production _π_ _i_ [H], 2 _t_ [2] pipeline costs [. The cost of H] [2] [ (including electricity, ] _C_ _j_ ∈H 2 ) is assigned to
electrofuels by the share of H 2 used for electrofuel production. The
total cost is calculated as in equation (14).



[Nature Energy | Volume 10 | February](http://www.nature.com/natureenergy) 2025 | 226–242 **237**


**Article** https://doi.org/10.1038/s41560-024-01693-6



A spatially explicit representation of geological carbon sequestration locations and of infrastructure for CO 2 transport was not considered and should be pursued in further work. Similarly, whereas a
constant cost for biomass transport to where it is used is contained in
the biomass residue cost, the specific transport distance required is
not assessed. Whereas the overall results are not notably affected by
assumptions on biomass residue and carbon handling costs, the practical spatial implementation probably is. Further work should assess
spatial aspects of carbon and biomass management [138][,][139] and connected
logistics challenges [140] . For computational reasons, a spatially more
explicit assessment could not be performed with the diverse biomass
technology portfolio and competitions across all sectors we consider
in this study, and for the same reason, a weighted average of domestic
biomass residue costs was used.

Leakage of methane and hydrogen is not considered and presents
a risk when relying on gaseous fuels [141] . However, digestible biomass
combustion may avoid methane emissions, which would otherwise
occur, which is valuable in its own right but not considered within the
system boundaries given here.
In runs with higher carbon sequestration, more fossil fuels are
used, which affects emissions of logistics of, for instance, biomass,
which is not considered as it would render the optimization problem non-convex and substantially more computationally intensive.
Upstream emissions of fossil fuels are also not considered, which would
affect results at higher carbon sequestration capacities.
Land transport was exogenously assumed to be fully electrified,
whereas electric or hydrogen-fuelled aviation was not considered as a
conservative assumption based on expected long lead times delaying
a substantial market penetration. Assumptions on fuel demand levels
in these sectors influence results by affecting primary energy demand
and costs more than any other part of the energy system [11] .
For the weather data we use historical data from 2013, which is
regarded as a characteristic year for wind and solar resources [121] . Interannual weather variability has an impact on variation management and
firm generation but is not assessed here. Whereas firm dispatchable
capacities can cover for almost the whole inflexible demand already
in the results given here, both more and less biomass would be used
for that purpose in more extreme years, and the assessed case may be
seen to represent an average biomass usage case. The model was run
with perfect foresight of weather conditions and demands, whereas
in reality, there is substantial uncertainty in capacity planning and
adequacy requirements. This is especially important for combined
capacity and dispatch optimization as performed here and affects
results on firm generation requirements. However, substantial firm
capacity is deployed in the results but very seldom run. Given the low
biomass amounts, even a doubling would not affect results substantially. Also, whereas flexibility is assumed in electrolysers, BEVs and so
on, there is a part of the electricity demand that is assumed not to be
flexible, where some flexibility (demand elasticity) could be assumed
and which would reduce the need for firm generation. Future work
should assess these aspects and demand variations (flexibility and
absolute amounts) further.
A greenfield assessment was performed, which does not take existing infrastructure into account, aside from existing power transmission
lines and hydropower installations. Emphasis in this work is to assess
the diversity of system compositions of an energy system adhering to
stringent emissions targets, not on the transition leading there. The
timing of when net-zero or net-negative targets in the energy system are
achieved is uncertain, and the results here are not tied to a specific year.
If targets are achieved by 2050 or later, most current capacity would
have reached the end of their lifespan. In the event that targets would
be achieved as early as 2040, some energy infrastructure existing in
2024 is likely to remain, but these capacities amount to a small fraction
compared to the capacity expansion seen in the results. Nuclear capacity that began operation after 1990, or is currently under construction,



amounts to 29 GW (ref. 142), able to produce 2% of the total electricity
generation in the least-cost net-negative scenario. For dispatchable gas
generators, 242 GW el capacity was in place in the region in 2022 [143], or
46% of the gas power capacity obtained in the least-cost net-negative
scenario. Thus, accounting for any remaining gas power capacity in
the target year would not affect results. Therefore, we deem this to be
a mild limitation to the modelling.
We acknowledge the limitation of this study in focusing solely on
the solution space concerning the composition of a system adhering
to very stringent emissions targets, rather than analysing the transition
towards achieving these targets over time. Conducting a detailed analysis of the energy transition over the years would require a reduction in
spatial and temporal resolution to ensure computational feasibility,
leading to the loss of detail on other valuable aspects of the analysis.
Nevertheless, we recognize the importance of further research to
analyse the transition dynamics in achieving emissions targets over
time, considering both short-term variability and long-term capacity
planning.


**Data availability**

                                                  [The technology data can be accessed via GitHub at github.com/mill](https://github.com/millingermarkus/technology-data/tree/biopower)
[ingermarkus/technology-data/tree/biopower and are archived via](https://github.com/millingermarkus/technology-data/tree/biopower)
[Zenodo at https://doi.org/10.5281/zenodo.8099703 (ref. 144). Result-](https://doi.org/10.5281/zenodo.8099703)
[ing files and code to generate figures are archived via Zenodo at https://](https://doi.org/10.5281/zenodo.14169801)
[doi.org/10.5281/zenodo.14169801 (ref. 145).](https://doi.org/10.5281/zenodo.14169801)


**Code availability**
[The model code can be accessed via GitHub at github.com/millinger-](https://github.com/millingermarkus/pypsa-eur-sec/tree/mga)
[markus/pypsa-eur-sec/tree/mga and is archived via Zenodo at https://](https://github.com/millingermarkus/pypsa-eur-sec/tree/mga)
[doi.org/10.5281/zenodo.8099690 (ref. 119).](https://doi.org/10.5281/zenodo.8099690)


**References**

1. Hansen, K., Mathiesen, B. V. & Skov, I. R. Full energy system
transition towards 100% renewable energy in Germany in 2050.
_Renewable Sustain. Energy Rev._ **102**, 1–13 (2019).
2. Patrizio, P., Fajardy, M., Bui, M. & Dowell, N. M. CO 2 mitigation
or removal: the optimal uses of biomass in energy system
decarbonization. _iScience_ **24**, 102765 (2021).
3. Lauer, M. et al. The crucial role of bioenergy in a climate-neutral
energy system in Germany. _Chem. Eng. Technol._ **46**, 501–510
(2023).
4. Johansson, V., Lehtveer, M. & Göransson, L. Biomass in the
electricity system: a complement to variable renewables or a
source of negative emissions? _Energy_ **168**, 532–541 (2019).
5. Ruhnau, O. & Qvist, S. Storage requirements in a 100% renewable
electricity system: extreme events and inter-annual variability.
_Environ. Res. Lett._ [https://doi.org/10.1088/1748-9326/ac4dc8](https://doi.org/10.1088/1748-9326/ac4dc8)
(2022).
6. Lehtveer, M. & Fridahl, M. Managing variable renewables with
biomass in the European electricity system: emission targets and
investment preferences. _Energy_ **213**, 118786 (2020).
7. Schipfer, F. et al. Status of and expectations for flexible bioenergy
to support resource efficiency and to accelerate the energy
transition. _Renewable Sustain. Energy Rev._ [https://doi.org/](https://doi.org/10.1016/j.rser.2022.112094)
[10.1016/j.rser.2022.112094 (2022).](https://doi.org/10.1016/j.rser.2022.112094)
8. Sepulveda, N. A., Jenkins, J. D., de Sisternes, F. J. & Lester, R. K.
The role of firm low-carbon electricity resources in deep
decarbonization of power generation. _Joule_ **2**, 2403–2420 (2018).
9. Sepulveda, N. A., Jenkins, J. D., Edington, A., Mallapragada, D. S. &
Lester, R. K. The design space for long-duration energy storage in
decarbonized power systems. _Nat. Energy_ [https://doi.org/10.1038/](https://doi.org/10.1038/s41560-021-00796-8)
[s41560-021-00796-8 (2021).](https://doi.org/10.1038/s41560-021-00796-8)
10. Zeyen, E., Hagenmeyer, V. & Brown, T. Mitigating heat demand
peaks in buildings in a highly renewable European energy system.
_Energy_ **231**, 120784 (2021).



[Nature Energy | Volume 10 | February](http://www.nature.com/natureenergy) 2025 | 226–242 **238**


**Article** https://doi.org/10.1038/s41560-024-01693-6



11. Millinger, M. et al. Are biofuel mandates cost-effective? An
analysis of transport fuels and biomass usage to achieve
emissions targets in the European energy system. _Appl. Energy_
**326**, 120016 (2022).
12. Korberg, A. D., Mathiesen, B. V., Clausen, L. R. & Skov, I. R. The
role of biomass gasification in low-carbon energy and transport
systems. _Smart Energy_ **1**, 100006 (2021).
13. Millinger, M., Meisel, K. & Thrän, D. Greenhouse gas abatement
optimal deployment of biofuels from crops in Germany.
_Transp. Res. Part D Transp. Environ._ [https://doi.org/10.1016/j.](https://doi.org/10.1016/j.trd.2019.02.005)
[trd.2019.02.005 (2019).](https://doi.org/10.1016/j.trd.2019.02.005)
14. Codina Gironès, V., Moret, S., Peduzzi, E., Nasato, M. & Maréchal, F.
Optimal use of biomass in large-scale energy systems: insights for
energy policy. _Energy_ **137**, 789–797 (2017).
15. Stegmann, P., Daioglou, V., Londo, M., van, D. P. & Junginger, V. M.
Plastic futures and their CO 2 emissions. _Nature_ **612**, 272–276
(2022).
16. Meys, R. et al. Achieving net-zero greenhouse gas emission
plastics by a circular carbon economy. _Science_ **374**, 71–76 (2021).
17. Zheng, J. & Suh, S. Strategies to reduce the global carbon
footprint of plastics. _Nat. Clim. Change_ **9**, 374–378 (2019).
18. Tanzer, S. E., Blok, K. & Ramírez, A. Can bioenergy with carbon
capture and storage result in carbon negative steel? _Int. J._
_Greenhouse Gas Control_ **100**, 103104 (2020).
19. Mandova, H. et al. Achieving carbon-neutral iron and steelmaking
in Europe through the deployment of bioenergy with carbon
capture and storage. _J. Cleaner Prod._ **218**, 118–129 (2019).
20. Sabatino, F. et al. A comparative energy and costs assessment
and optimization for direct air capture technologies. _Joule_ **5**,
2047–2076 (2021).
21. _Communication from the Commission to the European Parliament,_
_the European Council, the Council, the European Economic_
_and Social Committee and the Committee of the Regions—The_
_European Green Deal_ (European Commission, 2019).
22. _Net Zero Strategy: Build Back Greener. Presented to Parliament_
_pursuant to Section 14 of the Climate Change Act 2008_
[(UK Government, 2021); https://www.gov.uk/government/](https://www.gov.uk/government/publications/net-zero-strategy)
[publications/net-zero-strategy](https://www.gov.uk/government/publications/net-zero-strategy)
23. Babiker, M. et al. in _Climate Change 2022: Mitigation of Climate_
_Change_ (eds Shukla, P.R. et al.) 295–408 (Cambridge Univ. Press,
2022).
24. Fuhrman, J. et al. Diverse carbon dioxide removal approaches
could reduce impacts on the energy–water–land system.
_Nat. Clim. Change_ **13**, 341–350 (2023).
25. Rogelj, J. et al. Energy system transformations for limiting
end-of-century warming to below 1.5 °C. _Nat. Clim. Change_ **5**,
519–527 (2015).
26. Creutzig, F. et al. Bioenergy and climate change mitigation: an
assessment. _GCB Bioenergy_ **7**, 916–944 (2015).
27. Creutzig, F. Economic and ecological views on climate change
mitigation with bioenergy and negative emissions. _GCB Bioenergy_
**8**, 4–10 (2016).
28. Gough, C. et al. Challenges to the use of BECCS as a keystone
technology in pursuit of 1.5 °C. _Glob. Sustain._ **1**, e5 (2018).
29. Smith, P. et al. Biophysical and economic limits to negative CO 2
emissions. _Nat. Clim. Change_ **6**, 42–50 (2016).
30. Heck, V., Gerten, D., Lucht, W. & Popp, A. Biomass-based negative
emissions difficult to reconcile with planetary boundaries.
_Nat. Clim. Change_ **8**, 151–155 (2018).
31. Calvin, K. et al. Bioenergy for climate change mitigation:
scale and sustainability. _GCB Bioenergy_ [https://doi.org/10.1111/](https://doi.org/10.1111/gcbb.12863)
[gcbb.12863 (2021).](https://doi.org/10.1111/gcbb.12863)
32. Fajardy, M. & Mac Dowell, N. Can BECCS deliver sustainable and
resource efficient negative emissions? _Energy Environ. Sci._ **10**,
1389–1426 (2017).



33. Donnison, C. et al. Bioenergy with carbon capture and storage
(BECCS): finding the win-wins for energy, negative emissions and
ecosystem services-size matters. _GCB Bioenergy_ **12**, 586–604
(2020).
34. European Parliament. Directive (EU) 2018/2001 of the European
Parliament and of the Council on the promotion of the use of energy
from renewable sources. _Off. J. Eur. Union_ **2018**, 82–209 (2018).
35. The European Parliament and the Council of the European Union.
Directive (EU) 2023/2413 of the European Parliament and of the
Council of 18 October 2023 amending Directive (EU) 2018/2001,
Regulation (EU) 2018/1999 and Directive 98/70/EC as regards
the promotion of energy from renewable sources, and repealing
Council. _Off. J. Eur. Union_ **2413**, 1–77 (2023).
36. Daioglou, V. et al. Bioenergy technologies in long-run climate
change mitigation: results from the EMF-33 study. _Climatic_
_Change_ **163**, 1603–1620 (2020).
37. Muratori, M. et al. EMF-33 insights on bioenergy with carbon
capture and storage (BECCS). _Climatic Change_ **163**, 1621–1637
(2020).
38. Riahi, K. et al. in _Climate Change 2022: Mitigation of Climate_
_Change_ (eds Shukla, P.R. et al.) 295–408 (Cambridge Univ. Press,
2022).
39. Azar, C. et al. The feasibility of low CO 2 concentration targets and
the role of bio-energy with carbon capture and storage (BECCS).
_Climatic Change_ **100**, 195–202 (2010).
40. Azar, C., Johansson, D. J. & Mattsson, N., Meeting global
temperature targets—the role of bioenergy with carbon capture
and storage. _Environ. Res. Lett._ [https://doi.org/10.1088/1748-9326/](https://doi.org/10.1088/1748-9326/8/3/034004)
[8/3/034004 (2013).](https://doi.org/10.1088/1748-9326/8/3/034004)
41. Klein, D. et al. The value of bioenergy in low stabilization
scenarios: an assessment using REMIND-MAgPIE. _Climatic_
_Change_ **123**, 705–718 (2014).
42. Strefler, J. et al. Between Scylla and Charybdis: Delayed
mitigation narrows the passage between large-scale CDR and
high costs. _Environ. Res. Lett._ [https://doi.org/10.1088/1748-9326/](https://doi.org/10.1088/1748-9326/aab2ba)
[aab2ba (2018).](https://doi.org/10.1088/1748-9326/aab2ba)
43. Van Vuuren, D. P. et al. Alternative pathways to the 1.5 °C target
reduce the need for negative emission technologies. _Nat. Clim._
_Change_ **8**, 391–397 (2018).
44. Rogelj, J. et al. A new scenario logic for the Paris Agreement
long-term temperature goal. _Nature_ **573**, 357–363 (2019).
45. Obersteiner, M. et al. How to spend a dwindling greenhouse gas
budget. _Nat. Climate Change_ **8**, 7–10 (2018).
46. Xiao, M., Junne, T., Haas, J. & Klein, M. Plummeting costs of
renewables—are energy scenarios lagging? _Energy Strategy Rev._
**35**, 100636 (2021).
47. Luderer, G. et al. Impact of declining renewable energy costs on
electrification in low-emission scenarios. _Nat. Energy_ **7**, 32–42
(2022).
48. Grant, N., Gambhir, A., Mittal, S., Greig, C. & Köberle, A. C.
Enhancing the realism of decarbonisation scenarios with
practicable regional constraints on CO 2 storage capacity.
_Int. J. Greenhouse Gas Control_ [https://doi.org/10.1016/j.](https://doi.org/10.1016/j.ijggc.2022.103766)
[ijggc.2022.103766 (2022).](https://doi.org/10.1016/j.ijggc.2022.103766)
49. Kazlou, T., Cherp, A. & Jewell, J. Feasible deployment of carbon
capture and storage and the requirements of climate targets.
_Nat. Clim. Change_ [https://doi.org/10.1038/s41558-024-02104-0](https://doi.org/10.1038/s41558-024-02104-0)
(2024).
50. Grant, N., Hawkes, A., Napp, T. & Gambhir, A. Cost reductions in
renewables can substantially erode the value of carbon capture
and storage in mitigation pathways. _One Earth_ **4**, 1588–1601
(2021).
51. Realmonte, G. et al. An inter-model assessment of the role of
direct air capture in deep mitigation pathways. _Nat. Commun._ **10**,
3277 (2019).



[Nature Energy | Volume 10 | February](http://www.nature.com/natureenergy) 2025 | 226–242 **239**


**Article** https://doi.org/10.1038/s41560-024-01693-6



52. Chen, C. & Tavoni, M. Direct air capture of CO 2 and climate
stabilization: a model-based assessment. _Climatic Change_ **118**,
59–72 (2013).
53. Muratori, M. et al. Carbon capture and storage across fuels
and sectors in energy system transformation pathways. _Int. J._
_Greenhouse Gas Control_ **57**, 34–41 (2017).
54. Bauer, N. et al. Global energy sector emission reductions and
bioenergy use: overview of the bioenergy demand phase of the
EMF-33 model comparison. _Climatic Change_ **163**, 1553–1568
(2020).
55. Leblanc, F. et al. The contribution of bioenergy to the
decarbonization of transport: a multi-model assessment. _Climatic_
_Change_ **170**, 21 (2022).
56. Humpenöder, F. et al. Investigating afforestation and bioenergy
CCS as climate change mitigation strategies. _Environ. Res. Lett._
[https://doi.org/10.1088/1748-9326/9/6/064029 (2014).](https://doi.org/10.1088/1748-9326/9/6/064029)
57. Ahlgren, E. O., Börjesson Hagberg, M. & Grahn, M. T. Transport
biofuels in global energy–economy modelling – a review of
comprehensive energy systems assessment approaches. _GCB_
_Bioenergy_ **9**, 1168–1180 (2017).
58. Azar, C., Lindgren, K. & Andersson, B. A. Global energy scenarios
meeting stringent CO 2 constraints—cost-effective fuel
choices in the transportation sector. _Energy Policy_ **31**, 961–976
(2003).
59. Grahn, M., Azar, C., Lindgren, K., Berndes, G. & Gielen, D. Biomass
for heat or as transportation fuel? A comparison between two
model-based studies. _Biomass Bioenergy_ **31**, 747–758 (2007).
60. Victoria, M., Zeyen, E. & Brown, T. Speed of technological
transformations required in Europe to achieve different climate
goals. _Joule_ **6**, 1066–1086 (2022).
61. Pickering, B., Lombardi, F. & Pfenninger, S. Diversity of options to
eliminate fossil fuels and reach carbon neutrality across the entire
European energy system. _Joule_ **6**, 1253–1276 (2022).
62. Wu, F., Muller, A. & Pfenninger, S. Strategic uses for ancillary
bioenergy in a carbon-neutral and fossil-free 2050 European
energy system. _Environ. Res. Lett._ **18**, 014019 (2023).
63. Brill, E. D. The use of optimization models in public-sector
planning. _Manage. Sci._ **25**, 413–422 (1979).
64. Lombardi, F., Pickering, B. & Pfenninger, S. What is redundant and
what is not? Computational trade-offs in modelling to generate
alternatives for energy infrastructure deployment. _Appl. Energy_
**339**, 121002 (2023).
65. Biehl, J. et al. Wicked facets of the German energy transition—
examples from the electricity, heating, transport, and industry
sectors. _Int. J. Sustain. Energy_ **42**, 1128–1181 (2023).
66. Palmer, J. Risk governance in an age of wicked problems: lessons
from the European approach to indirect land-use change. _J. Risk_
_Res._ **15**, 495–513 (2012).
67. Fast, S. & McCormick, K. Biofuels: from a win–win solution to a
wicked problem? _Biofuels_ **3**, 737–748 (2012).
68. Gamborg, C., Anker, H. T. & Sandøe, P. Ethical and legal
challenges in bioenergy governance: coping with value
disagreement and regulatory complexity. _Energy Policy_ **69**,
326–333 (2014).
69. Rittel, H. W. & Webber, M. M. Dilemmas in a general theory of
planning. _Found. Plann. Enterpr. Crit. Essays Plann. Theory_ **1**,
67–169 (1969).
70. Trutnevyte, E. Does cost optimization approximate the real-world
energy transition? _Energy_ **106**, 182–193 (2016).
71. DeCarolis, J. et al. Formalizing best practice for energy
system optimization modelling. _Appl. Energy_ **194**, 184–198
(2017).
72. Trutnevyte, E. EXPANSE methodology for evaluating the
economic potential of renewable energy from an energy mix
perspective. _Appl. Energy_ **111**, 593–601 (2013).



73. DeCarolis, J. F., Babaee, S., Li, B. & Kanungo, S. Modelling to
generate alternatives with an energy system optimization model.
_Environ. Modell. Softw._ **79**, 300–310 (2016).
74. Neumann, F. & Brown, T. The near-optimal feasible space of a
renewable power system model. _Electric Power Syst. Res._ **190**,
106690 (2021).
75. Pedersen, T. T., Victoria, M., Rasmussen, M. G. & Andresen, G. B.
Modeling all alternative solutions for highly renewable energy
systems. _Energy_ **234**, 121294 (2021).
76. Grochowicz, A., van Greevenbroek, K., Benth, F. E. & Zeyringer, M.
Intersecting near-optimal spaces: European power systems with
more resilience to weather variability. _Energy Econ._ **118**, 106496
(2022).
77. Price, J. & Keppo, I. Modelling to generate alternatives: a
technique to explore uncertainty in energy–environment–
economy models. _Appl. Energy_ **195**, 356–369 (2017).
78. Williams, J. H. et al. Carbon-neutral pathways for the United
States. _AGU Adv._ [https://doi.org/10.1029/2020AV000284 (2021).](https://doi.org/10.1029/2020AV000284)
79. European Union _Regulation of the European Parliament and of_
_the Council Establishing the Framework for Achieving Climate_
_Neutrality and Amending Regulations (EC) No 401/2009 and (EU)_
_2018/1999_ (European Climate Law, 2021).
80. McLaren, D. Quantifying the potential scale of mitigation
deterrence from greenhouse gas removal techniques. _Climatic_
_Change_ **162**, 2411–2428 (2020).
81. Geden, O., Peters, G. P. & Scott, V. Targeting carbon dioxide
removal in the European Union. _Clim. Policy_ **19**, 487–494 (2019).
82. McLaren, D. P., Tyfield, D. P., Willis, R., Szerszynski, B. &
Markusson, N. O. Beyond ‘net-zero’: a case for separate targets for
emissions reduction and negative emissions. _Front. Clim._ **1**, 1–5
(2019).
83. _ENSPRESO Integrated Data_ (European Commission, Joint
[Research Centre, 2021); http://data.europa.eu/89h/88d1c405-](http://data.europa.eu/89h/88d1c405-0448-4c9e-b565-3c30c9b167f7)
[0448-4c9e-b565-3c30c9b167f7](http://data.europa.eu/89h/88d1c405-0448-4c9e-b565-3c30c9b167f7)

[84. Statistics—Eurostat Data Browser (Eurostat, 2023); https://](https://ec.europa.eu/eurostat/databrowser/)
[ec.europa.eu/eurostat/databrowser/](https://ec.europa.eu/eurostat/databrowser/)
85. _UK Energy In Brief_ (United Kingdom Department for Business,
[Energy & Industrial Strategy, 2022); https://www.gov.uk/](https://www.gov.uk/government/statistics/uk-energy-in-brief-2022)
[government/statistics/uk-energy-in-brief-2022](https://www.gov.uk/government/statistics/uk-energy-in-brief-2022)
86. _Government Expenditure on Defence—Statistics Explained_
[(Eurostat, 2023); https://ec.europa.eu/eurostat/statistics-](https://ec.europa.eu/eurostat/statistics-explained/index.php?title=Government_expenditure_on_defence)
[explained/index.php?title=Government_expenditure_on_defence](https://ec.europa.eu/eurostat/statistics-explained/index.php?title=Government_expenditure_on_defence)
87. _In-depth Analysis in Support of the Commission Communication_
_COM(2018) 773 - A Clean Planet for All A European Long-term_
_Strategic Vision for a Prosperous, Modern, Competitive and_
_Climate Neutral Economy_ (European Commission, 2018).
88. _Climate Watch, Historical GHG emissions_ (World Resources
[Institute, 2022); https://www.climatewatchdata.org](https://www.climatewatchdata.org)
89. _Biomass Markets—Weekly Biomass Market News and Analysis_ 23-1
(Argus, 2023).
90. Cherp, A., Vinichenko, V., Tosun, J., Gordon, J. A. & Jewell, J.
National growth dynamics of wind and solar power compared
to the growth required for global climate targets. _Nat. Energy_ **6**,
742–754 (2021).
91. Weinand, J. M. et al. Historic drivers of onshore wind power siting
and inevitable future trade-offs. _Environ. Res. Lett._ [https://doi.org/](https://doi.org/10.1088/1748-9326/ac7603)
[10.1088/1748-9326/ac7603 (2022).](https://doi.org/10.1088/1748-9326/ac7603)
92. McKenna, R. et al. Scenicness assessment of onshore
wind sites with geotagged photographs and impacts on
approval and cost-efficiency. _Nat. Energy_ **6**, 663–672
(2021).
93. McKenna, R. et al. High-resolution large-scale onshore
wind energy assessments: a review of potential definitions,
methodologies and future research needs. _Renewable Energy_
**182**, 659–684 (2022).



[Nature Energy | Volume 10 | February](http://www.nature.com/natureenergy) 2025 | 226–242 **240**


**Article** https://doi.org/10.1038/s41560-024-01693-6



94. Delafield, G. et al. Conceptual framework for balancing society
and nature in net-zero energy transitions. _Environ. Sci. Policy_ **125**,
189–201 (2021).
95. Odenweller, A., Ueckerdt, F., Nemet, G. F., Jensterle, M. &
Luderer, G. Probabilistic feasibility space of scaling up green
hydrogen supply. _Nat. Energy_ [https://doi.org/10.1038/s41560-022-](https://doi.org/10.1038/s41560-022-01097-4)
[01097-4 (2022).](https://doi.org/10.1038/s41560-022-01097-4)
96. Kätterer, T. & Bolinder, M. A. in _Understanding and Fostering Soil_
_Carbon Sequestration_ (ed. Rumpel, C.) 453–488 (Burleigh Dodds
Science Publishing, 2022).
97. Scarlat, N., Fahl, F., Lugato, E., Monforti-Ferrario, F. &
Dallemand, J. F. Integrated and spatially explicit assessment of
sustainable crop residues potential in Europe. _Biomass Bioenergy_
**122**, 257–269 (2019).
98. Mayer, M. et al. Influence of forest management activities on soil
organic carbon stocks: a knowledge synthesis. _For. Ecol. Manage._
**466**, 118127 (2020).
99. Mäkipää, R. et al. How does management affect soil C
sequestration and greenhouse gas fluxes in boreal and temperate
forests?—a review. _For. Ecol. Manage._ [https://doi.org/10.1016/](https://doi.org/10.1016/j.foreco.2022.120637)
[j.foreco.2022.120637 (2023).](https://doi.org/10.1016/j.foreco.2022.120637)
100. Clarke, N. et al. Effects of intensive biomass harvesting on forest
soils in the Nordic countries and the UK: a meta-analysis. _For. Ecol._
_Manage._ [https://doi.org/10.1016/j.foreco.2020.118877 (2021).](https://doi.org/10.1016/j.foreco.2020.118877)
101. Northrup, D. L., Basso, B., Wang, M. Q., Morgan, C. L. & Benfey, P. N.
Novel technologies for emission reduction complement
conservation agriculture to achieve negative emissions from
row-crop production. _Proc. Natl Acad. Sci. USA_ **118**, e2022666118
(2021).
102. Amundson, R. Negative emissions in agriculture are improbable
in the near future. _Proc. Natl Acad. Sci. USA_ **119**, e2118142119
(2022).
103. Northrup, D. L., Basso, B., Wang, M. Q., Morgan, C. L. &
Benfey, P. N. Reply to Amundson: time to go to work. _Proc. Natl_
_Acad. Sci. USA_ **119**, e2122842119 (2022).
104. Nabuurs, G.-J. et al. in _Climate Change 2022: Mitigation of Climate_
_Change_ (eds Shukla, P.R. et al.) 747–860 (Cambridge Univ. Press,
2022).
105. Meyfroidt, P. et al. Focus on leakage and spillovers: informing
land-use governance in a tele-coupled world. _Environ. Res. Lett._
[https://doi.org/10.1088/1748-9326/ab7397 (2020).](https://doi.org/10.1088/1748-9326/ab7397)
106. Yu, B., Zhao, Q. & Wei, Y. M. Review of carbon leakage under
regionally differentiated climate policies. _Sci. Total Environ._ **782**,
146765 (2021).
107. Williams, E. _The Economics of Direct Air Carbon Capture and_
_Storage_ (Global CCS Institute, 2022).
108. Young, J. et al. The cost of direct air capture and storage can be
reduced via strategic deployment but is unlikely to fall below
stated cost targets. _One Earth_ **6**, 899–917 (2023).
109. Terlouw, T., Treyer, K., Bauer, C. & Mazzotti, M. Life cycle
assessment of direct air carbon capture and storage with
low-carbon energy sources. _Environ. Sci. Technol._ **55**, 11397–11411
(2021).
110. Hanssen, S. V. et al. Biomass residues as twenty-first century
bioenergy feedstock-a comparison of eight integrated
assessment models. _Climatic Change_ **163**, 1569–1586 (2020).
111. Millinger, M. & Thrän, D. Biomass price developments inhibit
biofuel investments and research in Germany: the crucial future
role of high yields. _J. Cleaner Prod._ **172**, 1654–1663 (2018).
112. Daigneault, A. et al. How the future of the global forest sink
depends on timber demand, forest management, and carbon
policies. _Glob. Environ. Change_ **76**, 102582 (2022).
113. Favero, A., Daigneault, A. & Sohngen, B. Forests: carbon
sequestration, biomass energy, or both? _Sci. Adv._ **6**, eaay6792
(2020).



114. Cintas, O. et al. The climate effect of increased forest bioenergy
use in Sweden: evaluation at different spatial and temporal
scales. _Wiley Interdiscip. Rev. Energy Environ._ **5**, 351–369 (2016).
115. Merfort, L. et al. State of global land regulation inadequate to
control biofuel land-use-change emissions. _Nat. Clim. Change_ **13**,
610–612 (2023).
116. Englund, O. et al. Large-scale deployment of grass in crop
rotations as a multifunctional climate mitigation strategy.
_GCB Bioenergy_ **15**, 166–184 (2023).
117. Rosa, L., Sanchez, D. L. & Mazzotti, M. Assessment of carbon
dioxide removal potential: via BECCS in a carbon-neutral Europe.
_Energy Environ. Sci._ **14**, 3086–3097 (2021).
118. European Parliament. Amendments adopted by the European
Parliament on 14 September 2022 on the proposal for a directive
of the European Parliament and of the Council amending
Directive (EU) 2018/2001 of the European Parliament and of the
Council, Regulation (EU) 2018/1999 of. _Off. J. Eur. Union_ **125**,
398–461 (2023).
119. Brown, T. et al. millingermarkus/pypsa-eur-sec: PyPSA-EurSec-MGA-Bio. _Zenodo_ [https://doi.org/10.5281/zenodo.8099691](https://doi.org/10.5281/zenodo.8099691)
(2023).
120. Brown, T., Hörsch, J. & Schlachtberger, D. PyPSA: Python for
power system analysis. _J. Open Res. Softw._ [https://doi.org/](https://doi.org/10.5334/jors.188)
[10.5334/jors.188 (2018).](https://doi.org/10.5334/jors.188)
121. Neumann, F., Zeyen, E., Victoria, M. & Brown, T. The potential role
of a hydrogen network in Europe. _Joule_ **7**, 1793–1817 (2023).
122. Neumann, F., Hagenmeyer, V. & Brown, T. Assessments of linear
power flow and transmission loss approximations in coordinated
capacity expansion problems. _Appl. Energy_ **314**, 118859 (2022).
123. Mantzos, L. et al. _JRC-IDEES: Integrated Database of the European_
_Energy Sector: Methodological Note_ (Publications Office of the
European Union, 2017).
124. Victoria, M., Zhu, K., Zeyen, E. & Brown, T. Millingermarkus/
technology-data. _GitHub_ [https://github.com/millingermarkus/](https://github.com/millingermarkus/technology-data/tree/biopower)
[technology-data/tree/biopower (2023).](https://github.com/millingermarkus/technology-data/tree/biopower)
125. Neumann, F. & Brown, T. Broad ranges of investment
configurations for renewable power systems, robust to cost
uncertainty and near-optimality. _iScience_ **26**, 106702 (2023).
126. Boiteux, M. Peak-load pricing. _J. Bus._ **33**, 157–179 (1960).
127. Brown, T. & Reichenberg, L. Decreasing market value of variable
renewables can be avoided by policy action. _Energy Econ._ **100**,
105354 (2021).
128. DeCarolis, J. F. Using modeling to generate alternatives (MGA) to
expand our thinking on energy futures. _Energy Econ._ **33**, 145–152
(2011).
129. Ruiz, P. et al. ENSPRESO—an open, EU-28 wide, transparent and
coherent database of wind, solar and biomass energy potentials.
_Energy Strategy Rev._ **26**, 100379 (2019).
130. KC, S. & Lutz, W. The human core of the shared socioeconomic
pathways: population scenarios by age, sex and level of
education for all countries to 2100. _Glob. Environ. Change_ **42**,
181–192 (2017).
131. Kearns, D., Liu, H. & Consoli, C. _Technology Readiness and_
_Costs of CCS_ [(Global CCS Institute, 2021); https://www.](https://www.globalccsinstitute.com/wp-content/uploads/2021/03/Technology-Readiness-and-Costs-for-CCS-2021-1.pdf)
[globalccsinstitute.com/wp-content/uploads/2021/03/](https://www.globalccsinstitute.com/wp-content/uploads/2021/03/Technology-Readiness-and-Costs-for-CCS-2021-1.pdf)
[Technology-Readiness-and-Costs-for-CCS-2021-1.pdf](https://www.globalccsinstitute.com/wp-content/uploads/2021/03/Technology-Readiness-and-Costs-for-CCS-2021-1.pdf)
132. _RECTISOL Wash_ [(Linde Engineering, 2023); https://www.](https://www.linde-engineering.com/en/process-plants/hydrogen_and_synthesis_gas_plants/gas_processing/rectisol_wash/index.html)
[linde-engineering.com/en/process-plants/hydrogen_and_](https://www.linde-engineering.com/en/process-plants/hydrogen_and_synthesis_gas_plants/gas_processing/rectisol_wash/index.html)
[synthesis_gas_plants/gas_processing/rectisol_wash/index.html](https://www.linde-engineering.com/en/process-plants/hydrogen_and_synthesis_gas_plants/gas_processing/rectisol_wash/index.html)
133. Liu, X., Yang, S., Hu, Z. & Qian, Y. Simulation and assessment of
an integrated acid gas removal process with higher CO 2 capture
rate. _Comput. Chem. Eng._ **83**, 48–57 (2015).
134. Yang, S., Qian, Y. & Yang, S. Development of a full CO 2 capture
process based on the Rectisol wash technology. _Ind. Eng. Chem._
_Res._ **55**, 6186–6193 (2016).



[Nature Energy | Volume 10 | February](http://www.nature.com/natureenergy) 2025 | 226–242 **241**


**Article** https://doi.org/10.1038/s41560-024-01693-6



135. Hannula, I. Co-production of synthetic fuels and district heat from
biomass residues, carbon dioxide and electricity: performance
and cost analysis. _Biomass Bioenergy_ **74**, 26–46 (2015).
136. _Technology Data_ [(Danish Energy Agency, 2021); https://ens.dk/en/](https://ens.dk/en/our-services/projections-and-models/technology-data/)
[our-services/projections-and-models/technology-data/](https://ens.dk/en/our-services/projections-and-models/technology-data/)
137. Jewell, J. & Cherp, A. The feasibility of climate action: bridging the
inside and the outside view through feasibility spaces.
_WIREs Clim.Change_ **14**, e838 (2023).
138. Freer, M., Gough, C., Welfle, A. & Lea-Langton, A. Carbon optimal
bioenergy with carbon capture and storage supply chain
modelling: how far is too far? _Sustain. Energy Technol. Assess._ **47**,
101406 (2021).
139. Freer, M., Gough, C., Welfle, A. & Lea-Langton, A. Putting
bioenergy with carbon capture and storage in a spatial context:
what should go where? _Front. Clim._ **4**, 826982 (2022).
140. Liu, B. & Rajagopal, D. Life-cycle energy and climate benefits of
energy recovery from wastes and biomass residues in the United
States. _Nat. Energy_ **4**, 700–708 (2019).
141. Shirizadeh, B. et al. The impact of methane leakage on the role of
natural gas in the European energy transition. _Nat. Commun._ **14**,
5756 (2023).
142. _Global Nuclear Power Tracker_ (Global Energy Monitor,
[2024); https://globalenergymonitor.org/projects/](https://globalenergymonitor.org/projects/global-nuclear-power-tracker/)
[global-nuclear-power-tracker/](https://globalenergymonitor.org/projects/global-nuclear-power-tracker/)
143. _Electricity Data Explorer_ [(Ember, 2024); https://ember-climate.org/](https://ember-climate.org/data/data-tools/data-explorer/)
[data/data-tools/data-explorer/](https://ember-climate.org/data/data-tools/data-explorer/)
144. Zeyen, E. et al. millingermarkus/technology-data: technology
data v0.5.0+bio. _Zenodo_ [https://doi.org/10.5281/zenodo.10886177](https://doi.org/10.5281/zenodo.10886177)
(2024).
145. Millinger, M. Results data and plot scripts. _Zenodo_ [https://doi.org/](https://doi.org/10.5281/zenodo.14169801)
[10.5281/zenodo.14169801 (2024).](https://doi.org/10.5281/zenodo.14169801)
146. Avitabile, V. et al. _Biomass Production, Supply, Uses and Flows in_
_the European Union. First Results from an Integrated Assessment_
(Publications Office of the European Union, 2023).
147. Millinger, M., Meisel, K., Budzinski, M. & Thrän, D. Relative
greenhouse gas abatement cost competitiveness of biofuels in
Germany. _Energies_ **11**, 615 (2018).
148. Malhotra, A. & Schmidt, T. S. Accelerating low-carbon innovation.
_Joule_ **4**, 2259–2267 (2020).
149. _The Chemical Engineering Plant Cost Index_ (CEPCI, 2023);

[https://www.chemengonline.com/pci-home](https://www.chemengonline.com/pci-home)
150. Bogdanov, D. et al. Low-cost renewable electricity as the key
driver of the global energy transition towards sustainability.
_Energy_ **227**, 120467 (2021).
151. Fasihi, M., Efimova, O. & Breyer, C. Techno-economic assessment
of CO 2 direct air capture plants. _J. Cleaner Prod._ **224**, 957–980
(2019).
152. Fuhrman, J. et al. The role of direct air capture and negative
emissions technologies in the shared socioeconomic pathways
towards +1.5 °C and +2 °C futures. _Environ. Res. Lett._ [https://doi.org/](https://doi.org/10.1088/1748-9326/ac2db0)
[10.1088/1748-9326/ac2db0 (2021).](https://doi.org/10.1088/1748-9326/ac2db0)


**Acknowledgements**
We acknowledge funding from the Swedish Energy Agency, project
numbers 2021-00067 (M.M., F.H., L.R.), 2023-00888 (RESILIENT, M.M.)
and 2020-004542 (M.M., F.H., L.R.). This research was partially funded
by CETPartnership, the Clean Energy Transition Partnership under
the 2022 joint call for research proposals, co-funded by the European
Commission (grant agreement number 101069750). This research was
partially funded by the European Union’s Horizon Europe



research and innovation programme under the UPTAKE project
(g.a. no. 101081521). The views and opinions expressed are, however,
those of the author(s) only and do not necessarily reflect those of
the European Union or CINEA. The computations and data handling
were enabled by resources provided by the National Academic
Infrastructure for Supercomputing in Sweden (NAISS) and the
Swedish National Infrastructure for Computing (SNIC) at Chalmers
Centre for Computational Science and Engineering (C3SE), partially
funded by the Swedish Research Council through grant agreement
numbers 2022-06725 and 2018-05973.


**Author contributions**

M.M. conceived and designed the research together with F.H.; M.M.
extended the model with feedback from F.N. and E.Z.; M.M. performed
the modelling and analysis and created the visualizations; M.M. wrote
the paper with feedback from F.H., F.N., E.Z., G.B. and L.R.


**Funding**
Open access funding provided by Chalmers University of Technology.


**Competing interests**
The authors declare no competing interests.


**Additional information**

**Extended data** is available for this paper at
[https://doi.org/10.1038/s41560-024-01693-6.](https://doi.org/10.1038/s41560-024-01693-6)


**Supplementary information** The online version
contains supplementary material available at
[https://doi.org/10.1038/s41560-024-01693-6.](https://doi.org/10.1038/s41560-024-01693-6)


**Correspondence and requests for materials** should be addressed to
M. Millinger.


**Peer review information** _Nature Energy_ thanks Nico Bauer, Caspar
Donnison and Francesco Lombardi for their contribution to the peer
review of this work.


**Reprints and permissions information** is available at
[www.nature.com/reprints.](http://www.nature.com/reprints)


**Publisher’s note** Springer Nature remains neutral with regard
to jurisdictional claims in published maps and institutional
affiliations.


**Open Access** This article is licensed under a Creative Commons
Attribution 4.0 International License, which permits use, sharing,
adaptation, distribution and reproduction in any medium or format,
as long as you give appropriate credit to the original author(s) and the
source, provide a link to the Creative Commons licence, and indicate
if changes were made. The images or other third party material in this
article are included in the article’s Creative Commons licence, unless

indicated otherwise in a credit line to the material. If material is not

included in the article’s Creative Commons licence and your intended
use is not permitted by statutory regulation or exceeds the permitted
use, you will need to obtain permission directly from the copyright
[holder. To view a copy of this licence, visit http://creativecommons.](http://creativecommons.org/licenses/by/4.0/)
[org/licenses/by/4.0/.](http://creativecommons.org/licenses/by/4.0/)


© The Author(s) 2025



[Nature Energy | Volume 10 | February](http://www.nature.com/natureenergy) 2025 | 226–242 **242**


**Article** https://doi.org/10.1038/s41560-024-01693-6



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-17-0.png)

**Extended Data Fig. 1 | Simplified depiction of main biomass usage options**
**and competing pathways based on electricity-derived energy carriers and**
**fossil fuels.** Energy flows are shown, except for the dashed lines, which show
mass flows of captured carbon (which is optional for each process). The captured


[Nature Energy](http://www.nature.com/natureenergy)



carbon can be utilized for hydrocarbon production (CCU), or sequestered (CCS).
Abbreviations: AD = anaerobic digestion, CCU=carbon capture and utilization,
CCS=carbon capture and storage, DAC=direct air capture, EV=electric vehicle,
SMR=steam methane reforming, SNG=substitute natural gas, V2G=vehicle to grid.


**Article** https://doi.org/10.1038/s41560-024-01693-6



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-18-3.png)

**Extended Data Fig. 2 | Carbon sequestration capacity requirement for**
**meeting net-nero and net-negative (-110%) emissions targets.** 12 MtCO 2 carbon
sequestration slack is added on top of the minimum amount necessary to achieve


[Nature Energy](http://www.nature.com/natureenergy)



targets while sequestering process emissions and negative emissions, resulting
in 140 MtCO 2 /a for the net-zero scenario and 600 MtCO 2 /a for the net-negative
(-110%) emissions scenario.


**Article** https://doi.org/10.1038/s41560-024-01693-6

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-19-4.png)


**Extended Data Fig. 3 | Biomass cost-supply curve.** Medium domestic residues from JRC ENSPRESO [83] and biomass imports as described in Methods and in [11] are
assumed. Solid biomass usage in the assessed region in 2021 [84][,][85] and 2020 wood chip prices [89] are also shown.


[Nature Energy](http://www.nature.com/natureenergy)


**Article** https://doi.org/10.1038/s41560-024-01693-6

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-20-0.png)


**Extended Data Fig. 4 | Sankey diagram of energy flows in the cost-optimal result for the net-negative (-110%) emissions scenario.** The width corresponds to the
energy flow. Abbreviations: CHP=combined heat and power.


[Nature Energy](http://www.nature.com/natureenergy)


**Article** https://doi.org/10.1038/s41560-024-01693-6



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-21-5.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-21-6.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-21-20.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-21-21.png)

**Extended Data Fig. 5 | Heat maps of cost-optimal configurations for achieving**
**a net-negative (-110%) emissions target when varying carbon capture**
**efficiency (x-axis) and direct air capture capital expenditure (y-axis).** The
cost of BECC is held constant but would likely in reality also experience cost
reductions if DAC capital expenditure (CAPEX) is reduced through technological
learning. However, cost reductions are expected to be lower in comparison
because BECC is considered to be less modular than DAC [108][,][148] . DAC CAPEX of


[Nature Energy](http://www.nature.com/natureenergy)



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-21-11.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-21-22.png)

1500-7000 €/kgCO 2 /h correspond to 171-800 €/tCO 2 /a at a full utilization rate,
or a capital cost of 16-75 €/tCO 2 with a 7% discount rate and a 20 year lifetime.
Panel ( **a** ) shows total biomass usage while ( **b** ) shows biomass imports. Panel
( **c** ) shows the system cost increase of excluding biomass. Panels ( **d** - **f** ) show BECC
(bioenergy with carbon capture), DAC (direct air capture) and total CC (carbon
capture) and DAC, respectively.


**Article** https://doi.org/10.1038/s41560-024-01693-6


**Extended Data Fig. 6 | Nodal system cost distribution for achieving a net-negative (-110%) emissions target in the least-cost case.** Above with medium domestic

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-22-0.png)
biomass potentials and including biomass imports, and below without biomass (except municipal solid waste).


[Nature Energy](http://www.nature.com/natureenergy)


**Article** https://doi.org/10.1038/s41560-024-01693-6


**Extended Data Table 1 | Cost and efficiency assumptions of biomass technologies, e-fuels and direct air capture, compared**
**to assumptions in key studies**

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-23-0.png)


Capital expenditure (CAPEX) costs have been converted to €2015 for Luderer and Klein, who use 2005, through the Chemical Engineering Plant Cost Index (CEPCI) [149] and subsequent
conversion to €. _η_ _el_ denotes the conversion efficiency specifically from biomass to electricity, while _η_ _fu_ = fuel efficiency, _η_ _th_ = thermal efficiency and _η_ _cc_ = carbon capture efficiency. The data
from Bogdanov et.al. [150] is for 2040. FT = Fischer-Tropsch, CC = carbon capture, SNG = substitute natural gas, CHP = combined heat and power.


[Nature Energy](http://www.nature.com/natureenergy)


**Article** https://doi.org/10.1038/s41560-024-01693-6


**Extended Data Table 2 | Biomass residue potentials in terms of energy and CO** **2**


Medium and high potentials are taken from the respective JRC ENSPRESO scenarios [83] . Forest residues, industry wood residues and landscape care biomass are included in the solid biomass
potential, while manure & slurry, straw and sewage sludge are assumed to be digestible and municipal solid waste needs to be incinerated separately. For solid biomass and municipal
solid waste, the CO 2 is calculated at full combustion, while for digestible biomass the CO 2 waste stream from the anaerobic digestion process is added to the CO 2 at full combustion of the
methane produced.


[Nature Energy](http://www.nature.com/natureenergy)


**Article** https://doi.org/10.1038/s41560-024-01693-6


**Extended Data Table 3 | Technology growth to achieve cost-optimal capacities in net-negative (-110%) emission scenarios**


Compared to historical precedents (solar and wind power, maximum growth rate G [88] ) and growth projections for 2050 (electrolysers, capacity in GW [95] ). For solar and wind, Gompertz curves
for technological growth have been estimated as described in the Methods section. The G-values are the highest growth at the inflection point of the S-curve, normalized to total electricity
demand at that point.


[Nature Energy](http://www.nature.com/natureenergy)


**Article** https://doi.org/10.1038/s41560-024-01693-6


**Extended Data Table 4 | Direct-Air-Capture (DAC) projections and assumptions**

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/millingerDiversityBiomassUsage2025/millingerDiversityBiomassUsage2025.pdf-26-7.png)


For the cost projections, a 1 GtCO 2 /a total scale of global DAC deployment is used for comparability between the two studies. Learning rates of 10-18% and uncertainties for the cost of a
First-of-a-kind (FOAK) plant, as well as uncertain deployment magnitudes lead to a large future CAPEX cost range. Substantial uncertainties in electricity and heat input (for which some
assume waste heat only) and the cost of energy provision lead to further differences between both cost projections [108][,][151] and modelling studies [47][,][51][,][62][,][78][,][150][,][152] . The DAC technology assumptions
in this study are based on values for 2040 from DEA [136]. [‡] LCOC (Levelised Cost of Carbon) indicatively estimated as follows: levelised cost calculated by annuitising CAPEX with 7% discount
rate and a 20 year lifetime, and using base scenario model output time-averaged shadow prices for electricity (65 €/MWh), process steam (66 €/MWh) and district heat (25 €/MWh, for which
0.1 MWh/tCO 2 is output from the process and credited).


[Nature Energy](http://www.nature.com/natureenergy)


