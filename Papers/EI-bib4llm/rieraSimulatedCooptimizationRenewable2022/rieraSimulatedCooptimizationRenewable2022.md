# Citation Key: rieraSimulatedCooptimizationRenewable2022

---

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-0-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-0-1.png)
### ARTICLE

https://doi.org/10.1038/s41467-022-31233-3 **OPEN**

## Simulated co-optimization of renewable energy and desalination systems in Neom, Saudi Arabia


Jefferson A. Riera [1], Ricardo <PERSON> 2, <PERSON> 1 & <PERSON> 2 ✉


The interdependence between the water and power sectors is a growing concern as the need

for desalination increases globally. Therefore, co-optimizing interdependent systems is

necessary to understand the impact of one sector on another. We propose a framework to

identify the optimal investment mix for a co-optimized water-power system and apply it to

Neom, Saudi Arabia. Our results show that investment strategies that consider the co
optimization of both systems result in total cost savings for the power sector compared to

independent approaches. Analysis results suggest that systems with higher shares of nondispatchable renewables experience the most significant cost reductions.


1 Physical Science and Engineering Division, King Abdullah University of Science and Technology, (KAUST), Thuwal 23955-6900, Saudi Arabia. 2 Computer,
Electrical and Mathematical Sciences & Engineering Division, King Abdullah University of Science and Technology, (KAUST), Thuwal 23955-6900, Saudi
Arabia. [✉] [email: <EMAIL>](mailto:<EMAIL>)


NATURE COMMUNICATIONS | (2022) 13:3514 | https://doi.org/10.1038/s41467-022-31233-3 | www.nature.com/naturecommunications 1


### ARTICLE NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-022-31233-3



he water and energy sectors are becoming inextricably
linked as water resources become scarcer worldwide [1] . In
# T 2014, nearly 4% of global electricity consumption was due

to the water sector. Projections estimate that the energy used by
the water sector will double by 2040 [2] . The largest increase is
associated with desalination, an energy-intensive process that
requires electrical and/or thermal power to produce freshwater.
Today, desalination and water re-use meet only 0.7% of the global
water need; however, these processes account for nearly a quarter
of the total energy consumption by the water sector [2] .
Desalination technology has been commonly used in the
Middle East and North Africa (MENA), where surface freshwater
resources are limited, and fossil groundwater is rapidly being
depleted. Countries in the MENA region have increased their
desalination capacity to keep up with water demand and reduce
groundwater withdrawals [3] . In 2016, approximately 7 billion m [3] of
water were desalinated in the MENA region, a number that is
expected to increase twelve-fold to eliminate reliance on nonrenewable groundwater extraction [2] . Saudi Arabia, which experiences the largest water deficit in the region, will see the largest
growth in desalination and water storage capacity. The country is
expected to desalinate nearly 30 billion m [3] /day of water in 2040,
up from 1.5 billion m [3] /day in 2014 [4] .
While desalination plays a significant role in the Middle East
region, it has gained prominence in other parts of the world,
including the United States, where droughts have dwindled water
supplies [5] . Water-rich states located in the central region of the US
have, for decades, provided water across borders to meet the
growing demand of the dry Southwest. To do so requires transportation networks with significant investment and high operational costs. Similarly, geopolitics and concerns over aquifer
recharge in the central states may limit water sent to dry areas [6] .
With the risk of permanent aridification of the Southwest
states, the region has begun to invest heavily in desalination
technology rather than relying on neighboring states to meet
water demand. In 2015, the Carlsbad Desalination Plant, the
largest desalination plant in the United States with a capacity of
190,000 m [3] /day, opened in California [7] . Other states such as
Arizona and Texas have followed suit to address their water
scarcity issues. Currently, the United States has a total installed
desalination capacity of 7.5 million m [3] /day [6] .
The impact of growing desalination demand on the power
sector is significant. Electricity consumption due to desalination is
expected to grow ten times the current consumption in MENA by
2050 [3] . Similar trends are found in other parts of the world [1] .
These changes are not only associated with growth in desalination
needs but also due to a shift away from thermal-based
desalination [2] . The alternative approach, electric-based desalination, relies on electric pumps to push seawater through membranes to remove the salt. Such an approach is deemed a
sustainable method to desalinate water if the electricity is derived
from renewable sources.
New projects in the region are pushing the integration of
desalination with renewable technologies to ensure a sustainable
stream of water. Such projects include the futuristic cities/communities of Masdar City in the United Arab Emirates and Neom [8]
in Saudi Arabia. The idea of futuristic cities is to create environments dedicated to sustainable practices such as zero waste,
high renewable energy penetration (zero carbon) and carbonneutral fuels [9], and green infrastructure. With these considerations
in mind, it is essential to develop modeling and optimization tools
to understand the impact of a growing water sector on the power
system.
Tools such as generation expansion planning (GEP) models
enable decision-makers to identify the optimal generation/production mix of a system [10] . Generation expansion planning



models have evolved [11] and become more important as power
systems transition to integrate renewable generation
facilities [10][,][12][–][19], energy storage [16], and required transmission
expansion [17][,][19], accommodate environmental costs [10], and address
reliability and flexibility concerns. The integration of investment
and operational constraints in GEPs provides a means to determine the best course of action regarding what technologies to
install, the plant capacities, or where and when to build them [20] .
These models are driven by technology costs and renewable
resource availability to meet power demand.
GEPs are challenging to solve because they involve decisions
happening at different time scales. Operational decisions typically
occur hourly for the power sector, whereas investment decisions
occur yearly or after several years [11] . Such models become harder
to solve when dealing with variabilities such as renewable power
output, intra-annual demand variability, or long-term uncertainty
such as annual demand growth, investment costs, or operating
cost [17] . It is necessary to consider that when dealing with uncertainty, longer time horizons result in higher uncertainty.
GEPs have been historically used to model an individual sector,
particularly the power sector [10][,][12][–][19] . As sectors become more
interdependent, there has been growing research interest in sector
coupling and co-optimization to understand the reliability and
flexibility of such systems. With co-optimization, sectors can
react to one another to make more informed investment and
operational decisions. On the operational side, electricity consumption peaks can be shifted by considering incentives/disincentives on the consumer end (the other sector), a practice known
as demand-side management [21] .
In the traditional sense, demand-side management (DSM)
refers to strategies and initiatives that encourage consumers to
shift their energy use. DSM has two main benefits; first, consumers can benefit from favorable tariffs when their behavior is
altered to adjust when and how much electricity they use. Secondly, the power system benefits by shifting energy consumption
from peak to non-peak hours, preventing the system from
potentially overloading [22] . Demand-side management benefits can
also be assessed by shifts in production from high production cost
hours to low-cost hours. In investment models, shifting electricity
peaks may allow for cost savings by reducing the total power
generating capacity necessary to meet demand [23] .
The goal of co-optimizing the water and energy sectors is to
exploit potential investment and operational cost savings arising
from the optimized sizing of generation plants and storage due to
desalination-induced demand-side management. In a traditional
power system GEP model, electricity consumption from desalination is accounted for in the total hourly demand at each node,
with assumptions made regarding the power consumption by the
water sector.
Literature on GEP is vast; however, literature that focuses on
the co-optimization of the water and power sectors is limited.
Bognar et al. [24] quantify the cost reductions of water and electricity due to the integration of desalination with a wind-diesel
power system on a Cape Verdean island. Caldera et al. [25] explore
the investment strategy of desalination and renewables in Saudi
Arabia’s power sector. They found a 1-3% decrease in the annual
levelized cost of the integrated system. However, in the previous
references, the models are deterministic. Therefore, they do not
consider uncertainties in long-term demand growth.
Al-Nory and Brodsky [26] examine the optimal scheduling of a
desalination plant with a smart power grid to provide a buffer for
times when renewable resources are interrupted. They found that
desalination plants over-produced water when electricity prices
were at the marginal level. When electricity prices are high,
desalination output decreases, and demand is met primarily with
stored water. That study, however, did not explore capacity



2 NATURE COMMUNICATIONS | (2022) 13:3514 | https://doi.org/10.1038/s41467-022-31233-3 | www.nature.com/naturecommunications


### NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-022-31233-3 ARTICLE



investments, given that the considered time horizon is only one
day. Al-Nory and El-Beltagy [27] use polynomial chaos expansion to
account for uncertainty (renewable supply, water, and electricity
demand). However, their planning time horizon is only seven
days with a daily resolution. While short-term uncertainty is
considered, long-term uncertainties such as demand growth or
investment costs are ignored.

–
This work proposes a multiperiod (2020 2029) generation and
expansion planning co-optimization model for a system that
considers investment and operation decisions for the power and
water sectors under uncertainty. The model accommodates a high
penetration of renewable energy sources and introduces flexibility
through water storage, batteries, and demand-side management.
We assess the benefits of co-optimizing the water and electricity
sectors by comparing the results to stand-alone models that
optimize the water and power sectors, each acting independently
of the other. In a co-optimized model, the electricity demand at
each hour excludes desalination consumption – we call this
baseline power demand. The water sector has an hourly water
demand schedule and must desalinate to meet this demand. The
significance of co-optimization is that the two sectors can interact
and make cost-saving decisions. Accordingly, the water sector
may desalinate water based on electricity production costs or the
availability of renewable resources. The power sector must produce enough electricity to meet the baseline demand and the
power demand required by the water sector, shifting water production to non-peak electricity hours. The model was applied to
Neom, Saudi Arabia, as it is a new construction project –
greenfield – which aims to be fully/or near fully renewable. In a
particular case, the model includes a 4 GW transmission connection to the existing Saudi Arabian power grid to study the
costs and added flexibility to the power-water coupled system.
With the same goal in mind, the model also allows for CCGT
investments.


Results
We present a case study to highlight a clear example of the
benefits of co-optimization. We choose Neom, Saudi Arabia [8] . The
area of Neom is slightly smaller than the country of Belgium;
therefore, the implementation of our model takes a country-level
perspective, and it can be applied to other countries as well.
Neom is projected (high scenario estimate) to consume nearly
45.35 TWh of electricity by 2030 for a population of 770 thousand people; this will translate to an annual per capita electricity
consumption of 59.35 MWh. The projected per capita consumption will be higher than any developed country, including
Iceland, at 55.05 MWh. Industries that will contribute to Neom’s
high per capita power consumption include renewable tech
manufacturing, green chemical production, desalination, and data
centers. We follow a scenario-based approach to capture the
uncertainty in demand growth each year. The uncertainty is
characterized by a set of distinct realizations. Each scenario
defines the number of decisions that must be made. Our model
considers three scenarios: low, medium, and high, each with a
definite demand for water and power and a probability of
occurrence. We do not consider uncertainty in renewable
resources due to the difficulty of generating hourly forecasts for
an entire year or more. However, we used ten years of highresolution data to generate representative days that represent
clusters of days with similar attributes.
We divide Neom into nine nodes; we also include one node
located outside of Neom and surrounding the city of Tabuk, as
depicted in Fig. 1. Power demand is considered for one node
(Node 7), given that most industries and residential areas would
be in that location. Having a single demand node could limit the



model to building all power generating and water producing
capacities at that given node. However, that is only the case if the
renewable resource potential for that node is equivalent to or
better than all surrounding nodes. Therefore, an expansion would
depend on the resource availability of renewables in the regions
surrounding the demand node. If desalination facilities are built
in other regions along the Red Sea coastline (Nodes 1 and 9), their
electricity consumption will create power demand in the nodes
where they are constructed. We assess the economics of investing
in four possible power generating technologies: combined cycle
gas turbines (CCGT), photovoltaics (PV), concentrated solar
power (CSP), and onshore wind turbines, as well as battery storage and hydroelectric pumped storage (HPS). We consider
constructing 14 possible transmission lines with a nodal balance
to ensure that supply meets demand.
For the water sector, reverse osmosis (RO) desalination and
water storage tanks are considered for investments. We do not
account for water flow at a nodal level but instead define a
constraint requiring that the aggregate hourly water production
equal the aggregate hourly water demand.
Given that desalination can account for a large percentage of
the electricity consumption in the Middle East, we present a
power mix where desalination accounts for 20% of the total
electricity consumption. We have also included results for a
power mix where desalination accounts for 4% of the total electricity consumption in the Supplementary Information under
Supplementary Figs. 9–14 and Supplementary Tables 11–17.
We present results for four cases: 1) Base case, where investment is allowed in only renewable generating technologies
(CCGT is not considered), 2) the Kingdom of Saudi Arabia (KSA)
Grid, where Neom is allowed to obtain power from the existing
KSA power network via a 4-GW transmission line, 3) Photovoltaics (PV) Only, where investments are only allowed in PV
solar technology and battery storage, and 4) Wind Only, where
investments are only allowed in wind turbines and battery storage. The PV Only and Wind Only cases are used to validate our
results and understand why wind was selected over PV by our
model in the Base case. Furthermore, given Saudi Arabia’s high
potential for solar energy, we sought to quantify the decision
strategy for systems that are solely powered by PV. In Supplementary Figs. 3 and 4, we present results where we allow for the
investment of CCGT under varying levels of renewable penetrations. These scenarios were designed to explore the added flexibility of allowing investments in fossil-fuel-based power
generators.
For each case, we compare two strategies: (1) an independent
approach, where the power and water sector investments are
planned individually, and (2) a co-optimized approach where
both sectors are aware of their corresponding decisions and adjust
accordingly.
Figure 2 presents the optimal power generation mix for all
cases using the co-optimization strategy. For the Base case, the
5.2-GW system is comprised of CSP and wind power, with
CSP accounting for 75% of the total power capacity. Investments
for CSP are higher than those for wind due to the availability of
thermal energy storage, which allows power to be generated
even when solar resources are low. There are no investments in
PV solar capacity, as it is more cost-effective to invest in
wind turbines than in PV, due to their slightly lower per megawatt investment costs and the more readily available
renewable resource. PV would only generate power for half of
the day.
Hydroelectric pumped storage systems are expensive. Their
application in hot arid regions such as the Middle East would
require significant maintenance to deal with high evaporation
losses, further driving up costs. Given its high costs compared to



NATURE COMMUNICATIONS | (2022) 13:3514 | https://doi.org/10.1038/s41467-022-31233-3 | www.nature.com/naturecommunications 3


### ARTICLE NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-022-31233-3

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-3-1.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-3-2.png)

Fig. 1 Nodal system for Neom. Generation technologies can be built on ten nodes and linked via 14 possible transmission lines (L1-L14).



other power generation technologies, the model does not invest in
hydroelectric pumped storage in the Base case.
For the KSA Grid case, CSP and wind are also the only generating technologies the model selects for investment. However,
the total investment capacity is lower (4.4 GW) than in the Base
case, and the capacity mix is roughly 48% CSP and 52% wind.
The model decides to invest slightly more in wind capacity than
in CSP because of the added flexibility and reliability offered by
obtaining power from the rest of Saudi Arabia. At any time, 4 GW
of power can be obtained from outside of Neom. Therefore, the
model can invest in more wind technology (cheaper) than in CSP
because additional thermal energy storage is not required.
A summary of the capacity values for all cases is presented in

–
Supplementary Tables 8 10. Location siting maps for the invested
technologies for all cases are shown in Supplementary Figs. 5–8.
For the Wind Only and the PV Only cases, the total generating
power capacities are much larger than in the Base case, and they
require investments in battery storage. Because wind and solar
power are intermittent resources, farm capacities must be large
enough to generate power at a given hour to meet demand when
the resource is available, and enough power to store in batteries to
meet demand when the resource is limited. As a result, the
capacity investment for the Wind Only case is 12.1 GW, which is
2.3 times larger than the Base case. However, the battery storage
capacity is about the same as the thermal storage capacity
required in the Base case. For the PV Only case, the power
generating capacity is the largest at 40.4 GW due to the limitation
of solar resources. Solar power is only available for half the day, so
the system invests significantly in storage capacity and must
operate the batteries more than the other cases.
Table 1 presents the power sector’s investment, operating, and
total costs for all cases and strategies. As expected, the cooptimization of both sectors is beneficial in that it results in a total
cost reduction for the power sector compared to the independent
strategy. The cost savings are due to decreased total invested
power generating capacity. Note that systems that do not invest in



dispatchable power generators are significantly more expensive as
they require battery capacity to supply power when renewable
resources are limited or unavailable. Nevertheless, the cost savings
are more significant when co-optimizing these systems with the
water sector.
All co-optimized models show a decrease in total investment
capacity for the power sector. Co-optimization allows for shaving
peak demand by shifting necessary power consumption by the
water sector to other times during the year. Such shifts reduce the
total generating capacity required to meet power demand. The
savings are greatest for the Wind Only and PV Only cases. For
these cases, the generating capacity is significantly greater than
the power demand at any given hour because these systems must
generate enough electricity to meet demand and battery storage
for future use.
Table 2 presents the costs incurred by the water sector for all
cases under the two strategies. Unlike the power sector, which
saw cost savings, the water sector experienced increased total
costs, mainly due to increased investments. Note that for cases
with dispatchable power generators such as the Base and KSA
Grid, the percentage change in total costs between the independent and co-optimized models is negligible, 0.1% and 0.2%,
respectively.
For the Wind Only and PV Only cases, total costs increased by
about 1% and 5%, respectively, due to increased total RO and
tank storage capacity. For the Wind Only case, RO capacity
increased by 8%, and storage tanks capacity increased by about
14% for the co-optimized strategy compared to the independent
strategy.
The PV Only case saw a substantial increase in total desalination capacity. The co-optimized PV Only model required
nearly 41% more RO desalination capacity than the independent
strategy. There was also a 4% increase in water storage capacity.
Because the PV system can only generate power half of the day,
more desalination capacity is needed to produce excess water
during hours when there is a power surplus, or when it is more



4 NATURE COMMUNICATIONS | (2022) 13:3514 | https://doi.org/10.1038/s41467-022-31233-3 | www.nature.com/naturecommunications


### NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-022-31233-3 ARTICLE

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-4-2.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-4-3.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-4-7.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-4-8.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-4-13.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-4-20.png)

Fig. 2 Power generating capacities for the a) Base, b) the Kingdom of Saudi Arabia (KSA) Grid, c) Photovoltaics (PV) Only, and d) Wind Only cases.
Cases PV Only and Wind Only, without dispatchable generators, require significantly larger generating capacities and battery storage. Plotted are curves
for concentrated solar power (CSP), photovoltaics (PV), wind, combined cycle gas turbine (CCGT), battery, thermal energy storage (TES), hydroelectric
pumped storage (HPS), and the Kingdom of Saudi Arabia (KSA) Grid.



cost-effective to do so (namely when batteries are not discharging
to meet power demand).
Despite the increase in total costs for the water sector, the total
costs of the entire system (water and power) decreased for all
cases, as shown in Table 3. Co-optimization, therefore, resulted in
net savings for the power-water system.
The costs of both the power and water sectors were also
evaluated using levelized cost metrics, reported in Table 4. For all
cases, excluding the KSA Grid case, the levelized cost of electricity



(LCOE) decreased when the sectors were co-optimized. For the
Base case, the reduction in LCOE is negligible, a minor 0.1%
reduction. The LCOE of the independent Wind Only and PV
Only cases were 134.07 USD per MWh and 47.90 USD per MWh.
Co-optimizing the two sectors resulted in a 4% and 7% decrease
in LCOE for the respective cases. By understanding water production patterns, capacity investment or power spillage can be
decreased, which reduced the LCOE. However, in the KSA Grid
case, the co-optimized model had a higher LCOE than the



NATURE COMMUNICATIONS | (2022) 13:3514 | https://doi.org/10.1038/s41467-022-31233-3 | www.nature.com/naturecommunications 5


### ARTICLE NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-022-31233-3



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-5-0.png)





![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-5-1.png)





![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-5-2.png)





![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-5-3.png)





independent model because it buys more power from the Saudi
power grid to supply water desalination facilities at more strategic
times.
For all cases, excluding the KSA Grid, the change in LCOW is
negligible.
The benefits of co-optimization can also be seen on the
operational level, for both the power and water sectors. Figure 3a
shows the operations for the Base case where water production is
relatively constant until there are two dips – a minor dip between
hours 1604-1607 and a more significant decrease between hours
1640-1656. Note that the reduction in water production coincides
with the exact hours when CSP operates at its maximum capacity.
In Fig. 4a, for the PV Only system, we see sharp drops in water
production during hours when there is no sun, and the system
relies solely on battery discharge to meet the total power demand.
We see a similar pattern in the Wind Only case, where water



production declines when batteries meet most power demand.
This is seen in Fig. 4b between hours 1637 and 1656. These drops
in water production are related to the need to minimize total
costs. By producing less water during these periods, battery discharge is lower, and the system incurs a smaller battery operational cost.
From Fig. 5a, we see that the co-optimized model shaved the
power demand peaks compared to the independent strategy
during these times. In the case of the KSA grid, the same limiting
factor occurs between hours 1596–1608 and hours 1650–1654, as
depicted in Fig. 3b. It is important to note that the desalination
plant is built in the same node as the CSP facility in both cases.
The desalination facilities are powered by CSP exclusively;
therefore, when the generators are at maximum capacity, water
production decreases to ensure that the baseline power demand
is met. Figure 5b shows the power consumption line from the



6 NATURE COMMUNICATIONS | (2022) 13:3514 | https://doi.org/10.1038/s41467-022-31233-3 | www.nature.com/naturecommunications


### NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-022-31233-3 ARTICLE

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-6-4.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-6-5.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-6-8.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-6-9.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-6-16.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-6-17.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-6-20.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-6-21.png)

Fig. 3 Power and water system operations in year 10 for the a) Base and b) the Kingdom of Saudi Arabia (KSA) Grid cases. Desalination plants are
powered entirely by concentrated solar power (CSP) at their given node; therefore, when CSP farms operate at maximum capacity, water production
decreases to meet the baseline power demand.



co-optimized model falling significantly below the independent
model’s consumption, evidence of power demand peak shaving.
The solutions of the proposed model are driven by the technology costs and the availability of the renewable resources (solar
irradiance, temperature, and wind speed), and their hourly
availability to match power and water demand. Therefore, projected technology costs impact the capacity of each technology to
install, as discussed in Alraddadi et al. [19] . However, projected
technology costs over the medium-term future are uncertain. In
the proposed model, we do not consider uncertainty in the



technology investment costs; average values are adopted. Thus,
the model can be solved using a rolling horizon approach to
accommodate the future variability of technology costs. The
presented case study assumes a time horizon of ten years, with
investments in technology capacities being made at the beginning
of each year. The investments for each year are the so-called firststage decisions that are made before the realization of the
uncertain parameters. In a rolling horizon approach, the optimization problem is solved in the first iteration, and only the
investments for the first year are implemented. In the second



NATURE COMMUNICATIONS | (2022) 13:3514 | https://doi.org/10.1038/s41467-022-31233-3 | www.nature.com/naturecommunications 7


### ARTICLE NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-022-31233-3

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-7-4.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-7-5.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-7-8.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-7-9.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-7-15.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-7-16.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-7-20.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-7-21.png)

Fig. 4 Power and water system operations in year 10 for the a) Photovoltaics (PV) Only and b) Wind Only cases. Co-optimization allows water
production to decrease during hours when batteries are discharging to meet the majority of or all power demand.



iteration, moving forward one year along the time horizon and
after the first-year investments are made, technologies forecast
costs are updated. The optimization problem is then solved for
the next ten years. Again, only the first year (second year from the
original time horizon) investments are implemented from the
optimization solution. The nine years after the first year in each
optimization problem prevent a limited view of future demand.
In this way, technology costs are updated yearly, and their
uncertainty is mitigated.
Incorporating stochasticity into generation expansion
planning models makes them more challenging to solve.



Nevertheless, stochastic programming allows decision-makers
to make decisions that are feasible over a number of scenarios
and obtain a solution that is optimal for the expectation of the
scenarios considered. This feasibility feature is highly relevant.
For example, an optimal solution resulting from a deterministic optimization problem using average values of the
uncertain parameters may be infeasible for some scenarios
describing the uncertain parameters. This can occur if the
optimization model does not include recourse actions (e.g., the
option to obtain power from the KSA grid) to handle some
investment decisions. Besides, stochastic programming



8 NATURE COMMUNICATIONS | (2022) 13:3514 | https://doi.org/10.1038/s41467-022-31233-3 | www.nature.com/naturecommunications


### NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-022-31233-3 ARTICLE

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-8-2.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-8-6.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-8-10.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-8-17.png)

Fig. 5 Power demand for the a) Base, b) the Kingdom of Saudi Arabia (KSA) Grid, c) Photovoltaics (PV) Only, and d) Wind Only cases under
independent and co-optimization strategies. Due to co-optimization, peak shavings occur in all cases. Power consumption is shifted accordingly – at the
start of the year, the co-optimized model consumes more power than the independent model. However, the co-optimized model consumes less power than
the independent model at specific times throughout the remainder of the year.



problems can unveil solutions that are not available by optimizing one scenario at a time.
To assess the quality of the stochastic programming solutions,
we present the values of the stochastic solution (VSS) obtained.
We also report the expected value of perfect information (EVPI),
which indicates the maximum price that decision-makers should
pay to obtain perfect information. Table 5 presents the VSS and
EVPI values for the four cases presented. On average, decisionmakers can save 115 million USD for the Base case by considering
uncertainty. The VSS is also greater than zero for the remaining



cases, indicating potential cost savings when using a stochastic
programming approach. The EVPI results show that perfect
information is less important in the KSA Grid case, which results
from the constant power availability provided by the KSA grid,
and more relevant in the PV Only case.
In the multiple cases addressed in this work, representative
days are adopted in the stochastic programming model to overcome the computational burden of considering a full-time resolution with 8760 h per year in a multi-year horizon model. This
reduces the model complexity and computational burden by



NATURE COMMUNICATIONS | (2022) 13:3514 | https://doi.org/10.1038/s41467-022-31233-3 | www.nature.com/naturecommunications 9


### ARTICLE NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-022-31233-3



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-9-0.png)



reducing the model size in the time dimension. However, an
approximation is introduced by compressing the variability of the
renewable resources time series into a number of representative
days. To investigate the trade-offs of using representative days, we
compare results from models using representative days and a fulltime resolution in Supplementary Note 9. The results show that a
stochastic planning model using a multi-year horizon with a fulltime resolution is computationally intractable, highlighting the
importance of using representative days in these types of models.
Considering a single-year horizon stochastic model, the results
with both time representations lead to similar investment decisions in various technologies. However, compared to the representative day model, the full-time resolution model shows a
5.12% and 2.21% increase in CSP and wind capacities, respectively, with a 193.5% increase in computing time. The results of
the deterministic model with both approaches follow a similar
trend, with equivalent investments in various technologies.
However, the full-time resolution model shows a 3.00% and
1.64% increase in CSP and wind capacities, respectively, with a
167% increase in computing time. Overall, these results highlight
the computational advantage of using representative days for
multi-year models. Furthermore, close investment decisions are
obtained using representative days compared with the full-time
resolution models. These results are summarized in Supplementary Tables 18 and 19.
The impact of the water sector on the power grid is significant
as co-optimizing the two systems reveals changes in the investment strategy and shifts in the power demand. Thus, cooptimization over multiple sectors can be instrumental for
decision-makers to make more effective decisions regarding
operations and system investments. The results show that cooptimizing the power and water sectors results in lower total
costs. In particular, if only renewable energy sources are selected,
the total cost reduction is more significant. The operational
results also demonstrate that the water desalination operations
adapt to the availability of wind and solar resources, which prevents the model from making further investments. Other sectors
of importance would include the transportation sector, where
there is a push towards utilizing electric or hydrogen-fueled cars
and the chemical sector. Neom is constructing the largest
hydrogen facility to produce hydrogen using renewable energy
sources and water. Hydrogen production will directly impact
Neom’s energy storage capability and power demand. In the same
realm, the electrification of the transportation fleet will also drive
power demand and serve as a distributed battery system in Neom
for short-term energy storage.
The Base case results provide the optimal generation mix for a
fully renewable power system. However, we also analyzed one
case where Neom could obtain power from the KSA grid based
on fossil fuels. The carbon emissions related to this power generation can be accounted for, and a mechanism to offset emissions can be implemented. The hydrogen produced can be
utilized to produce e-fuels, carbon-based fuels such as methanol,



and formic acid [9] . The production of these fuels will also require
carbon dioxide to be sequestered from the air or from industry. In
a similar context, geothermal technology that uses carbon dioxide
as the injection fluid can generate power while reducing the total
emissions of the system.
Future work will evolve in two directions: 1) quantifying carbon emissions and addressing a dynamic carbon offsetting system; and 2) coupling additional sectors (hydrogen production,
transportation, e-fuels) in our co-optimization model to highlight
sector interactions and the collective and individual reliability of
the systems.
The proposed model was applied to Neom, Saudi Arabia;
however, it is a generic model which can be adapted to other
regions given the respective data inputs. The results obtained for
Neom may provide insights into other systems with similar climates, renewable resource availability, or a need for desalination.
These regions include the Southwestern region of the United
States, parts of Australia, Chile, South Africa, and the MENA
region [28] .


Methods
Stochastic programming model. We developed a stochastic programming model
to co-optimize the expansion planning of water desalination, power generation,
and transmission lines. The model considers long-term uncertainty for electricity
and water demand and variability of the availability of renewable resources. The
decision framework involves a two-stage decision process encapsulated into a
single model. The first-stage decisions are investment decisions at the start of each
year to install power generation facilities, storage technologies, transmission lines,
and water desalination systems. The second-stage decisions are related to power
generation operations, water desalination operations, and storage inputs and outputs. The model equations can be found in Supplementary Note 6.
The objective function consists of the total investment cost plus the expected
operating cost from 2020 to 2029. To minimize the objective function, we
approximate a multiple-stage decision process with a two-stage framework.
Investment decisions for each year are all treated as first-stage decisions. The model
should be run every year to improve the approximation, using updated demand
growth forecasts to determine the investment decisions.
Power generating and storage technologies include concentrated solar power
(tower), photovoltaics, hydroelectric pumped storage, combined cycle gas turbines,
and batteries. Water desalination and storage technologies include reverse osmosis
desalination and water storage tanks. Investment decisions are based on the
investment costs of the technologies themselves in a given year. We assume a 1%
decrease in technology investment costs every year. The demand growth
uncertainty is revealed in the subsequent stage, and operating decisions are made
regarding power generation, water desalination, and storage inputs and outputs.
These decisions are made considering the variable operating costs of the
technologies and the resource availability of renewables. Operating decisions are
made on an hourly scale.
The uncertainty in interannual demand growth of both water and electricity is
represented by three scenarios: low, medium, and high demands, each
characterized by a corresponding probability of occurrence. We assume that water
demand growth is related to electricity demand growth; a high demand scenario
means that both water and electricity demands are high. A low-demand scenario
means that both water and electricity demands are low.
Electricity demand, water demand, and renewable resources available for wind
and solar power generation vary throughout a given day and year. We use
representative days rather than all 365 days (8760 h) of a year to make the model
tractable.
The model is formulated as a mixed-integer linear program using the modeling
system GAMS [29] 27.2 and solved with a branch & bound algorithm within the
CPLEX 12.8 [30] package using an optimality gap tolerance of 0.1% and a time limit
of 168 hours. All optimization runs were performed on the KAUST IBEX computer
cluster using exclusive nodes, each having 40 Intel Gold 6148 @ 2.6-GHz
processors and 384 GB of RAM.


Electricity and water demand profiles. Because Neom is a planned project, there
is no historical electricity or water demand data. Therefore, we use annual projections between 2020 through 2029 for both water and electricity (all sectors)
demand. The scenarios are dependent on the projected population sizes. Because
we are co-optimizing the electricity and water systems, we subtract the electricity
demand from desalination based on water demand projections using a conversion
factor of 4 kWh/m [3] . The model accounts for the power consumption needed for
desalination when it determines how much water to desalinate.
We derived the yearly water and electricity demand profiles for Neom from the
demands at KAUST - an academic institution of 7000+ residents approximately in



10 NATURE COMMUNICATIONS | (2022) 13:3514 | https://doi.org/10.1038/s41467-022-31233-3 | www.nature.com/naturecommunications


### NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-022-31233-3 ARTICLE



Thuwal, Saudi Arabia. We obtained the 2018 hourly water and electricity demand
curves for KAUST. We applied the profiles to Neom by normalizing the area under
the curve to 1 and multiplying it by the total annual demand for Neom in a
given year.


Climate data. We use direct normal irradiance, global horizontal irradiance, and
temperature data for Jan 2008 through June 2019 (11.5 years) at a 5 km resolution
spanning the entire region of Neom. This data was obtained from a high-resolution
evaluation of the solar energy resources over the Arabian Peninsula using reanalysis data generated by the Weather Research and Forecasting (WRF) Solar model.
Reanalysis data was validated using daily observations at 46 in-situ radiometer
stations throughout Saudi Arabia [31] .
Wind speed data for the Neom region over the same timespans and resolution
as the irradiance and temperature data was used. The wind speed data was derived
from a high-resolution reanalysis generated using WRF and validated with data
from buoys, scatterometers, and altimeters placed throughout the area [32] .


k-means clustering for representative regions and days. Clustering methods
involve several steps, (1) data normalization, (2) data assignment, and (3) cluster
representation [33] . We normalize our data using the z-scoring full scope normalization on each attribute to shift the mean to 0 and standard deviation to 1. In the
data assignment step, we use a partitional clustering method known as k-means
clustering, which uses Euclidean distance as its distance measure and the centroid
(mean) as its cluster center. Once the clusters are determined, each cluster is
represented by one of its assigned observations, typically done by selecting the
observation that minimizes its distance to the cluster center.
The goal of k-means clustering is to select a set of k clusters that minimizes the
sum of the squared distance between the rows of a dataset and the cluster centroid
they are assigned to; this sum is called the potential. The k-means approach most
commonly used is Lloyd’s algorithm [34], which begins by selecting k arbitrary
centroids, chosen uniformly at random from the normalized dataset. Each row is
assigned to the nearest centroid, determined by calculating the Euclidean distance
between a given row to the k possible centroids. This step forms the initial clusters.
The centroid is then recalculated for each cluster by taking the mean of all data
points in the cluster. We used an alternative approach, k-means + +, which
initializes Lloyd’s algorithm with random starting centers with specific probabilities
proportional to their contribution to the overall potential [35] . Clustering was
performed on MATLAB R2020a using the kmeans function. For more information
on k-means + +, see Supplementary Note 2.


Representative regions. Clustering methods are used to divide Neom into regions
according to climate conditions (e.g., wind speed, solar irradiance, and
temperature) [10] . Climate data is available for a spatial resolution of 5 km,
accounting for 4270 locations within Neom. k-means clustering is used to group
these locations into ten representative regions. More information can be found in
Supplementary Note 3.


Representative days. Clustering methods are also used to capture the hourly
variability from renewable generation and power and water demand [33][,][36] . This
approach decreases the model size by aggregating long time series into shorter time
slices. We use k-means clusters to group 4194 days (~11.5 years) into seven
representative days per year. The days were clustered based on the water and power
demand, wind and solar availability, and temperature. More information can be
found in Supplementary Note 4.


Capacity factors of renewable technologies. Global Horizontal Irradiance
(GHI), the total amount of radiation received by a horizontal surface, and temperature data were used to obtain the capacity factors of photovoltaic panels based
on a standard 0.25 kW solar cell. Direct Normal Irradiance (DNI), the amount of
radiation received per unit area by a surface perpendicular to the incoming rays, is
used to obtain power outputs for CSP farms. Wind power output was obtained
from wind speed data by applying the power curve of Vestas 150-4.2 [37], a 4.2 MW
wind turbine with a hub height of 150 m. Supplementary Note 5 presents the
equations to obtain the power production and capacity factors.


Water desalination. Several technologies are commonly utilized for water desalination, such as multi-effect distillation (MED), multistage flash (MSF) distillation,
and reverse osmosis (RO). Our model includes only RO and limits the operational
constraints, such as ramping rates, to reduce computational complexity. We
assume that a RO plant cannot be shut down as periodic shutdowns generally
damage the permeable membranes. Electricity consumption for a typical RO plant
is held constant throughout the project lifetime at 4 kWh/m [3] .


Preoptimization calculation of annualized costs. Annualized cost is a metric that
allows decision-makers to compare the cost-effectiveness of technologies with
different lifespans, accounting for a given annuity factor. The definitions are given
in Supplementary Note 1. Annualized cost values are given in Supplementary
Tables 1 and 2.



Postoptimization analysis using levelized costs of electricity and water.
Levelized costs of electricity (LCOE) and water (LCOW) metrics are used
to compare the competitive costs of different investment plans. They
describe the average net present value of electricity generation or water production over the lifetime of the project. Their definitions are given in Supplementary Note 7.


The value of the stochastic solution and the expected value of perfect
information [38] . The value of the stochastic solution (VSS) is a measure that
quantifies the cost of planning without considering uncertainty. The expected value
of perfect information (EVPI) is the maximum amount one should pay for complete and accurate information about the future. The definitions of both terms are
given in Supplementary Note 8.


Reporting summary. Further information on research design is available in the Nature
Research Reporting Summary linked to this article.


Data availability
The data relating to the representative days and regions, capacity factors, and electricity and
water demand are available in a Supplementary Data file: Supplementary_Data_2.xlsx. For
original climate data, please contact the authors in [31] and [32] . The cost information and
performance parameters for technologies and transmission lines are available in the
Supplementary Information. The raw data for Figs. 2–5 are available in a Supplementary
Data file: Supplementary_Data_1.xlsx.


Code availability
This work did not use a specific developed algorithm to perform the optimizations. The
mathematical programming models were written in the modeling system GAMS 27.2
and solved with the commercial solver CPLEX 12.8. Detailed descriptions of the sets,
parameters, objective function, constraints, and variables are available in the
supplementary information document. The clustering analysis applied to the climate data
was performed using the commercial software MATLAB R2020a.


Received: 28 March 2021; Accepted: 7 June 2022;


References
1. IRENA. Renewable energy in the water, energy and food nexus. Int. Renew.
Energy Agency 1–125 (2015).
2. International Energy Agency. Water Energy Nexus- Excerpt from the World
Energy Outlook 2016. IEA 60 (2016).
3. Siddiqi, A. & Anadon, L. D. The water-energy nexus in Middle East and North
Africa. Energy Policy 39, 4529–4540 (2011).
4. GWI. Global Water Market 2017. (GWI: Oxford, UK 2016).
5. Jones, E., Qadir, M., van Vliet, M. T. H., Smakhtin, V. & Kang, S. The state of
desalination and brine production: A global outlook. Sci. Total Environ. 657,
1343–1356 (2019).
6. Ziolkowska, J. R. & Reyes, R. Prospects for Desalination in the United StatesExperiences From California, Florida, and Texas. Competition for Water
Resources: Experiences and Management Approaches in the US and Europe
(Elsevier Inc., 2017).
7. [Carlsbad Desal Plant. Carlsbad Desal Plant - FAQ. Available at: https://www.](https://www.carlsbaddesal.com/)
[carlsbaddesal.com/. (Accessed: 2nd February 2021).](https://www.carlsbaddesal.com/)

’
8. NEOM. NEOM – IT [S TIME TO DRAW THE LINE. Available at: https://](https://www.neom.com/whatistheline/?utm_source=google&utm_medium=cpc&utm_campaign=856_RPL_RPL_Search_Clicks_SA_CPC_Text_Brand_EN_08Jan21_20Feb21_2367_Brand&utm_content=brand)
[www.neom.com/whatistheline/?utm_source=google&utm_medium=](https://www.neom.com/whatistheline/?utm_source=google&utm_medium=cpc&utm_campaign=856_RPL_RPL_Search_Clicks_SA_CPC_Text_Brand_EN_08Jan21_20Feb21_2367_Brand&utm_content=brand)
[cpc&utm_campaign=856_RPL_RPL_Search_Clicks_SA_CPC_Text_Brand_](https://www.neom.com/whatistheline/?utm_source=google&utm_medium=cpc&utm_campaign=856_RPL_RPL_Search_Clicks_SA_CPC_Text_Brand_EN_08Jan21_20Feb21_2367_Brand&utm_content=brand)
[EN_08Jan21_20Feb21_2367_Brand&utm_content=brand. (Accessed: 2nd](https://www.neom.com/whatistheline/?utm_source=google&utm_medium=cpc&utm_campaign=856_RPL_RPL_Search_Clicks_SA_CPC_Text_Brand_EN_08Jan21_20Feb21_2367_Brand&utm_content=brand)
February 2021).
9. Air Products. Air Products, ACWA Power and NEOM Sign Agreement
for $5 Billion Production Facility in NEOM Powered by Renewable
Energy for Production and Export of Green Hydrogen to Global Markets.
[(2020). Available at: https://www.airproducts.com/news-center/2020/07/0707-](https://www.airproducts.com/news-center/2020/07/0707-air-products-agreement-for-green-ammonia-production-facility-for-export-to-hydrogen-market#/)
[air-products-agreement-for-green-ammonia-production-facility-for-export-](https://www.airproducts.com/news-center/2020/07/0707-air-products-agreement-for-green-ammonia-production-facility-for-export-to-hydrogen-market#/)
[to-hydrogen-market#/.](https://www.airproducts.com/news-center/2020/07/0707-air-products-agreement-for-green-ammonia-production-facility-for-export-to-hydrogen-market#/)
10. Lara, C. L., Mallapragada, D. S., Papageorgiou, D. J., Venkatesh, A. &
Grossmann, I. E. Deterministic electric power infrastructure planning: Mixedinteger programming model and nested decomposition algorithm. Eur. J.
Oper. Res. 271, 1037–1054 (2018).
11. Dagoumas, A. S. & Koltsaklis, N. E. Review of models for integrating
renewable energy in the generation expansion planning. Appl. Energy 242,
1573–1587 (2019).



NATURE COMMUNICATIONS | (2022) 13:3514 | https://doi.org/10.1038/s41467-022-31233-3 | www.nature.com/naturecommunications 11


### ARTICLE NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-022-31233-3



12. Domínguez, R., Conejo, A. J. & Carrión, M. Toward fully renewable electric
energy systems. IEEE Trans. Power Syst. 30, 316–326 (2015).
13. Kazempour, S. J. & Conejo, A. J. Strategic generation investment under
uncertainty via Benders decomposition. IEEE Trans. Power Syst. 27, 424–432
(2012).
14. Baringo, L. & Conejo, A. J. Wind power investment: A Benders decomposition
approach. IEEE Trans. Power Syst. 27, 433–441 (2012).
15. Liu, Y., Sioshansi, R. & Conejo, A. J. Multistage Stochastic Investment
Planning With Multiscale Representation of Uncertainties and Decisions.
IEEE Trans. Power Syst. 33, 781–791 (2017).
16. Xu, Q., Li, S. & Hobbs, B. F. Generation and storage expansion cooptimization with consideration of unit commitment. 2018 Int. Conf.
[Probabilistic Methods Appl. to Power Syst. PMAPS 2018 - Proc. 1–6. https://](https://doi.org/10.1109/PMAPS.2018.8440205)
[doi.org/10.1109/PMAPS.2018.8440205 (2018).](https://doi.org/10.1109/PMAPS.2018.8440205)

‐
17. Micheli, G., Vespucci, M. T., Stabile, M., Puglisi, C. & Ramos, A. A two stage
stochastic MILP model for generation and transmission expansion planning
[with high shares of renewables. Energy Syst. https://doi.org/10.1007/s12667-](https://doi.org/10.1007/s12667-020-00404-w)
[020-00404-w. (2020).](https://doi.org/10.1007/s12667-020-00404-w)
18. Skar, C., Doorman, G. L., Pérez-Valdés, G. A. & Tomasgard, A. A multihorizon stochastic programming model for the European power system. (2016).
19. Alraddadi, M., Conejo, A. J. & Lima, R. M. Expansion Planning for Renewable
Integration in Power System of Regions with Very High Solar Irradiation. J.
Mod. Power Syst. Clean. Energy 9, 485–494 (2021).
20. Babatunde, O. M., Munda, J. L. & Hamam, Y. A comprehensive state-of-the-art
survey on power generation expansion planning with intermittent renewable
energy source and energy storage. Int. J. Energy Res. 43, 6078–6107 (2019).
21. Hobbs, B. F. The ‘Most Value’ Test: Economic Evaluation of Electricity
Demand-Side Management Considering Customer Value. Energy J. 12, 67–92
(1991).
22. Palensky, P. & Dietrich, D. Demand side management: Demand response,
intelligent energy systems, and smart loads. IEEE Trans. Ind. Inform. 7,
381–388 (2011).
23. Steven A. Gabriel et al. Complementarity Modeling in Energy Markets. Media
180, (2013).
24. Bognar, K., Pohl, R. & Behrendt, F. Seawater reverse osmosis (SWRO) as
deferrable load in micro grids. Desalin. Water Treat. 51, 1190–1199 (2013).
25. Caldera, U., Bogdanov, D., Afanasyeva, S. & Breyer, C. Role of seawater
desalination in the management of an integrated water and 100% renewable
energy based power sector in Saudi Arabia. Water (Switzerland) 10, (2017).
26. Al-Nory, M. T. & Brodsky, A. Towards Optimal Decision Guidance for Smart
Grids with Integrated Renewable Generation and Water Desalination. Proc. Int. Conf. Tools with Artif. Intell. ICTAI 2014-Decem, 512–519 (2014).
27. Al-Nory, M. & El-Beltagy, M. An energy management approach for renewable
energy integration with power generation and water desalination. Renew.
Energy 72, 377–385 (2014).
28. Beck, H. E. et al. Present and future köppen-geiger climate classification maps
at 1-km resolution. Sci. Data 5, 1–12 (2018).
[29. GAMS Development. GAMS. (2019). Available at: https://www.gams.com/.](https://www.gams.com/)
(Accessed: 1st March 2021).
30. IBM. CPLEX User’s Manual Version 12 Release 8. (2017).
31. Dasari, H. P. et al. High-resolution assessment of solar energy resources over
the Arabian Peninsula. Appl. Energy 248, 354–371 (2019).
32. Langodan, S., Viswanadhapalli, Y., Dasari, H. P., Knio, O. & Hoteit, I. A highresolution assessment of wind and wave energy potentials in the Red Sea.
Appl. Energy 181, 244–255 (2016).
33. Teichgraeber, H. & Brandt, A. R. Clustering methods to find representative
periods for the optimization of energy systems: An initial framework and
comparison. Appl. Energy 239, 1283–1293 (2019).



34. Lloyd, S. P. Least Squares Quantization in PCM. IEEE Trans. Inf. Theory 28,
129–137 (1982).
35. Ostrovsky, R., Rabani, Y., Schulman, L. J. & Swamy, C. The effectiveness of
Lloyd-type methods for the k-means problem. J. ACM 59, 1–22 (2012).
36. Pfenninger, S. Dealing with multiple decades of hourly wind and PV time
series in energy models: A comparison of methods to reduce time resolution
and the planning implications of inter-annual variability. Appl. Energy 197,
1–13 (2017).
[37. Vestas. Homepage. Available at: https://www.vestas.com/. (Accessed: 11th](https://www.vestas.com/)
March 2021).
38. Birge, J. R. The value of the stochastic solution in stochastic linear programs
with fixed recourse. Math. Program. 24, 314–325 (1982).


Acknowledgements
This work was partly funded by the Center of Excellence NEOM and the Virtual Red Sea
Initiative at King Abdullah University of Science and Technology (KAUST).


Author contributions

J.R. performed the literature review, drafted the manuscript, and contributed with
modeling and results analysis. R.L. contributed to the modeling and results analysis. J.R.,
R.L., O.K., and I.H. contributed with the initial idea, planned the analysis and reviewed
the manuscript.


Competing interests
The authors declare no competing interests.


Additional information

Supplementary information The online version contains supplementary material
[available at https://doi.org/10.1038/s41467-022-31233-3.](https://doi.org/10.1038/s41467-022-31233-3)


Correspondence and requests for materials should be addressed to Omar Knio.


Peer review information Nature Communications thanks Malak Al-Nory and the other,
anonymous, reviewer(s) for their contribution to the peer review of this work.


[Reprints and permission information is available at http://www.nature.com/reprints](http://www.nature.com/reprints)


Publisher’s note Springer Nature remains neutral with regard to jurisdictional claims in
published maps and institutional affiliations.


Open Access This article is licensed under a Creative Commons
Attribution 4.0 International License, which permits use, sharing,
adaptation, distribution and reproduction in any medium or format, as long as you give
appropriate credit to the original author(s) and the source, provide a link to the Creative
Commons license, and indicate if changes were made. The images or other third party
material in this article are included in the article’s Creative Commons license, unless

indicated otherwise in a credit line to the material. If material is not included in the

article’s Creative Commons license and your intended use is not permitted by statutory
regulation or exceeds the permitted use, you will need to obtain permission directly from
[the copyright holder. To view a copy of this license, visit http://creativecommons.org/](http://creativecommons.org/licenses/by/4.0/)
[licenses/by/4.0/.](http://creativecommons.org/licenses/by/4.0/)


© The Author(s) 2022



12 NATURE COMMUNICATIONS | (2022) 13:3514 | https://doi.org/10.1038/s41467-022-31233-3 | www.nature.com/naturecommunications




---

# **Supplementary Information** Simulated co-optimization of renewable energy and desalination systems in Neom, Saudi Arabia

**Jefferson A. Riera** **[1]** **, Ricardo M. Lima** **[2]** **, Ibrahim Hoteit** **[1]** **, Omar Knio** **[2*]**

1 Physical Science and Engineering Division, King Abdullah University of Science and Technology, (KAUST), Thuwal 23955-6900, Saudi
Arabia. [2] Computer, Electrical and Mathematical Sciences & Engineering Division, King Abdullah University of Science and Technology,
(KAUST), Thuwal 23955-6900, Saudi Arabia. [*] email: <EMAIL>


1


**Supplementary Figures.**

# a)



29°N


28°N


29°N


28°N



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-1-1.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-1-3.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-1-4.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-1-6.png)



















35°E 36°E 37°E


# b)

















35°E 36°E 37°E


Supplementary Figure 1: (a) Clusters after spatial clustering assignments;
(b) Clusters after manual reassignment.



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-1-0.png)

2



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-1-2.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-1-5.png)
a)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-2-1.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-2-2.png)


Locations


b)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-2-4.png)


Hours

Locations



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-2-0.png)



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-2-3.png)



Supplementary Figure 2: (a) Temporal _k_ -means clustering assigns all 4194 days to 5 different clusters.
(b) Centroids are determined by taking the mean of in-cluster days.



3


a) Power Sector b) Water Sector



**Cost** 2000



**Cost**

**[M$]**



3000 0



0



2500 0


2000 0


1500 0


1000 0


500 0





![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-3-2.png)

1600


1400


1200


1000


800


600


400


200


0
0 10 20 30 40 50 60 70 80 90 100


**Renewable Penetration [%]**





![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-3-3.png)

0 0
0 10 20 30 40 50 60 70 80 90 100

**Renewable Penetration [%]**



Supplementary Figure 3: Costs dependent on renewable penetration when desalination consume 20% of total power. (a) Power sector
costs as a function of renewable penetration. With increasing levels of renewable penetration, investment costs increase. Operating cost
decreases, on the other hand decrease at first and then begin to increase when the system relies on more concentrated solar power. (b)
Water sector costs as a function of renewable penetration. Water sector is relatively unaffected by increasing renewable penetration in
the power system.



4


a) Power Sector b) Water Sector



**Cost** 2000



800 0



0



**Cost**

**[M$]**



700 0



0





![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-4-2.png)

1600


1400


1200


1000


800


600


400


200


0
0 10 20 30 40 50 60 70 80 90 100


**Renewable Penetration [%]**





![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-4-3.png)

600 0


500 0


400 0


300 0


200 0


100 0


0 0
0 10 20 30 40 50 60 70 80 90 100


**Renewable Penetration [%]**



Supplementary Figure 4: Costs dependent on renewable penetration when desalination consume 4% of total power. Results are similar
to the 20% results in Supplementary Figure 3.



5


29.25


28.75


28.25



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-5-0.png)





















28.75


27.25

|Col1|2<br>BASE – 20%<br>4<br>1<br>CSP<br>8<br>WIND L12 10<br>9 CSP<br>L13 5<br>DESAL<br>7 3<br>6<br>CSP<br>DESAL<br>DEMAND|Col3|
|---|---|---|
|.|35<br>35.5<br>36<br>36.5<br>||



Longitude


Supplementary Figure 5: Transmission network and net power flows for Base case for 20% power consumption
from desalination. The system depends heavily on concentrated solar power (CSP) to meet power demand.
Desalination facility at node 9 relies entirely on power generated at that node.


6


29.25


28.75


28.25



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-6-0.png)



















28.75


27.25

|Col1|2<br>KSA GRID – 20%<br>4<br>1<br>WIND<br>8<br>WIND L12 10<br>9<br>L13 5<br>7 3<br>6<br>CSP<br>DESAL<br>DEMAND|Col3|
|---|---|---|
|.|35<br>35.5<br>36<br>36.5<br>||



Longitude


Supplementary Figure 6: Transmission network and net power flows for the Kingdom of Saudi Arabia (KSA) Grid
case for 20% power consumption from desalination. Transmission lines are built to connect with the KSA grid to
obtain power. Wind plants are installed in regions 8 and 9, whereas concentrated solar power (CSP) and
desalination facilities are located in region 7.


7


29.25


28.75


28.25


28.75



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-7-0.png)















27.25

|Col1|2<br>PV ONLY – 20%<br>4<br>1<br>8<br>10<br>9<br>5<br>7 3<br>6<br>PV<br>DESAL<br>DEMAND<br>BATTERY|Col3|
|---|---|---|
|.|35<br>35.5<br>36<br>36.5<br>||



Longitude


Supplementary Figure 7: Transmission network and net power flows for photovoltaics (PV) Only case for 20%
power consumption from desalination. Solar availability is relatively constant throughout Neom; therefore,
building a large enough PV farm in node 7, where demand is located, is most cost effective.


8


29.25


28.75


28.25



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-8-0.png)













28.75


27.25

|Col1|2<br>L5<br>L1 WIND<br>WIND ONLY – 20%<br>4<br>BATTERY<br>DESAL<br>1<br>L3<br>WIND<br>BATTERY<br>8<br>WIND L12 10<br>9<br>L13 5<br>BATTERY<br>7 3<br>6<br>BATTERY<br>DEMAND|Col3|
|---|---|---|
|.|35<br>35.5<br>36<br>36.5<br>||



Longitude


Supplementary Figure 8: Transmission network and net power flows for the Wind Only case with 20% power
consumption from desalination. Large wind resources in nodes 4, 8 and 9 are coupled with batteries to supply
sufficient power to meet desalination power demand and baseline power demand.


9


29.25


28.75


28.25



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-9-0.png)























28.75


27.25

|Col1|2<br>BASE – 4%<br>4<br>1<br>8<br>WIND 10<br>9 CSP<br>L13 5<br>DESAL<br>7 3<br>6<br>CSP<br>DESAL<br>DEMAND|Col3|
|---|---|---|
|.|35<br>35.5<br>36<br>36.5<br>||



Longitude


Supplementary Figure 9: Transmission network and net power flows for Base case for 4% power consumption
from desalination. A two-node system where desalination demand is met by entirely by renewable power. Only
wind and concentrated solar power (CSP) technologies are built to meet power demand.


10


29.25


28.75


28.25



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-10-0.png)



















28.75


27.25

|Col1|2<br>KSA GRID – 4%<br>4<br>1<br>WIND<br>8<br>WIND L12 10<br>9<br>L13 5<br>7 3<br>6<br>CSP<br>DESAL<br>DEMAND|Col3|
|---|---|---|
|.|35<br>35.5<br>36<br>36.5<br>||



Longitude


Supplementary Figure 10: Transmission network and net power flows for the Kingdom of Saudi Arabia (KSA)
Grid case for 4% power consumption from desalination. Transmission lines connect Neom to the KSA grid at
node 10, in order to meet power demand. Wind facilities are located in nodes 8 and 9, whereas concentrated solar
power (CSP) and desalination plants are located in node 7.


11


29.25


28.75


28.25


28.75



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-11-0.png)

















27.25

|Col1|2<br>PV ONLY – 4%<br>4<br>1<br>8<br>10<br>9<br>5<br>7 3<br>6<br>PV<br>DESAL<br>DEMAND<br>BATTERY|Col3|
|---|---|---|
|.|35<br>35.5<br>36<br>36.5<br>||



Longitude


Supplementary Figure 11: Transmission network and net power flows for the photovoltaics (PV) Only case for
4% power consumption from desalination. Solar power resources are constant throughout Neom, so an isolated
system is created at node 7, where there is power demand.


12


29.25


28.75


28.25



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-12-0.png)













28.75


27.25

|Col1|2<br>L5<br>L1 WIND<br>WIND ONLY – 4%<br>4<br>BATTERY<br>DESAL<br>1<br>L4 WIND<br>BATTERY<br>8<br>WIND L12 10<br>9<br>L13 5<br>BATTERY<br>7 3<br>6<br>BATTERY<br>DEMAND|Col3|
|---|---|---|
|.|35<br>35.5<br>36<br>36.5<br>||



Longitude


Supplementary Figure 12: Transmission network and net power flows for the Wind Only case with 4% power
consumption from desalination. Good wind resources are scattered throughout the kingdom. Wind farms are built
at node 4 far away from the baseline demand. Desalination plants in node 1 are powered by winds farms and
battery storage.


13


a) Base



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-13-0.png)

**Power**

**Capacity**

**[GW]**











b) KSA Grid







![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-13-1.png)

**Power**

**Capacity**

**[GW]**











c) PV Only







![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-13-2.png)

**Power**

**Capacity**

**[GW]**











d) Wind Only



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-13-4.png)

**Power**

**Capacity**

**[GW]**





150 **Storage**
**Capacity**

**[GWh]**






|0|Col2|
|---|---|
|0<br>|0<br>|


|Col1|Col2|Col3|
|---|---|---|
||||



Supplementary Figure 13: Power generating capacities for all cases when desalination accounts for
only 4% of total power consumption. Plotted are curves for concentrated solar power (CSP),



14



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-13-6.png)
a) Base



**Power**

**[GW]**



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-14-0.png)





b) KSA Grid



**Power**

**[GW]**



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-14-1.png)





c) PV Only



**Power**

**[GW]**



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-14-2.png)





d) Wind Only



**��������**


**��������**


**��������**


**��������**



**Power**

**[GW]**



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/rieraSimulatedCooptimizationRenewable2022/rieraSimulatedCooptimizationRenewable2022.pdf-14-4.png)





Supplementary Figure 14: Power consumption for the Base, Kingdom of Saudi Arabia (KSA) Grid,
Photovoltaics (PV) Only, and Wind Only cases. Desalination accounts for 4% total power consumption.
In all cases the peak power consumed is lower than in the co-optimized strategy. The Wind Only case
shows the largest peak reduction during the summer months compared to all other cases.



15


**Supplementary Tables**


Supplementary Table 1: Investment and Operating Cost of Power Generating and Storage Technologies. Provided are estimates for combined cycle
gas turbine (CCGT), concentrated solar power (CSP), photovoltaics (PV), wind, battery, and hydroelectric pump storage (HPS) technologies.
Technology Costs 2020 2021 2022 2023 2024 2025 2026 2027 2028 2029


AIC [$/kW] 97.93 96.95 95.99 95.03 94.08 93.13 92.2 91.28 90.37 89.46
CCGT
VOC [$/MWh] 3.61 3.61 3.61 3.61 3.61 3.61 3.61 3.61 3.61 3.61


AIC [$/kW] 381.16 377.35 373.57 369.84 366.14 362.48 358.85 355.26 351.71 348.19
CSP
VOC [$/MWh] 3.5 3.5 3.5 3.5 3.5 3.5 3.5 3.5 3.5 3.5


AIC [$/kW] 167.03 165.36 163.71 162.07 160.45 158.84 157.25 155.68 154.13 152.58
PV
VOC [$/MWh] 0 0 0 0 0 0 0 0 0 0


AIC [$/kW] 159.2 157.61 156.04 154.48 152.93 151.4 149.89 148.39 146.9 145.44
Wind
VOC [$/MWh] 0 0 0 0 0 0 0 0 0 0


AIC [$/kW] 47.69 47.21 46.74 46.27 45.81 45.35 44.9 44.45 44.01 43.57
Battery
VOC [$/MWh] 7.26 7.26 7.26 7.26 7.26 7.26 7.26 7.26 7.26 7.26


AIC [$/kW] 453.37 448.84 444.35 439.91 435.51 431.15 426.84 422.57 418.35 414.16
HPS
VOC [$/MWh] 0.51 0.51 0.51 0.51 0.51 0.51 0.51 0.51 0.51 0.51


AIC: annualized investment cost; VOC: variable operating cost



16


Supplementary Table 2: Investment and Operating Costs of Water System Technologies. Provided are estimates for reverse osmosis (RO) and tank storage
technologies.
Technology Costs 2020 2021 2022 2023 2024 2025 2026 2027 2028 2029


AIC [$/(m [3] /hr)] 2396.68 2372.71 2348.98 2325.49 2302.24 2279.22 2256.42 2233.86 2211.52 2189.41
RO
VOC [$/m [3] ] 0.5 0.5 0.5 0.5 0.5 0.5 0.5 0.5 0.5 0.5



Tank

Storage



AIC [$/(m [3] /hr)] 6.4 6.33 6.27 6.21 6.14 6.08 6.02 5.96 5.9 5.84

VOC [$/m [3] ] 0.02 0.02 0.02 0.02 0.02 0.02 0.02 0.02 0.02 0.02



AIC: annualized investment cost; VOC: variable operating cost



17


Supplementary Table 3: Transmission Line Technical
Parameters


Line Corridor Length [km]


! ! 1-2 64


! " 1-7 95


! # 1-8 42


! $ 1-9 63


! % 2-5 59


! & 3-5 28


! ' 3-6 36


! ( 4-8 76


! ) 4-10 115


! !* 5-10 48


! !! 6-7 41


! !" 7-8 59


! !# 7-9 69


! !$ 8-10 143



18


Supplementary Table 4: Annualized Cost of Prospective Transmission Lines


Line 2020 2021 2022 2023 2024 2025 2026 2027 2028 2029


! ! 128.08 126.80 125.54 124.28 123.04 121.81 120.59 119.38 118.19 117.01


! " 189.49 187.60 185.72 183.86 182.03 180.21 178.40 176.62 174.85 173.11


! # 83.89 83.05 82.22 81.40 80.58 79.78 78.98 78.19 77.41 76.63


! $ 127.15 125.88 124.62 123.37 122.14 120.92 119.71 118.51 117.33 116.15


! % 117.46 116.28 115.12 113.97 112.83 111.70 110.58 109.48 108.38 107.30


! & 56.09 55.53 54.98 54.43 53.88 53.34 52.81 52.28 51.76 51.24


! ' 72.70 71.97 71.25 70.54 69.84 69.14 68.45 67.76 67.08 66.41


! ( 151.90 150.38 148.87 147.38 145.91 144.45 143.01 141.58 140.16 138.76


! ) 230.91 228.60 226.31 224.05 221.81 219.59 217.40 215.22 213.07 210.94


! !* 95.35 94.39 93.45 92.51 91.59 90.67 89.77 88.87 87.98 87.10


! !! 82.26 81.44 80.63 79.82 79.02 78.23 77.45 76.67 75.91 75.15


! !" 118.44 117.25 116.08 114.92 113.77 112.63 111.51 110.39 109.29 108.20


! !# 139.27 137.88 136.50 135.13 133.78 132.44 131.12 129.81 128.51 127.23


! !$ 288.37 285.48 282.63 279.80 277.00 274.23 271.49 268.78 266.09 263.43



19


Supplementary Table 5: Wind Turbine Parameters [7]

Parameter Description Value Units


" [+,-./01] Rated Power 4.2 MW


# [+,23] Cut-in wind speed 3 m/s


# [+,-./01] Rated wind speed 12.5 m/s


# [+,45/] Cut out wind speed 22.5 m/s


$ Rotor Diameter 150 m


Supplementary Table 6: Photovoltaic Cell Parameters

Parameter Description Value Units


"%" [-06] Rated Maximum Power 0.25 kW


& [78] Power Temperature coefficient 3 %/°C

'()* Nominal operating cell temperature 41.5 °C
 - [-06] Reference temperature 25 °C


Supplementary Table 7: Expected Value of Information and Value of Stochastic Solution. Shown are results for the
Base, Kingdom of Saudi Arabia (KSA) Grid, Wind Only, and Photovoltaics (PV) Only cases.
SP [M$] WS [M$] EVRS [M$] EVPI [M$] VSS [M$]


Base 4,608 4,185 4,724 423 115

KSA Grid 4,204 4,009 4,289 195 85


Wind Only 7,397 6,202 7,509 1,195 111

PV Only 16,803 13,412 16,864 3,391 60

SP: stochastic problem; WS: wait and see; EVRS: expected value of the reference scenario; EVPI: expected
value of perfect information; VSS: value of the stochastic solution



20


Supplementary Table 8: Power sector generating capacity investments for 20% power consumption from
desalination. Shown are results for the Base, Kingdom of Saudi Arabia (KSA) Grid, Wind Only, and Photovoltaics

             (PV) Only cases. Co optimization reduces the total necessary power generating capacity in all cases.

Generation [GW]



Power

Sector



% Change
CCGT CSP PV Wind Total

of Total


Independent/Co-Optimization



Base 0 / 0 4.2 / 3.9 0 / 0 1.2 / 1.4 5.4 / 5.2 -3.0

KSA Grid 0 / 0 2.1 / 2.1 0 / 0 2.33 / 2.3 4.4 / 4.4 -1.0

Wind Only 0 / 0 0 / 0 0 / 0 12.2 / 12.1 12.2 / 12.1 -1.4

PV Only 0 / 0 0 / 0 42.4 / 40.4 0 / 0 42.4 / 40.4 -4.7


CCGT: Combined-cycle gas turbine; CSP: Concentrated solar power


Supplementary Table 9: Power sector energy storage capacity investments for 20% power consumption from
desalination. Shown are results for the Base, Kingdom of Saudi Arabia (KSA) Grid, Wind Only, and Photovoltaics
(PV) Only cases. Co-optimization enables a reduction in total power storage capacity needed to meet power
demand for all cases.


Storage [GWh]



Power

Sector



% Change
Batteries HPS TES Total

of Total


Independent/Co-Optimization



Base 0 / 0 0 / 0 49.9 / 46.5 49.9 / 46.5 -6.9

KSA Grid 0 / 0 0 / 0 25.4 / 25.6 25.4 / 25.6 -0.7

Wind Only 70.9 / 58.5 0 / 0 0 / 0 70.9 / 58.5 -17.5

PV Only 153.0 / 140.0 0 / 0 0 / 0 153.0 / 140.0 -8.5


HPS: Hydroelectric pump storage; TES: Thermal energy storage



21


Supplementary Table 10: Water sector capacity investments for 20% power consumption from desalination. Shown are results for the Base, Kingdom
of Saudi Arabia (KSA) Grid, Wind Only, and Photovoltaics (PV) Only cases. Investment capacities for reverse osmosis (RO) and water tanks increase

                   to supply for all cases when co optimizing the power and water sectors.

Production [1000 m [3] /h] Storage [1000 m [3] ]



Water Sector



of Total of Total


Independent/Co-Optimization Independent/Co-Optimization



% Change
RO Total



% Change % Change

Water Tanks Total
of Total of Total



Base 147.2 / 148.3 147.2 / 148.3 0.7 13.5
3367. 4 / 3,821.0 3367. 4 / 3,821.0

KSA Grid 147.2 / 154.6 147.2 / 154.6 5.0 3367. 4 / 3,492.5 3367. 4 / 3,492.5 3.7


Wind Only 147.2 /158.9 147.2 /158.9 7.9 13.5
3367. 4 / 3,821.0 3367. 4 / 3,821.0

PV Only 147.2 / 207.5 147.2 / 207.5 40.9 3367. 4 / 3,496.4 3367. 4 / 3,496.4 3.8


Supplementary Table 11: Power sector generating capacity investments for 4% power consumption from
desalination. Shown are results for the Base, Kingdom of Saudi Arabia (KSA) Grid, Wind Only, and
Photovoltaics (PV) Only cases. In the Wind Only and Photovoltaics Only cases, co-optimization reduces the total
necessary power generating capacity for cases without dispatchable generators.

Generation [GW]



Power

Sector



% Change
CCGT CSP PV Wind Total

of Total


Independent/Co-Optimization



Base 0 / 0 4.2 / 4.1 0 / 0 1.2 / 1.3 5.40 / 5.40 0

KSA Grid 0 / 0 2.1 / 2.0 0 / 0 2.3 / 2.4 4.4 / 4.4 0

Wind Only 0 / 0 0 / 0 0 / 0 12.3 / 12.4 12.3 / 12.4 -0.8

PV Only 0 / 0 0 / 0 42.4 / 41.8 0 / 0 42.4 / 41.8 -1.4


CCGT: Combined-cycle gas turbine; CSP: Concentrated solar power



22


Supplementary Table 12: Power sector energy storage capacity investments for 4% power consumption from
desalination. Shown are results for the Base, Kingdom of Saudi Arabia (KSA) Grid, Wind Only, and
Photovoltaics (PV) Only cases. Co-optimization enables a reduction in total storage capacity needed to meet
power demand for all cases.

Storage [GWh]



Power

Sector



% Change
Batteries HPS TES Total

of Total


Independent/Co-Optimization



Base 0 / 0 0 / 0 49.9 / 48.9 0 -2.0

KSA Grid 0 / 0 0 / 0 25.4 / 24.4 25.4 / 24.4 -3.9

Wind Only 70.9 / 68.2 0 / 0 0 / 0 70.9 / 68.2 -3.8

PV Only 153.0 / 149.8 0 / 0 0 / 0 153.0 / 149.8 -2.1


HPS: Hydroelectric pump storage; TES: Thermal energy storage


Supplementary Table 13: Water sector capacity investments for 4% power consumption from desalination. Shown are results for the Base, Kingdom
of Saudi Arabia (KSA) Grid, Wind Only, and Photovoltaics (PV) Only cases. Investment capacities for reverse osmosis (RO) and water tanks increase

                   to supply for all cases when co optimizing the power and water sectors. An exception is RO for the Base case which exhibits a small decrease.

Production [1000 m [3] /h] Storage [1000 m [3] ]



Water Sector



of Total of Total


Independent/Co-Optimization Independent/Co-Optimization



% Change
RO Total



% Change % Change

Water Tanks Total
of Total of Total



Base 39.5 / 39.0 39.5 / 39.0 -1.3 894.5 / 932.4 894.5 / 932.4 4.2


KSA Grid 39.5 / 41.0 39.5 / 41.0 3.8 894.5 / 931.1 894.5 / 931.1 4.1


Wind Only 39.5 / 44.0 39.5 / 44.0 11.4 894.5 / 1,018.7 894.5 / 1,018.7 13.9


PV Only 39.5 / 44.5 39.5 / 44.5 12.7 894.5 / 931.1 894.5 / 931.1 4.1



23


Supplementary Table 14: Power sector cost measures for all cases when desalination accounts for 4% of the total
power consumption. Shown are results for the Base, Kingdom of Saudi Arabia (KSA) Grid, Wind Only, and
Photovoltaics (PV) Only cases. For the power sector, all systems experience a decrease in total cost due to
decreased investment spending. However, systems lacking dispatchable power generators see the biggest
savings.



Total cost



% Change of



Investment cost



Operating cost



Power

Sector




[M$]




[M$] [M$] Total Cost


Independent/Co-Optimization




[M$]




[M$]



Base 1,705/1,688 299/283 2,004/1,971 -1.6

KSA Grid 1,146/1,132 359/ 360 1,505/1,492 -0.9

Wind Only 5,088/4,939 149/147 5,236/5,086 -2.9

PV Only 13,766/13,539 1,094/1,090 14,860/14,629 -1.6


Supplementary Table 15: Water sector cost measures for all cases when desalination accounts for 4% of the total
power consumption. Shown are results for the Base, Kingdom of Saudi Arabia (KSA) Grid, Wind Only, and
Photovoltaics (PV) Only cases. For the water sector, an increase in total costs resulting from higher investment
spending is particularly evident in the PV Only and Wind Only cases.



Total cost



% Change of



Investment cost



Operating cost



Water

Sector




[M$]




[M$] [M$] Total Cost


Independent/Co-Optimization




[M$]




[M$]



Base 97/96 633/634 730/730 0.0

KSA Grid 97/100 633/631 730/731 0.1

Wind Only 97/108 633/628 730/736 0.8

PV Only 97/108 633/630 730/738 1.1



24


Supplementary Table 16: System cost measures for all cases when desalination accounts for 4% of the total power
consumption. Shown are results for the Base, Kingdom of Saudi Arabia (KSA) Grid, Wind Only, and Photovoltaics

                                     (PV) Only cases. System costs decrease for all cases when co optimizing the power and water sectors.



Total cost



% Change of



Investment cost



Operating cost




[M$] [M$] Total Cost


Independent/Co-Optimization




[M$]




[M$]



System




[M$]



Base 1,802 / 1,784 932 / 917 2,734 / 2,701 -1.2

KSA Grid 1,243 / 1,232 992 / 991 2,235 / 2,223 -0.5

Wind Only 5,185 / 5,047 782 / 775 5,966 / 5,822 -2.4

PV Only 13,863 / 13,647 1,727 / 1,720 15,590 / 15,367 -1.4


Supplementary Table 17: Levelized cost of electricity (LCOE) and levelized cost of water
(LCOW) for the Base, Kingdom of Saudi Arabia (KSA) Grid, Wind Only, and Photovoltaics
(PV) only cases. Desalination accounts for 4% of the total power consumption.



LCOE

[$/MWh]



LCOW




[$/MWh] [$/m [3] ]

Independent/Co- Independent/Co


Independent/Co- Independent/Co
% Change
Optimization Optimization



Independent/Co- Independent/Co- %

% Change
Optimization Optimization Change


Base 17.95/17.86 -1 0.61/0.61 0

KSA Grid 13.34/13.25 -1 0.61/0.61 0

Wind Only 134.07/132.85 -1 0.61/0.62 2

PV Only 47.90/47.11 -2 0.61/0.62 2



Optimization



25


Supplementary Table 18: Computation performance for two stochastic programming problems
with a time horizon of ten years using: 1) representative periods; and 2) full-time resolution.
Using representative periods, the model demonstrates greater computational efficiency than
solving a full resolution model.
Representative

Full-time resolution
periods

Computation time [h] 6.54 336
Optimality gap [%] 0 43.2


Supplementary Table 19: Results for two stochastic programming problems with a time horizon of
one year using: 1) representative periods; and 2) full time resolution.



Representative

periods



Full time
Increment (%)
resolution



Investment capacity


CSP [GW] 3.91 4.11 5.12

PV [GW] 0 0 0

Wind [GW] 1.36 1.39 2.21

CCGT [GW] 0 0 0

Battery [GWh] 0 0 0

HPS [GWh] 0 0 0

TES [GWh] 46.95 49.39 5.20


Computational results


Computation time [h] 0.31 0.91 193.5

Optimality gap [%] 0 0 0


CSP: Concentrated solar power; PV: Photovoltaics; CCGT: Combined cycle gas turbine; HPS:
Hydroelectric pump storage; TES: Thermal energy storage



26


Supplementary Table 20: Results for two deterministic problems with a time horizon of one year
using: 1) representative periods; and 2) full time resolution.



Representative

periods



Full time
Increment (%)
resolution



Investment capacity
CSP [GW] 3.00 3.09 3.00
PV [GW] 0 0 0
Wind [GW] 1.22 1.24 1.64
CCGT [GW] 0 0 0

Battery [GWh] 0 0 0
HPS [GWh] 0 0 0
TES [GWh] 35.95 36.99 2.89


Computational results


Computation time [h] 0.06 0.17 167

Optimality gap [%] 0 0 0


CSP: Concentrated solar power; PV: Photovoltaics; CCGT: combined cycle gas turbine; HPS:
Hydroelectric pump storage; TES: Thermal energy storage



27


**Supplementary Notes.**

**Supplementary note 1: Annualized Cost Calculations**


Annualized cost is a metric that allows decision-makers to compare the cost-effectiveness of
technologies with different lifespans. The annualized investment cost (AIC) of technology !,
in year ", is a function of that year’s overnight capital cost, $%% !,#, and the technology’s
capital recovery factor, %&' ! :

()% !,# = $%% !,# %&' ! _._ (1)


The capital recovery factor is the net present value of payments over a fixed period – in this
case, the lifespan, +,+ !, of the technology. It is calculated using,



%&' ! =



$(&'$) [!"!#]

(2)
(&'$) [!"!#] )&



where _r_ is the nominal interest rate.

Overnight investment and operating costs power generating technologies excluding CSP
were obtained from the U.S. Energy Information Administration website. [1] For CSP and HPS,
cost values were obtained from the National Renewable Energy Laboratory. [2] Overnight
investment and operating costs power for desalination was obtained from Caldera et al.
2017. [3]

We present the annualized investment costs of the power generating and energy storage
technologies in Supplementary Table 1. Supplementary Table 2 includes the annualized
investment costs of the water systems technologies. Supplementary Table 4 includes the
annualized investment cost of the prospective transmission lines.


28


**Supplementary note 2:** _**k-**_ **means Clustering Framework**
The goal of _k_ -means clustering is to select _k_ number of representative periods/regions that
minimizes the sum of squared distance between the rows, - !, of matrix . [/] and their cluster
centroid1 - :


2

2 [+,-] = min [∑] *∈1 ∑ !∈. 7|9 ! −; - |7 _,_ (3)



*∈1 ∑ !∈. 7|9 ! −;    - |7
. $ …. % [∑]



2
_,_ (3)



where < is a set of _k_ clusters and = is a set of _k_ centroids. The _k_ -means approach most
commonly used is Lloyd’s algorithm [4], which begins by selecting _k_ arbitrary centroids, chosen
uniformly at random from the normalized dataset, . [/] . Each row is assigned to the nearest
centroid, determined by calculating the Euclidean distance between a given row to the _k_
possible centroids. This step forms the initial clusters. The centroid is then recalculated for
each cluster, taking the mean of all data points in the cluster.

The assignment and recalculation steps are repeated until the total distance, 2 [+,-], converges.
However, despite its simplicity, the disadvantage of Lloyd’s algorithm is that it is not always
accurate and may generate inappropriate clusters. [5] The initialization of the centroids has a high
impact on finding optimal clusters and centroids.

Arthur & Vassilvitskii [6] proposed an alternative, _k_ -means++, that involved initializing Lloyd’s
_k_ -means algorithm with random starting centers with specific probabilities proportional to their
contribution to the overall potential. The first centroid, 1 &, is selected uniformly from the
dataset. [/] . The next center, 1 2, is chosen at random from, . [/], where 1 2 = - [3] ∈. [/] with a



probability of



456 [&] 7 [']

. Here ?(-) [2] denotes the shortest distance between row - to the closest
∑ (∈* [+] 4(6) [']



center we have. This is repeated until all _k_ centroids are selected. The remainder of Lloyd’s _k_ means algorithm (reassignment and recalculation) is subsequently performed.


29


**Supplementary note 3: Selection of representative regions**
Spatial clustering is used to delineate regions with similar climate conditions: wind speed, DNI,
and temperature. The dataset comprises 4270 (number of location) time series, each 11.5 years
long for each of the 3threeattributes. The spatial resolution is 5 km. The original data set was
a 3-dimensional, 4270 locations × 100,656 hours × 3 attributes matrix. It was reshaped as:

B [9+:-] = [B ;9 B < B - ] _,_ (4)



a 2-dimensional 4270 locations × M (302184), where _m_ is composed of 100,656 hours for each
of the 3 attributes.

For any given location, the time series corresponds to the values of the attributes throughout
the 100,656 hours. This ensures that clustered locations have similar patterns for all attributes.
Each attribute group is normalized using the z-scoring full scope normalization to produce a
normalized matrix,


& & &

B [/] [9+:-] = ~~E~~ =,- [(B] [;9] [ −F] [;9] [)] = . [(B] [<] [ −F] [<] [)] = / [(B] [-] [ −F] [-] [)G] _[. ]_ (5)



& &

=,- [(B] [;9] [ −F] [;9] [)] =



&



& &

= . [(B] [<] [ −F] [<] [)] =



&

= / [(B] [-] [ −F] [-] [)G] _[. ]_ (5)



To ensure optimal clustering and reduce the value of the potential equation, the k-means++
method, for a given _k_, is initialized with 1000 random starting points. The clusters of the
replicate with the best sum of the distances are selected as the optimal clusters. Given the nature
of the dataset, clusters may not be continuous spatially, as seen in Supplementary Figure 1a.
Points that are discontinuous or “wandering” within other clusters are manually reclassified.
The distance between cluster centroid is calculated with,



2



<!9
!,> = H7; ! −; - 7H



%
!,>



_,_ (6)



to determine which regions are most similar.

Wandering points surrounded entirely by a cluster get reassigned to that cluster. A wandering
point between two or more clusters is reassigned to the cluster with the shortest distance to its
original assignment. The centroids of all clusters are subsequently recalculated. Supplementary
Figure 1b shows the cluster distribution after reassignment takes place.

For spatial clustering, the cluster centroids are chosen as the time series for the representative
regions. The centroids are merged into a, 10 locations × 302184 (100,656 hours × 3 attributes)
matrix and reshaped into a three dimensional, 10 locations × 100,656 hours × 3 attributes
matrix that is used for temporal clustering.


30


**Supplementary note 4: Selection of representative days**
For representative periods, we consider the attributes from the spatial clustering (wind speed,
DNI, and temperature) and water demand and power load. In GEP models, we would ideally
know the power and water demand of the original locations and subsequently include these
attributes as part of the spatial clustering procedure. However, when only total demands are
known, they are excluded from spatial clustering.

Once the representative regions are obtained, they are assigned power and water demands
based on knowledge of what will/does exist in those regions (e.g., residences, industry, mixed,
greenfield). The matrix from the spatial cluster is merged with a, 10 locations × 100,656 hours
× 2, matrix containing the demand and water profiles for the regions. This 3-dimensional,
100,656 hours × l0 locations × 5 attributes matrix is reshaped as:

B [/] [-?@+] = [B ;9 B < B - B ;- B ? ] _,_ (7)


a 2-dimensional, 4194 days ×M (1200) matrix, where M contains the hours per day (24),
locations (10), and attributes (5).

For a given day, the signal pattern corresponds to the value of the attributes throughout the day
for all locations. This was done to ensure that the selected representative days best capture the
spatial and temporal variability between all attributes together. Each attribute group is
normalized using the z-scoring full scope normalization scope us to produce the normalized
matrix,



% %

& !" [(!] ['(] [ −'] ['(] [)] &



% %

& # [(!] [)] [ −'] [)] [)] &



% %

& $ [(!] [!] [ −'] [!] [)] &



% %

& !$ [(!] ['!] [ −'] ['!] [)] &



%

& % [(!] ["] [ −'] ["] [))] _[.  ]_ [(] [8)]



! ["] [!"#$] =
~~$~~



%



To ensure optimal clustering and reduce the value of the potential equation, the _k-_ means++
method for a given _k_ is initialized with 1000 random starting points. The clusters of the replicate
with the best sum of the distances are selected as the optimal clusters.

For visual simplicity, Supplementary Figure 2 shows the _k_ -means clustering output of the
temperature portion of the dataset. In this case, _k_ = 5.

In the representation stage, representative days that minimize the Euclidean distance to its
corresponding cluster centroid are selected. Weights of the representative days are calculated
by dividing the number of days in their associated cluster by the number of days in the entire
dataset.


31


**Supplementary note 5: Renewable Power Capacity Factors**
**Wind Power**
The electrical power generated by a wind turbine is a function of the power produced by the
wind upon entering the turbine itself and a power coefficient at the given wind speed. We
present the power outputs of the Vestas 150-4.2MW turbine. The wind turbine parameters are
shown in Supplementary Table 5.

The power coefficients (%I), provided by the turbine manufacturer at varying winds speeds,
are a measure of efficiency represented as the product of turbine efficiency, the efficiency of
the shaft bearings and gears, and the generator efficiency. We fit the given %I values to an
exponential curve:



C!D& J ! K ) ~~A~~ 0123# # B ' _,_ (9)



3#



012#



B



%I = ∑ !D& J ! K



)
~~A~~



where, J !, L !, M ! are the coefficient of the regression, and u is the wind speed.

The power entering the wind turbine,



E,!F

I - =



G [4#5] H [6,78/] I /9



/

_,_ (10)
2



9



is a function of air density _,_ N [:!$], times the swept area of the turbine blade, ( [J;?+-], and the
wind speed, O, cubed. Air density generally varies according to pressure and temperature;
however, we assume it to be constant at 1.225 kg/m [3] . The area swept by the turbine is given
by:



( [J;?+-] =



KL [']

_[ . ]_ (11)
M



The electrical power produced by the turbine,


E,,I- E,!F

I - = %I - I - _,_



E,!F

- _,_ (12)



E,,I
- = %I - I 


is obtained by multiplying the power coefficients by the power entering the turbine.

To obtain the capacity factor of the wind turbine,



:,<0/



' -E =



/ (13)

N [:,54/7.] ~~_[,]_~~



+ /



we divide power output by the rated power capacity.

**PV Solar Power**

The electrical power produced by a PV panel at a given hour, I 


NO

The electrical power produced by a PV panel at a given hour, I -, is a function of temperature,

, [P?QQ], and solar irradiance, ?P) -, received by the cell [8] :

I -NO = QRQ $?# [4RS] [/] P?QQ −, $?# )] _._ (14)

[[1 + U] [NO] [(,] [-]



NO = QRQ $?# [4RS] [/]



P?QQ −, $?# )] _._ (14)



&TTT [/] [[1 + U] [NO] [(,] [-]



The temperature of the cell depends on the ambient temperature,, -:@U, and the solar

irradiance:


32


P?QQ =, 


RVWX)2T

?P)    - _._ (15)
YTT



, 


:@U +



The photovoltaic capacity factor, ' -NO, is calculated using the following equation,



=>



' -NO =



/

_[. ]_ (16)
NZN [57?]



+ /



The solar panel parameters are shown in Supplementary Table 6.



33


**Supplementary note 6: Mathematical Formulation of the Stochastic Programming Model**
**Nomenclature**

**Indices**
_b_ Battery farms
_c_ CSP farms
_f_ Years
_h_ HPS plants
_j_ CCGT plant
_k_ Water tank units

_l_ Transmission lines
_lf(f)_ Last year
_lo(o)_ Last day
_lt(t)_ Last hour
_n_ Nodes
_o_ Representative days
_rl(l)_ Receiving-end node of transmission line V
_s_ PV solar farms

_sl(l)_ Sending-end node of transmission line V
_t_ Hours

_w_ Wind farms
_z_ RO desalination plants
W Scenarios

**Sets**
Ω [[] Set of battery plants
Ω F [[] Set of battery plants located at node Y
Ω [W] Set of CSP farms
Ω WF Set of CSP farms located at node Y



Ω WF Set of CSP farms located at node Y

Ω [\] Set of HPS plants
Ω \F Set of HPS plants located at node Y



Ω \F Set of HPS plants located at node Y

Ω []] Set of CCGT plants

]

Ω F Set of CCGT plants located at node



]

Ω F Set of CCGT plants located at node Y

Ω [^] Set of water tank units
Ω ^F Set of water tank units located at node



Ω ^F Set of water tank units located at node Y

Ω [_] Set of existing transmission lines
Ω [_'] Set of new transmission lines
Ω [N] Set of photovoltaic solar farms
Ω NF Set of photovoltaic solar farms located at node



Ω NF Set of photovoltaic solar farms located at node Y

Ω [E] Set of wind farms
Ω F [E] Set of wind farms located at node Y
Ω [`] Set of RO desalination plants
Ω F` Set of RO desalination plants located at node Y



Ω F` Set of RO desalination plants located at node Y


**Parameters**
( L [`] Conversion factor between water produced and electricity consumed

[MWh/m [3] ]
Z Q Susceptance of transmission line V [S]
% U[ Operating cost of battery plant L [$/MWh]



% U[ Operating cost of battery plant L [$/MWh]

% a\ Operating cost of HPS plant ℎ [$/MWh]



\ Operating cost of HPS plant ℎ [$/MWh]



34


% *^ Operating cost of water tank \ [$/m [3] ]

]

% - Operating cost of CCGT ] [$/MWh]


[̅]


[̅]



% 

[̅]


[̅]



]

% - Operating cost of CCGT ] [$/MWh]

% P [N[] Operating cost of CSP farms M [$/MWh]
% + [NO] Operating cost of PV solar farms I [$/MWh]
% ;E Operating cost of wind farm ^ [$/MWh]


[̅]


[̅]



% ;E Operating cost of wind farm ^ [$/MWh]

^

% - Operating cost of RO desalination plant _


[̅]


[̅]



^

% - Operating cost of RO desalination plant _ [$/m [3] ]

[,!Fb,@:c

` [a]
U,#


[̅]


[̅]




[,!Fb,@:c Maximum capacity that can be built for CSP farm c in year f [MWh]


[̅]


[̅]



_,P:+

' Q Capacity of transmission line V [MW]

' d,+,#,,,-NO Capacity factor of PV solar farm I


[̅]


[̅]



'
Q


[̅]


[̅]



' d,+,#,,,-NO Capacity factor of PV solar farm I for scenario W, year ", day b and hour c [p.u.]

' d,;,#,,,-E Capacity factor of wind farm ^ for scenario W, year ", day b and hour c [p.u.]


[̅]


[̅]



' d,;,#,,,-E Capacity factor of wind farm ^ for scenario W, year ", day b and hour c [p.u.]

d PXeJ,N[ Hours of TES energy storage [hrs]


[̅]


[̅]



d PXeJ,N[ Hours of TES energy storage [hrs]

d a Water head in plant HPS ℎ [m]
) [
U,#


[̅]


[̅]



) U,#[ Annualized investment cost of battery plant b in year f [$/MWh]

) #fe,@:c Annual investment budget for power sector [$]


[̅]


[̅]



) #fe,@:c Annual investment budget for power sector [$]

) #fE,@:c Annual investment budget for water sector[$]


[̅]


[̅]



) #fE,@:c Annual investment budget for water sector[$]

) a,#\ Annualized investment cost of HPS plant ℎ


[̅]


[̅]



) a,#\ Annualized investment cost of HPS plant ℎ in year f [$/MW]

]

) >,# Annualized investment cost of CCGT plant ] in year "


[̅]


[̅]



]

) >,# Annualized investment cost of CCGT plant ] in year " [$/MW]

) *,#^ Annualized investment cost of water tank \ in year " [$/m [3] ]


[̅]


[̅]



) *,#^ Annualized investment cost of water tank \ in year " [$/m [3] ]

) Q,#R Annualized investment cost of prospective transmission line


[̅]


[̅]



) Q,#R Annualized investment cost of prospective transmission line V in year " [$]

) P,#N[ Annualized investment cost of the power block of CSP farm M in year


[̅]


[̅]



) P,#N[ Annualized investment cost of the power block of CSP farm M in year "

[$/MWh]
) +,#NO Annualized investment cost of PV solar farm I in year " [$/MWh]


[̅]


[̅]



) +,#NO Annualized investment cost of PV solar farm I in year " [$/MWh]

) ;,#E Annualized investment cost of wind farm ^ in year " [$/MWh]


[̅]


[̅]



) ;,#E Annualized investment cost of wind farm ^ in year " [$/MWh]

) L,#^ Annualized investment cost of RO desalination plant _


[̅]


[̅]



) L,#^ Annualized investment cost of RO desalination plant _ in year f [$/MWh]

e a+I@+ Power consumption factor [MWh/m [4] ]


[̅]


[̅]



e a+I@+ Power consumption factor [MWh/m [4] ]

e a-I$U!F? Power generation factor [MWh/m [4] ]


[̅]


[̅]



e a-I$U!F? Power generation factor [MWh/m [4] ]

R PJg,N[ Solar multiple of CSP farm M [p.u.]


[̅]


[̅]



R PJg,N[ Solar multiple of CSP farm M [p.u.]

Q d,F,#,,,-4 Electricity demand at node Y


[̅]


[̅]



Q d,F,#,,,-4 Electricity demand at node Y, for scenario W, year ", day b and hour c [MW]

[,!Fb,@:c

Q [a] >,# Maximum capacity that can be built of CCGT plant ] in year " [MW]


[̅]


[̅]




[,!Fb,@:c
Maximum capacity that can be built of CCGT plant ] in year " [MW]


[̅]


[̅]



N[,!Fb,@:c

Q [a] W,# Maximum power block (PB) capacity that can be built for CSP farm M in year M

[MW]

NO,!Fb,@:c

Q [a] +,# Maximum capacity that can be built of PV solar farm I in year " [MW]


[̅]


[̅]



Q [a]
W,#


[̅]


[̅]



NO,!Fb,@:c
Maximum capacity that can be built of PV solar farm I in year " [MW]


[̅]


[̅]



E,!Fb,@:c

Q [a] >,# Maximum capacity that can be built of wind farm ^ in year " [MW]

4

f d,F,#,,,- Water demand at node Y, for scenario W, year ", day b and hour c [m [3] /h]


[̅]


[̅]



Q [a]
>,#


[̅]


[̅]



4

f d,F,#,,,- Water demand at node Y, for scenario W, year ", day b and hour c [m [3] /h]

`,!Fb,@:c

f [a] L,# Maximum capacity that can be built of RO desal plant _ in year " [m [3] /h]


[̅]


[̅]



`,!Fb,@:c

f [a] L,# Maximum capacity that can be built of RO desal plant _ in year " [m [3] /h]

& >4 Ramping-down limit of CCGT ] [MW]


[̅]


[̅]



& >4 Ramping-down limit of CCGT ] [MW]

& >h Ramping-up limit of CCGT ] [MW]

[̅]


[̅]



& >h Ramping-up limit of CCGT ] [MW]

^,!Fb,@:c

g *,# [̅] Maximum capacity that can be built for tank

[̅]




[̅] ^,!Fb,@:c Maximum capacity that can be built for tank \ in year " [m [3] ]


[̅]




[̅]


^,-,-P:+

g # [̅] Maximum total capacity for all tanks in year " [m [3] ]




[̅]


[̅]


\,!Fb,@:c

i [a] a,# Maximum reservoir capacity that can be built for HPS plant ℎ in year " [m [3] ]




[̅]


[̅]


\,!Fb,@!F

i ~~a~~,# Minimum reservoir capacity that can be built for HPS plant ℎ in year " [m [3] ]


35


j, Weight of the representative day b. A given weight equals the number of days
in a year that a representative day represents.
k d Probability of scenario W
l U Minimum energy level coefficient of battery plant L [p.u.]
m U Maximum charge/discharge coefficient of battery plant L [p.u.]

Pa$i

n U Charging efficiency coefficient of battery plant L [p.u.]



Pa$i

n U Charging efficiency coefficient of battery plant L [p.u.]

<Pa$i

n U Discharging efficiency coefficient of battery plant L



<Pa$i

n U Discharging efficiency coefficient of battery plant L [p.u.]

o P Minimum energy level coefficient of TES for CSP farm M [p.u.]
p P Power block efficiency coefficient of CSP farm M [p.u.]
F a Minimum water level coefficient of reservoir ℎ [p.u.]
q a Maximum pump/turbine coefficient of HPS plant ℎ [p.u.]
r U+I@+ Pumping efficiency coefficient of HPS plant L [p.u.]



r U+I@+ Pumping efficiency coefficient of HPS plant L [p.u.]

r U-I$U!F? Turbining efficiency coefficient of HPS plant L [p.u.]



r U-I$U!F? Turbining efficiency coefficient of HPS plant L [p.u.]

s - Minimum power output coefficient of CCGT plant ] [p.u.]
N - Minimum stored water level coefficient of water tank \ [p.u.]
t - Maximum inflow/outflow coefficient of water tank unit \ [p.u.]
2 L Minimum water output coefficient of desalination plant _ [p.u.]
u L Total renewable capacity coefficient [p.u.]

**Binary Variables**
J Q,# Equal to 1 after transmission line V is built in year ", and 0 otherwise
L Q,# Equal to 1 if transmission line V is built in year ", and 0 otherwise
J d,>,#,,,- Equal to 1 if CCGT unit ] is online for scenario W, year ", day b, and hour c,
and 0 otherwise

**Deterministic Continuous Variables**


[,P:+

` [,# Cumulative built capacity of battery unit L in year " [MWh]




[,P:+ Cumulative built capacity of battery unit L in year " [MWh]




[,!Fb

` [,# Capacity battery unit L built in year " [MWh]



],P:+

Q >,# Cumulative built capacity of CCGT unit ] in year " [MW]



],!Fb

Q >,# Capacity CCGT unit ] built in year " [MW]



N[,P:+

Q P,# Cumulative built of PB for CSP farm ] in year " [MW]



N[,!Fb

Q P,# Capacity of PB for CSP farm M built in year " [MW]



NO,P:+

Q +,# Cumulative built capacity PV farm I in year " [MW]



NO,!Fb

Q +,# Capacity of PV farm I built in year " [MW]



E,P:+

Q ;,# Cumulative built capacity of wind farm ^ in year " [MW]



E,!Fb

Q ;,# Capacity of wind farm I built in year " [MW]



`,P:+

f L,# Cumulative built capacity of RO desalination plant _ in year " [m [3] ]



`,!Fb

f L,# Capacity of RO desalination _ built in year " [m [3] ]



g *,#^,P:+ Cumulative built capacity of water tank \ in year " [m [3] ]



g *,#^,!Fb Capacity of water tank \ built in year " [m [3] ]



\,P:+

i a,# Cumulative reservoir capacity of HPS unit ℎ in year " [m [3] ]



\,!Fb

i a,# Capacity of HPS unit ℎ in year " [m [3] ]



36


**Stochastic Continuous Variables**
` d,U,#,,,-[ Energy level of battery



` d,U,#,,,-[ Energy level of battery L in scenario W, year ", day b, and hour c [MWh]

` d,U,#,,,-XeJ Energy level of TES for CSP farm M in scenario W, year ", day b



` d,U,#,,,-XeJ Energy level of TES for CSP farm M in scenario W, year ", day b, and hour c

[MWh]
' d,Q,#,,,-_ Power flow through transmission line V in scenario W, year ", day b, and hour c



' d,Q,#,,,-_ Power flow through transmission line V in scenario W, year ", day b, and hour c

[MW]

[,Pa$i

I d,U,#,,,- Charging power from battery unit L in scenario W, year ", day b, and hour c




[,Pa$i

I d,U,#,,,- Charging power from battery unit L in scenario W, year ", day b, and hour c

[MW]

[,<Pa$i

I d,U,#,,,- Discharging power from battery unit L in scenario W, year ", day b, and hour c




[,<Pa$i

I d,U,#,,,- Discharging power from battery unit L in scenario W, year ", day b, and hour c

[MW]

WJN,9+!QQ

I d,P,#,,,- Power spilled by CSP solar farm M in scenario W, year ", day b, and hour c [MW]



WJN,9+!QQ

d,P,#,,,- Power spilled by CSP solar farm M in scenario W, year ", day b, and hour c [MW]

\,+I@+ Power consumed by HPS unit ℎ in scenario W, year ", day b, and hour c [MW]



I d,a,#,,,-\,+I@+ Power consumed by HPS unit ℎ in scenario W, year ", day b, and hour c [MW]



I d,a,#,,,-\,-I$U!F? Power produced by HPS unit ℎ in scenario W, year ", day b, and hour c [MW]

I d,>,#,,,-] Power produced by CCGT unit in scenario W, year ", day b, and hour c [MW]

I d,P,#,,,-N[ Power produced by PB of CSP plant M in scenario W, year ", day b, and hour c



\,-I$U!F? Power produced by HPS unit ℎ in scenario W, year ", day b, and hour c [MW]



I d,>,#,,,


I d,P,#,,,-N[ Power produced by PB of CSP plant M in scenario W, year ", day b, and hour c

[MW]

NO,9+!QQ

I d,+,#,,,- Power spilled by PV solar farm I in scenario W, year ", day b, and hour c [MW]



NO,9+!QQ

I d,+,#,,,- Power spilled by PV solar farm I in scenario W, year ", day b, and hour c [MW]

I d,P,#,,,-Jg Power produced by SF of CSP plant M for scenario W, year ", day b, and hour c



I d,P,#,,,-Jg Power produced by SF of CSP plant M for scenario W, year ", day b, and hour c

[MW]

XeJ,!F>

I d,P,#,,,- Power stored in TES of CSP plant M for scenario W, year ", day b, and hour c



XeJ,!F>

I d,P,#,,,- Power stored in TES of CSP plant M for scenario W, year ", day b, and hour c

[MW]
I d,P,#,,,-XeJ,,I- Power withdrawn from TES of CSP plant M for scenario W, year ", day b, and



I d,P,#,,,-XeJ,,I- Power withdrawn from TES of CSP plant M for scenario W, year ", day b, and

hour c [MW]

;!F<,9+!QQ

I d,;,#,,,- Power spilled by wind farm ^ for scenario W, year ", day b, and hour c [MW]

v d,a,#,,,-\,+I@+ Pumped flow of water by HPS unit ℎ for scenario W, year ", day b, and hour c

[MW]
v d,a,#,,,-\,-I$U!F? Turbined flow of water by HPS unit ℎ for scenario W, year ", day b, and hour c



;!F<,9+!QQ
Power spilled by wind farm ^ for scenario W, year ", day b, and hour c [MW]



v d,a,#,,,


v d,a,#,,,-\,-I$U!F? Turbined flow of water by HPS unit ℎ for scenario W, year ", day b, and hour c

[m [3] /h]
v d,*,#,,,-^,!F Inflow of water from water tank \ for scenario W, year ", day b, and hour c



v d,*,#,,,-^,!F Inflow of water from water tank \ for scenario W, year ", day b, and hour c

[m [3] /h]
v d,*,#,,,-^,,I- Outflow of water from water tank \ for scenario W, year ", day b, and hour c



v d,*,#,,,-^,,I- Outflow of water from water tank \ for scenario W, year ", day b, and hour c

[m [3] /h]
v d,L,#,,,-` Water desalinated by plant h for scenario W, year ", day b, and hour c [m [3] /h]



v d,L,#,,,-` Water desalinated by plant h for scenario W, year ", day b, and hour c [m [3] /h]

g d,*,#,,,-^ Water level of tank unit \ for scenario W, year ", day b, and hour c [m [3] ]



g d,*,#,,,-^ Water level of tank unit \ for scenario W, year ", day b, and hour c [m [3] ]

i d,a,#,,,-\ Reservoir level of unit ℎ for scenario W, year ", day b, and hour c [m [3] ]



i d,a,#,,,-\ Reservoir level of unit ℎ for scenario W, year ", day b, and hour c [m [3] ]

^ d,L,#,,,- Product of binary variable (O d,L,#,,,- ) and continuous variable () [MW]
w d,F,#,,,- Voltage angle at node Y for scenario W, year ", day b, and hour c [rad]



37


**Objective Function**



/,+,,,!'.!"- + /()* /,+,,,!'.!"


+

- [∑(&'()*] [+]



/ / ∑∑. +,, ∑ 12!3% (/()* /,+,,,!'.!"- + /()* /,+,,,!'.!"- ) (17)



min




[∑(&'()*] + [+] $,'"- + &'()* +'.!"- ) + ∑- / / ∑∑. +,, ∑ 12!3% (/()* /,+,,,!'.!"


$,'"- + &'()* +



where



+$,'"- = ∑ 4∈7 [&'] 1 4+5



9 9,:;< >9

8∈7 [(] 1 8,+ 3 8,+ + ∑ =∈7 [)] 1 =,+



9 3

8,+ 8,+



>9 >9,:;< @

=∈7 [)] 1 =,+ 4 =,+ + ∑ ?∈7 [*] 1 ?,+



>9 4

=,+ =,+



@ @,:;<

1 4 +

?∈7 [*] ?,+ ?,+



@
4
?,+



&'()* +



4∈7 [&'] 1 4+5 2 4+ + ∑ 8∈7 [(] 1 8,+9



B !C-8:;" B,:;< >D

A∈7 [+] 1 A+ 56 A 7 A 8 A,+ 9 A : + + ∑ $∈7 [,] 1 $,+



B

A+ 56 A



>D >D,:;< E

$∈7 [,] 1 $,+ 4 $,+ + ∑ '∈7 [-] 1 ',+



>D 4

$,+ $,+



E E,:;< (18)

'∈7 [-] 1 4



E 4

',+ ',+



∑ A∈7 [+] 1 A+



!C-8:;"

A 7 A 8 A,+



+'.!"- = ∑ F∈7 [.] 1 F,+G



I I,:;<

H∈7 [/] 1 < (19)



H,+I < H,+



&'()* +



G G,:;< I

F∈7 [.] 1 F,+ ; F,+ + ∑ H∈7 [/] 1 H,+



G ;

F,+ F,+



$,'"- = ∑ 8∈7 [(] = 89



9,=A.-J"
+ 3

/,8,+,,,! /,8,+,,,!



=∈7 [)] = = [>9] ( >9 +



/()* /,+,,,!

∑ A∈7 [+] = AB



B,$C#$ !C-8:;"

/,A,+,,,! + ( /,A,+,,,!



9 9,=A.-J" 9,):(=A.-J" >9

8∈7 [(] = 8 >3 /,8,+,,,! + 3 /,8,+,,,! ? + ∑ =∈7 [)] = = [>9] ( /,=,+,,,!



9 9,=A.-J"

8 >3 /,8,+,,,!



B B,$C#$ !C-8:;" @

A∈7 [+] = A >( /,A,+,,,! + ( /,A,+,,,! ? + ∑ ?∈7 [*] = /,?,+,,,!



B B,$C#$

A >( /,A,+,,,!



@ @,:;< >D

?∈7 [*] = /,?,+,,,! ( ?,+ + ∑ $∈7 [,] = $ [>D] @ /,$,+,,,!



@

/,?,+,,,! ( ?,+



>D >D,=.$

= @ 4 +

$∈7 [,] $ [>D] /,$,+,,,! /,$,+,,,!



>D >D,=.$
4

/,$,+,,,! /,$,+,,,!



(20)



E E,=.$

'∈7 [-] = ' [E] @ 4



E E,=.$
4

/,',+,,,! /,',+,,,!



∑ '∈7 [-] = ' [E] @ /,',+,,,!E



I I,:;<

H∈7 [/] 1 < (21)



'.!"- = ∑ F∈7 [.] = FG



G,:; G,,C!

/,F,+,,,! + A /,F,+,,,!



H,+I < H,+



/()* /,+,,,!



F∈7 [.] = FG 5A /,F,+,,,!G,:; + A /,F,+,,,!G,,C! : + ∑ H∈7 [/] 1 H,+I



G 5A /,F,+,,,!G,:;



@,:;<
, 4
=,+



9,=.$
, 3

8,+ 8,+



KLM,=.$
, 4

=,+ ?,+



>9,=.$
, 4
=,+



@,=.$
, 4
?,+



>9,:;<

,



⎧ ' 4+, 2 4+, H /,?,+,,,!, 3 8,+


>D,=.$ >D,:;< E,=.$

4, 4, 4

⎪ [⎪] $,+ $,+ ',+



9,:;<
, 3

8,+ =,+



E,=.$
, 4
',+



I,=.$

H,+, < H,+



G,=.$
, ;

F,+ F,+



B,=.$
, 8
A,+



E,:;<

',+, < H,+



I,:;<
, ;

H,+ F,+



G,:;<
, 8

F,+ A,+



B,:;<

,



4
$,+



>D,:;<
, 4

$,+ ',+



>D,=.$
, 4

$,+ $,+



( /,?,+,,,!


B,!C-8:;"

A /,A,+,,,!



B,!C-8:;" G,:;

/,A,+,,,!, A /,F,+,,,!



G,,C! I

/,F,+,,,!, A /,H,+,,,!



/,H,+,,,!I, ; /,F,+,,,!G



OM>,($:44 B,$C#$

/,=,+,,,!, ( /,A,+,,,!



(22)



9,=A-J 9,)=A-J

/,8,+,,,!, ( /,8,+,,,!



N

/,4,+,,,!, ( /,8,+,,,!



9,)=A-J OM>,($:44

/,8,+,,,!, ( /,=,+,,,!



B,$C#$

/,A,+,,,!, ( /,A,+,,,!



B,!C-8:;"

,



/,8,+,,,!KLM, @ /,4,+,,,!N



Δ =



⎨
⎪ [⎪]
⎩



3
/,8,+,,,!



/,8,+,,,!9, 3 /,8,+,,,!KLM



KLM,:;? KLM,,C!

/,=,+,,,!, ( /,=,+,,,!



':;),($:44 B,$C#$

/,',+,,,!, A /,A,+,,,!



⎫
⎪ [⎪]

⎬
⎪ [⎪]



@ >9

/,?,+,,,!, ( /,=,+,,,!



>D,($:44 MP

/,$,+,,,!, ( /,=,+,,,!



B,$C#$



KLM,,C!

/,=,+,,,!, ( /,',+,,,!



MP

/,=,+,,,!, ( /,=,+,,,!



>9

/,=,+,,,!, ( /,$,+,,,!



G,:; G,,C!

/,F,+,,,!, A /,F,+,,,!



/,F,+,,,!G, 8 /,A,+,,,!B



B

/,A,+,,,!, I /,H,+,,,!, J /,;,+,,,! ⎭



**Annual Power Generating Capacities Built**



>9,:;< ≤4P

=,+ =,+



0 ≤4
=,+



>9,:;<,#.Q
! ∈# [0], ∀&, ∀' (23)



@,:;< ≤4P

?,+ ?,+



0 ≤4
?,+



@,:;<,#.Q
( ∈# [1], ∀&, ∀' (24)



>D,:;< ≤4P

$,+ $,+



0 ≤4
$,+



>D,:;<,#.Q
) ∈# [2], ∀&, ∀' (25)



E,:;< ≤4P

',+ ',+



0 ≤4
',+



E,:;<,#.Q

                    - ∈# [3], ∀&, ∀' (26)



9,:;< ≤3P

8,+ 8,+



0 ≤3
8,+



9,:;<,#.Q
+ ∈# [4], ∀&, ∀' (27)



B,:;<,#:;
≤8

A,+ A,+



B,:;<,#.Q
ℎ∈# [5], ∀&, ∀' (28)



Q A,+ 8 [P] A,+



A,+B,:;< ≤Q A,+ 8P A+



∑Q + A,+ = 1 ℎ∈# [5], ∀& (29)


**Annual Transmission Lines Built**


' 4,+ ≥' 4,+R% - ∈# [67], ∀' (30)


2 4,+ = ' 4,+ - ∈# [67], ' = 1 (31)


2 4,+ = ' 4,+ −' 4,+ - ∈# [67], ' > 1 (32)


38


**Annual Water Producing Capacities Built**



H,+I,:;< ≤<P H,+



0 ≤< H,+



I,:;<,#.Q
1 ∈# [8], ∀&, ∀' (33)



G,:;< ≤;̅

F,+ F,+



0 ≤;
F,+



G,:;<,#.Q
2 ∈# [9], ∀&, ∀' (34)



**Capacity Balancing Between Years**



>9,=.$
= 4

=,+ =,+R%



>9,:;< ! ∈# :0



4
=,+



>9,=.$
+ 4

=,+R% =,+



0

:, ∀&, ' = 1 (35)



>9,=.$
= 4

=,+ =,+R%



>9,:;< ! ∈# :0



4
=,+



>9,=.$
+ 4

=,+R% =,+



0

:, ∀&, ' > 1 (36)



@,=.$
= 4
?,+



4
?,+



@,:;< ( ∈# :1



:1, ∀&, ' = 1 (37)



@,=.$ @,=.$
= 4
?,+R%



@,:;< ( ∈# :1



4
?,+



@,=.$
+ 4

?,+R% ?,+



:1, ∀&, ' > 1 (38)



>D,=.$
= 4
$,+



44
$,+



>D,:;< ) ∈# :2



2

:, ∀&, ' = 1 (39)



>D,=.$
= 4

$,+ $,+R%



>D,:;< ) ∈# :2



4
$,+



>D,=.$
+ 4

$,+R% $,+



2

:, ∀&, ' > 1 (40)



E,=.$
= 4

',+ ',+



4
',+



E,:;<

                     - ∈# :



3

:, ∀&, ' = 1 (41)



E,=.$ E,=.$
= 4

',+ ',+R%



E,:;<

                  - ∈# :



4
',+



E,=.$
+ 4

',+R% ',+



3

:, ∀&, ' > 1 (42)



9,=.$
= 3

8,+ 8,+



3
8,+



9,:;< + ∈# :4



4

:, ∀&, ' = 1 (43)



9,=.$ 9,=.$
= 3

8,+ 8,+R%



9,:;< + ∈# :4



3
8,+



9,=.$
+ 3

8,+R% 8,+



4

:, ∀&, ' > 1 (44)



B,=.$
= 8

A,+ A+



8
A,+



B,:;< ℎ∈# :5



5

:, ∀&, ' = 1 (45)



B,=.$ B,=.$
= 8

A,+ A+R%



B,:;< ℎ∈# :5



8
A,+



B,=.$
+ 8

A+R% A,+



5

:, ∀&, ' > 1 (46)



I,=.$

H,+ = < H,+



< H,+



I,:;< 1 ∈# :;



;

:, ∀&, ' = 1 (47)



I,=.$ I,=.$

H,+ = < H,+R%



I,:;< 1 ∈# :;



< H,+



I,=.$

H,+R% + < H,+



;

:, ∀&, ' > 1 (48)



G,=.$
= ;

F,+ F,+



;
F,+



G,:;< 2 ∈# :9



9

:, ∀&, ' = 1 (49)



G,=.$ G,=.$
= ;

F,+ F,+R%



G,:;< 2 ∈# :9



;
F,+



G,=.$
+ ;

F,+R% F,+



9

:, ∀&, ' > 1 (50)



G,=.$
F F,+ ≤; +



∑; F F,+



G,!,!.4=.$
∀' (51)



**Investment Budget of Power Sector**


>9 >9,:;< B

∑ =∈7 [)] 1 =,+ 4 =,+ + ∑ A∈7 [+] 1 A,+ 56 A



>9 >9,:;< B

=∈7 [)] 1 =,+ 4 =,+ + ∑ A∈7 [+] 1 A,+



!C-8:;"
7 A 8 A,+



>9 4

=,+ =,+



B !C-8:;" B,:;<

A∈7 [+] 1 A,+ 56 A 7 A 8 A,+ 9 A : +



B

A,+ 56 A



@ @,:;< >D

?∈7 [*] 1 ?,+ 4 ?,+ + ∑ $∈7 [,] 1 $,+



∀' (52)


39



@
4
?,+



>D >D,:;< E

$∈7 [,] 1 $,+ 4 $,+ + ∑ '∈7 [-] 1 ',+



E E,:;<

'∈7 [-] 1 4 +



E 4

',+ ',+



∑ ?∈7 [*] 1 ?,+



>D 4

$,+ $,+



9 9,:;<

8∈7 [(] 1 8,+ 4 8,+ ≤1 +



9 4

8,+ 8,+



SL,#.Q



∑ 8∈7 [(] 1 8,+


**Investment Budget of Water Sector**



G G,:;< I

F∈7 [.] 1 F,+ ; F,+ + ∑ H∈7 [/] 1 H,+



G ;

F,+ F,+



I I,:;<

H∈7 [/] 1 H,+ < H,+ ≤1 +



H,+I < H,+



SE,#.Q
∀' (53)



∑ F∈7 [.] 1 F,+



**CCGT Plant Operations**



@,=.$ @
H /,?,+,,,! ≤( /,?,+,,,!



@,=.$ H /,?,+,,,! ∀3, ( ∈# :1



W ? 4 ?,+



@
≤4

/,?,+,,,! ?,+



:1, ∀&, ∀', ∀4, ∀5 (54)



@
≤X

/,?,+,,,! ?



( /,?,+,,,!



T ∀3, ( ∈# :



@ @

/,?,+,,,! −( /,?,+R%,,34,(,),!34!(!)



T ∀3, ( ∈# :



( /,?,+,,,!



@ T
≤X

/,?,+R%,,34,(,),!34!(!) ?



@ @

/,?,+,,,! −( /,?,+,,R%,!34!(!)



T ∀3, ( ∈# :



( /,?,+,,,!



@
≤X

/,?,+,,R%,!34!(!) ?



@ @

/,?,+,,,! −( /,?,+,,,!R%



T ∀3, ( ∈# :



( /,?,+,,,!



@
≤X

/,?,+,,,!R% ?



@
≤X

/,?,+,,,! ?



( /,?,+,,,!



W ∀3, ( ∈# :



@ @

/,?,+R%,,34,(,),!34!(!) −( /,?,+,,,!



W ∀3, ( ∈# :



∀3, ( ∈# :1, ∀&, (55)

' = 1, 4 = 1, 5 = 1

∀3, ( ∈# :1, ∀&, (56)

' > 1, 4 = 1, 5 = 1

∀3, ( ∈# :1, ∀&, (57)

∀', 4 > 1, 5 = 1

∀3, ( ∈# :1, ∀&, (58)

∀', ∀4, 5 > 1

∀3, ( ∈# :1, ∀&, (59)

' = 1, 4 = 1, 5 = 1

∀3, ( ∈# :1, ∀&, (60)

' > 1, 4 = 1, 5 = 1

∀3, ( ∈# :1, ∀&, (61)

∀', 4 > 1, 5 = 1



( /,?,+R%,,34,(,),!34!(!)



@ W
≤X

/,?,+,,,! ?



@ @

/,?,+,,R%,!34!(!) −( /,?,+,,,!



W ∀3, ( ∈# :



(
/,?,+,,R%,!34!(!)



@
≤X

/,?,+,,,! ?



@ @

/,?,+,,,!R% −( /,?,+,,,!



( /,?,+,,,!R%



@
≤X

/,?,+,,,! ?



W ∀3, ( ∈# :1



:1, ∀&, ∀', ∀4, 5 > 1 (62)



**CSP Farm Operations**



<,@ ∀!, ∀&, ∀' (63)

Y <



,(,<AB



MP,=.$

4 =
=,+



X <



=>,,(

< - <,@



<,@ ∀!, ∀&, ∀' (64)

Y <



,(,<AB



KLM,=.$

3 =
=,+



B <



CD=,,(

< - <,@



=>,<AB



0

:, ∀&, ∀', ∀4, ∀5 (65)



( /,=,+,,!MP =



W5Z E,<,@,F,$ - <,@



< ∀3, ! ∈# :

%[[[ E/# [G]



KLM

/,=,+,,,! = ( /,=,+,,,!



KLM,,C! ∀3, ! ∈# :



3
/,=,+,,,!



KLM,:;? KLM,,C!

/,=,+,,,! −( /,=,+,,,!



∀3, ! ∈# :0, ∀&, (66)

' = 1, 4 = 1, 5 = 1


∀3, ! ∈# :0, ∀&, (67)

' > 1, 4 = 1, 5 = 1


∀3, ! ∈# :0, ∀&, (68)

∀', 4 > 1, 5 = 1



KLM,,C! ∀3, ! ∈# :



KLM = 3 KLM

/,=,+,,,! /,=,+R%,,34,(,),!34!(!)



KLM,:;? KLM,,C!

/,=,+,,,! −( /,=,+,,,!



3
/,=,+,,,!



KLM KLM,:;?

/,=,+R%,,34,(,),!34!(!) + ( /,=,+,,,!



KLM = 3 KLM

/,=,+,,,! /,=,+,,R%,!34!(!)



KLM,:;? KLM,,C!

/,=,+,,,! −( /,=,+,,,!



KLM,,C! ∀3, ! ∈# :



3
/,=,+,,,!



KLM

/,=,+,,R%,!34!(!) + ( /,=,+,,,!



KLM = 3 KLM

/,=,+,,,! /,=,+,,,!R%



KLM,:;? KLM,,C!

/,=,+,,,! −( /,=,+,,,!



KLM,,C! ∀3, ! ∈# :0



3
/,=,+,,,!



KLM

/,=,+,,,!R% + ( /,=,+,,,!



0

:, ∀&, ∀', ∀4, 5 > 1 (69)



KLM ≤3

/,=,+,,,! =,+



0 ≤3
/,=,+,,,!



KLM,=.$ ∀3, ! ∈# :0



0

:, ∀&, ∀', ∀4, ∀5 (70)



>9 KLM,,C!

,=,+,,,!/ = Y = ( /,=,+,,,!



(,=,+,,,!/



KLM,,C! ∀3, ! ∈# :0



0

:, ∀&, ∀', ∀4, ∀5 (71)



>9 ≤4

,=,+,,,!/ =,+



0 ≤(,=,+,,,!/



>9,=.$ ∀3, ! ∈# :0



0

:, ∀&, ∀', ∀4, ∀5 (72)



40


**HPS Plant Operations**



B,!C-8:;"
= Z A 6 A



( /,A,+,,,!



!C-8:;" !C-8:;"
A /,A,+,,,!



!C-8:;" 9 A ∀3, ℎ6# :



5

:, ∀', ∀4, ∀5 (73)



5

:, ∀&, ∀', ∀4, ∀5 (74)



_ E+H,H,@,F,$ B H ∀3, ℎ6# :



B,!C-8:;"

( /,A,+,,,! =



G H



BIJB +

H ^ E,H,@,F,$



B,!C-8:;"

/,A,+,,,! ≤8 A 8 A,+



0 ≤A /,A,+,,,!



B,=.$
∀3, ℎ6# :



5

:, ∀&, ∀', ∀4, ∀5 (75)



B,$C#$

/,A,+,,,! ≤8 A 8 A,+



0 ≤A /,A,+,,,!



B,=.$
∀3, ℎ6# :



5

:, ∀&, ∀', ∀4, ∀5 (76)



B,!C-8:;" ∀3, ℎ6# :



B B,:;<

/,A,+,,,! = [ A 8 /,A,+,,,!



B,$C#$ B,!C-8:;"

/,A,+,,,! −A /,A,+,,,!



8
/,A,+,,,!



B,:;< B,$C#$

/,A,+,,,! + A /,A,+,,,!



B,!C-8:;" ∀3, ℎ6# :



∀3, ℎ6# :5, ∀&, (77)

' = 1, 4 = 1, 5 = 1


∀3, ℎ6# :5, ∀&, (78)

' > 1, 4 = 1, 5 = 1


∀3, ℎ6# :5, ∀&, (79)

', 4 > 1, 5 = 1



B B,:;<
= 8

/,A,+,,,! /,A,+R%,,34,(,),!34!(!)



B,$C#$

/,A,+,,,! −A /,A,+,,,!



8
/,A,+,,,!



B,:;< B,$C#$

/,A,+R%,,34,(,),!34!(!) + A /,A,+,,,!



B,!C-8:;" ∀3, ℎ6# :



B B,:;<
= 8

/,A,+,,,! /,A,+,,R%,!34!(!)



B,$C#$

/,A,+,,,! −A /,A,+,,,!



8
/,A,+,,,!



B,:;< B,$C#$

/,A,+,,R%,!34!(!) + A /,A,+,,,!



B,!C-8:;"
∀3, ℎ6# :



B B,:;<
= 8

/,A,+,,,! /,A,+,,,!R%



B,$C#$

/,A,+,,,! −A /,A,+,,,!



8
/,A,+,,,!



B,:;< B,$C#$

/,A,+,,,!R% + A /,A,+,,,!



:5, ∀&, ∀4, 5 > 1 (80)



B,=.$
∀3, ℎ6# :



0 ≤8 /,A,+,,,! ≤8 A,+



5

:, ∀&, ∀', ∀4, ∀5 (81)



**Battery Plant Operations**



9,)=A-J

/,8,+,,,! ≤\ 8 3 8,+



0 ≤( /,8,+,,,!



9,=.$ ∀3, +6# :4



4

:, ∀&, ∀', ∀4, ∀5 (82)



9,=A-J

/,8,+,,,! ≤\ 8 3 8,+



0 ≤( /,8,+,,,!



9,=.$ ∀3, +6# :4



4

:, ∀&, ∀', ∀4, ∀ (83)



(,#<HLM



∀3, +6# :4, ∀&, (84)

∀' = 1, 4 = 1, 5 = 1



$ E,K,@,F,$



` K



,K,@,F,$ ∀3, +6# :

#<HLM



=A-J
−



9 9,:;<

/,8,+,,,! = ] 8 3 /,8,+,,,!


9 9,:;<

/,8,+,,,! = ] 8 3 /,8,+,,,!



9,=A-J
^ 8



3
/,8,+,,,!



9,:;< 9,=A-J

/,8,+,,,! + ( /,8,+,,,!,



∀3, +6# :4, ∀&, (85)

' > 1, 4 = 1, 5 = 1



3
/,8,+,,,!



9,:;< 9
+ 3

/,8,+,,,! /,8,+R%,,34,(,),!34!(!)



9
+



9,=A-J
^ 8



(,#<HLM

$ E,K,@,F,$



(,#<HLM

E,K,@,F,$

#<HLM

` K



#<HLM



( /,8,+,,,!



=A-J
−



(,#<HLM



∀3, +6# :4, ∀&, (86)

∀', 4 > 1, 5 = 1


∀3, +6# :4, ∀&, (87)

∀', ∀4, 5 > 1



$ E,K,@,F,$



` K



,K,@,F,$ ∀3, +6# :

#<HLM



=A-J
−



9 = 3 9

/,8,+,,,! /,8,+,,R%,!34!(!)



9,=A-J
^ 8



3
/,8,+,,,!



9 9,=A-J

/,8,+,,R%,!34!(!) + ( /,8,+,,,!



(,#<HLM



$ E,K,@,F,$



` K



E,K,@,F,$ ∀3, +6# :

#<HLM



9 = 3 9

/,8,+,,,! /,8,+,,,!R%



9,=A-J
^ 8



=A-J
−



3
/,8,+,,,!



/,8,+,,,!R%9 + ( /,8,+,,,!



9,=.$ 9
≤3

8,+ /,8,+,,,!



9,=.$ ∀3, +6# :4



] 8 3 8,+



9 ≤3

/,8,+,,,! 8,+



4

:, ∀&, ∀', ∀4, ∀5 (88)



**Transmission Line Flow**


@ /,4,+,,,!N = ' 4, _` 4 5J /,((4),+,,,! −J /,-(4),+,,,! : ∀3, ∀- ∈# [6] [!], ∀', ∀4, ∀5 (89)



N,=.$ N
≤@
/,4,+,,,!



N,=.$
∀3, ∀- ∈# [6] [!], ∀', ∀4, ∀5 (90)



−@
4



/,4,+,,,!N ≤@ 4



−a ≤J /,;,+,,,! ≤a ∀3, ∀&, ∀', ∀4, ∀5 (91)


J /,;,+,,,! = 0 &: 89'989&!9 (92)


41


**Renewable Spillage Tracking**



>D,($:44 >D
= @

/,$,+,,,! /,$,+,,,!



>D,=.$ >D,=,;

$,+ −( /,$,+,,,!



>D,=,; ∀3, ) ∈# :2



( /,$,+,,,!



>D 4

/,$,+,,,! $,+



2

:, ∀&, ∀', ∀4, ∀5 (93)



>D,($:44 E
= @

/,',+,,,! /,',+,,,!



E,=.$ E,=,;

$,+ −( /,',+,,,!



E,=,; ∀3, ) ∈# :3



( /,',+,,,!



E 4

/,',+,,,! $,+



3

:, ∀&, ∀', ∀4, ∀5 (94)



**Desalination Operations**



I,=.$ I

H,+ ≤A /,H,,,!



I,=.$ ∀3, 1 ∈# :;



b H < H,+



/,H,,,!I ≤< H,+



;

:, ∀&, ∀', ∀4, ∀5 (95)



**Water Storage Tank Operations**



G,:;

/,F,+,,,! ≤c F ; F,+



0 ≤A /,F,+,,,!



G,=.$ ∀3, 2 ∈# :9



9

:, ∀&, ∀', ∀4, ∀5 (96)



/,F,+,,,!G,,C! ≤c F ; F,+



0 ≤A /,F,+,,,!



G,=.$ ∀3, 2 ∈# :9



9

:, ∀&, ∀', ∀4, ∀5 (97)



G G,:;

/,F,+,,,! = A /,F,+,,,!



G,,C! ∀3, 2 ∈# :



;
/,F,+,,,!



G,:; G,,C!

/,F,+,,,! −A /,F,+,,,!



∀3, 2 ∈# :9, ∀&, (98)

' = 1, 4 = 1, 5 = 1


∀3, 2 ∈# :9, ∀&, (99)

' > 1, 4 = 1, 5 = 1



G,,C! ∀3, 2 ∈# :



G = ; G

/,F,+,,,! /,F,+R%,,34,(,),!34!(!)



G,:; G,,C!

/,F,+,,,! −A /,F,+,,,!



;
/,F,+,,,!



G G,:;

/,F,+R%,,34,(,),!34!(!) + A /,F,+,,,!



∀3, 2 ∈# :9, ∀&, (100)

∀', 4 > 1, 5 = 1


∀3, 2 ∈# :9, ∀&, (101)

∀', ∀4, 5 > 1


∀3, 2 ∈# :9, ∀&, (102)

∀', ∀4, ∀5



G = ; G

/,F,+,,,! /,F,+,,R%,!34!(!)



G,:; G,,C!

/,F,+,,,! −A /,F,+,,,!



G,,C! ∀3, 2 ∈# :



;
/,F,+,,,!



G G,:;

/,F,+,,R%,!34!(!) + A /,F,+,,,!



G = ; G

/,F,+,,,! /,F,+,,,!R%



G,:; G,,C!

/,F,+,,,! −A /,F,+,,,!



G,,C! ∀3, 2 ∈# :



;
/,F,+,,,!



G G,:;

/,F,+,,,!R% + A /,F,+,,,!



G ≤;

/,F,+,,,! F,+



0 ≤;
/,F,+,,,!



G,=.$ ∀3, 2 ∈# :



**Nodal Power Balance**
∑ ?∈7 O ( /,?,+,,,!@ + ∑ =∈7



@

O (

?∈7 N



>9 >D,=,;
=∈7 N [,] ( /,=,+,,,! + ∑ $∈7 N [,] ( /,$,+,,,!



O ( /,?,+,,,! + ∑ =∈7 [,] ( /,=,+,,,!



>D,=,;
$∈7 N [,] ( /,$,+,,,! +



E,=,; B,!C-8:;" 9,)=A-J

∑ '∈7 N [-] ( /,',+,,,! + ∑ A∈7 N [+] ( /,',+,,,! + ∑ 8∈7 N [(] ( /,',+,,,! +

∑ 4 -(4)3; @ /,',+,,,!N



'∈7 N [-] ( /,',+,,,!E,=,; + ∑ A∈7 N [+] ( /,',+,,,!



∑ '∈7 N [-] ( /,',+,,,!



A∈7 N [+] ( /,',+,,,!B,!C-8:;" + ∑ 8∈7 N [(] ( /,',+,,,!9,)=A-J



@ N

4 -(4)3;



= 4 /,A,+,,,!B,$C#$ + ∑ A∈7 N [+] ( /,A,+,,,!B,$C#$ + ∑ 8∈7 N [(] ( /,8,+,,,!9,=A-J +

∑ 4 ((4)3; @ /,4,+,,,!N + ∑ '∈7 [-] A /,H,+,,,!I d H [I]



/,A,+,,,!B,$C#$ + ∑ A∈7 N [+] ( /,A,+,,,!B,$C#$



= 4
/,A,+,,,!



A∈7 N [+] ( /,A,+,,,!B,$C#$ + ∑ 8∈7 N [(] ( /,8,+,,,!9,=A-J



4 ((4)3; @ /,4,+,,,!N + ∑ '∈7 [-] A /,H,+,,,!I



'∈7 N [-] A /,H,+,,,!I d H [I]



**System Water Balance**



H∈7 [P] A /,H,+,,,!I + ∑ F∈7 [.] A /,F,+,,,!G,,C!



; W +



∑ H∈7 [P] A /,H,+,,,!

∑ F∈7 [.] A /,F,+,,,!G,:;



G,,C! = W
F∈7 [.] A /,F,+,,,! ∑< ; /,;,+,,,!



G,:;
F∈7 [.] A



**Limit on Renewable Generation**


b [J";],, ∑ 12!3% ?a7 [*] ( /,?,+,,,!@
e∑. f∑



>9
? (
=a7 N [)]



?a7 [*] ( /,?,+,,,!@ + ∑ ? ( /,?,+,,,!>9



12 @ >9
,, ∑ !3% [*] ( + ∑ ? ( +



∀3, ∀&, ∀', ∀4, ∀5 (103)


∀3, ∀', ∀4, ∀5 (104)


∀3, ∀' (105)


42



>D,=.$ E
$a7 N [,] @ /,?,+,,,! + ∑ ba7 N [-] @ /,b,+,,,!



E,=.$ + ∑ Aa7 N [+] ( /,A,+,,,!



∑ $a7 N [,] @ /,?,+,,,!



E E,=.$ B,!C-8:;"

ba7 N [-] @ /,b,+,,,! 4 b + ∑ Aa7 N [+] ( /,A,+,,,!
gh



E 4 b



≤>∑.,, ∑ 12!3% i∑ =a7 N [)] ( /,=,+,,,!>9



12 >9 >D >D,=.$
,, ∑ !3% i∑ =a7 N [)] ( /,=,+,,,! + ∑ $a7 N [,] @ /,?,+,,,! 4 (,+ +



=a7 N [)] ( /,=,+,,,!>9 + ∑ $a7 N [,] @ /,?,+,,,!>D



>D 4

/,?,+,,,! (,+



E E,=.$

ba7 N [-] @ /,b,+,,,! 4 ',+ + ∑ Aa7 N [+] ( /,?,+,,,!



E 4

/,b,+,,,! ',+



B,!C-8:;"
Aa7 N [+] ( /,?,+,,,! j?



∑ ba7 [-] @ /,b,+,,,!


**Linearization of Thermal Generation Constraints**
We model Equation 53 with the disjunction in Equation 105, where U is a Boolean variable
that is true if [O] d,>,#,,,- [= ] 1 [  (^] d,>,#,,,- = Q >,#],P:+ ) and false if O d,>,#,,,- = 0  (^ d,>,#,,,- = 0).



y



∀3, ∀(, ∀', ∀4, ∀5 (106)
|



z


],P:+

Q >,# −^ d,>,#,,,- ≤0



| ∨y



¬z


W:+

^ ≤0
d,>,#,,,


],P:+
≤0



−^
d,>,#,,,


W:+
≤0



−^ d,>,#,,,- + Q >,#



The disjunction in Equation 105 is reformulated as

^ d,>,#,,,- −Q >,#],P:+ ≤R & (1 −O d,>,#,,,- ) ∀3, ∀(, ∀', ∀4, ∀5 (107)


Q >,#],P:+ −^ d,>,#,,,- ≤R 2 (1 −O d,>,#,,,- ) ∀3, ∀(, ∀', ∀4, ∀5 (108)


^ d,>,#,,,- ≤R C O d,>,#,,,- ∀3, ∀(, ∀', ∀4, ∀5 (109)


−^ d,>,#,,,- ≤R M O d,>,#,,,- ∀3, ∀(, ∀', ∀4, ∀5 (110)


where R &, R 2, R C, R M are constant values.

By inspection we can deduce that the value of the Rare:


R & = 0 (111)


R 2 = Q >,#ZHj (112)


R C = Q >,#ZHj (113)


R M = 0 (114)


**Linearization of Power Flow Constraints**
We model Equation 89 with the disjunction in Equation 115 where A is a Boolean variable that
is true if [J] Q,# [= ] 1 and false if [J] Q [,] # [= 0] .



⎡−Z Q Çw d,9(Q),#,,,- −w d,$(Q),#,,,- ( É + ' d,Q,#,,,-_
⎢
⎢ Z Q Çw d,9(Q),#,,,- −w d,$(Q),#,,,- É −' d,Q,#,,,-_
⎢ ' d,Q,#,,,-_ −' Q_,@:c ≤0
⎢
⎣ −' _ + ' _,@:c ≤0



'
d,Q,#,,,


_ ≤0



_ ≤0



Z Q Çw d,9(Q),#,,,- −w d,$(Q),#,,,- É −' d,Q,#,,,-_



_ ≤0



∨



⎡ ' _ ¬(
d,Q,#,,,⎢
⎢ −' _
⎢−' _ d,Q,#,,,⎢ d,Q,#,,,⎣ ' _



−'
d,Q,#,,,


_ ≤0 ⎤

d,Q,#,,,
⎥

_ ≤0 ⎥

d,Q,#,,,- ≤' Q_,@:c ⎥

⎥
_ ≤0 ⎦



_ ≤0



−'
d,Q,#,,,


_ ≤' Q



(115)



_ −'
Q



É + ' d,Q,#,,,-_ ≤0⎤

⎥

É −' d,Q,#,,,-_ ≤0 ⎥

_,@:c ≤0 ⎥⎥

_,@:c ≤0 ⎦



'
d,Q,#,,,


_,@:c ≤0



_,@:c



_ + '
Q



−'
d,Q,#,,,



[∀3, ∀-, ∀', ∀4, ∀5]


The disjunction in Equation 115 is reformulated as


' d,Q,#,,,-_ −Z Q Çw d,9(Q),#,,,- −w d,$(Q),#,,,- É ≤R & Ç1 −J Q,# É ∀3, ∀-, ∀', ∀4, ∀5 (116)


−' d,Q,#,,,-_ + Z Q Çw d,9(Q),#,,,- −w d,$(Q),#,,,- É ≤R 2 (1 −J Q,# ) ∀3, ∀-, ∀', ∀4, ∀5 (117)


43


_ −'
Q



'
d,Q,#,,,


_,@:c ≤ R C (1 −J Q,# ) ∀3, ∀-, ∀', ∀4, ∀5 (118)



_ −'
Q



−'
d,Q,#,,,


_,@:c ≤ R M (1 −J Q,# ) ∀3, ∀-, ∀', ∀4, ∀5 (119)



' d,Q,#,,,-_ ≤ R k J Q,# ∀3, ∀-, ∀', ∀4, ∀5 (120)



−' d,Q,#,,,-_ ≤ R l J Q,# ∀3, ∀-, ∀', ∀4, ∀5 (121)



where R &, R 2, R C, R M, R k, R l are constant values.

By inspection we can deduce that the value of the R are:


R & = á (122)


R 2 = á (123)


R C = 0 (124)


R M = −' Q_,@:c (125)


R k = ' Q_,@:c (126)


R l = −' Q_,@:c (127)


We simplify the previous equations as


' d,Q,#,,,-_ −Z Q Çw d,9(Q),#,,,- −w d,$(Q),#,,,- É ≤á(1 −J Q,# ) ∀l, ∀m, ∀_, ∀/, ∀n (128)


−' d,Q,#,,,-_ + Z Q Çw d,9(Q),#,,,- −w d,$(Q),#,,,- É ≤á(1 −J Q,# ) ∀l, ∀m, ∀_, ∀/, ∀n (129)



_ ≤ ' Q



'
d,Q,#,,,


_,@:c
J Q,# ∀l, ∀m, ∀_, ∀/, ∀n (130)



_ ≤ −' Q



−'
d,Q,#,,,


_,@:c
J Q,# ∀l, ∀m, ∀_, ∀/, ∀n (131)



44


**Supplementary note 7:** **Levelized Cost Calculations**
**Levelized cost of electricity (LCOE)**
Levelized cost of electricity [9] measures the average net present cost of electricity generation
over the useful lifetime of a generating technology. In this case, the lifetime is the duration of
the planning horizon. LCOE is a ratio between the discounted sum of total investment costs
(TIC) and the total operating cost (TOC) divided by the discounted sum of the total power
produced (TPP).



8<,75



∑



"@A
?



8<,75B"CA

? ?



+%$` =



~~?~~ ($B5) [?]



(132)



∑



"== ~~8<,75~~
?



?
($B5) [?]



-I$U!F?
q a i a,#



+,;?$ = ∑ U∈m [F] ) U,#[



N[ N[,!Fb \

P∈m [A] ) P,# Q P,# + ∑ a∈m [G] ) a#



N[ Q

P,# P,#



\ -I$U!F? \,!Fb

a∈m [G] ) a# Çe a q a i a,# d a É +



\

a# Çe a



,)%
#




[ [,!Fb N[

U∈m [F] ) U,# Q U,# + ∑ P∈m [A] ) P,#




[ Q

U,# U,#



] ],!Fb NO

>∈m [H] ) >,# Q >,# + ∑ +∈m [=] ) +,#



]
Q
>,#



E E,!Fb

;∈m [:] ) Q (133)



E Q

;,# ;,#



∑ >∈m [H] ) >,#



NO NO,!Fb E

+∈m [=] ) +,# Q +,# + ∑ ;∈m [:] ) ;,#



NO Q

+,# +,#



+,;?$ = ∑k d d ∑j,, ∑ 2M-D& U∈m [F] % U[
E∑



,$% #+,;?$ = ∑k d d ∑j,, ∑ 2M-D& E∑ U∈m [F] % U[ à` d,U,#,,,- + ` d,U,#,,,- â + ∑ P∈m [A] % PN[ I d,P,#,,,-N[ +

∑ a∈m [G] % a\ ÇI \,+I@+ + I -I$U!F? É + ∑ [H] % ] I ],!Fb + ∑ [=] % NO ' NO Q NO,P:+ +



,$%
#




[ [,Pa:$i? [,<!9Pa:$i?

U∈m [F] % U à` d,U,#,,,- + ` d,U,#,,,- â + ∑ P∈m [A] % P




[ [,Pa:$i?
à` d,U,#,,,



[,Pa:$i?
+ `

d,U,#,,,- d,U,#,,,


N[ N[
I d,P,#,,,


NO NO,P:+
Q

d,+,#,,,- d,+,#,,,


\,+I@+ -I$U!F?

d,a,#,,,- + I d,a,#,,,


] ],!Fb

>∈m [H] % d,>,#,,,- I >,# + ∑ +∈m [=] % +



]

d,>,#,,,- I >,#



% NO ' NO Q NO,P:+ +

+∈m [=] + d,+,#,,,- d,+,#,,,


NO ' NO

+ d,+,#,,,


\ \,+I@+ -I$U!F? ]

a∈m [G] % a ÇI d,a,#,,,- + I d,a,#,,,- É + ∑ >∈m [H] % d,>,#,,,


\ ÇI d,a,#,,,-\,+I@+



;∈m [:] % ;E ' d,;,#,,,-E Q d,;,#,,,-E,P:+ G (134)



E ' E
d,;,#,,,


∑ ;∈m [:] % ;



E E,P:+
Q

d,;,#,,,- d,;,#,,,


+,;?$ 2M N[ \,+I@+ -I$U!F?

,QQ # = ∑k d d ∑j,, ∑ -D& ä∑ P∈m [A] I d,P,#,,,- + ∑ a∈m [G] ÇI d,a,#,,,- + I d,a,#,,,- É +

],!Fb NO NO,P:+ NO,9+!QQ E E,P:+

∑ >∈m [H] I >,# + ∑ +∈m [=] Ç' d,+,#,,,- Q d,+,#,,,- − I d,+,#,,,- É + ∑ ;∈m [:] Ç' d,;,#,,,- Q d,;,#,,,


+,;?$ = ∑k d d ∑j,, ∑ 2M-D& ä∑ P∈m [A] I d,P,#,,,-N[



\,+I@+ -I$U!F?

d,a,#,,,- + I d,a,#,,,


,QQ
#



P∈m [A] I d,P,#,,,-N[ + ∑ a∈m [G] ÇI d,a,#,,,-\,+I@+



>∈m [H] I >,#],!Fb + ∑ +∈m [=] Ç' d,+,#,,,-NO



NO NO,P:+ NO,9+!QQ E

+∈m [=] Ç' d,+,#,,,- Q d,+,#,,,- − I d,+,#,,,- É + ∑ ;∈m [:] Ç' d,;,#,,,


NO,P:+ NO,9+!QQ

d,+,#,,,- − I d,+,#,,,


E,P:+ E,9+!QQ
−Q

d,;,#,,,- d,;,#,,,


E E,P:+ E,9+!QQ

;∈m [:] Ç' d,;,#,,,- Q d,;,#,,,- −Q d,;,#,,,- Éã
(135)



E E,P:+
Q

d,;,#,,,- d,;,#,,,


NO NO,P:+
Q

d,+,#,,,- d,+,#,,,


**Levelized cost of water (LCOW)**
Levelized cost of water [3] measures the average net present cost of water production over the
useful lifetime of a generating technology. In this case, the lifetime is the duration of the
planning horizon. LCOW is a ratio between the discounted sum of total investment costs (TIC)
and the total operating cost (TOC) divided by the discounted sum of the total power produced
(TPP).



,4/75



∑



~~?~~ ($B5) [?]



"@A?



,4/75B"CA

? ?



+%$å =



"==,4/75?

∑ ?

($B5) [?]



(136)



` `,!Fb

L∈m [J] ) f (137)



L,#` f L,#



;:-?$ = ∑ *∈m [I] ) *,#^



,)%
#



^ ^,!Fb `

*∈m [I] ) *,# g *,# + ∑ L∈m [J] ) L,#



^ g

*,# *,#



;:-?$ = ∑k d d ∑j,, ∑ 2M-D& ä∑ *∈m [%] % 


^,!F ^,,I
d,*,#,,,- + v d,*,#,,,


,$%
#



d d ∑j,, ∑ 2M-D& ä∑ *∈m [%] % ** Çv d,*,#,,,-^,!F + v d,*,#,,,-^,,I- É + ∑ L∈m [K] % L [`] I d,*,#,,,-N[ ã   (138)



*∈m [%] % ** Çv d,*,#,,,-^,!F + v d,*,#,,,-^,,I- É + ∑ L∈m [K] % L [`] I d,*,#,,,-N[



- ^,!F

- Çv d,*,#,,,


#;:-?$ = ∑k d d ∑j,, ∑ 2M-D& ä∑ L∈m [K] I d,*,#,,,-N[



,QQ
#



d d ∑j,, ∑ 2M-D& ä∑ L∈m [K] I d,*,#,,,-N[ ã (139)



45


**Supplementary note 8:** **The expected value of perfect information and the value of the**
**stochastic solution** [10]

**The Expected Value of Perfect Information (EVPI)**
A general two-stage stochastic programming problem can be represented as:

min c,n [E[1] [X] [é + è(r)] [X] [ê(r)]] (140)


s.t

ëé = í (141)

ì(r)é + î(r)ï(r) = ñ(r) (142)

é ∈ó, ï(r) ∈ò, (143)

where ô is a random variable with a certain probability distribution. We address the solution of
problem (139)-(142) assuming discrete realizations of the random variable r, the so-called
discrete scenarios. We denote the optimal objective function of problem (139)-(142) as SP.

To calculate the EVPI, we introduce the wait-and-see solution (WS). The WS solution is
obtained by solving problem (139)-(142) for each discrete scenario individually. We denote
the optimal solution of each problem as ö 9∗, ∀ú ∈g, where ú is a scenario index and g is the set

of scenarios, and å 9 as the corresponding optimal objective function value.
The wait-and-see solution is defined as

åg = E[å 9 ]. (144)

Based on this notation, the EVPI is defined as:

`iQ) = gQ −åg. (145)

The EVPI represents the maximum payment one would be willing to make to obtain perfect
information.

**The Value of the Stochastic Solution (VSS)**
To calculate the VSS, we first calculate the expected value problem, defined by using the
average of the random variables in problem (19)-(142) instead of the multiple scenarios. The
optimal solution of this problem is denoted by the expected value solution ö [eO] . Then we solve
problem (139)-(142) with ö fixed to ö [eO] considereing the discrete scenarios. The optimal
objective function value is denoted by EEV. Based on this notation, the VSS is defined as

igg = ``i −gQ. (146 _)_

The VSS represents the cost of ignoring uncertainty when planning.

However, for some problems, such as the one presented in this manuscript, using the EEV
solution, ö [eO],  may result in infeasibilities for some scenarios. In this case, the value of EEV
is positive infinity. However, this answer does not say much about the VSS. [10] For these cases,
using the worst-case solution, ö [;P], obtained from solving problem (139)-(142) for the worstcase scenario only (given that it can be identified) is an alternative. Then we solve problem
(139)-(142) with ö fixed to ö [;P] considering the discrete scenarios. The optimal objective


46


function value is denoted as the expected value of the reference scenario (EVRS) and the VSS
is defined as

igg = `i&g −gQ. (147)

The results for the SP, WS, EVRS, EVPI and VSS, are presented in Supplementary Table 7.


47


**Supplementary note 9: Comparison of Representative days vs. Full-time Resolution**

This note provides a comparison between time representations in the planning model used in


this study: 1) representative days, each with an hourly resolution, used to approximate weather


and demand conditions over one year; and 2) using a full-time resolution with 8760 hours per


year. The use of representative days in the stochastic programming models is motivated by the


expected high computational cost of models using a full-time resolution over a multi-year


horizon. To compare the performance of the proposed model using representative days versus


a full-time resolution model, we designed three computational experiments:


1. Comparing stochastic programming models using a) representative days; and b) a full

time resolution; considering a time horizon with ten years.


2. Comparing stochastic programming model using a) representative days; and b) a full

time resolution; considering a time horizon with one year.


3. Comparing deterministic models using a) representative days; and b) a full-time


resolution; considering a time horizon with one year.


In the first experiment, we contrast the results discussed in the main manuscript text with the


ones from a stochastic programming model for ten years using the full hourly resolution (8760


hours) for the base case. We specified a computational time limit of 14 days, and when the


time limit was reached, we obtained a final optimality gap of 43.2%. Thus, in this case we do


not have a certificate of global optimality, and consequently cannot consider the results to be


close to the optimal solution. The inability to solve the full-time resolution ten-year model after


14 days (336 hours) of computation time shows the limitations of a full-time resolution model


in a stochastic setup and a multi-year horizon The representative period model presented in the


results section of the manuscript solved the model in under seven hours and was able to close


the optimality gap, which highlights the advantages of adopting representative days.


Supplementary Table 18 summarizes the computation time and optimality gap for the two


models.


In the second experiment, for a clearer comparison between a stochastic programming model


with a representative period approach and full-time hourly resolution approach, we run for only


the final year, 2029. By doing so, we reduce the model size while still capturing the amount of


capacity necessary by the end of the time horizon; a common practice found in the literature. [11–]


15 The downside of running the model for only the last year is the inability to determine when


48


along the time horizon a technology would be installed; rather we obtain a snapshot of the what


the system will look like.


Supplementary Table 19 shows that using the stochastic programming model with a full-time


resolution, there is an increase in CSP and wind capacity; 5.12% and 2.21% more of the


respective capacities compared to the representative day model. In this case, the additional


capacity is attributed to infrequent hours throughout the year that have high power demand and


low available renewable resources. To meet power demand a slightly higher capacity is needed.


Because the representative day model uses periods that best represent an entire year, days that


contain these outliers were not selected. Nevertheless, the representative day model gives a


very close approximation to the generation mix to that obtained using a full-time resolution


model. The close agreement between the two models, together with the decreased computation


time that a representative day model offers, is indicative of the benefits of using the latter


approach.


In the third experiment, we focus on one-year deterministic runs using the mean power and


water demand data. Supplementary Table 20 presents similar results to those generated in the


analysis of the stochastic models. The full-time resolution model suggests additional CSP and


wind capacity; the model indicates 3% and 1.64% more of the respective technologies. This


further highlight that adopting a representative day approach is quite advantageous in obtaining


a robust approximation of the optimal decision.


It is important to note that for the deterministic model, we assume perfect information meaning


that we assume the power and water demands are certain. However, with planning models we


know that there is no perfect information concerning demands occurring many years in the


future. In the case that the demand realizations are much higher than the mean scenario, the


built system would be unable to produce sufficient water and electricity. In contrast, a


stochastic optimization approach would allow the decision-maker to build a system that would


be feasible for every scenario envisioned.


49


**Supplementary References**
1. U.S. Energy Information Adminstration. Homepage. Available at:
https://www.eia.gov/. (Accessed: 10th March 2021)
2. National Renewable Energy Laboratory. Homepage. (2014). Available at:
https://www.nrel.gov/. (Accessed: 10th March 2021)
3. Caldera, U., Bogdanov, D., Afanasyeva, S. & Breyer, C. Role of seawater desalination
in the management of an integrated water and 100% renewable energy based power
sector in Saudi Arabia. _Water (Switzerland)_ **10**, (2017).
4. Lloyd, S. P. Least Squares Quantization in PCM. _IEEE Trans. Inf. Theory_ **28**, 129–137
(1982).
5. Ostrovsky, R., Rabani, Y., Schulman, L. J. & Swamy, C. The effectiveness of Lloydtype methods for the k-means problem. _J. ACM_ **59**, 1–22 (2012).
6. Arthur, D. & Vassilvitskii, S. K-means++: The advantages of careful seeding. _Proc._
_Annu. ACM-SIAM Symp. Discret. Algorithms_ **07**    - **09**    - **Janu**, 1027–1035 (2007).
7. Vestas. Homepage. Available at: https://www.vestas.com/. (Accessed: 11th March
2021)
8. Lorenzo, E. _Energy Collected and Delivered by PV Modules_ . _Handbook of_
_Photovoltaic Science and Engineering_ (2011). doi:10.1002/9780470974704.ch22
9. Short, W., Packey, D. & Holt, T. A manual for the economic evaluation of energy
efficiency and renewable energy technologies. _Renew. Energy_ **95**, 73–81 (1995).
10. Birge, J. & Louveaux, F. _Stochastic Programming._ **49**, (1998).
11. Alraddadi, M., Conejo, A. J. & Lima, R. M. Expansion Planning for Renewable
Integration in Power System of Regions with Very High Solar Irradiation. _J. Mod._
_Power Syst. Clean Energy_ **9**, 485–494 (2021).
12. Baringo, L. & Conejo, A. J. Correlated wind-power production and electric load
scenarios for investment decisions. _Appl. Energy_ **101**, 475–482 (2013).
13. Domínguez, R., Conejo, A. J. & Carrión, M. Toward fully renewable electric energy
systems. _IEEE Trans. Power Syst._ **30**, 316–326 (2015).
14. Almansoori, A. & Shah, N. Design and operation of a stochastic hydrogen supply
chain network under demand uncertainty. _Int. J. Hydrogen Energy_ **37**, 3965–3977
(2012).
15. Xu, Q., Li, S. & Hobbs, B. F. Generation and storage expansion co-optimization with
consideration of unit commitment. _2018 Int. Conf. Probabilistic Methods Appl. to_
_Power Syst. PMAPS 2018 - Proc._ 1–6 (2018). doi:10.1109/PMAPS.2018.8440205


50


