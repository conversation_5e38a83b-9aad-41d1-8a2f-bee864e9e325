# Citation Key: schyskaSensitivityPowerSystem2021

---

<PERSON><PERSON>, Volume 5

# Supplemental information The sensitivity of power system expansion models


<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> von <PERSON>, and <PERSON>d
<PERSON>


**S. Supplemental Experimental Procedures**



_S.1. Correlation Lengths of Wind and Solar Power_


35 ~~0~~

800


30 ~~0~~


600

25 ~~0~~



400


200


0



|Col1|Col2|Col3|Col4|Col5|Col6|Col7|
|---|---|---|---|---|---|---|
||||||||
||||||||
|||||1H<br>|||
|||||3H<br>6H|||
|||||12<br>24|H<br>H||
||||||||


0 1000 2000 3000 4000 5000
node separation [km]



20 ~~0~~


150


10 ~~0~~


|Col1|Col2|Col3|Col4|Col5|Col6|
|---|---|---|---|---|---|
|||||||
|||||||
|||||||
|||||||
|||||||
|||||||



200



400 ~~600~~ 800 1000
number of nodes



Figure S1: **The Correlation Lengths exceeds the Node Separation** Left: Integrated
correlation data of wind power capacity factor time series for the PyPSA-Eur 1024 node
setup and different temporal resolutions; the value at largest node separation is referred
to as _correlation length_ ; correlation data is integrated over separation using the trapezoid
rule, for further details see Martin et al. [46] . Right: Average line length versus number of
buses obtained from clustering the original 380-kV network.


In meteorology, there is a clear relation between a phenomenon’s charac

teristic temporal and spatial scale. Usually, one differentiates between the


_microscale_, the _mesoscale_ and the _synoptic scale_ . While the microscale ba

sically includes turbulent motions acting within second to minutes and with


a spatial extent of millimeters to centimeters, the mesoscale covers phenom

ena like thunderstorms, hurricanes, fronts and convective systems occurring


within minutes to days and on several kilometers extent. The synoptic scale


includes high and low pressure systems possibly lasting for up to several days


on 100 to 1000 kilometers extent [63] . Hence, hourly weather time series – as


used here – only contain the variability introduced by mesoscale and synop

tic processes. Sub-hourly microscale processes are filtered out. As mesoscale


and synoptic processes act on relatively large spatial scales, the correlation


lengths of wind speed can consequently be up to several hundreds kilome

ters. For wind speed, Martin et al. [46] found a correlation length of 273 km in


Canada and 368 km in Australia. They computed these correlation lengths


for the high frequency, stochastic, part of the time series by applying a high

pass filter and by removing the seasonal cycle prior to estimating the corre

lation length. However, PSEM have to cope with both aspects of the time


series, the high-frequency (stochastic) _and_ the low-frequency (deterministic)


part. It is the interplay of these two aspects which determines the need for


balancing and the optimal capacity share of the respective resource. Low


frequency variations, in general, exhibit an even higher correlation length as


the stochastic time series. For Europe, Schlott et al. [28] estimated a corre

lation length in wind speed of 300 to 700 km which is likely to increase in


Northern-Central Europe and to decrease around the Mediterranean towards


the end of the century. Without applying any data pre-processing we find


that the spatial extent of the power system (5000 km max) is not sufficient


to determine the correlation length of the wind power capacity factor by


integrating correlation over distance via



_ξ_ ( _r_ _n_ ) =



_n_
�


_k_ =2



1
(24)
2 [(] _[r]_ _[k]_ _[ −]_ _[r]_ _[k][−]_ [1] [)(] _[ρ]_ _[r]_ _[k]_ [ +] _[ ρ]_ _[r]_ _[k][−]_ [1] [)]



for the PyPSA-Eur 1024 node setup (Figure S1 left). For calculating the cor

relation length, the pairwise distance _r_ and correlation _ρ_ between all nodes


have been computed. Correlation data has then been sorted according to


node separation. As _ξ_ ( _r_ _n_ ) does not saturate until the largest separation, the


derived correlation length of approximately 670 km (the integrated correla

tion at the largest node separation) still is an underestimate of the correlation


length which the model experiences.


Compared to wind power, the time series of the solar power availabil

ity exhibit a larger deterministic component: the diurnal cycle. On the


other hand, incremental changes might be larger. The transition from cloud


(shadow) to sun (light) is potentially faster than the transition from windy


times to less windy times. As sunrise and sunset occur at the same time over


large geographical areas, the correlation length of solar power is even larger


as the correlation length of wind power.


For the PyPSA-Eur network, the average distance between two nodes


varies between 60 km for 1000 nodes and 350 km for the 45 node setup (Figure


S1 right). These distances are far below the estimated correlation length


and although the amount of meteorological information lost depends on the


distance of the aggregated nodes, the loss of information – at least of the


kind of information which is relevant for investment decisions – when buses


are aggregated is comparably small. As described above, this is expressed


in a relatively small sensitivity to the spatial resolution. Presumably, this


would only change when time series with a higher temporal resolution would


be used or when an even smaller number of buses would be considered. Time


series with a higher temporal resolution would include a higher share of high


frequency variability originating from microscale meteorological phenomena.


As these phenomena act on smaller spatial scales, the correlation lengths


would decrease, too. When the number of buses would be reduced further, the


distance between the aggregated nodes might exceed the correlation length.


Additionally to their temporal variability, time series can be described


by their amplitude. In the context of renewable resource assessment, the


quality of the resource is commonly described by the average or the sum of


the capacity factor time series, the latter being referred to as _full load hours_ .


This quantity varies as well but on an even lower frequency, on seasonal to


climatological scales. The spatial variance of the full load hours is to a large


extent determined by the orography and the latitude. Locations close to the


shore, for instance, generally exhibit higher wind power full load hours as


locations upcountry. Locations far North are less sunny as locations in the


South and, hence, exhibit lower solar power full load hours. Consequently,


there is no clear relationship between the distance between two nodes and


the difference in the full load hours and the effect of aggregating nodes is


hard to assess. It depends on the specific location.


_S.2. Nomenclature and Cost Assumptions_


Table S1: Nomenclature


_n_, _s_, _t_, _l_ indices for node, generation/storage type, time and transmission link
_C_ _n,s_ capital costs for carrier _s_ at node _n_ [EUR/MW]
CAP CO 2 global limit on CO 2 emissions [tons]
CAP _F_ global limit of the sum of all single transmission line capacities [MWkm]
_C_ _l_ capital costs of transmission capacities at link _l_ [EUR/MWkm]
_D_ _n,t_ demand at node _n_ and time _t_ [MWh]
_e_ _n,s_ CO 2 emissions of generators of technology _s_ at node _n_ [tons/MWh]
_η_ 0 _,s_ standing losses of storage units of technology _s_ [a.u.]
_η_ _n,s_ efficiencies of generators of technology _s_ at node _n_ [a.u.]
_τ_ _n.s_ energy-to-power ratio of storage units of technology _s_ at node _n_ [hours]
_λ_ dual variables
_f_ ¯ _l_ transmission capacities of link _l_ [MW]
_f_ _l,t_ flows over link _l_ at time _t_ [MWh]
_g_ ¯ _n,s_ capacity of generators or storage units of technology _s_ at node _n_ [MW]
_g_ _n,s,t_ dispatch of generators or storage units of technology _s_ at node _n_ and time _t_
˜ [MWh]
_G_ _n,s,t_ maximum power output of generators or storage units of technology _s_ at
node _n_ and time _t_ in units of ¯ _g_ _n,s_
_K_ _n,l_ incidence matrix of the network
_L_ _l_ length of link _l_ [km]
_O_ _n,s_ marginal costs of generation of technology _s_ at node _n_ [EUR/MWh]
_soc_ _n,s,t_ state of charge of storage of technology _s_ at node _n_ and time _t_


Table S2: Cost assumptions for generation and storage technologies, originally based on
estimates from Schr¨oder et al. [64] for the year 2030; fixed operational costs are included in
the capital costs.

Technology Capital Cost Marginal cost Efficiency Lifetime energy-to-power ratio

[EUR/GW/a] [EUR/MWh] dispatch/store [years] [hours]
OCGT 47,235 58.385 0.390 30
Onshore Wind 136,428 0.015 1 25
Offshore Wind 295,041 0.020 1 25
PV 76,486 0.010 1 25
Run-Off-River – 0 1 –
Hydro Reservoir – 0 1 / 1 –
PHS – 0 0,866 / 0,866 –
Hydrogen Storage 195,363 0 0.580 / 0.750 30 168
Battery 120,389 0 0.900 / 0.900 20 6




---

#### **ll**

### Article
# The sensitivity of power system expansion models

Bruno U. Schyska, Alexander



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/schyskaSensitivityPowerSystem2021/schyskaSensitivityPowerSystem2021.pdf-0-7.png)











Kies, Markus Schlott, Lueder von


Bremen, Wided Medjroubi



[<EMAIL>](mailto:<EMAIL>)


Highlights
An error metric allows to quantify
optimization model sensitivities



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/schyskaSensitivityPowerSystem2021/schyskaSensitivityPowerSystem2021.pdf-0-3.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/schyskaSensitivityPowerSystem2021/schyskaSensitivityPowerSystem2021.pdf-0-4.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/schyskaSensitivityPowerSystem2021/schyskaSensitivityPowerSystem2021.pdf-0-5.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/schyskaSensitivityPowerSystem2021/schyskaSensitivityPowerSystem2021.pdf-0-6.png)

Optimization models are a widely used tool in academia. They come with their own
weaknesses and uncertainties. Here, we show that the sensitivity of optimization
models can systematically be described by quantifying the error originating from
uncertain model parameters and model designs. Using the example of power
system models, we emphasize that disclosing sensitivity information can
contribute to openness and transparency in research. Power system optimization
models are found to be especially sensitive to the temporal resolution.



The metric can be applied to both,
model parameters and model
designs


Encourages to disclose sensitivity
information together with the

model results


Power system models are
particularly sensitive to the
temporal resolution



Schyska et al., Joule 5, 2606–2624


October 20, 2021 ª 2021 Elsevier Inc.


[https://doi.org/10.1016/j.joule.2021.07.017](https://doi.org/10.1016/j.joule.2021.07.017)


#### **ll**

##### Article
## The sensitivity of power system expansion models

Bruno U. Schyska, [1][,][3][,] - Alexander Kies, [2] Markus Schlott, [2] Lueder von Bremen, [1] and Wided Medjroubi [1]



SUMMARY

Optimization models are a widely used tool in academia. In order to
build these models, various parameters need to be specified, and
often, simplifications are necessary to ensure the tractability of the
models; both of which introduce uncertainty about the model results.
However, a widely accepted way to quantify how these uncertainties
propagate does not exist. Using the example of power system expansion modeling, we show that uncertainty propagation in optimization
models can systematically be described by quantifying the sensitivity
to different model parameters and model designs. We quantify the
sensitivity based on a misallocation measure with clearly defined
mathematical properties for two prominent examples: the cost of capital and different model resolutions. When used to disclose sensitivity
information in power system studies our approach can contribute to
openness and transparency in power system research. It is found
that power system models are particularly sensitive to the temporal
resolution of the underlying time series.


INTRODUCTION


In order to address the issue of climate change and sustainability, countries around
the world make great efforts to transform their energy systems. In this context, large
shares of weather-dependent renewable power sources need to be integrated into
existing power systems. [1] This is a challenging task and requires a great number of
political decisions. The societal and political decision-making process is accompanied by energy system research, which proposed manifold approaches to support
the integration of renewable resources. [2][,][3] These approaches include the large-scale
integration of storage technologies, [4][,][5] the extension of the transmission grid, [6][,][7] the
over-installation of renewable capacities, [8] the use of meteorological information to
find the optimal spatial deployment, [9] or optimizing the mix of different renewable
generation sources. [10–12] The question whether resources such as nuclear or fossil
power plants with carbon capture and storage can help achieve the decarbonization
of the power system has recently been addressed by Sepulveda et al. [13] and Brown
and Botterund [14] (among others). Kies et al. [15] found that demand-side management
can balance generation-side fluctuations for a renewable share of up to 65% in Europe. Hirth and Muller, [16] Chattopadhyay et al., [17] and Pfenninger et al. [18] proposed
to deploy system-friendly generation assets. Lund and Kempton [19] proved the
usefulness of vehicle-to-grid technologies for the integration of renewables, and
Brown et al. [20] investigated the synergies between the sectors electricity, heat, and
transportation.


Many of these solutions are the result of studies using power system expansion
models (PSEMs). In most cases, studies using PSEMs have the aim to find the least
expensive design of a power system given the constraint that the CO 2 emissions
from power plants may not exceed an upper limit. They are used not only in research


2606 Joule 5, 2606–2624, October 20, 2021 ª 2021 Elsevier Inc.



Context & scale


Power system models are a widely
used tool in academia. They
support the societal and political
decision-making process in the
context of the transformation of

power systems. In particular, they
can propose approaches to ease
the integration of renewable
resources. However, model

results are subject to uncertainties
originating from uncertain model
parameters or model
formulations.


Here, we show that the error
caused by uncertain assumptions
for model parameters or model
designs can be measured by a
mathematically well-defined
concept: a distance measure (or
metric). When applied in a
systematic manner, this metric can
be used to describe the sensitivity
of many kinds of optimization
models. Therefore, it is found that
power system models are
especially sensitive to the
temporal resolution. We
emphasize that our approach
could be used as a standard to

disclose sensitivity information in
power system studies and, hence,
contribute to openness and

transparency.


##### Article

but also for policy advise and as a basis for far-reaching political decisions and societal discussions. [21–23] Considering the potential outreach of the results, the models
and assumptions behind them should be trustworthy and transparent. [24]


Mathematically, PSEMs are often expressed as a linear program (LP) of the general
form:


min x c [T] x (Equation 1)


s:t AxRb


xR0


PSEMs combine several aspects being described by a set of socio-economical, technical, and meteorological parameters. Model formulation and structure must be chosen in a way that ensures the tractability of the model and the ability to deal with long
time series of load and meteorological data. [25][,][26] Together, model structure and the
choice of the parameters define the coefficient matrix A, the vector b, and the objective
coefficients c of the LP. They determine the actual problem: the dimensionality and
shape of the solution space. [27] In turn, the choice of the parameters and the model
structure determine the simulation results. [28–30] If A and/or b and/or c are modified,
the solution of the LP changes depending on the model’s sensitivity to the respective
parameters and/or structure. Consequently, making good assumptions and reasonable choices for/of the model parameters and structure is of crucial importance. The
uncertainty related to these choices leads to additional uncertainty related to the
model results. [31–33] Having a deep understanding of this process appears fundamental
to the public debate about the conversion of the power system.


Based on a global sensitivity analysis, [34] it was found that the uncertainty of economic
parameters has the highest influence on the results of an energy model. Similarly, a
study [35] investigated the robustness of a fully renewable power system model of
France to uncertainties in the future cost of generation technologies. They
found that although the optimal generation mix clearly depends on the
respective cost for the different technologies, overall system costs are relatively
insensitive. Recently, Nacken et al. [36] applied a method called modeling to generate
alternatives [37–39] (MGA) to a future German energy supply and showed that it produces a number of significantly different energy scenarios. MGA is based on changing the PSEM structure by setting the cost-optimal objective value plus some slack as
a new constraint and exploring the resulting solution space. Another study [27] used a
similar method to explore the near-optimal solution space of a cost-optimized European power system. They observed a high variance in the deployment of individual
components near the optimal solution. Another not directly related kind of uncertainty is introduced by the use of learning curves, which are commonly applied to account for cost reductions of certain technologies in pathway-optimizations. [40] The
term learning curve refers to the concept according to which cumulative experience
in the production of a product increases efficiency in the use of inputs and leads to
cost reductions. These learning curves are typically non-linear and introduce nonconvexities to the solution space, which are hard to resolve. [41] A graphical depiction
of this effect is shown in Figure 1. For an overview of methods applied in the context
of uncertainty in power system modeling, see Yue et al. [42]


The aim of this paper is the concise description of the implications of uncertainties on
the results of PSEMs. We do this by analyzing the sensitivity of a common power system expansion model to different model parameters and structures based on a


#### **ll**

1 German Aerospace Center (DLR), Institute of
Networked Energy Systems, Oldenburg,
Germany


2 Frankfurt Institute for Advanced Studies, Goethe
University, Frankfurt, Germany


3 Lead contact


[*Correspondence: <EMAIL>](mailto:<EMAIL>)


[https://doi.org/10.1016/j.joule.2021.07.017](https://doi.org/10.1016/j.joule.2021.07.017)


Joule 5, 2606–2624, October 20, 2021 2607


#### **ll**

Cost

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/schyskaSensitivityPowerSystem2021/schyskaSensitivityPowerSystem2021.pdf-3-3.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/schyskaSensitivityPowerSystem2021/schyskaSensitivityPowerSystem2021.pdf-3-4.png)


Local Minimum Global Minimum Capacity
without investment
into tech with steep
learning curve


Figure 1. Learning curves introduce additional uncertainty
Schematic representation of non-convexities caused by learning curves. If a solver starts in the left
part, it might run into the local optimum, whereas investments into the technology reduce cost in
the medium-term and lead to the global optimum.


metric, which expresses the error of PSEM results when deciding for one of two plausible assumptions (in the following referred to as scenarios) a i and a j as the increase
in the objective function values G:


[½][DG][�] [a] [j]
M [a] a [i] j [=] [½][DG][�] [a] [i] [ +] 2 (Equation 2)


Commonly, model parameters are derived from a collection of different data sources. The background of the approach introduced here is that the assumptions
made for setting up a model, which shall be used to make decisions, are based on
data observed at a different time from when the power system is planned. Meaning
that planning and operation phase of energy system components are typically separated by long times, during which assumptions or knowledge can dramatically
change (see Figure 2). Here, we assume that planning and operation phase can be
expressed by two different scenarios a i and a j . However, this approach can easily
be generalized to measure the sensitivity of linear programs to any assumptions
made about model parameters and/or the model structure. It allows to cross-validate input data and to find representative datasets. By exemplarily quantifying
the sensitivity of a simple PSEM to the definition of the cost of capital (as an example
for model parameters) as well as to the spatial and temporal resolution of the model
(as an example for the model structure), this study introduces a standardized way to
disclose the uncertainties of power system studies and, hence, contributes to
increasing transparency in power system research.


RESULTS


The sensitivity to the cost of capital—an introductory example
In the formulation used here, the PSEM (1) consists of two problems (also referred to
as levels). While the design problem minimizes the investment cost in generation,
storage, and transmission capacities keeping all capacities within given bounds,
the operation problem minimizes the operational cost of the power system ensuring
that electricity generation meets the demand and keeps the generation lower or
equal to the capacities derived from the design problem. For volatile renewable resources—such as wind and solar PV—the available dispatchable capacity is


2608 Joule 5, 2606–2624, October 20, 2021


##### Article


##### Article

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/schyskaSensitivityPowerSystem2021/schyskaSensitivityPowerSystem2021.pdf-4-3.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/schyskaSensitivityPowerSystem2021/schyskaSensitivityPowerSystem2021.pdf-4-4.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/schyskaSensitivityPowerSystem2021/schyskaSensitivityPowerSystem2021.pdf-4-5.png)

**2021** **2022** **2023** **2024** **2025** **2026** **2027** **2028** **2029**


**Assumptions**
**Planning Phase** **Operational Phase**
**typically change**


Figure 2. Planning and operation phase of energy system components are typically several years

apart
Schematic representation of the original idea of our approach.


additionally limited by the prevailing meteorological conditions, i.e., the capacity
factor time series. Furthermore, the consistency of the state of charge of storage

units must be ensured.


Consequently, the capital costs for all generation, storage, and transmission assets
are one of several major parameters to this PSEM, a subset of the objective coefficients c. These capital costs are influenced by the cost of capital, i.e., the costs an
investor has to defray for his credits. It has been shown that for wind power projects,
these costs of capital vary significantly between European countries. [43] Let us assume
that we would like to investigate the error in the results of a simple PSEM originating
from uncertainties in the definition of these costs of capital. In order to do this, we
define two plausible realizations: the first scenario (hom) assumes a homogeneous
(constant) distribution of the cost of capital across the European countries. The second scenario (dia) takes regional differences into account. The cost of capital is set as
the weighted average cost of capital (WACC) reported from the diacore project. [43] In
this scenario, the cost of capital is a function of the country. It ranges from 12% in the
South-Eastern European Countries to only 4% in Germany (Figure 3).


The PSEM used for this example is the PyPSA-Eur model published by Ho¨ rsch et al. [44]

in its ‘‘one-node-per-country’’ setup. It consists of one node per country in the European Union plus Norway, Switzerland, the United Kingdom, Croatia, Serbia, and
Bosnia, and it includes time series of capacity factors for onshore and offshore—
where applicable—wind power as well as solar PV power and time series of electricity demand for each substation. Furthermore, time series for the inflow into hydro
reservoirs and run-off-river power plants and upper bounds for the extendable generation capacity per renewable technology and substation are included. Nodes are
connected via simplified high-voltage direct current transmission links. A rather
limited number of technologies is considered: wind (onshore + offshore), solar photovoltaics (PV), open-cycle gas turbines (OCGT), hydro (run-off-river, reservoirs, and
pumped hydro), as well as large-scale battery and hydrogen storage units. Investment in these technologies and the operation are optimized over a year (2013) in
hourly resolution assuming perfect foresight, inelastic demands, as well as limited
allowance of transmission capacity expansion and CO 2 emissions. For further details, see power system model and data.


For the same setup, a study [29] has shown that the two scenarios hom and dia lead to
significantly different solutions for the cost-optimal generation capacity layout x hom [�]
and x dia [�] [. In particular, the optimal solution for the dia scenario contains a larger share]


#### **ll**

Joule 5, 2606–2624, October 20, 2021 2609


#### **ll**

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/schyskaSensitivityPowerSystem2021/schyskaSensitivityPowerSystem2021.pdf-5-3.png)

Figure 3. Inhomogeneous spatial distribution of the cost of capital
Weighted average cost of capital taken from Noothout et al., [43] adopted from Schyska and Kies. [29]


of offshore wind power, whereas the shares of onshore wind power, solar photovoltaics (PV), and OCGT decrease compared with the hom scenario. However, the
difference in the objective function values G [hom] 0 and G [dia] 0 is small (1:4 MWhEUR [, approxi-]
mately 2% of the objective function values). Now, let us assume that one of the
two scenarios shall be taken as the basis for the decision on where and how many
large-scale PV farms should be erected. From the pure differences in x [�] and G 0,
one cannot tell with certainty whether this is a good decision or not. On the one
hand, the error when deciding for one scenario might indeed be as large as the difference the optimal capacity layout x [�] indicates. In this case, a great number of suboptimal investments would be made, leading to unnecessary high costs in the end,
because the costs of capital, in reality, are different than the ones used for the planning. The difference in the objective function values would be small only by chance.
On the other hand, the error might actually be small because the solution space is
flat near the optimal point or a secondary optimum exists. Here, both solutions
are almost equally valid. x hom [�] [would solve the program from scenario][ dia][ at only little]
additional cost and vice versa. The decision to erect the PV farms either as proposed
by the hom scenario or as proposed by the dia scenario is of minor importance.


In order to determine which of the two cases is true, one could set the solution x hom [�]
as lower bound to the LP from scenario dia and vice versa, by adding the following

constraints to each of the two models:


2610 Joule 5, 2606–2624, October 20, 2021


##### Article


##### Article

**Cost**


**Cost**


#### **ll** unconstrained constrained



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/schyskaSensitivityPowerSystem2021/schyskaSensitivityPowerSystem2021.pdf-6-3.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/schyskaSensitivityPowerSystem2021/schyskaSensitivityPowerSystem2021.pdf-6-4.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/schyskaSensitivityPowerSystem2021/schyskaSensitivityPowerSystem2021.pdf-6-5.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/schyskaSensitivityPowerSystem2021/schyskaSensitivityPowerSystem2021.pdf-6-6.png)

**Cost**



**Capacity**





**Capacity**



**Capacity**



Figure 4. Measuring the error in optimization model results originating from uncertainties in model parameters and design
First, the original (unconstrained) problems using assumptions a i and a j are solved (left columns). Second, the solutions of these simulations are used for
formulating additional constraints to the respective other problem (constrained problems, right column). The resulting differences in the cost ½DG� [a] [i] =
G [a] a [i] j [�] [G] 0 [a] [i] [can be used to measure the error. See text for further details.]


x hom=dia Rx dia [�] =hom (Equation 3)


The program constructed, such as this, defines an upper bound to the original program. Hence, the optimal objective function value of that program is higher or
equal to the objective function value of the original solution. If the second case
is true, this rise in the objective function value, i.e., the levelized cost of electricity,
should be small. However, invoking constraint (Equation 3) should cause a large
additional cost in the first case. Then, the solution space is comparably steep,
and/or x hom [�] [is not a secondary optimum of the LP from scenario][ dia][. Constraint]
(Equation 3) significantly messes up the optimization, leading to significantly
higher costs. This cost increase can be measured via ½DG� [hom] = G [hom] dia [�] [G] 0 [hom], where
G [hom] dia denotes the optimal value of the objective function with lower bounds
defined by the optimal solution of the dia scenario. As one cannot tell which of
the two scenarios is closer to reality (let us assume both scenarios are equally probable), this procedure should be performed in both directions (Figure 4). The potential error when deciding for one of the two scenarios can then be quantified as
follows:


Joule 5, 2606–2624, October 20, 2021 2611


#### **ll**


##### Article



1.0


0.8


0.6


0.4


0.2


0.0



2.0


1.5


1.0


0.5


0.0



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/schyskaSensitivityPowerSystem2021/schyskaSensitivityPowerSystem2021.pdf-7-3.png)

carrier



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/schyskaSensitivityPowerSystem2021/schyskaSensitivityPowerSystem2021.pdf-7-4.png)

carrier



Figure 5. The spatial distribution of the cost of capital affects the cost-optimal investment
Investment in generation capacity relative to the unconstrained solution for the homogeneous scenario constrained with the solution of the diacore
scenario (left, blue) and for the diacore scenario constrained with the solution of the homogeneous case (right, orange), crosses indicate the minimum
investment for each generation source.




[½][DG][�] [dia]
M [hom] dia = [½][DG][�] [hom] [ +]



2



(Equation 4)
= [1] 2 �G [hom] dia [�] [G] [hom] 0 + G [dia] hom [�] [G] [dia] 0 �



After computing G [hom] dia and G [dia] hom [, one finds][ M] [hom] dia = 2:3 MWhEUR ~~[.]~~ [ This is 3.3% (3.2%) of]
G [hom] 0 (G [dia] 0 [). Recall that the difference in][ G] [hom] 0 and G [dia] 0 suggests a smaller error of

EUR
only 1:4 MWh ~~[.]~~ [ Here, we are able to show that the potential error indeed is higher]
than this difference.


This higher error can be explained by taking a look at the shape of the solution space: in
general, the solution space for the design problem of the expansion problem is steeper
than for the operation problems. This means that slight changes in the capacity layout
may lead to significantly different investment cost, whereas there potentially exist
many ways to solve the operational problem with similar cost. This effect is enhanced
if additional regional differences in the cost of capital are considered. Building an
offshore wind park in Germany or in Greece, for example, makes a bigger difference
now as it made in the homogeneous case. For this example, we modified the regional
distribution of the cost of capital cost but kept the nodal loads and the weather time
series, which determine the availability of the volatile renewable resources, unchanged. Consequently, we find that both solutions of the unconstrained problems
x dia [�] [and][ x] hom [�] [also solve the operational problems of the respective other problem.]
All demands can be met with the prescribed capacities and no changes to the capacity
layouts are necessary (Figure 5). However, since the two capacity layouts are quite
different (as reported earlier) the investment cost in the constrained cases increases
by 6% and 5%, respectively, compared with the unconstrained cases due to the
different cost assumptions in the two scenarios (Figure 6).


Measuring the sensitivity of LPs to modifications of their model structure
A distinct strength of the proposed approach is that it can be used to measure the
difference between the optimization results of any pair of LPs, which either (1)
have the same decision variables, (2) have a common subset of decision variables,
or (3) whose decision variables can be mapped on each other. Hence, it allows to systematically investigate the potential errors for a great number of scenarios. If all of
these scenarios are related to the same aspect of the LP, this process can be


2612 Joule 5, 2606–2624, October 20, 2021


##### Article



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/schyskaSensitivityPowerSystem2021/schyskaSensitivityPowerSystem2021.pdf-8-3.png)









Figure 6. Investment costs increase due to different cost of capital
Investment for the two scenarios hom and dia in the unconstrained case (blue) and the constrained

formulation (red).


understood as investigating the sensitivity of the considered program to modifications of that specific aspect. If errors are small overall, the sensitivity of the LP can
also be considered small. The other way around, strong sensitivities should express
themselves in large errors. As explained earlier, this not only applies to model parameters but also to the model structure, meaning that M can also measure the error
(or the sensitivity) of a LP to changes in its own model structure. Here, we demonstrate this by investigating the sensitivity of the PyPSA-Eur [44] model (see the sensitivity to the cost of capital –— an introductory example and power system model
and data) to modifications of its temporal and spatial resolution. In its full spatial
and temporal resolution, PyPSA-Eur consists of 3,567 substations and 6,047 transmission lines and it covers 1 year in hourly resolution, i.e., 8,760 time steps. In this
resolution, the PSEM can hardly be solved. In order to reduce the spatial resolution,
the original network has been scaled down to 45, 64, 90, and 128 substations using
the network clustering approach introduced by Ho¨ rsch and Brown. [45] The temporal
resolution has been reduced by averaging the parameter time series over consecutive time spans of length t˛f3; 6; 12; 24g h as described in reducing the spatial and
temporal resolution of the model.


The sensitivity measure M for all possible combinations of these different resolutions
(both spatial and temporal) exhibits a clear pattern (Figure 7). Basically, it can be
divided into three different blocks: two blocks of (relatively) low sensitivity where
M%4:2 EUR/MWh and one of (relatively) high sensitivity where MR7:2 EUR/MWh.
The first block of low sensitivity contains all combinations of scenarios with a temporal resolution higher than 6 h, i.e., (N, 1H), (N, 3H), and (N, 6H), independent from the
spatial resolution N. The second block of low sensitivity contains all combination of
scenarios with a temporal resolution smaller than 12 h—again independent from the
spatial resolution, and the block of high sensitivity contains all combinations of scenarios where one scenario has high (% 6 h) temporal resolution and the other scenario has low temporal resolution (R 12 h). From this definition of blocks, one can
already see that the expansion problem is much less sensitive to changes in the
spatial resolution as it is to changes in the temporal resolution. For instance, the
sensitivity of the problem with hourly temporal resolution to increases in the spatial
resolution from 45 nodes up to 128 nodes is below 4 EUR/MWh. In contrast, the
sensitivity of the 128-node setup to reductions in the temporal resolution from hourly to minimum 12-hourly reaches a maximum value of almost 13 EUR/MWh.


#### **ll**

Joule 5, 2606–2624, October 20, 2021 2613


#### **ll**

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/schyskaSensitivityPowerSystem2021/schyskaSensitivityPowerSystem2021.pdf-9-3.png)

Figure 7. PSEM are particularly sensitive to the temporal resolution
Sensitivity heatmap [EUR/MWh]; in the tick labels, the number left of the hyphen indicates the
number of nodes, the number right of the hyphen indicates the temporal resolution in hours.


The reasons for this are 2-fold. First of all, increasing the spatial resolution does not
necessarily lead to a higher degree of information in the time series, and vice versa.
Consequently, the results obtained from models with different spatial resolutions do
not differ much. This phenomenon is of meteorological nature: hourly wind power
and solar PV capacity factor time series exhibit large correlation lengths. [28][,][46] Consequently, aggregating nodes, which are geographically close, does not lead to a
significant loss of information about the temporal characteristics of the aggregated
nodes. For a detailed investigation of the correlation lengths of wind and solar PV in
the PyPSA-Eur model see Section S1 in the supplemental information. In contrast,
modifying the temporal resolution potentially leads to significant differences in
the optimal capacity deployment, especially when the temporal resolution ‘‘jumps’’
from one of the blocks we defined earlier to another. The main reason for this is that
downsampling the time series via averaging removes part of the temporal variability.
In general, a rolling window averaging can be understood as a filter. For instance,
averaging a time series with a rolling 24-h window filters out most of the sub-24 h
variability of the time series, including the diurnal cycle (if present). If such a filter
is applied to both the capacity factor time series and the demand time series, the residual load gets implicitly filtered as well. As a consequence, any storage technology
meant to flatten the sub-12 h variability of the residual load time series would no
longer be needed (because there is no sub-12 h variability).


In general, storage technologies can be assigned to a characteristic variability in the
residual load time series via their energy-to-power ratio r, i.e., the number of hours
they can store (dispatch) electricity at full power when starting from empty (full) storage. In the setup used here, batteries are characterized by an energy-to-power ratio
of r = 6 h. They are meant to balance discrepancies between demand and availability, which occur on the intra-day scale. As described earlier, these discrepancies
disappear when the demand and capacity factor time series are down sampled to
a lower resolution. Consistently, no battery storage devices are optimally deployed
in the model setups with a temporal resolution below 6 h (Figure 8). In contrast,
hydrogen cavern storage units exhibit an energy-to-power ratio of r = 168 h, making


2614 Joule 5, 2606–2624, October 20, 2021


##### Article


#### **ll**
##### Article

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/schyskaSensitivityPowerSystem2021/schyskaSensitivityPowerSystem2021.pdf-10-3.png)

Figure 8. The temporal resolution affects the optimal deployment of storage capacities
Optimal investment in generation and storage capacity [billion Euro] for the 128-node network and for different temporal resolutions of the exogeneous

parameter time series.


them a weekly storage. As the weekly variability is still present in the down-sampled
time series, hydrogen storage devices are still useful.


In broad terms, filtering the high-frequency part of a time series’ variability can
be understood as removing scatter. This, in turn, also increases the correlation between the time series, again not only between the availability time series but also
between the availability and the demand. Apparently, this rise in correlation mainly
increases the system-friendliness of solar PV. Its investment share grows from
approximately 40 billion Euro for the 3-hourly time series to more than 60 billion
Euro for the 24-hourly time series (Figure 8). In turn, the importance of offshore
wind power, which is mainly used to cover the baseload in the highly resolved model,
decreases because the filtered time series no longer contain any non-baseload part.
The offshore wind power share drops from approximately 23 billion Euro to zero.
Overall, down sampling time series leads to reduced cost and a significantly
different capacity mix. Setting this capacity mix as lower bound to the highly
resolved model causes large additional costs, mainly because the model deploys
much more solar PV than it would otherwise deploy. Vice versa, the downscaled
model with the additional constraints from the highly resolved model deploys
more offshore wind power and battery storage units than it would deploy in the unconstrained case. Overall, this is expressed in a high sensitivity.


However, there is one effect counteracting this phenomenon. This effect appears
when the spatial resolution is modified in addition to the temporal resolution. In
this case, averaging takes not only part in the temporal dimension but also in the
spatial dimension. More precisely, models with a higher spatial resolution experience less averaging on the spatial scale than models with a coarser spatial resolution—assuming that the models’ resolutions are in any case below the resolution
of the underlying weather data. This potentially leads to higher capacity factors in
the highly resolved case. When transmission capacity is sufficiently available and/or
the network is sufficiently meshed, higher capacity factors require less generation
capacity as the model with lower spatial resolution. Setting these relatively low


Joule 5, 2606–2624, October 20, 2021 2615


#### **ll**

capacities as lower bounds to the coarser model does not lead to any additional
costs, because the optimal capacities are above these bounds anyhow. The lower
bounds are non-binding. Consequently, the sensitivity is determined by the additional cost arising from setting the optimal capacities of the coarser model as lower
bounds to the finer resolved model. Apparently, these additional costs are small
compared with the costs arising from modifying the temporal resolution. When
the spatial resolution is not modified, both differences in the definition of M (Equation 4) are non-zero. This causes the sensitivity between two models of the same
spatial but different temporal resolutions, i.e., (N, 1H) and (N, 24H) to be larger
as between two models of different spatial and temporal resolutions (N, 1H) and
(M, 24H).


DISCUSSION


In this study, we introduced a method to study the sensitivity of power system optimization models to different parameter scenarios and model structures. The core of
this method is an error measure M originally designed to measure the error in the
results of power system expansion studies originating from the potential discrepancies between the assumptions made for the planning process and the experiences
made during the operation phase of power system components. Although they
might partly be corrected over time, decisions made based on the model results
might be suboptimal in case assumptions turn out to be different from what can
be observed in the real world. The model derives a solution, which either is more
expensive as the ‘‘real’’ optimal solution or which even does not fulfill the constraints
under real world conditions. Investments might be ‘‘misallocated.’’ Therefore, M can
be understood as measuring the misallocation of investments. As an example, we
investigated the error originating from an inaccurate definition of the cost of capital
of the power generation assets.


In the subsequent section, it has been shown that the original approach can easily be
generalized to measure the sensitivity of any linear program to both changes in
model parameters and/or modifications of the model structure. In particular, M
quantitatively measures the sensitivity of any pair of similar linear programs on a specific set of scenarios for model parameters and/or the model structure and allows to
compare with a great(er) number of scenarios. In fact, M fulfills the properties of a
distance measure (or metric): it is positive definite


M [a] a [i] j [R][0] (Equation 5)


because G [a] 0 [i] [%][G] a [a] [i] j [, symmetric to the order of the scenarios]


M [a] a [i] j [=][ M] a [a] [j] i (Equation 6)


and fulfills the triangle inequality


M [a] a [i] j [%][ M] a [a] [i] k [+][ M] a [a] [k] j [:] (Equation 7)


The identity of indiscernibles


M [a] a [i] j [=][ 0][4][a] [i] [ =][ a] [�] j (Equation 8)


is fulfilled if a unique solution of the program exists.


In order to compute M, new constraints must be added to the original program. But
since these constraints only define a lower bound for the decision variables, which
the program tries to minimize, this cannot cause any infeasibilities. A mathematical
proof is given in computing the misallocation metric. Nonetheless, adding the


2616 Joule 5, 2606–2624, October 20, 2021


##### Article


##### Article

constraints might affect the convergence of the program. This is hard to estimate
and probably crystallizes when the approach is applied.


For testing the developed methodology, we used a simplified setup of a European
power system model. For instance, we limited the available technologies for electricity generation to OCGT, wind, solar, and hydro power. Other technologies
such as nuclear or combined-cycle gas turbines have not been considered. Furthermore, no coupling of the electricity sector to other sectors has been modeled and
rather straightforward approaches to reduce the temporal resolution of the model
have been applied, although more sophisticated methods exist (see Hoffmann
et al. [47] for an overview). The problems of considering storage units in models with
low temporal resolution could be addressed by the approaches of Gabrielli
et al. [48] and de Guibert et al., [49] for instance;however, the explanations for the
described sensitivities are rather general. We believe that including more technologies and/or incorporating sector coupling would not influence these general findings and the general applicability of the proposed method.


Nevertheless, it seems reasonable to compare and combine the proposed methods
with the approach of Nacken et al [36] or the methods to investigate the shape of the
solution space proposed by Neumann and Brown [27] to study the uncertainty of energy system models in future research. The sensitivity to modifications in the temporal resolution could be further investigated by applying the approaches of coupling
design periods introduced by Gabrielli et al. [48] and Kotzur et al. [50] or the time series
aggregation approach based on hierarchical clustering with connectivity published
by Pineda and Morales. [51] Furthermore, it is left open to investigate the robustness of
M, for instance, to correlated parameters or reductions in the cost of key technologies, the effect of single technologies on the sensitivity of the PSEM, and the
behavior of M in multi-stage optimization problems, which allow to adapt the solu
tion over time.


As mentioned earlier, various approaches to address uncertainty and/or sensitivities
of PSEM exist. These approaches can be divided into two approaches, which are to
(1) assess the uncertainty based on a comparably small number of deterministic
scenarios and (2) use a more systematic (statistical) approach. [42] According to Yue
et al., [42] most power system expansion studies investigate the sensitivity of their
results following the first approach. Here, the results obtained from a small set of
scenarios are compared against a chosen reference. Despite some obvious shortcomings compared with statistical approaches such as Monte-Carlo simulations,
MGA, or robust optimization techniques regarding its statistical robustness, this
approach has the advantage of being much less computationally expensive. Furthermore, the data needed to derive statistically robust information—as required for
Monte-Carlo simulations for instance—are seldom available in sufficient quality
and quantity. On the other hand, choosing a reference often appears arbitrary. Usually, there is no measured observation, i.e., no ‘‘reality’’ that the model results could
be compared against, but rather a small number of more or less equally probable
and plausible scenarios. Our approach can be classified somewhere in between
the two groups of approaches to investigate sensitivities in PSEM. On the one
hand, it allows for the situation of having only few scenarios and limited computational resources available. On the other hand, it defines a systematic approach
and a sensitivity measure, which exhibits clearly defined mathematical properties
(those of a metric). Being comparable with the methods applied by Nacken et al [36]

and Neumann and Brown [27] in the sense of modifying and re-solving the original
optimization problem, M quantifies the sensitivity by a metric instead of exploring


#### **ll**

Joule 5, 2606–2624, October 20, 2021 2617


#### **ll**

it visually. Furthermore, it can be applied to almost any kind of optimization model.
As such, the obstacles for establishing our approach as a standard to disclose sensitivity information in power system research seem to be comparably low.


In particular, the following conclusions can be drawn from our experiments:


(1) For the (few) scenarios of the spatial resolution used in this study, we find a
relatively small sensitivity to increases and decreases in the number of nodes.
As long as the temporal resolution of the underlying time series does not
include any information about microscale meteorological processes (which
hourly time series usually do not), modeling the European power system
with only a few dozens of nodes seems reasonable. This may change when
more detailed information about the regional distribution of the demand
and demand-side flexibilities are included.
(2) In contrast, the temporal resolution of the underlying time series must be chosen carefully, especially with storage devices involved. The power system
model shows the highest sensitivity to modifications of the temporal resolution across the characteristic storage horizon of the storage devices. As a
conclusion, the temporal resolution should be chosen such that the variability,
which the storage devices are supposed to balance, is well represented.
Particularly, a temporal resolution lower than 6-hourly might cause
misleading results when daily storage units—such as batteries—are considered. Contrarily, time series with daily resolution might be appropriate
when only weekly and/or seasonal storage types are part of the model.
(3) We showed that common PSEM exhibit significant sensitivities, in-line with
many other studies in the field. Considering the potential political and societal impacts of power system studies, it appears crucial to quantify and report
these model sensitivities and uncertainties along with the model results. Our
approach is one possible, easily applicable way to achieve this for a great
number of applications, not only PSEMs.


EXPERIMENTAL PROCEDURES


Resource availability
Lead contact

Further information and requests for resources should be directed to and will be ful[filled by the lead contact, Bruno Schyska (<EMAIL>).](mailto:<EMAIL>)


Materials availability
This study did not generate new unique materials.


Data and code availability
The code used to generate the results reported in this study will be made available
[on the gitlab page of DLR, Institute of Networked Energy Systems (https://gitlab.](https://gitlab.com/dlr-ve)
[com/dlr-ve).](https://gitlab.com/dlr-ve)


Power system model and data
In this study, we investigate the sensitivity of a common power system expansion
problem to (1) different cost of capital assumptions and (2) different temporal and
spatial resolutions. The model is formulated as follows [20][,][28][,][52] :


##### Article



min



C n;s, ~~g~~ n;s + X
n;s l



XO n;s,g n;s;t (Equation 9)

n;s;t



~~g~~ ;g;f ;f X



C l,L l,f l + X
l n s t



2618 Joule 5, 2606–2624, October 20, 2021


##### Article



s: t: X



g n;s;t � D n;t = X
s l



K n;l,f l (Equation 10)

l


#### **ll**

Joule 5, 2606–2624, October 20, 2021 2619



G [�] n;s;t ~~[g][%]~~ [n][;][ sg] [n][;][s][;][t] [ %][ ~][G] [n][;][s][;][t] [,] ~~[g]~~ n;s ; cn; t (Equation 11)


soc n;s;t = �1 � h [l] n;s �,soc n;s;t�1 + h [u] n;s [uptake] n;s;t ; cn; s; t>1 (Equation 12)


soc n;s;0 = soc n;s;jtj ; cn; s (Equation 13)


0 % soc n;s;t % t n;s, ~~g~~ n;s (Equation 14)


jf l ðtÞj%f l ; cl (Equation 15)



X

l



f l, L l %CAP F (Equation 16)



1

X ~~,~~ g n;s;t,e n;s %CAP CO 2 (Equation 17)

n;s;t h n;s



~~g~~ n;s;t ; g n;s ; f l ; soc n;s;t R0 (Equation 18)


The used symbols are explained in Table S1. Constraint (Equation 10) ensures that
the generation meets the demand. Constraints (Equations 11, 12, 13, 14, and 15)
define the bounds for dispatch and transmission. Here, the potential generation
~~g~~ n;s ðtÞ describes the resource availability in case of fluctuating renewable generation
facilities. Constraints Equation 12 and Equation 13 ensure the consistency of the
state of charge as well as the cyclic use of storage units, where in Equation 12,
uptake n;s;t refers to the net energy uptake of the storage unit given as follows:


uptake n;s;t = h 1,g n;s;t;store � h [�] 2 [1] [,][g] [n][;][s][;][t][;][dispatch] [ +][ inflow] [n][;][s][;][t] [ �] [spillage] n;s;t


Upper bounds for system-wide transmission capacities and CO 2 emissions are
defined in Equations 16 and 17, respectively. For this paper, no global limit for
the net transfer capacities is assumed. In-line with European emission reduction targets, CO 2 emissions are limited to 5% of the historic level of 1990. Upper limits of
generation capacities have been derived by restricting the available area to agricultural areas and forest and semi natural areas given in the CORINE Land Cover dataset [53] and by excluding all nature reserves and restricted areas. [54] From the available
area, the maximally extendable generation capacity has been computed via fixed
densities of 3 MW per square kilometer for onshore wind and 1.45 MW per square
kilometer for solar PV, respectively. [44]


Capacity factor time series are commonly derived from reanalysis datasets. [55] In
PyPSA-Eur, time series for wind power capacity factors and the inflow to hydroelectric power plants are derived based on the ERA5 reanalysis. [56] Onshore and offshore
wind power capacity factors have been computed using the power curves of a 3 MW
Vestas V112 with 80 m hub height and the NREL Reference Turbine with 5 MW at
90 m hub height, respectively. Solar PV capacity factor time series have been
computed from the Heliosat (SARAH) surface radiation dataset [57] using the electric
model of Huld et al. [58] and the electrical parameters of the crystalline silicon
panel fitted in the same publication. All solar panels are assumed to face south at
a tilting angle of 35 degrees. Hourly electricity demand for all European countries
has been obtained from the European Network of Transmission System Operators


#### **ll**

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/schyskaSensitivityPowerSystem2021/schyskaSensitivityPowerSystem2021.pdf-15-3.png)

(ENTSO-E) [59] and assigned to substations via a linear regression of the GDP and the
population. For further details on the dataset and the underlying methodology
please see Ho¨ rsch and Brown. [45]


From this dataset, the parameters for the corresponding PSEM (9)–(17) have been
defined. Therefore, we fixed the nominal power of all hydro power plants and
pumped hydro storage units to the values reported by Kies et al., [60] whereas the
nominal power of wind, solar PV, and OCGT power plants can be expanded within
given bounds. Additionally, we consider two generic storage types with fixed powerto-energy ratio r:


(1) batteries: r = 6 h

(2) hydrogen storage: r = 168 h


Their nominal power can be expanded as well. For each technology, the investment
and operational costs depicted in Table S2 have been used.


The topology of the network clustered to 128 nodes is shown in Figure 9. The time
series aggregation method described in reducing the spatial and temporal resolution of the model is then applied to these clustered networks. Although most
PSEM aim at finding optimal solutions for power system design, they may significantly vary in structure and in scope. For a list of models, see, for instance, the
[Open Energy Platform (https://openenergy-platform.org/factsheets/models/).](https://openenergy-platform.org/factsheets/models/)


Reducing the spatial and temporal resolution of the model
In general, two types of time series aggregation methods can be distinguished. The
first one aims at decreasing the number of time steps by reducing the resolution of
the parameter time series. The down sampling approach described below, for
instance, can be assigned to this class.


The second class aims at decreasing the number of time steps, whereas keeping the
temporal resolution unchanged. In this way, as much of the temporal variability as


2620 Joule 5, 2606–2624, October 20, 2021


##### Article


##### Article



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/schyskaSensitivityPowerSystem2021/schyskaSensitivityPowerSystem2021.pdf-16-3.png)



















M





Figure 10. Computing the misallocation metric M
The sensitivity of a linear program to two different scenarios for model parameters and/or the
model structure can be quantified by constraining both optimizations with the results of the
respective other program.


possible shall be conserved. Usually, this is achieved by selecting a limited number
of representative design periods from the original time series. Depending on the periods’ lengths, the variability on different temporal scales can be retained. This, of
course, breaks the natural order of the time steps and, consequently, no variability
on timescales longer than the periods’ lengths can be pictured. Hence, ways need
to be found, which allow to model the variability on long timescales (months-seasons), which is represented by the natural inflow into hydro power plants or the
seasonal cycle in electricity demand. For an overview of these methodologies, see
Pfenninger [25] and Kotzur et al. [61]


In order to account for different time step intervals, weightings need to be defined
for each time step considered in the expansion problem: first, in the objective (w t in
Equation 9), and second, in the definition of the storage units’ state of charge (u t in
Equation 12).


For this study, we applied a simple down sampling technique. It averages the original exogenous parameter time series over consecutive time spans of length t.
Hence, it yields [T] t [time steps at constant intervals. The snapshot weightings][ w] [t] [ and]

u t are set to t.


The spatial resolution of the PSEM is modified by applying the network clustering
approach introduced by Ho¨ rsch and Brown. [45] The original model is clustered to
45, 64, 90, and 128 nodes.



Computing the misallocation metric
For each of the scenarios a i the model (9)–(17) is first solved without any lower
bounds to the nominal power. In this paper, the additional constraints (Equation 3)
are applied to variables representing long-term investment decisions, i.e., generation, storage, and transmission capacities. Meaning, the resulting solution vector
for the cost-optimal capacities ½ ~~g~~ ~~[�]~~ n;s [�] [a] 0 [i] [is then set as the lower bound to the nominal]
power for the respective partner problem a j (Figure 10):


a j a i

~~g~~ n;s R ~~g~~ ~~[�]~~ n;s (Equation 19)

" # a " # 0



# aa j


#### **ll**

Joule 5, 2606–2624, October 20, 2021 2621



# a0 i



(Equation 19)

0



"



~~g~~ ~~[�]~~ n;s



~~g~~ n;s



R

a i


#### **ll**

Following this procedure in both directions delivers the terms of Equation 4. For this
study, constraints (Equation 19) have been applied to all generation and storage
assets. The resulting linear programs have been solved using the Python for Power System Analysis tool (PyPSA) [62] and the numerical solver Gurobi on the high-performance
cluster infrastructure of the German Aerospace Center (DLR) in Oldenburg, Germany.


As mentioned earlier, invoking constraints (Equation 19) should not cause the optimization model to become infeasible. This can be proven as follows: let us assume


� a i
the original problem is feasible, and let f½g [�] n;s;t [�] [a] 0 [i] [;] ~~[½][g]~~ n [�] ;s [�] [a] 0 [i] [,][ ½][f] [ �] l;t [�] [a] 0 [i] [;][ ½][f] l [�] 0 [g][ be the solution]


##### Article



of the original problem under scenario a i . We now assume


a i a i a j

~~g~~ ~~[�]~~ n;s = max ~~g~~ ~~[�]~~ n;s ; ~~g~~ ~~[�]~~ n;s

" # a (" # 0 " # 0 )



(Equation 20)


(Equation 21)



# aa i



# a0 i



0



~~g~~ ~~[�]~~ n;s



= max

a j



("



;

0



"



# a0 j



~~g~~ ~~[�]~~ n;s



~~g~~ ~~[�]~~ n;s



)



Then



"



# a0 i



# a0 i



a i


%G [�] n;s;t

# a j



"



~~g~~ ~~[�]~~ n;s



G [�] n;s;t



~~g~~ ~~[�]~~ n;s



%g n;s;t


%Gn [~] ; s; t


%Gn [~] ; s; t



~~g~~ [�] n;s


~~g~~ [�] n;s



"

"



a i


s

# 0



a i


s

# 0



a i


s ; cn; t

# a j



a i


s

# a



if G [�] n;s;t [%][0 and][ ~][G] [n][;][s][;][t] [R][0, which it is, in all cases considered here. Consequently,]

f½g [�] n;s;t [�] [a] 0 [i] [;] ~~[½][g]~~ ~~[�]~~ n;s [�] [a] a [i] j [,][ ½][f] [ �] l;t [�] [a] 0 [i] [;][ ½][f] �lf [�] a0 i [g][ is in the feasible space of the new problem, and the]

problem therefore is not infeasible.


In case the number of substations of the two parameter sets differs, i.e., N i s N j, the
lower bounds for each parameter set are computed from the corresponding cluster
of buses of the other parameter set: let N i = fS i;1 ; S i;2 ; .; S i;m ; .; S i;N i g, N j =
fS j;1 ; S j;2 ; .; S j;k ; .; S j;N j g be the two sets of clusters of buses derived from the orig
inal full-resolution dataset with ��N i=j �� = N i=j . In the clustered networks, each of these

clusters S is merged into one single bus nðS i;m Þ, nðS j;k Þ. Then, the lower bound to a

generator of technology s at a bus of set N i is set to the weighted sum of the optimal
capacity of the buses of set N j and vice versa:



"



# aa j



a i


; cS i;m ; nðS i;m Þ (Equation 22)

# 0



# a0 i



~~g~~ ~~[m]~~ n ð [in] S i;m Þ ;s



a j N j


=
X
a i k = 1



w k

k = 1



"



~~g~~ ~~[�]~~ n ð S j;k Þ ;s



where the weights w k are determined from the number of common nodes of the two
clusters S i;m, S j;k :



��S i;m XS j;k ��

w k = (Equation 23)

~~��~~ S j;k ~~��~~



Here, ��S i;m XS j;k �� is the number of nodes, which appears in both clusters, i.e., the clusters’ intersection.


SUPPLEMENTAL INFORMATION


[Supplemental information can be found online at https://doi.org/10.1016/j.joule.](https://doi.org/10.1016/j.joule.2021.07.017)

[2021.07.017.](https://doi.org/10.1016/j.joule.2021.07.017)


2622 Joule 5, 2606–2624, October 20, 2021


##### Article

ACKNOWLEDGMENTS


M.S. and A.K. are financially supported by the research project CoNDyNet II (Bundesministerium fu¨ r Bildung und Forschung, Fkz. 03EK3055C). The authors thank Jakub Jurasz for helping with Figures 1, 2, and 4 as well as four anonymous reviewers
for their thorough reviews and helpful comments.


AUTHOR CONTRIBUTIONS


Conceptualization, B.U.S. and A.K.; methodology, B.U.S. and A.K.; investigation,
B.U.S.; writing and original draft preparation, B.U.S. and A.K.; resources, M.S.; project administration and funding acquisition, all authors; writing – review and editing,
L.v.B. and W.M.


DECLARATION OF INTERESTS


The authors declare no competing interests.


Received: March 10, 2021

Revised: June 18, 2021
Accepted: July 29, 2021
Published: August 24, 2021


REFERENCES


#### **ll**

[Potential for concentrating solar power to](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref18)
[provide baseload and dispatchable power.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref18)
[Nature Clim. Change 4, 689–692.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref18)


[19. Lund, H., and Kempton, W. (2008). Integration](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref19)
[of renewable energy into the transport and](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref19)
[electricity sectors through v2g. Energy Policy](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref19)
[36, 3578–3587.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref19)


[20. Brown, T., Schlachtberger, D., Kies, A.,](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref20)
[Schramm, S., and Greiner, M. (2018a).](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref20)
[Synergies of sector coupling and transmission](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref20)
[reinforcement in a cost-optimised, highly](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref20)
[renewable European energy system. Energy](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref20)
[160, 720–739.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref20)


21. Spencer, T., Rodrigues, N., Pachouri, R.,
Thakre, S., and Renjith, G. (2020). Renewable
power pathways: modelling the integration of
wind and solar in India by 2030 (Teri the Energy
[and Resources Institute). https://www.teriin.](https://www.teriin.org/sites/default/files/2020-07/Renewable-Power-Pathways-Report.pdf)
[org/sites/default/files/2020-07/Renewable-](https://www.teriin.org/sites/default/files/2020-07/Renewable-Power-Pathways-Report.pdf)
[Power-Pathways-Report.pdf.](https://www.teriin.org/sites/default/files/2020-07/Renewable-Power-Pathways-Report.pdf)


[22. Pescia, D. (2020). Minimizing the Cost of](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref22)
[Integrating Wind and Solar Power in Japan.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref22)
[Insights for Japanese power system](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref22)
[transformation up to 2030. ANALYSIS (Agora](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref22)
[Energiewende).](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref22)


23. Lotze, J., Salzinger, M., Gaillardon, B., Mogel,
M., and Troitskyi, K. (2020). Stromnetz 2050,
Technical report (TransnetBW), Stuttgart.
[https://www.transnetbw.de/de/](https://www.transnetbw.de/de/stromnetz2050/)
[stromnetz2050/.](https://www.transnetbw.de/de/stromnetz2050/)


[24. Pfenninger, S. (2017a). Energy scientists must](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref24)
[show their workings. Nature 542, 393.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref24)


[25. Pfenninger, S. (2017b). Dealing with multiple](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref25)
[decades of hourly wind and pv time series in](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref25)
[energy models: a comparison of methods to](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref25)
[reduce time resolution and the planning](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref25)
[implications of inter-annual variability. Appl.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref25)
[Energy 197, 1–13.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref25)


Joule 5, 2606–2624, October 20, 2021 2623



[1. Pacala, S., and Socolow, R. (2004). Stabilization](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref1)
[wedges: solving the climate problem for the](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref1)
[next 50 years with current technologies.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref1)
[Science 305, 968–972.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref1)


[2. Lund, H. (2007). Renewable energy strategies](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref2)
[for sustainable development. Energy 32,](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref2)
[912–919.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref2)


[3. Connolly, D., Lund, H., Mathiesen, B.V., and](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref3)
[Leahy, M. (2010). A review of computer tools for](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref3)
[analysing the integration of renewable energy](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref3)
[into various energy systems. Appl. Energy 87,](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref3)
[1059–1082.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref3)


[4. Steinke, F., Wolfrum, P., and Hoffmann, C.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref4)
[(2013). Grid vs. storage in a 100% renewable](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref4)
[Europe. Renew. Energy 50, 826–832.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref4)


[5. Weitemeyer, S., Kleinhans, D., Wienholt, L.,](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref5)
[Vogt, T., and Agert, C. (2016). A European](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref5)
[perspective: potential of grid and storage for](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref5)
[balancing renewable power systems. Energy](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref5)
[Technol. 4, 114–122.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref5)


[6. Rodrı´guez, R.A., Becker, S., Andresen, G.B.,](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref6)
[Heide, D., and Greiner, M. (2014). Transmission](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref6)
[needs across a fully renewable European](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref6)
[power system. Renew. Energy 63, 467–476.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref6)


[7. Kies, A., Schyska, B.U., and von Bremen, L.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref7)
[(2016a). Curtailment in a highly renewable](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref7)
[power system and its effect on capacity factors.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref7)
[Energies 9, 510.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref7)


[8. Heide, D., Greiner, M., Von Bremen, L., and](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref8)
[Hoffmann, C. (2011). Reduced storage and](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref8)
[balancing needs in a fully renewable European](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref8)
[power system with excess wind and solar](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref8)
[power generation. Renew. Energy 36, 2515–](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref8)
[2523.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref8)


[9. Grams, C.M., Beerli, R., Pfenninger, S., Staffell,](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref9)
[I., and Wernli, H. (2017). Balancing Europe’s](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref9)
[wind power output through spatial](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref9)
[deployment informed by weather regimes.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref9)
[Nat. Clim. Chang. 7, 557–562.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref9)



[10. Ming, B., Liu, P., Guo, S., Zhang, X., Feng, M.,](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref10)
[and Wang, X. (2017). Optimizing utility-scale](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref10)
[photovoltaic power generation for integration](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref10)
[into a hydropower reservoir by incorporating](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref10)
[long- and short-term operational decisions.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref10)
[Appl. Energy 204, 432–445.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref10)


[11. Jurasz, J., and Ciapała, B. (2017). Integrating](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref11)
[photovoltaics into energy systems by using a](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref11)
[run-off-river power plant with pondage to](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref11)
[smooth energy exchange with the power grid.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref11)
[Appl. Energy 198, 21–35.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref11)


[12. Santos-Alamillos, F.J., Pozo-Va´zquez, D., Ruiz-](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref12)
[Arias, J.A., Von Bremen, L., and Tovar-](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref12)
[Pescador, J. (2015). Combining wind farms with](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref12)
[concentrating solar plants to provide stable](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref12)
[renewable power. Renew. Energy 76, 539–550.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref12)


[13. Sepulveda, N.A., Jenkins, J.D., de Sisternes,](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref13)
[F.J., and Lester, R.K. (2018). The role of firm](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref13)
[low-carbon electricity resources in deep](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref13)
[decarbonization of power generation. Joule 2,](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref13)
[2403–2420.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref13)


[14. Brown, P.R., and Botterud, A. (2021). The value](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref14)
[of inter-regional coordination and transmission](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref14)
[in decarbonizing the us electricity system.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref14)
[Joule 5, 115–134.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref14)


[15. Kies, A., Schyska, B.U., and von Bremen, L.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref15)
[(2016b). The demand side management](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref15)
[potential to balance a highly renewable](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref15)
[European power system. Energies 9, 955.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref15)


[16. Hirth, L., and Mu¨ ller, S. (2016). System-friendly](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref16)
[wind power. Energy Econ 56, 51–63.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref16)


[17. Chattopadhyay, K., Kies, A., Lorenz, E., von](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref17)
[Bremen, L., and Heinemann, D. (2017). The](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref17)
[impact of different pv module configurations](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref17)
[on storage and additional balancing needs for](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref17)
[a fully renewable European power system.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref17)
[Renew. Energy 113, 176–189.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref17)


[18. Pfenninger, S., Gauche´, P., Lilliestam, J.,](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref18)
[Damerau, K., Wagner, F., and Patt, A. (2014).](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref18)


#### **ll**

[26. Hilbers, A.P., Brayshaw, D.J., and Gandy, A.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref26)
[(2019). Quantifying demand and weather](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref26)
[uncertainty in power system models using the](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref26)
[m out of n bootstrap. arXiv, arXiv:1912.10326.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref26)


[27. Neumann, F., and Brown, T. (2019). The near-](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref27)
[optimal feasible space of a renewable power](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref27)
[system model. arXiv, arXiv:1910.01891.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref27)


[28. Schlott, M., Kies, A., Brown, T., Schramm, S.,](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref28)
[and Greiner, M. (2018). The impact of climate](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref28)
[change on a cost-optimal highly renewable](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref28)
[European electricity network. Appl. Energy](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref28)
[230, 1645–1659.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref28)


[29. Schyska, B.U., and Kies, A. (2020). How regional](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref29)
[differences in cost of capital influence the](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref29)
[optimal design of power systems. Appl. Energy](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref29)
[262, 114523.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref29)


[30. Frysztacki, M.M., Ho¨ rsch, J., Hagenmeyer, V.,](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref30)
[and Brown, T. (2021). The strong effect of](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref30)
[network resolution on electricity system](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref30)
[models with high shares of wind and solar.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref30)
[Appl. Energy 291, 116726.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref30)


[31. Trutnevyte, E. (2016). Does cost optimization](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref31)
[approximate the real-world energy transition?](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref31)
[Energy 106, 182–193.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref31)


[32. Mavromatidis, G., Orehounig, K., and](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref32)
[Carmeliet, J. (2018). Uncertainty and global](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref32)
[sensitivity analysis for the optimal design of](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref32)
[distributed energy systems. Appl. Energy 214,](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref32)
[219–238.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref32)


[33. Schlachtberger, D.P., Brown, T., Scha¨fer, M.,](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref33)
[Schramm, S., and Greiner, M. (2018). Cost](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref33)
[optimal scenarios of a future highly renewable](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref33)
[European electricity system: exploring the](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref33)
[influence of weather data, cost parameters and](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref33)
[policy constraints. Energy 163, 100–114.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref33)


34. Moret, S., Codina Girone` s, V.C., Bierlaire, M.,
and Mare´ chal, F. (2017). Characterization of
input uncertainties in strategic energy planning
[models. Appl. Energy 202, 597–617. https://](https://doi.org/10.1016/j.apenergy.2017.05.106)
[doi.org/10.1016/j.apenergy.2017.05.106.](https://doi.org/10.1016/j.apenergy.2017.05.106)


35. Shirizadeh, B., Perrier, Q., and Quirion, P.
(2019). How sensitive are optimal fully
renewable power systems to technology cost
uncertainty? Policy Papers 2019.04 (FAERE French Association of Environmental and
[Resource Economists). https://ideas.repec.](https://ideas.repec.org/p/fae/ppaper/2019.04.html)
[org/p/fae/ppaper/2019.04.html.](https://ideas.repec.org/p/fae/ppaper/2019.04.html)


[36. Nacken, L., Krebs, F., Fischer, T., and](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref36)
[Hoffmanna, C. (2019). Integrated renewable](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref36)
[energy systems for Germany–a model-based](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref36)
[exploration of the decision space. In 2019 16th](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref36)
[International Conference on the European](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref36)
[Energy Market (EEM), pp. 1–8.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref36)


[37. Brill, E.D., Jr., Chang, S.-Y., and Hopkins, L.D.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref37)
[(1982). Modeling to generate alternatives: the](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref37)
[hsj approach and an illustration using a](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref37)
[problem in land use planning. Manag. Sci. 28,](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref37)
[221–235.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref37)


[38. Price, J., and Keppo, I. (2017). Modelling to](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref38)
[generate alternatives: a technique to explore](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref38)


2624 Joule 5, 2606–2624, October 20, 2021



[uncertainty in energy-environment-economy](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref38)
[models. Appl. Energy 195, 356–369.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref38)


[39. DeCarolis, J.F., Babaee, S., Li, B., and Kanungo,](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref39)
[S. (2016). Modelling to generate alternatives](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref39)
[with an energy system optimization model.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref39)
[Environ. Modell. Software 79, 300–310.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref39)


40. Louwen, A., Junginger, M., and Krishnan, S.
(2018). Technological learning in energy
modelling–experience curves: policy brief for
[the reflex project. https://reflex-project.eu/wp-](https://reflex-project.eu/wp-content/uploads/2018/12/REFLEX_policy_brief_Experience_curves_12_2018.pdf)
[content/uploads/2018/12/REFLEX_policy_](https://reflex-project.eu/wp-content/uploads/2018/12/REFLEX_policy_brief_Experience_curves_12_2018.pdf)
[brief_Experience_curves_12_2018.pdf.](https://reflex-project.eu/wp-content/uploads/2018/12/REFLEX_policy_brief_Experience_curves_12_2018.pdf)


[41. Mattsson, N., and Wene, C.-O. (1997).](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref41)
[Assessing new energy technologies using an](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref41)
[energy system model with endogenized](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref41)
[experience curves. Int. J. Energy Res. 21,](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref41)
[385–393.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref41)


42. Yue, X., Pye, S., DeCarolis, J., Li, F.G.N., Rogan,
F., and Gallacho´ ir, B.O [´ ] . (2018). A review of
approaches to uncertainty assessment in
energy system optimization models. Energy
[Strategy Rev 21, 204–217. https://doi.org/10.](https://doi.org/10.1016/j.esr.2018.06.003)
[1016/j.esr.2018.06.003.](https://doi.org/10.1016/j.esr.2018.06.003)


43. Noothout, P., de Jager, D., Tesnie` re, L., van
Rooijen, S., Karypidis, N., Bru¨ ckmann, R.,Jirou�s, F., Breitschopf, B., Angelopoulos, D.,
Doukas, H., et al. (2016). The impact of risks in
renewable energy investments and the role of
[smart policies (Diacore). http://www.](http://www.indiaenvironmentportal.org.in/files/file/impact-of-risk-in-res-investments.pdf)
[indiaenvironmentportal.org.in/files/file/](http://www.indiaenvironmentportal.org.in/files/file/impact-of-risk-in-res-investments.pdf)
[impact-of-risk-in-res-investments.pdf.](http://www.indiaenvironmentportal.org.in/files/file/impact-of-risk-in-res-investments.pdf)


[44. Ho¨ rsch, J., Hofmann, F., Schlachtberger, D.,](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref44)
[and Brown, T. (2018). Pypsa-eur: an open](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref44)
[optimisation model of the European](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref44)
[transmission system. arXiv, arXiv:1806.01613.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref44)


[45. Ho¨ rsch, J., and Brown, T. (2017). The role of](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref45)
[spatial scale in joint optimisations of](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref45)
[generation and transmission for European](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref45)
[highly renewable scenarios. In 2017 14th](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref45)
[International Conference on the European](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref45)
[Energy Market (EEM), pp. 1–7.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref45)


[46. St. Martin, C.M.S., Lundquist, J.K., and](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref46)
[Handschy, M.A. (2015). Variability of](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref46)
[interconnected wind plants: correlation length](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref46)
[and its dependence on variability time scale.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref46)
[Environ. Res. Lett. 10, 044004.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref46)


[47. Hoffmann, M., Kotzur, L., Stolten, D., and](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref47)
[Robinius, M. (2020). A review on time series](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref47)
[aggregation methods for energy system](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref47)
[models. Energies 13, 641.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref47)


[48. Gabrielli, P., Gazzani, M., Martelli, E., and](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref48)
[Mazzotti, M. (2018). Optimal design of multi-](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref48)
[energy systems with seasonal storage. Appl.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref48)
[Energy 219, 408–424.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref48)


[49. de Guibert, P., Shirizadeh, B., and Quirion, P.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref49)
[(2020). Variable time-step: a method for](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref49)
[improving computational tractability for](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref49)
[energy system models with long-term storage.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref49)
[Energy 213, 119024.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref49)


[50. Kotzur, L., Markewitz, P., Robinius, M., and](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref50)
[Stolten, D. (2018a). Time series aggregation for](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref50)


##### Article

[energy system design: modeling seasonal](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref50)
[storage. Appl. Energy 213, 123–135.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref50)


[51. Pineda, S., and Morales, J.M. (2018).](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref51)
[Chronological time-period clustering for](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref51)
[optimal capacity expansion planning with](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref51)
[storage. IEEE Trans. Power Syst. 33, 7162–7170.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref51)


[52. Schlachtberger, D.P., Brown, T., Schramm, S.,](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref52)
[and Greiner, M. (2017). The benefits of](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref52)
[cooperation in a highly renewable European](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref52)
[electricity network. Energy 134, 469–481.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref52)


53. Copernicus Programme. (2017). Corine land
[cover. https://land.copernicus.eu/pan-](https://land.copernicus.eu/pan-european/corine-land-cover/clc-2012)
[european/corine-land-cover/clc-2012.](https://land.copernicus.eu/pan-european/corine-land-cover/clc-2012)


54. European Environment Agency (2016). Natura
2000 data, the European network of protected
[sites. https://www.eea.europa.eu/data-and-](https://www.eea.europa.eu/data-and-maps/data/natura-7)
[maps/data/natura-7.](https://www.eea.europa.eu/data-and-maps/data/natura-7)


[55. Jurasz, J., Canales, F.A., Kies, A., Guezgouz,](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref55)
[M., and Beluco, A. (2020). A review on the](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref55)
[complementarity of renewable energy sources:](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref55)
[concept, metrics, application and future](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref55)
[research directions. Sol. Energy 195, 703–724.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref55)


56. Copernicus Climate Change Service (2017).
Era5: Fifth generation of ecmwf atmospheric
reanalyses of the global climate (Copernicus),
Copernicus Climate Change Service Climate
[Data Store (CDS). https://www.ecmwf.int/en/](https://www.ecmwf.int/en/forecasts/dataset/ecmwf-reanalysis-v5)
[forecasts/dataset/ecmwf-reanalysis-v5.](https://www.ecmwf.int/en/forecasts/dataset/ecmwf-reanalysis-v5)


57. Pfeifroth, U., Kothe, S., Mu¨ ller, R., Trentmann,
J., Hollmann, R., Fuchs, P., and Werscheck, M.
(2017). Surface radiation data set - heliosat
(sarah) - edition 2.1. Technical report, Satellite
Application Facility on Climate Monitoring
[(CM-SAF). https://doi.org/10.5676/](https://doi.org/10.5676/EUM_SAF_CM/SARAH/V002_01)
[EUM_SAF_CM/SARAH/V002_01.](https://doi.org/10.5676/EUM_SAF_CM/SARAH/V002_01)


[58. Huld, T., Gottschalg, R., Beyer, H.G., and Topi�c,](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref58)
[M. (2010). Mapping the performance of pv](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref58)
[modules, effects of module type and data](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref58)
[averaging. Sol. Energy 84, 324–338.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref58)


59. ENTSO-E. (2012). Country-specific hourly load
[data. https://www.entsoe.eu/data/data-](https://www.entsoe.eu/data/data-portal/)
[portal/.](https://www.entsoe.eu/data/data-portal/)


60. Bremen, L.V., Buddeke, M., Heinemann, D.,
Kies, A., Kleinhans, D., Kru¨ ger, C., et al. (2021).
Ergebnisse und Empfehlungen des BMBFForschungsprojektes Regenerative
Stromversorgung & Speicherbedarf in 2050RESTORE 2050: Teilbericht D13+ D14.,
[Technical report (Wuppertal Institut). https://](https://epub.wupperinst.org/files/7786/7786_RESTORE2050.pdf)
[epub.wupperinst.org/files/7786/](https://epub.wupperinst.org/files/7786/7786_RESTORE2050.pdf)
[7786_RESTORE2050.pdf.](https://epub.wupperinst.org/files/7786/7786_RESTORE2050.pdf)


[61. Kotzur, L., Markewitz, P., Robinius, M., and](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref61)
[Stolten, D. (2018b). Impact of different time](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref61)
[series aggregation methods on optimal energy](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref61)
[system design. Renew. Energy 117, 474–487.](http://refhub.elsevier.com/S2542-4351(21)00355-X/sref61)


62. Brown, T., Ho¨ rsch, J., and Schlachtberger, D.
(2018b). PyPSA: Python for Power System
[Analysis. J. Open Res. Software 6. https://doi.](https://doi.org/10.5334/jors.188)
[org/10.5334/jors.188.](https://doi.org/10.5334/jors.188)


