# Citation Key: chenImpactsFleetTypes2018a

---

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenImpactsFleetTypes2018a/chenImpactsFleetTypes2018a.pdf-0-0.png)


## **Impacts of fleet types and charging modes for** **electric vehicles on emissions under different** **penetrations of wind power**

**Xinyu Chen** **[1,6]** **[*, <PERSON><PERSON><PERSON>](http://orcid.org/0000-0002-8294-6419)** **[2,6]** **, <PERSON><PERSON><PERSON>** **[2,6]** **, <PERSON>** **[3,6]** **, <PERSON>** **[4,6]** *** and Jiajun Lv** **[5,6]**


**Current Chinese policy promotes the development of both electricity-propelled vehicles and carbon-free sources of power.**
**Concern has been expressed that electric vehicles on average may emit more CO** **2** **and conventional pollutants in China. Here,**
**we explore the environmental implications of investments in different types of electric vehicle (public buses, taxis and private**
**light-duty vehicles) and different modes (fast or slow) for charging under a range of different wind penetration levels. To do**
**this, we take Beijing in 2020 as a case study and employ hourly simulation of vehicle charging behaviour and power system**
**operation. Assuming the slow-charging option, we find that investments in electric private light-duty vehicles can result in an**
**effective reduction in the emission of CO** **2** **at several levels of wind penetration. The fast-charging option, however, is counter-**
**productive. Electrifying buses and taxis offers the most effective option to reduce emissions of NO** _**x**_ **, a major precursor for**
**air pollution.**



ccording to the Chinese Ministry of Environmental
Protection, 66 of the 74 largest cities in China failed to meet
# A the nation’s ambient air quality standards in 2014 [1] . The

power generation and transportation sectors represent the largest
sources of air pollutants and their precursors [2][–][4] . While the annual
power demand in China tripled from 2003 to 2016, the growth in
vehicle purchases increased even faster: private vehicle sales grew
from 2 million in 2003 to 24 million in 2016.
Electric vehicles (EVs) have been promoted aggressively by both
policymakers and industry stakeholders in China. With an emphasis on electrifying buses and taxis, a city-based government pilot
project, Ten Cities, Thousand Vehicles [5], was initiated in 2009 and
expanded in 2014 [6] . Under this programme, the number of electric
buses in Beijing reached 2,672 in 2016 and is expected to double by
2017 and quadruple by 2020. For private EVs, strong incentives have
been introduced: a subsidy of up to US$9,500 has been provided
since 2010 for the purchase of such vehicles [7] and battery EVs have
been exempt from sales tax (10% of the retail price) since 2014 [7] . As a
result, about 800,000 EVs were produced in China in 2017 [8], a 134%
increase from the year 2015 [9] . Growth of EVs is projected to surpass
gasoline light-duty vehicles (LDVs) in Beijing by 2018 [10] . Along with
a rapid increase in EV sales, the charging infrastructure is also on
the rise. The annual growth rate for the number of public charging
piles averaged close to 90% over the past five years [9] . Approximately
40% of residential communities in Beijing are now equipped with
charging facilities [11] . A national fast-charging network is planned by
the State Grid Corporation of China (SGCC) for 2020.
In contrast to the flourishing EV market, the full fuel-cycle
environmental impacts of electrified LDVs have been deemed
negative compared to gasoline vehicles in areas dominated by coalfired electricity generation (for example, North China), in terms of
both emissions of conventional air pollutants (NO _x_, SO 2, PM 10 and



primary PM 2.5 ) and also CO 2 (refs [12][,][13] ). The existing analyses on the
environmental impact of EVs in China [12][,][13] are based on the current
annual average energy mix for power generation and regional average emission inventories. The differences among more specific EV
development options have not been addressed.
The overall environmental impacts of EVs are influenced
strongly by both the types of vehicle and the timing of charging [14] .
Public buses, taxis and private LDVs differ significantly in terms
of emission factors (for different pollutants), fleet sizes and annual
average mileage. The emission factor for NO _x_ from a diesel bus
(per kilometre travelled) is projected for example to be about 80
times greater in 2020 than the value from a private LDV [15] . The
differences in fleet type have not been considered in previous EV
studies for China or other countries [12][–][14][,][16][–][19], but could be important for air pollution control. In addition, it has been realized in
the United States [14][,][17][,][18] that the environmental impact of EVs on
the operation of power systems is highly dependent on the timing of charging, which depends both on fleet types and charging
strategies (fast versus slow). In the absence of actual statistical data,
the impacts of these considerations have not been discussed in the
Chinese context.
Increased investment in wind power could have an important
influence on the future of EVs in China. The installed capacity of
wind power in China had reached 188 GW by the end of 2017 [20] .
It is expected to climb to 200 GW by 2020, and to 400 GW by
2030 [21] . These wind investments have been concentrated mainly in
the northern part of China, and incorporating this variable power
resource in a power grid dominated by inflexible coal-fired units
has been a major challenge. In these regions, combined heat and
power (CHP) units account for approximately half of all thermal
generators [16] . The operational flexibility of power production is limited further by the demand for heat during the heating season [16] .



1 School of Engineering and Applied Sciences, Harvard University, Cambridge, MA, USA. 2 Department of Electrical Engineering, Tsinghua University,
Beijing, China. [3] Harvard China Project and School of Engineering and Applied Sciences, Harvard University, Cambridge, MA, USA. [4] School of Engineering
and Applied Sciences and Department of Earth and Planetary Sciences, Harvard University, Cambridge, MA, USA. [5] School of Electrical Engineering,
Xi’an Jiaotong University, Xi’an, Shaanxi, China. [6] These authors contributed equally: Xinyu Chen, Hongcai Zhang, Zhiwei Xu, Chris P. Nielsen,
[Michael B. McElroy and Jiajun Lv *e-mail: <EMAIL>; <EMAIL>](mailto:<EMAIL>)


**Nature Energy** [| VOL 3 | MAY 2018 | 413–421 | www.nature.com/natureenergy](http://www.nature.com/natureenergy) **413**


© 2018 Macmillan Publishers Limited, part of Springer Nature. All rights reserved.


#### Articles NATurE EnErgy



As a result, 17% of the total wind power potentially available from
China’s wind systems was curtailed—wasted—in 2016, resulting in
an economic loss of about US$3.7 billion (ref. [22] ).
With appropriately managed charging, EVs could facilitate wind
integration by providing a measure of flexibility in demand and/or
energy storage capacity. As we shall see, if the time for charging were
to overlap with the interval of peak power demand, additional coalfired capacity would be required, and benefits from this flexibility
would be reduced significantly, potentially increasing the requirements for curtailment of wind power. The impact of slow-charged
LDVs on wind power integration in the Inner Mongolia energy
system was found to be positive, on the basis of stylized charging
profiles [19] . However, the electrification of public fleets, options for
fast charging and implications with respect to reductions in air pollutants have not been considered to this point.
Here, we explore the environmental impacts of different electrified transportation options taking account of different strategies
for charging with respect to the accommodation of wind power.
Specifically, we investigate how various types of EV (buses, taxis and
private vehicles) and different modes of charging (fast and slow)
could affect the overall emission of greenhouse gases and selected
air pollutants (NO _x_, SO 2, primary PM 2.5 ) under different levels of
wind power penetration. Hourly simulations for interlinked electrified transportation, power and heating systems are conducted for
Beijing and the surrounding area, incorporating detailed statistics
for driving patterns and comprehensive modelling of energy systems. The results show that the vehicle charging mode for LDVs
will have a significant influence on power system operations and
the integration of wind power, especially at elevated wind levels.
Emissions of CO 2 can be reduced by slow-charging LDVs at several
levels of wind penetration. Electric LDVs with fast charging, in contrast, will result in higher emissions of CO 2, reflecting their negative
influence on wind integration. Electrifying public transportation
(buses and taxis) is most effective in reducing emissions of NO _x_, a
major precursor for air pollution.


**Model and scenarios**
The environmental impacts of different vehicle electrification strategies are quantified using a newly developed integrated energy
system optimization model, whose structure is presented in Fig. 1.
We begin using field investigations and regulatory reports to summarize information on driving patterns for buses, taxis and LDVs
in China. These driving patterns are then used to estimate probabilistic charging behaviour (arrival times, departure times and the
state of charge) for vehicles of different fleet types under different
charging conditions. The probabilities are employed in Monte Carlo
simulations to project the collective charging demand for vehicles
throughout the year, with 15-min time resolution. To quantify the
environmental impacts from EV charging, an optimization model is
developed to simulate the energy systems (covering power, heating



and electrified transportation) on an hourly basis for the entire
year. Hourly variations of power demand, wind power and charging
requirements for EV fleets are considered. The model accounts for
limitations on the possible range for power output, power generation changes over consecutive hours, and minimum times required
for restart or shut down of thermal units. It also accounts for variations in heating demand and limitations of power and heat production for individual CHP units. The detailed modelling formulation
is presented in the Methods.
Beijing, as the most populous metropolitan area in China with
the largest vehicle stock, is selected as a case study to evaluate the
impacts of EV options in 2020 [23] . Projections of vehicle populations for buses, taxis and private LDVs for the target year amount
to approximately 30 thousand, 66 thousand and 5.6 million, respectively. The projected annual kilometres travelled per vehicle (VKT)
for LDVs, taxis and buses correspond to 18,000 km, 58,000 km and
126,000 km, respectively (see Supplementary Fig. 1, Supplementary
Table 1 and Supplementary Note 1). Note that VKT values for public
buses and taxis are three and seven times higher than for LDVs [15][,][24] .
The study accounts for hourly power consumption, wind production and detailed energy system configurations and operations for
Beijing and its neighbouring areas, as detailed in the Methods.
Three vehicle electrification strategies are compared with a business-as-usual (BAU) reference in which conventional vehicle fleets
are powered solely by fossil fuels. These three strategies are: Pub-f,
electrification of public transportation vehicles, buses (non-natural
gas-propelled) and taxis, using fast charging; Prv-s, plug-in electric private LDVs serviced using slow-charging stations; and Prv-f,
which is the same as Prv-s but assuming service by fast-charging
stations. The value assumed for the number of electric private LDVs
is based on the number of new LDVs anticipated to be sold between
2015 and 2020, accounting for the projected growth in the vehicle
population and projected retirements and replacements for existing vehicles (detailed projections for growth and replacements of
vehicles are presented in Supplementary Notes 1 and 2). The government plan is to convert 37% of public buses to compressed natural gas; the scenario adopted here applies to the additional 63%. In
each scenario, penetration of wind power is allowed to vary from
0% to 40%. Wind curtailment, in addition to emissions of NO _x_, SO 2,
primary PM 2.5 and CO 2 from combined on-road transportation and
electricity generation, is calculated for each electrification strategy,
as well as for combinations of options (Cmb-f, combining Pub-f and
Prv-f; Cmb-s, combining Pub-f and Prv-s). Details of the above scenarios are summarized in Table 1.


**Characteristics of EV driving behaviour in Beijing**
The temporal distribution of electricity demand from an individual
EV is determined by the time of its arrival and departure at a specific charging station, by the mode employed for its charging (fast/
slow), the state of charge (SOC, the ratio of the energy remaining



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenImpactsFleetTypes2018a/chenImpactsFleetTypes2018a.pdf-1-0.png)



















**Fig. 1 |** **Modelling framework for the integrated energy system optimization.** The model incorporates statistical driving behaviours to simulate the
aggregated charging characteristics of different EV fleets. The charging characteristics are then fed into the integrated energy system optimization model,
generating a variety of results for simulation, including wind curtailments and overall emissions.


**414** **Nature Energy** [| VOL 3 | MAY 2018 | 413–421 | www.nature.com/natureenergy](http://www.nature.com/natureenergy)


© 2018 Macmillan Publishers Limited, part of Springer Nature. All rights reserved.


#### NATurE EnErgy Articles



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenImpactsFleetTypes2018a/chenImpactsFleetTypes2018a.pdf-2-0.png)



in the battery to the rated capacity of the battery) and the energy
required for its next trip.
Here, we investigate first the above characteristics using statistics on driving behaviour obtained through field investigations of
public bus fleets and observations at commercial parking lots [25], and
studies of transportation [26][,][27] complemented by additional information from interviews with drivers. The statistical distributions of
arrival/departure times for private cars, buses and taxis for charging
on both weekdays and weekends are summarized in Supplementary
Table 2 and Supplementary Fig. 2. Based on the above information,
the likelihoods for an EV to physically connect to a certain charging spot at specific time intervals are illustrated in Fig. 2. The figure
indicates the probability of physical connectivity as a function of
time and location.
Buses are in service normally from about 5:30 to about 23:00,
with an average daily travel distance of approximately 160 km.
Since the expected driving distance for an electric bus per charge is
estimated at 85 km in existing pilot projects [25], an electric bus must
be charged at least once during the day and once at night, using
fast-charging facilities at the bus terminal to minimize times when
the vehicle would be otherwise out of service. We assume that fastcharging of electric buses takes place between 10:00 and 16:30 and
between 23:00 and 5:30, as in ref. [25] .
Fifty-five per cent of taxis in Beijing are driven by a single driver,
denoted hereafter as ‘single shift’; the remaining 45% are shared by
two drivers, denoted as ‘double shift’ [26] . Reflecting the relatively long
daily travel distance of taxis in Beijing (averaging nearly 238 km
per day for single-shift taxis and 476 km per day for double-shift
taxis), single-shift and double-shift taxis are charged once and twice
per day, respectively, using fast charging. Here, we assume that the
one charge for single-shift taxis occurs between 20:00 and 22:00
(after rush hour), and that the two charges for double-shift taxis are
implemented between 2:00 and 4:00 (before the early morning shift)
and between 13:30 and 15:30 (before the afternoon shift).



Private LDVs in Beijing are employed mainly for commuting between homes and work places on weekdays, leaving home
between 6:00 and 9:30, arriving at work between 6:30 and 10:00.
The distribution of departure and arrival times is taken from ref. [27],
detailed in Supplementary Fig. 2. The majority of these vehicles
return home from work between 17:30 and 21:30 [27] . They may be
used also for non-work purposes after work between 19:00 and
22:00 on weekdays [27], with an average parking time estimated at
80 min based on field observations for nearly 10,000 private cars at
commercial centres in Haidian District, Beijing [27] . On weekends, we
assume that private vehicles are used solely for non-work purposes.
Besides parking at home at night, these vehicles may be parked also
at commercial establishments from 9:00 to 22:00.
Under the fast-charging scenarios (Prv-f and Cmb-f), EV owners must travel to fast-charging stations to recharge their batteries.
We assume that fast charging is concentrated mainly during lunch
hours (11:00–13:00) and after work (17:00–21:00). On weekends,
EV owners can either charge their vehicles at their residences
(9:00–21:00) or during trips to shopping or entertainment centres
(16:00–22:00). The detailed parameter settings for EV charging and
further assumptions are summarized in Supplementary Table 2.


**Optimized charging**
The impact of EV charging on power grid operation is evaluated on
an hourly basis throughout the year. Quarter-hourly driving behaviour for each EV is simulated first via Monte Carlo analysis based on
the probabilistic distributions for driving behaviours noted above.
Driving behaviours for EVs, together with hourly variations of wind
output and power demand, were incorporated in the energy system simulation model (see Methods) to determine the optimized
power consumption profile for the EV fleet and the corresponding
wind curtailment for the diverse EV scenarios and for the different
wind penetration levels over the course of the year. (The optimization model is detailed in the Methods.) Results for 10–16 March, for
a wind penetration level of 20%, are displayed in Fig. 3. The hourly
power demands for different EV strategies relative to Beijing electricity demand and wind generation are displayed in the panels on
the left, with corresponding wind power curtailment profiles, relative to the BAU case, illustrated on the right.
Power demand in Beijing peaks twice on workdays, at around
12:00 and 20:00, respectively, reaching a minimum between 3:00
and 7:00. Given that the fleet population is relatively small, the
charging power for electric buses and taxis has a limited impact on
the profile for total electricity demand (scenario Pub-f, Fig. 3a,f).
When fast charging is adopted for electric LDVs, the power demand
for charging LDVs overlaps significantly with the daily peak power
demand in Beijing, increasing daily peak power demand by 52.5%
(Fig. 3b). The increase in peak demand with the fast-charging scenario (Prv-f) requires the operation of additional coal-fired power
generators. These additional coal-fired generators are obliged to
keep producing power during off-peak hours, contributing further
to wind curtailment. If the same number of electric LDVs were
serviced using slow charging, the charging load could be allocated
mainly to off-peak periods (Fig. 3c), and could take advantage of
otherwise-curtailed wind power, thus lowering the emission intensity of associated power generation.


**Emission results**
Annual emissions of NO _x_, CO 2, SO 2 and primary PM 2.5 for different
combinations of EV strategies and wind levels were analysed on the
basis of the hourly simulation results described above. The relative
contributions to the total emissions of NO _x_, CO 2, SO 2 and primary
PM 2.5 from both the transportation and power generation sectors
under the BAU scenario (gasoline vehicles combined with 0% wind
power) for Beijing in 2020 are illustrated in Fig. 4. The potentials
for reduction of CO 2 and NO _x_ emissions corresponding to different



**Nature Energy** [| VOL 3 | MAY 2018 | 413–421 | www.nature.com/natureenergy](http://www.nature.com/natureenergy) **415**


© 2018 Macmillan Publishers Limited, part of Springer Nature. All rights reserved.


#### Articles NATurE EnErgy



**a**


**c**


**e**


**g**


**i**



0.30


0.15


0


0.6


0.3


0


0.50


0.25


0


0.1


0


0.5


0



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenImpactsFleetTypes2018a/chenImpactsFleetTypes2018a.pdf-3-0.png)



**b**













0.8


0.4





0
3 6 9 12 15 18 21 24


Hours



3 6 9 12 15 18 21 24


Hours



Fast charging Slow charging


**Fig. 2 |** **Probabilities for physical connection to charging facilities for different vehicles with different charging options.** **a** – **k**, Probabilities for connectivity
for different hours in a day for different fleet types, charging options, locations and weekday/weekends: bus using fast charging ( **a** ); taxi with double shift
( **b** ); taxi with single shift ( **c** ); private LDVs using fast charging near the work place during weekdays ( **d** ) or at a shopping centre during weekends ( **f** );
private LDVs using fast charging near home during weekdays ( **e** ) or weekends ( **g** ); private LDVs using slow charging at work during weekdays ( **h** ); private
LDVs using slow charging at home during weekdays ( **i** ) or weekends ( **k** ), private LDVs using slow charging at a shopping centre during weekdays ( **j** ) or
weekends ( **l** ). The colour indicates the charging mode and the horizontal axis indicates the hours in a day starting from midnight.



combinations of wind penetration and EV strategies relative to the
BAU scenario are summarized in Table 2. As will be indicated later,
electric LDVs, using slow charging, could take advantage of available wind power to accomplish an effective reduction in CO 2 emissions. However, emissions would be significantly increased if these
vehicles were to rely on the fast-charging option, mainly attributed
to an increase of wind curtailment. Electrifying public transportation, though contributing to a minor change in total CO 2 emissions
because of a limited fleet size, provides the most effective solution to reduce NO _x_ emissions. Results for SO 2 and primary PM 2.5
are presented in Supplementary Note 3 and Supplementary Figs. 3
and 4. Sensitivity analyses of emission results for different populations of electrified LDVs are summarized in Supplementary Note 4
and Supplementary Tables 3–6.
The power sector represents the major source of CO 2 emissions
(85% in Fig. 4). Shares of CO 2 emissions by vehicles are proportional to the shares of fuel consumed: private vehicles contribute
10% to total emissions, buses and taxis 1%, and other vehicles,
mainly heavy trucks, 3%, as indicated in Fig. 4. Renewable energy
offers a major opportunity to reduce overall CO 2 emissions in the
energy system: a 29% (19%) CO 2 reduction at 40% (20%) wind penetration could be achieved, following the scenarios summarized in
Table 2. We note here that wind penetration is defined on the basis
of total power consumption. Since part of the power consumption
is imported from other regions, wind penetration in the local power
generation mix is slightly higher. The marginal CO 2 reduction at
elevated wind penetration levels is low, limited by requirements for
increased wind curtailment. Noting that buses and taxis are responsible for only 2% of the total CO 2 emissions, electrifying public
buses and taxis has a minor impact on total CO 2 emissions.



Electric LDVs, using the slow-charging strategy, can contribute
to a further decease in CO 2 emissions assuming that power is provided partly from wind. Converting gasoline LDVs to electric LDVs,
using the slow-charging strategy, would increase CO 2 in emissions
in the absence of deployment of wind power, but would contribute
to a decrease in emissions if more than 30% of the power were supplied by wind. In addition to the reduced intensity of emissions in
the power sector at higher wind penetration, the reduction in wind
curtailment facilitated by using slow-charging EVs, as illustrated,
would contribute also to a decrease in CO 2 emissions.
Substituting gasoline LDVs with fast-charging EVs, however,
will result in an increase in CO 2 emissions as the penetration of
wind power rises. At 40% wind penetration, this substitution (gasoline to fast-charging EVs, Prv-f) will increase overall CO 2 emissions by 10%, cancelling the contribution from as much as 12 GW
of wind generation. Charging the same number of EVs in different ways (fast versus slow) would result in a 11% difference in total
CO 2 emissions at the 40% wind level, equivalent to the amount of
CO 2 emitted annually by 5.8 million gasoline LDVs. Vehicle charging strategies are projected to have a significant impact on emissions of SO 2 and primary PM 2.5, as summarized in Supplementary
Figs. 3 and 4.
A different strategy is required to reduce NO _x_ emissions, one
of the most important precursors for air pollution. The power
sector will be responsible for at most one-third of total NO _x_ emissions, when the more stringent emission standards for thermal
generators are fully implemented [28] . Increasing wind penetration
would be less effective in reducing NO _x_ emissions: NO _x_ emissions
would be reduced by only 14% at a wind penetration level of 40%.
Consequently, the influence on NO _x_ emissions of the choice of



**416** **Nature Energy** [| VOL 3 | MAY 2018 | 413–421 | www.nature.com/natureenergy](http://www.nature.com/natureenergy)


© 2018 Macmillan Publishers Limited, part of Springer Nature. All rights reserved.


#### NATurE EnErgy Articles









30





20


10


0







20


10


0







30





20


10


0


30







20


10


0


20


10


0

|O ff-p e a k p e rio d Ba se lin dem electricity e a n d Wind d Electricity eman d fo P r E V s<br>C u rta ile d w in w d ith ou charging PEV t C u w rtailed in d w cha PEV ith rging<br>Electricity demand and vehicle charging f Wind power curtailment<br>1.0<br>Pub-f Pub-f factor<br>0.5 Capacity<br>0<br>20 40 60 80 100 120 140 160 20 40 60 80 100 120 140 16<br>g<br>1.0<br>Prv-f Prv-f factor<br>0.5 Capacity<br>0<br>20 40 60 80 100 120 140 160 20 40 60 80 100 120 140 16<br>h<br>1.0<br>Prv-s Prv-s factor<br>0.5 Capacity<br>0<br>20 40 60 80 100 120 140 160 20 40 60 80 100 120 140 16<br>i<br>1.0<br>Cmb-f Cmb-f factor<br>0.5 Capacity<br>0<br>20 40 60 80 100 120 140 160 20 40 60 80 100 120 140 16<br>j<br>1.0<br>Cmb-s Cmb-s factor<br>0.5 Capacity|Col2|
|---|---|
|Cmb-s|Cmb-s|

20 40 60 80 100 120 140 160



0
20 40 60 80 100 120 140 160



Hours Hours


**Fig. 3 |** **Charging consumption profiles for EV fleets and corresponding implications for wind curtailment.** **a** – **j**, Hourly EV charging demand ( **a** – **e** )
and wind curtailment rates ( **f** – **j** ) for 10–16 March under the scenarios Pub-f ( **a**, **f** ), Prv-f ( **b**, **g** ), Prv-s ( **c**, **h** ), Cmb-f ( **d**, **i** ) and Cmb-s ( **e**, **j** ). PEV, plug-in

electric vehicle.



charging strategies for private EVs is relatively minor: adopting a
fast- or slow-charging strategy would impact total NO _x_ emissions
by no more than 5%, despite significantly different rates for wind
curtailment for the two scenarios.


1% 1%



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenImpactsFleetTypes2018a/chenImpactsFleetTypes2018a.pdf-4-0.png)















Electrification of the public fleet provides the most effective
strategy to reduce NO _x_ emissions. Public transportation, despite
the limited fleet size (30,000 buses and 66,000 taxis), is responsible
for 18% of total NO _x_ emissions, equivalent to the contribution from
8.2 million private LDVs. The importance of the emissions of NO _x_
from the public transportation sector is related in part to the high
associated annual average VKT values and emission factors. The
NO _x_ emission factor for public buses is on average 80 times higher
than that of gasoline LDVs (6.25 g km [−][1] for the public bus fleet versus 0.08 g km [−][1] for private LDVs) [15] . Electrification of the public fleet
alone would reduce total NO x emissions by 10%, equivalent to the
NO _x_ emitted annually by all of the LDVs operational in Beijing. The
reduction in NO _x_ emissions that would result from electrification
of the public fleets could rise to as much as 24% if combined with
exploitation of wind at a level of 40%.


**Discussion**
Whether an EV reduces or increases total CO 2 emissions depends
largely on how and when the vehicle is charged, as discussed above.
Especially at elevated levels of wind penetration, EVs could cancel
out a large fraction of the environmental benefits from renewables if
the charging is not managed properly. These complications have not
been recognized in current policies addressing either EV promotion













**Fig. 4 |** **Relative contributions to emissions of power generation**
**and transportation sectors.** Contributions of power generation and
transportation to CO 2, NO _x_, SO 2 and primary PM 2.5 emissions by source for
Beijing in 2020 assuming zero contribution from wind. The contribution to
SO 2 from taxis is less than 0.1% and is not shown in the figure.



**Nature Energy** [| VOL 3 | MAY 2018 | 413–421 | www.nature.com/natureenergy](http://www.nature.com/natureenergy) **417**


© 2018 Macmillan Publishers Limited, part of Springer Nature. All rights reserved.


#### Articles NATurE EnErgy



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenImpactsFleetTypes2018a/chenImpactsFleetTypes2018a.pdf-5-0.png)



or wind power integration. To realize the potential environmental
benefits of EVs, it is important for the power system to transition
away from coal-fired generation. More importantly, incentives
should be introduced to promote charging of the EVs at off-peak
times, to take advantage of idled grid capacity and otherwise curtailed wind power. Time-of-use tariffs for EV charging—a lower
tariff at off-peak hours with a higher tariff at peaking hours—could
provide such an incentive. Tariffs for fast-charging and slow-charging modes should be differentiated to internalize environmental
impacts. A coherent plan for the charging infrastructure, with an
emphasis on slow-charging piles, will be critical if potential benefits
are to be realized from the early stage of the growing EV market.
Electrification of the public fleet, as indicated, serves as the most
effective strategy for the control of NO _x_ emissions. The vehicle purchasing cost required to substitute 63% of the operating public buses
(about 20,000) would amount to about 20 billion RMB, assuming a
cost of 1 million RMB (US$0.16 million) per bus according to bidding in China in 2017 [29] . This expenditure would be equivalent to
the cost associated with as few as two or three gigawatt-level coalfired plants. The total investment for air pollution control in the
Beijing region amounted to 800 billion RMB over the period of 2013
to 2017 [30] . In this case, the purchasing costs to electrify the public
fleet could be accommodated at a cost equal to 2.5% of the total
budget. Considering the relative ease for deployment of charging
infrastructure at bus terminals, electrified public buses could offer a
potentially cost-effective means to address air pollution in Beijing.
The current target for Beijing, 5,000 electric buses by 2017 [30], underestimates such effectiveness. A comprehensive economic feasibility
assessment for the deployment of a highly electrified public transportation system should be prioritized.


**Methods**
**Overview of integrated energy system model and simulations.** The wind
curtailment and emission results presented above are based on hourly simulation
of energy systems for the Jing-Jin-Tang (JJT) region covering Beijing and the
surrounding area (geographical coverage illustrated in Supplementary Fig. 5 and
Supplementary Note 5). As indicated in the introduction, the inflexible CHP units
are considered as one of the most important factors currently contributing to wind
curtailment. In addition to requirements for the charging of EVs, the optimization
model employed here accounts also for the operational properties of CHP units in
simulating the operation of power systems.
The integrated energy system optimization model (IESOM) simulates and
optimizes the charging of electrified transportation, the operation of power
systems and the interaction with heating systems through CHP units. For the
transportation sector, a Monte Carlo simulation is conducted for individual EVs
based on the statistics on driving behaviours with a time resolution of 15 min over
the course of a year. The resulting requirements for charging of individual EVs
are aggregated to describe the requirement for composite EV fleets. The charging
requirements for EV fleets, for different scenarios, are then taken into account in
the IESOM, to optimize jointly the charging for EVs and the production of energy
from both the power and heating sectors.



Extending the conventional unit commitment model, IESOM considers
additionally requirements for EV charging, as well as the operational limits for
CHP units. The scheduling and simulation account for hourly variations in energy
demand and power generation from wind, charging for EVs, operational flexibility
of individual power generators and CHP units, constraints for inter-regional
transmission and requirements for reserves. Reserves are provided by thermal
and CHP units, slow-charging EVs and wind power. Internal restrictions for
power and heat production from CHP units are modelled accounting for hourly
variations of heating demand. Annual fuel costs, emissions and wind curtailment
rates are aggregated from the hourly simulation results. The modelling structure is
summarized in Fig. 1.


**Methodology for simulating and optimizing charging of EVs.** Monte Carlo
simulations are applied first to model the charging behaviour for vehicles according
to the statistical distributions of different fleet types, as described in the main
text. The charging behaviours for individual vehicles are then formulated and
aggregated to define the collective charging requirements for EV fleets.
The arrival/departure times and initial SOCs for each EV in the fleet were
generated using Monte Carlo simulations specific to vehicle type, subject to the
appropriate probability distribution. In scenarios that include public vehicles, we
sampled the arrival times stochastically and the associated initial SOCs every day
for the 30,499 buses and 66,646 taxis, calculating their time-varying electricity
demand assuming that each vehicle begins charging instantaneously at its rated
charging power when it plugs in, continuing until its battery is fully charged. For
private vehicles undergoing slow charging, we first determined stochastically the
charging location (that is, home, work place or shopping centre) for each private
vehicle according to the type of day (workday or weekend), and then generated the
appropriate arrival/departure times and SOCs, subject to the specified probability
distributions. For fast-charging private vehicles, arrival times, initial SOCs and
charging load profiles are determined in the same manner as for public vehicles
after randomly simulating charging locations and times.
Given information on simulated arrival/departure times and charging
requirements for each vehicle, a method for aggregating the charging requirements
for individual EVs was adopted to schedule the charging of EVs when they are
connected to charging piles under the slow-charging mode. Instead of scheduling
the charging power for millions of EVs at each time period, the strategy
proposed aggregates the constraints for each individual EV, and optimizes the
overall charging power for the entire fleet to balance fluctuations in wind power
generation and minimize the cost for energy production. Pioneered and proved in
earlier publications [31][–][33], this strategy could significantly improve the computational
efficiency of EV charging optimization.
Specifically, charging profiles for an individual EV must satisfy two constraints:
the maximum charging power allowed at the charging station (determined by
the rated charging power of the charging piles); and to reach the expected battery
energy level after a given time (determined by the charging behaviour described
above). We use two types of ‘boundary’ to define the aforementioned constraints.
First, the boundaries for the charging power (power boundaries): the upper
bound represents the maximum charging power of an EV when it is connected to
the station; the lower boundary represents its minimum charging power (zero).
Second, the boundaries for the cumulative charging energy (energy
boundaries): the upper boundary represents a charging strategy in which an
EV begins charging instantaneously at the maximum rate continuing until fully
charged; the lower boundary represents a charging strategy in which the charging
of an EV is delayed as much as possible, charging then at the maximum feasible
rate. Possible cumulative charging power and energy trajectories for an EV must
fall within these boundaries.
We utilize then the summation of power and energy boundaries for all
individual EVs to represent the EV fleet’s aggregate power and energy boundaries.



**418** **Nature Energy** [| VOL 3 | MAY 2018 | 413–421 | www.nature.com/natureenergy](http://www.nature.com/natureenergy)


© 2018 Macmillan Publishers Limited, part of Springer Nature. All rights reserved.


#### NATurE EnErgy Articles

​

##### ̄


​



A detailed description of the boundary aggregation is presented in refs [32][,][33] . For
the entire fleet, the overall charging trajectory must lie between the aggregated
upper and lower bounds for individual vehicles. Mathematically, the aggregated EV
charging power _p_ _t_ e is bounded by the following:


​

##### ̄


​



_t_


​

##### ̄


​


###### ≤ ∑ p i e, τ Δt ≤ e i t +,, ∀ i, ∀

​

##### ̄


​



_e_ _i t_ − ≤ _p_ e _Δt_ ≤ _e_ _i t_ +, ∀ _i_, ∀ _t_ (5)


​

##### ̄


​



_i_ e, _τ_ _Δt_ ≤ _e_ _i t_ +,


​

##### ̄


​



_i t_ −, ≤ _p_ _i_ _τ_


​

##### ̄


​



, _i_ _τ_ _i t_,,


​

##### ̄


​



_τ_


​

##### ̄


​



1


​

##### ̄


​



=


​

##### ̄


​



− e +


​

##### ̄


​



e


​

##### ̄


​



_p_ − ≤ _p_ ≤ _p_, 1 ≤≤ _t_ _T_


​

##### ̄


​



≤ _p_ ≤ _p_, 1 ≤≤ _t_


​

##### ̄


​



_t_ _t_ _t_


​

##### ̄


​



, 1


​

##### ̄


​



_t_


​

##### ̄


​



vehicle simulation section above.where the power and energy boundaries ( _p_ _i t_ −,, _p_ _i t_ +,, _e_ _i t_ [−], and _e_ _i t_ [+], ) are introduced in the
EV fleets employing slow charging can provide upward spinning reserve
by reducing their aggregated charging power, constrained by the power and energy boundaries at time _p_ _i t_ e, to _p_ _i t_ e,, while _t_ : _p_ _i t_ e, needs to be


​ _p_ _i t_ −, ≤ _p_ _i t_ e, ≤ _p_ _i t_ e,, ∀ _i_, ∀ _t_ (6)

##### ̄


​



+


​

##### ̄


​



_e_ _t_ − ≤ _p_ _Δt_ ≤ _e_ _t_, 1 ≤≤ _t_ _T_


​

##### ̄


​



−


_t_


​

##### ̄


​


###### ∑ p τ e Δ

​

##### ̄


​



e


_τ_


​

##### ̄


​



_t_


​

##### ̄


​



≤ _p_ _Δt_ ≤ _e_, 1 ≤≤ _t_


​

##### ̄


​



(1)
, 1 ≤≤ _t_ _T_


​

##### ̄


​



_τ_


​

##### ̄


​



1


​

##### ̄


​



=


​

##### ̄


​



where _T_ is the number of time intervals; Δ​ _t_ is the duration between two
consecutive time intervals; _p_ _t_ − and _p_ _t_ + are respectively the lower and upper bounds
and upper power bounds from the individual vehicles, respectively. for the aggregated charging power of the entire fleet at time _t_ —the sum of the lower _e_ _t_ − and _e_ _t_ + are

respectively the lower and upper bounds for the cumulative energy consumed by
the entire fleet by time _t_ —the sum of the lower and upper energy bounds from the
individual vehicles, respectively. The aggregated boundaries for equation (1) are
incorporated in the simulation of the energy system as discussed in the following
section (see further detail in Supplementary Note 6).


**Optimization for the integrated energy systems.** Incorporating the charging
requirements for EV fleets, the IESOM minimizes the overall cost for power and
heat production, as well as the curtailed wind power. Besides the constraints
considered in the regular unit commitment model (range of thermal unit power
outputs, ramping limits, minimum on/off times, power balance and reserves),
constraints for charging requirements are also incorporated. The model considers
the detailed constraints for CHP units, including restrictions for power and heat
production, hourly ramping limits and minimum on/off times.
The decision variables for the integrated energy optimization model include
the hourly energy production and consumption across three sectors: aggregated
charging power for EV fleets, hourly power generation for different generators,
and power and heat production for CHP units. In scheduling thermal units, the
represent respectively the aggregated (slow) charging power and equivalent energy model accounts for continuous variables associated with dispatch and binary variables associated with commitment decisions (online or offline). _[p]_ _i t_ e, and _e_ _i t_ e,
level for the power and heat production from the time _I_ _i_ [th] _i t_ h, conventional power plant and the and _t_ . The parameters and variables employed in the IESOM are summarized as _I_ _i t_ c, denote the online/offline status of the _i_ [th] EV fleet at time _t_ ; _p_ _i t_ h, and _ii_ [th][th] wind farm at time CHP unit at time _p_ _i t_ w, define the power production from the _i_ th thermal and CHP units at _tt_ . The binary variables ; _p_ _i tc_, [ and ] _[q]_ _i tc_, [ represent ]
nomenclature in Supplementary Table 7.
The model minimizes wind power curtailment, as well as fuel and start-up
costs. The objective function includes the total fuel cost, start-up costs and penalties
for curtailment of wind power. The fuel cost considered in the objective includes
the fuel cost for conventional power plants and for CHP units, according to:

##### ̄


​



​


_t_

##### ̄


​



​


1

##### ̄


​



​


−

##### ̄


​



​

###### Δt + ∑ p i e, τ Δt ≥ e i t −,, ∀ i, ∀

##### ̄


​



​


_i_ e, _τ_ _Δt_ ≥ _e_ _i t_ −,

##### ̄


​



​

###### i t e, Δt + ∑ p i e, τ Δt ≥ e i t −,,

##### ̄


​



​


_p_ _i t_ _Δt_ + _p_ _i_, _τ_ _Δt_ ≥ _e_ _i t_ −,, ∀ _i_, ∀ _t_ (7)

##### ̄


​



​


, _τ_


=1

##### ̄


​



​


_τ_

##### ̄


​



​


1

##### ̄


​



​


They can also provide downward reserve by increasing their aggregated
energy boundaries at time charging power, _p_ _i t_ e, to _p_ _i t_ e,, while _t_ : _p_ _i t_ e, needs also to be constrained by the power and


_p_ _i t_ e, ≤ _p_ _i t_ e, ≤ _p_ _i t_ +,, ∀ _i_, ∀ _t_ (8)

##### ̄


​



​


_t_

##### ̄


​



​


1

##### ̄


​



​


−

##### ̄


​



​

###### Δt + ∑ p i e, τ Δt ≤ e i t +,, ∀ i, ∀

##### ̄


​



​


_p_ _i t_ e _Δt_ + _p_ _i_ e _τ_ _Δt_ ≤ _e_ _i t_ +,, ∀ _i_, ∀ _t_ (9)

##### ̄


​



​


_i t_ e, _Δt_ + _p_ _i_ e, _τ_ _Δt_ ≤ _e_ _i t_ +,,

##### ̄


​



​


_i_ e, _τ_ _Δt_ ≤ _e_,

##### ̄


​



​


, _τ_


=1

##### ̄


​



​


_τ_

##### ̄


​



​


1

##### ̄


​



​


Second, constraints for the operation of CHP units and heating supplies are
also incorporated. Power output from a CHP unit is restricted by the requirement
for its heat production. The restrictions reflect internal technical limitations, such
as the steam pressure limits for individual turbines and fuel limits for boilers. All
of the possible combinations of heat and power output for CHP units satisfying
operational constraints constitute jointly a feasible operational area. For simplicity,
we assume that feasible operational areas for all CHPs are convex. The limitation
on power and heat production for a CHP unit can be represented by a convex
combination of the coordinates of the corner points [35] :

##### ̄


​



​








##### ̄


​



​


_M_

##### ̄


​



​


_p_ _i tc_

##### ̄


​



​

###### ∑

##### ̄


​



​


_p_ = _α_ _i t_ _x_ _i_

##### ̄


​



​


=

##### ̄


​



​


_α_

##### ̄


​



​


_k_ _k_
_i t_ _x_ _i_

##### ̄


​



​


,


_k_ =1

##### ̄


​



​


,

##### ̄


​



​


(10)

##### ̄


​



​


_k_

##### ̄


​



​


=

##### ̄


​



​


_M_

##### ̄


​



​


_c_

_i t_

##### ̄


​



​

###### ∑

##### ̄


​



​


_q_ = _α_ _i t_ _y_

##### ̄


​



​


=

##### ̄


​



​


_α_

##### ̄


​



​


,


_k_ =1

##### ̄


​



​


_k_ _k_
_i t_, _y_ _i_

##### ̄


​



​


,

##### ̄


​



​


_k_

##### ̄


​



​


=

##### ̄


​



​


_T_

##### ̄


​



​


_N_

##### ̄


​



​


_N_

##### ̄


​



​


1

##### ̄


​



​


1

##### ̄


​



​


h c − h − c

##### ̄


​



​


_T_

##### ̄


​



​


_T_

##### ̄


​



​


_T_

##### ̄


​



​


_N_

##### ̄


​



​


_N_

##### ̄


​



​


−

##### ̄


​



​


−

##### ̄


​



​

###### = ∑∑ C i t h, + ∑∑ C i t c, + ∑∑ S i t h, + ∑∑ S i t c, +

##### ̄


​



​


h


_i t_,

##### ̄


​



​


c


_i t_,

##### ̄


​



​


_i t_ c, + _C_ W

##### ̄


​



​


h


_i t_,

##### ̄


​



​


min f = _C_ _i t_, + _C_ _i t_ c, + _S_ _i t_, + _S_ _i t_ c, + _C_ W (2)


=1 =1 =1 =1 =1 =1 =1 =1

##### ̄


​



​


corner point, and that the unit is shut down at time The binary variable, ( _x_ _ik_, _y_ _ik_, _c_ _ik_ indicate the power output, heat production and fuel cost for the ) _α_ _i tk_, _I_ [ is the non-negative value constrained to satisfy ] _i t_ c,, defines the on/off status of the CHP unit: _t_ . _M_ represents the number of corner points. _I_ _i t_ c, =∑0 _kM_ = indicates 1 _α_ _i tk_, = _k_ _I_ th _i t_ c, .
Ramping constraints for a CHP unit, similar to conventional units presented in
ref. [34], are represented by:

##### ̄


​



​


_i t_

##### ̄


​



​


_i t_

##### ̄


​



​


_t_

##### ̄


​



​


_t_

##### ̄


​



​


_i_

##### ̄


​



​


1

##### ̄


​



​


_t_

##### ̄


​



​


_i_

##### ̄


​



​


1

##### ̄


​



​


_t_

##### ̄


​



​


1 _i_ =1

##### ̄


​



​


_i_

##### ̄


​



​


1 _i_ =1

##### ̄


​



​


_i_

##### ̄


​



​


1

##### ̄


​



​


1

##### ̄


​



​


= = = = =

##### ̄


​



​


= =

##### ̄


​



​


where _N_ h and _N_ c represent the numbers of conventional thermal power plants and
linearly on fuel consumption; plantsCHPs; according to equations ( [34] _C_ ; _i t_ _C_ c, and W defines the penalty term for wind curtailment: _S_ _i t_ c, represent fuel costs and start-up costs for CHPs determined 15) and ( _S_ _i t_ h, is the start-up costs for conventional power 16); the fuel costs for power plants _C_ _i t_ h, depend

##### ̄


​



​







##### ̄


​



​


c


_i t_,

##### ̄


​



​


_i t_ c, −1 ≤ _R_ _i_ u

##### ̄


​



​


_i t_ c, −1 + _S_ _i_ u

##### ̄


​



​


c


_i t_,

##### ̄


​



​


_i t_ c, −1 ) + _p_ _i_ c

##### ̄


​



​


( _I_ c − _I_ c − ) + _p_ c (1− _I_ c )

##### ̄


​



​


_p_ − _p_ ≤ _R_ _i_ × _I_ _i t_ −1 + _S_ _i_ ( _I_ _i t_ − _I_ _i t_ −1 ) + _p_ (1− _I_

##### ̄


​



​


− _p_ ≤ _R_ × _I_ + _S_ ( _I_ − _I_ ) + _p_ (1−

##### ̄


​



​


c


_i t_,

##### ̄


​



​


_p_ _i t_ c, − _p_ _i t_ c, −1 ≥− _i_ ( _R_ d × _i t_, − _I_ 1c + _S_ _i_ d ( _I_ _i t_,c − _i t_ −, − _I_ 1c )) _i_ _i t_, (11)

##### ̄


​



​


− − −

##### ̄


​



​


_i t_ c, − _p_ _i t_ c, −1 ≥−( _R_ _i_ d × _I_ _i t_ c, + _S_ _i_ d ( _I_ _i t_ c, −1 − _I_ _i t_ c,

##### ̄


​



​


_p_ − _p_ ≥−( _R_ _i_ × _I_ _i t_ + _S_ _i_ ( _I_ _i t_ −1 − _I_

##### ̄


​



​


− _p_ ≥−( _R_ × _I_ + _S_ ( _I_ − _I_

##### ̄


​



​


_i t_ _i t_ −1 _i_ _i t_, _i_ _i t_, −1 _i t_,

##### ̄


​



​


− −

##### ̄


​



​


_T_

##### ̄


​



​


_N_

##### ̄


​



​


w

( _P_ _i t_ w, − _p_ _i t_ w,

##### ̄


​



​

###### = θ ∑∑ ( P i t w, − p i t w, ) Δ

##### ̄


​



​


_C_ W = _θ_ ( _P_ _i t_, − _p_ _i t_ w, ) _Δt_ (3)

=1 =1

##### ̄


​



​


where _R_ _i_ u and _R_ _i_ d are the ramp-up and ramp-down limits for the _i_ th CHP unit.
_S_ _i_ u and _S_ _i_ d are the ramping limits for the unit to start up or shut down, _p_ _i_ c is the
##### maximum power output from the p i c ̄ [=] max x ( ik ), k = 1, …, M . i [th] CHP unit, a constant number defined by

Power outputs are constrained additionally by:


​



​


W _i t_, _i t_

##### ̄


​



​


_t_

##### ̄


​



​


1 _i_ =1

##### ̄


​



​


_i_

##### ̄


​



​

##### where N would be available from wind farm w indicates the number of wind farms. θ denotes the penalty factor for wind curtailment, i at time Δ​ t is set at 1 h hereafter. The Renewable t, p i t w, is the actual power output and P i t w, is the wind power that ̄

Energy Law in China mandated that the dispatch of wind power should be
prioritized when there is no technical constraint in energy systems, and the penalty
term _C_ W ensured such priority during the optimization process [35] . The penalty factor
_θ_ is set as 1,000 kg kWh [−][1] in the following simulation, sufficiently large to ensure
that the actual power output is as close to the available wind potential as possible.
The model includes constraints relating to charging of electric vehicles,
operations of CHP units and the system power balances and reserves. First,
constraints for the aggregated charging power of the EV fleet are incorporated. The
aggregated power consumption for the fast-charging vehicles at any given time _t_ is
not controlled by the control centre of the grid companies. This portion of power
e _f_
the relevant power and energy boundaries as follows:charging strategy, the aggregated charging power of EV fleet consumption is taken as given, indicated hereafter as _p_ _i t_, . When employing slow- _i_, _p_ _i t_ e,, is constrained by


_p_ _i t_ −, ≤ _p_ _i t_ e, ≤ _p_ _i t_ +,, ∀ _i_, ∀ _t_ (4)



​

##### ̄


​ _i t_ c, +1 + _S_ _i_ d



​

##### ̄


​ c


_i t_,



​

##### ̄


​ _p_ _i t_ c, ≤ _p_ _i_ c × _I_ _i t_ c, +1 + _S_ _i_ ( _I_ _i t_ c, − _I_ _i t_ c, +1 ) (12)



​

##### ̄


​ c


_i t_, +1



​

##### ̄


​


Minimum on and off time constraints, similar to those for conventional
thermal units [34], are formulated as follows:



​

##### ̄


​










​

##### ̄


​


_G_ _i_



​

##### ̄


​

###### ∑



​

##### ̄


​


c


_i t_,



​

##### ̄


​


(1− _I_ c ) = 0



​

##### ̄


​


_I_



​

##### ̄


​


− =



​

##### ̄


​


_i t_



​

##### ̄


​


_t_



​

##### ̄


​


1



​

##### ̄


​


=



​

##### ̄


​


_t_ +U _T_



​

##### ̄


​


U _T_ _i_ −1
###### ∑


_v t_ =


_T_
###### ∑



​

##### ̄


​


_t_ +U _T_ −1



​

##### ̄


​


_i_



​

##### ̄


​


_i v_ c, ≥ U _T I_ _i_ ( _i t_ c, − _I_ _i t_ c, −1



​

##### ̄


​


U _T I_ ( c − _I_ c − ) _t_ = _G_ + 1, …, _T_ −U _T_ + 1



​

##### ̄


​


(13)



​

##### ̄


​


_I_ _i v_ ≥ U _T I_ _i_ ( _i t_ − _I_ _i t_ −1 ) _t_ = _G_ _i_ + 1, …, _T_ −U _T_ _i_



​

##### ̄


​


≥ U _T I_ ( − _I_ ) _t_ = _G_ + 1, …, _T_ −U _T_ +



​

##### ̄


​


−



​

##### ̄


​


_i v_ c, −( _I_ _i t_ c, − _I_ _i t_ c, −1



​

##### ̄


​


( _I_ −( _I_ − _I_ − )) ≥ 0 _t_ = _T_ −U _T_ + 2, …,



​

##### ̄


​


_I_ − _I_ − _I_ ≥ 0 _t_ = _T_ −U _T_ + 2, … _T_



​

##### ̄


​


−( _I_ − _I_ )) ≥ 0 _t_ = _T_ −U _T_ + 2, …,



​

##### ̄


​


_i v_ _i t_ _i t_ −1 _i_



​

##### ̄


​


−



​

##### ̄


​


_v t_ =



​

##### ̄


​


**Nature Energy** [| VOL 3 | MAY 2018 | 413–421 | www.nature.com/natureenergy](http://www.nature.com/natureenergy) **419**


© 2018 Macmillan Publishers Limited, part of Springer Nature. All rights reserved.


#### Articles NATurE EnErgy






_t_









_G_ _i_
###### ∑



_I_ _i t_ c,



c

,



_I_



0



=



1



_t_



=



_t_ +D _T_



D _T_ −1



+D _T_ −1



_i_


###### ∑



_i v_ c, ) ≥ D _T I_ _i_ ( _i t_ c, −1 − _I_ _i t_ c,



(1− _I_ c ) ≥ D _T I_ ( c − − _I_ c ) _t_ = _G_ + 1, …, _T_ −D _T_ + 1



(14)



_I_ ≥ D _T I_ − _I_ _t_ = _G_ + 1, … _T_ −D _T_



at time _i_ in equations ( at time _t_ . _t_, as indicated in equations ( _[p]_ _i t_ e, is the minimum charging load for EV fleet 6) and (7); _p_ _i t_ e, is the maximum possible charging load for EV fleet 8) and (9). Note that EVs employing the _i_ at time _t_, as indicated
slow-charging strategy could provide both upward and downward reserves in this
formulation, in contrast to the case for fast-charging vehicles. In this formulation,
the slow-charging electric vehicles, CHP units, conventional thermal generators
and wind power jointly provide reserves to the system to allow for uncertainties
involved with power demand and renewable production. The curtailed wind power
can provide upward reserve, whereas the wind power is able to provide downward
reserve by curtailing its power generation.
The intra-regional constraints could be represented by inequalities employing
d.c. generation shift factors [36] . As the intra-regional transmission constraints are
minimal in the JJT region [37][,][38] (detailed in Supplementary Note 5) such limitations
are not included in the following analysis.
Other constraints related to conventional thermal units include the maximum
and minimum generation limits, the ramping constraints and minimum on/off
time constraints. The constraints adopted the formulation introduced from ref. [34] .
In sum, the optimization model presented above minimizes the overall costs
as well as the wind power curtailments indicated in equation (2), subject to
the constraints for EVs (equations (4)–(9)), for combined heat and power units
(equations (10)–(18)), for system energy balance and reserves (equations (19)–(21))
and for flexibility constraints of conventional thermal generators. The annual
simulation for the JJT area requires a total of approximately 10 million variables.


**Beijing and its surrounding JJT energy systems.** Using the proposed optimization
model, we simulate, on an hourly basis, the JJT energy system covering both
Beijing and its neighbouring region. The total energy demand in the JJT area is
expected to increase from 339.6 TWh in 2014 to 455 TWh in 2020 (approximately
5% increase per year), of which 117.8 TWh is the anticipated power demand for
Beijing in 2020. A detailed description of the geographical coverage and energy
system configuration of JJT energy systems is presented in Supplementary Note 5
and Supplementary Fig. 5.
Hourly power demand values for both the JJT regional grid and Beijing city
are provided by the SGCC for the years 2012 and 2009, scaled proportionally
to the projected level for 2020. The unit information for the JJT regional power
systems is derived from SGCC (for units before 2014) and from Global Coal Plant
Tracker (for units constructed or planned after 2014). Hourly variations of heating
demand for eight major heating districts within the JJT area are considered.
Operational characteristics of combined heat and power units are provided by the
grid company. CHPs are must-run units during the heating season—starting in
November and ending in March. Inter-regional transmission corridors connecting
JJT and other regions, as well as the transmission capacities, are summarized in
Supplementary Table 8. The simulation set-up is detailed also in Supplementary
Note 5. The hourly power balances for two 14-day periods for the JJT system are
presented in Supplementary Fig. 6.
Hourly wind power data were derived from wind fields compiled using the
National Aeronautics and Space Administration-assimilated meteorological
database (GEOS-5) [39] . Wind-generated electricity employed in this analysis
is assumed to originate from wind farms deployed in suitable areas of Hebei
province, within the JJT area. The hourly variation of wind capacity factor is
assumed to be consistent with the data indicated by GEOS-5 for 2009 [39] .


**Emissions from power generation and transportation sectors.** The annual total
emissions from on-road conventional vehicles were determined by the product
of population size of vehicle fleet, the average annual VKT of individual vehicles
in the fleet and the average fleet emission factors for individual pollutants. The
detailed data on population size, VKT and average fleet emission factors are
summarized by fleet type in Supplementary Tables 1 and 9. Data for emission
factors by emission type and plant type are presented in Supplementary Table 10.
The total emissions from the power sector in the regional grid are aggregated
from hourly emissions of all thermal generators. We adopt emission factors
derived from the most recently available emission standards for thermal generation
plants [28], standards that have become more stringent since 2012, with which all
power generation stations are required to comply by November 2014. The average
emission intensity for generating every kilowatt hour of electricity is calculated
on the basis of the total emissions divided by the annual total power demand
(excluding imports). For scenarios considering the deployment of EVs, the changes
in emissions from the power sector in Beijing account for both direct contributions
and indirect contributions. The direct contribution is the change in emissions
related to the power consumption in Beijing, assessed on the basis of the annual
average emission intensity and total Beijing power consumption. The indirect
contribution accounts for the changes of emissions in the regional grid resulting
from the charging of EVs in the Beijing area (largely due to changes in the wind
curtailment rate), which vary in response to differences in charging strategies and
vehicle types. The detailed description of emission factors, and calculations of
emissions for different scenarios are indicated in Supplementary Note 7.


**Data availability.** The hourly wind power capacity factors and variation of power
demand for typical days of each month, as well as the operational characteristics



− _I_ ) ≥ D _T I_ ( − _I_ ) _t_ = _G_ + 1, …, _T_ −D _T_ +



_i v_ _i_ _i t_ −1 _i t_ _i_ …, _i_



−



_v t_ =


_T_
###### ∑


_v t_ =



_i v_ c, −( _I_ _i t_ c, −1 − _I_ _i t_ c,



(1− _I_ −( _I_ − − _I_ )) ≥ 0 _t_ = _T_ −D _T_ + 2, …,



_I_ − _I_ − _I_ ≥ 0 _t_ = _T_ −D _T_ + 2, … _T_



− _I_ −( _I_ − _I_ )) ≥ 0 _t_ = _T_ −D _T_ + 2, …,



_i v_ _i t_ −1 _i t_ _i_



−



where UT _i_ and DT _i_ represent the minimum on and off times of the _i_ [th] CHP unit. _G_ _i_
is associated with the initial status of the _i_ [th] CHP unit. If unit _i_ is operating in the
initial time interval, _G_ _i_ represents the minimum time intervals required before it
is capable of shutting down; otherwise, _G_ _i_ indicates the minimum time intervals
required for restart.
combination formulationBoth fuel costs _C_ _i t_ c, and start-up costs [35] : _S_ _i t_ c, can be represented by the convex


_M_



=
###### ∑ [α],



_i t_ c, = _[α]_ _i tk_, _c_ _ik_



_C_ _i t_, = _[α]_ _i t_, _c_ _i_ (15)

_k_ =1



,



_k_



1



_S_ _i t_ c, ≥ _λ_ _i_ ( _I_ _i t_ c, − _I_ _i t_ c, −1 ) (16)


where _[S]_ _i t_ c, is a non-negative variable and _λ_ _i_ is the start-up cost for the _i_ th CHP unit.
According to current regulations in China, CHP units are required to be online
during the entire heating period. As such, flexibility constraint (11), assuming _R_ _i_ d
equals _R_ _i_ u, could be simplified as:


∣ _p_ _i t_ c, − _p_ _i t_ c, −1 ∣≤ _R_ _i_ d (17)


The heat demand is scheduled according to the requirements for the heating
system, balanced separately within each heating district [35] :


_N_ c


###### ∑ a q i j i t, c, =



_a q_ _i j i t_, _c_ = _Q_ _j t_,



_a q_ = _Q_ (18)



,,,



_i_



1



=



where _Q_ _j t_, is the heating demand in heating district _j_ at time _t_ . _a_ _i j_, indicates the
connectivity of the _i_ [th] heating source to the _j_ [th] heating district. When _a_ _i j_, = 1, the _i_ [th]
heating element is connected to the _j_ [th] heating district. Otherwise, _a_ _i j_, = 0 .
Third, the power balance and reserve constraints must be satisfied also for
reliable system operations. The power demand must be equal to the sum of the
power outputs from all power generating units, CHP plants, wind farms and net
power imports, subtracting the power consumed by EVs (both slow-charging and
fast-charging vehicles) at every time interval:



_N_



_N_



_N_



_N_



_N_



_N_



h _N_ c _N_ w _N_ T _N_ e e _f_
###### p i t h, + ∑ p i t c, + ∑ p i t w, + ∑ p i t T, = P t + ∑ p i t e, + ∑ p i t e, f


###### ∑ p i t h, + ∑ p i t c, + ∑ p i t w, + ∑ p i t T, = P t + ∑ p i t e, + ∑ p i t e, f, ∀



h


_i t_,



c


_i t_,



w


_i t_,



e _f_


_i t_



T


_i t_,



_p_ _i t_, + _p_ _i t_ c, + _p_ _i t_ w, + _p_ _i t_, = _P_ _t_ + _p_ _i t_ e, + _p_ _i t_ e,, ∀ _t_ (19)

=1 =1 =1 =1 =1 =1



e


_i t_,



_i t_



_i t_



_i t_ _t_



_i_



1



_i_



1



_i_



1



_i_



1



_i_



1



_i_



1



where _[N]_ T indicates the number of transmission corridors, _P_ _t_ is the system load at
e e _f_
_i_ time at time _t_, _p_ _i t_, _t_ and, and _pN_ _i t_,e are, respectively, the slow- and fast-charging load for the EV fleet and _N_ e _f_ are the number of EV fleets incorporating slow and fast
at time charging, respectively. _t_ . The hourly rates for the transmitted power are fixed and determined _p_ _i t_ T, is the power imported from the _i_ th transmission corridor
jointly by the annual contracting among different regional dispatch centres and
by the transmission capacity limitations, as detailed in Supplementary Table 8 and
Supplementary Note 5.
The uncertainties relating to potential errors in forecasting energy demand and
wind power generation are accounted for in the system reserve requirements:



_N_



_N_



_N_



_N_



_N_



h _N_ c _N_ T _N_ e e _f_
###### I i t h, × p i h + ∑ p i t c, + ∑ p i t T, ≥ P t + R S t u −R W t + ∑ p i t e + ∑ p i t e, f


###### ∑ I i t h, × p i h + ∑ p i t c, + ∑ p i t T, ≥ P t + R S t u −R W t + ∑ p i t e, + ∑ p i t e, f, ∀



h h

_i t_, × _p_ _i_



_i t_



c


_i t_,



_i t_ T, ≥ _P_ _t_ + R _S_ _t_ u



e _f_


_i t_



_I_ _i t_, × _p_ _i_ + _p_ _i t_ c, + _p_ _i t_, ≥ _P_ _t_ + R _S_ _t_ u −R _W_ _t_ + _p_ _i t_ e, + _p_ _i t_ e,, ∀ _t_ (20)

=1 =1 =1 =1 =1



_i t_, _i_



e


_i t_,



_i t_ _t_ _t_ _t_



_i_



1



_i_



1



_i_



1



_i_



1



_i_



1



_N_



_N_



_N_



_N_



_N_



h _N_ c _N_ T _N_ e e _f_
###### I i t h, × p i h + ∑ p i t c + ∑ p i t T, ≤− P t R S t d + ∑ p i t e, + ∑ p i t e, f


###### ∑ I i t h, × p i h + ∑ p i t c, + ∑ p i t T, ≤− P t R S t d + ∑ p i t e, + ∑ p i t e, f, ∀



_I_ _i t_ h, × _p_ _i_ h



c


_i t_,



e


_i t_,



e _f_

_p_ _i t_



_i t_ T, ≤− _P_ _t_ R _S_ _t_ d



_I_ _i t_, × _p_ _i_ + _p_ _i t_ c, + _p_ _i t_, ≤− _P_ _t_ R _S_ _t_ + _p_ _i t_ e, + _p_ _i t_ e,, ∀ _t_ (21)

=1 =1 =1 =1 =1



_i_



1



_i_



1



_i_



1



_i_



1



_i_



1



where _[I]_ _i t_ h, is the binary variable indicating the on/off status at time _t_ of thermal
time plant and the _i_ [th] _p_ _t_ thermal unit, _i_, respectively, _i t_, c, indicate the maximum and minimum power outputs for the _p_ _ih_ and _p_ _i_ h are the nameplate capacity and minimal power output level for RR _WS_ _t_ u _t_ [ is the reserve contribution from wind power at time ] and R _S_ _t_ d are the upward and downward reserve margin at _i_ th CHP unit _[t]_ [, ] _p_ _i t_ c,



**420** **Nature Energy** [| VOL 3 | MAY 2018 | 413–421 | www.nature.com/natureenergy](http://www.nature.com/natureenergy)


© 2018 Macmillan Publishers Limited, part of Springer Nature. All rights reserved.


#### NATurE EnErgy Articles



for typical thermal and combined heat and power units, are available in
Supplementary Data 1. The other data that support the plots within this paper and
other findings of this study are available from the corresponding authors upon
reasonable request.


Received: 10 September 2016; Accepted: 15 March 2018;
Published online: 30 April 2018


**References**
1. _Air Quality Review of Key Areas and 74 Cities in 2014_ (in Chinese;
Ministry of Environmental Protection of People’s Republic of China, 2015);
[http://www.mep.gov.cn/gkml/hbb/qt/201502/t20150202_295333.htm](http://www.mep.gov.cn/gkml/hbb/qt/201502/t20150202_295333.htm)
2. Nielsen, C. P. & Ho, M. S. _Clearer Skies Over China: Reconciling Air Quality,_
_Climate, and Economics Goals_ (MIT Press, Cambridge, MA, 2013).
3. Zhang, Q., He, K. & Huo, H. Cleaning China’s air. _Nature_ **484**, 161–162 (2012).
4. Wu, Y. et al. On-road vehicle emission control in Beijing: past, present and
future. _Environ. Sci. Technol._ **45**, 147–153 (2011).
5. China Automotive Technology and Research Center (CATARC), Beijing
Guoneng Yingchuang Energy Information Technology Co., Ltd. & Editorial
Office of Year-book of Energy-saving and New-energy Vehicles _2011 Yearbook_
_of Energy-saving and New Energy_ _Vehicles_ (China Economic Publishing
House, Beijing, 2011).
6. _About the Support for Promotion of New-energy Vehicle in Shenyang,_
_Changchun etc_ [(in Chinese; Ministry of Finance, 2014); http://jjs.mof.gov.cn/](http://jjs.mof.gov.cn/zhengwuxinxi/tongzhigonggao/201402/t20140208_1041231.html)
[zhengwuxinxi/tongzhigonggao/201402/t20140208_1041231.html](http://jjs.mof.gov.cn/zhengwuxinxi/tongzhigonggao/201402/t20140208_1041231.html)
7. _The Circular on Developing the Demonstration of Subsidies for Private Purchase_
_of New Energy Vehicles_ (in Chinese; Ministry of Finance, Ministry of Science
and Technology, Ministry of Industry and Information Technology &
National Development and Reform Commission, 2010).
8. Li, F. China has the most public EV charging stations worldwide
[(China Daily, 2018); http://usa.chinadaily.com.cn/a/201801/11/](http://usa.chinadaily.com.cn/a/201801/11/WS5a5759d9a3102c394518e9e1.html)
[WS5a5759d9a3102c394518e9e1.html](http://usa.chinadaily.com.cn/a/201801/11/WS5a5759d9a3102c394518e9e1.html)
9. _Annual Report on the Development of Electric Vehicle Charging Infrastructure_
_in China (2016–2017)_ (in Chinese; National Energy Administration & China
[EV Charging Infrastructure Promotion Alliance, 2017); http://www.nea.gov.](http://www.nea.gov.cn/136376732_14978397401671n.pdf)
[cn/136376732_14978397401671n.pdf](http://www.nea.gov.cn/136376732_14978397401671n.pdf)
10. Wang, K. Beijing limits new car plates, boosts new energy vehicles
[(China Daily, 2017); http://www.chinadaily.com.cn/a/201712/15/](http://www.chinadaily.com.cn/a/201712/15/WS5a33819aa3108bc8c6734ecb.html)
[WS5a33819aa3108bc8c6734ecb.html](http://www.chinadaily.com.cn/a/201712/15/WS5a33819aa3108bc8c6734ecb.html)
11. _Over 40% of Residential Areas are Equipped with Charging Facilities_
(in Chinese; Commission of Housing and Urban-Rural Development of
[Beijing, 2016) http://www.bjjs.gov.cn/bjjs/xxgk/xwfb/317878/index.shtml](http://www.bjjs.gov.cn/bjjs/xxgk/xwfb/317878/index.shtml)
12. Huo, H., Zhang, Q., Liu, F. & He, K. Climate and environmental effects of
electric vehicles versus compressed natural gas vehicles in China: a life-cycle
analysis at provincial level. _Environ. Sci. Technol._ **47**, 1711–1718 (2013).
13. Wu, Y. et al. Energy consumption and CO 2 emission impacts of vehicle
electrification in three developed regions of China. _Energy Policy_ **48**,
537–550 (2012).
14. Kim, J. & Rahimi, M. Future energy loads for a large scale adoption of
electric vehicles in the city of Los Angeles: impacts on greenhouse gas (GHG)
emissions. _Energy Policy_ **73**, 620–630 (2014).
15. Zhang, S. et al. Historic and future trends of vehicle emissions in Beijing,
1998–2020: a policy assessment for the most stringent vehicle emission
control program in China. _Atmos. Environ._ **89**, 216–229 (2014).
16. Kang, C. et al. Balance of power: towards a more environmentally friendly,
efficient, and effective integration of energy systems in China. _IEEE Power_
_Energy Mag._ **11**, 56–64 (2013).
17. Wang, J. et al. Impact of plug-in hybrid electric vehicles on power systems
with demand response and wind power. _Energy Policy_ **39**, 4016–4021 (2011).
18. Sioshansi, R. & Denholm, P. Emissions impacts and benefits of plug-in hybrid
electric vehicles and vehicle-to-grid services. _Environ. Sci. Technol._ **43**,
1199–1204 (2009).
19. Liu, W., Hu, W., Lund, H. & Chen, Z. Electric vehicles and large-scale
integration of wind power—the case of Inner Mongolia in China.
_Appl. Energy_ **104**, 445–456 (2013).
20. _Global Wind Statistics 2017_ [(Global Wind Energy Council, 2018); http://gwec.](http://gwec.net/wp-content/uploads/vip/GWEC_PRstats2017_EN-003_FINAL.pdf)
[net/wp-content/uploads/vip/GWEC_PRstats2017_EN-003_FINAL.pdf](http://gwec.net/wp-content/uploads/vip/GWEC_PRstats2017_EN-003_FINAL.pdf)
21. _China Wind Energy Development Roadmap 2050_ (International Energy
[Agency, 2011); https://www.iea.org/publications/freepublications/publication/](https://www.iea.org/publications/freepublications/publication/china_wind.pdf)
[china_wind.pdf](https://www.iea.org/publications/freepublications/publication/china_wind.pdf)
22. _Grid Integration of China’s Wind Power Generation in 2016_ (in Chinese;
[National Energy Administration of China, 2017); http://www.nea.gov.](http://www.nea.gov.cn/2017-01/26/c_136014615.htm)
[cn/2017-01/26/c_136014615.htm](http://www.nea.gov.cn/2017-01/26/c_136014615.htm)



23. Beijing Municipal Bureau of Statistics _Beijing Statistical Yearbook_
(China Statistics Press, Beijing, China, 2014).
24. National Bureau of Statistics of China _China City Statistical Yearbook_
_1998–2013_ (China Statistics Press, Beijing, China, 1998–2013).
25. Luo, Z., Hu, Z., Song, Y., Xu, Z. & Lu, H. Optimal coordination of plug-in
electric vehicles in power grids with cost-benefit analysis—part II: a case
study in China. _IEEE Trans. Power Syst._ **28**, 3556–3565 (2013).
26. Sun, R., Yu, H. & Du, Y. Time space distribution characteristics of
taxi shift in Beijing. _J. Transp. Syst. Eng. Inf. Technol._ **14**, 221–228 (2014)
(in Chinese).
27. _Beijing Transport Annual Report_ (in Chinese; Beijing Transportation Research
[Center, 2013); http://www.bjtrc.org.cn/](http://www.bjtrc.org.cn/)
28. _Emission Standard of Air Pollutants for Thermal Power Plants_ GB 13223-2011
(Ministry of Environmental Protection of China, 2011).
29. _Five Companies Got the Bidding for 914 Electric Buses in Qingdao with a Total_
_Amount of about 900 Million RMB_ (in Chinese; Electric Vehicle Resources
[Network, 2017); http://www.sohu.com/a/196982793_233844](http://www.sohu.com/a/196982793_233844)
30. _The Clean Air Action Plan 2013–2017_ (in Chinese; Beijing Municipal
[Government, 2013); http://fgcx.bjcourt.gov.cn:4601/law?fn=​lar830s997.txt](http://fgcx.bjcourt.gov.cn:4601/law?fn=lar830s997.txt)
31. Subramanian, A., Garcia, M. J., Callaway, D. S., Poolla, K. & Varaiya, P.
Real-time scheduling of distributed resources. _IEEE Trans. Smart Grid_ **4**,
2122–2130 (2013).
32. Xu, Z., Callaway, D. S., Hu, Z. & Song, Y. Hierarchical coordination
of heterogeneous flexible loads. _IEEE Trans. Power Syst._ **31**,
4206–4216 (2016).
33. Zhang, H., Hu, Z., Xu, Z. & Song, Y. Evaluation of achievable vehicle-to-grid
capacity using aggregate PEV model. _IEEE Trans. Power Syst._ **32**,
784–794 (2017).
34. Carrión, M. & Arroyo, J. M. A computationally efficient mixed-integer linear
formulation for the thermal unit commitment problem. _IEEE Trans. Power_
_Syst._ **21**, 1371–1378 (2006).
35. Chen, X. et al. Increasing the flexibility of combined heat and power for wind
power integration in China: modeling and implications. _IEEE Trans. Power_
_Syst._ **30**, 1848–1857 (2015).
36. Wang, S. J., Shahidehpour, S. M., Kirschen, D. S., Mokhtari, S. & Irisarri, G.
D. Short-term generation scheduling with transmission and environmental
constraints using an augmented Lagrangian relaxation. _IEEE Trans._
_Power Syst._ **10**, 1294–1301 (1995).
37. Chen, X., Lu, X., McElroy, M. B., Nielsen, C. P. & Kang, C. Synergies of wind
power and electrified space heating: case study for Beijing. _Environ. Sci._
_Technol._ **48**, 2016–2024 (2014).
38. Geng, Z., Chen, Q., Xia, Q., Kirschen, D. S. & Kang, C. Environmental
generation scheduling considering air pollution control technologies and
weather effects. _IEEE Trans. Power Syst._ **32**, 127–136 (2017).
39. McElroy, M. B., Lu, X., Nielsen, C. P. & Wang, Y. Potential for wind generated
electricity in China. _Science_ **325**, 1378–1380 (2009).
40. _Connection Set for Conductive Charging of Electric Vehicles_ GB/T 20234.22015 (General Administration of Quality Supervision, Inspection and
Quarantine, 2015).


**Acknowledgements**
The research was funded in part by the Harvard Global Institute. Additional support
was provided by the Hui Fund of the Ash Center of the Harvard Kennedy School of
Government. This research is also supported by the State Key Laboratory on Smart
Grid Protection and Operation Control of NARI Group, through the open topic
project (20171613).


**Author contributions**

All authors contributed to all aspects of this work.


**Competing interests**
The authors declare no competing interests.


**Additional information**

**Supplementary information** [is available for this paper at https://doi.org/10.1038/](https://doi.org/10.1038/s41560-018-0133-0)

[s41560-018-0133-0.](https://doi.org/10.1038/s41560-018-0133-0)


**Reprints and permissions information** [is available at www.nature.com/reprints.](http://www.nature.com/reprints)


**Correspondence and requests for materials** should be addressed to X.C. or M.B.M.


**Publisher’s note:** Springer Nature remains neutral with regard to jurisdictional claims in
published maps and institutional affiliations.



**Nature Energy** [| VOL 3 | MAY 2018 | 413–421 | www.nature.com/natureenergy](http://www.nature.com/natureenergy) **421**


© 2018 Macmillan Publishers Limited, part of Springer Nature. All rights reserved.




---

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenImpactsFleetTypes2018a/chenImpactsFleetTypes2018a.pdf-0-0.png)



In the format provided by the authors and unedited.

# **Impacts of fleet types and charging modes for** **electric vehicles on emissions under different** **penetrations of wind power**


**Xinyu Chen** **[1,6]** **[*, Hongcai Zhang](http://orcid.org/0000-0002-8294-6419)** **[2,6]** **, Zhiwei Xu** **[2,6]** **, Chris P. Nielsen** **[3,6]** **, Michael B. McElroy** **[4,6]** *** and Jiajun Lv** **[5,6]**


1 School of Engineering and Applied Sciences, Harvard University, Cambridge, MA, USA. 2 Department of Electrical Engineering, Tsinghua University,
Beijing, China. [3] Harvard China Project and School of Engineering and Applied Sciences, Harvard University, Cambridge, MA, USA. [4] School of Engineering
and Applied Sciences and Department of Earth and Planetary Sciences, Harvard University, Cambridge, MA, USA. [5] School of Electrical Engineering,
Xi’an Jiaotong University, Xi’an, Shaanxi, China. [6] These authors contributed equally: Xinyu Chen, Hongcai Zhang, Zhiwei Xu, Chris P. Nielsen,
[Michael B. McElroy and Jiajun Lv *e-mail: <EMAIL>; <EMAIL>](mailto:<EMAIL>)


**Nature Energy** [| www.nature.com/natureenergy](http://www.nature.com/natureenergy)


© 2018 Macmillan Publishers Limited, part of Springer Nature. All rights reserved.


**Supplementary Figures**

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenImpactsFleetTypes2018a/chenImpactsFleetTypes2018a.pdf-1-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenImpactsFleetTypes2018a/chenImpactsFleetTypes2018a.pdf-1-1.png)


**Supplementary Figure 1 | Linear projection on number of buses in 2020.** The linear
regression (indicated by solid line) using historical data from 1997 to 2012 (indicated by
triangle).


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenImpactsFleetTypes2018a/chenImpactsFleetTypes2018a.pdf-2-0.png)

**Supplementary Figure 2 | Arrival and Leaving time distributions at working place**
**and home for light duty vehicles.** Probabilities for departing time (indicated by solid
line) and arrival time (indicated by dashed line) in the morning ( **a** ) and in the afternoon
( **b** ).


80



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenImpactsFleetTypes2018a/chenImpactsFleetTypes2018a.pdf-3-0.png)



70


60


50


40


30


20


10


0
BAU Pub−f Prv−f Prv−s Cmb−f Cmb−s


**Supplementary Figure 3 | Total SO** **2** **emissions relative to BAU under different EV**
**scenarios and wind penetration levels.** SO 2 emissions with different combinations of
wind penetrations (indicated by color) and EV strategies (indicated by column).


9


8



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenImpactsFleetTypes2018a/chenImpactsFleetTypes2018a.pdf-4-0.png)



7


6


5


4


3


2


1


0
BAU Pub−f Prv−f Prv−s Cmb−f Cmb−s


**Supplementary Figure 4 | Total PM** **2.5** **emissions relative to BAU under different EV**
**scenarios and wind penetration levels.** Primary PM 2.5 emissions with different
combinations of wind penetrations (indicated by color) and EV strategies (indicated by
column).


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenImpactsFleetTypes2018a/chenImpactsFleetTypes2018a.pdf-5-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenImpactsFleetTypes2018a/chenImpactsFleetTypes2018a.pdf-5-1.png)
###### **Beijing** **Jing-Jin-Tang area** **Other controlling area** **Inter-regional transmission**

**Supplementary Figure 5 | Illustrative geographical coverage of Jing-Jin-Tang**
**energy systems.** The Jing-Jin-Tang regional energy system covers Beijing, and
surrounding areas, including Tianjin, majority of Hebei, and parts of Inner Mongolia and


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenImpactsFleetTypes2018a/chenImpactsFleetTypes2018a.pdf-6-1.png)



Power balance for Sep. 1 [st] −14 [th]



80


60


40


20



80


60


40


20



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenImpactsFleetTypes2018a/chenImpactsFleetTypes2018a.pdf-6-3.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenImpactsFleetTypes2018a/chenImpactsFleetTypes2018a.pdf-6-5.png)



**a**


**b**


**c**



Power balance for Mar. 10 [th] −23 [rd]
80



80


60


40


20



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenImpactsFleetTypes2018a/chenImpactsFleetTypes2018a.pdf-6-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenImpactsFleetTypes2018a/chenImpactsFleetTypes2018a.pdf-6-2.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenImpactsFleetTypes2018a/chenImpactsFleetTypes2018a.pdf-6-4.png)





60


40


20


0
50 100 150 200 250 300



0
50 100 150 200 250 300



80


60


40


20





80


60


40


20





0
50 100 150 200 250 300



**d**


**e**


**f**



0
50 100 150 200 250 300



0
50 100 150 200 250 300

Hours



0
50 100 150 200 250 300

Hours



Pthermal Pchp Phydro Ptrans Pwind Pwind cut


**Supplementary Figure 6 | Hourly power balances for two representative time**
**periods in JJT area.** Hourly power balances for March 10 [th] -23 [rd] (left panels) and
September 1 [st] -14 [th] (right panels) are presented under scenarios Pub-f ( **a**, **d** ), Prv-f ( **b**, **e** ),
and Prv-s ( **c**, **f** ) at 20% wind penetration level.


**Supplementary Tables**


**Supplementary Table 1 | Projections on vehicle population and annual vehicle**
**kilometers traveled (VKT) of Beijing in 2020** .















|Vehicle<br>classificationa|Abbreviation|Fuel type|Population size|Annual VKT<br>(km)|
|---|---|---|---|---|
|Light-duty<br>Passenger Vehicle<br>Category 1|LDV1|Gasoline|83856|14500|
|Light-duty<br>Passenger Vehicle<br>Category 2|LDV2|Gasoline|5493279|18000|
|Light-duty<br>Passenger Vehicle<br>Category 2|LDV2|Diesel|14620|14620|
|Medium-duty<br>Passenger Vehicle|MDV|Gasoline|67351|29500|
|Medium-duty<br>Passenger Vehicle|MDV|Diesel|16015|16015|
|Heavy-duty<br>Passenger Vehicle|HDPV|Gasoline|589|29800|
|Heavy-duty<br>Passenger Vehicle|HDPV|Diesel|32178|32178|
|Heavy-duty Truck<br>Category 1|HDT1|Gasoline|81873|31300|
|Heavy-duty Truck<br>Category 1|HDT1|Diesel|82738|82738|
|Heavy-duty Truck<br>Category 2|HDT2|Gasoline|353|43000|
|Heavy-duty Truck<br>Category 2|HDT2|Diesel|33485|33485|
|Heavy-duty Truck<br>Category 3|HDT3|Gasoline|926|44300|
|Heavy-duty Truck<br>Category 3|HDT3|Diesel|66332|66332|
|Taxi|Taxi|Gasoline|66646|126000|
|Bus|Bus|Diesel/Natural<br>Gas|30499b|58000|
|Motorcycle<br>|MC<br>|Gasoline<br>|100000<br>|5900<br>|


a The vehicle classification method is based on the analysis in [9, 10]. Rural vehicles and
other specific vehicles (e.g., military vehicles) are excluded in this study.
b The linear regression to estimate the number of bus in 2020 is illustrated in
Supplementary Figure 1.


**Supplementary Table 2 | Parameters for Simulation of EVs in Beijing.**























































































|Vehicle type|Day type|Charging<br>times/day|Charging<br>mode|Charging<br>location|Arrival<br>time|Constraints<br>on charging<br>time|Probability<br>of charging|Initial SOCa|Charging<br>power<br>(kW)|Battery<br>capacity<br>(kWh)|
|---|---|---|---|---|---|---|---|---|---|---|
|Bus|Weekday/<br>weekend|2|Fast|Charging<br>station|10:00-16:30|No|1|_N_ (0.5, 0.1<br>2)|150|96|
|Bus|Weekday/<br>weekend|2|Fast|Charging<br>station|23:00-5:30|23:00-5:30|1|<br>_N_ (0.5, 0.1<br>2)<br><br>|<br>_N_ (0.5, 0.1<br>2)<br><br>|<br>_N_ (0.5, 0.1<br>2)<br><br>|
|Taxi<br>(Double shift)|Weekday/<br>weekend|2|Fast|Charging<br>station|2:00-4:00|No|1|<br>_N_ 0.2, 0.1<br>2<br><br><br><br>|60|64|
|Taxi<br>(Double shift)|Weekday/<br>weekend|2|Fast|Charging<br>station|13:30-15:30|13:30-15:30|1|<br><br><br>_N_ 0.2, 0.1<br>2<br><br><br><br>|<br><br><br>_N_ 0.2, 0.1<br>2<br><br><br><br>|<br><br><br>_N_ 0.2, 0.1<br>2<br><br><br><br>|
|Taxi<br>(Single shift)|Weekday/<br>weekend|1|Fast|Charging<br>station|20:00-22:00|No|1|<br><br><br>_N_ 0.2, 0.1<br>2<br>~~Q~~<br>~~V~~|60|64|
|Private vehicle|Weekday|1|Fast|Charging<br>station|11:00-13:00<br>|No|0.2|_N_ (0.6, 0.1<br>2)|60|32|
|Private vehicle|Weekday|1|Fast|Charging<br>station|17.30-21:30~~c ~~|17.30-21:30~~c ~~|0.8|_N_ (0.6, 0.1<br>2)|_N_ (0.6, 0.1<br>2)|_N_ (0.6, 0.1<br>2)|
|Private vehicle|Weekend|1|Fast|Charging<br>station|9:00-21:00|No|0.5|<br>_N_ (0.6, 0.1<br>2)|60|32|
|Private vehicle|Weekend|1|Fast|Charging<br>station|16:00-22:00|16:00-22:00|0.5|_N_ (0.6, 0.1<br>2)|_N_ (0.6, 0.1<br>2)|_N_ (0.6, 0.1<br>2)|
|Private vehicle|Weekday|1|Slow|Working<br>place|6:30-10:00b <br>|No|0.2|<br>_N_ (0.6, 0.1<br>2)|7|32|
|Private vehicle|Weekday|1|Slow|Home|17:30-21:30~~c ~~|No|0.7|<br>_N_ (0.6, 0.1<br>2)|3|3|
|Private vehicle|Weekday|1|Slow|Shopping<br>place|19:00-21:00|75 minutes|0.1|<br>_N_ (0.6, 0.1<br>2)|7|7|
|Private vehicle|Weekend|1|Slow|Shopping<br>place|9:00-21:00|75 minutes|0.5|<br>_N_ (0.6, 0.1<br>2)|7|32|
|Private vehicle|Weekend|1|Slow|Home|16:00-22:00|No|0.5|_N_ (0.6, 0.1<br>2)|3|3|


a _N_ ( _a_, _b_ 2 ) denotes normal distribution with _a_ mean and _b_ standard deviation.
b The temporal distribution of EVs’ charging at working places is determined by their arrival time (since departure from home). To
obtain this distribution, we use the data from Beijing Transport Annual Report [11] and is illustrated in the upper figure of
Supplementary Figure 2.
c The temporal distribution of EVs’ charging at residential places is determined by their arrival time (since departure from work). To
obtain this distribution, we use the data from Beijing Transport Annual Report [11] and is illustrated in the lower figure of
Supplementary Figure 2.





_N_ ( _a_, _b_ 2 ) denotes normal distribution with _a_ mean and _b_ standard deviation.



2 )


**Supplementary Table 3 | Sensitivity analysis on penetration rate of electrical LDVs**
**for wind curtailment, and emissions for 2020 assuming zero contribution from wind.**












|Scenario|Penetration<br>rate|Curtailed wind<br>(GWh)|NO (kT)<br>x|CO (mT)<br>2|SO (kT)<br>2|PM (kT)<br>2.5|
|---|---|---|---|---|---|---|
|BAU|0%|NA|73.68|163.38|59.51|6.48|
|Prv-f|20%|NA|73.47|163.84|60.08|6.51|
|Prv-f|50%|NA|73.29|165.12|61.27|6.59|
|Prv-f|100%|NA|73.30|168.61|64.05|6.80|
|Prv-s|20%|NA|73.57|164.27|60.38|6.54|
|Prv-s|50%|NA|73.44|165.71|61.74|6.63|
|Prv-s|100%|NA|73.14|167.80|63.82|6.77|


**Supplementary Table** **4 | Sensitivity analysis on penetration rate of electrical LDVs**
**for wind curtailment, and emissions for 2020 under 20% of wind.**












|Scenario|Penetration<br>rate|Curtailed wind<br>(GWh)|NO (kT)<br>x|CO (mT)<br>2|SO (kT)<br>2|PM (kT)<br>2.5|
|---|---|---|---|---|---|---|
|BAU|0%|7484.24|67.10|132.48|46.58|5.25|
|Prv-f|20%|7487.00|67.01|133.47|47.43|5.31|
|Prv-f|50%|8918.68|67.05|135.81|49.04|5.43|
|Prv-f|100%|16606.70|68.17|144.59|53.90|5.83|
|Prv-s|20%|6699.89|66.86|132.78|47.17|5.28|
|Prv-s|50%|5718.08|66.54|133.37|48.11|5.34|
|Prv-s|100%|4416.36|66.05|134.59|49.77|5.43|


**Supplementary Table 5 | Sensitivity analysis on penetration rate of electrical LDVs**
**for wind curtailment, and emissions for 2020 under 40% of wind.**












|Scenario|Penetration<br>rate|Curtailed wind<br>(GWh)|NO (kT)<br>x|CO (mT)<br>2|SO (kT)<br>2|PM (kT)<br>2.5|
|---|---|---|---|---|---|---|
|BAU|0%|52883.9|63.53|115.72|39.56|4.58|
|Prv-f|20%|52622.4|63.43|116.65|40.41|4.64|
|Prv-f|50%|55889.9|63.84|120.70|42.77|4.83|
|Prv-f|100%|68141.1|65.63|132.68|48.90|5.35|
|Prv-s|20%|50919.1|63.14|115.29|39.85|4.59|
|Prv-s|50%|48259.2|62.58|114.78|40.33|4.60|
|Prv-s|100%|44168.8|61.68|114.08|41.17|4.62|


**Supplementary Table 6 | Sensitivity analysis on the charging frequency for CO** **2**

|emissions.|Col2|Col3|Col4|Col5|Col6|Col7|
|---|---|---|---|---|---|---|
|CO2 (mT)|CO2 (mT)|Wind level|Wind level|Wind level|Wind level|Wind level|
||Scenario|0%|10%|20%|30%|40%|
|TPD1a|Prv-f|168.61|154.53|144.59|137.55|132.68|
|TPD1a|Prv-s|167.80|150.12|134.59|122.31|114.08|
|TPD2a|Prv-f|171.18|156.66|146.30|139.06|134.02|
|TPD2a|Prv-s|169.95|152.42|136.92|124.99|117.01|
|Deviationb <br>|Prv-f|1.5%|1.4%|1.2%|1.1%|1.0%|
|Deviationb <br>|Prv-s<br>|1.3%<br>|1.5%<br>|1.7%<br>|2.2%<br>|2.6%<br>|



a TPD1 and TPD2 represent the two cases that electric vehicles are charged once per day
and once every two days, respectively.
b The deviation is calculated based on the absolute value for the relative difference

between TPD1 and TPD2 in the same scenario.


**Supplementary Table 7 | Nomenclature for the integrated energy system**
**optimization model.**


|Variables|Description|
|---|---|
|,<br>_e_<br>_e_<br>_i,t_<br>_i,t_<br>_p_<br>_e_<br>|aggregated slow charging power and equivalent energy level for the_i_th EV fleet at time_t_|
|,<br>_h_<br>_w_<br>_i,t_<br>_i,t_<br>_p_<br>_p_|power production from the_i_th conventional power plant and wind farm at time_t_|
|_T_<br>_i,t_<br>_p_|power imported from the_i_th transmission corridor at time_t_|
|,<br>_e_<br>_e_<br>_i,t_<br>_i,t_<br>_p_<br>~~_p_~~<br>|minimum and maximum load for slow-charging EV fleet_i_ at time_t_ for reserve requirements|
|,<br>_c_<br>_c_<br>_i,t_<br>_i,t_<br>_p_<br>_q_|power and heat production from the_ith_ CHP unit at time_t_|
|_W_<br>_C_<br>|penalty term for wind curtailment|
|,<br>_h_<br>_c_<br>_i,t_<br>_i,t_<br>_C_<br>_C_|fuel cost for the_i_th conventional power plant and CHP unit at time_t _respectively<br>|
|,<br>_h_<br>_c_<br>_i,t_<br>_i,t_<br>_I_<br>_I_<br>|binary variable indicating the online/offline status of the_i_~~_th_~~ thermal unit and CHP unit at time_t _<br>respectively|
|,<br>_c_<br>_c_<br>~~_i_~~_,t_<br>_i,t_<br>_P _~~_P_~~|minimum and maximum power output for the_i_th CHP unit at time_t_ respectively|
|_t_<br>_RW_|reserve contribution from wind power at time_t_|
|,<br>_h_<br>_c_<br>_i,t_<br>_i,t_<br>_S_<br>_S_<br>|start-up cost for the_i_th conventional power plant and CHP unit at time_t _respectively|
|_k_<br>_i,t_<br>α<br>|combination coefficient for the_kth_ corner point of the_ith_ CHP unit at time_t_|
|Parameters|Description|
|_i, j_<br>_a_<br>|connectivity between the_i_th heating source and the_j_th heating district|
|,<br>_i,t_<br>_i,t_<br>_e_<br>_e_<br>−<br>+|lower and upper bounds for the cumulative energy consumed by the EV fleet_i_ at time_t_|
|,<br>_i,t_<br>_i,t_<br>_p_<br>_p_<br>−<br>+|lower and upper bounds for the aggregated charging power of the EV fleet_i_ at time_t_|
|_ef_<br>_i,t_<br>_p_<br> <br>|fast charging load for the EV fleet_i_ at time_t_|
|_t_<br>~~Δ~~|duration between two consecutive time intervals<br>|
|,<br>,<br>_k_<br>_k_<br>_k_<br>_i_<br>_i_<br>_i_<br>_x_<br>_y_<br>_c_|power output, heat production and corresponding fuel cost for the_k_~~_th_~~ corner point of the_i_~~_th_~~ CHP<br>unit respectively|
|_i_<br>_G_|minimum time period required to restart (or shutdown) at the initial time interval for_ith_ CHP|
|_M_|number of corner points for CHP units|
|,<br>,<br>,<br>_h_<br>_w_<br>_c_<br>_T_<br>_N_<br>_N_<br>_N N_|numbers of conventional thermal power plants, wind farms, CHP units, and transmission corridors<br>respectively|
|,<br>_e_<br>_ef_<br>_N_<br>_N_<br>|number of EV fleets incorporating slow and fast charging respectively|
|_tP_|system load at time_t_|
|,<br>_h_<br>_h_<br>~~_i_~~<br>_i_<br>_P_<br>~~_P_~~|minimum power output and nameplate capacity for the_ith_ thermal unit respectively|
|_w_<br>_i,t_<br>~~_P_~~|available wind power from wind farm_i_ at time_t_|
|_c_<br>_i_~~_P_~~|maximum power output from the_i_th CHP unit|
|_j,t_<br>_Q_<br>|heating demand in heating district_j_ at time_t_|
|,<br>_u_<br>_d_<br>_i_<br>_i_<br>_R_<br>_R_|ramp-up and ramp-down limits for the_i_th CHP unit respectively|
|,<br>_u_<br>_d_<br>_t_<br>_t_<br>_RS_<br>_RS_<br>|up- and downward reserve margin at time_t _respectively|
|,<br>_u_<br>_d_<br>_i_<br>_i_<br>_S_<br>_S_|ramping limits for the_i_th CHP unit when starting up or shutting down respectively|
|_T_|number of time intervals|
|,<br>_i_<br>_i_<br>_UT DT_ <br>|minimum on and off times of the_ith_ CHP unit respectively|
|~~θ~~|penalty factor for wind curtailment|
|_i_λ|start-up cost for the_i_th CHP unit|


**Supplementary Table 8 | Inter-regional transmission capacity for JJT area in 2020.**







|Col1|Corridor connectivity|Voltage level|Number of<br>corridorse|Transmission<br>capacityf|
|---|---|---|---|---|
|1|WIM->JJTa,b|1000kV AC|1|3 GW|
|2|EIM->JJT->SDb|1000kV AC|1|3 GWg|
|3|IM->JJTb|500kV AC|2|3.95 GW|
|4|SX->JJTb|500kV AC|3|1.4 GW|
|5|NE->JJTb|±500kV DCc|1|3 GW|
|6|JJT->southern Hebeid|500kV AC|1|0.15 GW|
|7 <br>|JJT->SD<br>|500kV AC<br>|2 <br>|3.5 GW|


a The direction of the arrow represents the direction of power flow.
b WIM, IM, EIM, SX, SD and NE represent Western Inner Mongolia, Inner Mongolia,
Eastern Inner Mongolia, Shanxi, Shandong and Northeastern China, respectively.
c A DC back-to-back project.
d Southern Hebei is a small part of Hebei province that does not fall into the jurisdiction
of the JJT dispatch center.
e One corridor can have several parallel transmission lines.
f The transmission capacity is lower than the rated capacity, which accounts for the
limitations for stability and contingencies, as well as for inter-provincial power exchange

contracts.

g The capacity only accounts for the portion delivered to JJT area.




**Supplementary Table 9 | Projected Average fleet emission factor in 2020** [9] **.**


|Vehicle type|Fuel type|NO (g/km)<br>x|SO (g/km)<br>2|CO (g/km)<br>2|PM (g/km)<br>2.5|
|---|---|---|---|---|---|
|LDV1|Gasoline|0.06|0.004|134.4|0.004|
|LDV2|Gasoline|0.08|0.005|169.2|0.004|
|LDV2|Diesel|0.19|0.036|164.6|0.019|
|MDV|Gasoline|0.78|0.009|285.1|0.012|
|MDV|Diesel|1.83|0.061|277.78|0.056|
|HDPV|Gasoline|1.03|0.018|556.31|0.048|
|HDPV|Diesel|7.37|0.119|540.12|0.177|
|HDT1|Gasoline|0.12|0.008|266.57|0.006|
|HDT1|Diesel|0.77|0.061|277|0.017|
|HDT2|Gasoline|1.16|0.014|438.1|0.053|
|HDT2|Diesel|2.56|0.100|455.25|0.036|
|HDT3|Gasoline|0.92|0.021|658.3|0.05|
|HDT3|Diesel|3.01|0.151|684.16|0.04|
|Taxi|Gasoline|0.09|0.005|169.2|0.003|
|Bus|Diesel|6.49|0.176|827|0.069|
|Bus|Natural gas|5.85|0.0121|885|0.024|
|MC|Gasoline|0.06|0.002|57.95|0.004|


**Supplementary Table 10 | Emission factor by power plant type** [10] **.**


|Plant type|NO (mg/kWh)<br>x|SO (mg/kWh)<br>2|CO (g/kWh)<br>2|PM (mg/kWh)<br>2.5|
|---|---|---|---|---|
|Coal|263|525|1229.45 [3]|50|
|Natural gas|62.9|6.0|375.6|0.8|


**Supplementary Notes**


**Supplementary Note 1. Current situation and projections of vehicle population and**


**annual vehicle kilometers traveled.**


Beijing leads Chinese cities in terms of vehicle ownership, with a total vehicle


population of 5.62 million in 2015 (excluding a small number of vehicles falling under


unusual statistical classifications). LDVs constitute the largest share of the total vehicle


fleet, 4.40 million in 2015. To restrict the rapid growth of Beijing’s vehicle stock, a


license control policy was initiated in January 2011 limiting new car sales to a monthly


quota of 20,000, tightened to 12,000 per month in 2014 under the country’s Clean Air


Action Plan. Based on the current license control policy, the total number of LDVs is


projected to reach 5.6 million by 2020. Current annual kilometers traveled per vehicle


(VKT) for LDVs in China is estimated at 18,000 km.


The population of public buses in Beijing amounted to 22,146 in 2012 and is


projected to climb to 30,499 by 2020 based on a linear extrapolation of data from 1997

2012 (see Supplementary Figure 1). The annual VKT for buses is estimated at 58,000 km


in 2020, more than 3 times the average for private LDVs.


The Municipal Transportation Commission has limited the total number of taxis in


Beijing to about 66,000 since 2000. We assume that this limit will continue to 2020. Taxis


in Beijing have extremely high VKT values reflecting the constrained fleet size and the


high demand for services. The average annual VKT for taxis has increased from 97,000


km in 1998 to a relatively stable level of 126,000 km per year since 2009 (more than 7


times the average VKT of private LDVs). The population size and VKT data for other


types of vehicle fleets are summarized in Supplementary Table 1.


**Supplementary Note 2. Methodology for estimating the number of scrapped vehicles**


**during 2015-2020.**


In this paper, we not only consider the growth in the fleet size of LDVs, but also


consider the potential of replacement of the scrapped LDVs, which accounts for the


number of scrapped LDVs in 2015 and 2016, and projected replacement between 2017


and 2020. The dynamics of fleet turnover, which is quantified by the scale of new


vehicles entering into the fleet, the number of retired vehicles from the fleet and the


model-year distribution of in-fleet vehicles on each year, are required to estimate the


number of scrapped vehicles. Here, we adopt a Monte Carlo simulation method to


simulate the dynamics of the fleet based on the data of private LDVs stocks from 1987 to


2016 and new vehicle sales projection from 2017 to 2020. By assuming that the 7000


private LDVs in 1987 are all newly purchased vehicles at that year, we stochastically


generate the number of survival years for each vehicle based on the statistical distribution


of vehicle survival rates in Beijing [1]. It is projected that there are 1187378 scrapped


vehicles between 2015 and 2020.


**Supplementary Note 3. Impacts on SO** **2** **and primary PM** **2.5** **emissions.**


Power generation is responsible for 97% of SO 2 emissions. The contribution from


the transportation sector is relatively minor since ultra-low sulfur gasoline and diesel


have been mandated in Beijing and its surroundings (Hebei and Tianjin) since 2015.


Electrification of public fleets and private LDVs will both increase overall SO 2


emissions: the increase from the electrified public fleet will amount to at most 1%.


Electrified LDVs, at high wind penetration levels (40%), will result in an increase in


emissions of 4.1% or 23.8% corresponding to slow or fast charging, respectfully.


Emissions of primary PM 2.5 are similar. Electrification of public fleets reduces the overall


emissions by about 1%. Electrified LDVs with slow charging would result in similar


primary PM 2.5 . LDVs with fast charging, on the other hand, would increase emissions by


as much as 16.8% at 40% wind level, a consequence once again of the higher curtailment


of wind at elevated penetration levels. Detailed primary SO 2 and PM 2.5 emissions for


different scenarios under different penetration levels are summarized in the


Supplementary Figure 3 and Supplementary Figure 4.


**Supplementary Note 4. Sensitivity analysis on the environmental impact resulting**


**from lower growth of light duty electric vehicles.**


In this paper, the number of electric private LDVs is assumed based on the number


of new LDVs sold between 2017 and 2020, which is estimated based on two factors: (1)


projected vehicle growth and (2) projected retirements and replacements of existing


vehicles. For scenarios associated with electrified LDVs, we consider the sensitivity


analysis for the total number of LDVs to vary between 0%, 20%, 50% and 100% of the


projected vehicle growth. Results are illustrated through Supplementary Table 3-6.


Conclusions in the main text still holds at lower vehicle penetration level.


**Supplementary Note 5. JJT energy systems and simulation setup.**


The simulation for wind curtailment and quantifying emission changes is based on


the Jing-Jin-Tang (JJT) regional energy systems. The regional power system covers


Beijing, and neighboring areas, including Tianjin, Hebei, and parts of Inner Mongolia and


Shanxi province. The geographical coverage of Beijing and JJT area is shown in


Supplementary Figure 5. The total annual power demand in the region in 2014


approximately equal to the annual electricity consumption of the combined Nordic


countries. The generation mix in the area is constituted mainly by conventional thermal


units and combined heat and power (CHP) units, complemented by the interregional


transmission.


The total inter-regional transmission import for JJT area will reach approximately 10


GW in total in 2020. JJT imports 3GW from Eastern Inner Mongolia and 3GW from


Western Inner Mongolia through 1000kV ultra-high voltage transmission lines, another


3.95GW from Inner Mongolia via lower voltage transmission lines. The imports are


supplemented further by 1.4 GW from Shanxi and by 3GW from Northeastern China. JJT


also exports 0.15GW to southern Hebei and 3.5GW to Shandong. A summary of the


inter-regional transmission corridors and related capacities is presented in Supplementary


Table 8. The planned transmission capacities for the two 1000kV AC transmission


corridors are consistent with the 13th five-year-plan, a general description of which is


available in [2]. The capacities of the other five major corridors with 500kV transmission


lines are provided by the Northern China dispatch center of the State Grid Cooperation of


China. The schedule of hourly transmitted power for the inter-regional transmission lines


is predetermined based on the annual contracts between different provincial level


dispatch centers, except for contingencies. In other words, the hourly values of _p_ _i t_ in the _T_,


IESOM model are subject to equality constraints. Under the current contracting regime,


the power capacity during the off-peak hours (0 am to 6 am) is equal to half the


transmitted capacity listed in Supplementary Table 8.


The capacity for the intra-regional power transmission within the JJT region is


sufficient. As an example, seven 500kV substations connect Beijing and Hebei province


with a total capacity of 25GVA [3], larger than the projected peak power demand in


Beijing (23GW) in 2020. Reference [4] also stated that “BJ, TJ and IM are connected to


HB with sufficient tie lines”. The redundant transmission capacity within the area reflects


the incentive for the monopoly grid companies, and their major responsibility is to ensure


reliable power supply for the capital district [3]. Thus, the transmission limitations within


the JJT region are negligible and ignored.


The unit information (including unit type, fuel type and generation capacity) for the


JJT regional power systems in 2020 is derived from SGCC (for units before 2014) and


from Global Coal Plant Tracker (for units constructed or planned after 2014). The CHP


units constitute a total generation capacity of 32.5GW, of which 30.5GW are coal-fired.


The installed capacity for conventional thermal units will reach 32.5GW, of which 2.3


GW are gas-fired. In this analysis, we don't consider the possibility of converting coal

fired units to gas-fired ones for the units operational before 2014.


The CHP units in the JJT area serve heating demand to 8 major heating


districts/cities during the heating season, from November to March. During this time


period, the power outputs from CHPs are constraint by the heating demand. The hourly


heating demand for each heating district is determined jointly by the annual heating


consumption and hourly variation of ambient temperature. The former is provided in [5],


and the latter is derived by NASA meteorological database [6]. The operational


boundaries for 40 CHP units are provided by North China Power Grid. The operational


boundaries for the rest of CHP units are estimated based on the 40 known CHP


boundaries according to a similar capacity and type.


In the grid code of the North China power system, the positive spin reserve for power


demand is equal to the largest capacity of a single online conventional unit, currently


1000MW. The spin reserve requirement for the intermittent resource is determined by the


current wind forecasting level in China, as detailed in [7]. The downward reserve margin


is set at 5% of the power demand. We note that the downward reserve constraint is not


binding as both wind power and electric vehicles can provide downward reserve. Thus


this constraint does not influence the simulation results.


For each of the 30 combinations of the electric vehicle scenarios and wind power


levels, the simulation is conducted on an hourly basis for the entire year. The annual


simulation is rolling updated on a daily basis, and the daily optimization model will look


ahead for another 24 hours. At such, each optimization cycle will cover 48 hours, and the


model runs sequentially 365 times for an annual simulation. The optimization results for


the previous day will determine the initial status of electric vehicles (initial SOC, vehicle


connectivity status) for the first time period of the following day. The simulation structure


that incorporating rolling-update and look-ahead technics ensures the continuity for the


charging behavior of electric vehicles and operational status of the power plants, which is


important when significant amount of slow-charging electric vehicles are charged


overnights. The model is solved by Mixed Integer Linear Optimization Programming


provided by the CPLEX optimizer on Harvard Odyssey high performance computing


systems. The hourly power balances for two typical 14-day periods in the JJT area for


three EV scenarios are presented in Supplementary Figure 6.


**Supplementary Note 6. Allocation algorithm for determining charging power for**


**each EV.**


After optimizing the power consumption curve for the entire EV fleet, the aggregate


power curve is used to further allocate to each EV. The intelligent allocation algorithm is


mainly based on EVs’ charging priorities. Specifically, the EVs’ charging priorities are


primarily determined by their charging laxity [8]. For EV _j_, its laxity value is defined as


follows.


π = _K_ − _D_
_j_ _j_ _j_ (3)


where _K_ is the remaining parking duration of this EV _j_ _j_ before its earliest departure for


_D_
the next trip. is the minimum time needed to charge the EV to its required SOC. It is _j_


not difficult to note that the laxity value concisely describes the charging flexibility of a


EV. The larger the value of its laxity value, the less urgent the EV needs to get charged.


For EVs, whose laxities are no greater than 1, they should get charged instantly during


the current operation time interval; otherwise, their charging demands cannot be satisfied.


The allocation algorithm is designed to first allocate power to individual EV whose


laxities are no greater than 1 in order to satisfy their minimum electricity requirement.


The remaining positive available aggregate power is allocated further to the EVs


according to their laxity rankings. Specifically, the allocation algorithm first allocates



power to increase the electricity consumption of EV _j_



∗ that has the lowest laxity value,



∗
i.e., _j_ = arg min _j_ π _j_ . The spare available electricity is further scheduled to increase the


power consumption of the EV with the next lowest laxity value and the above procedure


terminates when either the optimized aggregate charging power is entirely expended or


the power consumption of all connected EVs has reached their rated values. The majority


of the EV charging piles are operated strictly in “on” and “off” control modes. The above



_rated_

_[p]_ [ ∗] _j_ .



allocation algorithm always allocates EV _j_



∗ with its rated power


**Supplementary Note 7. Calculation of emissions for power sector.**


The average emission intensity for generating every kilowatt hour of electricity for


the JJT energy systems is calculated based on the total emissions divided by the annual


total power demand (excluding imports), indicated as:



_T_ _N_ _h_ _N_ _c_

⎛ _h_ _h_ _c_ _c_ ⎞
#### ∑∑ ⎜ e p i k, i t, + ∑ e p i k, i t, ⎟

_t_ =1 _i_ =1 _i_ =1 (1)

= ⎝ ⎠

_P_ + _P_ _ef_ + _P_ _e_ − _P_ _T_



_T_ _N_ _h_ _N_ _c_


#### ∑∑ ⎜ e p i kh, i th, + ∑ e p i kc, i tc,

_k_ _t_ =1 _i_ =1 _i_ =1

= ⎝

_w j_, _ef_ _e_ _T_



_e p_ _i k_, _i t_, + _e p_ _i k_,

_k_ _t_ =1 _i_ =1 _i_ =1

_I_ = ⎝

_w j_, _P_ + _P_ _ef_ + _P_ _e_ − _P_ _T_



,,,,


_t_ =1 _i_ =1 _i_ =1

= ⎝

, _ef_ _e_ _T_



= = =



_h_ _c_
where _e_ _i k_, and _e_ _i k_, are the emission factors for the _k_ _[th]_ type of emission from the _i_ _[th]_


thermal plant and the _i_ _[th ]_ CHP unit. The emission factors are illustrated in Supplementary


Table 10. _I_ _w jk_, is the emission intensity for _k_ _[th]_ type of emissions under scenario ( _w_, _j_ ). _w_


indicate the wind penetration level (0%-40%), and _j_ represents the scenario for vehicle


electrification. Possible values of _j_ range from 0-5, representing BAU, Pub-f, Prv-f, Prv-s,


Cmb-f, and Cmb-s respectively. _P_, _P_ _ef_, _P_ _e_ [ and ] _P_ _T_ represent the annual power


consumption (not including power consumption for electric vehicles) from JJT area,


power consumption for fast-charging electric vehicles (if applicable), power consumption


for slow-charging electric vehicles (if applicable), and annual power import to the JJT


area respectively.


The total emissions from the power sector for Beijing _Es_ _w jk_, under scenarios ( _w_ [th]


wind penetration, _j_ [th] vehicle scenario) for _k_ _[t]_ [h] type of pollutant are defined as in (2):

##### Es w jk, = ( P b + P bef + P be ) ⋅ I w jk, + ( P − P b − P T ) ⋅Δ I w jk,            (2)



where _[P]_ _b_ [ is the annual power consumption for Beijing;]




_[P]_ _bef_ and _[P]_ _b_ [ are the power ] _e_


consumption incurred by fast-charging and slow-charging electric vehicles in Beijing


respectively. The second term of (2) indicates the indirect emission contributions, which


account for the changes of emissions in the JJT regional grid resulted from the charging


of EVs in Beijing area (largely due to changes in the wind curtailment rate). Δ _I_ _w jk_,


indicates the changes in emission intensity for scenario ( _w_, _j_ ) compared to that of all


gasoline vehicle scenario at the same level of wind penetration - scenario ( _w_, 0). It is


determined jointly by the changes in emission intensity by converting a gasoline vehicle


to electric in scenario _j_ at _w_ _[th]_ wind penetration, and by the number of electric vehicles in


Beijing. As indicated, the indirect contribution only accounts for the portion of power


consumption from JJT area excluding Beijing. The impact of Beijing EVs charging


beyond the JJT power grid is negligible and ignored.


**Supplementary References**


1. Huo, H. & Wang, M. Modeling future vehicle sales and stock in China. _Energy_


_Policy_ **43,** 17-29 (2012).


2. National Development and Reform Commission. _13_ _[th]_ _Five-Year Plan for Power_


_Sector_ _(2016-2020)_ (2016,


http://www.ndrc.gov.cn/zcfb/zcfbghwb/201612/P020161222570036010274.pdf) (in


Chinese).


3. Chen, X., Lu, X., McElroy, M. B., Nielsen, C. P. & Kang, C. Synergies of wind


power and electrified space heating: case study for Beijing. _Environ. Sci. Technol_ .


**48** (3) **,** 2016-2024 (2014).


4. Geng, Z., Chen, Q., Xia, Q., Kirschen, D. S. & Kang, C. Environmental generation


scheduling considering air pollution control technologies and weather effects. _IEEE_


_Trans. Power Syst._ **32** (1) **,** 127-136 (2017).


5. Ministry of Housing and Urban-Rural Construction of China. _China Urban_


_Construction Statistical Yearbook_ (China Housing and Urban-Rural Construction


Press, Beijing, China, 2014).


6. Rienecker, M. M. _et al_ . The GEOS-5 data assimilation system-documentation of


versions 5.0.1, 5.1.0, and 5.2.0. _NASA Tech. Rep. 104606_ **27,** 1-118 (2008).


7. Zhang, N. _et al_ . Planning pumped storage capacity for wind power integration.


_IEEE Trans. Sustain. Energy_ **4** (2) **,** 393-401 (2013).


8. Subramanian, A., Garcia, M. J., Callaway, D. S., Poolla, K. & Varaiya, P. Real-time


scheduling of distributed resources. _IEEE Trans. Smart Grid_ **4** (4) **,** 2122–2130


(2013).


9. Zhang, S. _et al_ . Historic and future trends of vehicle emissions in Beijing, 1998–


2020: A policy assessment for the most stringent vehicle emission control program


in China. _Atmos. Environ_ . **89,** 216-229 (2014).


10. Huo, H., Zhang, Q., Liu, F. & He, K. Climate and environmental effects of electric


vehicles versus compressed natural gas vehicles in China: a life-cycle analysis at


provincial level. _Environ. Sci. Technol._ **47** (3) **,** 1711-1718 (2013).


11. Beijing Transportation Research Center (BTRC). _Beijing Transport Annual Report_


(2013, http://www.bjtrc.org.cn/) (in Chinese).


