# Citation Key: zhangSustainablePluginElectric2024

---

#### nature reviews electrical engineering https://doi.org/10.1038/s44287-023-00004-7

**Review article** Check for updates
# Sustainable plug-in electric vehicle integration into power systems


**Hong<PERSON><PERSON> Zhang** **[1]** **, Xiaosong Hu** **[2]** **, <PERSON><PERSON><PERSON>** **[3]** **& <PERSON>** **[4]**



**Abstract**


Integrating plug-in electric vehicles (PEVs) into the power and transport
sectors can help to reduce global CO 2 emissions. This synergy can be
achieved with advances in battery technology, charging infrastructures,
power grids and their interaction with the environment. In this Review,
we survey the latest research trends and technologies for sustainable
PEV–power system integration. We first provide the rationale behind
addressing the requirements for such integration, followed by an
overview of strategies for planning PEV charging infrastructures.
Next, we introduce smart PEV charging and discharging technologies
for cost-efficient and safe power system operations. We then discuss
how PEVs can help to promote clean energy adoption and decarbonize
the interconnected power and transport systems. Finally, we outline
remaining challenges and provide a forward-looking road map for the
sustainable integration of PEVs into power systems.


1 State Key Laboratory of Internet of Things for Smart City, University of Macau, Macao, China. 2 Department
of Mechanical and Vehicle Engineering, Chongqing University, Chongqing, China. [3] Department of Electrical
Engineering, Tsinghua University, Beijing, China. [4] Department of Civil and Environmental Engineering, University
of California at Berkeley, Berkeley, CA, USA. [e-mail: <EMAIL>](mailto:<EMAIL>)



**Sections**


Introduction


Battery considerations for PEV
integration


Infrastructures for PEV
integration


Grid considerations of PEV
integration


Environmental aspects of PEV
integration


Sustainable PEV integration


Outlook



[Nature Reviews Electrical Engineering | Volume 1 | January](http://www.nature.com/natrevelectreng) 2024 | 35–52 **35**


### **Review article**

**Key points**


- Coupling plug-in electric vehicles (PEVs) to the power and transport
sectors is key to global decarbonization.


- Effective synergy of power and transport systems can be achieved
with advances in battery technology, charging infrastructures, power
grids and their interaction with the environment.


- Planning PEV charging infrastructures should support the active
interaction of PEVs with the power grid and zero-emissions power
generation.


- Advanced optimization and control technologies are in need to
fully exploit large-scale PEV flexibility in interconnected power and

transport.


- Innovative financial incentives are required to leverage the benefits
of PEVs while coordinating the interests of different stakeholders.


**Introduction**

Plug-in electric vehicles (PEVs) can be divided into two major categories:
battery PEVs and hybrid PEVs. The former are fully powered by batteries,
whereas the latter use a combination of battery and fossil fuel power
and can switch power modes on demand. The global PEV market has
experienced an explosive growth since 2010 (ref. 1); according to the
International Energy Agency [1], 10.5 million new PEVs, 73% of which are
battery PEVs, were delivered in 2022 accounting for 13% of global light
vehicle sales, an increase of 55% compared with 2021. Promoting the
adoption of PEVs, particularly battery PEVs, is key to the decarbonization of the transport system, which contributed 22% to the global
energy-related CO 2 emissions in 2022 (ref. 2).
PEV batteries are charged by plugging into the power grid via a
charging infrastructure, thereby reducing fossil fuel consumption.
As a result, PEVs produce no (for battery PEVs) or less (for hybrid
PEVs) direct emissions through the tailpipe, including PM2.5, NO _x_,
NH 3, volatile organic compounds and CO 2, compared with conventional internal combustion vehicles [3] . These advantages make PEVs
suitable for urban areas with high pollution levels. However, battery
PEVs might still have indirect emissions if the electricity for charging
comes from fossil fuels such as thermal power plants. In regions where
the electricity is mainly generated from fossil fuel, the well-to-wheel
CO 2 emissions (all CO 2 emissions related to electricity generation,
transmission, distribution and use) of PEVs can be higher than those of
conventional vehicles [4] . For example, a comprehensive analysis shows
that PEVs might have higher CO 2 emissions than conventional vehicles
after 2021 when nuclear energy began to be replaced by natural gas
power plants in New York State, USA [5] .
The power sector, responsible for 40% of global energy-related
CO 2 emissions in 2022, is also undergoing decarbonization because
of transitioning to zero-emissions wind and solar power generation [2] .
Operating power systems requires a real-time balance between electricity supply and demand; however, wind and solar generation are intermittent and variable, which makes the balance challenging. Therefore,
future power systems will need balancing resources that can adjust
power generation or consumption following system operators’ instructions to tackle real-time supply–demand mismatches [6] . Currently, these



balancing resources are mainly provided by fossil fuel power plants,
which are, however, being phased out with the decarbonization of
power systems worldwide. According to the International Energy
Agency, if the net zero-emissions targets in the European Union and
other 83 countries worldwide are fully realized on time, coal use in
the electricity sector will be reduced from 36% in 2021 to 3% in 2050;
whereas renewables will rise from 28% in 2021 to 80% by 2050 (ref. 7).
The growing PEV adoption and deployment of charging infrastructures are intertwined with the power and transport sectors (Fig. 1a).
For example, PEV charging profiles should match with zero-emissions
generation in the power system to reduce indirect CO 2 emissions.
Otherwise, PEV charging demands have to be satisfied by other power
sources and the corresponding CO 2 emissions can increase despite high
penetration of zero-emissions generation [8] . However, PEV charging
profiles are flexible in which energy can be pushed back to the power
grid from the battery, a concept known as vehicle to grid (V2G). The
flexibility of charging and discharging power can provide substantial
balancing resources in the power system and further promote the
adoption of zero-emissions generation [9] (Fig. 1b).
This Review highlights recent technological advances towards sustainable PEV integration from the perspectives of batteries, charging
infrastructure, power grids and their interaction with the environment.
It also envisions the pathway, remaining challenges and future research
directions from the perspectives of infrastructure planning, technologies for modelling, operation and control, and incentive mechanism
design to realize sustainable PEV integration into power systems.


**Battery considerations for PEV integration**
Batteries have a remarkable impact on PEV key performances such
as safety, durability, charging speed, driving mileage and cost. These
performances determine the capability and willingness of PEVs to interact with the power grid, and PEV driving experiences. Unfortunately,
battery abuses such as collision, overcharge, over-discharge and local
overheat of the battery can negatively affect these performances and
cause safety hazards [10] . Therefore, battery management technologies
are indispensable.


**Battery types and structures**
Lithium-ion (Li-ion) batteries have been widely used in PEVs owing to
their higher power and energy densities, longer cycle life, higher efficiency and lower environmental impact compared with other types of
rechargeable battery technologies. Commercial Li-ion battery types
for automotive applications vary in terms of their chemistry, design
(cell shape) and specifications (Table 1). Although the anodes of existing Li-ion batteries are identical, mainly from graphite, their cathode
materials are different, such as lithium iron phosphate (LFP), lithium
nickel–manganese–cobalt oxide (NMC) and lithium nickel–cobalt–
aluminium oxide (NCA) [11][,][12] . Compared with LFP batteries, NMC and
NCA batteries have a higher energy density and are more prevalent in
PEVs, but they are more expensive and have a shorter cycle life, poorer
safety and lower thermal stability [12] (Table 1). Battery shapes, including
cylindrical, prismatic and pouch cells, can also influence the energy
and power densities of the PEV battery pack once the individual cells
are scaled up [12][,][13] (Fig. 2a).
The cell design is fundamentally important for battery performance, including safety, battery degradation, charging speed and driving mileage [13][,][14] . For example, the all-climate battery structure — where
nickel foils serve as the heating components of the battery cells — allows
the battery to heat itself up at temperatures as low as −30 °C (ref. 14)



[Nature Reviews Electrical Engineering | Volume 1 | January](http://www.nature.com/natrevelectreng) 2024 | 35–52 **36**


### **Review article**

**a**



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhangSustainablePluginElectric2024/zhangSustainablePluginElectric2024.pdf-2-0.png)













|Col1|Col2|Col3|Col4|
|---|---|---|---|
|**Transport system**<br>|**Transport system**<br>|**Transport system**<br>|**Transport system**<br>|
|Electric car<br><br>|Electric car<br><br>|Electric car<br><br>|Electric car<br><br>|
|~~Electric bus~~<br>~~Electric truck~~|~~Electric bus~~<br>~~Electric truck~~|~~Electric bus~~<br>~~Electric truck~~|~~Electric bus~~<br>~~Electric truck~~|
|||||


**b**







**Scenario 1: uncoordinated charging** **Scenario 2: smart charging** **Scenario 3: smart discharging**





PEV charging is generation to satisfy mobility
fully satisfied by demands and extra electricity
solar generation Peak discharged to the grid







|smart discharging|Col2|Col3|Col4|
|---|---|---|---|
|Peak<br>PEV charging requires extra solar<br>generation to satisfy mobility<br>demands and extra electricity<br>discharged to the grid|Peak<br>PEV charging requires extra solar<br>generation to satisfy mobility<br>demands and extra electricity<br>discharged to the grid|Peak<br>PEV charging requires extra solar<br>generation to satisfy mobility<br>demands and extra electricity<br>discharged to the grid|Peak<br>PEV charging requires extra solar<br>generation to satisfy mobility<br>demands and extra electricity<br>discharged to the grid|
|Peak<br>PEV charging requires extra solar<br>generation to satisfy mobility<br>demands and extra electricity<br>discharged to the grid|Peak<br>PEV charging requires extra solar<br>generation to satisfy mobility<br>demands and extra electricity<br>discharged to the grid|Peak<br>PEV charging requires extra solar<br>generation to satisfy mobility<br>demands and extra electricity<br>discharged to the grid||
|~~Valle~~<br><br><br><br>|~~Valle~~<br><br><br><br>|~~Valle~~<br><br><br><br>|~~Valle~~<br><br><br><br>|
|||||


00:00 06:00 12:00 18:00 24:00



|PEV charging is<br>mostly not satisfied<br>Peak<br>by solar generation<br>Valle|Col2|Col3|
|---|---|---|
||||


00:00 06:00 12:00 18:00 24:00

Hours



|PEV charging is<br>fully satisfied by<br>solar generation Peak<br>Valle|Col2|Col3|Col4|
|---|---|---|---|
|||||


00:00 06:00 12:00 18:00 24:00



Hours Hours



Base load curve Total load curve Net load curve PEV charging Solar generation PEV charging + solar generation PEV discharging



**Fig. 1 | A zero-emissions interconnected power and transport system.** **a**, The
concept of an interconnected power and transport system. In the power system,
most of the energy is generated from zero-emissions renewable sources, mainly
wind and solar. This renewable generation is intermittent, variable and might
not match demands. Therefore, the power system will need more balancing
resources, such as energy storage, to mitigate the mismatch between electricity
demands and supplies. In the transport system, plug-in electric vehicles (PEVs)
consume electricity instead of fossil fuel. Their flexible charging or discharging


(Fig. 2b). The self-heating process can improve the energy and power
capabilities of Li-ion batteries in cold weather. The blade battery technology, proposed by the Chinese manufacturer BYD, makes the LFP
cells long and thin, thereby improving the pack energy density and
thermal safety [13] .



power can act as a balancing resource to help to smooth fluctuations of
renewable generation and promote decarbonization of the interconnected
system. **b**, Example of coordination of PEV charging and discharging with
renewable generation (solar as an example). Smart charging allows PEVs to
consume more electricity when renewable generation is high. Smart discharging
can transfer electricity from time periods with high renewable generation to time
periods with low renewable generation, which further flattens the net load profile
and promotes renewable generation consumption.


**Battery health and safety management**
Battery health and safety play vital roles in the charging–discharging
management of PEVs. Improper or frequent charging and discharging
cycles can accelerate battery degradation, resulting in reduced capacity
and heightened resistance. Such degradation can also induce safety



[Nature Reviews Electrical Engineering | Volume 1 | January](http://www.nature.com/natrevelectreng) 2024 | 35–52 **37**


### **Review article**

**Table 1 | PEV battery technologies**



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhangSustainablePluginElectric2024/zhangSustainablePluginElectric2024.pdf-3-0.png)





Quantitative values are from cited sources and qualitative evaluations such as safety, cost, low-temperature performance and charging time have been performed according to ref. 157.
C, graphite or carbon; LFP, lithium iron phosphate (LiFePO 4 ); LTO, lithium titanate oxide (Li 4 Ti 5 O 12 ); NCA, lithium nickel–cobalt–aluminium oxide (LiNi 0.8 Co 0.15 Al 0.05 O 2 ); NMC, lithium nickel–
manganese–cobalt oxide (LiNi 1– _x_ – _y_ Co _x_ Mn _y_ O 2 ); PEV, plug-in electric vehicle. [a] Year in parentheses indicates when the PEV model was first put on the market. [b] The specific power values that could
not be retrieved from the corresponding reference were estimated using typical data such as nominal voltage and maximum continuous discharging current from the battery manual.



issues, for example, thermal runaway — a scenario where exothermic
reactions within the battery go out of control.


**Ageing mechanism and prognostics.** Li-ion batteries inevitably age
during operation. Therefore, it is important to understand the ageing
mechanisms and influencing factors to inform the use of prognostic
methods [15] and develop suitable and accurate models for battery health
prognostics [16] . Degradation modes related to the capacity and power
decay of Li-ion batteries mainly involve the loss of active materials
and the loss of lithium inventory (the quantity of cyclable lithium) [17] .
Degradation mechanisms often arise from the side reactions on the
anode, cathode and electrolyte. For instance, growth of a passive film,
deposition of metal lithium and attenuation of active materials are
generally ageing reactions at the anode electrode, which dominate the
whole battery ageing process. For the metal oxide cathode, electrode
material dissolution, electrode structure destruction and phase transition commonly occur [18] . External usage conditions such as temperature,
loading current mode and rate, and depth of discharging strongly
influence battery ageing [19] . Ageing mechanism detection can be directly
measured based on an electron microscope test and an infrared test, or
through modelling methods such as pseudo-2D and molecular dynamics simulation [15] . In practical battery usage, the attenuation of energy
and power capacities is of primary concern, which requires accurate
battery health prognostics including state of health (SOH) estimation
and lifetime prognostics.
The methods for battery health prognostics can be broadly
divided into model-based, data-driven and hybrid methods [20] .
Model-based methods aim to build an empirical, equivalent circuit or
electrochemical model to simulate battery properties and then adopt
parameter optimization methods for ageing status prognostics [21] .
Data-driven methods are mainly used to map the relationship between
the observable features and battery SOH [22] . However, these models
have their own limitations. For example, the high computational
burden of electrochemical models and the poor generalizability of
data-driven methods can hinder their practical applications. One



option is to use hybrid methods that combine the strengths of
model-based and data-driven methods to provide more accurate
and reliable prognostics [15] . For example, a computationally efficient
physics-informed multi-output Gaussian process regression method
was proposed to simultaneously estimate battery capacity and diagnose its degradation modes using solely limited early-life battery
degradation data. This approach improved simulation fidelity by
more than 50% compared with the purely data-driven approach [23] .
Alternatively, a generative machine learning method that considers
physics-based constraints was used to improve purely data-driven
SOH diagnostics [24] . In this case, the physical laws and boundary conditions involved in the electrochemical reactions inside the battery
were introduced to limit the time-varying model weights or coefficients. Additionally, a battery electrochemical model was developed
to generate a set of physics features reflecting battery degradation
and these features serve as the inputs of a neural network model
for improving the accuracy and generalizability of battery health
prognostics [25] (Fig. 2c).


**Safety and fault diagnosis.** Safety concerns are critical in the
large-scale grid integration of PEV batteries. Li-ion batteries are
susceptible to various abuse conditions (such as mechanical penetration, external short circuiting and an overheating battery), which
increase the risks of safety issues such as thermal runaways [25] . To guarantee the safe operation of batteries, efficient thermal management
and online fault diagnosis are indispensable (Fig. 2d). Battery thermal
management systems serve to regulate the battery temperature within
a range that is favourable to battery operations and safety [26][,][27] . Their
development involves the characterization of heat generation and
transfer of batteries, pack-structure design and optimization, and thermal control strategies [26][,][27] . For fault diagnosis, effective numerical modelling and online fault diagnosis technologies guide the safer operation
of batteries [28] . Numerical model-based fault diagnosis can detect the
difference between the measured signal and the model-estimated
information, but it is difficult to implement large battery packs owing



[Nature Reviews Electrical Engineering | Volume 1 | January](http://www.nature.com/natrevelectreng) 2024 | 35–52 **38**


### **Review article**

to model uncertainty and inconsistency among cell parameters, such
as temperature, state of charge, internal resistance and capacity [29][,][30] .
By contrast, data-driven battery fault diagnosis uses measured data
to analyse the anomalies among the battery cells in the pack and flag
outliers instead of relying on a predefined model [30] . However, labelled
fault categories are required for model training, which are difficult to
obtain. Combining model-based and data-driven methods can improve
the efficiency of fault diagnosis. For example, fault-related battery
parameters and states can be estimated using physical battery models, which can then serve as the inputs of data-driven methods for
augmenting interpretability and accuracy.


**Battery charging management**
Charging speed is one of the key metrics of PEVs. Reducing the charging duration brings PEVs closer to internal combustion engine vehicles
in terms of refuelling convenience. However, increasing the charging speed can accelerate battery ageing and reduce PEV economic
viability [31] . Therefore, it is crucial to develop health-aware fast charging protocols to boost the charging speed without sacrificing battery
health. Given that PEVs are subject to different operating conditions
that can affect batteries’ charging performance, for instance, environmental temperature, adaptive charging management is also required
to guarantee optimal PEV charging under these variable conditions.
The trade-offs between charging speed, battery health and temperature
adaptability should be carefully considered in a battery management

system.


**Health-aware fast charging.** Fast charging that charges batteries with
a high power rate, for instance, charging an empty battery to its 80%
capacity in 15 min, can trigger negative side reactions inside the cell.
For example, lithium plating on graphite, which is considered the main
reason for capacity degradation and safety issues in Li‐ion batteries,
occurs when the charging rate exceeds the intercalation rate into the
graphite crystal structure [32] . Therefore, the trade-off between charging
speed and battery degradation should be addressed by optimizing
charging current profiles and developing health-aware charging
protocols [33][,][34] . For this purpose, electrochemical–ageing coupled battery models that accurately predict the degradation mechanism during
charging should be developed. Moreover, internal electrochemical
states can inform electrochemical–ageing coupled models to minimize
the side reactions responsible for degradation [31] . An accurate battery
lifetime model should be developed based on an historical experimental data set covering various operation conditions including environment temperature, depth of discharge and SOH. PEV fast charging
current profiles can be optimized to maximize the battery lifetime
according to the battery lifetime model, given the specific battery’s
degradation patterns and current SOH [32] .


**Adaptive charging.** Owing to the varying operational conditions of
PEV batteries, charging protocols might perform differently in terms
of charging efficiency, charging speed, temperature stability and
degradation across conditions. Therefore, adaptive charging management has an essential role in dealing with operation uncertainties and
guaranteeing PEV optimal charging performance adapted to a specific
set of conditions. For example, adaptive charging management at
extremely low temperatures (for example, below –30 °C) is one of the
most critical considerations [35] . PEV charging management in cold places
such as Nordic regions is challenging because of the increased battery
resistance and accelerated capacity degradation that greatly reduce



rechargeability [36] . Adaptive charging management warms up the battery to a favourable temperature range (15–35 °C) through controllable
internal preheating or hybrid warm-up technology combining internal
and external heating, which boosts the charging speed while reducing
battery degradation [36][,][37] .


**Infrastructures for PEV integration**
The manner in which drivers operate their PEVs, including where and
how long they charge, shapes their charging needs and preferences.
This, in turn, affects PEV integration into the interconnected power
and transport system. In this section, the most important aspects for
planning PEV charging infrastructures are discussed.


**Charging modes and options**
PEVs can be refuelled using different battery charging modes, such
as conductive charging, battery swapping, and wireless and mobile
charging (Table 2). The selection of each charging technique depends
on the needs of PEV users.


**Conductive charging.** Conductive charging is the most common wired
charging technology. Depending on the charging urgency, conductive charging can be further categorized into destination charging and
fast charging [38] . Destination charging is used when PEVs are charged
at parking lots of trip destinations [39], such as residential and commercial areas. Residential areas usually offer slow charging (a couple of
kilowatts), taking up to 10 h for a full charge, whereas commercial
areas can charge faster (from a couple of kilowatts to tens of kilowatts),
completing from 30 min to a few hours [40] . Destination charging is the
most popular option for PEV users because it does not require additional stops [41] . Particularly, residential charging accounts for about
50–80% of all charging events [42] . Furthermore, when a user parks at a
destination for a period longer than the time required for the vehicle
to completely charge, the vehicle’s charging power profile becomes
flexible and can be strategically optimized or even discharge electricity
to the power grid.
By contrast, fast charging and ultra-fast charging are used during
a trip to quickly boost a PEV’s range. Whereas common fast charging
typically uses power levels around tens of kilowatts [43], ultra-fast charging can go up to 200–350 kW (ref. 44). Fast charging complements
destination charging; whereby private PEV users driving for trips that
are longer than PEV ranges or commercial PEV users (such as taxis
drivers) often opt for fast charging to save time. Given that the primary
objective of fast charging is to minimize the charging duration, factors
such as deviation time from the original route, potential queuing duration and the inherent charging time predominantly influence the user’s
determination for modes of charging.
According to a survey by the International Energy Agency [1], the
number of public charging stations for PEVs worldwide reached
2.7 million at the end of 2022, more than 0.9 million of which were
installed in 2022. The distribution of conductive charging infrastructure varies considerably with countries. As the largest PEV market,
China owns more than 1 million slow destination chargers (over half of
the global stock) and 0.76 million fast chargers [1] . Because of the dense
population in China’s urban areas, access to home chargers is limited.
Consequently, the share of fast chargers (43%) is substantially higher
than in Europe (13%) or the USA (22%) [1] .
Nonetheless, promoting the development of charging infrastructure across the world remains challenging. For example, many people in
China live in multi-storey apartments, in which private parking spaces



[Nature Reviews Electrical Engineering | Volume 1 | January](http://www.nature.com/natrevelectreng) 2024 | 35–52 **39**


### **Review article**

**a** **b**


**Blade cell**



**All-climate battery**



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhangSustainablePluginElectric2024/zhangSustainablePluginElectric2024.pdf-5-1.png)

Positive and
negative
terminals


Hard

case



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhangSustainablePluginElectric2024/zhangSustainablePluginElectric2024.pdf-5-0.png)

**Cylindrical**


**Pouch**

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhangSustainablePluginElectric2024/zhangSustainablePluginElectric2024.pdf-5-6.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhangSustainablePluginElectric2024/zhangSustainablePluginElectric2024.pdf-5-7.png)


**c**



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhangSustainablePluginElectric2024/zhangSustainablePluginElectric2024.pdf-5-3.png)

**Hybrid method**



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhangSustainablePluginElectric2024/zhangSustainablePluginElectric2024.pdf-5-2.png)

Positive and negative
terminals


Al foil
soft pouch





![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhangSustainablePluginElectric2024/zhangSustainablePluginElectric2024.pdf-5-5.png)



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhangSustainablePluginElectric2024/zhangSustainablePluginElectric2024.pdf-5-4.png)

Metal foil Anode



**Physical model-based feature extraction**



Direct measurements Physical model Physics features **Neural network model** **Battery**



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhangSustainablePluginElectric2024/zhangSustainablePluginElectric2024.pdf-5-8.png)














|Col1|health prognostics|
|---|---|
|||
|**ral network model**<br>**Battery**<br>**health state**<br>Battery<br>capacity|**ral network model**<br>**Battery**<br>**health state**<br>Battery<br>capacity|
|**ral network model**<br>Battery<br>capacity|**ral network model**<br>Battery<br>capacity|





![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhangSustainablePluginElectric2024/zhangSustainablePluginElectric2024.pdf-5-10.png)







![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhangSustainablePluginElectric2024/zhangSustainablePluginElectric2024.pdf-5-9.png)

























![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhangSustainablePluginElectric2024/zhangSustainablePluginElectric2024.pdf-5-11.png)











[Nature Reviews Electrical Engineering | Volume 1 | January](http://www.nature.com/natrevelectreng) 2024 | 35–52 **40**


### **Review article**

**Fig. 2 | Battery technologies for PEV integration.** **a**, Different types of battery
format. Three types of battery format are commonly used in current plug-in
electric vehicle (PEV) applications: cylindrical cells, prismatic cells and pouch
cells. The battery format is related to the battery pack assembly, which affects
the energy density of the battery pack and, thus, the vehicle driving range.
**b**, Novel battery structure designs. The all-climate battery enables the battery
to heat itself at low temperatures (as low as –20 °C) by adding nickel foil to
the battery cell. The self-warming process improves the ability of lithium-ion
(Li-ion) batteries to charge in cold weather while alleviating battery degradation
and safety concerns [14] . The blade battery technology makes the lithium iron
phosphate (LFP) cell long and thin [13], which can increase the energy density of
the battery pack and improve the thermal safety of the battery cell. **c**, Example
of battery health prognostics based on the hybrid method. The battery direct
measurements are utilized to build an electrochemical model of the battery
and simulate the battery’s ageing process. Based on the simulation, a set of
physics features reflecting battery degradation are extracted as the inputs of a
neural network model for battery health prognostics [158] . This neural network
learns the nonlinear relationship between battery capacity (output, an explicit


and power supplies are too limited to install home chargers. In Europe,
some insurance companies are reluctant to provide insurance for
buildings with PEV charging stations because of safety concerns. Nevertheless, the strong government support and the rapidly growing PEV
numbers continue to generate investment into both destination and
fast chargers worldwide. In 2022, some countries such as China, the UK,
Germany and Switzerland began to shift their previous subsidies on
vehicles to charging infrastructures. For instance, the UK plans to install
300,000 public chargers by 2030 and committed about £1.6 billion in
government funding in 2022 (ref. 1).


**Battery swapping.** Swapping batteries instead of charging them is
a more convenient option for PEV users [45] . The entire battery swapping process typically takes less than 5 min, which is similar to how
long it takes to refuel a gasoline vehicle [46] . The removed batteries can
be charged during the valley-load period (the hours with low base
power demands and cheap electricity), which can save PEV electricity
consumption costs.
Despite its advantages compared with conductive charging,
battery swapping is less common. Battery swapping was briefly commercialized by Better Place in Israel and Denmark, Tesla in the USA
and State Grid Cooperation in China, but they all failed [47] . On the one
hand, battery swapping stations usually need to maintain sufficient
charged batteries to avoid queuing for customers, which increases
investment and maintenance costs [48] . On the other hand, the lack of
standardization among different battery manufacturers also limits the
adoption of battery swapping and causes concerns about the quality
of the replaced batteries [49] . Batteries of different PEV models — even
from the same manufacturer — often have different performance and
characteristics, which limits their exchangeability.
Currently, battery swapping companies either provide services
to PEVs that they produce (for example, NIO) or work with manufacturers to design PEV models compatible with their services (Aulton) [1] .
NIO now operates the world’s largest battery swapping system with
more than 1,300 stations providing services for private PEVs around
the world [1] . Aulton runs more than 800 battery swapping stations
for commercial PEVs (such as electric taxis and buses) in both China
and Europe [50] . Presently, the global share of battery swapping PEVs
remains low. For example, it accounts for less than 5% of all PEVs in



indicator reflecting battery degradation process) and these physics features
(input). Because these physics features have a closer relationship with battery
degradation and stronger physical interpretability compared with the direct
measurements, this neural network trained by the hybrid method can have
higher estimation accuracy and generalizability than a network trained
simply based on the direct measurements. SEI, solid electrolyte interphase.
**d**, Illustration of battery thermal management and fault diagnosis. Battery
thermal management uses coolant to keep the battery within a safe temperature
range. When an internal short circuit fault occurs in one cell, this will cause
abnormal heat generation and temperature rise. If no intervention is taken,
continuously rising temperatures can lead to battery thermal abuse, triggering
thermal runaway when temperatures reach a critical point, which in turn
destroys the battery. However, proper online fault diagnosis technology can
help to detect the internal short circuit fault in advance and pull the alarm. The
thermal management system will then try to further reduce battery temperature
by accelerating cooling. This fault will also be continuously monitored, and
battery engineers may also manually maintain the battery, that is, replace the cell,
when the fault is significant.


China [47] . The popularization of conductive charging infrastructures
and the improvement of fast charging technologies might make battery swapping less attractive in the future. Nevertheless, it could still
be appealing for commercial PEVs, such as taxis, which usually prefer
a short charging time to enhance vehicle utilization.


**Wireless charging.** Wireless charging refers to charging a PEV en
route through pads buried beneath the road surface [51] . This mode is the
most desirable option for PEV users as it almost entirely eliminates
the inconvenience of PEV charging. Wireless charging comes in two
main forms: inductive and capacitive. The former works through magnetic resonance coupling between transmitting and receiving coils,
whereas the latter functions by electric field interaction between coupled capacitors [52] . The currently available wireless charging technology
mainly belongs to the inductive type, as it allows power transmission
above 10 kW over air gaps reaching several metres. By contrast, capacitive charging is suitable for only a few millimetres in air gap [53] . The main
limitation of wireless charging is the investment cost; for example, in
the FABRIC EU project, the cost of building wireless charging infrastructures was evaluated at 3 million € per kilometre [54] . In another study
conducted in California, the cost was estimated at around US$1 million
per kilometre [55] .


**Mobile charging.** Mobile charging refers to a mobile charging station
(typically a truck with batteries) to charge PEVs [56] . Compared with other
charging options, mobile charging does not need to service PEVs in a
fixed spot, enabling it to charge PEVs in arbitrary locations. Mobile
charging can serve as a complementary solution before the large-scale
deployment of fixed charging infrastructures. Alternatively, it could
also address the charging demand in areas with limited power grid
capacity [57] . However, mobile charging requires a full-time driver, wastes
substantial electricity when moving the batteries and usually has a low
utilization level because their customers are often sparsely distributed
on the transport network. Hence, it is much more expensive than the
other charging options. As a result, it is only used for PEV charging
rescue services nowadays. In 2010, Nation-E launched the world’s first
mobile charging station for providing PEV charging rescue services [58] .
In 2021, China’s PEV manufacturer NIO announced the deployment of
120 mobile charging stations by 2024 (ref. 59).



[Nature Reviews Electrical Engineering | Volume 1 | January](http://www.nature.com/natrevelectreng) 2024 | 35–52 **41**


### **Review article**

**Table 2 | PEV charging infrastructures**



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhangSustainablePluginElectric2024/zhangSustainablePluginElectric2024.pdf-7-0.png)









PEV, plug-in electric vehicle. [a] Charging duration is estimated based on fully charging a 60-kWh battery. [b] The battery capacity of PEVs in specialized fast-charging stations is often large. For a
typical capacity of 200 kWh for electric buses, the charging duration is 0.5–2 h. [c] The charging power range in battery swapping stations depends on the type of installed chargers. [d] The extra
energy consumption for moving the mobile charger to users has not been counted in calculating the charging efficiency.



**Charging infrastructure planning**
Building convenient and economical charging facilities is key to
improving user satisfaction, reducing charging costs and promoting
PEV adoption. The aim of planning charging infrastructures includes
maximizing the charging demand coverage, improving service quality
and maximizing social welfare [60] . The key constraints considered in the
charging infrastructure plan fall into two categories: economy and
convenience. The former includes resource limitation such as invest
ment capital, land, optimal charging scheduling and user preference,
whereas the latter covers charging demand satisfaction and service
quality, for example, queuing duration [61] .
For the planning of charging infrastructure, it is imperative to
first delineate the distribution of charging demand. For destination
charging, the charging demand can be estimated based on statistics of
drivers’ mobility behaviours (particularly driving destinations). For fast
charging or battery swapping stations [62], the charging often happens en
route instead of at destinations. As a result, it is difficult to estimate the
locations where fast charging demands appear, whereas locations of
charging infrastructure can also affect PEV driving behaviours. Hence,
modelling the fast charging demand and battery swapping should
not only consider PEV mobility demands but also consider charging
infrastructure planning’s impacts on PEV behaviours [63] .
Early research in charging infrastructure planning was largely
inspired by classic work on conventional vehicle refuelling facility planning and conducted from the perspective of transport engineering [64] .
This approach considers mileage limitation of PEVs [65] and maximizes
traffic flow through charging stations, thereby maximizing the demand
coverage or minimizing the total cost [66] . Because PEVs have shorter
driving range and longer refuelling time than conventional vehicles,
modelling PEV charging demands faces new challenges. Traffic
simulation [67] and data-driven methods [68] have also been applied to
charging infrastructure planning. The former builds agent-based simulation tools to simulate PEV driving and charging behaviours, in which
it can validate charging system plans’ performance [67] . The latter utilizes
historical real-world PEV charging data to estimate future charging
demands to guide infrastructure planning [68] . Besides the transport
perspective, the siting and sizing of charging facilities in the power
distribution network have also been investigated to help to alleviate
the adverse effects of PEV charging (for example, overloading) on the
power grid or promote renewable generation adoption [69] .
In this context, combining transport and power networks in the
planning of PEV charging infrastructure can help to account for both



the users’ mobility demands and power grid constraints [70][,][71] . For example,
a user equilibrium-based framework to deploy a charging infrastructure has been proposed, which considers PEV rational routing and
charging decisions to minimize both electricity costs in the power
system and driving time costs in the transportation system [72] . When
optimal deployment of the charging infrastructure is achieved,
the PEVs also reach an equilibrium state, in which no vehicle tends
to change its routing and charging decision, and social costs can
be minimized. In addition to charging facilities, coordination with
renewable energy and energy storage systems can also be integrated
into the optimization model to improve the overall decarbonization
of the system [73] .


**Grid considerations of PEV integration**
The global rise in PEV adoption has led to increasing charging loads
which constitute a substantial fraction of peak power consumption in
many regions. For example, PEV charging will account for 10% of the
peak load in California by 2030 and 11% of the peak load in Shenzhen,
China by 2025 (ref. 74). In this section, we first analyse the impact of PEV
charging on the power grid and then introduce charging and discharging technologies that promote seamless and beneficial PEV integration
into the power grid (Fig. 3).


**Uncoordinated PEV charging**
The uncoordinated spatiotemporal charging of PEVs can perturb the

                                                  normal load profiles of power grids and exert pressure on the opera
tion of power systems. Therefore, smart PEV charging and discharging
technologies need to be developed.


**Impact on the distribution power grid.** PEV charging demands often
overlap with other power loads resulting in short-term overloading and
exacerbation of peak–valley differences [75] . For example, in some regions
of the Pacific Gas & Electric utility network of California, large-scale PEV
integration might exceed the feeder current threshold by more than
300% and last for up to 22 h (ref. 76). Such conditions will likely accelerate transformer ageing, cause power outages and risk fire hazards [77] .
Thus, utility companies must upgrade distribution grids based on
projections of PEV charging load profiles. However, the peak load of
uncoordinated PEV charging may only last for a brief period throughout the day, resulting in a low usage rate of the upgraded distribution
grid. Therefore, costly upgrades to accommodate uncoordinated PEV
charging might be uneconomical [78] .



[Nature Reviews Electrical Engineering | Volume 1 | January](http://www.nature.com/natrevelectreng) 2024 | 35–52 **42**


### **Review article**

Uncoordinated PEV charging can deteriorate the power quality
in distribution grids. For example, the number and charging modes
of PEVs being charged in a distribution power grid may change frequently, which, depending on the charger type, may produce different amounts of harmonic content, that is, currents and voltages
with frequencies that are integer multiples of the fundamental frequency of the alternating current (AC) power system, causing harmonic pollution and decreased usage efficiency [79] . Moreover, feeder
overloading can increase line losses, which might lower electrical bus
voltages for end users [80] . Finally, the single-phase slow charging mode
allows the charging load to be randomly connected to any phase of
the three-phase AC power. This situation might cause an imbalance
in the three-phase voltage and current of the distribution grid, leading
to temperature rises, drops in efficiency and abnormal operation for
equipment requiring three-phase power [81] .


**Impact on the transmission power grid.** The uncoordinated charging of PEVs has a similar impact on the transmission grids by causing
congestion in transmission lines, deteriorating power quality and
reducing system efficiency [82] . In particular, uncoordinated PEV charging
may increase peak–valley differences in load curves of the bulk power
system. Therefore, tackling such mismatches between electricity
supply and demand requires balancing sources, such as controllable



power plants or energy storage systems. For example, extra renewable
generation has to be curtailed when the load is low [8] .


**Smart charging for distribution grids**
The average daily usage of PEVs accounts for a small portion (about 4%)
of the time in a day [83] . Therefore, PEVs often have sufficient time to
connect to the power grid and their charging and discharging profiles
are flexible [84] . Smart charging is usually the strategy to manipulate PEV
charging and discharging power profiles to match the electricity supply and demand, which could help to mitigate the negative effects on
the power grid caused by uncoordinated PEV charging, save charging
costs and bring balancing advantages to the power grid.


**Smart destination charging.** The flexibility of destination charging allows grid operators and users to optimize their charging
behaviours [85] . First, based on the time-of-use electricity prices set by
utility companies, PEV users can charge more at the ‘valley’ price and
less at the ‘peak’ price [86], thereby reducing costs [87] . Second, through
coordination with other baseloads, grid operators can let PEVs charge
when the other baseloads are low (valley-load filling) and stop charging or even discharging when the other baseloads are high (peak-load
shaving). As a result, PEVs can help to flatten power loads in distribution
grids, reduce overload risks and defer the grid upgrade investment [88] .


























|Transmission system<br>Step-up<br>Power plant transformer<br>Transmission system operator<br>Step-up<br>transformer<br>Transmission<br>Centralized renewable network<br>generation • Alleviate line<br>Promote centralized congestion<br>renewable generation • Arbitrage in<br>consumption power market<br>• Provide<br>ancillary<br>services|Col2|Distribution system<br>Distributed renewable generation Other loads<br>Promote distributed renewable Peak-load shaving<br>generation consumption and valley-load filling<br>Distribution system operator<br>Step-down Energy<br>transformer storage<br>Distribution network<br>• Avoid overloading<br>• Reduce power loss<br>• Ensure three-phase balance<br>• Enhance power quality|Col4|Charging station<br>Renewable-powered charger<br>Promote on-site<br>renewable generation<br>consumption<br>Charging system operator<br>Fast charger Destination charger<br>Large power Reduce electricity<br>levelling costs|Col6|
|---|---|---|---|---|---|
|**Transmission system**<br>**Step-up**<br>**transformer**<br>**Step-up**<br>**transformer**<br>**Power plant**<br>**Transmission**<br>**network**<br>• Alleviate line<br> congestion<br>• Arbitrage in<br> power market<br>• Provide<br> ancillary<br> services<br>**Transmission system operator**<br>**Centralized renewable**<br>**generation**<br>Promote centralized<br>renewable generation<br>consumption||**Step-down**<br>**transformer**<br>**Energy**<br>**storage**<br>**Other loads**<br>Peak-load shaving<br>and valley-load filling<br>**Distributed renewable generation**<br>Promote distributed renewable<br>generation consumption<br>**Distribution system operator**<br>**Distribution system**<br>**Distribution network**<br>• Avoid overloading<br>• Reduce power loss<br>• Ensure three-phase balance<br>• Enhance power quality||||
|**Transmission system**<br>**Step-up**<br>**transformer**<br>**Step-up**<br>**transformer**<br>**Power plant**<br>**Transmission**<br>**network**<br>• Alleviate line<br> congestion<br>• Arbitrage in<br> power market<br>• Provide<br> ancillary<br> services<br>**Transmission system operator**<br>**Centralized renewable**<br>**generation**<br>Promote centralized<br>renewable generation<br>consumption||||||





**Fig. 3 | The role of smart PEV charging and discharging in the power system.**
By controlling their charging, discharging and reactive power, plug-in electric
vehicles (PEVs) can provide various services to charging stations, distribution
systems and transmission systems such as shaving peak load, enhancing power
quality, alleviating line congestion and providing ancillary services (for example,



frequency regulation). They can also help to promote the consumption of
renewables in the power system. Solid lines represent the power network and
dashed lines represent the communication network through which the system
operators, PEV charging stations and other resources share market prices and
control signals and measurement information.



[Nature Reviews Electrical Engineering | Volume 1 | January](http://www.nature.com/natrevelectreng) 2024 | 35–52 **43**


### **Review article**

Third, grid operators can also coordinate the charging demand of different voltage phases to avoid the three-phase imbalance problem,
which helps to reduce line loss costs and improve power quality [89] .
Moreover, PEVs can also work as backup energy storage systems that
can help to enhance the power supply reliability in distribution grids [90] .
For example, idle PEVs can be assigned to power grids at locations
at risk to prepare for outage events facing natural disasters such as
hurricanes [91][,][92] . Many PEV models on the market, including Nissan Leaf
and BYD Atto 3, already support discharging to power household appliances. In 2023, General Motors announced that they have planned to
equip all its electric cars and trucks with V2G ability to act as backup
power supplies during blackouts by 2026 (ref. 93).


**Coordinated fast charging.** Fast charging power is often high and
intermittent, and only lasts for a short period (from a few minutes
to 1 h) each time, which strongly impacts the normal operation of the
power grid [94] . Because fast-charging stations are mainly used in urgent
situations, they are regarded as uncontrollable [95] . However, charging
station operators may be able to reduce some PEV charging power to
avoid overloading at peak hours [96] . For example, fast-charging stations
can install energy storage systems to smooth the fluctuating charging
load curves by scheduling their charging and discharging [73] . Moreover,
advanced algorithms can be used to coordinate the scheduling of
multiple fast-charging stations to adjust the peak charging load [97] .


**Coordinated reactive power control.** In power systems, many power
loads are capacitive or inductive and generate or consume reactive
power. Although reactive power does not really dissipate energy, the
voltage stability will deteriorate if it is imbalanced. Reactive power
compensation helps to balance reactive power through the energy
exchange between capacitive and inductive power loads in the same
circuit to improve power quality [98] . Advanced power electronic devices
in PEV chargers, such as voltage source converters, can be used for reactive power compensation [99] . During the charging process, PEVs can participate in reactive power compensation through off-board chargers [100] .
This process not only satisfies the charging demand but also adjusts the
reactive power flow of the grid. Moreover, this compensation strategy
reduces the investment cost of traditional reactive power compensation equipment (such as capacitor banks and synchronous condensers)
and does not adversely impact the battery life of PEVs [101] .


**Smart charging for transmission grids**
Aggregating tens to thousands of PEVs can increase the power and
energy capacities to reach grid-scale energy storage levels [102] . As a
result, PEVs can arbitrage energy and provide ancillary services (such
as frequency regulation and operating reserve) in power markets.


**Price arbitrage.** PEVs can participate in power markets and use their
charging and discharging flexibility to arbitrage price differences,
that is, buy more electricity when the price is low and buy less or even
sell electricity when the price is high [103] . Power markets often have a
minimum bidding capacity threshold; for example, for PJM, the largest wholesale electricity market in the USA, the threshold is 1,000 kW,
which one PEV whose charging power is commonly only several kilowatts to tens of kilowatts can hardly meet [104] . Therefore, tens or more
PEVs need to group together to participate in power markets under
the coordination of an aggregator [105] . The aggregator represents these
PEVs to bid in power markets and conducts energy arbitrage transactions through day-ahead or intra-day markets to maximize revenue.



The aggregator is also responsible for the coordination and control of
PEV charging during real-time operations to ensure that the aggregation can meet the market commitment [106] [. An investigation by PJM and](https://learn.pjm.com/energy-innovations/plug-in-electric)
Bayerische Motoren Werke (BMW) North America on PEV interaction
with wholesale electricity pricing signals and vehicle owner battery
charging habits revealed that managing PEV charging alleviates power
grid operation stress and reduces PEV fuel costs.


**Providing ancillary services.** Ancillary services mainly provided
by the aforementioned balancing resources in power systems assist
power system operators in maintaining system reliability. PEV batteries often have a short response time (less than 1 s) — the duration of
power consumption adjustment responding to a signal triggered from
the power system — to control signals so that aggregation of PEVs can
swiftly adjust the charging and discharging power following a power
system operator’s request [107] . Therefore, PEVs can be used for various
ancillary services in power systems such as frequency regulation and
operating reserve [108] . Frequency regulation maintains the stability of
grid frequency by balancing the power load and demand, and its operating time scale ranges from one second to tens of seconds. Through
aggregators, PEVs can improve frequency control performance of the
system [109] . Operating reserve service refers to the spare power capacity
that can compensate for the generation gap between load demand and
power supply within a few to tens of minutes. Idle PEVs can work as
energy storage units and improve the flexibility and reliability of grid
operation [110] [. In a pilot V2G project conducted by PJM and the University](https://learn.pjm.com/energy-innovations/plug-in-electric)
of Delaware in 2007, an electric BMW Mini had earned approximately
US$100 per month for its services when charged and discharged in
response to the PJM frequency regulation signal. From 30 January 2016
to 30 September 2017, a mixed-use 29 PEV demonstration fleet at the
US Los Angeles Air Force Base provided a total of 373 MWh of power
regulation to the California ISO power market [111] .


**Environmental aspects of PEV integration**
The environmental impact of PEV integration depends on how the
PEVs interact with the power system, especially with respect to
zero-emissions renewable generation. The higher the penetration of
renewables in the power system, the lower well to wheel CO 2 that PEVs
will emit [4][,][5] . However, if PEV charging profiles do not coincide with that
of renewable power generation, the peak–valley difference of the net
power load, which is the load not satisfied by renewable generation,
will increase (Fig. 1b). The mismatch will further decrease the energy
efficiency of fossil fuel power plants, increase the demands for balancing resources, reduce renewable power generation and increase CO 2
emissions [8] . Therefore, adopting these PEVs might have a negative effect
on the decarbonization of the interconnected power and transport
system. Hence, it is necessary to achieve a synergy between PEVs and
renewable generation at distribution and transmission levels.


**PEVs with distributed renewables**

Unlike centralized bulk power plants that are often built in rural areas,
renewable generation (for example, solar panels) is mainly distributed
and located close to end users. Therefore, PEVs can be directly powered
by on-site or nearby distributed renewable generation.


**Renewable PEV charging station.** Some charging stations are
equipped with on-site rooftop solar panels [112] and can be connected to
the power grid for backups, or operate in an island mode whereby a battery storage system is installed to manage the mismatch of renewable



[Nature Reviews Electrical Engineering | Volume 1 | January](http://www.nature.com/natrevelectreng) 2024 | 35–52 **44**


### **Review article**

generation and PEV charging [113] . The intermittent and variable renewable generation is first stored in the battery storage system and then
supplied to PEVs when needed. For example, 30 100% solar-powered
[PEV charging stations have been installed by a US company, Electrify](https://www.electrifyamerica.com/renewable-energy/)
[America, in rural California. In 2023, the Republic of Kenya launched its](https://www.electrifyamerica.com/renewable-energy/)
first solar-powered battery charging and battery swapping hub for rural
mobility [114] . Renewable-powered PEV charging stations are an important
charging solution for rural areas with limited access to power grids.


**PEVs and renewables in distribution grids.** Integrating a large amount
of renewable generation could deteriorate the power quality in distribution power grids, part of which can be alleviated by smart PEV
charging and discharging [115] . For example, renewable generation might
cause inverse power flow in unidirectional distribution networks,
resulting in high nodal power voltages which could damage electric
appliances (also known as the ‘voltage rise’) [115] . Charging PEVs when
there is high renewable generation can help to reduce inverse power
flow and mitigate the voltage rise problem [116] . Similarly, intermittent and
variable wind or solar generation could cause power flow fluctuations
in the distribution network, resulting in voltage deviations, three-phase
voltage and current imbalance, and harmonic distortion [115] .
PEV batteries can swiftly adjust their charging and discharging
power and smooth these power flow fluctuations [117] . Although fast
charging is often uncontrollable, it can also be used to coordinate
with renewable generation through the support of battery storage
systems [118] . PEV coordination with renewable generation in distribution
power grids must account for stochastic PEV charging behaviours and
variable renewable generation as well as power grid operation safety.
Current power grids are mostly AC systems. However, because PEV
batteries, solar panels and battery storage systems work with direct
current (DC), power grids could also have DC distribution power grids
or microgrids [119] . Adopting DC can help to reduce the number of power
conversion stages and improve energy efficiency [120] . Furthermore,
because there is no reactive power flow in a DC grid, the power quality
is easier to regulate [120] . The role of PEVs in a DC power grid is similar to
that in an AC power grid; for example, PEVs in destination charging
stations can work as flexible loads or batteries to promote renewable
generation adoption, whereas fast charging PEVs are often treated as
stochastic loads.

Bidirectional DC–DC converters are the key interface connecting PEV batteries and other resources with the DC power grid. These
devices adapt PEV batteries’ voltages with the power grid and regulate power flow and voltage/current. Various circuit types have been
designed to improve the voltage conversion ratio, power density and
conversion efficiency of converters [121] . For example, a bidirectional
four-port DC–DC converter was designed, which has dual bidirectional ports with only six switches and can support flexible integration
of batteries and renewables in a DC microgrid [122] . A series of advanced
controlling technologies for DC–DC converters have also been proposed to ensure stable, reliable and efficient operation of DC power
grids [123] . For example, uncertainties of renewables or power loads in
DC microgrids often cause stability problems and deteriorate the performance of DC–DC converters. By employing a robust controller with
disturbance compensation, a DC–DC converter can be more tolerant
to these uncertain disturbances [124] .


**PEVs with grid-scale renewables**
Grid integration of large-capacity, centralized renewable generation
can cause spatiotemporal supply and demand imbalances in the power



system. Aggregated PEVs can synergize with renewable generation to
mitigate these fluctuations by trading renewable electricity or mutual
spatiotemporal coordination [125] .


**Trading renewable electricity.** Participating in the power market
could be an option to coordinate PEVs with grid-scale renewable generation. Because renewable generation consumes no fossil fuel, the
generation per kilowatt-hour of renewable electricity is often close to
zero during the operation stage. Therefore, renewable power plants
often act as price takers offering electricity at very low prices in the
power market [126] . Consequently, during the intervals of heightened
renewable power generation, the electricity price often declines, potentially reaching negative values [126] . Aggregators of PEVs can then buy
this low-priced electricity to reduce costs by manipulating PEV charging and discharging behaviours, which in turn promotes renewable
generation adoption (see PJM website for details).
PEV aggregators and renewable energy providers can also collaborate with each other to participate in power markets [127] . For example, PEV
charging and discharging flexibilities are used to neutralize fluctuating
outputs of renewable generation. This strategy helps to reduce the
bidding risks of renewable power plants and increases profitability
in power markets. Furthermore, a fleet of PEVs and renewable generation can also form a ‘virtual power plant’ (VPP) with or without
other resources (such as controllable loads and energy storage) [128] . By
coordinating PEVs with renewable generation, and other resources,
a VPP can function similar to a conventional power plant, in which it
bids in the power market and even provides ancillary services [129][,][130] . For
example, eight VPPs participated in a 3-year demonstration project
in Australia. These VPPs are constructed of household batteries con
nected with solar generation from more than 7,150 residential customers and their total battery capacity is more than 31 MW. This project
shows that VPPs are highly capable of responding to energy market
prices in real time and they can provide more than one service (such
as arbitraging electricity prices and providing frequency regulation),
at times simultaneously [131] .


**Spatiotemporal dispatching of PEVs.** Being mobile battery storage
systems, PEVs can alleviate spatial supply–demand imbalances in
power systems. Strategically routing PEVs allows them to get charged
with renewable power when and where needed [132] . The resulting price
differences are reflected by the locational marginal prices that reflect
the spatiotemporal supply–demand relationship in power markets [133] .
For example, given that fuel is the major operating cost of PEVs for long
heavy-duty freight transport [133], consuming cheaper renewables is
economically convenient [134] and environmentally beneficial [135] for them.
Coordinating mobile PEV batteries with renewable generation is
even more relevant for autonomous PEVs. For example, centrally operated passenger/driver-free freight transport has fewer time constraints
and higher flexibility compared with vehicles with drivers [136][,][137] .


**Sustainable PEV integration**
To achieve sustainable PEV integration in power systems, supporting
infrastructure, technologies for modelling, operating and controlling
power grids and batteries, environmental considerations and incentive
mechanisms need to be assessed (Table 3). At stage 1, PEV charging is
manually planned by the driver without much flexibility. At stage 2,
smart PEV charging occurs by advanced remote operation and control
technologies. As a result, PEV charging profiles can be optimized to
reduce charging costs and generate revenue by providing local grid



[Nature Reviews Electrical Engineering | Volume 1 | January](http://www.nature.com/natrevelectreng) 2024 | 35–52 **45**


### **Review article**

**Table 3 | Road map of sustainable PEV integration in power systems**



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhangSustainablePluginElectric2024/zhangSustainablePluginElectric2024.pdf-11-0.png)































































PEV, plug-in electric vehicle; V2G, vehicle to grid.


services. At stage 3, PEVs can discharge to the power grid, allowing
higher flexibility to interact with the power grid. At stage 4, when PEVs
and charging infrastructures are fully autonomous, they will function
as mobile storage systems to provide spatiotemporal flexibility to
power grids.


**Supporting infrastructures**
Supporting infrastructures including charging, information and
communication systems are required for sustainable PEV integration.


**Charging infrastructure.** Planning PEV charging infrastructures initially focused on satisfying customers’ charging demands [65], alleviating
PEV charging’s adverse impacts on power grids [68] or promoting renewable generation integration [72] . PEV chargers are designed to be used
for typically 15–20 years. However, how PEVs interact with the grids,
particularly V2G, is often neglected, mostly because the business model
of smart PEV charging and discharging is not yet mature. Therefore,
most of the chargers currently being installed do not support V2G or
even smart charging. Understanding how charging infrastructure



can be designed and planned not only to satisfy charging demands
but also to maximize the future grid interaction capability of PEVs is
thereby essential [138] .


**Information and communication infrastructures.** Information

and communication infrastructures are crucial for synergistic and
sustainable PEV integration [139] . To promote PEV coordination with
renewables, sufficient sensors should be installed to monitor the
real-time status of PEVs, chargers, renewable generation and grids [140] .
For example, to realize high-frequency PEV controlling for regulation services, the frequency of information collection and communication needs to be in the order of seconds. This data transmission

causes extra power loads on communication systems which are often
isolated from the public ones [140][,][141] . Therefore, it is indispensable to
design low-cost and efficient information and communication infrastructure. Moreover, the large-scale integration of PEVs might also
threaten the cyber-physical security of power systems [142] . This requires
the information and communication infrastructure to have strong
cyber resilience.



[Nature Reviews Electrical Engineering | Volume 1 | January](http://www.nature.com/natrevelectreng) 2024 | 35–52 **46**


### **Review article**

**Modelling, operation and control**
**Modelling of PEVs.** Actively discharging electricity to the power grid
will accelerate battery degradation and lower the economic benefits
of a V2G programme. Therefore, it is necessary to quantify battery
degradation for different V2G systems [143] . User behaviours, such as
plug-in and plug-out times, determine PEV charging and discharging flexibility. However, these behaviours are highly stochastic and
difficult to predict. An advanced forecasting method is therefore
required to accurately predict PEV user behaviours for smart PEV
charging and discharging. Data-driven artificial intelligence (AI)
algorithms are popular approaches to forecast PEV user behaviours
(Box 1).
For grid integration, a PEV aggregator might need to represent
thousands of PEVs to bid in power markets. Therefore, given that every
PEV’s detailed constraints are computationally expensive, developing aggregate models of large-scale PEVs is indispensable [144] . Such
a model would describe a fleet of PEV behaviours as a whole with

simplified aggregate parameters (such as their overall power and
energy boundaries) instead of heterogeneous parameters of individual PEVs to reduce model complexity and improve computational

#### **Box 1**



efficiency. However, this simplification might limit accuracy and
result in operation and control errors. How to balance modelling
efficiency and accuracy for large-scale PEVs needs further in-depth
research.


**Operation and control.** Operating and controlling large-scale PEVs to
provide grid services or promote renewable generation are difficult.
First, an operator must simultaneously coordinate tens to thousands of
small-capacity and heterogeneous PEVs and respond to power systems’
control signals efficiently. Such computational complexity would be
intolerable with a centralized algorithm. Decentralized or distributed
algorithms are commonly used, in which the operation or control
decisions are not made at the operator level but at the charging station
or PEV level based on information shared between the operator and
PEVs [145] . These algorithms can considerably reduce the computational
burden, but often result in a heavy communication burden and might
not achieve optimal solution when the fleet size is too large. A compromise is to adopt a hierarchical framework [146], which is half-centralized
and half-decentralized, to balance between computational and communication overloads. Moreover, the operation and control of PEVs


## Application of AI in modelling, operating and controlling PEVs in power systems



The application of artificial intelligence (AI) technologies for the
integration of plug-in electric vehicles (PEVs) into power systems is a
hot research area. These AI methods can be divided into three major
categories, that is, unsupervised learning, supervised learning and
reinforcement learning.
Unsupervised and supervised learning acquire knowledge by
leveraging historical data. The difference is that the former works
with unlabelled data, as opposed to the latter. With the growing
use of PEVs and their supporting information and communications
systems, vast amounts of data are collected daily. Data-driven AI
methods can use these data to help with integrating PEVs into power
systems. Unsupervised learning (such as the _k_ -means algorithm) is
often used for the pre-processing of PEV charging data. For example,
by adopting an unsupervised clustering algorithm, PEV driving and
charging profiles can be grouped based on their similarity, which can
help in understanding PEV usage patterns [159] . Unsupervised learning
could also be used to plan PEV charging systems. By clustering PEV
charging demands based on their locations, a planner algorithm
can strategically locate their charging systems to satisfy these
demands [160][,][161] .

Based on historical data on PEV charging loads, supervised
learning can be used to train a neural network model to predict future
charging loads [162][,][163] . This prediction is important for ‘ahead-of-time’
operation of PEVs, such as bidding in power markets. Supervised
learning can also be applied for real-time operation or control
decision-making. For example, a system operator can first collect
historical PEV charging station operation data under different power
grid conditions (such as electricity price, charging demand and



local renewable generation). These data can then be used to train
a supervised learning model (for example, a neural network) to
predict suitable operation decisions under new conditions based
on the past experiences [164] . However, historical operation decisions
data may be unavailable or of suboptimal quality. To overcome this
issue, a PEV charging system operator can first build a model-based
operation strategy to generate optimal operation data under different
conditions, and then use these generated data to train an AI model [165] .
Reinforcement learning does not rely on historical data; instead,
it trains an agent by interacting with the environment through a ‘trial
and error’ process. During the training, if the agent makes a good
decision, which contributes positively to the operation objective
(for example, reducing electricity consumption costs), it gets
rewarded; otherwise, it is penalized. After taking sufficient iterations,
the agent learns how to make good decisions even in new situations.
Moreover, as the agent is continuously trained online, it can adapt to
a changing environment. Various reinforcement learning algorithms
have been used to solve the operation and control problems of
PEVs in power systems [151] . For example, a constrained reinforcement
learning algorithm [166] and a soft actor-critic deep reinforcement
learning algorithm [167] have been used to schedule PEV charging power
in distribution systems to respond to time-varying electricity prices
while including uncertain PEV parking and charging behaviours.
Reinforcement learning has also been applied to solve complex
decision-making challenges for coupling power and transport system.
Similarly, a multi-agent deep reinforcement learning algorithm has
been developed to study PEV charging stations’ pricing strategies
for autonomous mobility on-demand systems in cities [168] .



[Nature Reviews Electrical Engineering | Volume 1 | January](http://www.nature.com/natrevelectreng) 2024 | 35–52 **47**


### **Review article**

are exposed to inevitable uncertainties, including user behaviours,
renewable generation and market prices [147] . These uncertainties are
commonly addressed by stochastic programming, robust optimization, chance-constrained programming [148] and model predictive
control [149] . However, these methods often require large historical samples for uncertainty modelling, which may be unavailable, or have limitations in over-conservativeness (for instance, underestimating PEV
regulation capacity). Finally, PEV operation and control face demands
and constraints from both the power grid and the transport network [150] .
Notably, data-driven AI algorithms have shown great promise for
overcoming the limitations of existing operation and control models
(Box 1).


**Incentive mechanism**

Multiple stakeholders are involved in PEV integration, such as automobile companies, users, charging service providers and power grids.
Effective incentive mechanisms will therefore need to address the

interests of all stakeholders.


**Business models.** PEVs are already integrated into the power market,
such as time-of-use electricity prices, energy and ancillary service markets, in which PEVs can make revenue by smart charging and discharging. However, only smart charging has been applied so far whereas
discharging is still a proof of concept [151] . Because active discharging
may lead to battery degradation, cost and user compensation are
hard to evaluate [152] . Furthermore, this active discharging might also
influence vehicle warranty. Currently, V2G is only occasionally used,
for example, during camping when there is no access to the power
grid, by most customers in the current markets. In contrast, when PEVs
are used intensively to provide power grid services in the future, the
amount of battery degradation will be comparable with driving, which
might raise battery warranty concerns. To fully leverage PEV potential,
a business model which can solve this issue is urgently required. One
solution could be warranting battery charging cycles instead of driving
mileages [153], which would better reflect V2G usage.


**New power markets.** Existing power markets are mostly designed at
the transmission level for traditional power consumers and generators.
Policymakers need to redesign current power markets to encourage new players such as PEV aggregators or VPPs. These new power
markets should allow PEV-like distributed small-capacity resources
from different locations to jointly bid in them. Furthermore, power
markets at the distribution level also need to be designed; these new
distributed markets should provide a lucrative environment for PEVs to
provide distribution grid services and accommodate nearby renewable
generation [154] .


**Power and transport coordination.** Traditionally, the power and
transport sectors have always operated independently, a status which
has now changed with the emergence of PEVs. Failing to coordinate
between these two sectors might lead to energy inefficiencies, higher
carbon emissions and malfunctions. For example, in Shenzhen on
19 May 2018, a scheduled power outage forced up to 2,700 taxis (10%
of the total) out of service because they could not be charged on time [155] .
Furthermore, it is essential to appropriately price PEV charging services. Prices should not only encapsulate the electricity supply costs
but also account for traffic congestion costs. This pricing approach
can bolster the integration of renewable generation, modulate traffic
flow and augment transportation efficiency [156] .



**Outlook**

In the future, anticipated advancements in battery technology include
reduced costs, augmented energy densities, accelerated charging
rates, extended durability and enhanced safety. An increase in energy
density allows a PEV to achieve equivalent range with a more compact

                                                  and lightweight battery, thereby optimizing space utilization and aug
menting fuel efficiency. Should a PEV reduce its charging duration for
reaching 80% capacity from the present standard of approximately
30 min to a mere 10 min or less, its charging experience could parallel
the refuelling time frames of traditional vehicles. There are two main
future research directions. One is to design new types of batteries.
For example, since 2020, solid-state batteries have attracted interest
owing to their superior energy density, charging rate and safety when
compared with Li-ion batteries. The other direction is to further exploit
the potential of existing batteries with more advanced battery management. For example, techniques such as data-driven predictive operations and maintenance — which combine battery health estimation,
fault diagnosis and safety alerts — are expected to markedly improve
the durability and safety of battery systems.
A well-designed charging infrastructure is the key to alleviating
range anxiety and promoting adoption of PEVs. The future planners
should take the objectives and constraints of both the transport and
power sectors into consideration when designing the infrastructure.
Apart from passively satisfying the charging demands of PEVs, a more
forward-looking infrastructure plan that supports the upcoming PEV
integration paradigms, such as smart charging and even discharging,
is needed. Different charging options are suitable for different application scenarios, and they complement each other. Future infrastructure
planning should also address this complementarity. In addition, an
affordable, efficient, reliable and secure information and communication infrastructure is essential to support the large-scale integration of PEVs. This requires systematic innovations in data collection,
perception and communication.
With adequate operation and control, smart PEV charging and
discharging power can increase the operational flexibility of the power
grid and synergize with renewable generation to promote the decarbonization of the interconnected power and transport system.
However, it is still a challenge to effectively model, operate and control
large-scale, small-capacity and heterogeneous PEVs. Uncertainty is
one of the crucial problems. Compared with stationary energy storage
systems, the combined power and energy capacity of a fleet of PEVs
fluctuates and is uncertain due to the variable driving and charging
behaviours. Another substantial challenge lies in scalability. For system
operators managing thousands of PEVs, executing optimal operational
decisions and meticulously controlling each vehicle’s power become
arduous. As the global adoption of PEVs grows, there will be a surge in
real-world data related to their driving and charging patterns. Merging
conventional physical model-based modelling, operation and control
approaches with cutting-edge AI-assisted data-driven technologies
offers a promising avenue for future research.
Moreover, innovative financial incentives are required. Currently,
no mature business model or market mechanism exists that maximizes

the flexibility of PEVs while also effectively coordinating the interests of various stakeholders. This challenge calls for joint innovations
involving power system operators, charging service providers and
automobile manufacturers. As the costs of grid-scale energy storage
systems continue to decline, the comparative advantage of V2G may
diminish. After all, leveraging numerous small-capacity PEVs demands
more efforts than utilizing a singular, large grid-scale storage. Hence,



[Nature Reviews Electrical Engineering | Volume 1 | January](http://www.nature.com/natrevelectreng) 2024 | 35–52 **48**


### **Review article**

how to economically utilize the V2G capacity of each individual PEV for
providing power grid services remains a pivotal question. However,
despite these considerations, V2G holds promise in bolstering the
resilience of power systems, especially in scenarios where cost is not
the primary concern.
Autonomous driving is a disruptive technology set to revolutionize
the landscape of PEV grid integration. Acting as driverless mobile storage systems, autonomous PEVs will possess capabilities to interact with
the power grid and promote system decarbonization. For instance, an
autonomous PEV fleet can be dispatched to locations with abundant
renewables to harness zero-emissions electricity or to regions experiencing power outages, offering emergency power supply without
disturbing any driver or passenger. Yet, relevant studies are still nascent
and necessitate sustained interdisciplinary collaboration.


Published online: 11 January 2024


**References**

1. International Energy Agency. Global EV outlook 2023. _iea_ [https://www.iea.org/reports/](https://www.iea.org/reports/global-ev-outlook-2023)
[global-ev-outlook-2023 (2023).](https://www.iea.org/reports/global-ev-outlook-2023)
2. International Energy Agency. CO 2 emissions in 2022. _iea_ [https://www.iea.org/reports/](https://www.iea.org/reports/co2-emissions-in-2022)
[co2-emissions-in-2022 (2023).](https://www.iea.org/reports/co2-emissions-in-2022)
3. Choma, E. F., Evans, J. S., Hammitt, J. K., Gómez-Ibáñez, J. A. & Spengler, J. D. Assessing
the health impacts of electric vehicles through air pollution in the United States.
_Environ. Int._ **144**, 106015 (2020).
4. Knobloch, F. et al. Net emission reductions from electric cars and heat pumps in 59 world
regions over time. _Nat. Sustain._ **3**, 437–447 (2020).
**This article reports a comprehensive analysis of life-cycle emissions of electric vehicles,**
**which motivates the synergy between power and transport sectors for decarbonization** .
5. Challa, R., Kamath, D. & Anctil, A. Well-to-wheel greenhouse gas emissions of electric
versus combustion vehicles from 2018 to 2030 in the US. _J. Environ. Manage._ **308**,
114592 (2022).
6. Heptonstall, P. J. & Gross, R. J. K. A systematic review of the costs and impacts of
integrating variable renewables into power grids. _Nat. Energy_ **6**, 72–83 (2021).
7. International Energy Agency. World energy outlook 2022. _iea_ [https://www.iea.org/](https://www.iea.org/reports/world-energy-outlook-2022)
[reports/world-energy-outlook-2022 (2022).](https://www.iea.org/reports/world-energy-outlook-2022)
8. Chen, X. et al. Impacts of fleet types and charging modes for electric vehicles on
emissions under different penetrations of wind power. _Nat. Energy_ **3**, 413–421 (2018).
9. Denholm, P. et al. The challenges of achieving a 100% renewable electricity system in the
United States. _Joule_ **5**, 1331–1352 (2021).
10. Feng, X. et al. Thermal runaway mechanism of lithium-ion battery for electric vehicles:
a review. _Energy Stor. Mater._ **10**, 246–267 (2018).
11. Frith, J. T., Lacey, M. J. & Ulissi, U. A non-academic perspective on the future of
lithium-based batteries. _Nat. Commun._ **14**, 420 (2023).
12. Schmuch, R. et al. Performance and cost of materials for lithium-based rechargeable
automotive batteries. _Nat. Energy_ **3**, 267–278 (2018).
13. Yang, X., Liu, T. & Wang, C. Thermally modulated lithium iron phosphate batteries for
mass-market electric vehicles. _Nat. Energy_ **6**, 176–185 (2021).
14. Wang, C. et al. Lithium-ion battery structure that self-heats at low temperatures. _Nature_
**529**, 515–518 (2016).
**This article is the first presentation of a Li-ion battery self-heating structure without**
**external heating devices or electrolyte additives** .
15. Che, Y. et al. Health prognostics for lithium-ion batteries: mechanisms, methods, and
prospects. _Energy Environ. Sci._ **16**, 338–371 (2023).
16. Jones, P. K., Stimming, U. & Lee, A. A. Impedance-based forecasting of lithium-ion battery
performance amid uneven usage. _Nat. Commun._ **13**, 4806 (2022).
17. Han, X. et al. A review on the key issues of the lithium ion battery degradation among the
whole life cycle. _eTransportation_ **1**, 100005 (2019).
18. Xiong, R. et al. Lithium-ion battery ageing mechanisms and diagnosis method for
automotive applications: recent advances and perspectives. _Renew. Sust. Energ. Rev._
**131**, 110048 (2020).
19. Edge, J. S. et al. Lithium ion battery degradation: what you need to know. _Phys. Chem._
_Chem. Phys._ **23**, 8200–8221 (2021).
20. Hu, X., Xu, L., Lin, X. & Pecht, M. Battery lifetime prognostics. _Joule_ **4**, 310–346 (2020).
21. Suri, G. & Onori, S. A control-oriented cycle-life model for hybrid electric vehicle
lithium-ion batteries. _Energy_ **96**, 644–653 (2016).
22. Hu, X., Che, Y., Lin, X. & Onori, S. Battery health prediction using fusion-based feature
selection and machine learning. _IEEE Trans. Transp. Electrif._ **7**, 382–398 (2020).
23. Thelen, A. et al. Integrating physics-based modeling and machine learning for
degradation diagnostics of lithium-ion batteries. _Energy Stor. Mater._ **50**, 668–695
(2022).
24. Aykol, M. et al. Perspective-combining physics and machine learning to predict battery
lifetime. _J. Electrochem. Soc._ **168**, 030525 (2021).



25. Chen, Y. et al. A review of lithium-ion battery safety concerns: the issues, strategies, and
testing standards. _J. Energy Chem._ **59**, 83–99 (2021).
26. Hao, M., Li, J., Park, S., Moura, S. & Dames, C. Efficient thermal management of Li-ion
batteries with a passive interfacial thermal regulator based on a shape memory alloy.
_Nat. Energy_ **3**, 899–906 (2018).
27. Longchamps, R. S., Yang, X. & Wang, C. Fundamental insights into battery thermal
management and safety. _ACS Energy Lett._ **7**, 1103–1111 (2022).
28. Lai, X. et al. Mechanism, modeling, detection, and prevention of the internal short circuit
in lithium-ion batteries: recent advances and perspectives. _Energy Stor. Mater._ **35**,
470–499 (2021).
**This article comprehensively reviews the mechanism, detection and prevention of the**
**internal short circuit in Li-ion batteries, which provides insights for more advanced**
**battery fault diagnosis and safer battery management systems** .
29. Deng, J., Bae, C., Marcicki, J., Masias, A. & Miller, T. Safety modelling and testing
of lithium-ion batteries in electrified vehicles. _Nat. Energy_ **3**, 261–266 (2018).
30. Finegan, D. P. et al. The application of data-driven methods and physics-based learning
for improving battery safety. _Joule_ **5**, 316–329 (2021).
31. Tomaszewska, A. et al. Lithium-ion battery fast charging: a review. _eTransportation_ **1**,
100011 (2019).
32. Liu, Y., Zhu, Y. & Cui, Y. Challenges and opportunities towards fast-charging battery
materials. _Nat. Energy_ **4**, 540–550 (2019).
33. Attia, P. M. et al. Closed-loop optimization of fast-charging protocols for batteries with
machine learning. _Nature_ **578**, 397–402 (2020).
**This article develops a machine learning method to help prolong battery cycle life by**
**combining an early-prediction model and a Bayesian optimization algorithm** .
34. Wang, C. et al. Fast charging of energy-dense lithium-ion batteries. _Nature_ **611**, 485–490
(2022).
35. Yang, X., Zhang, G., Ge, S. & Wang, C. Fast charging of lithium-ion batteries at all
temperatures. _Proc. Natl Acad. Sci. USA_ **115**, 7266–7271 (2018).
36. Hu, X. et al. Battery warm-up methodologies at subzero temperatures for automotive
applications: recent advances and perspectives. _Prog. Energy Combust. Sci._ **77**, 100806
(2020).
37. Yang, X. et al. Asymmetric temperature modulation for extreme fast charging
of lithium-ion batteries. _Joule_ **3**, 3002–3019 (2019).
38. Rivera, S. et al. Electric vehicle charging infrastructure: from grid to battery. _IEEE Ind._
_Electron._ **15**, 37–51 (2021).
39. Schmidt, M., Staudt, P. & Weinhardt, C. Evaluating the importance and impact of user
behavior on public destination charging of electric vehicles. _Appl. Energy_ **258**, 114061
(2020).
40. Miele, A., Axsen, J., Wolinetz, M., Maine, E. & Long, Z. The role of charging and refuelling
infrastructure in supporting zero-emission vehicle sales. _Transp. Res. D Transp. Environ._
**81**, 102275 (2020).
41. Hardman, S. et al. A review of consumer preferences of and interactions with
electric vehicle charging infrastructure. _Transp. Res. D Transp. Environ._ **62**, 508–523
(2018).
42. Baresch, M. & Moser, S. Allocation of e-car charging: assessing the utilization of
charging infrastructures by location. _Transp. Res. Part A Policy Pract._ **124**, 388–395
(2019).

43. International Electrotechnical Commission. IEC 61851-1: 2017 electric vehicle conductive

charging system — part 1: general requirements. _IEC Webstore_ [https://webstore.iec.ch/](https://webstore.iec.ch/publication/33644)
[publication/33644 (2017).](https://webstore.iec.ch/publication/33644)
44. Wang, L. et al. Grid impact of electric vehicle fast charging stations: trends, standards,
issues and mitigation measures — an overview. _IEEE Open J. Power Electron._ **2**, 56–74
(2021).
45. Zhan, W. et al. A review of siting, sizing, optimal scheduling, and cost–benefit analysis for
battery swapping stations. _Energy_ **258**, 124723 (2022).
46. Dixon, J., Andersen, P. B., Bell, K. & Træholt, C. On the ease of being green: an investigation
of the inconvenience of electric vehicle charging. _Appl. Energy_ **258**, 114090 (2020).
47. Cui, D. et al. Operation optimization approaches of electric vehicle battery swapping and
charging station: a literature review. _Energy_ **263**, 126095 (2023).
48. Ahmad, F., Alam, M. S. & Asaad, M. Developments in xEVs charging infrastructure and
energy management system for smart microgrids including xEVs. _Sustain. Cities Soc._ **35**,
552–564 (2017).
49. Affanni, A., Bellini, A., Franceschini, G., Guglielmi, P. & Tassoni, C. Battery choice
and management for new-generation electric vehicles. _IEEE Trans. Ind. Electron._ **52**,
1343–1349 (2005).
50. Aulton New Energy Automotive Technology. About Aulton. _Aulton_ [https://www.aulton.com/](https://www.aulton.com/index.php/en/list-4.html)
[index.php/en/list-4.html (2023).](https://www.aulton.com/index.php/en/list-4.html)
51. Afridi, K. The future of electric vehicle charging infrastructure. _Nat. Electron._ **5**, 62–64
(2022).
**This review provides an insightful discussion and analysis on the benefits of promoting**
**dynamic wireless charging on highways** .
52. Regensburger, B. et al. in _2018 IEEE Applied Power Electronics Conf. Exposition_ 666–671
(2018).
53. Machura, P. & Li, Q. A critical review on wireless charging for electric vehicles. _Renew._
_Sust. Energ. Rev._ **104**, 209–234 (2019).
54. Laporte, S., Coquery, G., Deniau, V., De Bernardinis, A. & Hautière, N. Dynamic wireless
power transfer charging infrastructure for future EVs: from experimental track to real
circulated roads demonstrations. _World Electr. Veh. J._ **10**, 84 (2019).



[Nature Reviews Electrical Engineering | Volume 1 | January](http://www.nature.com/natrevelectreng) 2024 | 35–52 **49**


### **Review article**

55. Rim, C. T. and Mi, C. in _Wireless Power Transfer for Electric Vehicles and Mobile Devices_
Ch. 9 161–208 (Wiley, 2017).
56. Cui, S., Yao, B., Chen, G., Zhu, C. & Yu, B. The multi-mode mobile charging service based
on electric vehicle spatiotemporal distribution. _Energy_ **198**, 117302 (2020).
57. Afshar, S., Macedo, P., Mohamed, F. & Disfani, V. Mobile charging stations for electric
vehicles — a review. _Renew. Sust. Energ. Rev._ **152**, 111654 (2021).
58. Scwartz, A. Nation-E develops first mobile electric vehicle charging station.
_Fast Company_ [https://www.fastcompany.com/1688633/nation-e-develops-first-mobile-](https://www.fastcompany.com/1688633/nation-e-develops-first-mobile-electric-vehicle-charging-station)
[electric-vehicle-charging-station (2010).](https://www.fastcompany.com/1688633/nation-e-develops-first-mobile-electric-vehicle-charging-station)
59. NIO. NIO announces power north plan and its ET7 makes auto show debut. _NIO_ [https://](https://www.nio.com/news/nio-announces-power-north-plan-and-its-et7-makes-auto-show-debut)
[www.nio.com/news/nio-announces-power-north-plan-and-its-et7-makes-auto-show-debut](https://www.nio.com/news/nio-announces-power-north-plan-and-its-et7-makes-auto-show-debut)
(2021).
60. Ahmad, F., Iqbal, A., Ashraf, I., Marzband, M. & Khan, I. Optimal location of electric vehicle
charging station and its impact on distribution network: a review. _Energy Rep._ **8**, 2314–2333
(2022).
61. Metais, M. O., Jouini, O., Perez, Y., Berrada, J. & Suomalainen, E. Too much or not
enough? Planning electric vehicle charging infrastructure: a review of modeling options.
_Renew. Sust. Energ. Rev._ **153**, 111719 (2022).
62. Wu, H. A survey of battery swapping stations for electric vehicles: operation modes
and decision scenarios. _IEEE Trans. Intell. Transp. Syst._ **23**, 10163–10185 (2021).
63. Duan, X. et al. Planning strategy for an electric vehicle fast charging service
provider in a competitive environment. _IEEE Trans. Transp. Electrif._ **8**, 3056–3067
(2022).
64. MirHassani, S. A. & Ebrazi, R. A flexible reformulation of the refueling station location
problem. _Transp. Sci._ **47**, 617–628 (2013).
65. He, J., Yang, H., Tang, T. & Huang, H. An optimal charging station location model with the
consideration of electric vehicle’s driving range. _Transp. Res. Part C Emerg. Technol._ **86**,
641–654 (2018).
66. Shen, Z. M., Feng, B., Mao, C. & Ran, L. Optimization models for electric vehicle
service operations: a literature review. _Transp. Res. Part B Meth._ **128**, 462–477
(2019).
67. Kavianipour, M. et al. Electric vehicle fast charging infrastructure planning in urban
networks considering daily travel and charging behavior. _Transp. Res. D Transp. Environ._
**93**, 102769 (2021).
68. Li, C. et al. Data-driven planning of electric vehicle charging infrastructure: a case study
of Sydney, Australia. _IEEE Trans. Smart Grid_ **12**, 3289–3304 (2021).
69. Arias, N. B., Tabares, A., Franco, J. F., Lavorato, M. & Romero, R. Robust joint expansion
planning of electrical distribution systems and EV charging stations. _IEEE Trans. Sust._
_Energy_ **9**, 884–894 (2017).
70. Wang, X., Shahidehpour, M., Jiang, C. & Li, Z. Coordinated planning strategy for electric
vehicle charging stations and coupled traffic–electric networks. _IEEE Trans. Power Syst._
**34**, 268–279 (2018).
71. Wei, W., Wu, L., Wang, J. & Mei, S. Network equilibrium of coupled transportation and
power distribution systems. _IEEE Trans. Smart Grid_ **9**, 6764–6779 (2017).
72. Ferro, G., Minciardi, R., Parodi, L. & Robba, M. Optimal planning of charging stations
in coupled transportation and power networks based on user equilibrium conditions.
_IEEE Trans. Autom. Sci. Eng._ **19**, 48–59 (2022).
73. Shao, C., Qian, T., Wang, Y. & Wang, X. Coordinated planning of extreme fast charging
stations and power distribution networks considering on-site storage. _IEEE Trans. Intell._
_Transp. Syst._ **22**, 493–504 (2020).
74. Zheng, Y., Shao, Z., Zhang, Y. & Jian, L. A systematic methodology for mid-and-long
term electric vehicle charging load forecasting: the case study of Shenzhen, China.
_Sustain. Cities Soc._ **56**, 102084 (2020).
75. Muratori, M. Impact of uncoordinated plug-in electric vehicle charging on residential
power demand. _Nat. Energy_ **3**, 193–201 (2018).
76. Jenn, A. & Highleyman, J. Distribution grid impacts of electric vehicles: a California case
study. _Iscience_ **25**, 103686 (2022).
**This article presents a timely analysis in California, USA, showing that large-scale PEV**
**integration may significantly overload the current distribution power grids** .
77. Assolami, Y. O., Gaouda, A. & El-shatshat, R. Impact on voltage quality and transformer
ageing of residential prosumer ownership of plug-in electric vehicles: assessment and
solutions. _IEEE Trans. Transp. Electrif._ **8**, 492–509 (2021).
78. Shaukat, N. et al. A survey on electric vehicle transportation within smart grid system.
_Renew. Sust. Energ. Rev._ **8**, 1329–1349 (2018).
79. Lucas, A., Bonavitacola, F., Kotsakis, E. & Fulli, G. Grid harmonic impact of multiple
electric vehicle fast charging. _Electr. Power Syst. Res._ **127**, 13–21 (2015).
80. Jabalameli, N., Su, X. & Ghosh, A. Online centralized charging coordination of PEVs with
decentralized Var discharging for mitigation of voltage unbalance. _IEEE Power Energy_
_Technol. Syst. J._ **6**, 152–161 (2019).
81. Zhao, J., Wang, Y., Song, G., Li, P., Wang, C. & Wu, J. Congestion management method of
low-voltage active distribution networks based on distribution locational marginal price.
_IEEE Access._ **7**, 32240–32255 (2019).
82. Gunkel, P. A., Bergaentzlé, C., Jensen, I. G. & Scheller, F. From passive to active: flexibility
from electric vehicles in the context of transmission system development. _Appl. Energy_
**277**, 115526 (2020).
83. Speidel, S. & Braunl, T. Driving and charging patterns of electric vehicles for energy
usage. _Renew. Sust. Energ. Rev._ **40**, 97–110 (2014).
84. Solanke, T. U. et al. A review of strategic charging–discharging control of grid-connected
electric vehicles. _J. Energy Storage_ **28**, 101193 (2020).



85. Powell, S., Cezar, G. V., Min, L., Azevedo, I. M. L. & Rajagopal, R. Charging infrastructure
access and operation to reduce the grid impacts of deep electric vehicle adoption.
_Nat. Energy_ **7**, 932–945 (2022).
**This article reports a comprehensive analysis showing that building proper charging**
**infrastructure and adopting smart charging control can significantly alleviate the**
**adverse impacts of PEV charging on power grids** .
86. Venegas, F. G., Petit, M. & Perez, Y. Active integration of electric vehicles into distribution
grids: barriers and frameworks for flexibility services. _Renew. Sust. Energ. Rev._ **145**,
111060 (2021).
87. Kwon, S. Y., Park, J. Y. & Kim, Y. J. Optimal V2G and route scheduling of mobile energy
storage devices using a linear transit model to reduce electricity and transportation
energy losses. _IEEE Trans. Ind. Appl._ **56**, 34–47 (2020).
88. Li, X. et al. A cost–benefit analysis of V2G electric vehicles supporting peak shaving
in Shanghai. _Electr. Power Syst. Res._ **179**, 106058 (2020).
89. Huang, L. et al. A distributed optimization model for mitigating three-phase power
imbalance with electric vehicles and grid battery. _Electr. Power Syst. Res._ **210**, 108080
(2022).
90. Luo, Q., Zhou, Y., Hou, W. & Peng, L. A hierarchical blockchain architecture based V2G
market trading system. _Appl. Energy_ **307**, 118167 (2022).
91. Brown, M. A. & Soni, A. Expert perceptions of enhancing grid resilience with electric
vehicles in the United States. _Energy Res. Soc. Sci._ **57**, 101241 (2019).
92. Hussain, A. & Musilek, P. Resilience enhancement strategies for and through electric
vehicles. _Sustain. Cities Soc._ **80**, 103788 (2022).
**This review provides a comprehensive discussion on utilizing V2G to enhance the**
**resilience of power grids** .
93. Ewing, J. G.M. Will add backup power function to its electric vehicles. _NY Times_ [https://](https://www.nytimes.com/2023/08/08/business/energy-environment/gm-backup-electric-power.html)
[www.nytimes.com/2023/08/08/business/energy-environment/gm-backup-electric-power.](https://www.nytimes.com/2023/08/08/business/energy-environment/gm-backup-electric-power.html)
[html (2023).](https://www.nytimes.com/2023/08/08/business/energy-environment/gm-backup-electric-power.html)
94. González, L. G., Siavichay, E. & Espinoza, J. L. Impact of EV fast charging stations on the
power distribution network of a Latin American intermediate city. _Renew. Sust. Energ. Rev._
**107**, 309–318 (2019).
95. Tu, H., Feng, H., Srdic, S. & Lukic, S. Extreme fast charging of electric vehicles:
a technology overview. _IEEE Trans. Transp. Electrif._ **5**, 861–878 (2019).
96. Zhou, X., Zou, S., Wang, P. & Ma, Z. ADMM-based coordination of electric vehicles
in constrained distribution networks considering fast charging and degradation.
_IEEE Trans. Intell. Transp. Syst._ **22**, 565–578 (2021).
97. Huang, Y. & Kockelman, K. M. Electric vehicle charging station locations: elastic demand,
station congestion, and network equilibrium. _Transp. Res. D Transp. Environ._ **78**, 102179
(2020).
98. Hu, J., Ye, C., Ding, Y., Tang, J. & Liu, S. A distributed MPC to exploit reactive power V2G
for real-time voltage regulation in distribution networks. _IEEE Trans. Smart Grid_ **13**,
576–588 (2022).
99. Mejia-Ruiz, G. E. et al. Coordinated optimal Volt/Var control for distribution networks via
D-PMUs and EV chargers by exploiting the eigensystem realization. _IEEE Trans. Ind. Appl._
**12**, 2425–2438 (2021).
100. Pirouzi, S., Latify, M. A. & Yousefi, G. R. Conjugate active and reactive power management
in a smart distribution network through electric vehicles: a mixed integer-linear
programming model. _Sustain. Energy Grids Netw._ **22**, 100344 (2020).
101. Mazumder, M. & Debbarma, S. EV charging stations with a provision of V2G and voltage
support in a distribution network. _IEEE Syst. J._ **15**, 662–671 (2021).
102. Tarroja, B., Zhang, L., Wifvat, V., Shaffer, B. & Samuelsen, S. Assessing the stationary
energy storage equivalency of vehicle-to-grid charging battery electric vehicles. _Energy_
**106**, 673–690 (2016).
103. El-Taweel, N. A., Farag, H., Shaaban, M. F. & AlSharidah, M. E. Optimization model for EV
charging stations with PV farm transactive energy. _IEEE Trans. Ind. Inform._ **18**, 4608–4621
(2022).
104. Mersky, A. C. & Samaras, C. Environmental and economic trade-offs of city vehicle fleet
electrification and photovoltaic installation in the US PJM interconnection. _Environ. Sci._
_Technol._ **54**, 380–389 (2019).
105. Han, S., Lee, D. & Park, J. B. Optimal bidding and operation strategies for EV aggegators
by regrouping aggregated EV batteries. _IEEE Trans. Smart Grid_ **11**, 4928–4937 (2020).
106. Wolinetz, M. et al. Simulating the value of electric-vehicle–grid integration using
a behaviourally realistic model. _Nat. Energy_ **3**, 132–139 (2018).
107. Xu, C., Behrens, P. & Gasper, P. et al. Electric vehicle batteries alone could satisfy
short-term grid storage demand by as early as 2030. _Nat. Commun._ **14**, 119 (2023).
108. Sevdari, K., Calearo, L., Andersen, P. B. & Marinelli, M. Ancillary services and electric
vehicles: an overview from charging clusters and chargers technology perspectives.
_Renew. Sust. Energ. Rev._ **167**, 112666 (2022).
109. Wang, M., Mu, Y., Shi, Q., Jia, H. & Li, F. Electric vehicle aggregator modeling and control
for frequency regulation considering progressive state recovery. _IEEE Trans. Smart Grid_
**11**, 4176–4189 (2020).
110. Kong, L., Zhang, H., Li, W., Bai, H. & Dai, N. Spatial-temporal scheduling of electric bus
fleet in power-transportation coupled network. _IEEE Trans. Transp. Electrif._ **9**, 2969–2982
(2023).
111. Black, D., MacDonald, J., DeForest, N. & Gehbauer, C. Los Angeles Air Force Base
vehicle-to-grid demonstration: final project report ( _California Energy Commission_, 2018).
112. Kaufmann, R. K., Newberry, D., Chen, X. & Gopal, S. Feedbacks among electric vehicle
adoption, charging, and the cost and installation of rooftop solar photovoltaics.
_Nat. Energy_ **6**, 143–149 (2021).



[Nature Reviews Electrical Engineering | Volume 1 | January](http://www.nature.com/natrevelectreng) 2024 | 35–52 **50**


### **Review article**

113. Fachrizal, R., Shepero, M., Aberg, M. & Munkhammar, J. Optimal PV–EV sizing at solar
powered workplace charging stations with smart charging schemes considering
self-consumption and self-sufficiency balance. _Appl. Energy_ **307**, 118139 (2022).
114. Kuhudzai, J. R. First solar-powered battery charging & swapping hub for rural mobility
launches in Kenya. _CleanTechnica_ [https://cleantechnica.com/2023/08/07/first-solar-](https://cleantechnica.com/2023/08/07/first-solar-powered-battery-charging-swapping-hub-for-rural-mobility-launches-in-kenya/)
[powered-battery-charging-swapping-hub-for-rural-mobility-launches-in-kenya/ (2023).](https://cleantechnica.com/2023/08/07/first-solar-powered-battery-charging-swapping-hub-for-rural-mobility-launches-in-kenya/)
115. Kharrazi, A., Sreeram, V. & Mishra, Y. Assessment techniques of the impact of grid-tied
rooftop photovoltaic generation on the power quality of low voltage distribution
network — a review. _Renew. Sust. Energ. Rev._ **120**, 109643 (2020).
116. Kikusato, H. et al. Electric vehicle charging management using auction mechanism for
reducing PV curtailment in distribution systems. _IEEE Trans. Sust. Energy_ **11**, 1394–1403
(2019).
117. Wang, L., Dubey, A., Gebremedhin, A. H., Srivastava, A. K. & Schulz, N. MPC-based
decentralized voltage control in power distribution systems with EV and PV coordination.
_IEEE Trans. Smart Grid_ **13**, 2908–2919 (2022).
118. Saha, J., Kumar, N. & Panda, S. K. Adaptive grid-supportive control for solar-power
integrated electric-vehicle fast charging station. _IEEE Trans. Energy Convers._ **38**,
2034–2044 (2023).
119. Strunz, K., Abbasi, E. & Huu, D. N. DC microgrid for wind and solar power integration.
_IEEE J. Emerg. Sel. Top. Power Electron._ **2**, 115–126 (2013).
120. Chandra, A., Singh, G. K. & Pant, V. Protection techniques for DC microgrid — a review.
_Electr. Power Syst. Res._ **187**, 106439 (2020).
121. Safayatullah, M., Elrais, M. T., Ghosh, S., Rezaii, R. & Batarseh, I. A comprehensive review
of power converter topologies and control methods for electric vehicle fast charging
applications. _IEEE Access_ **10**, 40753–40793 (2022).
122. Zeng, J., Du, X. & Yang, Z. A multiport bidirectional DC–DC converter for hybrid
renewable energy system integration. _IEEE Trans. Power Electron._ **36**, 12281–12291 (2021).
123. Xu, Q. et al. Review on advanced control technologies for bidirectional DC/DC
converters in DC microgrids. _IEEE J. Emerg. Sel. Top. Power Electron._ **9**, 1205–1221 (2020).
124. Cao, M., Li, S., Yang, J. & Zhang, K. Mismatched disturbance compensation enhanced
robust H∞ control for the DC–DC boost converter feeding constant power loads.
_IEEE Trans. Energy Convers._ **38**, 1300–1310 (2023).
125. Colmenar-Santos, A., Muñoz-Gómez, A., Rosales-Asensio, E. & López-Rey, Á. Electric
vehicle charging strategy to support renewable energy sources in Europe 2050
low-carbon scenario. _Energy_ **183**, 61–74 (2019).
126. Csereklyei, Z., Qu, S. & Ancev, T. The effect of wind and solar power generation on
wholesale electricity prices in Australia. _Energy Policy_ **131**, 358–369 (2019).
127. Zeynali, S., Nasiri, N., Marzband, M. & Ravadanegh, S. N. A hybrid robust–stochastic
framework for strategic scheduling of integrated wind farm and plug-in hybrid electric
vehicle fleets. _Appl. Energy_ **300**, 117432 (2021).
128. Abbasi, M. H., Taki, M., Rajabi, A., Li, L. & Zhang, J. Coordinated operation of electric
vehicle charging and wind power generation as a virtual power plant: a multi-stage risk
constrained approach. _Appl. Energy_ **239**, 1294–1307 (2019).
129. Koraki, D. & Strunz, K. Wind and solar power integration in electricity markets and
distribution networks through service-centric virtual power plants. _IEEE Trans. Power_
_Syst._ **33**, 473–485 (2017).
130. Naval, N. & Yusta, J. M. Virtual power plant models and electricity markets — a review.
_Renew. Sust. Energ. Rev._ **149**, 111393 (2021).
131. Kristoff, M. M. What is the state of virtual power plants in Australia? From thin margins to a
future of VPP-tailers (IEEFA, 2022).
132. Ding, Z., Zhang, Y., Tan, W., Pan, X. & Tang, H. Pricing based charging navigation scheme
for highway transportation to enhance renewable generation integration. _IEEE Trans. Ind._
_Appl._ **59**, 108–117 (2023).
**This article presents a PEV charging service pricing mechanism to help promote**
**renewable generation adoption, which is also a great example of the power–transport**

**synergy** .
133. Zhang, H., Hu, Z. & Song, Y. Power and transport nexus: routing electric vehicles to
promote renewable power integration. _IEEE Trans. Smart Grid_ **11**, 3291–3301 (2020).
134. Qiu, K., Ribberink, H. & Entchev, E. Economic feasibility of electrified highways for
heavy-duty electric trucks. _Appl. Energy_ **326**, 119935 (2022).
135. Tong, F., Jenn, A., Wolfson, D., Scown, C. D. & Auffhammer, M. Health and climate impacts
from long-haul truck electrification. _Environ. Sci. Technol._ **55**, 8514–8523 (2021).
136. Zhong, H., Li, W., Burris, M. W., Talebpour, A. & Sinha, K. C. Will autonomous vehicles change
auto commuters’ value of travel time? _Transp. Res. D Transp. Environ._ **83**, 102303 (2020).
137. Jones, E. C. & Leibowicz, B. D. Contributions of shared autonomous vehicles to climate
change mitigation. _Transp. Res. D Transp. Environ._ **72**, 279–298 (2019).
138. Ali, A. et al. Multi-objective allocation of EV charging stations and RESs in distribution
systems considering advanced control schemes. _IEEE Trans. Veh. Technol._ **72**, 3146–3160
(2022).
139. Mohammadi, F. & Rashidzadeh, R. An overview of IoT-enabled monitoring and control
systems for electric vehicles. _IEEE Instrum. Meas. Mag._ **24**, 91–97 (2021).
140. Ghorbanian, M., Dolatabadi, S. H., Masjedi, M. & Siano, P. Communication in smart grids:
a comprehensive review on the existing and future communication and information
infrastructures. _IEEE Syst. J._ **13**, 4001–4014 (2019).
141. Umoren, I. A., Shakir, M. Z. & Tabassum, H. Resource efficient vehicle-to-grid (V2G)
communication systems for electric vehicle enabled microgrids. _IEEE Trans. Intell._
_Transp. Syst._ **22**, 4171–4180 (2020).
142. Rajasekaran, A. S., Azees, M. & Al-Turjman, F. A comprehensive survey on security issues
in vehicle-to-grid networks. _J. Control Decis._ **10**, 150–159 (2023).



143. Zheng, Y., Shao, Z., Lei, X., Shi, Y. & Jian, L. The economic analysis of electric vehicle
aggregators participating in energy and regulation markets considering battery
degradation. _J. Energy Storage_ **45**, 103770 (2022).
144. Wen, Y., Hu, Z., You, S. & Duan, X. Aggregate feasible region of DERs: exact formulation
and approximate models. _IEEE Trans. Smart Grid_ **13**, 4405–4423 (2022).
145. Nimalsiri, N. I. et al. A survey of algorithms for distributed charging control of electric
vehicles in smart grid. _IEEE Trans. Intell. Transp. Syst._ **21**, 4497–4515 (2019).
146. Saner, C. B., Trivedi, A. & Srinivasan, D. A cooperative hierarchical multi-agent system for
EV charging scheduling in presence of multiple charging stations. _IEEE Trans. Smart Grid_
**13**, 2218–2233 (2022).
147. DeForest, N., MacDonald, J. S. & Black, D. R. Day ahead optimization of an electric vehicle
fleet providing ancillary services in the Los Angeles Air Force Base vehicle-to-grid
demonstration. _Appl. Energy_ **210**, 987–1001 (2018).
148. Hajebrahimi, A., Kamwa, I., Abdelaziz, M. M. A. & Moeini, A. Scenario-wise distributionally
robust optimization for collaborative intermittent resources and electric vehicle
aggregator bidding strategy. _IEEE Trans. Power Syst._ **35**, 3706–3718 (2020).
149. Zhou, F., Li, Y., Wang, W. & Pan, C. Integrated energy management of a smart community
with electric vehicle charging using scenario based stochastic model predictive control.
_Energy Build._ **260**, 111916 (2022).
150. Luo, Y. et al. Charging scheduling strategy for different electric vehicles with optimization
for convenience of drivers, performance of transport system and distribution network.
_Energy_ **194**, 116807 (2020).
151. Qiu, D., Wang, Y., Hua, W. & Strbac, G. Reinforcement learning for electric vehicle
applications in power systems: a critical review. _Renew. Sust. Energ. Rev._ **173**, 113052
(2023).
**This review discusses the application of cutting-edge AI technology (reinforcement**
**learning) in PEV integration into power systems** .
152. Uddin, K., Dubarry, M. & Glick, M. B. The viability of vehicle-to-grid operations from
a battery technology and policy perspective. _Energy Policy_ **113**, 342–347 (2018).
153. Briones, A. et al. Vehicle-to-grid (V2G) power flow regulations and building codes review
by the AVTA (US Department of Energy, 2012) _._
154. Pena-Bello, A. et al. Integration of prosumer peer-to-peer trading decisions into energy
community modelling. _Nat. Energy_ **7**, 74–82 (2022).
155. Ting L. Charging stations out of service caused large-scale outage of taxis in Shenzhen.
_Shenzhen News_ [http://news.sznews.com/content/2018-05/22/content_19164366.htm](http://news.sznews.com/content/2018-05/22/content_19164366.htm)
(2018).
156. Cui, Y., Hu, Z. & Duan, X. Optimal pricing of public electric vehicle charging stations
considering operations of coupled transportation and power systems. _IEEE Trans. Smart_
_Grid_ **12**, 3278–3288 (2021).
157. Dinger, A. et al. Batteries for electric cars: challenges, opportunities, and the outlook
to 2020 (Boston Consulting Group, 2010).
158. Xu, L., Deng, Z., Xie, Y., Lin, X. & Hu, X. A novel hybrid physics-based and data-driven
approach for degradation trajectory prediction in Li-ion batteries. _IEEE Trans. Transp._
_Electrif._ **9**, 2628–2644 (2022).
159. Jahangir, H. et al. Plug-in electric vehicle behavior modeling in energy market: a novel
deep learning-based approach with clustering technique. _IEEE Trans. Smart Grid_ **11**,
4738–4748 (2020).
160. Marino, C. A. & Marufuzzaman, M. Unsupervised learning for deploying smart charging
public infrastructure for electric vehicles in sprawling cities. _J. Clean. Prod._ **266**, 121926
(2020).
161. Zhang, H., Sheppard, C. J. R., Lipman, T. E., Zeng, T. & Moura, S. J. Charging infrastructure
demands of shared-use autonomous electric vehicles in urban areas. _Transp. Res. D_
_Transp. Environ._ **78**, 102210 (2020).
162. Fu, T., Wang, C. & Cheng, N. Deep-learning-based joint optimization of renewable energy
storage and routing in vehicular energy network. _IEEE Internet Things J._ **7**, 6229–6241
(2020).
163. Zhang, X. et al. Deep-learning-based probabilistic forecasting of electric vehicle
charging load with a novel queuing model. _IEEE Trans. Cybern._ **51**, 3157–3170
(2020).
164. Henri, G. & Lu, N. A supervised machine learning approach to control energy storage
devices. _IEEE Trans. Smart Grid_ **10**, 5910–5919 (2019).
165. Lopez, K. L., Gagne, C. & Gardner, M. A. Demand-side management using deep
learning for smart charging of electric vehicles. _IEEE Trans. Smart Grid_ **10**, 2683–2691
(2019).
166. Li, H., Wan, Z. & He, H. Constrained EV charging scheduling based on safe deep
reinforcement learning. _IEEE Trans. Smart Grid_ **11**, 2427–2439 (2020).
167. Yan, L., Chen, X., Zhou, J., Chen, Y. & Wen, J. Deep reinforcement learning for continuous
electric vehicles charging control with dynamic user behaviors. _IEEE Trans. Smart Grid_
**12**, 5124–5134 (2021).
168. Lu, Y. et al. Deep reinforcement learning-based charging pricing for autonomous
mobility-on-demand system. _IEEE Trans. Smart Grid_ **13**, 1412–1426 (2022).


**Acknowledgements**
H.Z. discloses support for the research of this work from the National Natural Science
Foundation of China (NSFC) (grant number 52007200). X.H. discloses support for the
research of this work from the NSFC (grant number 52111530194) and the Basic Research
Funds for Central Universities (grant number 2022CDJDX-006). Z.H. discloses support for the
research of this work from the National Key Research and Development Program of China
(grant number 2022YFB2403900).



[Nature Reviews Electrical Engineering | Volume 1 | January](http://www.nature.com/natrevelectreng) 2024 | 35–52 **51**


### **Review article**

**Author contributions**

All authors contributed substantially to discussion of the content. H.Z., X.H. and Z.H.
researched data for the article and wrote the article. S.J.M. reviewed and edited the

manuscript before submission.


**Competing interests**
The authors declare no competing interests.


**Additional information**

**Peer review information** _Nature Reviews Electrical Engineering_ thanks Kai Strunz and the
other, anonymous, reviewer(s) for their contribution to the peer review of this work.


**Publisher’s note** Springer Nature remains neutral with regard to jurisdictional claims in
published maps and institutional affiliations.


Springer Nature or its licensor (e.g. a society or other partner) holds exclusive rights to this
article under a publishing agreement with the author(s) or other rightsholder(s); author
self-archiving of the accepted manuscript version of this article is solely governed by the
terms of such publishing agreement and applicable law.



**Related links**

**Electrify America:** [https://www.electrifyamerica.com/renewable-energy](https://www.electrifyamerica.com/renewable-energy)
**LFP/C1:** [https://www.batteryspace.com/prod-specs/6610.pdf](https://www.batteryspace.com/prod-specs/6610.pdf)
**LFP/C2:** [https://voltaplex.com/lfp-prismatic-battery-31ah-144a-2-5v](https://voltaplex.com/lfp-prismatic-battery-31ah-144a-2-5v)
**LFP/C3:** [https://www.buya123products.com/uploads/vipcase/4686239](https://www.buya123products.com/uploads/vipcase/468623916e3ecc5b8a5f3d20825eb98d.pdf)
[16e3ecc5b8a5f3d20825eb98d.pdf](https://www.buya123products.com/uploads/vipcase/468623916e3ecc5b8a5f3d20825eb98d.pdf)
**LTO/C1:** [https://www.yinlong.energy/yinlong-battery](https://www.yinlong.energy/yinlong-battery)
**LTO/C2:** [https://lithium-titanatebattery.com/product/2-3v-30ah-prismatic-lto-battery-cell](https://lithium-titanatebattery.com/product/2-3v-30ah-prismatic-lto-battery-cell/)
**LTO/C3:** [https://www.alibaba.com/product-detail/LTO-Prismatic-Square-Lithium-Titanate-](https://www.alibaba.com/product-detail/LTO-Prismatic-Square-Lithium-Titanate-Pouch_1600764573476.html?spm=a2700.7735675.0.0.3814sr3esr3era&s=p)
[Pouch_1600764573476.html?spm=a2700.7735675.0.0.3814sr3esr3era&s=p](https://www.alibaba.com/product-detail/LTO-Prismatic-Square-Lithium-Titanate-Pouch_1600764573476.html?spm=a2700.7735675.0.0.3814sr3esr3era&s=p)
**NCA/C:** [https://www.imrbatteries.com/content/panasonic_ncr18650b-2.pdf](https://www.imrbatteries.com/content/panasonic_ncr18650b-2.pdf)
**NMC/C1:** [https://www.batteryspace.com/prod-specs/11514.pdf](https://www.batteryspace.com/prod-specs/11514.pdf)
**NMC/C2:** [https://pushevs.com/product/prismatic-ncm-battery-cells](https://pushevs.com/product/prismatic-ncm-battery-cells/)
**NMC/C3:** [https://www.altertek.com/products/lithium-ion-pouch-cylindrical-cells/](https://www.altertek.com/products/lithium-ion-pouch-cylindrical-cells/a123-li-ion-cells/a123-26ah-lithium-ion-nmc-pouch-cell/)
[a123-li-ion-cells/a123-26ah-lithium-ion-nmc-pouch-cell](https://www.altertek.com/products/lithium-ion-pouch-cylindrical-cells/a123-li-ion-cells/a123-26ah-lithium-ion-nmc-pouch-cell/)
**PJM:** [https://learn.pjm.com/energy-innovations/plug-in-electric](https://learn.pjm.com/energy-innovations/plug-in-electric)


© Springer Nature Limited 2024



[Nature Reviews Electrical Engineering | Volume 1 | January](http://www.nature.com/natrevelectreng) 2024 | 35–52 **52**


