# Citation Key: sinhaDiverseDecarbonizationPathways2024

---

Article https://doi.org/10.1038/s41467-024-52433-z
# Diverse decarbonization pathways under near cost-optimal futures



Received: 28 November 2023


Accepted: 5 September 2024


Check for updates



<PERSON><PERSON><PERSON> 1, <PERSON><PERSON> 2,5, <PERSON> 2, <PERSON> 3,
<PERSON><PERSON> 1, <PERSON> 1,4, <PERSON><PERSON> 2,6 &
<PERSON> 1,6


Energy system optimization models offer insights into energy and emissions
futures through least-cost optimization. However, real-world energy systems
often deviate from deterministic scenarios, necessitating rigorous uncertainty
exploration in macro-energy system modeling. This study uses modeling
techniques to generate diverse near cost-optimal net-zero CO 2 pathways for
the United States’ energy system. Our findings reveal consistent trends across
these pathways, including rapid expansion of solar and wind power generation, substantial petroleum use reductions, near elimination of coal combustion, and increased end-use electrification. We also observe varying
deployment levels for natural gas, hydrogen, direct air capture of CO 2, and
synthetic fuels. Notably, carbon-captured coal and synthetic fuels exhibit high
adoption rates but only in select decarbonization pathways. By analyzing
technology adoption correlations, we uncover interconnected technologies.
These results demonstrate that diverse pathways for decarbonization exist at
comparable system-level costs and provide insights into technology portfolios
that enable near cost-optimal net-zero CO 2 futures.



To limit global temperature rise to below 1.5 °C and mitigate the worst
impacts of climate change, it is imperative to transition to a net-zero
CO 2 emissions energy system by the middle of the century [1] . In netzero systems, the amount of CO 2 emissions released into the atmosphere is balanced by the amount removed through various
mechanisms such as carbon capture and storage, reforestation, or
technological innovations. However, this transformation poses significant challenges and uncertainties in determining the configuration
of the energy system to meet these targets [2][,][3] . Key decisions regarding
capital-intensive and long-term energy system investments must be
made today, with far-reaching consequences for future social, economic, and environmental systems. Promoting energy efficiency,
electrifying end-use technologies, and transitioning to a carbonfree electricity grid are crucial components of this transition [4][–][6] .
Nevertheless, there are unresolved questions surrounding the



implementation of these solutions and coordinating efforts across
different energy sectors.
Energy system optimization models (ESOMs) enable the study of
energy transitions [7] . These models typically rely on least-cost optimization to inform decision-making, with investment and operational
decisions achieving the lowest net present cost. ESOMs can determine
the optimal deployment of resources, considering existing and new
technologies, within a specified time horizon and subject to various
constraints. Model designs can vary based on their sectoral representation and assumptions on technology advancements, policy
measures, or economic factors. ESOMs can provide insight into crucial
decision-making in interlinked systems where analyzing only a single
technology or sector in isolation may be insufficient. These models are
emerging as the standard in studying macro scale energy systems
spanning over multi-decadal time periods [8] . For example, previous



1 Department of Civil and Environmental Engineering, North Carolina State University, Raleigh, NC, USA. 2 Department of Engineering and Public Policy,
Carnegie Mellon University, Pittsburgh, PA, USA. [3] Sutubra Research Inc., Halifax, Nova Scotia, Canada. [4] Department of Decision Sciences, North Carolina
Central University, Durham, NC, USA. [5] Present address: EPRI, Palo Alto, CA, USA. [6] These authors jointly supervised this work: Paulina Jaramillo, Jeremiah X.

Johnson. [e-mail: <EMAIL>](mailto:<EMAIL>)


Nature Communications |  (2024) 15:8165 1


Article https://doi.org/10.1038/s41467-024-52433-z



studies have used capacity expansion models, a type of ESOM, and
explored ranges of fuel and technology options for achieving net-zero
CO 2 emissions in the United States by 2050 [9][–][11], yielding valuable
insights into the need for technological flexibility [10] and identifying key
challenges and opportunities for decarbonization [11][,][12] . However, these
studies have often relied on deterministic simulations of a small

number of scenarios, with limited exploration of uncertainties in input
parameters (parametric) or model architecture (structural) [13][,][14] . As a
result, model projections may fail to anticipate future events [15] . Additionally, the detailed narratives often associated with these scenarios
can introduce cognitive bias in interpreting results [16] .
Recent research has proposed expanding the modeling frameworks
to support feasibility assessment and robust strategy development to
address these limitations [17][,][18] . For example, Lempert and Trujillo suggest
that modelers should seek robust strategies for decarbonization, as this
encourages more expansive thinking over potential futures and an
iterative stress-testing process [19] . More recently, Jewell et al. proposed
developing feasibility spaces through which modelers can add boundaries to the solution spaces (i.e., all possible decarbonization pathways)
based on a multi-dimensional evaluation that accounts for parameters
not included in the ESOMs (e.g., technology acceptability) [17] . Thus,
identifying robust decarbonization pathways for the U.S. energy system
warrants a systematic treatment of the deep uncertainty under which
these models are formulated [20] .

Approaches to address the uncertainty in input parameters
include Monte Carlo analysis, stochastic programming, and robust
optimization. Monte Carlo analysis involves propagating the uncertainty of one or more input parameters, represented by probability
distributions, through the ESOM [21] . Stochastic programming considers
numerous uncertain factors in the future and seeks to offer an optimal
hedging strategy that informs a single best course of action [21][,][22] . However, these methods suffer from high computational burdens and
require reliable probability distributions for model inputs [14][,][22], limiting
their effectiveness in ESOMs, particularly when used alongside capacity expansion problems [22][–][26] . Robust optimization integrates elements
from sensitivity analysis, multi-objective optimization, and stochastic
programming to produce a set of solutions that gradually become less
influenced by the uncertainties associated with input variables. These
solutions remain stable and resilient even when facing modeled
uncertainties [14] . Unlike stochastic programming, robust optimization

                                                   cannot provide a unified hedging strategy yet still requires quantifi
cation of uncertain model parameters. Further, if knowledge of
probability distributions of uncertain inputs is available, this uncertainty can potentially be better captured by other methods [14] .
Structural uncertainties in ESOMs have been shown to lead to

dramatic differences in the cost-optimal pathway and real-world
energy transitions [27] . Modeling to generate alternatives (MGA) has
emerged as a method to mitigate this uncertainty by exploring the
near-optimal region to account for unmodelled considerations [28] . MGA
produces near-cost-optimal solutions that can be maximally different
to allow for more complete consideration of a wide range of alternatives. The solutions from this approach can represent outcomes
beyond cost-optimal technology choices, illustrating the potential
influence of non-monetary factors such as public acceptance, consumer preferences, and equity on decision-making. Further, MGA
alleviates the cognitive biases of the energy modeler and also allows
for the inspection of “knife-edge” effects, where small perturbations in
the input assumptions can lead to drastically different outcomes [14][,][16] .
The solutions from MGA can be assembled into a portfolio of options
and presented to policymakers, giving them insight into making
decisions while keeping in mind the interests of multiple stakeholders.
These options may be able to capture non-monetary factors without
any cognitive biases in a way that deterministic scenario modeling
cannot. The applicability of MGA in the context of energy system
modeling has been previously demonstrated [27][,][29][–][39] .



In applying MGA, we modify the original optimization problem by
converting the objective function into a constraint and allowing system costs to exceed the original optimal value by a specified threshold
or slack. This addition of slack permits the exploration of near-optimal
solutions within the decision space, even if there is a slight increase in
the total system cost. As a result, alternative solutions that capture a
broader range of possibilities beyond traditional least-cost formulations are extracted. MGA can identify correlations and trade-offs
among technology choices, as well as options that are consistently
favored or excluded across multiple pathways. By generating hundreds of pathways that cover a large solution space, this approach
enables stress-testing of different system representations to assess
robustness or the performance of multi-dimensional evaluations to
establish a feasibility space. Given the inevitable presence of structural
uncertainties and unmodeled objectives in ESOMs, near-optimal
model solutions may prove more desirable when factoring in decision-makers’ preferences. Furthermore, MGA results may also be
interpreted as perturbations in the objective function coefficients,
reflecting parametric uncertainty [40] . In this work, we use the Tools for
Energy Model Optimization and Analysis (Temoa), an open-source
energy system optimization model [41][,][42], in conjunction with an opensource database of the U.S. energy system [42] . Temoa represents the
energy system as a process-based network, linking technologies
through the flow of energy commodities. The database incorporates
various exogenous engineering-economic parameters to describe
each process in the network, including capital costs, operations and
maintenance costs, technology lifetimes, conversion efficiencies, and
emissions factors.

In this study, we introduce an application and design of MGA,
applied to a comprehensive U.S. energy system model to assess near
cost-optimal net-zero CO 2 futures. In the context of this study, near-costoptimal net-zero CO 2 futures refer to pathways to achieve net-zero CO 2
emissions by 2050 that are close to the lowest possible system cost.
These pathways allow for consideration of factors that may be desirable
to include but difficult to explicitly model. The model endogenizes
technology adoption, allowing for an extensive exploration of technology choices across diverse decarbonization pathways. By incorporating
explicit descriptions of the transportation, buildings, power, and
industrial sectors, the model accounts for the complex interactions
between the major energy sectors. Furthermore, our work extends
beyond previous studies in that it accounts for path dependencies
resulting from past investments in energy system infrastructure, providing insights into the dynamics of the energy system in later years of
the simulations. These features of the modeling effortenableus tobetter
address the questions: What are the characteristics of a wide range of
near cost-optimal pathways that achieve a net-zero energy system in the
United States? Are there common themes amongst these pathways,
including favored and disfavored technologies, from which we can
extract robust insights? Are there correlated decisions in technology
adoption that may be particularly informative for policy-making?
Through our modeling, we find several consistent trends across the near
cost-optimal pathways, including the rapid expansion of solar and wind
power generation, substantial reductions in petroleum use, near elimination of coal combustion, and increased end-use electrification. We
also observed varying levels of deployment for natural gas, hydrogen,
direct air capture of CO 2, and synthetic fuels, with important correlations in adoption across some technologies.


Results
This section presents the findings from analyzing 1100 near-costoptimal energy system pathways designed to achieve net-zero CO 2
emissions by 2050. These pathways were developed using MGA and
exhibit variations in fossil fuel use, levels of electrification in end-use,
as well as the incorporation of other net-zero enabling technologies,
such as hydrogen production and direct air capture.



Nature Communications |  (2024) 15:8165 2


Article https://doi.org/10.1038/s41467-024-52433-z



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-2-8.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-2-9.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-2-13.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-2-14.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-2-17.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-2-18.png)

Fig. 1 | Fossil fuel use in near cost-optimal net-zero CO 2 pathways. Near costoptimal pathways for coal (a, b), natural gas (c, d), and petroleum (e, f) in net-zero
CO 2 pathways. The box plots (a, c and e) show the distribution of the total energy
use by fossil fuel type across all 1100 net-zero pathways in exajoules (EJ). The
pathway plots in (b, d and f) show the range of fossil fuel use by sector, with the
shading representing each decile of results across the 1100 model runs (darkest


Fossil fuel use in near cost-optimal net-zero pathways
Figure 1 illustrates the range of primary fossil fuel use, specifically coal,
natural gas, and petroleum, within the near cost-optimal net-zero CO 2
pathways along with the least-cost (deterministic) net-zero pathway
for the U.S. energy system. The figure also shows the least-cost
(deterministic) current-policy pathway, which includes the Inflation
Reduction Act (IRA) provisions but excludes any other carbon constraints after the IRA provisions expire. Figure 1a shows that by 2050,
most near cost-optimal pathways result in the near elimination of coal
use by 2050, consistent with previous work [11][,][43][,][44] . Although achieving
net-zero targets while maintaining higher levels of coal use is theoretically possible, nearly 99% of the decarbonization pathways rely on
less than 0.1 exajoule (EJ) of coal in 2050, representing a 98% reduction



shades around the median, lightest shades around the 10th and 90th percentiles).
The solid lines show the deterministic least-cost net-zero pathway, while the
dashed lines depict the least-cost current-policy pathway. Box plots indicate
median (middle line), 25th, 75th percentile (box) and 1.5 times the inter-quartile
range from the first and third quartiles (whiskers) as well as outliers (single points).
Source data are provided as a Source Data file.


from current levels. In 2020, the U.S. electric sector accounted for
approximately 90% of the coal use in the energy system, with the
remainder attributed to the industrial sector [45] . Consequently, Fig. 1a
highlights that the pursuit of net-zero futures necessitates a rapid
reduction in coal use in the electric sector, with the median pathway
achieving a 67% reduction by 2030 compared to 2020. While most
near-cost-optimal net-zero pathways align with the least-cost deterministic pathway, leading to the elimination of coal in the electric
sector by 2040, some pathways extend coal phase-out until 2050. This
extension can be attributed, in part, to the availability of the IRA tax
credits that enable continued electric sector coal use when combined

with carbon capture and sequestration (CCS) technologies. As
observed in the least-cost current-policy pathway, the IRA succeeds in



Nature Communications |  (2024) 15:8165 3


Article https://doi.org/10.1038/s41467-024-52433-z



reducing coal use while the tax credits are active. Still, there is a
rebound to nearly pre-existing levels of coal use once these credits
expire (typically by 2033 for most provisions).
Figure 1c shows that the distribution of natural gas use exhibits
significant variation across the near cost-optimal pathways, with consumption in 2050 ranging from less than 3 EJ to 21 EJ, the latter being
comparable to 2020 levels. This diversity in natural gas use is primarily
driven by the industrial sector, which encompasses various applications, including direct air capture (DAC), hydrogen production
through steam methane reforming (with and without CCS), and
industrial manufacturing and non-manufacturing demands. In the
least-cost current-policy pathway, industrial natural gas consumption
increases steadily through 2050, surpassing the levels seen in any of
the net-zero pathways. The range of industrial natural gas used in the
near cost-optimal net-zero pathways often exceeds the results in the
current-policy case until the last decade of the study period (i.e.,
2040–2050). While natural gas use in the current-policy pathway may
be comparable in magnitude to the decarbonization pathways, the
drivers of such consumption differ. A substantial portion of natural gas
is allocated to DAC or hydrogen production in the decarbonization
pathways. For instance, the least-cost net-zero pathway uses about 8 EJ
of natural gas for DAC in 2050, accounting for almost half of total
natural gas use. In the current-policy pathway, these technologies are
not widely adopted, and natural gas is primarily used for process heat
or conventional boilers in the industrial sector. Within the electric

sector, natural gas use undergoes a rapid reduction, declining from
approximately 9 EJ in 2020 to less than 2.5 EJ by 2030 in over half of the
near cost-optimal pathways. By mid-century, natural gas use in the
electricity sector approaches zero in all modeled net-zero pathways. In
the commercial sector, natural gas use remains relatively constant
until 2035 across the pathways, after which it will decrease to
approximately one-tenth of the current commercial natural gas use by

–
2050 (0.1 0.5 EJ, Interquartile Range, IQR). By contrast, the transition
from natural gas in the residential sector is slower, with levels
remaining relatively constant until 2040 before declining to a median

–
of 0.3 EJ (0 0.9 EJ, IQR). Overall, reductions in natural gas use across all
decarbonization pathways are a consequence of the increased electrification of end-use technologies in all sectors. However, despite
substantial reductions, all decarbonization pathways retain some level
of natural gas use. Compared to coal and petroleum, natural gas is a
lower-carbon alternative, particularly for challenging-to-decarbonize
processes in the industrial sector. It is important to acknowledge that
factors not considered in Temoa, such as labor impacts and energy
security perceptions, will likely influence natural gas use during a lowcarbon transition.

Primary energy use from petroleum products, shown in Fig. 1e,

−
consistently declines from 31 EJ in 2020 to 3.6 EJ (3.1 4.1, IQR) in 2050
across the decarbonization pathways. The transportation sector
exhibits a steady decrease within a relatively narrow range of outcomes. By 2050, all pathways use more than 1.5 EJ of petroleum for
transportation but less than 4.8 EJ, with the deterministic least-cost
net-zero pathway falling on the higher end of this range. By contrast,
petroleum use in the transportation sector will reach 10 EJ by 2050 in
the least-cost current policy pathway (i.e., without a net-zero constraint). The adoption of electric vehicles (EVs), synthetic liquids, and
hydrogen (discussed below) drives the reduction in petroleum use in
the net-zero pathways. In the absence of additional decarbonization
policies, substantial petroleum use will continue through 2050.


Net-zero pathways exhibit increased end-use electrification
Figure 2a illustrates that total electricity consumption within the near
cost-optimal pathways consistently increases, reaching a median total

−
use of 9400 terawatt-hours, TWh (9200 9500 TWh, IQR) by 2050.
This growth is a three-fold increase in electricity use compared to
current levels. While the range of total electricity consumption varies,



all decarbonization pathways necessitate substantial and rapid
investments in clean electric generation capacity to fulfill the needs of
end-use sectors. Supplementary Fig. 12 disaggregates the electricity
consumption by sector. Electricity demand in the least-cost net-zero
pathway reaches 9200 TWh in 2050, placing it at the lower end of the
distribution of all the near cost-optimal pathways. Although some
pathways exhibit lower electricity consumption than the least-cost
option, most pathways lean toward higher relative electricity use by
2050. By contrast, the least-cost current-policy pathway remains
similar to net-zero trajectories until 2035 but diverges from the netzero pathways after that year. Most provisions of the IRA expire by
2033. Without additional policy interventions after the IRA expires,
electricity consumption would experience only modest increases to
meet population and economic growth.
Figure 2b–f offer insights into the evolving generation sources
within the power sector, and Supplementary Fig. 10 shows the capacities. Fig. 2b shows that even the most conservative decarbonization
pathways require a ten-fold increase in solar generation by 2050,
compared to current levels. This level of solar, totaling 3700 TWh

–
(3600 4100 TWh, IQR), nearly matches the total power system generation from all sources in 2020. This trend highlights the magnitude
of transformation required for deep decarbonization and emphasizes
the pivotal role of solar power in a decarbonized power system. Across
the modeled pathways, the greatest relative increase in solar generation occurs between 2025 and 2030, with a three-fold increase spurred
by the federal Production and Investment Tax Credits (PTC and ITC)
available through the IRA. Figure 2c shows that generation from wind
also experiences substantial growth, reaching a median of 6700 TWh

–
(6100 7400 TWh, IQR) in the net-zero pathways. Most of the wind
generation and capacity comes from onshore resources, with a smaller
contribution from offshore resources that produce a median 100 TWh

–
(80 110 TWh, IQR) by 2050. Much like solar, the PTC and ITC incentives drive a two-fold or more increase in wind generation between
2025 and 2030. The rapid expansion of wind and solar power highlights the need for substantial and sustained investments in integrating
these variable resources to achieve net-zero CO 2 emissions effectively.
The growth of wind and solar in the net-zero pathways is complemented by the expansion of battery storage, with installed battery

–
capacity projected to reach 380 gigawatts, GW (370 400 GW, IQR) by
2050, compared to less than 1 GW in 2020 (Fig. 2f). Further, Supplementary Fig. 11 shows that 94% of power generation in the median

−
pathway comes from renewables by 2050 (93 95%, IQR), consistent
with previous research [43] . Furthermore, variable renewable sources
constitute an increasing share of all renewable generation, reaching
96% by 2050.
Notably, no new nuclear infrastructure is brought online across
the decarbonization pathways. However, existing nuclear capacity
remains available across all the near cost-optimal decarbonization
pathways, contributing between 210 and 2000 TWh of generation and
providing approximately 100 GW of capacity in 2050 (Fig. 2d and
Supplementary Fig. 10). By contrast, most existing nuclear infrastructure retires by 2035 in the least-cost current policy pathway. This
pattern suggests that while nuclear generation is an important component of a net-zero power sector capable of providing firm power, its
continued use without an emissions constraint is not economical after
the expiration of the IRA tax credits [46] . When considering both
renewable energy and nuclear power, the median contribution to

–
power generation is 98.1% (97.9 98.5%, IQR) by 2050, underscoring
the importance of a low-carbon power system (Supplementary Fig. 11).
Figure 2e shows that electricity generation from natural gas
decreases from present levels (~1300 TWh) to nearly zero for most
near cost-optimal decarbonization pathways by 2050. This decrease is
not strictly monotonic, as a temporary increase in natural gas electricity generation occurs between 2030 and 2035 when federal IRA tax
credits expire. The rebound in natural gas consumption for power



Nature Communications |  (2024) 15:8165 4


Article https://doi.org/10.1038/s41467-024-52433-z



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-4-4.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-4-8.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-4-13.png)

Fig. 2 | Power sector characteristics in near cost-optimal net-zero CO 2 pathways. Near cost-optimal pathways in the power sector where box plots show (a)
total electricity use across the entire energy system, (b) electricity generation from
solar, (c) electricity generation from wind, (d) electricity generation from nuclear
and (e) electricity generation from natural gas all in terawatt-hours (TWh). f shows
the battery capacity in gigawatts (GW) deployed in the near cost-optimal


generation is most prominent in the least-cost current-policy scenario,
exceeding 1000 TWh in 2050. Consequently, additional policy measures beyond the IRA will be essential to fully decarbonize the power

sector.

Substantial investments in electricity transmission capacity will be
necessary to support the high levels of electrification in the net-zero
pathways. Based on the regional representation of the U.S. energy
system in Temoa (Supplementary Fig. 1), the results suggest the need

–
for 47 GW (46 49 GW, IQR) of new inter-regional transmission lines
between California and the Southwest by 2050. Substantial transmission expansion also occurs between the Central and North-Central

–
regions, totaling 50 GW (45 57 GW, IQR), with the highest pathway
reaching 106 GW. Other pathways also indicate the need for



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-4-5.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-4-9.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-4-14.png)

decarbonization pathways. The solid lines show the deterministic least-cost netzero pathway, while the dashed lines depict the least-cost current-policy. Box plots
indicate median (middle line), 25 [th], 75 [th] percentile (box) and 1.5 times the interquartile range from the first and third quartiles (whiskers) as well as outliers (single
points). Source data are provided as a Source Data file.


transmission expansion in various regions, such as between California
and the Northwest and between the Southwest and the Northwest.

New transmission into and out of the Northeast, Mid-Atlantic, Southeast, and Texas is comparatively small. The consistent deployment of
this transmission capacity in the near cost-optimal pathways highlights
the benefits derived from inter-regional electricity transfer in these
regions.


Hydrogen consistently meets hard-to-decarbonize demands
Hydrogen has the potential to play an important role in decarbonization efforts, particularly in the industrial and transportation
sectors [47][,][48] . Consistent with other studies that identify hydrogen as a
key component of a net-zero future, Fig. 3a shows a median



Nature Communications |  (2024) 15:8165 5


Article https://doi.org/10.1038/s41467-024-52433-z



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-5-3.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-5-7.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-5-12.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-5-15.png)

Fig. 3 | Hydrogen production and consumption in near-cost optimal net-zero
CO 2 pathways. The box plots in panels (a–d) show hydrogen production from
1100 near cost-optimal net-zero pathways: (a) total hydrogen production,
b hydrogen from electrolysis, c hydrogen from bioenergy with carbon capture and
storage (BECCS), and (d) hydrogen from natural gas steam methane reforming
with carbon capture and storage (SMR with CCS), all in exajoules (EJ). The box plots
in panels (e–h) show where hydrogen is used across the energy system in the near


production of 8.0 EJ (5.6–10.5 EJ, IQR) by 2050 [43] . While the least-cost
net-zero pathway shows 3.5 EJ of hydrogen production in 2050, this
value is at the lower end of a broad distribution of hydrogen production across the near cost-optimal net-zero pathways.
Figure 3b–d show that the primary mechanisms for hydrogen
production up until 2040 are from natural gas steam-methane
reforming and bioenergy with carbon capture and storage (BECCS).
In 4% of the near cost-optimal pathways, steam methane reforming
with CCS produces at least 1 EJ of hydrogen, reaching up to 3 EJ in
pathways with the highest hydrogen production via this method. In
2045 and 2050, electrolysis emerges as a cost-competitive alternative,
supplanting hydrogen production from natural gas steam-methane



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-5-4.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-5-6.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-5-8.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-5-11.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-5-16.png)

cost-optimal net-zero pathways: (e) transportation, (f) buildings, (g) industrial, and
(h) electric sectors. The solid lines show the deterministic least-cost net-zero

pathways, while the dashed lines in each panel represent the deterministic currentpolicy pathways. Box plots indicate median (middle line), 25 [th], 75 [th] percentile (box)
and 1.5 times the inter-quartile range from the first and third quartiles (whiskers) as
well as outliers (single points). Source data are provided as a Source Data file.


reforming. The IRA tax credit, Internal Revenue Code section 45 V,
incentivizes green hydrogen production with a subsidy of up to $3/kg
of H 2 . In the database used for this analysis, both electrolysis from new
renewables and BECCS qualify for the full credit, while steam methane
reforming with CCS qualifies for a $1/kg of H 2 credit. These incentives
result in an increase in total hydrogen production from 2030 onwards.
While hydrogen proves to be an attractive energy carrier, the chosen
pathway to production is sensitive to emissions and cost assumptions.
For example, a sensitivity test on the exogenous fuel prices indicates
that high fuel prices would result in considerably more hydrogen
production via steam methane reforming in the early years of the
modeling horizon (Supplementary Note 3). The carbon dioxide



Nature Communications |  (2024) 15:8165 6


Article https://doi.org/10.1038/s41467-024-52433-z



removal benefits from BECCS also prove to be an attractive way to take
advantage of the federal tax credits and meet CO 2 constraints in
the model.

Figure 3e–h illustrate that hydrogen is primarily used in the
transportation sector, followed by industrial applications. In the

–
transportation sector, 1.5 EJ (1.2 1.7 EJ, IQR) of hydrogen are used in
2050 for fueling fuel-cell vehicles, particularly heavy-duty vehicles.

–
Additionally, 2.1 EJ (0.4 4.0 EJ, IQR) of hydrogen is used for synthetic
fuel synthesis, serving several transportation demands. In the industrial sector, the primary role of hydrogen is to replace conventional
boilers and meet the demand for process heat. Hydrogen use for
industrial processes converges around 1.0 EJ in 2050 across all net-zero
pathways. Hydrogen is used for synthetic natural gas production for
heating in the residential and commercial sectors only in the final

−
decade, with a median 2050 hydrogen use of 1.2 EJ (0.9 1.4 EJ, IQR).
While hydrogen is a viable option for electricity generation in combined cycle power plants, it was seldom chosen in the near costoptimal net-zero pathways. However, in a subset of these pathways,
substantial amounts of hydrogen are used for electricity generation in
2025. The presence of two tax incentives in the IRA, one for hydrogen
production and the other for clean electricity generation facilitates this
choice. As the IRA provisions expire, the use of hydrogen-enabled
power plants diminishes in future time periods, but the capacity
remains to meet power sector reserve margins.


CO 2 removal and management span a range of deployment
As detailed above, all of the net-zero CO 2 emissions scenarios retain
some fossil fuel use across in 2050. The resulting residual CO 2 emissions are primarily from hard-to-decarbonize sectors like aviation or
high-temperature processes in the manufacturing sector. Given these
residual emissions, carbon management, and particularly carbon
dioxide removal (CDR), is likely to play a pivotal role in enabling netzero futures. Figure 4 shows the carbon management technologies
represented in the net-zero pathways, including bioenergy with carbon
capture and storage (BECCS), coal and natural gas electricity generation with carbon capture and storage (CCS), and direct air capture
(DAC). The near cost-optimal pathways exhibit a wide range of
potential deployment levels for these technologies, with some pathways more heavily reliant on carbon mitigation options.
Figure 4a shows that most near cost-optimal pathways have a
notable reliance on BECCS. BECCS can be an attractive tool in dec
arbonization efforts, as it couples the production of energy carriers
(electricity or hydrogen) that can then meet service demands across
the energy system with carbon dioxide removal. While the contribution to electricity generation from BECCS remains minimal in the near
cost-optimal pathways, many pathways incorporate hydrogen production (discussed above). Coal power with CCS and natural gas steam
methane reforming with CCS are not deployed in the least-cost current-policy or the least-cost net-zero pathways. By contrast, Figs. 4b, c
depict that these technologies are extensively deployed in a small
subset of near-cost-optimal net-zero pathways. Overall, total CCS,
calculated as the sum of BECCS, coal CCS, and natural gas CCS,
amounts to 690 Million tons of CO 2 /year (Mt CO 2 /yr) (250–1030 Mt
CO 2 /yr, IQR) in 2050 in these pathways (Fig. 4d).
Figure 4e shows that the median DAC deployment in 2030 (when
the technology first becomes available in the model) is 0.45 gigatons of
CO 2 /year (Gt CO 2 /yr) (0.10–1.18 Gt CO 2 /yr, IQR). Currently, DAC is a
nascent technology and has not been deployed on a large scale.
However, the IRA tax credit (Internal Revenue Code section 45Q) can
incentivize the adoption of DAC. Even in the least-cost current-policy
scenario (i.e., without a net-zero requirement), DAC is employed to
take advantage of the available tax credits. Consequently, the model
builds and uses DAC capacity while these tax credits remain in effect
until 2033. The tax credits catalyze capital investments, and the
infrastructure continues to be used beyond the expiration of the



credits. In 2050, DAC use is expanded in the net-zero pathways to
compensate for residual CO 2 emissions from hard to decarbonize
processes [49], reaching 1.22 Gt CO 2 /yr (0.97−1.50 Gt CO 2 /yr, IQR). This
wide range of outcomes suggests that DAC deployment is sensitive to
cost shifts and incentives, at times serving as a backstop in achieving
net-zero targets in response to changes in the rest of the energy

system.
Figure 4f displays the range of total geologic sequestration,
spanning 0.77 to 1.86 Gt CO 2 /yr in 2050, with a median result of 1.70 Gt
CO 2 /yr. The least-cost net-zero pathway prioritizes the extensive
sequestration of CO 2 rather than using it for synthetic fuel
production [50] . However, near cost-optimal pathways indicate that netzero futures are possible with lower levels of geologic sequestration
of CO 2 .


Near cost-optimal pathways may differ greatly from costoptimal
To understand the characteristics of near cost-optimal decarbonization
pathways in more detail, we used clustering approaches to identify
“illustrative” pathways that represent groups of decarbonization pathways. These illustrative pathways offer insights into key differences
between the least-cost solution and solutions obtained with a 1% slack

on the total system cost. As described in Supplementary Method 2, the
pathways are identified using k-means clustering on the near costoptimal decarbonization pathways. Figure 5 shows results for six illustrative pathways that differ in the deployment of hydrogen, DAC, and
energy system-wide electricity use. These groups were chosen as they
represent important levers in decarbonization efforts [51][–][53] .
Figure 5a presents carbon dioxide emissions for the chosen
illustrative pathways. While the timing and magnitude of mitigation
measures vary across the selected pathways, there are some observable common trends. The CO 2 constraints in this study apply a linear
reduction to net-zero emissions by 2050. However, these limits are not
binding in 2030 due to the decarbonization impacts of the IRA. Further, the power sector is generally the first to decarbonize, consistent
with other studies [53] . The pathways representing low hydrogen and
high electricity (Low H 2 and High Elec in Fig. 5) deploy coal power but
mitigate these emissions with CCS. There are also commonalities in the
primary energy consumption of illustrative pathways shown in Fig. 5b.
Increased deployments of solar, wind, and biomass accompanied by
reduced or eliminated coal and petroleum use are ubiquitous. All cases
greatly reduce or eliminate power sector, transportation, and building
emissions but must contend with residual CO 2 emissions from hard-todecarbonize industrial processes and upstream fuel emissions.
Carbon management is required in all illustrative pathways, but
there is heterogeneity in the technologies chosen for this purpose. For
example, the low hydrogen and high electricity pathways rely on carbon management from several technologies. In contrast, other pathways, such as the ones representing high hydrogen or DAC use, rely
heavily on one carbon management option. The carbon removal in the
high DAC case allows for higher emissions across the energy sector,
resulting in higher petroleum consumption and lower biomass use
compared to the low DAC pathway. More carbon management is
required in the low electricity pathway, with increased DAC use driving
higher natural gas consumption compared to the high electricity
pathway. Hydrogen can play an important role as a low-carbon energy
carrier. Its absence in the low hydrogen pathway results in higher
transportation and industrial emissions. The pathway with high
hydrogen use shows increased biomass consumption compared to the
low hydrogen case, indicative of the hydrogen production process
via BECCS.


Tradeoffs and synergies in decarbonization technologies
Different energy sources and end-use technologies tend to be used
more frequently alongside or in the absence of other options. Figure 6



Nature Communications |  (2024) 15:8165 7


Article https://doi.org/10.1038/s41467-024-52433-z



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-7-3.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-7-7.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-7-11.png)

Fig. 4 | Carbon management technologies in near cost-optimal net-zero CO 2
pathways. The box plots show the deployment of (a) bio-energy with carbon
capture and storage (BECCS), b carbon capture and storage from coal plants (Coal
with CCS), c carbon capture and storage from natural gassteam methane reforming
(natural gas SMR with CCS), d total carbon capture and storage (CCS) as the sum of
BECCS, coal CCS, and natural gas CCS, e direct air capture, and (f) total geologic
sequestration in million tons of CO 2 /year (Mt CO 2 /year) across 1100 near cost

explores the relationships between select technology deployments
across the near cost-optimal decarbonization pathways in 2050,
showing correlations among energy sources and carriers (Fig. 6a),
among end-use technologies (Fig. 6b), and between these two groups
(Fig. 6c). A positive correlation indicates that the technologies are
more often deployed together, while a negative correlation suggests
potential competition between technologies. The results only show
the strength of the correlation. Two technologies may have a positive
correlation even if both have low deployment levels or overall use is
decreasing.



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-7-4.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-7-8.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-7-12.png)

optimal pathways to achieve net-zero CO 2 by 2050. The solid lines show the
deterministic least-cost net-zero pathways, while the dashed lines in each panel
represent the deterministic current-policy pathways. Box plots indicate median
(middle line), 25 [th], 75 [th] percentile (box) and 1.5 times the inter-quartile range from
the first and third quartiles (whiskers) as well as outliers (single points). Source data
are provided as a Source Data file.


In Fig. 6a, solar generation, wind generation, and battery capacity
demonstrate a notable positive correlation. This association stems
from the critical role batteries play in ensuring reliability as the
adoption of variable renewable energy sources like solar and wind
increases. Hydrogen is produced extensively via electrolysis in the near
cost-optimal net-zero pathways in 2050. This pattern drives the positive correlations between hydrogen production with wind generation,
solar generation, and battery capacity, as additional renewable generation provides a carbon-free energy source for hydrogen electrolyzers. In relation to fossil fuels, increased hydrogen use leads to



Nature Communications |  (2024) 15:8165 8


Article https://doi.org/10.1038/s41467-024-52433-z



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-8-46.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-8-62.png)

Fig. 5 | Illustrative near cost-optimal net-zero CO 2 pathways. a CO 2 emissions in
million tons, and (b) primary energy consumption from 2020 to 2050 in exajoules
(EJ) for the deterministic net-zero and illustrative near cost-optimal pathways with
low/high hydrogen production (Low/High H 2 ), low/high direct air capture use
(Low/High DAC), and low/high overall electricity use (Low/High Elec).


reduced coal consumption but has a less dramatic impact on natural
gas consumption (Supplementary Note 4). While hydrogen commonly
replaces natural gas in the industrial sector, this negative relationship
is dampened due to pathways in which hydrogen is produced via
steam methane reforming. Further, Fig. 6a shows that hydrogen is
most negatively correlated with petroleum due to fuel switching in the
transportation sector, particularly for heavy-duty vehicles. Additionally, biomass exhibits a positive correlation with hydrogen, given the
prevalence of BECCS for hydrogen production. The positive correlation between biomass and synthetic liquids arises from the link
between hydrogen and synthetic liquids (the latter uses the former for
synthesis via the Fischer-Tropsch process). High synthetic fuel use



BECCS–Electricity refers to bioenergy with carbon capture and storage to produce
electricity. BECCS–Hydrogen refers to bioenergy with carbon capture and storage
to produce hydrogen. NG SMR (CCS) refers to natural gas steam methane
reforming with carbon capture and sequestration. Source data are provided as a
Source Data file.


narrows the range of outcomes in the power sector, most notably
eliminating coal CCS plants (Supplementary Note 4).
When considering end-use technologies, EVs compete with
hydrogen and internal combustion vehicles to meet transportation
demand (Fig. 6b). Competition also exists between heat pumps and
natural gas heaters in the buildings sector, as well as electric and
hydrogen boilers in the industrial sector. Scenarios with more internal
combustion engine (ICE) vehicle use are positively correlated with
more natural gas heating of buildings. The persistence of these technologies is accompanied by increased DAC use, allowing net-zero
emissions targets to be met. In pathways with more deployment of EVs
or heat pumps, less DAC use is required. Moreover, electric industrial



Nature Communications |  (2024) 15:8165 9


Article https://doi.org/10.1038/s41467-024-52433-z



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-9-21.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-9-53.png)

Fig. 6 | Correlations across key energy carriers and technologies across near
cost-optimal net-zero CO 2 pathways. A correlation plot showing a subset of key
(a) energy sources and carriers, (b) end-use technologies along with DAC, and (c)
end-use technologies versus energy sources and carriers across the 1100 near costoptimal pathways in 2050. Blue represents positive correlations; red represents
negative correlations. Battery–battery capacity, Solar–solar generation,
Wind–wind generation, Hydrogen–total hydrogen use, Synthetic
Liquids–production via the Fischer-Tropsch process, Petroleum–primary energy


boilers and heat pumps exhibit a positive correlation with EVs, indicating a trend where the electrification of various end-uses is interconnected. This counters the notion of exclusion or competition
among different electrification methods for end-uses.
Figure 6c illustrates the correlation between end-use technologies
and energy sources/carriers. Within the transportation sector, EV
adoption shows a clear connection to increased renewable electricity
generation, displaying positive correlations with wind and solar energy



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-9-22.png)

petroleum use, Natural gas–primary energy natural gas use, Biomass–primary
energy biomass use, EVs–electric vehicle use, H 2 vehicles–hydrogen used in
transportation, ICE vehicles–internal combustion vehicle use, Heat pumps–heat
pump use across the residential and commercial sectors, Natural gas
heating–natural gas-based heating use in the residential and commercial sectors,
Hydrogen boilers– hydrogen boiler use in the industrial sector, Electric
boilers–electric boiler use in the industrial sector, DAC–direct air capture deployment. Source data are provided as a Source Data file.


sources. Conversely, EV adoption exhibits negative relationships with
petroleum, synthetic fuels, natural gas, and biomass utilization. The
integration of hydrogen vehicles also contributes to increased demand
for renewable electricity and synthetic liquids but tends to reduce
petroleum use. There is a weak reliance on natural gas in net-zero
pathways where more ICE vehicles persist. A weak negative correlation
also exists between hydrogen and DAC, shaped by two competing
patterns. When low- or zero-carbon hydrogen is introduced as an



Nature Communications |  (2024) 15:8165 10


Article https://doi.org/10.1038/s41467-024-52433-z



alternative energy carrier, there is a reduced dependency on DAC.
However, when hydrogen facilitates the production of synthetic liquid
fuels, DAC becomes necessary to capture resulting CO 2 emissions.
Figure 6c also shows competition between biomass and DAC, which
Supplementary Note 4 shows is driven by BECCS.


Discussion

Deterministic energy system optimization modeling can identify leastcost decarbonization pathways, but input assumptions, model representation, and scenario selection constrain insights. Parametric
uncertainty methods quantify the impact of uncertain parameters on
model outputs. However, diverse near cost-optimal solutions from an
ESOM can provide additional insights that cannot be obtained from
deterministic modeling alone. Applying MGA to energy systems
models provides distinct and diverse alternative pathways that can be
missed by parametric assessment methods. In this study, we applied
MGA to assess decarbonization pathways for the U.S. energy system,
considering path dependencies from early decision-making and the
interactions between different sectors of the energy system. Our
analysis reveals distinct categories of decarbonization options: those
with consistent adoption across pathways, those experiencing universal decline or elimination, technologies with broad outcome distributions, and options highly adopted in only a few pathways. This
categorization enhances our understanding of technology dynamics
and decarbonization trends.

In the near cost-optimal decarbonization pathways examined in
this paper, there is a notable trend toward widespread adoption of
specific technologies such as solar and wind power, grid-connected
energy storage, and electrification across various sectors, including
industry, transportation, residential, and commercial sectors. These
options are consistently chosen for achieving net-zero CO 2 outcomes.
Our pathways also consistently show that eliminating coal power
without CCS and substantial reductions in petroleum use are required
for near cost-optimal decarbonization. Initiating planning now for a
just and orderly transition away from these industries, considering the
needs of affected communities, is imperative [54] .
A broad range of outcomes is possible for emerging energy
technologies that could enable decarbonization, such as hydrogen,
DAC, CCS, and synthetic fuels. Currently, these options are either
minimally deployed or not commercially available. In the near costoptimal decarbonization pathways explored in this work, their
deployment varies from trivial to pivotal, posing challenges for longterm planning. To enable their scaled deployment by mid-century,
continued research and development, supportive policies, and early
deployment requirements are needed. Widespread deployment of
carbon management technologies must overcome many challenges,
including biomass resource availability, CO 2 storage costs, and the
development of supporting infrastructure such as CO 2 pipelines [55] .
Furthermore, the availability of these novel technologies will have
substantial implications for natural gas consumption in a decarbonized energy system. Natural gas consumption could remain near
current levels in pathways where carbon management technologies
are widely deployed. By contrast, pathways in which carbon management technologies are more restricted, natural gas consumption is
nearly zero by 2050. The chosen pathway has significant implications
for gas system infrastructure, including pipeline utilization, maintenance, and potential expansion or decommissioning. Planning must
consider these factors to ensure a successful transition towards

decarbonization.

The results indicate several technologies that exhibit a pattern of
limited adoption across most decarbonization pathways but experience significant deployment in a small number of pathways. These
technologies include new coal with CCS, natural gas SMR with CCS,
and synthetic fuels. These technologies serve as potential insurance
policies, ensuring that decarbonization goals can still be achieved in



scenarios where renewable energy technologies face deployment
challenges or substantial cost increases. However, the construction of
such facilities requires substantial investment and commitment due to
their size and complexity. Therefore, careful consideration is necessary when pursuing these options, weighing their potential benefits as
fallback solutions against the overall decarbonization objectives.
The optimization framework used in this paper can help identify
the solution space for energy system decarbonization. This solution
space contains a diverse array of technically plausible energy pathways. However, technical plausibility is not the same as feasibility. The
feasibility of the pathways in the solution space depends on attributes
not well-represented in a least-cost optimization framework [56] . For
example, consumer behavior and preferences could limit the transition to electric vehicles. [44] . In high-penetration renewable systems,
tackling the rate of infrastructure buildout, such as the land requirements for wind and solar farms, can be challenging [57] . Furthermore, all
the decarbonization pathways in the solution space would require
large investments in supporting infrastructure like hydrogen and CO 2
pipelines, new transmission lines, and EV charging infrastructure.
While the modeling to generate alternatives framework used in this
paper can provide valuable insights into the decarbonization solution
space, additional analytical tools will be required to identify feasible
decarbonization options. Such feasibility analysis should consider
material and natural resources constraints, labor implications, supply
chain vulnerabilities, climate resilience, environmental justice, waste
generation, and energy equity. Additionally, the range of plausible
solutions and feasible space is likely to differ for different regions
based on local resource availability, behavioral preferences, existing
infrastructure and policy environments, among other factors.
Current and future U.S. policy will significantly shape the energy
transition towards a net-zero CO 2 system. The debate over the optimal
policy mechanisms to achieve this transition has been ongoing for
decades. Existing policies have made progress towards decarbonization. However, our results suggest that reaching net-zero GHG emissions in the U.S. energy sector will require additional policy
interventions after 2033, when key provisions of the IRA expire. Our
results also highlight a large and diverse solution space for energy
system decarbonization, in which technology deployment levels can
vary widely. The results also indicate that deploying some technologies would lock in the need for synergistic technologies and push out
the deployment of others. For example, expanding infrastructure for
synthetic liquid fuels would, to a certain extent, reduce the need for
EVs but would require DAC. Similarly, deploying EVs would likely be
coupled with electrifying other end-uses in buildings and industry.
While it is likely prudent to avoid locking out specific technologies,
decisions made over the next decade will narrow the solution space
and technology sets available to reach net-zero by 2050. Decision
makers should thus be aware of potential path dependencies to avoid
unintended consequences or unexpected outcomes as the United
States moves toward a sustainable and decarbonized energy system.


Methods
Tools for energy model optimization and analysis (Temoa)
We use the Tools for Energy Model Optimization and Analysis
(Temoa), an open-source technology-rich energy systems model.
Temoa is structured as a linear problem that can generate the leastcost pathway for energy system development over a user-specified
time horizon, subject to system- and user-defined constraints. Temoa
represents the energy system as a network of interconnected technologies and commodities. This allows for technology-rich representations of the major sectors of the energy system, which are
interlinked through a network. For example, there is competition for
energy carriers such as electricity to meet different energy service
demands across and within the different sectors. Supplementary
Method 1 details the objective function, which calculates the present



Nature Communications |  (2024) 15:8165 11


Article https://doi.org/10.1038/s41467-024-52433-z


Table 1 | Summary of database assumptions to represent the U.S. energy system



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-11-0.png)



















value of the cost of energy supply, considering financed capital costs,
fixed costs, and variable costs. A demand constraint drives the model
and ensures supply is met in every time interval. In Temoa, electricity
demand is determined endogenously based on the requirements of
the rest of the energy system, for which service demands are specified
exogenously. A commodity balance constraint ensures that intermediate system commodity demands are met. A capacity constraint

                                                   ensures that the capacity of a given process within the model is suffi
cient to support its activity. Installed capacities, associated activities,
fuel shares, supply, and end-use technologies are all decision variables
optimized in Temoa. Technology choice is based on several technoeconomic criteria and endogenized within the model. The operational
characteristics, costs, and lifetimes all influence which technologies
are chosen. The model has perfect foresight across the time horizon,
enabling decision-making with the knowledge of future developments
like carbon emission targets or changes in fuel price. The optimization
of technology choice in end-use technologies is one of the key features
of Temoa, similar to the MARKAL TIMES model [58][,][59] . Finally, several
physical and operational constraints like ramping considerations,
energy storage charging and discharging rates, and reserve margin
requirements are imposed on the linear problem. Supplementary
Method 1 contains a description of the major constraints in the model
while the complete algebraic formulation of Temoa is presented
elsewhere [41] . The model source code [42], and datasets [60] are available on
GitHub, with a commitment to full transparency to allow for easy
replication of our analyses.



In the version of Temoa used in this work, the model balances
energy commodity flows across a set of ordered time slices, which can
represent different combinations of seasons and times of day to
represent seasonal and diurnal variations in energy supply and
demand. While it would be preferable to model the variation in electricity supply and demand for all 8760 h of the year, doing so would
impose heavy computational constraints in running the model and
would not allow us to deploy the MGA approach. Here, we use highly
aggregated (12) time slices to allow for the additional computational
burden imposed. Supplementary Note 1 discusses using this temporal
resolution instead of representative days.


Input database description
For this analysis, we use a nine-region database of the U.S. energy
system developed as part of the Open Energy Outlook Initiative [60] .
Table 1 summarizes the information included in this database with

more detail in Supplementary Method 3. Supplementary Method 4
also describes the IRA provisions included in this analysis. Supplementary Note 3 contains an assessment of parametric uncertainty on
key uncertain input parameters of the database.


Details on MGA Formulation
Supplementary Fig. 1 shows a flow diagram laying out the main inputs
detailed above, and the major outputs analyzed in this work. A deterministic least-cost solution is first obtained by minimizing the total
system cost subject to physical, operation, and network constraints as



Nature Communications |  (2024) 15:8165 12


Article https://doi.org/10.1038/s41467-024-52433-z



described above and Supplementary Method 1. The net-zero scenario
has an additional emissions constraint driving CO 2 to zero by 2050.
Exogenously specified end-use demands drive the model which makes
technology investment decisions such that the result is the lowest
present value of the total system cost. In the case of MGA, the structure
of the deterministic optimization problem changes by altering the
model’s original objective function and introducing a new constraint.
This constraint allows the model to exceed the original objective
function value (i.e., minimized total system cost) by a user-specified
threshold or slack. The addition of this slack allows for exploring nearoptimal solutions in the decision space by accepting a small increase in
the total system cost relative to the optimal solution. Additionally, the
objective function in MGA runs is reformulated to emphasize a search
direction. In this work, the objective function minimizes the sum of
weighted activity (or flow) of technologies across the time horizon in
the model, i.e. each technology is represented by a cumulative activity
across the model time horizon. The technology representation in the
current work is diverse and all technologies are chosen agnostically to
be a part of the objective function to influence the search direction.

−
Weights were sampled from a uniform distribution [ 1, 1], assigned
independently for each activity, which allows for the development of
larger solution diversity with fewer MGA runs.The activity variables are
chosen instead of their capacity counterparts as they directly represent each technology’s contribution towards meeting end-use
demands. This formulation artificially incentivizes/de-incentivizes the
activity of technologies based on the sign and magnitude of the
coefficient. For example, larger weights put the associated decision
variable at a relative disadvantage, allowing other technologies to
enter the solution [30] . This process is repeated 1100 times, with each
iteration including an updated set of objective function coefficients

                                                   from the uniform distribution. Supplementary Note 2 contains justifi
cation for the chosen number of MGA iterations. In this way, MGA can
explore the decision space to find alternative solutions that are very
different in decision space but have a total cost close to the original
solution and are bound by the user-specified slack value. Equation (1)
summarized the MGA formulation.



Min X



w i x i

i



i ð1Þ


        s:t: f xð Þ ≤ f xð Þ × ð1 + δÞ



2. Meckling, J., Sterner, T. & Wagner, G. Policy sequencing toward
decarbonization. Nat. Energy 2, 918–922 (2017).
3. Breetz, H., Mildenberger, M. & Stokes, L. The political logics of clean
energy transitions. Bus. Polit. 20, 492–522 (2018).
4. Williams, J. H. et al. The technology path to deep greenhouse gas
emissions cuts by 2050: the pivotal role of electricity. Science 335,
53–59 (2012).
5. Fawcett, A. A., Clarke, L. C., Rausch, S. & Weyant, J. P. Overview of
EMF 24 policy scenarios. Energy J. 35, 33–60 (2014).
6. Bataille, C., Waisman, H., Colombier, M., Segafredo, L. & Williams, J.
The deep decarbonization pathways project (DDPP): insights and
emerging issues. Clim. Policy 16, S1–S6 (2016).
7. DeCarolis, J. F. et al. Leveraging open-source tools for collaborative
macro-energy system modeling efforts. Joule 4, 2523–2526 (2020).
8. Levi, P. J. et al. Macro-energy systems: toward a new discipline.
Joule 3, 2282–2286 (2019).

9. Low Carbon Resources Initiative. LCRI Net-zero 2050: U.S.

economy-wide deep decarbonization scenario analysis. EPRI, Palo
Alto, CA: (2022).
10. Larson, E. et al. Net-zero America: potential pathways, infrastructure, and impacts. (Princeton University, Princeton, NJ, 2021).
11. Ewing, J., Ross, M., Pickle, A., Stout, R., and Murray, B. Pathways To
Net-zero For The Us Energy Transition Ni R 22-06. (Nicholas Institute
for Energy, Environment & Sustainability, Duke University, Durham,
NC, 2022).
12. Clack, C. T. M., Choukulkar, A., Coté, B., & McKee, S. A. A plan for
economy-wide decarbonization of the United States (Vibrant Clean
Energy LLC). Boulder, CO. (2021).
13. Edenhofer, O., Lessmann, K., Kemfert, C., Grubb, M. & Kohler, J.
Induced technological change: exploring its implications for the
economics of atmospheric stabilization: synthesis report from the
innovation modeling comparison project. Energy J. SI2006, (2006).
14. Yue, X. et al. A review of approaches to uncertainty assessment in
energy system optimization models. Energy Strategy Rev. 21,
204–217 (2018).
15. Trutnevyte, E., McDowall, W., Tomei, J. & Keppo, I. Energy scenario
choices: insights from a retrospective review of UK energy futures.
Renew. Sustain. Energy Rev. 55, 326–337 (2016).
16. Morgan, M. G. & Keith, D. W. Improving the way we think about
projecting future energy use and emissions of carbon dioxide. Clim.
Change 90, 189–215 (2008).
17. Jewell, J. & Cherp, A. The feasibility of climate action: bridging the
inside and the outside view through feasibility spaces. WIREs Clim.
Change 14, e838 (2023).
18. Brutschin, E. et al. A multidimensional feasibility evaluation of lowcarbon scenarios. Environ. Res. Lett. 16, 064069 (2021).
19. Lempert, R. J. & Trujillo, H. R. Deep Decarbonization as a Risk Man[agement Challenge. https://www.rand.org/pubs/perspectives/](https://www.rand.org/pubs/perspectives/PE303.html)
[PE303.html (2018).](https://www.rand.org/pubs/perspectives/PE303.html)
20. Lempert, R. J., Popper, S. W. & Bankes, S. C. Shaping the next One
Hundred Years: New Methods for Quantitative, Long-Term Policy
Analysis. (RAND, Santa Monica, CA, 2003).
21. [Saltelli, A. et al. Global Sensitivity Analysis. The Primer. https://doi.](https://doi.org/10.1002/*************)
[org/10.1002/************* (Wiley, 2007).](https://doi.org/10.1002/*************)
22. Shapiro, A., Dentcheva, D. & Ruszczynski, A. Lectures on Stochastic
Programming: Modeling and Theory. (SIAM, 2021).
23. Patankar, N., De Queiroz, A. R., DeCarolis, J. F., Bazilian, M. D. &
Chattopadhyay, D. Building conflict uncertainty into electricity
planning: a South Sudan case study. Energy Sustain. Dev. 49,
53–64 (2019).
24. Kanudia, A. & Loulou, R. Robust responses to climate change via
stochastic MARKAL: the case of Québec. Eur. J. Oper. Res. 106,
15–30 (1998).
25. Loulou, R. & Lehtila, A. Stochastic programming and tradeoff analysis in TIMES. TIMES Version 3.3 User Note (2012).



Where w i 2 unif �ð 1, 1Þ, x i represents the decision variables in the
model, f ðxÞ is the new total system cost, f xð *Þ is the total system cost
resulting from the least-cost optimization and δ is the percent
additional slack on the total system cost (1% in our case). While
deviations of a larger magnitude have been observed in the electric
sector (average of 5%) [27], we chose 1% because our slack is applied
broadly over the entire U.S. energy system and not just the electric
sector, and we wish to explore pathways that are near cost-optimalthat
are not necessarily informed by historical deviations.


Data availability
Source data are provided with this paper.


Code availability
The code used to perform the modeling to generate alternative runs is
[available. (https://doi.org/10.5281/zenodo.13300106).](https://doi.org/10.5281/zenodo.13300106)


References

1. The Intergovernmental Panel on Climate Change (IPCC). Global
Warming of 1.5 °C: IPCC Special Report on Impacts of Global
Warming of 1.5 °C above Pre-Industrial Levels in Context of
Strengthening Response to Climate Change, Sustainable Develop[ment, and Efforts to Eradicate Poverty. https://doi.org/10.1017/](https://doi.org/10.1017/9781009157940)
[9781009157940 (Cambridge University Press, 2022).](https://doi.org/10.1017/9781009157940)



Nature Communications |  (2024) 15:8165 13


Article https://doi.org/10.1038/s41467-024-52433-z



26. Bennett, J. A. et al. Extending energy system modelling to include
extreme weather risks and application to hurricane events in Puerto
Rico. Nat. Energy 6, 240–249 (2021).
27. Trutnevyte, E. Does cost optimization approximate the real-world
energy transition? Energy 106, 182–193 (2016).
28. Brill, E. D., Chang, S.-Y. & Hopkins, L. D. Modeling to generate
alternatives: the HSJ approach and an illustration using a problem
in land use planning. Manag. Sci. 28, 221–235 (1982).
29. Price, J. & Keppo, I. Modelling to generate alternatives: a technique
to explore uncertainty in energy-environment-economy models.
Appl. Energy 195, 356–369 (2017).
30. Berntsen, P. B. & Trutnevyte, E. Ensuring diversity of national energy
scenarios: bottom-up energy system model with modeling to
generate alternatives. Energy 126, 886–898 (2017).
31. Li, F. G. N. & Trutnevyte, E. Investment appraisal of cost-optimal and
near-optimal pathways for the UK electricity sector transition to
2050. Appl. Energy 189, 89–109 (2017).
32. DeCarolis, J. F. Using modeling to generate alternatives (MGA) to
expand our thinking on energy futures. Energy Econ. 33,
145–152 (2011).
33. Neumann, F. & Brown, T. The near-optimal feasible space of a
renewable power system model. Electr. Power Syst. Res. 190,
106690 (2021).
34. Pedersen, T. T., Victoria, M., Rasmussen, M. G. & Andresen, G. B.
Modeling all alternative solutions for highly renewable energy
systems. Energy 234, 121294 (2021).
35. Patankar, N., Sarkela-Basset, X., Schivley, G., Leslie, E. & Jenkins, J.
Land use trade-offs in decarbonization of electricity generation in
the American West. Energy Clim. Change 4, 100107 (2023).
36. Neumann, F. & Brown, T. Broad ranges of investment configurations
for renewable power systems, robust to cost uncertainty and nearoptimality. iScience 26, 106702 (2023).
37. Lombardi, F., Pickering, B., Colombo, E. & Pfenninger, S. Policy
decision support for renewables deployment through spatially
explicit practically optimal alternatives. Joule 4, 2185–2207 (2020).
38. Pickering, B., Lombardi, F. & Pfenninger, S. Diversity of options to
eliminate fossil fuels and reach carbon neutrality across the entire
European energy system. Joule 6, 1253–1276 (2022).
39. Chen, Y., Kirkerud, J. G. & Bolkesjø, T. F. Balancing GHG mitigation
and land-use conflicts: alternative northern european energy system scenarios. Appl. Energy 310, 118557 (2022).
40. DeCarolis, J. F., Babaee, S., Li, B. & Kanungo, S. Modelling to generate alternatives with an energy system optimization model.
Environ. Model. Softw. 79, 300–310 (2016).
41. Hunter, K., Sreepathi, S. & DeCarolis, J. F. Modeling for insight using
tools for energy model optimization and analysis (Temoa). Energy
Econ. 40, 339–349 (2013).
42. DeCarolis, J. et al. GitHub - TemoaProject/temoa. Temoa Project,

“ ”
Tools for energy model optimization and analysis [. https://github.](https://github.com/TemoaProject/temoa)
[com/TemoaProject/temoa (2022).](https://github.com/TemoaProject/temoa)
43. Williams, J. H. et al. Carbon‐neutral pathways for the United States.
AGU Adv. 2, e2020AV000284 (2021).
44. Bistline, J. E. T. Roadmaps to net-zero emissions systems: emerging
insights and modeling challenges. Joule 5, 2551–2563 (2021).
45. Capuano, D. L. Annual energy outlook 2019. US Energy Inf. Adm. EIA.
[46. Schivley, G. Github - Powergenome. https://github.com/](https://github.com/PowerGenome/PowerGenome)
[PowerGenome/PowerGenome. (2022).](https://github.com/PowerGenome/PowerGenome)
[47. IEA (2019), The Future of Hydrogen, IEA, Paris https://www.iea.org/](https://www.iea.org/reports/the-future-of-hydrogen)
[reports/the-future-of-hydrogen, Licence: CC BY 4.0.](https://www.iea.org/reports/the-future-of-hydrogen)
48. Sepulveda, N. A., Jenkins, J. D., Edington, A., Mallapragada, D. S. &
Lester, R. K. The design space for long-duration energy storage in
decarbonized power systems. Nat. Energy 6, 506–516 (2021).
49. Davis, S. J. et al. Net-zero emissions energy systems. Science 360,
eaas9793 (2018).



50. Teletzke, G. et al. Evaluation of Practicable Subsurface CO 2 Storage
Capacity and Potential CO 2 Transportation Networks, Onshore
[North America. SSRN Electron. J. https://doi.org/10.2139/ssrn.](https://doi.org/10.2139/ssrn.3366176)
[3366176 (2019).](https://doi.org/10.2139/ssrn.3366176)
51. Akimoto, K., Sano, F., Oda, J., Kanaboshi, H. & Nakano, Y. Climate
change mitigation measures for global net-zero emissions and the
roles of CO2 capture and utilization and direct air capture. Energy
Clim. Change 2, 100057 (2021).
52. Van Der Spek, M. et al. Perspective on the hydrogen economy as a
pathway to reach net-zero CO 2 emissions in Europe. Energy
Environ. Sci. 15, 1034–1077 (2022).
53. Bistline, J. E. T. & Blanford, G. J. The role of the power sector in netzero energy systems. Energy Clim. Change 2, 100045 (2021).
54. Grubert, E. Fossil electricity retirement deadlines for a just transition. Science 370, 1171–1173 (2020).
55. Bistline, J. E. T. & Blanford, G. J. Impact of carbon dioxide removal
technologies on deep decarbonization of the electric power sector.
Nat. Commun. 12, 3732 (2021).
56. Jewell, J. & Cherp, A. The feasibility of climate action: bridging the
inside and the outside view through feasibility spaces. Wiley Interdiscip. Rev. Clim. Change 14, e838 (2023).
57. Williams, J. H., Jones, R. A. & Torn, M. S. Observations on the transition to a net-zero energy system in the United States. Energy Clim.
Change 2, 100050 (2021).
58. Loulou, R. &Labriet, M. ETSAP-TIAM: the TIMES integrated assessment
model Part I: model structure. Comput. Manag. Sci. 5, 7–40 (2008).
59. Loulou, R. ETSAP-TIAM: the TIMES integrated assessment model. part
II: mathematical formulation. Comput. Manag. Sci. 5, 41–66 (2008).
60. Venkatesh, A. et al. (2022). GitHub - TemoaProject/oeo: open
[energy outlook for the United States. https://github.com/](https://github.com/TemoaProject/oeo)
[TemoaProject/oeo. (2023).](https://github.com/TemoaProject/oeo)
61. Langholtz, M. H., Stokes, B. J. & Eaton, L. M. Billion-Ton Report:
Advancing Domestic Resources for a Thriving Bioeconomy. DOE/EE[1440, ORNL/TM-2016/160, 1271651 http://www.osti.gov/servlets/](http://www.osti.gov/servlets/purl/1271651/)
[purl/1271651/ https://doi.org/10.2172/1271651 (2016).](http://www.osti.gov/servlets/purl/1271651/)
62. Vimmerstedt, L. J. et al. Annual Technology Baseline. NREL/PR[6A20-74273, 1566062 http://www.osti.gov/servlets/purl/1566062/](http://www.osti.gov/servlets/purl/1566062/)
[https://doi.org/10.2172/1566062 (2019).](https://doi.org/10.2172/1566062)
63. Mai, T. T. et al. Electrification Futures Study: Scenarios of Electric
Technology Adoption and Power Consumption for the United States.
[NREL/TP-−6A20-71500, 1459351 http://www.osti.gov/servlets/](http://www.osti.gov/servlets/purl/1459351/)
[purl/1459351/ https://doi.org/10.2172/1459351(2018).](http://www.osti.gov/servlets/purl/1459351/)
64. Updated buildings sector appliance and equipment costs and
[efficiencies. US Energy Inf. Adm. EIA https://www.eia.gov/analysis/](https://www.eia.gov/analysis/studies/buildings/equipcosts/)
[studies/buildings/equipcosts/ (2023).](https://www.eia.gov/analysis/studies/buildings/equipcosts/)
[65. Manufacturing Energy Consumption (MECS). https://www.eia.gov/](https://www.eia.gov/consumption/manufacturing/data/2018/)
[consumption/manufacturing/data/2018/ (2018).](https://www.eia.gov/consumption/manufacturing/data/2018/)
66. Krey, V. et al. Looking under the hood: a comparison of technoeconomic assumptions across national and global integrated
assessment models. Energy 172, 1254–1267 (2019).
67. Zang, G., Sun, P., Elgowainy, A., Bafana, A. & Wang, M. Life cycle
analysis of electrofuels: fischer–tropsch fuel production from
hydrogen and corn ethanol byproduct CO 2 . Environ. Sci. Technol.
55, 3888–3897 (2021).
68. Keith, D. W., St. Holmes, G., Angelo, D. & Heidel, K. A process for
capturing CO 2 from the atmosphere. Joule 2, 1573–1594 (2018).


Acknowledgements
The authors gratefully acknowledge the Alfred P. Sloan Foundation (G2019-12328) and Advanced Research Projects Agency - Energy (ARPA-E)
(DE-AR0001471) for their financial support of the project. A.S and J.X.J
acknowledge the support of the Sloan Foundation (G-2019-12328) and
ARPA-E (DE-AR0001471). A.V., K.J., C.W, A.R.Q. and P.J. acknowledge the
support of the Sloan Foundation (G-2019-12328). The authors also



Nature Communications |  (2024) 15:8165 14


Article https://doi.org/10.1038/s41467-024-52433-z



gratefully acknowledge the support and input of Neha Patankar, Joseph
DeCarolis and the Open Energy Outlook advisory board. We thank
Downey Brill for his pioneering efforts in Modeling to Generate Alternatives methods.


Author contributions

A.S. wrote the initial draft, carried out the model runs, and performed the
data analysis. A.V. provided technical support, aided in data analysis and
provided edits and reviews for the draft. K.J. reviewed the final draft and
contributed to the visualization of model results. C.W. provided technical support and reviewed the final draft. H.E. built the initial framework
for the model runs. A.R.Q reviewed the draft and helped shape the final
manuscript. J.X.J and P.J. conceptualized the project, acquired funding,
and edited and reviewed the final manuscript.


Competing interests
The authors declare no competing interests.


Additional information

Supplementary information The online version contains
supplementary material available at
[https://doi.org/10.1038/s41467-024-52433-z.](https://doi.org/10.1038/s41467-024-52433-z)


Correspondence and requests for materials should be addressed to
Aditya Sinha.



Peer review information Nature Communications thanks Francesco

Lombardi, and the other, anonymous, reviewer(s) for their contribution
to the peer review of this work. A peer review file is available.


Reprints and permissions information is available at
[http://www.nature.com/reprints](http://www.nature.com/reprints)


Publisher’s note Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.


Open Access This article is licensed under a Creative Commons
Attribution-NonCommercial-NoDerivatives 4.0 International License,
which permits any non-commercial use, sharing, distribution and
reproduction in any medium or format, as long as you give appropriate
credit to the original author(s) and the source, provide a link to the
Creative Commons licence, and indicate if you modified the licensed
material. You do not have permission under this licence toshare adapted
material derived from this article or parts of it. The images or other third
party material in this article are included in the article’s Creative
Commons licence, unless indicated otherwise in a credit line to the

material. If material is not included in the article’s Creative Commons

licence and your intended use is not permitted by statutory regulation or
exceeds the permitted use, you will need to obtain permission directly
[from the copyright holder. To view a copy of this licence, visit http://](http://creativecommons.org/licenses/by-nc-nd/4.0/)
[creativecommons.org/licenses/by-nc-nd/4.0/.](http://creativecommons.org/licenses/by-nc-nd/4.0/)


© The Author(s) 2024



Nature Communications |  (2024) 15:8165 15




---

Supplementary Information for: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures


Aditya Sinha [1,*], Aranya Venkatesh [2,3], Katherine Jordan [2], Cameron Wade [4], Hadi Eshraghi [1],
Anderson R. de Queiroz [1,5], Paulina Jaramillo [2], Jeremiah X. Johnson [1]


1 Department of Civil and Environmental Engineering, North Carolina State University, Raleigh,

NC


2 Department of Engineering and Public Policy, Carnegie Mellon University, Pittsburgh, PA


3 Electric Power Research Institute, Palo Alto, CA


4 Sutubra Research Inc., Nova Scotia, Canada


5 Department of Decision Sciences, North Carolina Central University, Durham, NC


These authors jointly supervised this work: Paulina Jaramillo, Jeremiah X. Johnson


*Corresponding author: <EMAIL>


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  1


**Table of Contents:**


**Supplementary Method 1:** **Model Structure and Description of Deterministic Scenarios**


**Supplementary Method 2: Application of k-means clustering to select illustrative pathways**


**Supplementary Method 3: Sectoral details**


**Supplementary Method 4: Inflation Reduction Act Representation**


**Supplementary Note 1: Temporal-resolution representation sensitivity**


**Supplementary Note 2: MGA iteration termination criteria**


**Supplementary Note 3: Sensitivity to input parameters**


**Supplementary Note 4: Technology Choices with Varied Availability of Nascent**
**Technologies**


**Supplementary Figures**


**Supplementary References**


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  2


**Supplementary Method 1: Model Structure and Description of Deterministic Scenarios**


**Description of the main constraints:** Our optimization model is constructed as a linear program
in Temoa. [1] Four main equations govern the flow of energy through the model network. The
_Demand_Constrant_ (Supplementary Equation 1) ensures that the supply meets demand in all
time intervals. For each process, the _Capacity_Constraint_ (Supplementary Equation 2) ensures
sufficient capacity to meet the commodity flows in all time intervals. Between processes, the
_CommodityBalance_Constraint_ (Supplementary Equation 3) ensures that global commodity
production across the energy system is sufficient to meet the endogenous demands for that
commodity. Finally, the _objective function_ (Supplementary Equation 4) drives the model to
minimize the system-wide cost of energy supply by optimizing the deployment and utilization of
energy technologies across the system.


_Demand_Constrant:_


∑ 𝐼,𝑇−𝑇 [𝑎],𝑉 𝐹𝑂 𝑟,𝑝,𝑠,𝑑,𝑖,𝑡 + 𝑆𝐸𝐺 𝑠,𝑑 ⋅∑ 𝐼,𝑇 [𝑎],𝑉 𝐹𝑂𝐴 𝑟,𝑝,𝑖,𝑡 = 𝐷𝐸𝑀 𝑟,𝑝,𝑑𝑒𝑚 ⋅𝐷𝑆𝐷 𝑟,𝑠,𝑑,𝑑𝑒𝑚

**(1)**


Where 𝐹𝑂 𝑟,𝑝,𝑠,𝑑,𝑖,𝑡 is the commodity flow by time slice out of a technology based on a given
input, 𝑆𝐸𝐺 𝑠,𝑑 is the fraction of year represented by each (season (s), day (d)) tuple, 𝐹𝑂𝐴 𝑟,𝑝,𝑖,𝑡 is
the annual commodity flow out of a technology based on a given input, 𝐷𝐸𝑀 𝑟,𝑝,𝑑𝑒𝑚 is the enduse demands, by period (p) and 𝐷𝑆𝐷 𝑟,𝑠,𝑑,𝑑𝑒𝑚 is the demand-specific distribution. The subscripts
𝑟, 𝑝, 𝑠, 𝑑, 𝑖, 𝑡, 𝑜 belong to sets defined on the regions in the model (𝑟), the time periods in the
model (𝑝), the season (𝑠), time of day (𝑑), input commodity (𝑖), technology (𝑡) and output
commodity (𝑜).

_Capacity_Constraint:_



CFP ⋅ C2A ⋅ SEG ⋅ PLF
( 𝑟,𝑡,𝑣 𝑟,𝑡 𝑠,𝑑 𝑟,𝑝,𝑡,𝑣 ) ⋅𝐶𝐴𝑃 𝑟,𝑡,𝑣 𝑟,𝑝,𝑠,𝑑,𝑖,𝑡,𝑣,𝑜
= ∑𝐹𝑂

𝐼,𝑂



𝑟,𝑝,𝑠,𝑑,𝑖,𝑡,𝑣,𝑜
+ ∑𝐶𝑈𝑅

𝐼,𝑂

**(2)**



Where CFP 𝑟,𝑡,𝑣 is the process-specific capacity factor, C2A 𝑟,𝑡 converts from capacity to activity
units, 𝑆𝐸𝐺 𝑠,𝑑 like in Supplementary Equation 1 is the fraction of year represented by each
(season (s), day (d)) tuple, PLF 𝑟,𝑝,𝑡,𝑣 is the process life fraction active in a time period,
𝐹𝑂 𝑟,𝑝,𝑠,𝑑,𝑖,𝑡,𝑣,𝑜 is defined in Supplementary Equation 1 and 𝐶𝑈𝑅 𝑟,𝑝,𝑠,𝑑,𝑖,𝑡,𝑣,𝑜 is the commodity flow
out of a technology that is curtailed.

_CommodityBalance_Constraint:_



CFP ⋅ C2A ⋅ SEG ⋅ PLF
( 𝑟,𝑡,𝑣 𝑟,𝑡 𝑠,𝑑 𝑟,𝑝,𝑡,𝑣 ) ⋅𝐶𝐴𝑃 𝑟,𝑡,𝑣 𝑟,𝑝,𝑠,𝑑,𝑖,𝑡,𝑣,𝑜
= ∑𝐹𝑂

𝐼,𝑂



𝑟,𝑝,𝑠,𝑑,𝑖,𝑡,𝑣,𝑜
+ ∑𝐶𝑈𝑅

𝐼,𝑂

**(3)**



Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  3


Where CFP r,t,v, C2A r,t, SEG s,d, PLF r,p,t,v, 𝐹𝑂 𝑟,𝑝,𝑠,𝑑,𝑖,𝑡,𝑣,𝑜 and ∑ 𝐼,𝑂 𝐶𝑈𝑅 𝑟,𝑝,𝑠,𝑑,𝑖,𝑡,𝑣,𝑜 have been
defined above in Supplementary Equation 1 and Supplementary Equation 2. 𝐶𝐴𝑃 𝑟,𝑡,𝑣 refers to the
required tech capacity to support associated activity.


_Objective Function:_


𝑀𝑖𝑛 𝐶 𝑡𝑜𝑡 = 𝑀𝑖𝑛 (𝐶 𝑙𝑜𝑎𝑛𝑠 + 𝐶 𝑓𝑖𝑥𝑒𝑑 + 𝐶 𝑣𝑎𝑟𝑖𝑎𝑏𝑙𝑒 )


**(4)**


Where C tot refers to the total system cost, which is made up of the investment costs converted
into loans (C loans ), the fixed costs (C fixed ), and the variable costs (C variable ).



𝐶 ⋅ [(1 + 𝐺𝐷𝑅)] [𝑃] [0] [−𝑝+1] [ ⋅(1 −(1 + 𝐺𝐷𝑅)] [−𝑀𝑃𝐿] [𝑟,𝑡,𝑣] [)]
𝑓𝑖𝑥𝑒𝑑 𝑟,𝑝,𝑡,𝑣

𝐺𝐷𝑅

= ∑([𝐶𝐹



𝑟,𝑝,𝑡,𝑣




[ ⋅(1 −(1 + 𝐺𝐷𝑅)] [−𝑀𝑃𝐿] [𝑟,𝑡,𝑣] [)]

⋅𝐶𝐴𝑃
𝐺𝐷𝑅 𝑟,𝑡,𝑣
~~]~~ )



𝐶 𝑙𝑜𝑎𝑛𝑠 𝑟,𝑡,𝑣 ⋅𝐿𝐴 𝑟,𝑡,𝑣
= ∑([𝐶𝐼

𝑟,𝑡,𝑣



(1 + 𝐺𝐷𝑅) [𝑃] [0] [−𝑣+1] ⋅(1 −(1 + 𝐺𝐷𝑅) [−𝐿𝐿𝑃] [𝑟,𝑡,𝑣] )




[1 −(1 + 𝐺𝐷𝑅)] [−𝐿𝑃𝐴] [𝑟,𝑡,𝑣]

1 −(1 + 𝐺𝐷𝑅) [−𝐿𝑇𝑃] [𝑟,𝑡,𝑣] []]



**(5)**


**(6)**


**(7)**



⋅


⋅𝐶𝐴𝑃
𝑟,𝑡,𝑣
)



⋅(1 −(1 + 𝐺𝐷𝑅) [−𝐿𝐿𝑃] [𝑟,𝑡,𝑣] )

⋅ [1 −(1 + 𝐺𝐷𝑅)] [−𝐿𝑃𝐴] [𝑟,𝑡,𝑣]
𝐺𝐷𝑅 1 −(1 + 𝐺𝐷𝑅) [−𝐿𝑇𝑃] [𝑟,𝑡,𝑣]



𝐶 𝑣𝑎𝑟𝑖𝑎𝑏𝑙𝑒 𝑟,𝑝,𝑡,𝑣 ⋅
= ∑(𝐶𝑉



(1 + 𝐺𝐷𝑅) [𝑃] [0] [−𝑝+1] ⋅(1 −(1 + 𝐺𝐷𝑅) [−𝑀𝑃𝐿] [𝑟,𝑝,𝑡,𝑣] )



𝐺𝐷𝑅
𝑟,𝑝,𝑡,𝑣



𝑟,𝑝,𝑠,𝑑,𝑖,𝑡,𝑣,𝑜
⋅∑𝐹𝑂

𝑆,𝐷,𝐼,𝑂



)



𝑟,𝑝,𝑡,𝑣 ⋅
+ ∑(𝐶𝑉



(1 + 𝐺𝐷𝑅) [𝑃] [0] [−𝑝+1] ⋅(1 −(1 + 𝐺𝐷𝑅) [−𝑀𝑃𝐿] [𝑟,𝑝,𝑡,𝑣] )



𝐺𝐷𝑅
𝑟,𝑝,𝑡,𝑣



𝑟,𝑝,𝑖,𝑡∈𝑇 [𝑎],𝑣,𝑜
⋅∑𝐹𝑂𝐴

𝐼,𝑂



)



Where 𝐶𝐹 𝑟,𝑝,𝑡,𝑣 represents the fixed operations & maintenance cost, 𝐺𝐷𝑅 is the global discount
rate used to calculate present cost, 𝑃 0 is the start of the time horizon, 𝑀𝑃𝐿 𝑟,𝑡,𝑣 is the smaller of
the remaining model horizon or process technology life, 𝐶𝐴𝑃 𝑟,𝑡,𝑣 has been defined in
Supplementary Equation 3, 𝐶𝐼 𝑟,𝑡,𝑣 represents the technology-specific investment cost, 𝐿𝐴 𝑟,𝑡,𝑣 is


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  4


the loan amortization by technology and vintage based on the discount rate of the technology,
and 𝐶𝑉 𝑟,𝑝,𝑡,𝑣 refers to the variable operations & maintenance cost.


Along with these constraints, other constraints are also imposed in the model structure. These are
network-related constraints (for example, ensuring commodity balances throughout the model),
physical and operational constraints (for example, storage, firm generation, ramping, and reserve
margins in the power sector).


**Scenario Descriptions:** The deterministic least-cost pathway provides solutions minimizing the
total system cost of the linear program. The analysis spans 2020 to 2050 in 5-year increments.
All years within a given 5-year time period are assumed to be identical; thus, the optimal results
represent an indicative year within the five years. The United States is represented according to
the regions shown in Supplementary Figure 1. These regions were chosen as they represent
aggregations of United States electric balancing authorities and follow U.S. state boundaries.


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  5


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-5-0.png)

**Supplementary Figure 1: Overview of research methodology. A flow diagram showing the**
**inputs from the major end-use sectors, some characteristics of the database used,**
**application of the Temoa model, and type of runs conducted (deterministic and modeling to**
**generate alternatives). The major outputs analyzed in the current work are also included.**
**A nine-region description is used to represent the U.S., with the regions corresponding to**
**combinations of several states in most cases. Northwest (NW): Washington, Oregon, Idaho,**
**Montana, and Wyoming; North Central (N_CEN): Iowa, Wisconsin, Michigan, Illinois,**
**Minnesota, Indiana, North Dakota; Northeast (NE): New York, Massachusetts, Vermont,**
**Connecticut, Maine, New Hampshire, Rhode Island; Southwest (SW): Net Mexico, Nevada,**
**Utah, Colorado and Arizona, Central (CEN): Nebraska, Arkansas, Missouri, Louisiana,**
**Kansas, South Dakota and Oklahoma; Southeast (SE): Alabama, Kentucky, Georgia,**
**Tennessee, Florida, North Carolina, South Carolina, Mississippi; Mid-Atlantic (MID_AT):**
**Maryland, Delaware, Ohio, Pennsylvania, Virginia, New Jersey, District of Columbia, West**
**Virginia; California (CA) and Texas (TX). Data sources in figure: Energy Information**
**Administration (EIA)** **[2]** **; Billion Ton Report** **[3]** **; Annual Technology Baseline (ATB)** **[4]** **; National**
**Renewable Labs Electrification Futures Study (NREL EFS)** **[5]** **; Manufacturing Energy**
**Consumption Survey (MECS)** **[6]** **; MARKAL database.** **[7,8]**


Current-Policy scenario: This scenario assumes that all existing policies as of the end of 2022
remain in their current form and no new federal or state policies are implemented. These results
indicate how projected fuel prices and technology costs will shape the energy system in the
absence of additional climate policies. The policies included in this scenario are state-level


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  6


Renewable Portfolio Standards (RPS), the Cross-State Air Pollution Rule (CSAPR), the
California Cap and Trade program, the federal Investment Tax Credit (ITC), and the provisions
of the Inflation Reduction Act (also detailed in Supplementary Method 4). Note that these
policies are also included in the Net-Zero deterministic run as well as the modeling to generate
alternatives (MGA) runs. Unlike in the net-zero pathways, there are no additional policies to
constrain CO 2 emissions or support mitigation efforts after the provisions in the policies listed
above expire. In this Current-Policy scenario, exogenous fuel prices are based on the Energy
Information Administration (EIA) Annual Energy Outlook (AEO) 2022 ‘Reference’ case.


Net-Zero scenario: This scenario assumes that the U.S. energy system will reach net-zero CO 2
emissions by 2050. A constraint in the optimization caps CO 2 emissions across the energy
system to achieve this objective. The CO 2 cap is defined as a linear reduction in annual CO 2
emission beginning in the 2020 model period and reaching net-zero by 2050. In this scenario,
exogenous fuel prices are based on the EIA AEO 2022 ‘Low Oil Price’ case.


**Emissions Accounting:** The model specifies technology-specific pollutant emission factors for
all relevant processes in the energy system network. If the model selects a certain process, it
incurs emissions proportional to the “emission activity” associated with that process. For
example, consider the transportation sector in two scenarios: one with an emissions limit and
another without restrictions. In the unrestricted scenario, the model will choose to deploy fossil
fuel-based vehicles, which have emission factors measured in ‘kt of pollutant/vehicle miles
traveled.’ The model captures the pollutant burden from using these vehicles, including the
upstream emissions associated with the fuel supply for fossil fuel-based vehicles. In the
alternative scenario with an emissions limit, the model is likely to select electric vehicles to meet
transportation demand. Although the use of electric vehicles results in no tailpipe emissions, the
production of electricity may generate carbon emissions. The model accounts for these emissions
as well.


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  7


**Supplementary Method 2: Application of k-means clustering to select illustrative pathways**


To highlight the diverse ways in which net-zero CO 2 emissions can be achieved across the U.S.
energy system, Figure 5 of the main manuscript highlights a set of illustrative pathways. We first
create a feature matrix for the 2050 model year, incorporating all relevant parameters (such as
hydrogen, electricity, or natural gas use) for the 2050 model year. Subsequently, we normalized
the feature matrix to consider parameters spanning multiple orders of magnitude, ensuring equal
weight for each parameter. We then applied k-means clustering and tested for clusters between 2
and 10, ranking our findings based on a silhouette score and a Calinski-Harabasz score. We
found that three clustering parameters best represented the set of 1,100 decarbonization
pathways. Out of the set of parameters initially introduced, the combination of hydrogen,
synthetic liquids, and petroleum use was associated with the most favorable scores. Next, we
divided the 1,100 decarbonization runs into low/high hydrogen, low/high direct air capture, and
low/high electricity use. Low and high were defined as the lowest 25 [th] percentile or higher than
the 75 [th] percentile of the 1,110 decarbonization runs. The features we identified above were then
used to find the cluster centers in each of these low/high groups. We identified the nearest
decarbonization pathways to the cluster center based on the Euclidean distance and presented
these as the illustrative pathways in Figure 5 in the main manuscript.


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  8


**Supplementary Method 3: Sectoral details**


The energy system representation includes the electric, buildings (residential and commercial),
industrial, and transportation sectors.


**Electric sector:**


The electric sector includes representations of existing and new generation technologies indexed
by the region in which they are located. Thermal power plants include coal-fired steam with and
without CCS, natural gas steam plants, open-cycle, and combined-cycle natural gas turbines with
and without CCS, and light-water nuclear reactors. Renewable sources include conventional
hydro, centralized solar photovoltaics (PV), centralized solar thermal, distributed solar, onshore
and offshore wind, biomass, and geothermal technologies. Data for the electric sector are
compiled using PowerGenome, an open-source tool that allows users to create input datasets for
power system capacity expansion models. [16] PowerGenome primarily uses data from the National
Renewable Energy Laboratory (NREL), the U.S. Energy Information Administration (EIA), and
the US Environmental Protection Agency (EPA). The Catalyst Cooperative’s Public Utility Data
Liberation Project compiled much of these underlying data into a single SQLite database that
PowerGenome uses. Using PowerGenome, we aggregated balancing authorities (as defined by
EPA’s Integrated Planning Model [17] regions) into nine OEO regions to develop the spatial
representation for the electric sector shown in Supplementary Figure 1.


Existing generators: Due to computational limitations, the electricity sector operations in the
modeled regions cannot be represented by individual generator operations. To develop a
reduced-order representation of these generators that is computationally tractable, PowerGenome
uses k-means clustering techniques to aggregate existing generators into groups or clusters. The
generators are clustered using four generator characteristics: nameplate capacity, heat rate,
installation year, and fixed O&M costs. In each region, existing conventional coal-fired steam
and natural gas combined cycle plants are represented by 4 clusters each; natural gas combustion
turbine, natural gas steam turbine, nuclear, and conventional hydroelectric plants are represented
by 2 clusters each, while all other technology types – biomass, geothermal, centralized solar
photovoltaic, onshore wind – are represented by a single cluster each.


New thermal generators: New thermal generators are represented by a single cluster for every
modeled year and include natural gas combined cycle (NGCC), natural gas combustion turbine,
NGCC with 90% efficient carbon capture and storage (CCS), NGCC with 100% efficient CCS,
geothermal (hydro binary and hydro flash technologies), coal integrated gasification combined
cycle (IGCC), ultra-supercritical pulverized coal with 90% efficient CCS, biomass combinedcycle, and hydrogen combined-cycle. Data for these technologies are derived from the NREL
Annual Technology Baseline (ATB) via PowerGenome, except for items listed below:


   Hydrogen combined-cycle turbines: Hydrogen at 100 bar pressure can be burned new in
combined-cycle turbines to produce electricity, which is assumed to have the same
techno-economic characteristics as NGCC generators but without any combustion
emissions.


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  9


   Bio-energy with carbon capture and storage: This technology representation comes from
the U.S. EPA MARKAL database [7,8] .

   Hydrogen storage: This technology representation comes from the literature. [18]


New renewable generator clusters: In all model regions, new utility solar photovoltaics (PV) and
onshore wind capacities are represented by three clusters each, while offshore wind is
represented by a single cluster in each of three regions with offshore potential (CA, NW, NE
regions in Supplementary Fig. 1). Within a region, techno-economic data for all renewable
clusters of a single technology type are identical except for capacity factor and maximum
available capacity. These techno-economic characteristics are derived from the NREL ATB via
PowerGenome. Further, PowerGenome uses k-means clustering to develop these groups, which
differ by capacity factor and maximum available capacity in each region. Although
PowerGenome does not explicitly develop technology clusters for new residential-scale PV, we
use the tool to develop cost estimates (investment and O&M costs) for a single representative
cluster with underlying data from the NREL ATB. As an approximation, we also use the
capacity factors developed for the three utility PV clusters in each region to represent residential
PV generation. Each cluster has the same cost estimates as developed through PowerGenome.
Residential PV annual generation is specified exogenously, with data from the NREL dGen [19]
model’s Mid (PV cost) scenario – data available at the state level is aggregated to each of the
nine OEO regions. This technology is assumed to have no other resource constraints. We also
developed a single cluster of concentrating solar thermal technologies in the California and
Southwestern US regions. In the NREL ATB, the representative technology is assumed to be a
100 MW molten salt power tower with 10 hours of thermal energy storage driven by Class 5
(excellent) resources.


**Buildings sector:**


The buildings sector is divided into the residential and commercial sectors. The main
components used to represent these sectors are 1) end-use service demands, 2) the technoeconomic characteristics of the technologies used to meet those demands, and 3) the existing
capacity of technologies currently in service.


The service demands are derived from the NREL Electrification Futures Study [5] and consist of
two components: 1) the region-specific annual demands projected to 2050 and 2) the
apportionment of these annual demands at the hourly level. The demand technologies are
characterized by their capital costs, fixed and variable operation and maintenance costs,
efficiencies, capacity factors, existing capacity, and fuel inputs.


Service Demands: The service demands represented in the residential sector include space
cooling, space heating, refrigeration, freezing, lighting, water heating, cooking, clothes drying,
and dishwashing. In the commercial sector, we model space cooling, space heating, water
heating, refrigeration, cooking, lighting, and ventilation. The NREL Electrification Futures Study
(EFS) provides annual projections for these end-use demands in the United States through 2050.
These annual projections are available at the state level for each end-use demand in
Supplementary Table 1.


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  10


We use the estimates of buildings-related service demands for the end-use demands listed above.
In certain cases, external data was required to spatially allocate the demands reported from the
EFS. For example, residential space heating service demands were downscaled using the product
of each state’s share of heating-degree-days and residential square footage. Next, we perform an
additional aggregation to represent the state-level demands from this exercise into the nine
regions represented in Supplementary Fig. 1. In the case of commercial ventilation, the annual
service demands were estimated from the AEO as the product of the commercial square footage
of a region multiplied by ventilation efficiency and ventilation energy consumption.


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  11


**Supplementary Table 1. Annual service demand categories and units are drawn from the**
**NREL Electrification Futures Study.**

|Sector|End-use Demand|Units|
|---|---|---|
|Commercial|Space Cooling|Tera-BTU|
|Commercial|Cooking|Tera-BTU|
|Commercial|Lighting|Giga-Lumen-Year|
|Commercial|Refrigeration|Tera-BTU|
|Commercial|Space Heating<br>|Tera-BTU|
|Commercial|Ventilation~~1 ~~|Trillion-Cubic Feet/min-hours|
|Commercial|Water Heating<br>|Tera-BTU|
|Commercial|Other – Gas~~2 ~~<br>|Million-BTU|
|Commercial|Other – Electricity~~2 ~~<br>|Million-BTU|
|Commercial|Other – Diesel~~2 ~~|Million-BTU|
|Residential|Space Cooling|Million-BTU<br>|
|Residential|Refrigeration|Mega-Cubic Feet|
|Residential|Lighting|Giga-Lumen-Year|
|Residential|Space Heating|Million-BTU|
|Residential|Water Heating|Million-BTU|
|Residential|Clothes Drying|Giga-Pound|
|Residential|Dishwashing|Giga-Cycle<br>|
|Residential|Freezing|Mega-Cubic Feet|
|Residential|Cooking<br>|Million-BTU|
|Residential|Other – Electricity~~3 ~~|Million-BTU|



1 Service demand estimated from Annual Energy Outlook as commercial regional square footage × ventilation
efficiency × ventilation energy consumption. [2 ] The commercial other category in our databases aggregates the
service demands reported by Electrification Futures Study, for the categories: 'Commercial Other,’ 'District
Services,' 'Office Equipment (NON-P.C.),' and 'Office Equipment (P.C.).' [3 ] The residential other category in the
aggregates the service demands reported by EFS for the categories: 'Televisions and related,' 'Computers and
related,' 'residential other uses,' and 'residential furnace fans.' BTU – British thermal units.


The EFS study provides state-level projections of hourly electricity demands for a subset of the
subsectors listed above. These load profiles were developed by NREL using the outputs of other
models such as ResStock/ComStock and other data (e.g., from metering studies) for a range of
future scenarios. The resulting load profile from the 'High-Electrification and Rapid End-use
Technology Advancement' scenario is selected as the load profile for electric end-uses in our
database as it incorporates the largest share of total service demands. In cases where the hourly
profile of an end-use demand is not available, we assume the service demands to be constant
throughout the year.


The load profiles for space heating and cooling are combined into a single profile in the EFS
data. However, we are interested in separating these two profiles to represent these demands
distinctly and allow for technological choice in the model. We use population-weighted heating
and cooling degree hour (hdh/cdh) data to separate the heating and cooling profiles. [20] This data is
available at the hourly timescale for the year 2010. We first calculate an average hourly hdh (and


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  12


cdh) fraction for each hour of 2010 within a given model region. Next, the combined heating
(/cooling) load taken from EFS is multiplied by these fractions to estimate each hour's heating
(/cooling) load.


Demand Technology Specification: The characteristics of the demand technologies in the
residential and commercial sectors in the OEO database are based on the Residential Demand

Module (RDM) and Commercial Demand Module (CDM) of the National Energy Modeling
System (NEMS) - Updated Buildings Sector Appliance and Equipment Costs and Efficiency. In
our database, we incorporate 1) technology-specific efficiencies, 2) fixed and variable operations
and maintenance costs, 3) investment costs, 4) lifetimes, and 5) typical capacities based on
contract reports prepared by Navigant Consulting, Inc. for the U.S. Energy Information
Administration. We represent a diverse set of equipment classes/types capable of servicing
different parts of the building sector. For example, the technologies capable of meeting
residential cooling demand include room air conditioners, central air conditioners, and heat
pumps. Three types of heat pumps are represented for residential cooling: air-source, groundsource, and natural gas heat pumps. We use this dataset to adjust the heat pump coefficient of
performance (COP) to reflect more regionally appropriate values. Other work has estimated a
linear relationship between COP and outside temperature for various heat pumps. [21] Our work
assumes that the indoor temperatures stay constant at 70ºF, and we use the slope of this linear
relationship to adjust heat pump COPs at the regional level using state-level population-weighted

temperatures.


Along with diversity in technology representations, we also incorporate the changes in the
techno-economic parameters for residential and commercial equipment from 2020 to 2050 in 5year increments based on projections incorporated in NEMS. In most cases, two versions of a
given technology are included in our database: 1) a standard version and 2) a high-efficiency
version. For example, the heating seasonal performance factor (HSPF) for a typical air-source
heat pump of a 2020 vintage is 8.6, whereas a high-efficiency version has an HSPF of 9.0. The
costs and lifetimes may also vary across these different technology options. The technoeconomic parameters of a given technology are assumed to be region agnostic, except heat pump
COPs as described above.


Existing Capacity: We rely on two data sources to estimate existing capacity at the technology
level in the buildings database: 1) NREL EFS service demands scaled using a derived utilization
factor, which gives us the existing capacities of most of the existing electric technologies and 2)
the MARKAL database, which gives us the existing capacities for technologies not included in
1). The estimation method from the two data sources is briefly described below:


1) NREL EFS: First, an end-use service demand-specific 'utilization factor' is calculated using
the hourly load profile data published in the EFS. This is done by calculating the average
demand across the 8,760 hours divided by the 95th percentile demand for each end-use service
demand in each model region for which hourly load profiles are available. The existing capacity
of the buildings sector technologies is then estimated as the service demand in 2017 from EFS
scaled using the calculated utilization factors.


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  13


2) MARKAL database: We rely on existing capacity estimations from the U.S. EPA MARKAL
database. [22] MARKAL reports the existing capacities for most technologies listed in the EIA
dataset for the nine census divisions. However, since our regional representation differs from the
census divisions, we scale the reported existing capacities using U.S. Census state population
data to obtain estimates of existing capacity in each U.S. state. Finally, we aggregate up to the
regions shown in Supplementary Fig. 1 and estimate the existing capacities of the technologies
represented. MARKAL estimates the existing capacity by multiplying the demand met (taken
from AEO) in an end-use service sub-sector by the estimated market share of a technology
contributing to the end-use service category. This value is then divided by a utilization factor to
estimate the existing capacity of a certain technology in a given region.


**Transportation sector:**


The transportation sector models the following transport modes: light-duty vehicles, trucks,
buses, rail passenger, rail freight, subways, aviation, marine, and off-highway. The demands,
efficiencies, and costs for these transport modes are drawn from various data sources, including
the MARKAL database, Princeton's Net-Zero America study, the National Renewable Energy
Laboratory, and Argonne National Laboratory.


The transportation sector in our database can be split into three categories: light-duty, mediumduty, and heavy-duty vehicles. We represent seven light-duty vehicle-size classes. We
exogenously constrain the percent of end-use demand met by each size class. As Temoa
optimizes for the least-cost solution, the model would deploy only cheap, fuel-efficient minicars
and compact cars without this constraint. We base size-class demand distributions using the
MARKAL database. Medium-duty vehicles consist only of medium-duty trucks, corresponding
to class 6 and 7 trucks. Heavy-duty vehicles include heavy trucks, buses, passenger and freight
rail, aviation, marine vessels, and off-highway vehicles. Each transportation technology meets a
separate, exogenously specified end-use demand. Depending on the technology, the demand may
be in ton miles, passenger miles, or vehicle miles traveled. Temoa does not currently allow for
modal switching. For example, a long-haul truck cannot meet the same freight demand as a train;
the two have distinct exogenously defined demand profiles. Existing work shows that modal
switching is one effective means of reducing transportation energy consumption, and future work
will incorporate modal switching into the Temoa framework. [23]


We define technology-specific discount rates, or hurdle rates, for some technologies. Hurdle
rates allow the modeler to account for consumer preferences otherwise ignored in a least-cost
optimization model. [24] In the light-duty vehicle sector, we assume that conventional and hybrid
gasoline-fueled vehicles (excluding plug-in hybrid vehicles) have a 5% hurdle rate, equal to the
model's global discount rate, and all other powertrains have a 6% hurdle rate. For heavy-duty
vehicles, conventional technologies have a 5% hurdle rate, and alternative-fueled vehicles have a
10% hurdle rate.


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  14


**Industrial Sector:**


The industrial sector in the OEO database is subdivided into the manufacturing and nonmanufacturing sectors. Their representation in the OEO database consists of two key
components: 1) end-use service demands and 2) the demand technologies/processes used to meet
those demands.


Service Demands: End-use demands in the industrial sector of the database are aggregated based
on the North American Industry Classification System (NAICS). Supplementary Table 2 shows
the end-use demands represented, mapped to their NAICS codes. The sectors represented here
account for approximately 99% of industrial energy consumption per the EIA. The demands for
these industrial sub-sectors are represented in estimates of annual billion dollars of production
shipment value, except cement manufacturing, where demands are instead represented in a
million metric tons of cement production. These demands are derived from the Manufacturing
Energy Consumption Survey (MECS) for the base year 2014 on a U.S. state-by-state basis. The
state-based demands are then aggregated into the regions shown in Supplementary Fig. 1.
Subsector-specific growth rates are applied to the end-use demands in the industrial sector. For
example, growth in demand in the petroleum refining and the iron and steel mills and ferroalloys
sub-sectors are assumed to be flat based on the refinery utilization and iron and steel
manufacturing projections and in the AEO reference case. Based on other work, the demand for
plastic and rubber products is assumed to increase by 3% annually from 2020 to 2050. [25] The
growth in demand for cement manufacturing is also taken from the AEO reference case. Finally,
the growth rates of all other manufacturing and non-manufacturing sub-sectors are assumed to
increase at an annual rate of 1%.


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  15


**Supplementary Table 2: Annual service demand categories making up the Industrial**
**Sector.**

|Sub-sector|End-use demand|
|---|---|
|Manufacturing|Food and Beverage|
|Manufacturing|Pulp, Paper and Paperboard Mills|
|Manufacturing|Petroleum Refining|
|Manufacturing|Chemical Manufacturing|
|Manufacturing|Plastics and Rubber Products|
|Manufacturing|Cement Manufacturing|
|Manufacturing|Iron and Steel Mills and Ferroalloys|
|Manufacturing|Alumina and Aluminum|
|Manufacturing|All other manufacturing|
|Non-Manufacturing|Agriculture – Crops|
|Non-Manufacturing|Agriculture – Other|
|Non-Manufacturing|Coal Mining|
|Non-Manufacturing|Oil and Gas Mining|
|Non-Manufacturing|Metal and other Non-Metal Mining|
|Non-Manufacturing|Construction|



Demand Technology Specification: Due to the heterogeneous nature of the industrial sector,
explicit end-use technology options with costs and efficiencies are not depicted for most
subsectors. Instead, we consider the major energy consumed by common industrial processes to
account for the heterogeneity across the industrial sector. These industrial processes include 1)
process heating, 2) conventional boiler use, 3) combined heat and power or co-generation
systems, 4) machine drives, 5) facility heating ventilation and air conditioning systems, 6)
process cooling and refrigeration and 7) a catch-all 'other' energy use category. However, we
incorporate technological choice in the case of new boiler use and process heat. We specify cost
premiums for new conventional electric boilers (368 $2016/kW), electric process heating
equipment (368 $2016/kW), hydrogen boilers (227 $2016/kW), and hydrogen process heating
equipment (227 $2016/kW) across the industrial manufacturing sectors. We also set the fixed
cost premiums at 0.6 $2018/kW for the entire lifetime of these boilers and process heating
equipment (20 years). In our modeling approach, heavy industries, including cement, chemicals,
iron and steel mills, ferro-alloys, petroleum refining, and plastics, are precluded from using
hydrogen or electricity for process heat. However, we assume that all sectors can electrify

conventional boilers.


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  16


**Supplementary Methods 4: Inflation Reduction Act Representation**


We include the major provisions from the 2022 Inflation Reduction Act. The provisions we
model include the investment and production tax credits for renewable electricity generators,
carbon capture, and use/sequestration, the production tax credit for existing nuclear capacity, the
clean hydrogen production tax credit, and tax credits for passenger and commercial vehicles. We
provide details on each modeled provision below.


**Production Tax Credit (sections 45, 45Y):** The production tax credit (PTC) provides
$27.50/MWh over ten years of operation for qualified facilities (assuming labor requirements are
met). Bonuses are available for domestic content and projects located in energy communities.
Following modeling assumptions from the National Renewable Energy Laboratory, we assume
the bonus credits start at an average rate of 5% in 2023, increase to 10% by 2028, and stay at
10% until the credit expires in 2033. We also adjusted the tax credit to align with Temoa’s 5-year
time periods. The IRA PTC begins in 2023, so it is available only in the last two years of
Temoa’s first time period (2020-2024). We prorate the tax credit as necessary, given our 5-year
model increment, ensuring that projects receive the equivalent of 10 years of tax credits. We
represent the IRA’s extension of the existing PTC (section 45) and the newly introduced
technology-neutral PTC (section 45Y) that allows any zero-emission generation to qualify.
Under section 45, qualifying technologies are solar, onshore and offshore wind, geothermal,
biomass, and hydropower. Section 45 is replaced with 45Y in 2025.


**Investment Tax Credit (sections 48, 45E):** The investment tax credit (ITC) offers 30% of a
project’s installed capital costs, plus bonuses for domestic content and energy community
locations. Pre-IRA, an ITC of 26% was available. We model the ITC as a reduction in capital
cost for qualifying technologies, which include solar, on and offshore wind, geothermal, and
energy storage. In Temoa’s first time period, we assume new qualifying technologies could
receive a 26% reduction in 2020 and 2021 and a 30% reduction for 2022-2024, for an average
credit of 28.4%. Projects constructed in the model’s second time period are eligible for full
credit. The ITC drops to 22.5% in 2034, after which it sunsets.


Again, following the NREL convention, we assume a 5% bonus starting in 2023 that increases to
10% by 2028, where it stays until the credit sunsets after 2034. We represent the IRA’s extension
of the existing ITC (section 48) and the newly introduced technology-neutral ITC (section 48E)
that allows any zero-emission generation to qualify.


**Carbon Capture (section 45Q):** Credits for carbon capture vary depending on the source of the
carbon dioxide and whether the captured carbon is sequestered or used. For post-combustion
carbon capture, facilities earn $85/tonne for sequestered CO 2 and $60/tonne for facilities that
capture and use the CO 2 . For direct air capture facilities, the credits increase to $180/tonne for
sequestration and $130/tonne for use. While we do not represent enhanced oil recovery, we
credit facilities using CO 2 to produce synthetic fuels. We assume all wage and apprenticeship
requirements are met. The credit is available for new facilities for 12 years, and facilities must be
in place by 2033. Like the PTC and ITC, we model the credit in the first and third time periods as


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  17


a weighted average to effectively prorate Temoa’s five-year time periods and the credit’s time
horizon. The credit is modeled as a negative variable cost.


**Existing Nuclear (section 45U):** Existing nuclear technologies receive $30/MWh starting in
2024 and sunsetting in 2032. We model this credit as a negative variable cost, with the first and
third time periods receiving a weighted average credit of 1/5 and 3/5, respectively.


**Clean Hydrogen (section 45V):** The clean hydrogen tax credit is tiered based on the carbon
intensity of the production mechanism (see Supplementary Table 3). With the current average
grid mix, electrolytic hydrogen is not eligible for even the lowest tier. We allow hydrogen
produced by biomass with CCS or an electrolyzer fed with new zero-emission electric capacity
to receive the full $3/kg H 2 credit. The International Energy Association assumes an emission
factor of 1.0 kg CO 2 /kg H 2 for natural gas reforming with carbon capture, qualifying for the
second tier of the tax credit ($1/kg H 2 ). All credits are modeled as a negative variable cost.


**Supplementary Table 3: Hydrogen credits in the Inflation Reduction Act (IRA) by carbon**
**intensity in kilograms of CO** **2** **equivalents incurred by hydrogen production.** _[26]_

|Carbon intensity<br>[kg CO -eq / kg H ]<br>2 2|IRA credit<br>[$ / kg H ]<br>2|
|---|---|
|> 4.0|0|
|2.5 – 4.0|0.06|
|1.5 – 2.5|0.075|
|0.45 – 1.5|1.00|
|< 0.45|3.00|



**Passenger Vehicles (section 30D):** Individuals purchasing a qualified plug-in electric vehicle
(EV) or fuel cell vehicle (FCV) between 2023 and 2032 may be eligible for credit up to $7,500
(segmented into two $3,750 credits). The credits are subject to several requirements, including
limits on the vehicle’s manufacturer-suggested retail price (MSRP), the individual’s adjusted
gross income, requirements for domestic battery assembly, critical mineral sourcing, and
avoidance of entities of concern. The number of vehicles and individuals qualifying for the credit
remains uncertain. Still, a recent report from the International Council on Clean Transportation
(ICCT) and Energy Innovation Policy & Technology estimates the fraction of vehicle sales
eligible for each. We use the report’s “moderate” scenario in our modeling. The study assumes
that 100% of new EVs are eligible for the full $3,750 domestic battery assembly credit. In 2023,
the study assumes 100% of new EVs meet the critical material requirements, dropping to 79% in
2025 and 72% in 2030 as the requirements increase in stringency. In 2032, the study assumes
78% of vehicles qualify. The report assumes that 87% of new EVs meet MSRP eligibility and
50% meet the entities of concern provision. Finally, the study assumes that 68% of individuals


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  18


meet the adjusted gross income (AGI) requirements in 2023, rising to 77% in 2030. We model
the provision as an average credit, implemented as a reduction in capital cost applied to all
qualifying vehicles (all EVs and FCVs). Using the fractions outlined above, we calculate the
average credit in a given year as follows:


𝐶 𝑇 = 𝐹 𝑀𝑆𝑅𝑃 𝐹 𝐴𝐺𝐼 𝐹 𝐸𝐶 [(𝐶 𝐷𝐵𝐴 𝐹 𝐷𝐵𝐴 ) + (𝐶 𝐶𝑀𝑆 𝐹 𝐶𝑀𝑆 )]


**(8)**


Where 𝐶 𝑇 is the total credit available, 𝐶 𝐷𝐵𝐴 is the domestic battery assembly credit, 𝐶 𝐶𝑀𝑆 is the
critical mineral sourcing credit, and 𝐹 𝑥 are the fractions of vehicles meeting the MSRP, AGI,
entities of concern, domestic battery assembly, and critical minerals sourcing requirements.
Supplementary Table 4 outlines the calculated credit values in each year and each Temoa time
period.


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  19


**Supplementary Table 4: Application of IRA vehicle credits. F** **CMS** **– fraction of vehicles**
**meeting the critical minerals sourcing requirements, F** **MSRP** **– fraction of vehicles meeting**
**the manufacturer-suggested retail price requirements, F** **AGI** **– fraction of vehicles meeting**
**the adjusted gross income requirements, F** **EC** **– fraction of vehicles meeting the entities of**
**concern requirements, C** **DBA** **– domestic battery assembly credit, C** **CMS** **– critical mineral**
**sourcing credit, C** **T** **– total credit available.**







|Year|𝑭<br>𝑪𝑴𝑺|𝑭<br>𝑴𝑺𝑹𝑷|𝑭<br>𝑨𝑮𝑰|𝑭<br>𝑬𝑪|𝑪<br>𝑫𝑩𝑨|𝑪<br>𝑪𝑴𝑺|𝑪<br>𝑻|Credit by time<br>period, 2022 USD|
|---|---|---|---|---|---|---|---|---|
|**2020**|0|0|0|0|0|0|$0.00|$3,000.00|
|**2021**|0|0|0|0|0|0|$0.00||
|**2022**|0|0|0|0|0|0|$0.00||
|**2023**|1|1|1|1|$3,750|$3,750|$7,500.00||
|**2024**|1|1|1|1|$3,750|$3,750|$7,500.00||
|**2025**|0.79|0.87|0.68|0.5|$3,750|$3,750|$3,971.12|$4,059.41|
|**2026**|0.698|0.87|0.698|0.5|$3,750|$3,750|$3,866.73||
|**2027**|0.716|0.87|0.716|0.5|$3,750|$3,750|$4,008.49||
|**2028**|0.734|0.87|0.734|0.5|$3,750|$3,750|$4,152.37||
|**2029**|0.752|0.87|0.752|0.5|$3,750|$3,750|$4,298.36||
|**2030**|0.72|0.87|0.77|0.5|$3,750|$3,750|$4,320.86|$2,637.73|
|**2031**|0.75|0.87|0.77|0.5|$3,750|$3,750|$4,396.22||
|**2032**|0.78|0.87|0.77|0.5|$3,750|$3,750|$4,471.58||
|**2033**|0|0|0|0|0|0|$0.00||
|**2034**|0|0|0|0|0|0|$0.00||
|**2035**|0|0|0|0|0|0|$0.00||


**Commercial Vehicles (section 45W):** Commercial vehicles are not subject to the same
restrictions as passenger vehicles. The IRA provides a tax credit equal to 30% of the vehicle's
purchase price for qualifying light-, medium-, and heavy-duty vehicles. Light-duty commercial
vehicles tax credits cannot exceed $7,500, and medium- and heavy-duty vehicles may not
receive credits over $40,000. To implement this provision, we reduce the capital cost by 30%. If
30% of the capital cost exceeds $7,500/$40,000, we reduce the capital cost by that value.


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  20


**Stacked credits:** Temoa’s technology-explicit representation enables us to represent distinct
pathways in cases where a technology can qualify for one of multiple provisions. Renewable
energy project owners may receive either the PTC or ITC, but not both. Similarly, projects
cannot qualify for both 45V and 45Q. We implement tax credits such that the model may choose
one or the other, allowing it to optimize credit efficacy.


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  21


**Supplementary Note 1: Temporal-resolution representation sensitivity**


Research has highlighted the importance of adequate representations of operation detail in
capacity expansion models for the power system, particularly at higher renewable penetration
levels. [9,10] The incorporation of this operational detail comes with an additional computational
burden. Depending on the computational resource available at an energy system modeler’s
disposal, it may be possible to run optimization models (in a greenfield setting) of up to 96
regions with 2-hourly time steps for a year for hundreds of scenarios in “reasonable” amounts of
time. [11] However, even highly detailed models produce outputs with uncertainty. [12] Furthermore,
researchers are required to balance between model resolution, uncertainty, and computational
burden. [13]


Due to computational limits, the power system representation used in the MGA simulations in
the current study relies on an aggregate representation of twelve time slices. Our aim with this
analysis is to answer the question of uncertainty in capacity expansion across multiple decades
for hundreds of net-zero futures. We are interested in capturing insights from multi-decadal
pathway optimization for near cost-optimal futures instead of evaluating a highly detailed 1-year
snapshot. Due to the need to create many future scenarios, we face a tradeoff between temporal
resolution and computational burdens. While high-resolution models are necessary to assess finer
points of implementation in energy transitions, they cannot provide the range of scenarios we
explore in this analysis. The distribution of pathways enabled by hundreds of model runs with
lower temporal resolution can and should be considered in tandem with high resolution analyses
that offer few modeling runs. The feasibility space discussed in the main manuscript should also
include an evaluation of power system reliability for each near-cost-optimal pathway.


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  22


**Supplementary Note 2: MGA iteration termination criteria**


The near-optimal decision space in energy system linear problems is typically vast. For example, in our
model, there are numerous (1080 unique technologies) and diverse technologies represented. A
comprehensive search of this space can pose challenges due to its multi-dimensional nature. Recent
methods in literature employ strategies that attempt to identify all feasible design options within the
convex hull of the MGA solution space. [14] These methods use a subset of dimensions of interest,
determined beforehand, in an algorithmic way with unit vectors to look for the decision boundaries of the
near-optimal space. The current study employs a search function that weights the activities (or flow)
through technologies in the model with a random weight sampled from a uniform distribution between -1
and 1. Activities are used in the objective function as they represent the contribution of each technology
towards meeting intermediate or end-use demands. Allowing both positive and negative weights resulted
in a more expansive set of solutions relative to “one-sided” weights (example: [0, 1]). Overall, this
approach allows for an agnostic inclusion of all technologies in the model to influence the search
direction in the convex hull, which is the primary tradeoff with other approaches where a small subset of
technologies (or technology groups) are chosen as axes to search on apriori.


To illustrate that the number of MGA iterations from which statistics are drawn is sufficient, we provide
the change in statistics as a function of iterations for the quantities of interest covered in the manuscript.
There is little change in the distributions in almost all cases after 300-400 iterations. However, in a multidimensional space such as the one in consideration here, even an extended number of iterations may not
be sufficient to capture all energy system configurations of interest. It is possible that the structure of the
search function (objective function) in the current study is unable to find certain configurations of the
energy system. Adoption of this approach comes with a tradeoff of not having complete assurance of a
comprehensive search in the near-optimal decision space. Applying the weighting factors directly to
activity variables, however, ensures a broad response in the model. In our formulation of MGA, we see
more than 95% of activities vary, with 75% of activities respond showing a coefficient of variation larger
than 20% in response to different weighting factors, illustrating the breadth of the solution space that is
being explored. This shows that our approach is capturing a wide range of possible futures within the
solution space.


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  23


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-23-0.png)

**Supplementary Figure 2: Convergence in statistics of parameters discussed in the manuscript as a**
**function of MGA iteration (up to 1,100 iterations). The different colors in each panel show the 10** **[th]** **,**
**25** **[th]** **, 50** **[th]** **, 75** **[th]** **,** **and 90** **[th]** **percentile values for the different parameters. The plot presents the**
**convergence of primary energy use of fossil fuels (a, b, c, d), electricity use by sector (e, f, g, h),**
**electric capacity (i, j, k, l, m, n), and carbon mitigation technologies (o, p, q, r). Primary energy is in**
**exajoules (EJ), electricity use is in terawatt-hours (TWh), electricity capacity is in gigawatts (GW)**
**and carbon mitigation technologies is in million tons of CO** **2** **(Mt CO** **2** **). BECCS – Bio-energy with**
**carbon capture and sequestration; DAC – Direct Air Capture. Source data are provided as a**
**Source Data file.**


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  24


**Supplementary Note 3: Sensitivity to input parameters**


In this section we discuss the influence of uncertain inputs to the model: 1) the fuel prices, 2) cost of
renewables and 3) demand projections in the residential, commercial, transportation and industrial
sectors. The sensitivity of these input parameters is assessed against a subset of the major outputs from
the model: 1) electric capacity, 2) electricity generation, 3) electricity use by sector, 4) energy system
wide hydrogen production, 5) total primary energy use and, 6) the use of carbon management
technologies in Figures S3-S5. For an ideal parametric uncertainty assessment of the model, advanced
methods such as the Method of Morris should be employed. These methods are capable of
comprehensively evaluating the influence of model parameters.


Sensitivity to renewable prices trajectories: Supplementary Figure 3 shows the model results when the
cost of renewables is perturbed from the “Moderate” market scenario as projected by the Annual
Technology Baseline (ATB, Table 1). The ATB also publishes “Conservative” and “Advanced” market
scenarios. Supplementary Figure 3 demonstrates that, under a net-zero emissions constraint, these higher
and lower cost perturbations result in minor changes in the deployment of electric capacity and generation
resources over the modeling horizon. In the scenario with higher renewable costs, nuclear generation
increases slightly, whereas it decreases in the scenario with lower renewables cost. Additionally, the
higher renewables cost scenario results in reduced hydrogen production. Conversely, the lower
renewables cost case results in an increase in hydrogen production.


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  25


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-25-0.png)

**Supplementary Figure 3: Sensitivity of model results to renewable price trajectories. Sensitivity of**
**renewable price trajectories as observed in: a) electric capacity in gigawatts (GW), b) electric**
**generation in terawatt-hours (TWh), c) electricity use by sector in TWh, d) hydrogen production**
**pathways in petajoules (PJ), e) energy system wide primary energy use in exajoules (EJ), and f)**
**carbon management technologies in tons of CO** **2** **. The left bar represents the results with the**
**moderate renewable cost assumptions (Net-Zero, presented as the least-cost net-zero pathway in the**
**manuscript), the middle bar represents a high renewable cost scenario (High Ren), and the right**
**bar represents a low renewable cost scenario (Low Ren). NG Steam Methane Reforming – Natural**
**Gas Steam Methane Reforming; CCS – Carbon Capture and Sequestration. Source data are**
**provided as a Source Data file.**


Sensitivity to fossil fuel price trajectories: Supplementary Figure 4 shows model results obtained by
perturbing the fossil fuel prices from the “Reference” scenario projected by the Annual Energy Outlook
(AEO, Table 1). The AEO also publishes “Low” and “High” fuel price scenarios. Supplementary Figure 4
shows that these price profiles result in some notable changes across the modeling horizon. Electrification
in the transportation sector is sensitive to petroleum prices, with a slower rate of electrification noted
when petroleum prices are lower and a faster rate when prices are higher. By 2050, the total electricity use
in the transportation sector is slightly higher in the scenario with higher petroleum prices compared to


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  26


other scenarios. The high oil price scenario leads to substantial hydrogen production 2025 to 2050. In this
scenario, the expensive transportation fuels are displaced by synthetic fuels created via the FischerTropsch process, using hydrogen in their synthesis. The primary energy panel in Supplementary Figure 4
also highlights the difference in petroleum utilization across the sensitivity scenarios in response to the
price profiles.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-26-0.png)


**Supplementary Figure 4: Sensitivity of model results to fuel price trajectories. Sensitivity of fuel**
**price trajectories as observed in: a) electric capacity in gigawatts (GW), b) electric generation in**
**terawatt-hours (TWh), c) electricity use by sector in TWh, d) hydrogen production pathways in**
**petajoules (PJ), e) energy system wide primary energy use in exajoules (EJ), and f) carbon**
**management technologies in tons of CO** **2** **. The left bar represents the results presented as the least-**
**cost net-zero pathway in the manuscript (Net-Zero), the middle bar represents a high fuel price**
**scenario (High Oil), and the right bar represents a low fuel price scenario (Low Oil). NG Steam**
**Methane Reforming – Natural Gas Steam Methane Reforming; CCS – Carbon Capture and**
**Sequestration. Source data are provided as a Source Data file.**


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  27


Sensitivity to exogenously specified demands: Supplementary Figure 5 shows model results obtained by
perturbing the exogenously specified demands for all service demands in the model by 10%. The service
demands are sector-specific, and Supplementary Method 3 provides details on the underlying assumptions
for each of these demands. Supplementary Figure 5 shows that nearly all of the displayed model outputs
respond to perturbations in the demand across the modeling horizon. Notably, electricity demand is not
exogenously specified but instead responds to changes in service demands from the buildings,
transportation, and industrial sectors. Lower service demands in these sectors translate to a reduced need
for electricity, and vice versa. Hydrogen production follows this trend as well. In terms of primary energy,
solar, wind and nuclear resources show the most variation across the scenarios. Finally, carbon
management technologies are relatively stable by 2050, though they exhibit some variation earlier in the
modeling horizon.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-27-0.png)


**Supplementary Figure 5: Sensitivity of model results to exogenous demand assumptions. Sensitivity**
**of exogenously specified demands as observed in: a) electric capacity in gigawatts (GW), b) electric**
**generation in terawatt-hours (TWh), c) electricity use by sector in TWh, d) hydrogen production**
**pathways in petajoules (PJ), e) energy system wide primary energy use in exajoules (EJ), and f)**


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  28


**carbon management technologies in tons of CO** **2** **. The left bar represents the results presented in the**
**least-cost net-zero pathway in the manuscript (Net-Zero), the middle bar represents a high service**
**demand scenario (High Dem), and the right bar represents a low service demand scenario (Low**
**Dem). NG Steam Methane Reforming – Natural Gas Steam Methane Reforming; CCS – Carbon**
**Capture and Sequestration. Source data are provided as a Source Data file.**


Uncertain input parameters have the potential to influence modeling results in a significant way. Here, we
demonstrate that substantial variation in the model inputs results in model results that are fully
encompassed in the range of results obtained from the MGA model runs. While this does not guarantee
that all parametric tests or more stringent tests on the studied parameters will be inclusive of the MGA
results, it does demonstrate that the MGA approach is identifying near-cost-optimal results that include
and extend beyond those from the standard parametric sensitivity analysis.


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  29


**Supplementary Note 4: Technology Choices with Varied Availability of Nascent**
**Technologies**


This section explores technology/energy carrier choices when technologies that are still in nascent stages
for large-scale use are either abundant or scarcely available. We analyzed subsets of the MGA runs,
including the top 10% or the bottom 10% regarding the use of: 1) hydrogen, 2) DAC, 3) synthetic fuels,
and 4) BECCS.


Low and high hydrogen: Supplementary Figure 6 shows that scenarios with higher hydrogen use greatly
reduce coal consumption relative to low hydrogen use scenarios through the modeling horizon. However,
coal is eliminated across both scenarios by 2050. Low hydrogen scenarios also result in a narrowing of
natural gas use outcomes. Scenarios with low hydrogen availability create a lower ceiling for BECCS.
These scenarios are also associated with higher DAC use to accommodate the loss of a low/zero carbon
energy carrier.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-29-0.png)


**Supplementary Figure 6: Comparison of fossil fuel use in low/high hydrogen production pathways.**
**Primary energy in near cost-optimal net-zero pathways with low hydrogen production as defined**
**by the lowest 10** **[th]** **percentile of all 1100 MGA iterations (thus representing results from 110 MGA**
**runs) of coal (a, b), natural gas (c, d), and petroleum (e, f). Primary energy of the highest 10** **[th]**
**percentile of hydrogen production pathways, of coal (g, h), natural gas (i, j), and petroleum (k, l).**
**The solid line shows the deterministic least-cost net-zero pathway, while the dashed lines**
**depict the least-cost current-policy pathway.** **All values are presented in exajoules (EJ). Source**
**data are provided as a Source Data file.**


Low and high DAC: Supplementary Figure 7 shows that scenarios with lower DAC use are associated
with higher hydrogen use. These lower DAC scenarios were also associated with fewer instances of low
BECCS. This relationship has also been observed in other work. [15] Unsurprisingly, scenarios with high
DAC are associated with increased natural gas consumption compared to low DAC scenarios.


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  30


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-30-0.png)

**Supplementary Figure 7: Comparison of carbon management technologies in low/high direct air**
**capture use pathways. The box plots show the deployment of bio-energy with carbon capture and**
**storage (BECCS) (a, g), coal power with carbon capture and storage (coal CCS) (b, h), natural gas**
**steam methane reforming with carbon capture and storage (natural gas SMR with CCS) (c, i), total**
**carbon capture and storage (CCS) as the sum of BECCS, coal CCS, and natural gas CCS, (d, j),**
**direct air capture (DAC) (e, k), and total geologic sequestration (f, l). For each category, the left**
**panel shows pathways with low DAC use as defined by the lowest 10** **[th]** **percentile of all 1100 MGA**
**iterations (thus representing results from 110 MGA runs). The panel on the right represents the**
**same but for the highest 10** **[th]** **percentile of DAC use pathways.** **The solid line shows the**
**deterministic least-cost net-zero pathway, while the dashed lines depict the least-cost**
**current-policy pathway. All values are in in million tons of CO** **2** **/year (Mt CO** **2** **/year).** **Source**
**data are provided as a Source Data file.**


Low and high synthetic fuel use: Supplementary Figure 8 shows that scenarios with high synthetic fuel
use result in the narrowing of outcomes from the power sector compared to low synthetic fuel use
scenarios. High synthetic fuel use scenarios result in the elimination of coal CCS plants. While DAC
levels by 2050 were comparable across the scenarios, the deployment of DAC starts earlier in the high
synthetic fuel use scenarios. Unsurprisingly, hydrogen use in these high scenarios is also higher.


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  31


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-31-0.png)

**Supplementary Figure 8: Comparison of power sector characteristics in low/high synthetic fuel use**
**pathways. Near cost-optimal pathways in the power sector where box plots show total electricity**
**use across the entire energy system (a, g), electricity generation from solar (b, h), electricity**
**generation from wind (c, i), electricity generation from nuclear (d, j), electricity generation from**
**natural gas (e, k) all in terawatt-hours (TWh). Panels f and l show the battery capacity in gigawatts**
**(GW) deployed in the near cost-optimal decarbonization pathways as presented in the manuscript.**
**For each category, the left panel shows pathways with low synthetic fuel use as defined by the**
**lowest 10** **[th]** **percentile of all 1100 MGA iterations (thus representing results from 110 MGA runs).**
**The panel on the right represents the same but for the highest 10** **[th]** **percentile of synthetic fuel use**
**pathways.** **The solid line shows the deterministic least-cost net-zero pathway, while the**
**dashed lines depict the least-cost current-policy pathway.** **Source data are provided as a**

**Source Data file.**


Low and high BECCS: Supplementary Figure 9 shows that scenarios with increased BECCS use see more
coal consumption across the modeling horizon. As in the case of the high and low hydrogen use
comparisons, coal is eliminated across both scenarios by 2050. Petroleum and natural gas largely show
the same range of outcomes across the two scenarios.


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  32


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-32-0.png)

**Supplementary Figure 9: Comparison of fossil fuel use in low/high bio-energy with carbon capture**
**and storage pathways. Primary energy in near cost-optimal net-zero pathways with low bio-energy**
**with carbon capture and sequestration (BECCS) use as defined by the lowest 10** **[th]** **percentile of all**
**1100 MGA iterations (thus representing results from 110 MGA runs) of coal (a, b), natural gas (c,**
**d), and petroleum (e, f). Primary energy of the highest 10** **[th]** **percentile of BECCS use, of coal (g, h),**
**natural gas (i, j), and petroleum (k, l). All values are presented in exajoules (EJ).** **The solid line**
**shows the deterministic least-cost net-zero pathway, while the dashed lines depict the least-**
**cost current-policy pathway. All values are in exajoules (EJ).** **Source data are provided as a**

**Source Data file.**


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  33


**Supplementary Figures:**

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-33-0.png)


**Supplementary Figure 10: Electric capacity in near cost-optimal pathways. Near cost-**
**optimal pathways showing capacity in the power sector for a) total capacity, b) solar, c)**
**wind, d) nuclear, e) coal, and f) natural gas in net-zero CO2 pathways. All values are in**
**gigawatts (GW). The solid line shows the deterministic least-cost net-zero pathway, while**
**the dashed lines depict the least-cost current-policy pathway. Source data are provided as a**
**Source Data file.**


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  34


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-34-0.png)

**Supplementary Figure 11: Fraction of renewables in near cost-optimal pathways. Fraction**
**of electricity generation in each time period from only renewables (green) and renewables**
**+ nuclear (dark green) in the near cost-optimal decarbonization pathways.** **Source data are**
**provided as a Source Data file.**


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  35


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/sinhaDiverseDecarbonizationPathways2024/sinhaDiverseDecarbonizationPathways2024.pdf-35-0.png)

**Supplementary Figure 12: Electricity use across the energy system. Electricity use for the**
**a) total energy system, b) commercial, c) residential, d) industrial, and e) transport sectors**
**in terawatt-hours (TWh) across the near cost-optimal net-zero pathways. The solid line**
**shows the deterministic (least-cost) net-zero scenario, while the dashed lines distinguish the**
**current-policy deterministic run. All values are in terawatt-hours (TWh).** **Source data are**
**provided as a Source Data file.**


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  36


**Supplementary References**


1. Hunter, K., Sreepathi, S., and DeCarolis, J.F. (2013). Modeling for insight using Tools for

Energy Model Optimization and Analysis (Temoa). Energy Econ. _40_, 339–349.
https://doi.org/10.1016/j.eneco.2013.07.014.


2. Annual Energy Outlook 2020 with Projections to 2050 (2020).


3. Langholtz, M.H., Stokes, B.J., and Eaton, L.M. (2016). 2016 Billion-Ton Report: Advancing

Domestic Resources for a Thriving Bioeconomy https://doi.org/10.2172/1271651.


4. Vimmerstedt, L.J., Akar, S., Augustine, C.R., Beiter, P.C., Cole, W.J., Feldman, D.J., Kurup,

P., Lantz, E.J., Margolis, R.M., Stehly, T.J., et al. (2019). 2019 Annual Technology Baseline
(National Renewable Energy Labs) https://doi.org/10.2172/1566062.


5. Mai, T.T., Jadun, P., Logan, J.S., McMillan, C.A., Muratori, M., Steinberg, D.C.,

Vimmerstedt, L.J., Haley, B., Jones, R., and Nelson, B. (2018). Electrification Futures Study:
Scenarios of Electric Technology Adoption and Power Consumption for the United States
(National Renewable Energy Labs) https://doi.org/10.2172/1459351.


6. Manufacturing energy consumption (MECS) (2018). (U.S. Energy Inf. Adm. (EIA)).


7. Loulou, R. (2008). ETSAP-TIAM: the TIMES integrated assessment model. part II:

mathematical formulation. Comput. Manag. Sci. _5_, 41–66. https://doi.org/10.1007/s10287007-0045-0.


8. Loulou, R., and Labriet, M. (2008). ETSAP-TIAM: the TIMES integrated assessment model

Part I: Model structure. Comput. Manag. Sci. _5_, 7–40. https://doi.org/10.1007/s10287-0070046-z.


9. Poncelet, K., Delarue, E., Six, D., Duerinck, J., and D’haeseleer, W. (2016). Impact of the

level of temporal and operational detail in energy-system planning models. Appl. Energy
_162_, 631–643. https://doi.org/10.1016/j.apenergy.2015.10.100.


10. Mallapragada, D.S., Papageorgiou, D.J., Venkatesh, A., Lara, C.L., and Grossmann, I.E.

(2018). Impact of model resolution on scenario outcomes for electricity sector system
expansion. Energy _163_, 1231–1244. https://doi.org/10.1016/j.energy.2018.08.015.


11. Pickering, B., Lombardi, F., and Pfenninger, S. (2022). Diversity of options to eliminate

fossil fuels and reach carbon neutrality across the entire European energy system. Joule _6_,
1253–1276. https://doi.org/10.1016/j.joule.2022.05.009.


12. DeCarolis, J., Daly, H., Dodds, P., Keppo, I., Li, F., McDowall, W., Pye, S., Strachan, N.,

Trutnevyte, E., Usher, W., et al. (2017). Formalizing best practice for energy system
optimization modelling. Appl. Energy _194_, 184–198.
https://doi.org/10.1016/j.apenergy.2017.03.001.


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  37


13. Yliruka, M., Moret, S., and Shah, N. (2023). Detail or uncertainty? Applying global

sensitivity analysis to strike a balance in energy system models. Comput. Chem. Eng. _177_,
108287. https://doi.org/10.1016/j.compchemeng.2023.108287.


14. Pedersen, T.T., Victoria, M., Rasmussen, M.G., and Andresen, G.B. (2021). Modeling all

alternative solutions for highly renewable energy systems. Energy _234_, 121294.
https://doi.org/10.1016/j.energy.2021.121294.


15. Mignone, B.K., Clarke, L., Edmonds, J.A., Gurgel, A., Herzog, H.J., Johnson, J.X.,

Mallapragada, D.S., McJeon, H., Morris, J., O’Rourke, P.R., et al. (2024). Drivers and
implications of alternative routes to fuels decarbonization in net-zero energy systems. Nat.
Commun. _15_, 3938.


16. Schivley, G. (2022). Github - Powergenome.

https://github.com/PowerGenome/PowerGenome.


17. Supply, C., and Horizon, M.T. (2010). Updates to EPA Base Case v3. 02 EISA Using the

Integrated Planning Model.


18. Dowling, J.A., Rinaldi, K.Z., Ruggles, T.H., Davis, S.J., Yuan, M., Tong, F., Lewis, N.S.,

and Caldeira, K. (2020). Role of Long-Duration Energy Storage in Variable Renewable
Electricity Systems. Joule _4_, 1907–1928. https://doi.org/10.1016/j.joule.2020.07.007.


19. Sigrin, B., Gleason, M., Preus, R., Baring-Gould, I., and Margolis, R. (2016). Distributed

generation market demand model (dGen): Documentation (National Renewable Energy
Lab.(NREL), Golden, CO (United States)).


20. Roberts, M.J., Zhang, S., Yuan, E., Jones, J., and Fripp, M. (2022). Using temperature

sensitivity to estimate shiftable electricity demand. Iscience _25_ .


21. Vaishnav, P., and Fatimah, A.M. (2020). The environmental consequences of electrifying

space heating. Environ. Sci. Technol. _54_, 9814–9823.


22. Victor, N., Nichols, C., and Zelek, C. (2018). The US power sector decarbonization:

Investigating technology options with MARKAL nine-region model. Energy Econ. _73_, 410–
425.


23. Kaack, L.H., Vaishnav, P., Morgan, M.G., Azevedo, I.L., and Rai, S. (2018). Decarbonizing

intraregional freight systems with a focus on modal shift. Environ. Res. Lett. _13_, 083001.


24. McCollum, D.L., Wilson, C., Pettifor, H., Ramea, K., Krey, V., Riahi, K., Bertram, C., Lin,

Z., Edelenbosch, O.Y., and Fujisawa, S. (2017). Improving the behavioral realism of global
integrated assessment models: An application to consumers’ vehicle choices. Transp. Res.
Part Transp. Environ. _55_, 322–342.


25. Meys, R., Kätelhön, A., Bachmann, M., Winter, B., Zibunas, C., Suh, S., and Bardow, A.

(2021). Achieving net-zero greenhouse gas emission plastics by a circular carbon economy.
Science _374_, 71–76.


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  38


26. White House (2022). Clean Energy Tax Provisions in the Inflation Reduction Act. White

House. https://www.whitehouse.gov/cleanenergy/clean-energy-tax-provisions/.


Supplement to: Diverse Decarbonization Pathways Under Near Cost-Optimal Futures  39


