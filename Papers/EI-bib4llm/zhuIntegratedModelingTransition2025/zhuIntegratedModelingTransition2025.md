# Citation Key: zhuIntegratedModelingTransition2025

---

Supplementary Information (SI) for Energy & Environmental Science.
This journal is © The Royal Society of Chemistry 2025

# Supplementary information for: Integrated Modeling for the Transition Pathway of China’s Power System


Z<PERSON><PERSON> [1], <PERSON> [1] _[∗]_, <PERSON><PERSON> [2] _[∗]_, and <PERSON><PERSON><PERSON> [1] _[∗]_


1 Institute of Energy, Environment and Economy,
Tsinghua University, Beijing, 100084, China
2 State Key Laboratory of Severe Weather,
Chinese Academy of Meteorological Sciences, Beijing, 100081, China
_∗_ To whom correspondence should be addressed;
E-mail: <EMAIL>, <EMAIL>, zhang ~~x~~ <EMAIL>.


CONTENTS

## **Contents**


**S1 Introduction** **1**

S1.1 Regions and grids . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 1
S1.2 Model comparison . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 3


**S2 Energy Resource Assessment** **4**
S2.1 Assessment of wind and solar power . . . . . . . . . . . . . . . . . . . . . . . . . . 4
S2.1.1 Assessment of the hourly capacity factor for wind power . . . . . . . . . . . 5
S2.1.2 Assessment of the hourly capacity factor for solar photovoltaic power . . . . 9
S2.1.3 Assessment of the hourly capacity factor for concentrating solar power . . . 10
S2.1.4 Assessment of suitable area for developing wind, utility-scale solar photovoltaic power and concentrating solar power . . . . . . . . . . . . . . . . . . 12
S2.1.5 Assessment of suitable area for developing distributed solar photovoltaic
power . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 19
S2.1.6 Assessment of installation capacity potential . . . . . . . . . . . . . . . . . 23
S2.2 Assessment of hydropower . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 29
S2.3 Assessment of biomass energy . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 31
S2.4 Assessment of carbon sequestration . . . . . . . . . . . . . . . . . . . . . . . . . . . 32


**S3 Input Data and Pre-process** **33**
S3.1 Hourly power demand factor profile . . . . . . . . . . . . . . . . . . . . . . . . . . . 33
S3.2 Annual power demand projections . . . . . . . . . . . . . . . . . . . . . . . . . . . 38
S3.3 Wind and solar power . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 39
S3.3.1 Installed capacity allocation . . . . . . . . . . . . . . . . . . . . . . . . . . . 39
S3.3.2 Grid integration . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 40
S3.3.3 Clustering wind and solar PV . . . . . . . . . . . . . . . . . . . . . . . . . . 43
S3.3.4 Cost projections . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 45
S3.4 Hydropower . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 46
S3.5 Thermal and nuclear power . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 48
S3.5.1 Installed capacity . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 48
S3.5.2 Technical parameters . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 51
S3.5.3 Cost projections . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 52
S3.6 Energy storage . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 54
S3.6.1 Installed and potential capacity . . . . . . . . . . . . . . . . . . . . . . . . . 54
S3.6.2 Cost projections and technical parameters . . . . . . . . . . . . . . . . . . . 56
S3.7 Inter-grid transmission . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 57
S3.8 Carbon source and sink match . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 61

S3.9 Direct air capture . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 61
S3.10Financial parameters . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 62


**S4 Formula of the Power-system Optimization Model** **63**
S4.1 Variables and parameters . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 63
S4.1.1 Indices . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 63

S4.1.2 Sets . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 64

S4.1.3 Decision variables . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 65

S4.1.4 Intermediate variables . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 66

S4.1.5 Parameters . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 66

S4.2 Objective function . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 70
S4.3 Constraints . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 71

S4.3.1 Wind and solar PV power output . . . . . . . . . . . . . . . . . . . . . . . . 71
S4.3.2 Concentrating solar power output . . . . . . . . . . . . . . . . . . . . . . . 72
S4.3.3 Hydropower output . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 72


1


LIST OF FIGURES


S4.3.4 Intra-grid transmission . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 74
S4.3.5 Thermal and nuclear power . . . . . . . . . . . . . . . . . . . . . . . . . . . 75
S4.3.6 Energy storage . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 79
S4.3.7 Inter-grid transmission . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 81
S4.3.8 Power demand balance . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 82

S4.3.9 Reserve requirement . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 83
S4.3.10Inertia requirement . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 84
S4.3.11Carbon emissions limitations . . . . . . . . . . . . . . . . . . . . . . . . . . 85

S4.3.12Direct air capture . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 85
S4.3.13Carbon source-sink match . . . . . . . . . . . . . . . . . . . . . . . . . . . . 86


**S5 Model implementation and dynamic optimization** **86**


**S6 Scenario design** **87**


**S7 Supplementary results** **89**
S7.1 System cost of electricity . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 90
S7.2 National capacity mix to 2060 . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 91
S7.3 National generation mix to 2060 . . . . . . . . . . . . . . . . . . . . . . . . . . . . 92
S7.4 Carbon emissions and source-sink matching . . . . . . . . . . . . . . . . . . . . . . 93
S7.5 Optimized capacity of wind and solar power . . . . . . . . . . . . . . . . . . . . . . 97
S7.6 Optimized capacity of hydropower . . . . . . . . . . . . . . . . . . . . . . . . . . . 101
S7.7 Charging and discharging route of energy storage . . . . . . . . . . . . . . . . . . . 102
S7.8 Inter-regional transmission . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 103
S7.9 Intra-regional transmission . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 104
S7.10Distribution of hourly marginal cost of generation . . . . . . . . . . . . . . . . . . 105
S7.11Hourly load dispatch . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 108

## **List of Figures**


S1 Framework of the CISPO model. . . . . . . . . . . . . . . . . . . . . . . . . . . . . 1

S2 Region division in this model. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 2
S3 Assessment process for wind and solar power. . . . . . . . . . . . . . . . . . . . . . 4
S4 Normalized power output curve of different wind power models [[1]] . . . . . . . . . . 6
S5 Illustration of the fitting results of the normalized power output curve for the
selected wind turbine models. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 6
S6 Cell level annual average capacity factor (0–1) for onshore and offshore wind power
in 2019. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 8
S7 Cell level annual average capacity factor (0–1) for solar photovoltaic power in 2019. 10
S8 Simplified schematic of a parabolic trough power plant. Graphic: © NREL/Parthiv
and Craig [[2]] . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 11
S9 Cell level annual average capacity factor (0–1) for concentrating solar power in 2019. 12
S10 Land use distribution in RESDC CNLUCC dataset. . . . . . . . . . . . . . . . . . 13
S11 Suitable area (km [2] ) for wind (a, b, and c) and utility-scale solar PV (d, e, and f)
in open, base, and conservative scenarios. . . . . . . . . . . . . . . . . . . . . . . . 18
S12 Suitable area (km [2] ) for concentrating solar power in open (a), base (b), and conservative (c) scenarios. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 19
S13 Framework for rooftop area assessment using XGBoost regression model. . . . . . . 20
S14 Illustration of Roof Vector Data Area. . . . . . . . . . . . . . . . . . . . . . . . . . 21
S15 Correlation coefficient between rooftop area and road length (A), built-up area
(B), population (C), and night lights (D). . . . . . . . . . . . . . . . . . . . . . . . 22


2


LIST OF FIGURES


S16 Rooftop area (km [2] ) of training data (A) fed into XGBoost model, and predicted
results (B) by the trained XGBoost model. . . . . . . . . . . . . . . . . . . . . . . 22
S17 Suitable area (km [2] ) for building distributed solar PV in each grid cell in open (a),
base (b), and conservative (c) scenario. . . . . . . . . . . . . . . . . . . . . . . . . . 23
S18 Installation capacity potential (MW) for onshore and offshore wind power in each
grid cell in open (a), base (b) and, conservative (c) scenarios. . . . . . . . . . . . . 25
S19 Installation capacity potential (MW) for utility-scale (a, b, and c) and distributed
solar PV (d, e, and f) in each grid cell in open, base, and conservative scenario. . . 27
S20 Installation capacity potential (MW) for concentrating solar power in each grid
cell in open (a), base (b), and conservative (c) scenarios. . . . . . . . . . . . . . . . 29
S21 Natural inflow discharge (m [3] /s) for the installed and potential hydropower plants
in each province. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 30
S22 Annual bio-energy fuel potential (TJ/yr) in each 1km _×_ 1km pixel (a), and installation capacity potential (GW) in each power grid (b). . . . . . . . . . . . . . . . . 31
S23 Deep saline aquifer carbon storage potential in China (about 180 Gt in total). . . . 32
S24 Load (MW) profile in typical weekday (a), non-work day (b), and daily maximum
and minimum demand (MW) all year round (c) in Anhui province [[3]] . . . . . . . . . 33
S25 Normalized demand load profile in Anhui, Beijing, Chongqing, Fujian, Gansu,
Guangdong, Guangxi, and Guizhou. . . . . . . . . . . . . . . . . . . . . . . . . . . 34
S26 Normalized demand load profile in Hainan, Hebei, Heilongjiang, Henan, Hubei,
Hunan, Jiangsu, and Jiangxi. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 35
S27 Normalized demand load profile in Jilin, Liaoning, East Inner Mongolia, West
Inner Mongolia, Ningxia, Qinghai, Shaanxi, and Shandong. . . . . . . . . . . . . . 36
S28 Normalized demand load profile in Shanghai, Shanxi, Sichuan, Tianjin, Xinjiang,
Xizang, Yunnan, and Zhejiang. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 37
S29 Annual power demand (TWh/yr) for each province in 2019 and 2022. . . . . . . . 38
S30 Annual electricity demand projection toward 2060. . . . . . . . . . . . . . . . . . . 38
S31 Existing installed capacity of wind and utility-scale solar PV by 2022 [[4]] . . . . . . . 39
S32 Illustration of grid cell interconnection to the main transmission network. Connections consist of spur lines (silver) to the nearest substation (black point) and
trunk lines (red) to the nearest load center (orange polygon). . . . . . . . . . . . . 41
S33 Matching results of connecting cells to the major nodes in mainland China. . . . . 42
S34 Illustration of the integration to the major node for offshore wind. . . . . . . . . . 43
S35 Provincial aggregation results of wind power in mainland China. (a): Onshore
wind; (b): Offshore wind; (c): Utility-scale solar PV; (d): Distributed solar PV. . . 44
S36 Cost projections of wind and solar power. A: onshore wind; B: offshore wind; C:
utility-scale solar PV; D: distributed solar PV; E: CSP. . . . . . . . . . . . . . . . 46
S37 Existing installed capacity (MW) of hydropower. In CISPO, the capacity expansion and operation of hydropower is optimized at the dam site level. . . . . . . . . 47
S38 Projections of installed capacity (GW) for thermal and nuclear power toward 2060. 50
S39 Nuclear installation potential for each province. Graphic: © Xinjian Xiao and
Kejun Jiang [[5]] . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 51
S40 Cost projections of thermal and nuclear power. . . . . . . . . . . . . . . . . . . . . 53
S41 Coal and gas fuel cost for each province. . . . . . . . . . . . . . . . . . . . . . . . . 54
S42 Cost projections for energy storage. . . . . . . . . . . . . . . . . . . . . . . . . . . . 57
S43 Capacity (GW) of existing inter-grid transmission lines. . . . . . . . . . . . . . . . 58
S44 Relationship between transmission capacity and distance for 500kV and 1000kV
AC lines [[6]] . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 59

S45 Commitment status at each timestep. The number of online units at timestep _t_
equals the pre-timestep ( _t −_ 1) online units plus start-up and minus shut-down
units in timestep _t_ . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 76


3


LIST OF FIGURES


S46 Annual emissions limitations in each year toward 2060. The “NoEmisCap” scenario
is not modeled with an annual emissions limitation. . . . . . . . . . . . . . . . . . . 88

S47 Temporal coverage in different representative hour selections. . . . . . . . . . . . . 89
S48 System cost of electricity (SCOE, yuan/kWh) from 2030 to 2060 across scenarios,
excluding distribution and administration costs. . . . . . . . . . . . . . . . . . . . . 90
S49 Nationwide optimized capacity mix from 2030 to 2060 across scenarios. . . . . . . . 91
S50 Nationwide optimized generation mix from 2030 to 2060 across scenarios. . . . . . 92
S51 Annual net carbon emissions (MtCO 2 /yr) in each provincial grid from 2030 to 2060
in the base scenario. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 93
S52 Annual carbon (MtCO 2 /yr) captured and injected in each province and CO 2 sequestration site, and transportation volume of each capture-sequestration link from
2050 to 2060 in the “NegEmis200Mt” scenario. . . . . . . . . . . . . . . . . . . . . 93
S53 Annual carbon (MtCO 2 /yr) captured and injected in each province and CO 2 sequestration site, and transportation volume of each capture-sequestration link from
2050 to 2060 in the “NegEmis700Mt” scenario. . . . . . . . . . . . . . . . . . . . . 93
S54 Annual carbon (MtCO 2 /yr) captured and injected in each province and CO 2 sequestration site, and transportation volume of each capture-sequestration link from
2050 to 2060 in the “LimitedBECCS” scenario. . . . . . . . . . . . . . . . . . . . . 94
S55 Annual carbon (MtCO 2 /yr) captured and injected in each province and CO 2 sequestration site, and transportation volume of each capture-sequestration link from
2050 to 2060 in the “WithoutBECCS” scenario. . . . . . . . . . . . . . . . . . . . . 94
S56 Annual carbon (MtCO 2 /yr) captured and injected in each province and CO 2 sequestration site, and transportation volume of each capture-sequestration link from
2050 to 2060 in the “VRELandOpen” scenario. . . . . . . . . . . . . . . . . . . . . 95
S57 Annual carbon (MtCO 2 /yr) captured and injected in each province and CO 2 sequestration site, and transportation volume of each capture-sequestration link from
2050 to 2060 in the “VRELandConservative” scenario. . . . . . . . . . . . . . . . . 95
S58 Annual carbon (MtCO 2 /yr) captured and injected in each province and CO 2 sequestration site, and transportation volume of each capture-sequestration link from
2050 to 2060 in the “Capex1.25X” scenario. . . . . . . . . . . . . . . . . . . . . . . 96
S59 Annual carbon (MtCO 2 /yr) captured and injected in each province and CO 2 sequestration site, and transportation volume of each capture-sequestration link from
2050 to 2060 in the “Demand1.25X” scenario. . . . . . . . . . . . . . . . . . . . . . 96
S60 National capacity (GW) of wind (onshore + offshore) and solar PV (utility-scale
+ distributed) across scenarios from 2030 to 2060. . . . . . . . . . . . . . . . . . . 97
S61 Cell-level capacity expansion of wind (onshore and offshore) in the base scenario
from 2030 to 2060. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 98

S62 Cell-level capacity expansion of utility-scale solar PV in the base scenario from

2030 to 2060. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 99

S63 Cell-level capacity expansion of distributed solar PV in the base scenario from 2030

to 2060. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 100
S64 Provincial installations (GW) of concentrating solar power in 2060 in the base
scenario. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 100
S65 Installed capacity (GW) of wind and solar PV in the “VRECluster” scenario. . . . 101
S66 Capacity expansion of hydropower in the base scenario from 2030 to 2060. . . . . . 101
S67 Energy stored (GWh) at the national level from 2030 to 2060 in the base scenario. 102
S68 Net energy flow (TWh/yr) alongside inter-regional transmission line from 2030 to
2060 in the base scenario. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 103
S69 Capacity (GW) of trunk lines connecting substations to load centers for VRE
integration in the base scenario from 2030 to 2060. . . . . . . . . . . . . . . . . . . 104


4


LIST OF TABLES


S70 Cumulative distribution of total distance (km) of spur and trunk lines that connect solar cells (weighted by planned capacity) to their corresponding load center
(distributed solar is modeled with zero connection distance) from 2030 to 2060 in
the base scenario. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 104
S71 Density distribution of hourly marginal cost (yuan/kWh) of generation from 2030
to 2060 in the base scenario. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 105
S72 Density distribution of hourly marginal cost (yuan/kWh) of generation from 2030
to 2060 in the “NegEmis200Mt” scenario. . . . . . . . . . . . . . . . . . . . . . . . 105
S73 Density distribution of hourly marginal cost (yuan/kWh) of generation from 2030
to 2060 in the “NegEmis700Mt” scenario. . . . . . . . . . . . . . . . . . . . . . . . 105
S74 Density distribution of hourly marginal cost (yuan/kWh) of generation from 2030
to 2060 in the “NoEmisCap” scenario. . . . . . . . . . . . . . . . . . . . . . . . . . 105
S75 Density distribution of hourly marginal cost (yuan/kWh) of generation from 2030
to 2060 in the “LimitedBECCS” scenario. . . . . . . . . . . . . . . . . . . . . . . . 106
S76 Density distribution of hourly marginal cost (yuan/kWh) of generation from 2030
to 2060 in the “WithoutBECCS” scenario. . . . . . . . . . . . . . . . . . . . . . . . 106
S77 Density distribution of hourly marginal cost (yuan/kWh) of generation from 2030
to 2060 in the “VRELandOpen” scenario. . . . . . . . . . . . . . . . . . . . . . . . 106
S78 Density distribution of hourly marginal cost (yuan/kWh) of generation from 2030
to 2060 in the “VRELandConservative” scenario. . . . . . . . . . . . . . . . . . . . 106
S79 Density distribution of hourly marginal cost (yuan/kWh) of generation from 2030
to 2060 in the “Capex1.25X” scenario. . . . . . . . . . . . . . . . . . . . . . . . . . 107
S80 Distribution of hourly marginal cost (yuan/kWh) of generation from 2030 to 2060
in the “Demand1.25X” scenario. . . . . . . . . . . . . . . . . . . . . . . . . . . . . 107

S81 Nationwide power demand and supply profile in the base scenario from 2030 to 2060.108

## **List of Tables**


S1 Province names, abbreviations, and corresponding grid region. . . . . . . . . . . . 2
S2 Comparisons of power system expansion models. . . . . . . . . . . . . . . . . . . . 3
S3 Technical parameters of the wind turbine selected by CISPO. . . . . . . . . . . . . 5
S4 Threshold values that are unsuitable for developing wind and utility-scale solar PV
power considering natural conditions. C: Conservative, B: Base, O: Open. . . . . . 13
S5 Suitability factor of each land use type for determining suitable areas for onshore
wind and utility-scale solar. C: Conservative, B: Base, O: Open. . . . . . . . . . . 16
S6 Land use classification and definition used in this study. . . . . . . . . . . . . . . . 17
S7 Onshore wind turbine parameters. . . . . . . . . . . . . . . . . . . . . . . . . . . . 24
S8 Offshore wind turbine parameters. . . . . . . . . . . . . . . . . . . . . . . . . . . . 24
S9 Provincial installation capacity potential (GW) of onshore wind in mainland China. 25
S10 Provincial installation capacity potential (GW) of onshore wind in mainland China. 26
S11 Provincial installation capacity potential (GW) of UPV and DPV in mainland China. 28
S12 Provincial installed capacity (GW) of distributed solar by 2022 [[7]] . . . . . . . . . . 40
S13 Provincial installed capacity (GW) of biomass energy power by 2022. . . . . . . . . 49
S14 Technical parameters for thermal and nuclear power. . . . . . . . . . . . . . . . . . 52
S15 Installed (by 2022) and under construction pumped hydro storage capacity (GW)
for each province. Under-construction projects with an expected production year
before the target year are taken as installed capacity. These capacities are the
lower bound of PHS deployment in each optimization year. . . . . . . . . . . . . . 55
S16 Installed capacity (GW) of BAT for top 10 provinces by 2022. . . . . . . . . . . . . 55
S17 Provincial battery storage deployment target (GW) by 2025. . . . . . . . . . . . . 55


5


LIST OF TABLES


S18 Long-term potential capacity (GW, including installed capacity) of the PHS by
province in mainland China. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 56
S19 Performance parameters for storage technologies. . . . . . . . . . . . . . . . . . . . 57
S20 CapEx assumptions of substations and overhead lines. . . . . . . . . . . . . . . . . 58
S21 Inter-grid transmission corridor options for newly expanding. . . . . . . . . . . . . 60
S22 Cost (in 2022) and technical parameters for direct air capture. . . . . . . . . . . . 62
S23 Cost projection (ratio to 2022) from 2030 to 2060. . . . . . . . . . . . . . . . . . . 62


6


S1 INTRODUCTION

## **S1 Introduction**


The China Integrated Sustainable Power-system Optimization Model (CISPO) is designed to sim

ulate the dynamic changes and hourly operations within China’s power systems resulting from


novel investments in power generation, storage, and transmission spanning the target period (e.g.,


from 2030–2060) within a given optimization step (e.g., 10 years). Within each planning year


interval, the CISPO optimizes the least-cost portfolio, considering input assumptions related


to future electricity demand, investment costs, technology performance parameters, planning


and operating reserves, inertia requirements, and energy availability factors including installa

tion capacity potential and hourly generation profiles. The technologies incorporated into the


CISPO model encompass variable renewable energies (VREs), including onshore and offshore


wind, utility-scale and distributed solar photovoltaic (PV), concentrating solar power (CSP), hy

dropower, thermal power (coal, natural gas, and biomass energy), nuclear power, battery storage,


pumped hydro storage (PHS), and both intra-grid and inter-grid transmission (alternating cur

rent (AC) and direct current (DC)). The data exchange across planning years encompasses the


installed and retired capacity of generation. We show the model framework in Figure S1.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-7-0.png)


Figure S1: Framework of the CISPO model.


**S1.1** **Regions and grids**


In this model, we optimize various parameters at the provincial level, including power balance,


electricicty flow, energy storage deployment, dispatch route, and unit commitment of thermal and


1


S1 INTRODUCTION


nuclear power, while also addressing grid safety requirements (inertia and reserve requirements).


The model encompasses a total of 32 provinces, with Inner Mongolia divided into two regions,


namely Mengdong and Mengxi, due to their different grid region connecting. Throughout the


model formulation section, these provinces are denoted as the power grid, refer to Table S1 and


Figure S2 for an overview of the regional division and their corresponding designations. VREs


are optimized at the cell level (0.25° _×_ 0.25°, approximately 25 _×_ 25 km at middle altitude in this


study) for both capacity expansion and power dispatch. Additionally, these for hydropower are


considered at the dam site level.


Table S1: Province names, abbreviations, and corresponding grid region.


Province Abbr. Grid region Province Abbr. Grid region Province Abbr. Grid region


Anhui AH East Hainan HI South Sichuan SC Central

Beijing BJ North Heilongjiang HL Northeast Shandong SD North
Chongqing CQ Central Hunan HN Central Shanghai SH East
Fujian FJ East Jilin JL Northeast Shaanxi SN Northwest
Guangdong GD South Jiangsu JS East Shanxi SX North
Gansu GS Northwest Jiangxi JX Central Tianjin TJ North
Guangxi GX South Liaoning LN Northeast Xinjiang XJ Northwest
Guizhou GZ South Mengdong [a] MD Northeast Xizang XZ Xizang
Henan HA Central Mengxi [a] MX North Yunnan YN South
Hubei HB Central Ningxia NX Northwest Zhejiang ZJ East
Hebei HE North Qinghai QH Northwest


a Inner Mongolia is split into two regions (Mengdong and Mengxi, also denoted as East and West Inner
Mongolia) as they belong to two different grid regions.



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-8-0.png)

















































Figure S2: Region division in this model.


2


S1 INTRODUCTION


**S1.2** **Model comparison**


Power system expansion models (PSEMs) have been widely used in previous practices for en

ergy system analysis. Generally, PSEMs are expressed as linear programming (LP) problems to


minimize the total system costs subject to various engineering, social, and economic constraints.


Limited by the computing complexity, previous models usually adopt the two-stage optimization


schema to expand capacity deployment of generators using tens to hundreds of typical hours, and


then optimize system operation for a longer temporal coverage (e.g., 8,760 hours). The CISPO


model here co-optimizes the capacity expansion and system operation throughout 8,760 hours of


a planning year to fill the modeling gap for better integrating weather-dependent renewable ener

gies. Table S2 compares our model with previous PSEMs about China, focusing on a) temporal


resolution and coverage, b) technologies and simulation methods, and c) security constraints.


Table S2: Comparisons of power system expansion models.


Model CISPO RESPO [[8]] Chen et al. [[6]] Zhuo et al. [[9]] Li et al. [[10]] SWITCH-China [[11]] Fan et al. [[12]] REPO [[13]]


Base year 2022 2021 2019 2020 2020 2020 2020 2020


End year 2060 2060 2050 2050 2060 2030 2060 2035


Temporal resolution 1 hour 1 hour 1 hour 1 hour 1 hour 1 hour 1 hour 1 hour


Temporal coverage (hours) for expansion decision 8,760 8,760 hundreds 288 192 576 8,760 72


National
Spatial resolution for balancing Provincial Provincial Provincial Provincial Provincial Provincial (one node) Provincial


Onshore wind Resolution Cell (0.25°×0.25°) Cell (0.3125°×0.25°) Provincial Provincial Provincial Provincial Provincial Cluster


Resource limit Yes Yes Yes Yes Yes Yes Yes Yes


Offshore wind Resolution Cell (0.25°×0.25°) Cell (0.3125°×0.25°) Provincial Provincial Provincial Provincial Provincial Cluster



Spatial resolution and
resource limits for

renewable energy


Simulation method for

thermal and nuclear power



Resource limit Yes Yes Yes Yes Yes Yes Yes Yes


UPV Resolution Cell (0.25°×0.25°) Cell (0.3125°×0.25°) Provincial Provincial Provincial Provincial Provincial Cluster


Resource limit Yes Yes Yes Yes Yes Yes Yes Yes


DPV Resolution Cell (0.25°×0.25°) Cell (0.3125°×0.25°) Provincial Provincial Provincial Provincial Provincial Cluster


Resource limit Yes Yes No No No No No Yes


CSP Resolution Cell (0.25°×0.25°) No No Provincial Provincial Provincial No Provincial


Resource limit Yes No No No No No No NO


Provincial Dam site
Resolution Dam site Provincial Provincial Project Provincial Provincial
Hydropower (no expansion) (no expansion)


Resource limit Yes No No No No No No No


Biomass Resolution Cell (1km×1km) No No No No No Cell (1km×1km) No


Resource limit Yes No No No No No Yes No


Nuclear RUC Layer RUC RUC RUC RUC Layer RUC


Coal RUC No RUC RUC RUC RUC Layer RUC


Coal CCS RUC Layer RUC RUC No No Layer RUC


Coal CHP RUC No RUC No No No Layer RUC


Coal CHP CCS RUC Layer No No No No Layer RUC


Gas RUC No RUC RUC RUC RUC Layer RUC


Gas CCS RUC Layer No RUC No No Layer RUC


Gas CHP RUC No No No No No Layer RUC


Gas CHP CCS RUC Layer No No No No Layer RUC


Biomass RUC No No RUC No No Layer RUC


Biomass CCS RUC Layer No RUC No No Layer RUC



Inter-regional AC Yes Yes Yes Yes Yes Yes No Yes
Transmission Inter-regional DC Yes Yes Yes Yes Yes Yes No Yes



Carbon capture and
sequestration



Intra-regional connection cost Yes Yes No No No No No No

Direct air capture withcarbon sequestration (DACCS) Yes No No No No No No No


Endogenous carbon Yes No No No No No No No
source-sink match


Sequestration Yes No No No No No Yes No
potential constraint



Spinning reserve Yes Yes Yes Yes Yes Yes Yes Yes
Security constraints Inertia requirement Yes No No Yes No No Yes No


Capacity reserve Yes No Yes Yes Yes Yes Yes Yes


**Abbreviation notes** UPV: utility-scale solar PV; DPV: distributed solar PV; CSP: concentrating solar power;
CCS: carbon capture and sequestration; CHP: combined heat power; AC: alternative current; DC: direct
current; RUC: relaxed unit-commitment, or fast unit-commitment [[6]] .


3


S2 ENERGY RESOURCE ASSESSMENT

## **S2 Energy Resource Assessment**


**S2.1** **Assessment of wind and solar power**


This section assesses the resource availability of onshore and offshore wind, utility-scale and dis

tributed solar photovoltaic (PV), and concentrating solar power (CSP). The resource assessment


of wind and solar power consists of two main components. First is the hourly generation po

tential, also referred to as the capacity factor (CF _∈_ [0,1]), which represents the ratio between a


generator’s available output and its nameplate capacity within a one-hour time frame. Second is


the estimate of the maximum installation capacity potential (MW) for each grid cell.


The hourly generation potential of wind and solar power is assessed based on simulation models


and meteorological conditions, encompassing wind speed, temperature, surface shortwave solar


radiation, and direct short-wave radiation. The maximum installation capacity potential refers


to the utmost suitable installation capacity for a given cell, which is determined by installation


density (MW/km [2] ) and suitable development area (km [2] ). For wind, utility-scale solar PV, and


CSP, we estimate the suitable surface land area. For distributed solar PV power, specifically


rooftop solar PV in this study, we consider the suitability of the rooftop area. We show the


assessment procedure in Figure S3.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-10-0.png)


Figure S3: Assessment process for wind and solar power.


4


S2 ENERGY RESOURCE ASSESSMENT


**S2.1.1** **Assessment of the hourly capacity factor for wind power**


The hourly capacity factor of wind power is determined by its power output curve and the wind


speed at the turbine hub height. The power output curve, along with other technical parameters


is the key to assessing the hourly capacity factor for wind power. The National Renewable Energy


Laboratory (NREL) provides technical parameters for various wind turbine models in their wind


energy assessment project, including nameplate capacity, cut-in, and cut-out wind speeds, rated


wind speed, turbine hub height, and power output curve [[1]] . The hourly capacity factor of onshore


and offshore wind power is evaluated using the 2020 ATB NREL Reference 5.5 MW 175 and IEA


15MW 240 RWT turbine models respectively, with the key parameters presented in Table S3. The


power output curve of a wind turbine is a piecewise function based on the wind speed ( _v_ _w_ _[h]_ [, m/s)]


at the turbine hub height. No power generation occurs when _v_ _w_ _[h]_ [is below the cut-in or above the]


cut-out wind speed. When the wind speed is between the cut-in and rated wind speeds, the power


generation of the turbine exhibits a monotonically increasing behavior to the wind speed. On


reaching or exceeding the rated wind speed but remaining below the cut-out wind speed, power


generation by the turbine operates at its nameplate capacity. We first normalize the power output


curve of wind turbines to assess the hourly capacity factor of wind power. Figure S4 presents


normalized power output curves for various turbine types provided by NREL [[1]] . Considering that


the uncertainty lies solely within the segment from cut-in to rated wind speed in the power output


curve, we employ a third-degree polynomial fitting method to establish a functional relationship


within this range. The results of this fitting process are shown in Figure S5.


Table S3: Technical parameters of the wind turbine selected by CISPO.


2020ATB NREL Reference 5.5MW 175 IEA 15MW 240 RWT


nameplate capacity (MW) 5.5 15.0
hub height (m) 120.0 150.0
cut-in wind speed (m/s) 3.25 3.00
cut-out wind speed (m/s) 25.0 25.0
rated wind speed (m/s) 10.0 10.6


5


S2 ENERGY RESOURCE ASSESSMENT





|1.0<br>0.8<br>(0~1)<br>nameplate<br>0.6<br>output /<br>0.4 20<br>20<br>20 Power<br>20<br>20<br>0.2 2 20 0<br>20<br>DT<br>IE<br>IE<br>0.0 L NE R<br>0 5 10|16CACost NREL Reference 10MW 205<br>16CACost NREL Reference 6MW 155<br>16CACost NREL Reference 8MW 180<br>19ORCost NREL Reference 12MW 222<br>19ORCost NREL Reference 15MW 248<br>20ATB NREL Reference 12MW 214|
|---|---|
|0<br>5<br>10<br>0.0<br>0.2<br>0.4<br>0.6<br>0.8<br>1.0<br>Power output / nameplate (0~1)<br>20<br>20<br>20<br>20<br>20<br>20<br>20<br>20<br>DT<br>IE<br>IE<br>LE<br>NR|16CACost NREL Reference 10MW 205<br>16CACost NREL Reference 6MW 155<br>16CACost NREL Reference 8MW 180<br>19ORCost NREL Reference 12MW 222<br>19ORCost NREL Reference 15MW 248<br>20ATB NREL Reference 12MW 214<br>|
|0<br>5<br>10<br>0.0<br>0.2<br>0.4<br>0.6<br>0.8<br>1.0<br>Power output / nameplate (0~1)<br>20<br>20<br>20<br>20<br>20<br>20<br>20<br>20<br>DT<br>IE<br>IE<br>LE<br>NR|15<br>20<br>25<br>Wind speed (m/s)<br>20ATB NREL Reference 15MW 240<br>20ATB NREL Reference 18MW 263<br>U 10MW 178 RWT v1<br>A 10MW 198 RWT<br>A 15MW 240 RWT<br>ANWIND 8MW 164 RWT<br>EL 5MW 126 RWT|


(b) Offshore wind power



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-12-0.png)





(a) Onshore wind power



Figure S4: Normalized power output curve of different wind power models [[1]] .



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-12-1.png)





|1.0 Power curve Sample point|Col2|
|---|---|
|5<br>10<br>0.0<br>0.2<br>0.4<br>0.6<br>0.8<br><br>Power output / nameplate (0~1)<br>Sample point<br>Fit curve||
|5<br>10<br>0.0<br>0.2<br>0.4<br>0.6<br>0.8<br><br>Power output / nameplate (0~1)<br>Sample point<br>Fit curve|15<br>20<br>25<br>Wind speed (m/s)|


(b) Offshore wind power



(a) Onshore wind power



Figure S5: Illustration of the fitting results of the normalized power output curve for the selected
wind turbine models.


Wind speed at the turbine hub height is a critical input for evaluating the capacity factor


of wind power. We use the original meteorological data from the European Centre for Medium

Range Weather Forecasts Reanalysis Version 5 (ERA5) dataset, which provides a comprehensive


record of global climate and weather spanning over eight decades with a spatial resolution of 0.25°


_×_ 0.25° and hourly temporal resolution [[14]] . The ERA5 dataset includes wind speed data at 10


m and 100 m heights (U10m, V10m, U100m and V100m, m/s). As shown in Table S3, the hub


heights of the selected turbine models in this study are 120 m and 150 m for onshore and offshore


wind, respectively. Therefore, we use the vertical power law profile to estimate the target height


wind speed based on the 10 m and 100 m wind speed data. The wind vertical power law profile


is expressed as:



_v_ _w_ _[h]_ [=] _[ v]_ _w_ [100] _×_ ( _[z]_ _[h]_ ) _[α]_ _,_ (S2-1)

_z_ 100



where _v_ _w_ _[h]_ [is the wind speed at height] _[ h]_ [ (m/s),] _[ v]_ _w_ [100] is the wind speed at 100 m height (m/s) which



is calculated using _v_ _w_ [100] = ~~�~~ _U_ 100 ~~[2]~~ [+] _[ V]_ 100 ~~[2]~~ [,] _[ z]_ _[h]_ [ is the target height (120 m for onshore wind and 150]


6


S2 ENERGY RESOURCE ASSESSMENT


m for offshore wind), and _z_ 100 = 100 m, _α_ is wind shear coefficient which is a parameter varying


as a function of the terrain where wind farms are located. We apply equation S2-1 based on the


wind speeds at 10 m and 100 m to estimate the value of _α_ for each hour at each 0.25° _×_ 0.25°


grid cell.


The power output curve of a wind turbine is standardized based on measurements made


under conditions of standard air density ( _ρ_ _[h]_ _std_ [=1.225 kg/m] [3] [). Therefore, when calculating power]


output at a given moment using the power output curve, it is necessary to convert the observed


wind speed to the equivalent wind speed under standard air density. The relationship between


wind speed under standard conditions ( _v_ _std_ _[h]_ [) and measured conditions (] _[v]_ _meas_ _[h]_ [) is described by the]


following equation [[15]] :



_h_
_ρ_ _meas_
_v_ _std_ _[h]_ [=] _[ v]_ _meas_ _[h]_ _[×]_
� _ρ_ _[h]_ _std_



1 _/_ 3
_,_ (S2-2)
�



in this equation, _v_ _std_ _[h]_ [is the wind speed at height] _[ h]_ [ under standard air density,] _[ v]_ _meas_ _[h]_ [is the]


measured wind speed calculated using Equation S2-1, and _ρ_ _[h]_ _meas_ [is the actual air density. However,]


ERA5 reanalysis data does not directly provide observed air density. Instead, air density must be


calculated based on other reported meteorological variables using the ideal gas state equation [[15]] :

_ρ_ _[h]_ _meas_ [=] _[p]_ _meas_ _[h]_ _R_ _[−]_ _dry_ _[p]_ _T_ _[va][p][our]_ + _Rp_ _vapourvapour_ _T_ _[,]_ (S2-3)


where _R_ _dry_ is the gas constant for dry air (287.1 J/kg·K), _R_ _vapour_ is the specific gas constant for


water vapor (461.5 J/kg·K), _p_ _[h]_ _meas_ [is the measured atmospheric pressure (Pa) at hub height (h),]


_p_ _vapour_ is the partial pressure (Pa) of water vapor at 2 m height, and _T_ is the temperature at


hub height (K). The partial pressure of water vapor ( _p_ _vapour_ ) can be determined from the relative


humidity of the air (%, _ϕ_ ) and the temperature using the Clausius-Clapeyron equation:



17 _._ 27( _t −_ 273 _._ 15)
_p_ _vapour_ = _ϕ ×_ 610 _._ 78 _×_ exp
� _t −_ 273 _._ 15 + 237 _._ 3



_,_ (S2-4)
�



where _t_ is the temperature at 2 m height. We assume that the partial pressure of water vapor at


hub height is equal to that at 2 m [[15]] . And the air humidity ( _ϕ_ ) can be calculated according to


the dewpoint temperature (K) at 2 m height from the ERA5 data using the equation [[16]] :



17 _._ 625 _×_ ( _t_ _d_ _−_ 273 _._ 15)
_ϕ_ = exp
� 243 _._ 04 + ( _t_ _d_ _−_ 273 _._ 15)



17 _._ 625 _×_ ( _t_ _d_ _−_ 273 _._ 15) [(] _[t][ −]_ [273] _[.]_ [15][)]

243 _._ 04 + ( _t_ _d_ _−_ 273 _._ 15) _[−]_ 243 [17] _[.]_ [625] _._ 04 + ( _[ ×]_ _t −_ 273 _._ 15)



243 _._ 04 + ( _t −_ 273 _._ 15)



_×_ 100% _._ (S2-5)
�



Finally, we calculate the atmospheric pressure (Pa) at hub height ( _h_ ), denoted as _p_ _[h]_ _meas_ [, using]


the surface pressure from ERA5 (Surface pressure, Pa, _p_ 0 ), by:


_−_ _[g][h]_
_p_ _[h]_ _meas_ [=] _[ p]_ [0] _[×][ e]_ _Rdt_ _,_ (S2-6)


where _e_ is the base of the natural logarithm, _g_ = 9.81 m/s [2] is the gravitational acceleration at the


earth’s surface, _t_ is the temperature at 2 m height, and _R_ _d_ is a function of the specific humidity


7


S2 ENERGY RESOURCE ASSESSMENT


of the air ( _q_, kg/m [3] ):

_R_ _d_ = _qR_ _vapour_ + 1 + _[R]_ _[dr]_ _q_ _[y]_ _[.]_ (S2-7)


After wind speed at the hub height under standard air density is obtained through the previ

ously described calculations, the hourly capacity factor for wind power generation can be deter

mined for each 0.25° _×_ 0.25° cell using the normalized power output curve of the wind turbine


model. Additional adjustments to the capacity factor are then made based on the following


considerations:


  - The capacity factor at each timestep is decreased by 5% due to factors such as wake effects


and electrical losses in wind farms [[17,18]] .


  - The wind turbine ceases operation when the temperature at the hub height drops below


°
-30 C due to extreme cold, leading to a correction of the capacity factor to 0 [[15]] .


  - The wind turbine ceases operation when the wind speed surpasses the cut-out threshold


and can only be reactivated upon meeting specific wind speed criteria. CISPO employs a


hysteresis window approach to address this concern, whereby the wind turbine can only


resume operation once the wind speed falls below or equals 20 m/s for the first time after


shutdown. The cut-out threshold of the wind turbine can be found in Table S3.


In the CISPO model, the geographical scope considered for offshore wind power is limited to


China’s Exclusive Economic Zone (EEZ) [[19]] . In Figure S6, we show the annual average capacity


factor ( [�] _cf_ _t_ _/|T_ _|_ ) for both onshore and offshore wind power at each grid cell in 2019.

_t∈T_

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-14-0.png)


Figure S6: Cell level annual average capacity factor (0–1) for onshore and offshore wind power
in 2019.


8


S2 ENERGY RESOURCE ASSESSMENT


**S2.1.2** **Assessment of the hourly capacity factor for solar photovoltaic power**


To quantify the hourly capacity factor of solar PV power at a given grid cell, we implement the


fixed tilt photovoltaic system model [[20]] . The solar PV power output constitutes a fraction of the


nameplate capacity, which can be derived from meteorological data. Specifically, we use the ERA5


hourly meteorological dataset [[14]], which provides measurements of surface downwelling shortwave


radiation (SSRD2M, J/m [2] ), surface temperature (T2m, K), and 10-meter height surface wind


speed (U10m and V10m, m/s) at a 0.25° _×_ 0.25° spatial resolution. Based on these inputs, we


assess the DC power output to the nameplate capacity fraction using the following equation [[21]] :


_P_ _dc_
_P_ _dc_ 0 = [1 + _γ ×_ ( _T_ _cell_ ( _t_ ) _−_ _T_ _std_ )] _×_ _[ssrd]_ _ssrd_ [(] _std_ _[t]_ [)] _× η_ _sys_ _,_ (S2-8)


where _P_ _dc_ _/P_ _dc_ 0 is the fraction of the DC output to the nameplate capacity, _T_ _cell_ ( _t_ ) is the temper

ature of the solar PV module’s cell, _T_ _std_ is the PV panel’s cell temperature under standard test


conditions (25 °C), _γ_ is the temperature coefficient of the solar PV cell, which is set as _−_ 0 _._ 005


°C _[−]_ [1] reflecting the efficiency at different temperatures, _ssrd_ ( _t_ ) is the hourly surface downwelling


shortwave radiation (W/m [2], converting from J/m [2] ) in the environment, which can be derived


from SSRD2M variable in the ERA5 data, _ssrd_ _std_ is the shortwave flux on the solar PV panel


under standard test conditions, defined as 1,000 W/m [2], and _η_ _sys_ _∈_ [0 _,_ 1] is the efficiency of the


DC electric system, set as 0.86 [[21,22]] .


According to previous studies, the ambient temperature ( _T_ _cell_ ( _t_ ), °C) of solar PV cell can


be calculated using the following equation based on surface downwelling shortwave radiation


( _ssrd_ ( _t_ ), W/m [2] ), ambient temperature ( _T_ ( _t_ ), °C), and wind speed ( _v_ _w_ ( _t_ ), m/s) [[23,24]] :


_T_ _cell_ ( _t_ ) = _c_ 1 + _c_ 2 _× T_ ( _t_ ) + _c_ 3 _× ssrd_ ( _t_ ) + _c_ 4 _× v_ _w_ ( _t_ ) _,_ (S2-9)


where _c_ 1 =4.3 °C, _c_ 2 =0.943, _c_ 3 =0.028 °C·m [2] - W _[−]_ [1], _c_ 4 = _−_ 1.528 °C·s·m _[−]_ [1], _T_ ( _t_ ) is the hourly


ambient temperature, and _v_ _w_ ( _t_ ) is the hourly surface wind speed. And then the rearrangement


of equations S2-8 and S2-9 yields:


_P_ _dc_
_P_ _dc_ 0 = [ _α_ 1 _× ssrd_ ( _t_ ) + _α_ 2 _× ssrd_ ( _t_ ) [2] + _α_ 3 _× ssrd_ ( _t_ ) _× T_ ( _t_ ) + _α_ 4 _× ssrd_ ( _t_ ) _× v_ _w_ ] _× η_ _sys_ _,_ (S2-10)


where _α_ 1 = 1 _._ 1035 _×_ 10 _[−]_ [3], _α_ 2 = _−_ 1 _._ 4 _×_ 10 _[−]_ [7], _α_ 3 = _−_ 4 _._ 715 _×_ 10 _[−]_ [6], _α_ 4 = 7 _._ 64 _×_ 10 _[−]_ [6], and the


calculated _P_ _dc_ _/P_ _dc_ [0] [is a dimensionless quantity. The AC output of the PV module can be derived]


according to PVWatts [[22]] by:


_P_ _ac_
= min( _η ×_ _[P]_ _[dc]_ _,_ 1) _,_ (S2-11)
_P_ _ac_ 0 _P_ _ac_ 0


where _P_ _ac_ _/P_ _ac_ 0 is the fraction of the AC output to the nameplate capacity, _P_ _ac_ 0 = _η_ _nom_ _× P_ _dc_ 0 is


the nameplate AC capacity, and _η_ _nom_ = 0 _._ 96 is the nominal DC-AC inverter efficiency, and the


9


S2 ENERGY RESOURCE ASSESSMENT


_η_ is a function of _P_ _dc_ _/P_ _dc_ 0 with the inverting performance given by [[22]] as:




_[nom]_ _×_ ( _−_ 0 _._ 0162 _× ζ −_ [0] _[.]_ [0059]

_η_ _ref_ _ζ_



_η_ = _[η]_ _[nom]_



+ 0 _._ 9858) _,_ (S2-12)
_ζ_



where _ζ_ = _P_ _dc_ _/P_ _dc_ 0, and _η_ _ref_ = 0 _._ 9637 is reference inverter efficiency. Combining formula S2-10,


S2-11, and S2-12, the fraction of the AC output to the nameplate capacity is:


_P_ _ac_ 1
= min _×_ ( _−_ 0 _._ 0162 _× ζ_ [2] + 0 _._ 9858 _× ζ −_ 0 _._ 0059) _,_ 1 _._ (S2-13)
_P_ _ac_ 0 � _η_ _ref_ �


We consider that utility-scale and distributed solar PV within the same cell share the same


capacity factor value. We show the annual average capacity factor for solar PV in 2019 in each


grid cell in Figure S7.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-16-0.png)


Figure S7: Cell level annual average capacity factor (0–1) for solar photovoltaic power in 2019.


**S2.1.3** **Assessment of the hourly capacity factor for concentrating solar power**


Concentrating solar power (CSP) technologies leverage the heat of the sun to power a thermoelec

tric generation cycle. In this study, parabolic trough collector (PTC) is used as the representative


CSP technology to assess the available resources. Trough power plants employ extensive arrays


of solar collectors featuring a reflective parabolic surface to concentrate sunlight onto a receiver


pipe (Figure S8). A heat transfer fluid (HTF), commonly a synthetic oil, circulates through the


receiver and is heated by the absorbed solar irradiation. This heated fluid is then used to generate


high-pressure steam, which drives a conventional steam turbine generator to produce electricity.


The steam exiting the turbine is condensed back into water and recirculated by feedwater pumps


to be converted into high-pressure steam once more.


10


S2 ENERGY RESOURCE ASSESSMENT

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-17-0.png)


Figure S8: Simplified schematic of a parabolic trough power plant. Graphic: © NREL/Parthiv
and Craig [[2]]


Different from solar PV power systems, concentrating solar power technologies harness the


direct normal irradiance (DNI) and use the captured thermal energy by transferring it to a heat


transfer fluid. To evaluate the energy generation potential of CSP systems, we employ the surface


direct shortwave radiation (dsr) data from the ERA5 reanalysis dataset. The generation potential


per unit area (W/m [2] ) at timestep t, denoted as _P_ _t_, is given by the following expression [[25,26]] :


_P_ _t_ = _η_ _R_ _×_ ( _k_ 0 _× dsr_ _t_ _−_ _k_ 1 _×_ ( _T_ _f_ _−_ _T_ _ai_ )) (S2-14)


where _η_ _R_ represents the Rankine cycle efficiency (assumed to be 40%), _k_ 0 is 0.762, _dsr_ _t_ is the


direct shortwave radiation (W/m [2] ), _k_ 1 is 0.2125 W/(m [2] °C [1] ), _T_ _f_ is the temperature of the fluid


in the absorber, and _T_ _ai_ is the ambient temperature. The quantity _P_ _t_ assessed here corresponds


to the technical generation potential using a PTC system. Based on the installed projects and


the projected technology improvements, we set the maximum generation capacity of the per-area


PTC collector at 100 W/m [2], denoted as _P_ _i_, and estimate the capacity factor as:


_cf_ _t_ = min( _[P]_ _[t]_ _,_ 1) _._ (S2-15)

_P_ _i_


This equation constrains the maximum generation of the PTC system to the installed nameplate


capacity. We show the annual average capacity factor of the PTC system at the cell level in


Figure S9.


11


S2 ENERGY RESOURCE ASSESSMENT

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-18-0.png)


Figure S9: Cell level annual average capacity factor (0–1) for concentrating solar power in 2019.


**S2.1.4** **Assessment of suitable area for developing wind, utility-scale solar photo-**
**voltaic power and concentrating solar power**


We estimate the installation capacity potential of wind and solar power based on the suitable


land area and assumed installation density [[27,28]] for each 0.25° _×_ 0.25° grid cell, consistent with


the resolution of the ERA5 meteorological data. To evaluate the suitable land area for developing


wind and solar PV power, we employ the China Land Use/Cover Change Dataset (CNLUCC) for


2020 [[29]], provided by the Resource and Environmental Science and Data Center of the Chinese


Academy of Sciences (RESDC), which is represented at the “pixel” level within each grid cell. The


CNLUCC land use dataset adopts a two-level classification system. Level 1 categorizes land into


six primary types: cropland, forest land, grassland, inland water areas, urban and built-up land,


and unused land. Level 2 further delineates these into 24 specific land use types. Definitions for


each land use type are provided in Table S6, and the distribution of each land use type is shown in


Figure S10. We initially exclude the pixels within each grid cell that are situated in nature reserves


and biodiversity reserve areas due to environmental protection reasons [[30,31]] . Subsequently, we


formulate three scenarios (open, base, and conservative) to further eliminate the remaining pixels


that do not meet natural condition constraints such as steep slopes, high altitudes, and water


depths. Additionally, for offshore wind applications, pixels located within shipping lanes are also


omitted for safety considerations. Following these steps, the suitability factor is devised for the


three scenarios to estimate the suitable area within the remaining pixels based on land use types


12


S2 ENERGY RESOURCE ASSESSMENT


to represent different policy requirements. Finally, we aggregate the suitable area of qualified


pixels in each grid cell to determine the total suitable area for developing wind, utility-scale solar


PV power, and concentrating solar power.







![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-19-1.png)

Figure S10: Land use distribution in RESDC CNLUCC dataset.


The threshold values [1] for the natural conditions are presented in Table S4. For the shipping


lanes in these three scenarios, offshore pixels with shipping density larger than the threshold


are excluded. We use the shipping density data from World Bank [[35]], which is derived from


IMF’s analysis of hourly AIS positions received between Jan-2015 and Feb-2021, representing


the total number of AIS positions reported by ships in each grid cell at 0.005° _×_ 0.005° resolution


(approximately a 500 m _×_ 500 m grid cell at the Equator) [[35]] . Following the recommendations


from the previous study [[36]], we adopt the historical 90th, 85th, and 80th percentile shipping


density to identify the shipping lanes for open, base, and conservative scenarios, respectively, to


filter out the unsuitable pixels. The corresponding absolute values for these percentiles are about


400, 235, and 104 ships per hour.


Table S4: Threshold values that are unsuitable for developing wind and utility-scale solar PV
power considering natural conditions. C: Conservative, B: Base, O: Open.


Natural condition Onshore wind Offshore wind Utility-scale solar CSP


C B O C B O C B O C B O
Slope (%) _>_ 15 _>_ 20 _>_ 25 - - - _>_ 3 _>_ 5 _>_ 7 _>_ 3 _>_ 5 _>_ 7
Altitude (m) _>_ 3000 - - - - - - - - - - Water depth (m) - - - _>_ 40 _>_ 60 _>_ 100 - - - - - 

After excluding pixels that are located in nature reserves or biodiversity conservation regions,


and do not satisfy natural condition constraints or safety requirements for shipping, we estimate


1 Data source: The slope data is from OpenTopography [32], altitude data is from MERIT [33] and water depth
data is from GEBCO [[34]] .


13


S2 ENERGY RESOURCE ASSESSMENT


the suitable area of the remaining pixels by designing the suitability factor, which is defined as


the ratio of the suitable area for deployment to the total area of a pixel, for each land use type


according to policies and previous studies [[8,27,28,37,38]] . Finally, we determine the suitable land


area for each grid cell by aggregating suitable areas of all the pixels within it.


The Chinese government has advocated for the widespread adoption of renewable energy


sources, including wind, solar PV, and CSP, particularly on underutilized land parcels designated


as level 1 ID 6 (unused land) in the CNLUCC dataset. In 2019, the Ministry of Natural Resources


issued a mandate emphasizing the imperative utilization of unused land for infrastructural de

velopment [[39]] . This directive was further reinforced in 2023, when the Ministry reaffirmed its


endorsement of repurposing both unused land and extant construction plots for the advancement


of photovoltaic power generation [[40]] . Because of the land-intensive nature of utility-scale solar PV


and CSP installations, as compared to the smaller footprints of wind turbines, lower suitability


factors are assigned to utility-scale solar PV and CSP (40%) projects than onshore wind (80%)


installations in the unused land type. Since the lands used in the installed CSP projects are pri

marily desert and state-owned unused lands [[41]], the authors have selected only the unused land


(level 1 ID 6) for developing CSP. Additionally, we have constrained the sum of the suitability


factors for solar PV and CSP in the same land use type to be less than 100%.


The Chinese government has implemented strict regulations to protect permanent basic farm

land in order to ensure food security. Article 35 of China’s Land Administration Law stipulates


that no organization or individual may occupy or change the use of permanent basic farmland


without authorization [[42]] . Similarly, the 2019 Guidelines for Implementing Industrial Land Poli

cies issued by the Ministry of Natural Resources prohibit the construction of land on cropland


and mandate the strict protection of permanent basic farmland [[39]] . This directive was further


reinforced in the 2023 Notice on Supporting the Development of Photovoltaic Power Generation


Industry, which explicitly states that permanent basic farmland shall not be occupied for any


purpose [[40]] . Due to the lack of public information regarding the detailed boundaries of per

manent basic farmland, assumptions must be made based on the CNLUCC data. We consider


paddy/irrigated cropland, which includes land equipped with reliable water sources and irrigation


facilities, to be consistent with the second category of permanent basic farmland. Accordingly,


the suitability factors for wind and solar development on paddy/irrigated cropland are set to 0%.


For dry cropland, however, current policies do not strictly prohibit wind or solar PV development


if the land is not classified as permanent basic farmland. A conservative assumption is made,


setting the suitability factor for solar at 5% in the base case, as solar PV panels require a sig

nificant land area. In contrast, wind turbines occupy a much smaller footprint (approximately


0.1% of the total wind farm area), and their impact on farming is limited, leading to a base case


14


S2 ENERGY RESOURCE ASSESSMENT


suitability factor of 80% for onshore wind development on dry cropland.


We set corresponding suitability factors based on canopy closure of forested areas and percent


cover of grasslands, respectively [[8]] . In 2015, the State Forestry Administration issued a notice


prohibiting wind or solar PV development in nature reserves, forest parks (including national


parks), habitats of endangered species, natural forest protection project areas, and state-owned


forests in Northeast China and Inner Mongolia [[30]] . In 2019, the State Forestry and Grassland


Administration further stipulated strict protection for ecologically important and fragile forest


areas, leading to the exclusion of “closed forest land” (level 2 ID 21 in CNLUCC data) from


consideration [[31]] . Additionally, the Grassland Law of the People’s Republic of China (Article


38) states that mining and engineering construction should avoid or minimize the occupation


of grasslands, resulting in the exclusion of “high coverage grassland” and “moderate coverage


grassland” (level 2 ID 31 and 32 in CNLUCC data) [[43]] . For the remaining forest land and


grassland types, the suitability factor is set at 80% for onshore wind and 5% and 20% for utility

scale solar PV, respectively.


The suitability factor for inland water bodies is set at 0% for the reason that the Guiding


Opinions on Strengthening the Spatial Regulation and Control of River and Lake Shorelines issued


by the Ministry of Water Resources in 2022 stipulate that solar PV power stations, wind power


projects, and other constructions shall not be built within river channels, lakes or reservoirs [[44]] .


In urban and built-up areas, the suitability factors for onshore wind were set to 0% considering


noise and greater visual impact concerns. For solar PV, a more conservative assumption was made,


setting the suitability factor at 5% in the base case, as solar PV can potentially be deployed on


some industrial, mining, and transportation land types.


The detailed suitability factor values for each land use type in three scenarios are listed in


Table S5. And the resulting suitable area for developing wind, utility-scale solar power, and


concentrating solar power in each grid cell is shown in Figure S11.


15


S2 ENERGY RESOURCE ASSESSMENT


Table S5: Suitability factor of each land use type for determining suitable areas for onshore wind
and utility-scale solar. C: Conservative, B: Base, O: Open.

Level 1 Level 2 Onshore wind (%) Utility-scale solar (%) CSP (%)
C B O C B O C B O

ID Name ID Name - - - - - - - - 
1 Cropland - - - - - - - - - -  -  - 11 Paddy/irrigated cropland 0 0 0 0 0 0 0 0 0
 -  - 12 Dry cropland 60 80 100 0 5 10 0 0 0
2 Forest land - - - - - - - - - - 
 -  - 21 Closed forest land 0 0 0 0 0 0 0 0 0

 -  - 22 Shrubland 60 80 100 0 5 10 0 0 0

 -  - 23 Open forest land 60 80 100 0 5 10 0 0 0
 -  - 24 Other forest land 60 80 100 0 5 10 0 0 0

3 Grassland - - - - - - - - - - 
 -  - 31 High coverage grassland 0 0 0 0 0 0 0 0 0
 -  - 32 Moderate coverage grassland 0 0 0 0 0 0 0 0 0
 -  - 33 Low coverage grassland 60 80 100 15 20 25 0 0 0
4 Inland water area - - - - - - - - - - 
 -  - 41 Rivers and streams 0 0 0 0 0 0 0 0 0

 -  - 42 Lakes 0 0 0 0 0 0 0 0 0

 -  - 43 Reservoirs and ponds 0 0 0 0 0 0 0 0 0
 -  - 44 Permanent ice and snow 0 0 0 0 0 0 0 0 0

 -  - 45 Tidal flats 0 0 0 0 0 0 0 0 0
 -  - 46 River/lake shoals 0 0 0 0 0 0 0 0 0
5 Urban and built-up land - - - - - - - - - -  -  - 51 Urban land 0 0 0 0 5 10 0 0 0

 -  - 52 Rural settlements 0 0 0 0 5 10 0 0 0

 -  - 53 Other construction land 0 0 0 0 5 10 0 0 0

6 Unused land - - - - - - - - - - 
 -  - 61 Sandy land 80 90 100 30 40 50 30 40 50
 -  - 62 Gobi land 80 90 100 30 40 50 30 40 50

 -  - 63 Saline-alkali land 80 90 100 30 40 50 30 40 50

 -  - 64 Wetland 80 90 100 30 40 50 30 40 50

 -  - 65 Bare land 80 90 100 30 40 50 30 40 50

 -  - 66 Bare rock 80 90 100 30 40 50 30 40 50

 -  - 67 Other unused land 80 90 100 30 40 50 30 40 50


16


S2 ENERGY RESOURCE ASSESSMENT



17


S2 ENERGY RESOURCE ASSESSMENT

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-24-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-24-1.png)


(a) wind open (b) wind base

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-24-2.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-24-3.png)


(c) wind conservative (d) utility-scale solar open

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-24-4.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-24-5.png)


(e) utility-scale solar base (f) utility-scale solar conservative


Figure S11: Suitable area (km [2] ) for wind (a, b, and c) and utility-scale solar PV (d, e, and f) in
open, base, and conservative scenarios.


18


S2 ENERGY RESOURCE ASSESSMENT

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-25-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-25-1.png)


(a) Open (b) Base

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-25-2.png)


(c) Conservative


Figure S12: Suitable area (km [2] ) for concentrating solar power in open (a), base (b), and conservative (c) scenarios.


**S2.1.5** **Assessment of suitable area for developing distributed solar photovoltaic**

**power**


Distributed solar photovoltaic (DPV) systems in this study also refer to building-integrated pho

tovoltaic (BIPV). Different from assessments of suitable land areas for wind and utility-scale solar


PV power, estimating the suitable area for DPV requires first approximating the total rooftop


area available in each grid cell. While previous work has shown that rooftop areas can be ac

curately determined by integrating remote sensing imagery with deep learning computer vision


algorithms [[45]], obtaining precise rooftop area estimates within an acceptable timeframe remains


challenging. To address this, prior studies [[46,47]] have demonstrated the feasibility of using an XG

Boost regression model to predict rooftop area based on built-up area, population, road length,


and night lights in each grid cell. The predictions from this modeling framework, as illustrated


in Figure S13, have shown acceptable errors compared to the rooftop areas recognized using CV


algorithm [[46,47]] .


19


S2 ENERGY RESOURCE ASSESSMENT

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-26-0.png)


Figure S13: Framework for rooftop area assessment using XGBoost regression model.


In this framework, we initially divide the global landmass into fishnet cells with a spatial


resolution of 0.125° _×_ 0.125°, ensuring that each ERA5 grid cell (0.25° _×_ 0.25°) encompasses four


corresponding fishnet cells. Subsequently, we collect publicly accessible datasets, including global


land cover raster data (with a resolution of 10 m) from the Environmental Systems Research


Institute (ESRI) [[48]], population distribution raster data (with a resolution of 100 m) from World

Pop [[49]], VIIRS global night lights raster data (with a resolution of 500 m) [[50]], and vectorized


road network and building footprints data from Microsoft AI [[51]] . For cells in China specifically,


vectorized roads and rooftops data are from Open Street Map (OSM) [[52]] and another dataset


containing rooftop information for 90 cities in China [[45]] due to the absence in the Microsoft AI


dataset. Based on the fishnet cells and datasets, we use ArcGIS to calculate the built-up area


(BA), population (POP), and average night light (NL) within each fishnet cell. Additionally, we


determine the road length (RL) by measuring the total length of vectorized roads and assess the


rooftop area (RA) by calculating the total area of building footprints within each fishnet cell’s


built-up area. Figure S14 shows the selected portions of built-up areas, roads, and building foot

prints within Bologna, Italy’s fishnet cells. The yellow regions with gray borders represent blocks


comprising built areas, while red regions and brown lines denote actual buildings and roads.


20


S2 ENERGY RESOURCE ASSESSMENT

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-27-0.png)


Figure S14: Illustration of Roof Vector Data Area.


We then divide the processed dataset into training and prediction sets based on the rooftop


data. For those fishnet cells with vectorized rooftop data and also located within the built-up


area, we use these cells as training and validation data. The remaining fishnet cells are divided


into the prediction set and will be predicted by the trained regression model. Before training the


regression model, we first examine the correlation coefficient between rooftop area and road length,


built-up area, population and night lights, and results are consistent with previous studies [[46,47]] .


We show the correlation coefficients in Figure S15.


The machine learning (ML) model, based on the XGBoost algorithm, utilizes BA, POP, RL,


and NL as independent variables and RA as the dependent variable. It is then trained and tested


using data from fishnet cells that have positive BA and RA values. Before model training, missing


values of POP, RL, and NL are interpolated through iterative multiple regression imputation.


After tuning hyperparameters via 10-fold cross-validation, the ML model achieves an accuracy


score (R [2] value) of 0.865 on the test data. The rooftop area (RA) for both the training set cells


and all evaluated fishnet cells are shown in Figure S16.


Finally, we adopt the appropriate area for developing distributed solar PV power in each


fishnet cell as 0.40, 0.35 [[47]], and 0.30 in the open, base, and conservative scenarios respectively.


The suitable area within each ERA5 land grid cell was obtained by aggregating the corresponding


fishnet cells and is illustrated in Figure S17.


21


S2 ENERGY RESOURCE ASSESSMENT

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-28-0.png)


Figure S15: Correlation coefficient between rooftop area and road length (A), built-up area (B),
population (C), and night lights (D).

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-28-1.png)


Figure S16: Rooftop area (km [2] ) of training data (A) fed into XGBoost model, and predicted
results (B) by the trained XGBoost model.


22


S2 ENERGY RESOURCE ASSESSMENT

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-29-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-29-1.png)


(a) Open (b) Base

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-29-2.png)


(c) Conservative


Figure S17: Suitable area (km [2] ) for building distributed solar PV in each grid cell in open (a),
base (b), and conservative (c) scenario.


**S2.1.6** **Assessment of installation capacity potential**


The installation capacity potential for wind, solar PV, and CSP can be obtained by multiplying


the suitable installation area (km [2] ) and the installation density (MW/km [2] ), as follows:


_Cap_ _[max]_ _g_ = _SA × D_ _g_ _,_ (S2-16)


where _Cap_ _[max]_ _g_ is the installation capacity potential (MW) of power _g_ (wind, solar PV, or CSP),


_SA_ is the suitable installation area (km [2] ) in each grid cell, and _D_ _g_ is the installation density


(MW/km [2] ) of power _g_ .


**Wind power** The spacing between wind turbines is a key factor in wind farm design, as it


impacts both power production and turbine structural loading. The optimal spacing is primarily


determined by the predominant wind direction and turbine rotor diameter, with previous studies


suggesting a spacing of 5–10 rotor diameters between turbines to mitigate wake effects and turbine


fatigue [[6,17]] . In this study, focusing on utility-scale turbines with above 1 MW nameplate capacity,


we calculate the installation densities using NREL turbine specifications [[1]] and the adopted 7 _×_ 7


23


S2 ENERGY RESOURCE ASSESSMENT


rotor diameters spacing [[17,53]] . The resulting onshore wind power installation densities range from


2.40 to 5.50 MW/km [2], while offshore densities range from 4.80 to 6.30 MW/km [2], as detailed in


Tables S7 and S8. For evaluating wind power potentials, we use reference installation densities of


4.0 MW/km [2] for onshore wind [[17]] and 5.0 MW/km [2] for offshore wind [[53]], as defined in Equation


S2-16. We show the resulting installation capacity potential (MW) for onshore and offshore wind


in open, base, and conservative scenarios for each cell and province in Figure S18 and Table


S9–S10, respectively. Additionally, the annual power generation potential can be calculated by


the installation capacity potential and the annual average capacity factor.


Table S7: Onshore wind turbine parameters.

Rotor diameter Nameplate capacity Installation density
Wind turbine model
(m) (MW) (MW/km [2] )
2020 ATB NREL Reference 5.5 MW 175.0 5.50 3.60

2020 ATB NREL Reference 7 MW 200.0 7.00 3.50

BAR BAU LowSP 3.25 MW 166.0 3.25 2.40

BAR BAUa 5 MW 167.4 5.00 3.60

BAR LBNL-IEA 3.3 MW 156.0 3.30 2.72

BAR HighSP 5 MW 135.0 5.00 5.50
BAR LowSP 4.5 MW 194.0 4.50 2.40

BAR LowSP 6.5 MW 234.0 6.50 2.40

DOE GE 1.5 MW 77.0 1.50 5.00

IEA 3.4 MW Reference 130.0 3.37 4.00


Table S8: Offshore wind turbine parameters.

Rotor diameter Nameplate capacity Installation density
Wind turbine model
(m) (MW) (MW/km [2] )
2016CACost NREL Reference 6 MW 155 155.0 6.00 5.00

2016CACost NREL Reference 8 MW 180 180.0 8.00 5.00

2016CACost NREL Reference 10 MW 205 205.0 10.00 4.80

2019ORCost NREL Reference 12 MW 222 222.0 12.00 4.90

2019ORCost NREL Reference 15 MW 248 248.0 15.00 4.90

2020ATB NREL Reference 12 MW 214 214.0 12.00 5.20

2020ATB NREL Reference 15 MW 240 240.0 15.00 5.20

2020ATB NREL Reference 18 MW 263 263.0 18.00 5.20

DTU 10 MW 178 RWT v1 178.0 10.00 6.30

IEA 10 MW 198 RWT 198.0 10.00 5.10

IEA 15 MW 240 RWT 240.0 15.00 5.20

LEANWIND 8 MW 164 RWT 164.0 8.00 6.00

NREL 5 MW 126 RWT 126.0 5.00 6.25


24


S2 ENERGY RESOURCE ASSESSMENT

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-31-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-31-1.png)


(a) Open (b) Base

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-31-2.png)


(c) Conservative


Figure S18: Installation capacity potential (MW) for onshore and offshore wind power in each
grid cell in open (a), base (b) and, conservative (c) scenarios.


Table S9: Provincial installation capacity potential (GW) of onshore wind in mainland China.


Province Conservative Base Open Province Conservative Base Open


Anhui 96 135 174 Jilin 205 266 327

Beijing 14 21 27 Liaoning 148 199 248
Chongqing 58 103 148 Mengdong 371 481 589
Fujian 46 88 123 Mengxi 1,201 1,427 1,651
Gansu 650 920 1,124 Ningxia 75 100 123
Guangdong 85 124 158 Qinghai 269 975 1,206
Guangxi 137 244 353 Shaanxi 184 285 392
Guizhou 142 277 395 Shandong 245 329 411
Hainan 32 43 54 Shanghai 1 2 2
Hebei 244 342 434 Shanxi 225 326 416

Heilongjiang 480 619 757 Sichuan 165 343 507
Henan 233 321 407 Tianjin 13 17 21
Hubei 126 208 291 Xinjiang 2,783 3,823 4,528
Hunan 106 166 223 Xizang 0 1,135 1,617
Jiangsu 56 75 93 Yunnan 158 370 594
Jiangxi 88 130 168 Zhejiang 18 31 45
Total 8,654 13,925 17,606


25


S2 ENERGY RESOURCE ASSESSMENT


Table S10: Provincial installation capacity potential (GW) of onshore wind in mainland China.


Province Conservative Base Open Province Conservative Base Open


Fujian 61 89 257 Liaoning 106 144 147
Guangdong 141 266 579 Shandong 162 211 321
Guangxi 23 26 42 Shanghai 22 68 115
Hainan 31 49 105 Tianjin 8 8 8
Hebei 68 69 69 Zhejiang 62 151 505
Jiangsu 311 382 530 Total 995 1,463 2,678


**Solar power** In this study, we assume a fixed tilt model for the installation of solar PV panels


to assess the capacity potential of utility-scale and distributed solar PV power. Under this


assumption, it becomes imperative to calculate the optimal tilt, orientation, and inter-panel


distance in each grid cell. Initially, we determine the corresponding optimal tilt based on the


latitude of each grid cell as below [[27]] :


Σ = 1 _._ 3793 + _θ ×_ [1 _._ 2011 + _θ ×_ ( _−_ 0 _._ 0014404 + _θ ×_ 0 _._ 000080509)] _,_ (S2-17)


where _θ_ is the latitude of the grid cell. In assessing the solar PV resource potential in China (or


the Northern Hemisphere), it is hypothesized that the PV arrays are oriented facing the equator


to maximize solar radiation receipt. Inter-panel spacing can be determined by avoiding shading


from adjacent panels. Given that shadows reach maximum length in the Northern Hemisphere on


the winter solstice at 3 PM, the solar altitude and azimuth at this time are adopted to evaluate


the inter-panel distance to preclude potential shading concerns. The equation for computing


separation between adjoining PV panels is [[20]] :



_D_ = _L ×_ cos Σ + [sin Σ] _×_ cos _ϕ_ _s_
� tan _β_ _n_



_,_ (S2-18)
�



where _D_ is the distance between adjacent PV panels, _L_ is the length of the PV panel, Σ is the


optimal tilt angle in radians, _β_ _n_ and _ϕ_ _s_ are the solar altitude angle and azimuth angle at 3 PM


on the winter solstice in the Northern Hemisphere, respectively. After determining the distance


_D_, we can calculate the packing factor, which represents the ratio of the area occupied by PV


panels to the installation area, as follows:



_PF_ = _[L]_



_._ (S2-19)

[sin] _[ Σ]_

tan _β_ _n_ _[×]_ [ cos] _[ ϕ]_ _[s]_




_[L]_ _1_

_D_ [=] cos _Σ_ + [sin] _[ Σ]_



Finally, we assume a unit capacity of 161.9 W/m [2 [27]] and determine the installation density


for solar power in each grid cell by multiplying it with the packing factor, denoted as _D_ _pv_ =


161 _._ 9 _× PF_ . The installation density is consistent for utility-scale and distributed solar PV


within the same grid cell. We show the resulting installation capacity potential (MW) for utility

scale and distributed solar PV in each cell and provincial grid in Figure S19 and Table S11,


respectively.


26


S2 ENERGY RESOURCE ASSESSMENT

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-33-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-33-1.png)


(a) Open (b) Base

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-33-2.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-33-3.png)


(c) Conservative (d) Open

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-33-4.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-33-5.png)


(e) Base (f) Conservative


Figure S19: Installation capacity potential (MW) for utility-scale (a, b, and c) and distributed
solar PV (d, e, and f) in each grid cell in open, base, and conservative scenario.


27


Table S11: Provincial installation capacity potential (GW) of UPV and DPV in mainland China.


Province Utility-scale solar PV Distributed solar PV


Conservative Base Open Conservative Base Open
Anhui 1 224 453 54 63 72

Beijing 0 24 51 12 14 16
Chongqing 0 39 111 25 30 34
Fujian 1 36 94 36 42 48
Gansu 2,583 3,878 5,159 23 26 30
Guangdong 2 189 417 107 125 143
Guangxi 1 182 433 55 64 74
Guizhou 1 29 119 42 49 56

Hainan 2 78 161 9 11 12

Hebei 26 373 754 66 77 88

Heilongjiang 405 884 1,385 16 18 21

Henan 1 442 919 80 93 106

Hubei 9 187 400 52 61 69

Hunan 28 156 340 67 78 90

Jiangsu 2 189 377 79 92 105
Jiangxi 15 140 306 48 56 64
Jilin 178 426 695 14 17 19

Liaoning 18 196 414 29 33 38
Mengdong 421 798 1,231 9 11 12
Mengxi 5,238 7,736 10,180 12 14 16
Ningxia 113 218 339 7 8 9
Qinghai 2,825 4,519 6,241 5 6 7
Shaanxi 118 285 492 26 31 35

Shandong 12 477 967 100 117 134
Shanghai 2 18 35 17 19 22
Shanxi 14 172 411 27 32 37

Sichuan 53 234 524 84 98 112

Tianjin 6 35 64 10 12 14
Xinjiang 13,366 20,383 26,867 17 20 23
Xizang 1,843 3,370 5,208 4 4 5

Yunnan 1 57 182 56 65 74

Zhejiang 1 53 115 56 66 75
Total 27,286 46,027 65,444 1,244 1,452 1,660


For concentrating solar power, we use the packing factor of 0.37 [[38]] multiplying the maximum


generation capacity of the per-area collector of 100 W/m [2] as the installation density, which is 37


MW/m [2] . We show the cell-level installation capacity potential of CSP in Figure S20.


S2 ENERGY RESOURCE ASSESSMENT

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-35-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-35-1.png)


(a) Open (b) Base

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-35-2.png)


(c) Conservative


Figure S20: Installation capacity potential (MW) for concentrating solar power in each grid cell
in open (a), base (b), and conservative (c) scenarios.


**S2.2** **Assessment of hydropower**


We assess the hydropower potential in China through an analysis of the least levelized cost of


energy (LCOE), taking into account factors including the inundated area, environmental conserva

tion, population displacement, and integration distance, as documented in previous studies [[54,55]] .


Initially, we identify hypothetical dam sites along the MERIT [[56]] river network at 4.5 km in

tervals. Subsequently, we conduct simulations of the inundated area for each dam site, varying


dam heights from 10 m to 300 m. Employing raster analysis, we eliminate areas intersecting


with zones designated for natural and biodiversity protection, as well as those resulting in the


displacement of over 50,000 individuals. For the remaining dam heights, hydro discharge data


from [[57]] and cost assumptions from [[54]] are utilized to estimate the LCOE for each virtual dam


site. The optimal dam height, associated with the least LCOE, is then selected. Ultimately,


we establish a profitability threshold for development projects at an LCOE of 0.2 $/kWh. Our


findings reveal an additional 270 GW of hydropower potential in mainland China, contributing


to a total potential capacity of approximately 700 GW, including the installed capacity by 2022.


29


S2 ENERGY RESOURCE ASSESSMENT



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-3.png)





![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-4.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-8.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-13.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-20.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-22.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-31.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-36.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-43.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-48.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-56.png)



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-2.png)



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-7.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-9.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-12.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-14.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-18.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-19.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-23.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-24.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-29.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-30.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-37.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-38.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-42.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-44.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-49.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-50.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-55.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-36-57.png)

Figure S21: Natural inflow discharge (m [3] /s) for the installed and potential hydropower plants in
each province.


In the CISPO model, we utilize the discharge data with a 3-hour temporal resolution from


2019 [[57]] to simulate the generation of run-of-river and reservoir hydropower. Considering the


environmental impact, we first estimate the environmental flow using the 10th percentile of his

torical discharge from 1980 to 2019 [[54]] . Within this estimate, the inflow discharge to hydropower


30


S2 ENERGY RESOURCE ASSESSMENT


for generation is equal to the natural inflow minus the environmental flow. For run-of-river hy

dropower, we assess the capacity factor at each time step by dividing the inflow discharge by the


design discharge and constraining the capacity factor to be less than 1. We present the natural


inflow discharge for the installed and potential hydropower plants in Figure S21.


**S2.3** **Assessment of biomass energy**


Biomass energy is considered a carbon-neutral energy source in this study. Due to the uneven


geographical distribution of bioenergy feedstocks in China, we first evaluate the biomass resource


potential and subsequently calculate the installation capacity potential in each power grid. We use


the agricultural residue, forest residue, and energy crop on abandoned cropland with quantified


potential in TJ/yr for each 1km _×_ 1km grid cell from [[58]] . Accordingly, we aggregate these energy


potentials in TJ at the power grid level and then estimate the installation capacity potential using


the following equation:

_[η]_ _[bio]_ _[ ×]_ _[ξ]_
_Cap_ _[max]_ _bio_ = _[cv][ ×]_ _eoh_ _bio_ _,_ (S2-20)


where _Cap_ _[max]_ _bio_ is the installation capacity potential of biomass energy, _cv_ is the calorific value


(TJ), _η_ _bio_ is the biomass power efficiency (0.35 in this study [[59]] ), _ξ_ is the coefficient for converting


TJ to GWh, and _eoh_ _bio_ is the equivalent operating hours of biomass power (assumed to be


6,132 hrs/yr). The annual available bioenergy fuel is approximately 29.8 EJ from residuals and


energy crops on abandoned cropland, with an installation capacity potential of about 470 GW.


We employ the bioenergy fuel and installation capacity potentials to constrain biomass power


generation and installation in each grid. The bioenergy fuel potential per 1km _×_ 1km cell and


installation capacity potential per grid are depicted in Figure S22.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-37-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-37-1.png)


(a) Bio-energy fuel potential (b) Installation capacity potential


Figure S22: Annual bio-energy fuel potential (TJ/yr) in each 1km _×_ 1km pixel (a), and installation
capacity potential (GW) in each power grid (b).


31


S2 ENERGY RESOURCE ASSESSMENT


**S2.4** **Assessment of carbon sequestration**


Carbon capture and sequestration (CCS) is considered a potentially viable technology for decar

bonizing the power system, or even as a negative emission technology (NET) in conjunction with


biomass energy sources [[12,60]] . Carbon dioxide can be geologically sequestered in various forma

tions such as sedimentary basins for aquifer storage, oil and gas reservoirs, among others [[60]] . In


this study, we primarily focus on deep saline aquifers (DSA) as a preferred option for carbon


storage due to their substantial capacity within China [[60]] .


We assess the effective CO 2 storage potential for DSA using following equation [[60]] :


_V_ _CO_ 2 = _A × η_ _A_ _× h × ϕ × ρ_ _CO_ 2 _× η_ _E_ _,_ (S2-21)


where _V_ _CO_ 2 is the storage potential (Gt) for DSA, _A_ is the geographical area of sedimentary basins


assessed for CO 2 storage, _η_ _A_ is the effective area ratio for CO 2 storage (0.02 [[60]] ), _h_ is the gross


average thickness of the saline aquifer (200 m), _ϕ_ is the total porosity in volume (0.2 [[60]] ), _ρ_ _CO_ 2 is


the density of CO 2 under storage conditions (710 kg/m [3 [61]] ), and _η_ _E_ is the effective CO 2 storage


ratio, which is set as 0.05 [[62]] . We use the World Geologic Provinces dataset, which is further


adjusted based on the findings of [[60]] and [[63]], to accurately compute the surface area encompassing


all sedimentary basins. Nationally, we find approximately 180 Gt CO 2 storage potential (120 Gt


onshore + 60 Gt offshore) by deep saline aquifers, and we show the geographical results in Figure


S23.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-38-0.png)


Figure S23: Deep saline aquifer carbon storage potential in China (about 180 Gt in total).


32


S3 INPUT DATA AND PRE-PROCESS

## **S3 Input Data and Pre-process**


**S3.1** **Hourly power demand factor profile**


To construct the hourly power demand load curve for the specified year, we begin by collecting


data on typical weekday and non-workday hourly demand loads, as well as daily maximum and


minimum loads recorded throughout 2019, across 32 provinces as documented in [[3]] . An illustrative


example of the typical hourly demand load in Anhui province during 2019 is provided to elucidate


the original dataset retrieved from [[3]], see Figure S24. We then adjust the hourly demand load


for each day of 2019 considering whether it falls on a weekday or non-workday and scaling to the


respective maximum and minimum demand levels as shown in Figure S24. Finally, the hourly


demand profile for the whole year of 2019 is derived by concatenating these hourly demand profiles


of each day. Upon these absolute demands, we normalize the hourly demand load relative to the


peak demand observed during the year 2019, thereby establishing the base load factor for each


power grid, see Figures S25 to S28. In this study, we designate the year 2022 as the base year, and


the hourly demand profile in 2022 for each province is adjusted by the annual demand increase


compared to 2019, as illustrated in Figure S29.







![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-39-2.png)



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-39-3.png)



(a) Hourly demand load profile in typical weekday



(b) Hourly demand load profile in typical non-work day



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-39-9.png)



(c) Daily maximum and minimum demand through 2019


Figure S24: Load (MW) profile in typical weekday (a), non-work day (b), and daily maximum
and minimum demand (MW) all year round (c) in Anhui province [[3]] .


33


S3 INPUT DATA AND PRE-PROCESS

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-40-1.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-40-3.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-40-5.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-40-7.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-40-9.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-40-11.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-40-13.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-40-15.png)


Figure S25: Normalized demand load profile in Anhui, Beijing, Chongqing, Fujian, Gansu, Guangdong, Guangxi, and Guizhou.


34


S3 INPUT DATA AND PRE-PROCESS

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-41-1.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-41-3.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-41-5.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-41-7.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-41-9.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-41-11.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-41-13.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-41-15.png)


Figure S26: Normalized demand load profile in Hainan, Hebei, Heilongjiang, Henan, Hubei,
Hunan, Jiangsu, and Jiangxi.


35


S3 INPUT DATA AND PRE-PROCESS

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-42-1.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-42-3.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-42-5.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-42-7.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-42-9.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-42-11.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-42-13.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-42-15.png)


Figure S27: Normalized demand load profile in Jilin, Liaoning, East Inner Mongolia, West Inner
Mongolia, Ningxia, Qinghai, Shaanxi, and Shandong.


36


S3 INPUT DATA AND PRE-PROCESS

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-43-1.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-43-3.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-43-5.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-43-7.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-43-9.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-43-11.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-43-13.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-43-15.png)


Figure S28: Normalized demand load profile in Shanghai, Shanxi, Sichuan, Tianjin, Xinjiang,
Xizang, Yunnan, and Zhejiang.


37


S3 INPUT DATA AND PRE-PROCESS




|Col1|Col2|Col3|Col4|Col5|Col6|Col7|Col8|Col9|Col10|Col11|Col12|Col13|Col14|Col15|Col16|Col17|Col18|Col19|Col20|Col21|Col22|Col23|Col24|Col25|Col26|Col27|Col28|Col29|Col30|2<br>2|019<br>022|
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
|||||||||||||||||||||||||||||||||
|||||||||||||||||||||||||||||||||
|||||||||||||||||||||||||||||||||
|||||||||||||||||||||||||||||||||
|||||||||||||||||||||||||||||||||
|||||||||||||||||||||||||||||||||
|||||||||||||||||||||||||||||||||



Figure S29: Annual power demand (TWh/yr) for each province in 2019 and 2022.


**S3.2** **Annual power demand projections**


In this study, we estimate the annual power demand toward 2060 by accounting for the expert


judgment and the real electricity consumption in 2023 (about 9.2 PWh/yr [[64]] ). Shu et al. [[65]]


and Li et al. [[66]] both report that the annual power demands for China in 2030 and 2060 are


about 11.8 PWh/yr and 15.7 PWh/yr, respectively. Besides, the 15.7 PWh/yr demand in 2060


is also close to the 15.4 PWh/yr forecast from an economy-wide model [[67]] . Considering the fast


demand increase in 2023 (an increase of about 6.7% compared to 2022 [[64]] ), we adjust the annual


demand in 2030 to be 12.5 PWh/yr, and in 2060 to be 16.0 PWh/yr. Subsequently, we use


linear interpolation to estimate the annual demand in 2040 and 2050 with 12.5 PWh/yr in 2030


as the start point and 16.0 PWh/yr in 2060 as the endpoint. See the national annual demand


projections in Figure S30. Finally, we extrapolate the load profile to the target year by scaling


up in accordance with the ratio of the projected national annual demand for the target year to


that of the base year (2022), which is about 8.6 PWh/yr [[68]] . In the sensitivity test on a higher


demand projection (“Demand1.25X”), the national annual power demand is 1.25 times that of


the base scenario.





|20000 Base ( 550Mt)<br>Demand1.25X<br>19000<br>18000<br>17000<br>16000<br>15000<br>14000<br>13000<br>2030 203|Col2|Base ( 550Mt)<br>Demand1.25X|Col4|Col5|Col6|Col7|Col8|Col9|Col10|
|---|---|---|---|---|---|---|---|---|---|
|2030<br>203<br>13000<br>14000<br>15000<br>16000<br>17000<br>18000<br>19000<br>0000<br>~~Base (~~<br>~~550Mt)~~<br>Demand1.25X||||||||||
|2030<br>203<br>13000<br>14000<br>15000<br>16000<br>17000<br>18000<br>19000<br>0000<br>~~Base (~~<br>~~550Mt)~~<br>Demand1.25X||||||||||
|2030<br>203<br>13000<br>14000<br>15000<br>16000<br>17000<br>18000<br>19000<br>0000<br>~~Base (~~<br>~~550Mt)~~<br>Demand1.25X||||||||||
|2030<br>203<br>13000<br>14000<br>15000<br>16000<br>17000<br>18000<br>19000<br>0000<br>~~Base (~~<br>~~550Mt)~~<br>Demand1.25X||||||||||
|2030<br>203<br>13000<br>14000<br>15000<br>16000<br>17000<br>18000<br>19000<br>0000<br>~~Base (~~<br>~~550Mt)~~<br>Demand1.25X||||||||||
|2030<br>203<br>13000<br>14000<br>15000<br>16000<br>17000<br>18000<br>19000<br>0000<br>~~Base (~~<br>~~550Mt)~~<br>Demand1.25X||||||||||
|2030<br>203<br>13000<br>14000<br>15000<br>16000<br>17000<br>18000<br>19000<br>0000<br>~~Base (~~<br>~~550Mt)~~<br>Demand1.25X||||5<br>20|0<br>2|45<br>20|0<br>20|5<br>2060|5<br>2060|


Figure S30: Annual electricity demand projection toward 2060.


38


S3 INPUT DATA AND PRE-PROCESS


**S3.3** **Wind and solar power**


**S3.3.1** **Installed capacity allocation**


We collect the existing installed capacity data at the project level of onshore wind, offshore wind,


and utility-scale solar PV by the end of 2022 from Global Energy Monitor (GEM) [[4]] . Only


projects labeled as “Operating” in the GEM dataset are considered in the analysis, representing


the existing installed capacity input to the CISPO model, as shown in Figure S31. Under the


“Operating” status, approximately 278 GW of onshore wind, 30 GW of offshore wind, and 228


GW of utility-scale solar PV projects are identified, corresponding to around 83.2%, 98%, and


97% of the national installed capacity data released by the National Energy Administration


(NEA) [[7,69]], respectively. The GEM dataset provides the location information with approximate


longitude and latitude for these projects which are used to aggregate installed capacities within


each grid cell for modeling inputs. Although the GEM dataset covers most of the installed VRE


capacity, it still leaves gaps between the actual installation in some provinces. In this situation,


we allocate the remaining capacity to the cells according to the annual capacity factor ranked


from high to low. Given there is no specific location data for installed distributed solar, we


collect the provincial-level data from NEA [[7]], and then allocate the capacity to cells using the


same methods as the capacity gap allocation of utility-scale solar PV. We show the installed


capacity (GW) for distributed solar PV of each province in Table S12. Since the lack of public


CSP installation location data, and the installed CSP capacity is only 0.58 GW by 2022, we do


not allocate installed capacity to CSP grid cells.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-45-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-45-1.png)


(a) Wind (b) Utility-scale solar PV


Figure S31: Existing installed capacity of wind and utility-scale solar PV by 2022 [[4]] .


39


S3 INPUT DATA AND PRE-PROCESS


Table S12: Provincial installed capacity (GW) of distributed solar by 2022 [[7]] .


Province Capacity Province Capacity Province Capacity Province Capacity


Anhui 10.90 Guizhou 0.24 Jiangxi 5.07 Shanxi 4.39
Beijing 0.90 Hainan 0.46 Jilin 0.92 Sichuan 0.33
Chongqing 0.15 Hebei 18.6 Liaoning 2.19 Tianjin 0.99
Mengdong 0.21 Heilongjiang 1.08 Ningxia 0.92 Mengxi 1.00
Fujian 4.26 Henan 17.00 Qinghai 0.16 Xinjiang 0.27
Gansu 0.86 Hubei 3.40 Shaanxi 3.23 Xizang 0.002
Guangdong 8.37 Hunan 3.50 Shandong 30.20 Yunnan 0.60
Guangxi 0.83 Jiangsu 15.55 Shanghai 1.71 Zhejiang 19.26


**S3.3.2** **Grid integration**


CISPO considers the intra-grid transmission lines to facilitate the integration of renewable energy


sources into load centers, followed by transmission to other grids via Ultra-High Voltage (UHV)


transmission networks. As outlined below, the estimation of these connecting distances relies


on the methodology proposed in [[8,15]] . Power generated from wind, solar, CSP, and hydropower


at a specific cell or dam site must initially be connected to a nearby substation using “spur


lines”. Subsequently, “trunk lines” must be constructed or reinforced for transmission from the


substation to the nearest major node in the power grid.


**Matching cells with substations and major nodes** We collect the geographical coordinates


of electrical substations in China with voltage levels exceeding 220 kV from Open Street Map


(OSM) [[52,70]] . We then identify the centroids of urban areas detected via satellite imagery at 1:50


M map scale precision [[71]] as the major load centers. To compensate for the paucity of urban


areas identified at the 1:50 M resolution in Xizang, additional load centers are designated from


urban areas discerned under 1:10 M scale satellite imagery. Based on the location information of


load centers, substations, and grid cells, we match each grid cell first to a substation and then


to a major node in the same power grid with an algorithm that guarantees the total geodesic


distance of connecting lines for this cell is minimal. We show the matching results in Figure S32


and S33.


40


S3 INPUT DATA AND PRE-PROCESS

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-47-0.png)


Figure S32: Illustration of grid cell interconnection to the main transmission network. Connections consist of spur lines (silver) to the nearest substation (black point) and trunk lines (red) to
the nearest load center (orange polygon).


We evaluate the cost of spur lines and trunk lines based on the required capacity to be


strengthened and incorporate these costs into the objective function of the CISPO model. For


a spur line that connects a grid cell to a substation, its capacity is equal to the total capacity


installed in this cell (existing capacity discussed in Section S3.3.1 plus capacity for future devel

opment optimized by the model) multiplied by the maximum hourly capacity factor of this grid


cell. For a trunk line that connects a substation to a major node, its capacity depends on the peak


output of aggregated profiles of the total capacity in all the cells connected to this substation.


Since the peak output of each cell connected to the substation may occur at different timesteps


(i.e., max _t_ [[] _[cf]_ _[we]_ [(] _[t]_ [)+] _[cf]_ _[pv]_ [(] _[t]_ [)]] _[ ≤]_ [max] _t_ [[] _[cf]_ _[we]_ [(] _[t]_ [)]+max] _t_ [[] _[cf]_ _[pv]_ [(] _[t]_ [)]), we do not simply sum the peak output]


over all the cells to obtain the required capacity of the trunk line. A detailed representation of


the spur line and trunk line in our optimization model is described in Section S7.9.


41


S3 INPUT DATA AND PRE-PROCESS

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-48-0.png)


Figure S33: Matching results of connecting cells to the major nodes in mainland China.


We assume 220 kV alternating-current (AC) transmission is used for trunk lines [[15]], and spur


lines connecting onshore wind, utility-scale solar PV, CSP, and hydropower to the nearest sub

station. Additionally, for offshore wind power, 220 kV AC submarine transmission lines are used


to connect the power generated by offshore wind to onshore substations [[72]] . We show the spur


and trunk line connecting offshore wind to the major node in Figure S34. Capital expendi

ture (CapEx) for onshore and submarine 220 kV AC transmission lines are given as 1,200 _×_ 10 [3]


yuan/km [[73]] and 4,050 _×_ 10 [3] yuan/km [[72]], respectively, and CapEx for 220 kV substation is given


as 2,620 _×_ 10 [3] yuan/kW [[73]] . The fixed operation & maintenance costs for overhead lines and


substations are given as 1.2% of CapEx [[72]] .


42


S3 INPUT DATA AND PRE-PROCESS

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-49-0.png)


Figure S34: Illustration of the integration to the major node for offshore wind.


**S3.3.3** **Clustering wind and solar PV**


This study formulates an optimization model to effectively manage gridded VRE resource pro

files while maintaining computational efficiency (see model details in Section S4). Unlike tradi

tional continent-scale renewable energy expansion studies (e.g., [[6,10,15]] ), which commonly aggre

gate high-resolution resource data into a limited number of representative profiles per region, we


assess the implications of incorporating high spatio-temporal granularity through a VRE aggre

gation sensitivity analysis.


In this analysis, we employ a two-step clustering approach tailored to each VRE technology


(onshore wind, offshore wind, utility-scale solar PV, and distributed solar PV). Since generation


and connection costs are key determinants of VRE deployment costs, we group grid cells based


on capacity factor and connection distance. Initially, we apply the k-means algorithm to cluster


cells with similar hourly capacity factor profiles, using Euclidean distance to measure differences.


The optimal number of clusters per province is determined using the Elbow method (minimizing


the sum of squared errors) and the Silhouette Coefficient. Within each resulting cluster, a second


k-means clustering is performed based on total connection distance (sum of spur and trunk line


lengths). We only aggregate the distributed solar PV using the hourly capacity factor as it is


not modeled with integration distance. Clustering results for wind and solar PV are presented


in Figure S35. For each final cluster, total capacity is calculated by summing the capacities of


constituent cells, and weighted average capacity factors and connection distances are used to


estimate representative generation and connection costs.


43


S3 INPUT DATA AND PRE-PROCESS

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-50-0.png)


(a) Onshore wind

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-50-1.png)


(b) Offshore wind

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-50-2.png)


(c) Utility-scale solar PV

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-50-3.png)


(d) Distributed solar PV


Figure S35: Provincial aggregation results of wind power in mainland China. (a): Onshore wind;
(b): Offshore wind; (c): Utility-scale solar PV; (d): Distributed solar PV.


44


S3 INPUT DATA AND PRE-PROCESS


**S3.3.4** **Cost projections**


We collect current capital expenditure and yearly fixed operation and maintenance (O&M,


yuan/kW*yr) costs for wind and solar PV from the China Renewable Energy Development Re

port 2022 [[74]] . The CapEx for onshore and offshore wind are 5,800 yuan/kW and 11,500 yuan/kW.


And for utility-scale solar PV and distributed solar PV, the CapEx is 3,923 yuan/kW and 3,740


yuan/kW, respectively. Current CSP CapEx is 16,643 yuan/kW from [[75]] .


For projecting the CapEx toward the years 2030, 2040, 2050, and 2060, we comparatively


analyzed the predictions of wind and solar power technologies from the studies conducted by


Sun et al. [[75]], Li et al. [[10]], and the National Renewable Energy Laboratory’s (NREL) Annual


Technology Baseline (ATB). The CapEx projections for VRE sources are reported directly in


yuan/kW by Sun et al. and Li et al., whereas the NREL ATB provides these values in $/kW,


reflecting the United States context. Considering this discrepancy, we employed the current


CapEx and decline rates furnished by the NREL ATB to estimate future projections, rather than


relying on absolute values. Since the cost predictions from the NREL ATB extend only until 2050,


we used linear interpolation to approximate the value for 2060. The comparative projections for


onshore wind, offshore wind, utility-scale solar PV, distributed solar PV, and CSP from Sun et


al., Li et al., and the NREL ATB are depicted in Figure S36.


In this study, we adopt the cost projections for onshore wind that closely align with those pro

posed by Li et al. [[10]], and for offshore wind, we follow projections that are proximate to the NREL


ATB [[76]] . Regarding utility-scale solar PV, we employ the decline rates provided by the NREL


ATB prior to 2050 and adopt the CapEx projection for 2060 from [[8]] . For distributed solar PV


systems, their deployment potential and future installation predictions are considerably smaller


than those of utility-scale solar PV installations, according to [[8,9,20,47]] . Since larger deployments


may decrease the unit cost due to the learning curve effect [[77,78]], we assume a higher CapEx for


distributed solar PV than for utility-scale solar PV. Concerning CSP, we use CapEx projections


that closely resemble those of the NREL ATB. CapEx projections in this study are also illustrated


in Figure S36. The fixed O&M costs are specified as a fraction of CapEx [[6,10]] . We set the fixed


O&M (% of CapEx) at 1.5% for onshore/offshore wind [[6]], 0.5% for utility-scale/distributed solar


PV [[6]], and 1.0% for CSP [[10]] .


45


S3 INPUT DATA AND PRE-PROCESS






|6000|Col2|Col3|Col4|Col5|Col6|Col7|Col8|Col9|
|---|---|---|---|---|---|---|---|---|
|2000<br>3000<br>4000<br>5000<br>6000<br>|||||||||
|2000<br>3000<br>4000<br>5000<br>6000<br>|||||||||
|2000<br>3000<br>4000<br>5000<br>6000<br>|||||||||
|2000<br>3000<br>4000<br>5000<br>6000<br>|S<br>L|un et al.<br>i et al.|||||||
|2000<br>3000<br>4000<br>5000<br>6000<br>|S<br>L|un et al.<br>i et al.|un et al.<br>i et al.|un et al.<br>i et al.|un et al.<br>i et al.|un et al.<br>i et al.|un et al.<br>i et al.|un et al.<br>i et al.|
|2000<br>3000<br>4000<br>5000<br>6000<br>|N<br>T|REL ATB<br>his study|||||||






|2000 12000|Col2|Col3|Col4|Col5|Col6|Col7|Col8|Col9|
|---|---|---|---|---|---|---|---|---|
|8000<br>9000<br>10000<br>11000<br><br>|||||||Sun<br>Li et|et al.<br>al.|
|8000<br>9000<br>10000<br>11000<br><br>|||||||NRE<br>This|L ATB<br>study|
|8000<br>9000<br>10000<br>11000<br><br>|||||||||
|8000<br>9000<br>10000<br>11000<br><br>|||||||||
|8000<br>9000<br>10000<br>11000<br><br>|||||||||


|7000 3000|Col2|Col3|Col4|Col5|Col6|Col7|Col8|Col9|
|---|---|---|---|---|---|---|---|---|
|1000<br>1500<br>2000<br>2500<br><br>|||||||Sun<br>Li et<br>~~NRE~~<br>|et al.<br>al.<br>~~L ATB~~<br>|
|1000<br>1500<br>2000<br>2500<br><br>|||||||This|study|
|1000<br>1500<br>2000<br>2500<br><br>|||||||||


|1000 4000|Col2|Col3|Col4|Col5|Col6|Col7|Col8|Sun e|et al.|
|---|---|---|---|---|---|---|---|---|---|
|1000<br>1500<br>2000<br>2500<br>3000<br>3500<br>||||||||Sun<br>|et al.<br>|
|1000<br>1500<br>2000<br>2500<br>3000<br>3500<br>|||||||~~Li et~~<br>NRE<br>~~This~~|~~Li et~~<br>NRE<br>~~This~~|~~l.~~<br>L ATB<br>~~study~~|
|1000<br>1500<br>2000<br>2500<br>3000<br>3500<br>||||||||||
|1000<br>1500<br>2000<br>2500<br>3000<br>3500<br>||||||||||



|22500|Col2|Col3|Col4|Col5|Col6|Col7|Col8|Sun e|et al.|
|---|---|---|---|---|---|---|---|---|---|
|2030<br>2035<br>20<br>7500<br>10000<br>12500<br>15000<br>17500<br>20000<br>22500<br>||||||||~~Sun~~<br>Li et<br>|~~t al.~~<br>al.<br>|
|2030<br>2035<br>20<br>7500<br>10000<br>12500<br>15000<br>17500<br>20000<br>22500<br>||||||||~~NRE~~<br>This|~~ ATB~~<br>study|
|2030<br>2035<br>20<br>7500<br>10000<br>12500<br>15000<br>17500<br>20000<br>22500<br>||||||||||
|2030<br>2035<br>20<br>7500<br>10000<br>12500<br>15000<br>17500<br>20000<br>22500<br>||||||||||
|2030<br>2035<br>20<br>7500<br>10000<br>12500<br>15000<br>17500<br>20000<br>22500<br>||||||||||
|2030<br>2035<br>20<br>7500<br>10000<br>12500<br>15000<br>17500<br>20000<br>22500<br>||||40<br>20|5<br>2|50<br>20|5<br>2060|5<br>2060|5<br>2060|


Figure S36: Cost projections of wind and solar power. A: onshore wind; B: offshore wind; C:
utility-scale solar PV; D: distributed solar PV; E: CSP.


**S3.4** **Hydropower**


In this study, we use the installed hydropower dataset from [[79]], henceforth referred to as the China


Hydropower Database (CHD), which provides comprehensive information on installed capacity,


location, year of construction, and hydropower type. The CHD dataset encompasses approxi

mately 362 GW of hydropower installations as of 2022, which is close to the 367 GW released by


the China Renewable Energy Engineering Institute [[74]] . The distribution of installed hydropower


46


S3 INPUT DATA AND PRE-PROCESS


is shown in Figure S37. In this model, we optimize the capacity expansion at the dam site level


and the hourly operation through the 8,760 hours for both run-of-river and reservoir hydropower.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-53-0.png)


Figure S37: Existing installed capacity (MW) of hydropower. In CISPO, the capacity expansion
and operation of hydropower is optimized at the dam site level.


Hydropower plants labeled as run-of-river or without type labels in the CHD dataset are


modeled as run-of-river hydropower in this study. In modeling reservoir hydropower, the key


input parameters include reservoir capacity and dam height. However, approximately 1,500 MW


(roughly 13.5%) of the installed reservoir projects lack this information in the CHD dataset,


therefore they are modeled as run-of-river facilities instead. Given that the location information


for these hydropower plants is roughly accurate and may not sit exactly on the river routes [[57]] .


We first allocate them to the virtual dam site assessed in Section S2.2 by searching within a 5 km


radius buffer area [[54]] . And then use the installed capacity to select the nearest dam height, and


accordingly calculate the design flow and capacity factor. We model the plants labeled as reser

voir hydropower and with reservoir capacity information as the installed reservoir hydropower.


Different from the allocation of run-of-river hydropower to the virtual dam site, the installed


reservoir hydropower plants are allocated to the nearest river routes to use the natural water


inflow. The minimum reservoir capacity is another key parameter to model the reservoir hy

dropower. Of the remaining projects with reservoir capacity data, around 44,000 MW have this


parameter, enabling calculation of their minimum capacity coefficient (approximately 0.58). For


installed reservoir projects with known capacity but unknown minimum reservoir capacity, the


minimum is estimated by multiplying the reservoir capacity by the averaged minimum capacity


47


S3 INPUT DATA AND PRE-PROCESS


coefficient. Potential dam sites assessed in this study with design capacity larger than 750 MW


(large-I hydropower) are modeled as reservoir hydropower, which totals about 88 GW. And the


minimum reservoir capacity is also estimated by multiplying the reservoir capacity by the av

eraged minimum capacity coefficient. The remaining potential dam sites (about 181 GW) are


modeled as run-of-river hydropower.


We use the water discharge (m [3] /s) dataset from the Global Reach-Level Flood Reanalysis


(GRFR [[57]] ) as the inflow for each hydropower plant. This discharge dataset is calculated based


on 90 m digital elevation model data, which divides the river network into discrete watersheds and


reaches. Following the methodology of [[54]], the environmental flow requirement (Q _e_ ) is initially


determined using the 10th percentile of historical discharge rates from 1980-2019. In this study,


the 3-h discharge data in 2019 is used to calculate the capacity factor for run-of-river hydropower


and as natural inflow for reservoir hydropower. To align with this model’s hourly operation, the


3-h discharge rate values are applied uniformly for each hour within a given 3-h period. In the


base year (2022), we find about 1.33 PWh generation from the CISPO model, close to the actual


generation of about 1.35 PWh [[74]] . These simulation results validate our model methodology.


We adopt the CapEx projection for hydropower installations at 13,319 yuan/kW from 2030


to 2060 [[74]], as the installation cost is anticipated to remain stable according to [[8]] . The fixed


O&M expenses for hydropower facilities are assumed to be 2% of the CapEx [[54]], which is in close


proximity to the 1.8% figure reported in [[9]] . The capital recovery period for hydropower projects


is set at 40 years in this study. Furthermore, we postulate that the installed hydropower capacity


will remain operational until the year 2060.


**S3.5** **Thermal and nuclear power**


In the CISPO model, we optimize the installation capacity and hourly operation of thermal


and nuclear power including coal, coal-fired combined heat and power (CHP), coal CCS, coal


CHP CCS, gas, gas-fired CHP, gas CCS, gas CHP CCS, biomass, biomass CCS and nuclear.


Within the carbon emissions goal, we allow the traditional coal, gas, and biomass power plants


to be retrofitted with a CCS system. The biomass and nuclear installation capacity are bounded


considering the bio-fuel availability and nuclear security requirement, respectively.


**S3.5.1** **Installed capacity**


We collect the project-level installation information of coal, gas, and nuclear power from Global


Energy Monitor (GEM) [[4]], which is widely used. This dataset provides detailed information on


building year, operating status (operating, retired, or under-construction), technology type, and


location. Since the operating requirement is different in non-CHP and CHP plants, we divide


coal and gas plants labeled as CHP in GEM dataset. In the base year (2022), the operating units


48


S3 INPUT DATA AND PRE-PROCESS


are recognized as the installed capacity, totaling 1,015 GW for coal power, 94 GW for coal CHP,


31 GW for gas, 78 GW for gas CHP, and 56 GW for nuclear. After 2030, the under-construction


units are assumed to be complete and therefore modeled as the installed capacity. We assume


these installations will retire if reaching the designed lifetime (40 years) in the optimization year


(e.g. 2050). In 2030, these installed capacities leave 1,131 GW for coal, 95 GW for coal CHP, 41


GW for gas, 78 GW for gas CHP, and 81.7 GW for nuclear. We show the projections of these


installed capacities for each province toward 2060 in Figure S38.


Given the lack of detailed project-level information regarding biomass power generation units


in China, we use the installed capacity data at the provincial level as of 2022 from the Report


on China Electric Power Development 2023 [[80]] as shown in Table S13, and assume the biomass


power installations will be replaced upon retirement. In this model, the fuel used by biomass


power is assumed to be entirely bio-fuel, and the upper bound for each province to install biomass


power can be found in Section S2.3.


Table S13: Provincial installed capacity (GW) of biomass energy power by 2022.


Province Capacity Province Capacity Province Capacity Province Capacity


Anhui 2.45 Hainan 0.59 Jilin 1.11 Shanghai 0.69
Beijing 0.4 Hebei 2.19 Liaoning 1.09 Shanxi 0.93
Chongqing 0.59 Heilongjiang 2.6 Mengdong 0.31 Sichuan 1.27
Fujian 1.03 Henan 2.51 Mengxi 0.15 Tianjin 0.45
Gansu 0.23 Hubei 1.37 Ningxia 0.15 Xinjiang 0.22
Guangdong 4.22 Hunan 1.26 Qinghai 0.01 Xizang 0.02
Guangxi 2.51 Jiangsu 2.97 Shaanxi 0.6 Yunnan 0.65
Guizhou 0.63 Jiangxi 1.2 Shandong 4.11 Zhejiang 2.84


49


S3 INPUT DATA AND PRE-PROCESS




|Col1|Col2|Col3|Col4|Col5|Col6|Col7|Col8|Col9|Col10|Col11|Col12|Col13|Col14|Col15|Col16|2060|Col18|Col19|Col20|Col21|Col22|Col23|Col24|Col25|Col26|Col27|Col28|Col29|Col30|Col31|
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
||||||||||||||||||||||||||||||||
||||||||||||||||||||||||||||||||
||||||||||||||||||||||||||||||||
||||||||||||||||||||||||||||||||


|Col1|Col2|Col3|Col4|Col5|Col6|Col7|Col8|Col9|Col10|Col11|Col12|Col13|Col14|Col15|Col16|2050|Col18|Col19|Col20|Col21|Col22|Col23|Col24|Col25|Col26|Col27|Col28|Col29|Col30|Col31|
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
||||||||||||||||||||||||||||||||
||||||||||||||||||||||||||||||||



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-56-3.png)



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-56-5.png)



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-56-7.png)



Figure S38: Projections of installed capacity (GW) for thermal and nuclear power toward 2060.


Despite the identification of an under-construction inland nuclear power plant located in


Macheng, Hubei [[4]], the planned installation capacity of nuclear power in this model is assumed


to be deployed in coastal provinces due to nuclear security considerations. We adopt the coastal


nuclear installation forecast from [[5]] as the upper bound, as depicted in Figure S39, which amounts


to a total of 230 GW, including the installed capacity.


50


S3 INPUT DATA AND PRE-PROCESS

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-57-0.png)


Figure S39: Nuclear installation potential for each province. Graphic: © Xinjian Xiao and Kejun
Jiang [[5]] .


**S3.5.2** **Technical parameters**


CISPO optimizes the hourly operation of thermal and nuclear power using the relaxed unit


commitment (RUC) algorithm which has been validated with negligible loss of accuracy ( _<_ 1%) [[81]],


and is used in many optimization models [[6,9,13]] .


In the RUC algorithm, we model four operational states for thermal and nuclear units, encom

passing online units, load units, start-up units, and shut-down units. The load unit dispatched to


thermal and nuclear power is constrained by the available online units and maximum/minimum


output rates. We adopt these parameters disclosed in [[6]] for coal, coal CHP, and gas plants, [[9,12]]


for biomass plants, and [[10,15]] for nuclear. The same maximum/minimum output rates are used


for thermal power if equipped with a CCS-system [[9]] . Online units can be augmented from start

up offline units, and transition into offline status through shut-down. Newly start-up/shut-down


units must remain online/offline for a minimum duration, for which we adopt these parameters


for coal, and gas from [[6]], and nuclear from [[10]], and assume biomass with the same value as coal.


The start-up cost for thermal and nuclear power is adopted from [[9]], and the shut-down cost is


assumed as same [[82]] . Committed thermal and nuclear power plants can provide inertia to the


power system, which is estimated by multiplying online capacity with inertia constants [[9]] . We


adopt the inertia constants for thermal and nuclear power from [[9]] . Ramp-up/down rates are used


to constrain the load difference between two consecutive timesteps. The ramp rate is assumed


as 25%/h for coal plants [[8]], 50%/h for gas plants [[6]], 25%/h for biomass plants [[8]], and 5%/h for


51


S3 INPUT DATA AND PRE-PROCESS


nuclear [[15]] . We assume the fuel consumed by coal, gas, biomass, and nuclear power plants in


accordance with the parameters from [[6,10,13,59,83]], which either reports the standard coal used per


kWh generation, or the thermal efficiency for generation, or the non-load unit fuel consumption.


Carbon emissions in the power system are primarily attributable to coal-fired and gas-fired


plants. Emission factors (EF, kgCO 2 /kWh) are employed to quantify the CO 2 emissions from


thermal plants. We adopt the emissions factor of coal (gas) power at 0.82 (0.37) kgCO 2 /kWh


in 2030 and 0.65 (0.3) kgCO 2 /kWh from [[66]], and set these values for 2040 and 2050 using the


linear interpolation. Since biomass power is assumed to combust biofuels, it is considered net

zero emissions [[12]] in the CISPO model. Carbon capture and sequestration (CCS) systems can be


implemented in thermal power plants to mitigate carbon emissions. Previous studies report [[12]]


that the CCS system can capture approximately 90% of the total emissions. Consequently, the


emissions factor for CCS plants decreases by 90%. For biomass energy with CCS (BECCS), we


use the EF reported by Fan et al. [[12]], which are -1.77 kgCO 2 /kWh in 2030, -1.40 kgCO 2 /kWh in


2040, -1.21 kgCO 2 /kWh in 2050, and -1.10 kgCO 2 /kWh in 2060. However, the CCS system may


lead to an efficiency loss, for which we adopt a value of 5% based on [[8]] . We present the detailed


technical parameters in Table S14.


Table S14: Technical parameters for thermal and nuclear power.



_φ_
Technology _pt_

[%]



~~_φ_~~ _pt_ _τ_ _pt,up_ _τ_ _pt,dn_ _κ_ _[su]_ _pt_ [/] _[κ]_ _[sd]_ _pt_ _ι_ _pt_ _δ_ _pt_ _[up]_ [/] _[δ]_ _pt_ _[dn]_ _f_ _pt_ _[load]_ _ξ_ _pt_ _[ccs]_

[%] [h] [h] [yuan/MW] [s] [%/h] [MJ/kWh] [%]



Biomass 35 100 8 8 500 2.94 25 9.0 0

Biomass CCS 35 100 8 8 500 2.94 25 10.30 5

Coal 40 100 8 8 1100 5.89 25 9.00 0

Coal CCS 40 100 8 8 1100 5.89 25 11.25 5

Coal CHP 60 90 8 8 1100 5.89 25 9.47 0

Coal CHP CCS 60 90 8 8 1100 5.89 25 11.25 5

Gas 30 100 4 2 300 4.97 50 6.31 0

Gas CCS 30 100 4 2 300 4.97 50 7.83 5

Gas CHP 30 100 4 2 300 4.97 50 6.31 0

Gas CHP CCS 30 100 4 2 300 4.97 50 7.83 5

Nuclear 85 100 22 22 2200 4.07 5 10.00 0


In this table, ~~_φ_~~ _pt_ is the maximum output rate of thermal and nuclear power if online; _φ_ ~~_p_~~ _t_ is the minimum
output rate of thermal and nuclear power if online; _τ_ _pt,up_ is the minimum online duration of thermal and nuclear
power if started up; _τ_ _pt,dn_ is the minimum offline duration of thermal and nuclear power if shut down; _κ_ _[su]_ _pt_ [/] _[κ]_ _[dn]_ _pt_
is the start-up/shut-down cost of thermal and nuclear power; _ef_ _pt_ is the carbon emissions factor of thermal
power; _ι_ _pt_ is the inertia constant of thermal and nuclear power; _δ_ _pt_ _[up]_ [/] _[δ]_ _pt_ _[dn]_ [is the maximum ramp-up/down rate]
for thermal and nuclear power; _f_ _pt_ _[load]_ is the fuel consumption by the load capacity of thermal and nuclear power;
_ξ_ _pt_ _[ccs]_ is the efficiency loss of thermal power if equipped with CCS technology; these symbols are consistent with
that in the model formula section.


**S3.5.3** **Cost projections**


In the objective function of CISPO, the annual cost of thermal and nuclear power encompassing


CapEx, fixed O&M cost, variable O&M cost, start-up/shut-down cost, and fuel cost. We adopt


52


S3 INPUT DATA AND PRE-PROCESS


the CapEx projections toward 2050 for thermal power from [[84]] . Since the CapEx remains stable


after 2040, we assume these values in 2060 are the same as in 2050 or with a slight decrease.


Bowen et al. report that the investment cost for nuclear power in China is around 2,800 $/kW


(about 19,320 yuan/kW) in 2030 and 2,500 $/kW (about 17,250 yuan/kW) in 2050 [[85]] . We


project these investment forecasts to 2060 using an equal decrease rate (about 1,035 yuan/kW


every 10 years). Figure S40 shows the CapEx projection for thermal and nuclear power in this


study. The annual fixed O&M cost (in yuan/MW*yr) is given as a fraction of the CapEx. We


adopt fixed O&M for coal, coal CHP, gas, and gas CHP at 2% of their CapEx, which lies in


the range 1.9%–2.6% from [[6]] . A slight increase to 2.5% of CapEx for the CCS-equipped plant


is assumed. This parameter for nuclear power is set at 1.5% from [[10]] . We assume the variable


O&M cost (in yuan/MWh) at 31 for coal (non-CHP and CHP), 58 for coal CCS (non-CHP and


CHP), 23 for gas (non-CHP and CHP), 46 for gas CCS (non-CHP and CHP), 35 for biomass, 60


for biomass CCS, and 14 for nuclear according to [[10,13,86]] .


In the CISPO model, the process of power generation employs various fuel sources, including


coal, natural gas, biomass, and nuclear energy, which are consumed to produce electricity. For


coal prices, we collect China’s electricity coal price in January 2020 for each province from the


Price Testing Center of the National Development and Reform Commission (NDRC) [[87]], and


scale up these prices to 2022 using the yearly average coal cost in 2022 (722 yuan/ton) and in


2020 (543 yuan/ton) [[88,89]] . For gas prices, we collect the provincial-level fuel cost data from the


State Council [[6,90]] . Figure S41 illustrates the coal and gas fuel costs across different provinces.


Each province’s price is set at 700 yuan/ton assuming that bio-fuel is locally sourced. As for


nuclear power, the fuel cost is assumed to be 0.069 yuan/kWh (including spent fuel treatment


cost) according to [[91]] .

|Col1|Col2|Col3|Col4|Col5|Col6|Col7|Col8|
|---|---|---|---|---|---|---|---|
||Coal|Coal CCS<br>|Nuclear<br>Gas|Gas CCS|Biomass|Biomass CCS|Biomass CCS|
|||||||||
|||||||||
|||||||||
|||||||||
|||||||||
|||||||||
|||||||||
|||||||||
|||||||||
|||||||||



Figure S40: Cost projections of thermal and nuclear power.


53


S3 INPUT DATA AND PRE-PROCESS



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-60-3.png)

Figure S41: Coal and gas fuel cost for each province.


**S3.6** **Energy storage**


In the CISPO model, we model the energy storage using representative storage technologies, which


have been implemented in many other models [[6,8,9,15,92]] . We primarily consider two types of stor

age technologies–pumped hydro storage (PHS, 8-hr duration) representing mechanical storage [[8]]


and lithium-ion batteries (BAT, 4-hr duration) representing electrochemical storage.


**S3.6.1** **Installed and potential capacity**


The installed capacity by 2022 and the expected installed capacity of PHS before the target


optimization year (e.g. 2050) are modeled as the lower bound for each power grid. We collect


the deployment and under-construction information of PHS from [[93]], which also provides the


expected year for production. In this dataset, we find a total of 46.69 GW installed PHS capacity


by 2022 and 86.43 GW expected capacity for production in 2030. The provincial data is shown


in Table S15.


54


S3 INPUT DATA AND PRE-PROCESS


Table S15: Installed (by 2022) and under construction pumped hydro storage capacity (GW) for
each province. Under-construction projects with an expected production year before the target
year are taken as installed capacity. These capacities are the lower bound of PHS deployment in
each optimization year.

Province 2022 2030 Province 2022 2030 Province 2022 2030

Anhui 4.68 5.96 Henan 1.32 4.92 Shaanxi 0 1.4

Beijing 0.8 0.8 Hubei 1.27 1.27 Shandong 4 7
Chongqing 0 1.2 Hunan 1.2 2.6 Shanghai 0 0
Fujian 1.2 6.8 Jiangsu 2.6 3.95 Shanxi 1.2 3.9
Gansu 0 0 Jiangxi 1.2 2.4 Sichuan 0 0
Guangdong 9.68 9.68 Jilin 1.7 2.9 Tianjin 0 0
Guangxi 0 0 Liaoning 1.2 4 Xinjiang 0 2.4
Guizhou 0 0 Mengdong 0 1.2 Xizang 0.1 0.1
Hainan 0.6 0.6 Mengxi 1.2 1.2 Yunnan 0 0
Hebei 4.87 8.67 Ningxia 0 0 Zhejiang 6.68 12.28
Heilongjiang 1.2 1.2 Qinghai 0 0 Total 46.69 86.43


By the end of 2022, China had commissioned approximately 8.7 GW of new type energy


storage installations, of which battery storage systems accounted for roughly 95% [[80]] . We collect


the battery installation capacities of the top 10 provinces, which cumulatively amount to 6.8


GW, as illustrated in Table S16. Concomitant with the escalating deployment of renewable


energy sources, many provinces have promulgated their respective targets for battery storage


system installations by the culmination of the 14th Five-Year Plan (2025), as delineated in Table


S17. These targeted installation capacities are presumed to represent the lower bounds for the


year 2030.


Table S16: Installed capacity (GW) of BAT for top 10 provinces by 2022.


Province Capacity Province Capacity Province Capacity Province Capacity


Shandong 1.55 Hunan 0.63 Tianjin 0.5 Anhui 0.412
Ningxia 0.898 Mengxi 0.586 Jiangsu 0.489 Total 6.8
Guangdong 0.71 Hubei 0.544 Qinghai 0.482


Table S17: Provincial battery storage deployment target (GW) by 2025.


Province Capacity Province Capacity Province Capacity Province Capacity


Anhui [[94]] 3 Guizhou [[95]] 1 Jiangxi [[96]] 1 Shanxi [[97]] 6
Beijing [[98]] 0.7 Hainan 0 Jilin [[99]] 0.25 Sichuan [[100]] 2
Chongqing 0 Hebei [[101]] 4 Liaoning [[102]] 1 Tianjin [[103]] 0.5
Mengdong [[104]] 2 Heilongjiang 0 Ningxia [[105]] 5 Mengxi [[104]] 3
Fujian [[106]] 0.6 Henan [[107]] 2.2 Qinghai [[108]] 6 Xinjiang 0
Gansu [[109]] 6 Hubei [[110]] 2 Shaanxi 0 Xizang 0
Guangdong [[111]] 3 Hunan [[112]] 2 Shandong [[113]] 5 Yunnan [[114]] 2
Guangxi [[115]] 2 Jiangsu [[116]] 2.6 Shanghai 0 Zhejiang [[117]] 3


In the CISPO model, we do not set deployment upper bound for battery storage because of


55


S3 INPUT DATA AND PRE-PROCESS


its flexible installation feature. Concerning the development potential for pumped hydro stor

age, we employ the latest nationwide pumped storage planning undertaken by provincial energy


authorities in 2020, organized by the National Energy Administration (NEA) [[118]] . During the


planning process, a comprehensive evaluation was conducted, taking into account factors such


as geography, geology, water resources, submergence areas, environmental impacts, and technical


conditions. Over 1,500 potential sites were identified across China, with a cumulative capacity of


approximately 1,600 GW. Building upon this, the NEA proposed priority projects for long-term


development, totaling around 679 GW. We establish this long-term development capacity, in ad

dition to the installed and under-construction capacity, as the upper capacity constraint in our


model, with the provincial breakdown delineated in Table S18.


Table S18: Long-term potential capacity (GW, including installed capacity) of the PHS by
province in mainland China.


Province Potential capacity Province Potential capacity Province Potential capacity


Anhui 27.76 Henan 28.62 Shaanxi 36.95

Beijing 0.8 Hubei 37.67 Shandong 20.6
Chongqing 9.6 Hunan 35.41 Shanghai 0
Fujian 6.8 Jiangsu 7.55 Shanxi 18.9
Gansu 33.5 Jiangxi 15.41 Sichuan 14.6
Guangdong 45.48 Jilin 35.2 Tianjin 2.4
Guangxi 22.8 Liaoning 22.6 Xinjiang 39
Guizhou 40.6 Mengdong 6.2 Xizang 72.04
Hainan 8.8 Mengxi 7.9 Yunnan 0
Hebei 27.47 Ningxia 7.8 Zhejiang 50.275
Heilongjiang 42.5 Qinghai 40.3 Total 765.5


**S3.6.2** **Cost projections and technical parameters**


Given the projected stability of CapEx associated with PHS in the forthcoming years [[119]], we


have adopted the CapEx of PHS reported in [[84]] towards the year 2050, with a slight decrease


assumed for 2060. Figure S42 illustrates the cost projections for energy storage technologies.


The fixed O&M costs and variable O&M costs are assumed to be 1.5% of CapEx [[120]] and 0.0015


yuan/kWh [[121]], respectively. Furthermore, the lifespan is presumed to be 40 years [[122]] . Predicated


on the analyses of PHS conducted by [[119,122]], we postulate that the charge efficiency and discharge


efficiency of PHS are 88%, while the self-discharge rate is 0%.


56


S3 INPUT DATA AND PRE-PROCESS




|Col1|Col2|Col3|PHS|BAT|Col6|Col7|Col8|
|---|---|---|---|---|---|---|---|
|||||||||
|||||||||
|||||||||
|||||||||
|||||||||
|||||||||
|||||||||
|||||||||



Figure S42: Cost projections for energy storage.


The lithium-ion battery technology is selected as the representative electrochemical energy


storage system in this analysis. The cost projections for battery energy storage systems in China


towards the year 2050 are adopted from [[84]], with the value for 2060 being derived from [[8]] . It


is assumed that the fixed O&M costs are equivalent to 1.0% of the CapEx, as suggested by [[10]] .


The variable O&M costs are presumed to be 20 yuan/MWh (approximately 3 $/MWh), which


aligns with the range of approximately 1.2–4.8 $/MWh provided by [[119]] . The self-discharge rate


is postulated as 2% per month (approximately 0.07%/day), in accordance with [[123]] . Furthermore,


it is assumed that the round-trip efficiency is about 90%, as reported by [[124]] . The anticipated


lifespan of the battery energy storage system is assumed to be 15 years, as per [[122]] . We show the


technical parameters for energy storage in Table S19.


Table S19: Performance parameters for storage technologies.


Fixed O&M Variable O&M Charge Discharge Self-discarge Duration Lifetime
Type

[% of CapEx] [yuan/MWh] efficiency [%] efficiency [%] rate [%/day] [hrs] [yrs]


PHS 1.5 1.5 88 88 0 8 40

BAT 1.0 20 95 95 0.07 4 15


**S3.7** **Inter-grid transmission**


We collect the existing inter-grid transmission line data by 2020 from the State Grid and China


Southern Power Grid. This dataset provides AC/DC, voltage and capacity information for each


inter-grid transmission line. In this mid-long term power system planning model, we apply the


pipeline model (also called transportation model) to simulate the inter-grid transmission flow


to avoid introducing binary variables, which has been validated [[125]] and used in many other


models [[6,9,15]] . Under this assumption, we aggregate these lines to the power grid level and show


the capacity for the inter-grid transmission line in Figure S43.


57


S3 INPUT DATA AND PRE-PROCESS

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-64-0.png)


Figure S43: Capacity (GW) of existing inter-grid transmission lines.


The cost of an inter-grid transmission line contains two main parts–substation and overhead


line, with the per unit of capacity distance depending on its voltage level. We adopt the cost


assumption on the CapEx of transmission line and substation by voltage level from [[73]], shown in


Table S20. The lifetime for substations is assumed to be 25 years which lies in the range from [[72]]


(20 years) to an actual case (30 years) [[126]] . The lifetime for overhead lines is assumed to be 50


years following [[127]] . The distance between two provinces is calculated as the geographic distance


between their centroids as shown in Figure S43. We use the power loss rate at 0.0032%/km [[128]],


and calculate the long-distance transmission efficiency by


_η_ _uhv_ = (1 _−_ 0 _._ 0032%) _[d]_ _,_


where _d_ is the geographic distance (km).


Table S20: CapEx assumptions of substations and overhead lines.


Voltage [kV] _±_ 1100 1000 _±_ 800 750 _±_ 500 500 330


Substation [yuan/kW] 637 370 592 148 912 159 241
Overhead line [1,000 yuan/km] 7,000 7,080 4,950 2,990 1,986 2,640 1,214


In this model, we allow any two grids to construct new inter-provincial transmission lines,


with the caveat that Xizang and Hainan are limited to connecting only to neighboring provinces


owing to the challenges of estimating costs and building lines over heavily mountainous terrain


and over the sea, respectively. To determine the voltage level of the transmission line between two


58


S3 INPUT DATA AND PRE-PROCESS


specific grids, we first use the voltage level information of existing transmission lines connecting


these two grids and assume the highest would be the voltage level for new lines built in the future.


For a few lines with voltage level lower than 500 kV (due to small capacity at the current level),


we assume 500 kV AC lines will be built in the future. And then for girds that need to build


new lines, we determine the optimal AC/DC configuration and voltage levels by implementing a


pre-optimization procedure following the approach of [[6]] . In this pre-optimization procedure, we


consider 330 kV, 500 kV, 750 kV, and 1,000 kV for AC lines, and ±500 kV, ±800 kV, and ±1,100


kV for DC lines, with AC 330 kV and AC 750 kV only suitable for the Northwest region [[6]] .


Subsequently, maximal transmission distances for AC lines by voltage level are applied. For the


remaining AC and DC options, we calculate the total cost per kW and select the voltage level


with the lowest unit cost for both AC and DC as the optimal expansion choice. The total cost for


each voltage level includes overhead lines and substations/converters, with CapEx values shown


in Table S20. To determine the total substation/converter cost and unit cost (yuan/kW) for


each DC voltage, we assume fixed capacities (3,000 MW for ±500 kV, 8,000 MW for ±800 kV,


and 12,000 MW for ±1100 kV) [[6]] . The transmission capacity of AC lines is related to distance


and voltage level. Based on capacity and distance data of existing lines, we use a polynomial


fitting model to determine the numerical relationship between them [[6]], with results shown in


Figure S44. Finally, we present the pre-optimized choice (AC/DC and voltage level) of newly


built inter-provincial lines in Table S21.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-65-0.png)


Figure S44: Relationship between transmission capacity and distance for 500kV and 1000kV AC
lines [[6]]


59


S3 INPUT DATA AND PRE-PROCESS



60


S3 INPUT DATA AND PRE-PROCESS


**S3.8** **Carbon source and sink match**


To model the transportation of captured carbon dioxide (CO 2 ) from emissions sources to injection


sites, we employ a minimum geographic distance heuristic given that the optimization model does


not determine the locations of newly installed coal, gas, and biomass power units. Specifically,


we allow each power grid to potentially route emissions to any onshore carbon storage reservoir


within the system boundary. The CISPO model then determines the optimal annual pairings of


sources and sinks for transported CO 2, subject to capture costs of 260 yuan/tCO 2, transporta

tion costs of 0.8 yuan/tCO 2 *km, and storage costs of 116 yuan/tCO 2, according to [[63]] . A key


model constraint requires that all CO 2 captured at thermoelectric plants equipped with carbon


capture and sequestration (CCS) technology must be transported and injected exclusively into


the designated storage sites.


**S3.9** **Direct air capture**


In this model, we optimize four direct air capture (DAC) technologies: KOH absorption paired


with regeneration via calcium looping (KOH-Ca looping), KOH absorption paired with regen

eration via bipolar membrane electrodialysis (BPMED), solid sorbent DAC using temperature


vacuum swing adsorption, and MgO ambient weathering with regeneration via calcination. The


deployment of these DAC technologies is optimized at the provincial level. Under this assump

tion, the carbon captured by DACs in each province can be transported to any potential carbon


sequestration site without distance limitations, with transportation costs estimated based on the


distances between the provincial geographical centers and the sequestration sites. The trans

portation and carbon injection costs are consistent with those described in Section S3.8.


We primarily adopt the cost parameters for China from Young et al. [[129]] . The total overnight


cost includes the plant cost, owner’s cost, spare parts, startup capital, startup labor, startup


energy, and startup chemicals, as reported in [[129]] . The annual fixed operations and maintenance


(O&M) cost comprises labor costs, maintenance costs, insurance, and local taxes and fees. The


variable O&M costs for DAC include water costs, chemical/mineral usage, and gasoline costs.


Unlike [[129]], we endogenously model the electricity used by DAC for operation, with heat supplied


directly from the power system. We assume that the yearly electricity consumption is evenly


distributed across each hour. For instance, if 8,760 GWh/yr of electricity is used for DAC,


the hourly consumption is 1 GWh/hr in the electricity balance constraint. Additionally, since


we endogenously model the transportation and storage costs for captured CO 2, the variable


O&M costs here do not include transport and storage costs as reported in [[129]] . The cost and


technological parameters for each DAC technology in the base year are presented in Table S22.


The designed lifetime for DAC is assumed to be 25 years, and the coefficient of performance


61


S3 INPUT DATA AND PRE-PROCESS


for the heat pump providing heat through electricity is 2.5. In this study, we adopt the most


conservative scenario (“Low uptake, 25% technology dominance”) from [[129]] to project the CapEx


and O&M costs from 2030 to 2060, as shown in Table S23.


Table S22: Cost (in 2022) and technical parameters for direct air capture.


MgO looping with
Technology KOH with Ca looping KOH with BPMED Solid sorbent
ambient weathering


CapEx
5894 5580 50754 6403

[yuan/tCO2*yr]

Fixed O&M
171 161 1378 184

[yuan/tCO2*yr]

Variable O&M
17 1820 862 14

[yuan/tCO2*yr]

Direct electricity
1.32 22.5 0.99 0.78
use [GJ/tCO2]

Direct heat use
5.3 N/A 9.8 6.2

[GJ/tCO2]


Table S23: Cost projection (ratio to 2022) from 2030 to 2060.


MgO looping with
Year KOH with Ca looping KOH with BPMED Solid sorbent
ambient weathering


2022 1.00 1.00 1.00 1.00

2030 0.94 0.83 0.41 0.95

2040 0.91 0.79 0.36 0.89

2050 0.88 0.75 0.32 0.84

2060 0.75 0.69 0.28 0.72


**S3.10** **Financial parameters**


We convert the upfront capital expenditure cost to yearly annuities using a capital recovery factor


(CRF) _ξ_ by [[8,15]] :


[(] _[1]_ [ +] _[ WACC]_ [)] _[τ]_
_CRF_ = _ξ_ = _[WACC]_ ( _1_ + _[ ×]_ _WACC_ ) _[τ]_ _−_ _1_ _,_ (S3-1)


where _WACC_ is the real weighted average cost of capital, which is as a fraction; and _τ_ is the


financial lifetime (or capital recovery period) of the investment. In this model, we adopt 7.4% for


the _WACC_, close to [[6,130]] . The annualized CapEx cost is given the upfront capex cost multiplied


by the CRF.


62


S4 FORMULA OF THE POWER-SYSTEM OPTIMIZATION MODEL

## **S4 Formula of the Power-system Optimization Model**


**S4.1** **Variables and parameters**


**S4.1.1** **Indices**


  - _pt_ : Power technology, including


**–**
onshore and offshore wind;


**–**
utility-scale and distributed solar photovoltaic (PV);


**–**
concentrating solar power (CSP);


**–**
run-of-river (ror) and reservoir (resvor) hydropower;


**–**
coal, coal combined heat and power (CHP) plant, coal with carbon capture and storage


(CCS), and coal CHP CCS;


**–**
nuclear;


**–**
natural gas, natural gas CHP, natural gas CCS and natural gas CHP CCS;


**–**
biomass and biomass CCS.


  - _z_ : Optimization spatial site (cell for wind, solar PV and CSP, dam site for hydropower).


  - _st_ : Energy storage technology, including lithium-ion battery (BAT) and pumped hydro


storage (PHS).


  - _g_ : Provincial power grid.


  - _sub_ : Substation that meets the modeling condition ( _≥_ 220 kV).


  - _lc_ : Load center.


  - _l_ _g,g_ _′_ : Transmission line between _g_ and _g_ _[′]_ .


  - _c_ : Potential carbon sequestration site.


  - _r_ _g,c_ : Carbon transportation route from grid _g_ to sequestration site _c_ .


  - _dac_ : Direct air capture (DAC), including KOH with scrubbing with bipolar membrane


electrodialysis (BPME, koh b), MgO looping with ambient weathering (mgo ~~a~~ m), solid


sorbent (ssor), and KOH with Ca looping (koh ~~c~~ l).


  - _t_ : Timestep, one hour in this study.


63


S4 FORMULA OF THE POWER-SYSTEM OPTIMIZATION MODEL


**S4.1.2** **Sets**


  - _WE_ : Wind power, including onshore wind and offshore wind, _WE_ = _{onshore, offshore}_ .


  - _PV_ : Solar PV power, including utility-scale and distributed solar PV, _PV_ = _{upv_ _, dpv_ _}_ .


  - _CSP_ : Concentrating solar power, _CSP_ = _{csp}_ ;


  - _HP_ : hydropower, including run-of-river and reservoir hydropower, _HP_ = _{ror_ _, resvor_ _}_ .


  - _CP_ : Coal-fired power, _CP_ = _{coal_ _, coal_ _ccs_ _, cchp, cchp_ _ccs_ _}_ .


  - _GP_ : Gas-fired power, _GP_ = _{gas, gas_ _ccs_ _, gchp, gchp_ _ccs_ _}_ .


  - _BP_ : Biomass power, _BP_ = _{bio, bio_ _ccs_ _}_ .


  - _NP_ : Nuclear power, _NP_ = _{nuclear_ _}_ .


  - _TP_ : Thermal power, _TP_ = _CP ∪_ _GP ∪_ _BP_ .


  - _CCHP_ : Coal-fired combined heat and power, _CCHP_ = _{cchp, cchp_ _ccs_ _}_ .


  - _GCHP_ : Gas-fired combined heat and power, _GCHP_ = _{gchp, gchp_ _ccs_ _}_ .


  - _CHP_ : Combined heat and power, _CHP_ is a set of sets, _CHP_ = _{CCHP_ _, GCHP_ _}_ .


  - _CCS_ : Generator equipped with carbon capture and storage,


_CCS_ = _{coal_ _ccs_ _, cchp_ _ccs_ _, gas_ _ccs_ _, gchp_ _ccs_ _, bio_ _ccs_ _}_ .


  - _CCS_ _pair_ : Power technology equipped with or without CCS,


_CCS_ _pair_ = _{_ ( _coal_ _, coal_ _ccs_ ) _,_ ( _cchp, cchp_ _ccs_ ) _,_ ( _gas, gas_ _ccs_ ) _,_ ( _gchp, gchp_ _ccs_ ) _,_ ( _bio, bio_ _ccs_ ) _}_ .


  - _PT_ : Power technologies, _PT_ = _WE ∪_ _PV ∪_ _CSP ∪_ _HP ∪_ _TP ∪_ _NP_ .


  - _ST_ : Energy storage technologies, _ST_ = _{_ BAT, PHS _}_ .


  - _Z_ _g,pt_ : Optimization spatial sites for wind, solar PV, CSP, and hydropower in grid _g_, _Z_ _g,pt_ =


_{z}, pt ∈_ _WE ∪_ _PV ∪_ _CSP ∪_ _HP_ .


  - _G_ : Provincial power grids, _G_ = _{g}_ .


  - _SUB_ _g_ : Substations in grid _g_ that meet the modeling condition, _SUB_ _g_ = _{sub} ∩_ _G_ .


  - _Z_ _g,pt_ _[sub]_ [: Spatial sites of power] _[ pt]_ [ connected to substation] _[ sub]_ [ in grid] _[ g]_ [,] _[ Z]_ _g,pt_ _[sub]_ [=] _[ SUB]_ _[g]_ _[∩]_ _[Z]_ _[g][,][pt]_ [.]


  - _LC_ _g_ : Load centers in grid _g_, _LC_ _g_ = _{lc} ∩_ _G_ .


  - _SUB_ _g_ _[lc]_ [: Substations in grid] _[ g]_ [ connected to load center] _[ lc]_ [,] _[ SUB]_ _g_ _[lc]_ [=] _[ SUB]_ _[g]_ _[∩]_ _[LC]_ _[g]_ [.]


64


S4 FORMULA OF THE POWER-SYSTEM OPTIMIZATION MODEL


  - _C_ : Potential carbon sequestration sites, _C_ = _{c}_ .


  - _DAC_ : Direct air capture technologies, _DAC_ = _{koh_ ~~_b_~~ _, mgo_ ~~_a_~~ _m, ssor_ _, koh_ ~~_c_~~ _l_ _}_ .


  - _T_ : Optimization timesteps, which covers the whole year, _T_ = _{_ 0 _,_ 1 _,_ 2 _,_ 3 _, . . .,_ 8759 _}_ .


  - _T_ _w_ : Timesteps in winter, including January to March-mid, and November-mid to December


of each year, _T_ _w_ _⊆_ _T_ .


**S4.1.3** **Decision variables**


**In the CISPO model, decision variables are non-negative by default if not specified.**


  - _p_ _g,z,pt_ : Optimized installation capacity of renewable energy _pt_ at site _z_ in grid _g_, GW.


  - _I_ _g,pt,t_ : Integrated capacity of renewable energy _pt_ to the grid _g_ at timestep _t_, GW.


  - _sto_ _[e]_
_g,csp,t_ [: Equivalent electricity power stored in CSP] _[ csp]_ [ in grid] _[ g]_ [ at timestep] _[ t]_ [, GWh.]


  - _q_ _g,z,resvor,t_ _[gen]_ [: Generation flow of reservoir hydropower at site] _[ z]_ [ in grid] _[ g]_ [ at timestep] _[ t]_ [, m] [3] [/s.]


  - _q_ _g,z,resvor,t_ _[spill]_ [: Spillage flow of reservoir hydropower at site] _[ z]_ [ in grid] _[ g]_ [ at timestep] _[ t]_ [, m] [3] [/s.]


  - _v_ _g,z,resvor,t_ : Reservoir level of reservoir hydropower at site _z_ in grid _g_ at timestep _t_, m [3] .


  - _I_ _g,z,resvor,t_ : Integration of reservoir hydropower at site _z_ in grid _g_ at timestep _t_, GW.


  - _u_ _[tot]_
_g,pt_ [: Optimized installation units of thermal and nuclear power] _[ pt]_ [ in grid] _[ g]_ [, unit.]


  - _u_ _[on]_
_g,pt,t_ [: Online units of thermal and nuclear power] _[ pt]_ [ in grid] _[ g]_ [ at timestep] _[ t]_ [, unit.]


  - _u_ _[su]_
_g,pt,t_ [: Start-up units of thermal and nuclear power] _[ pt]_ [ in grid] _[ g]_ [ at timestep] _[ t]_ [, unit.]


  - _u_ _[sd]_
_g,pt,t_ [: Shut-down units of thermal and nuclear power] _[ pt]_ [ in grid] _[ g]_ [ at timestep] _[ t]_ [,unit.]


  - _u_ _[load]_
_g,pt,t_ [: Load units dispatched to thermal and nuclear power] _[ pt]_ [ in grid] _[ g]_ [ at timestep] _[ t]_ [, unit.]


  - _p_ _g,st_ : Optimized installation capacity of storage _st_ in grid _g_, GW.


  - _sto_ _[char]_
_g,st,t_ [: Charging capacity from the grid] _[ g]_ [ to storage] _[ st]_ [ at timestep] _[ t]_ [, GW.]


  - _sr_ _g,st,t_ [+] _[,char]_ [: Upward reserve capacity provided by storage] _[ st]_ [ while charging in grid] _[ g]_ [ at timestep]


_t_, GW.


  - _sr_ _g,st,t_ _[−][,char]_ [: Downward reserve capacity provided by storage] _[ st]_ [ while charging in grid] _[ g]_ [ at]


timestep _t_, GW.


  - _sto_ _[dis]_
_g,st,t_ [: Post-loss discharging capacity from storage] _[ st]_ [ to the grid] _[ g]_ [ at timestep] _[ t]_ [, GW.]


65


S4 FORMULA OF THE POWER-SYSTEM OPTIMIZATION MODEL


  - _sr_ _g,st,t_ [+] _[,dis]_ [: Upward reserve capacity provided by storage] _[ st]_ [ while discharging in grid] _[ g]_ [ at]


timestep _t_, GW.


  - _sr_ _g,st,t_ _[−][,dis]_ [: Downward reserve capacity provided by storage] _[ st]_ [ while discharging in grid] _[ g]_ [ at]


timestep _t_, GW.


  - _sto_ _[e]_
_g,st,t_ [: Energy stored within storage] _[ st]_ [ in grid] _[ g]_ [ at timestep] _[ t]_ [, GWh.]


  - _p_ _l_ _AC_
_g,g_ _[′]_ [: Optimized installation capacity of AC line] _[ l]_ _[g,g]_ _[′]_ [, connecting grid] _[ g]_ [ and] _[ g]_ _[′]_ [, GW.]


  - _p_ _l_ _DC_
_g,g_ _[′]_ [: Optimized installation capacity of DC line] _[ l]_ _[g,g]_ _[′]_ [, from grid] _[ g]_ [ to] _[ g]_ _[′]_ [, GW.]

  - _[−→]_ _f_ _l_ _g,gAC_ _[′]_ _[,t]_ [: Power transmitted from grid] _[ g]_ [ to] _[ g]_ _[′]_ [ along AC line] _[ l]_ _g,g_ _[AC]_ _[′]_ [ at timestep] _[ t]_ [, GW.]

  - _[←−]_ _f_ _l_ _g,gAC_ _[′]_ _[,t]_ [: Power transmitted from grid] _[ g]_ _[′]_ [ to] _[ g]_ [ along AC line] _[ l]_ _g,g_ _[AC]_ _[′]_ [ at timestep] _[ t]_ [, GW.]

  - _[−→]_ _f_ _l_ _g,gDC_ _[′]_ _[,t]_ [: Power transmitted from grid] _[ g]_ [ to] _[ g]_ _[′]_ [ along DC line] _[ l]_ _g_ _[DC]_ _[′]_ _,g_ [at timestep] _[ t]_ [, GW.]


  - _p_ _g,dac_ : Optimized installation capacity of DAC in grid _g_, MtCO 2 /yr.


  - _m_ _g,dac_ : Annual carbon captured by DAC in grid _g_, MtCO 2 .


  - _m_ _r_ _g,c_ : Annual carbon transported from grid _g_ to carbon sequestration site _c_, MtCO 2 .


**S4.1.4** **Intermediate variables**


  - _I_ _g,t_ _[local]_ : Load dispatched to local generation in grid _g_ at timestep _t_, GW.


  - _ramp_ _[up]_ _g,pt,t_ [: Ramp up capacity of thermal and nuclear power] _[ pt]_ [ in grid] _[ g]_ [ at timestep] _[ t]_ [, GW.]


  - _ramp_ _[dn]_ _g,pt,t_ [: Ramp down capacity of thermal and nuclear power] _[ pt]_ [ in grid] _[ g]_ [ at timestep] _[ t]_ [,]


GW.


  - _ele_ _g,dac_ : Electricity load consumption by DAC _dac_ in grid _g_, GW.


**S4.1.5** **Parameters**


**Technical parameters**


  - _cf_ _g,z,pt,t_ : Capacity factor of power _pt_ at site _z_ in grid _g_ at timestep _t_, where _pt ∈_ _WE ∪_


_PV ∪_ _CSP ∪{ror_ _}_, _cf ∈_ [0 _,_ 1].


  - ~~_p_~~ _g,z,pt_ : Maximum installation capacity potential of power _pt_ at site _z_ in grid _g_, GW.


  - _p_ : Installed capacity before optimization year of power _pt_ at site _z_ in grid _g_, GW.
~~_g_~~ _,z,pt_


  - _q_ _g,z,t_ _[in]_ [: Natural inflow for hydropower at site] _[ z]_ [ in power grid] _[ g]_ [ at time] _[ t]_ [, m] [3] [/s.]


~~_c_~~ _ap_

  - _V_
_g,z,resvor_ [: Upper reservoir level of reservoir hydropower at site] _[ z]_ [ in grid] _[ g]_ [, m] [3] [.]


66


S4 FORMULA OF THE POWER-SYSTEM OPTIMIZATION MODEL


  - _V_ _[cap]_
~~_g_~~ _,z,resvor_ [: Lower reservoir level of reservoir hydropower at site] _[ z]_ [ in grid] _[ g]_ [, m] [3] [.]


  - _H_ _g,z,resvor_ : Water head of reservoir hydropower at site _z_ in grid _g_, m;


  - _f_ _pt_ _[load]_ : Fuel consumption by load capacity of thermal and nuclear power _pt_, GJ/GWh.


  - _f_ _pt_ _[on]_ [: Fuel consumption by online capacity of thermal and nuclear power] _[ pt]_ [, GJ/GWh.]


  - _ξ_ _pt_ _[ccs]_ [: Efficiency loss of thermal power] _[ pt]_ [ if equipped with CCS technology,] _[ ξ]_ _pt_ _[ccs]_ _[∈]_ [[0] _[,]_ [ 1].]


  - _η_ _pt_ _[ccs]_ [: Carbon capture rate of thermal power] _[ pt]_ [ if equipped with CCS technology,] _[ η]_ _pt_ _[ccs]_ _[∈]_ [[0] _[,]_ [ 1].]


  - _ϱ_ _pt_ : Per-unit capacity of thermal and nuclear power, GW/unit.


  - _δ_ _pt_ _[up]_ [: Maximum ramping up rate of thermal and nuclear power] _[ pt]_ [, %/h.]


  - _δ_ _pt_ _[dn]_ [: Maximum ramping down rate of thermal and nuclear power] _[ pt]_ [, %/h.]


  - _φ_ ~~_p_~~ _t_ : Minimum output rate of thermal and nuclear power _pt_ if online, %.


  - ~~_φ_~~ _pt_ : Maximum output rate of thermal and nuclear power _pt_ if online, %.


  - _τ_ _pt,up_ : Minimum online duration of thermal and nuclear power _pt_ if started up, h.


  - _τ_ _pt,dn_ : Minimum offline duration of thermal and nuclear power _pt_ if shut down, h.


  - _ef_ _pt_ : Carbon emissions factor of thermal power _pt_, where _pt ∈_ _TP_, MtCO 2 /GWh.


_cal_

  - _therm_
_g,bio_ [: Maximum available biomass fuel for power generation in grid] _[ g]_ [, GJ/yr.]


  - ~~_p_~~ _g,st_ : Maximum installation capacity potential of storage _st_ in power grid _g_, GW.


  - _p_ : Installed capacity before optimization year of storage _st_ in grid _g_, GW.
~~_g_~~ _,st_


  - _η_ _st_ _[char]_ : Charge efficiency of storage technology _st_, %.


  - _η_ _st_ _[dis]_ [: Discharge efficiency of storage technology] _[ st]_ [, %.]


  - _sto_ _[dur]_ _st_ [: Duration of storage technology] _[ st]_ [, h.]


  - _ζ_ _st_ : Self-discharge rate of storage technology _st_, %/h.


  - _d_ _[sub]_
_g,z,pt_ [: Geographical distance from site] _[ z]_ [ in power grid] _[ g]_ [ to the nearest substation connecting]


renewable energy _pt_, where _pt ∈_ _WE ∪_ _PV ∪_ _CSP ∪_ _HP_, km.


  - _d_ _[lc]_
_g,sub_ [: Geographical distance from substation] _[ sub]_ [ in power grid] _[ g]_ [ to the nearest load center,]


km.


  - _ξ_ _l_ _[trans]_ : Power loss rate of transmission line, %/km,


67


S4 FORMULA OF THE POWER-SYSTEM OPTIMIZATION MODEL


  - _d_ _l_ : Geographical distance of transmission line _l_, km.


  - _η_ _l_ : Transmission efficiency of transmission line _l_ given by (1 _−_ _ξ_ _l_ ) _[d]_ _[l]_, %.


  - _p_ : Installed capacity of DAC ( _dac_ ) before optimization year in grid _g_, MtCO 2 /yr.
~~_g_~~ _,dac_


  - _η_ _dac_ : Carbon sequestration efficiency of DAC ( _dac_ ), which refers to the ratio of valid carbon


sequestrated to the total carbon captured, _η_ _dac_ _∈_ [0 _,_ 1].


  - _d_ _r_ _g,c_ : Geographical distance of carbon transport route _r_ _g,c_, km.


  - _E_ : Annual carbon emissions limitation, where _E <_ 0 indicates negative emissions, MtCO 2 .


  - _C_ _c_ : Annual carbon injection capacity limitation in each carbon sequestration site _c_, MtCO 2 .


  - _dem_ _g,t_ : Power demand in grid _g_ at timestep _t_, GW.


  - _ρ_ [+] _sr_ [: Upward spinning reserve capacity requirement, %.]


  - _ρ_ _[−]_ _sr_ [: Downward spinning reserve capacity requirement, %.]


  - _ρ_ _vre_ : Reserve capacity requirement for variable renewable energy integration, %.


  - _ι_ _pt_ : Inertia constant of power _pt_, s.


  - _ι_ _st_ : Inertia constant of storage _st_, s.


  - _ι_ 0 : Current inertia level, determines inertia requirement with power demand, s.


  - _ι_ _tol_ : Ratio of current inertia level, which reflects the tolerance of system inertia drop, %.


**Economic parameters**


  - _κ_ _[cap]_ _pt_ [: Capital cost of power] _[ pt]_ [, yuan/GW.]


  - _κ_ _[cap]_ _st_ [: Capital cost of storage technology] _[ st]_ [, yuan/GW.]


  - _κ_ _[cap]_ _l_ : Capital cost of transmission line _l_, yuan/GW·km.


  - _κ_ _[cap]_
_pt,spur_ [: Capital cost of spur line] _[ spur]_ [ connecting power] _[ pt]_ [ to the substation, CISPO]


assumes AC 220 kV line for onshore wind, solar PV, CSP, hydropower, and AC 220 kV


submarine cable for offshore wind, yuan/GW·km.


  - _κ_ _[cap]_
_sub_ [: Capital cost of substation] _[ sub]_ [, yuan/GW.]


  - _κ_ _[cap]_
_trunk_ [: Capital cost of trunk line] _[ trunk]_ [ connecting substation to load center, yuan/GW][·][km.]


  - _κ_ _[cap]_
_dac_ [: Capital cost of DAC] _[ dac]_ [, yuan/(MtCO] [2] [/yr).]


68


S4 FORMULA OF THE POWER-SYSTEM OPTIMIZATION MODEL


  - _χ_ _pt_ : Capital recovery factor of investment to power _pt_, %/yr.


  - _χ_ _st_ : Capital recovery factor of investment to storage technology _st_, %/yr.


  - _χ_ _l_ : Capital recovery factor of investment to transmission line _l_, %/yr.


  - _χ_ _pt,spur_ : Capital recovery factor of investment to spur line _spur_ for power _pt_, %/yr.


  - _χ_ _trunk_ : Capital recovery factor of investment to trunk line _trunk_, %/yr.


  - _χ_ _dac_ : Capital recovery factor of investment to DAC _dac_, %/yr.


  - _κ_ _[fuel]_ _pt_ : Fuel cost of thermal and nuclear power _pt_, yuan/GJ.


  - _κ_ _[su]_ _pt_ [: Start-up cost of thermal and nuclear power] _[ pt]_ [, yuan/GW.]


  - _κ_ _[sd]_
_pt_ [: Shut-down cost of thermal and nuclear power] _[ pt]_ [, yuan/GW]


  - _κ_ _[fom]_ _pt_ : Fixed operation and maintenance (O&M) cost of power _pt_, yuan/GW·yr.


  - _κ_ _[fom]_ _st_ : Fixed O&M cost of storage technology _st_, yuan/GW·yr.


  - _κ_ _[fom]_ _l_ : Fixed O&M cost of transmission line _l_, yuan/GW·yr.


  - _κ_ _[fom]_
_pt,spur_ [: Fixed O&M cost of spur line] _[ spur]_ [ connecting power] _[ pt]_ [, yuan/GW][·][yr.]


  - _κ_ _[fom]_
_trunk_ [: Fixed O&M cost of trunk line] _[ trunk]_ [, yuan/GW][·][yr.]


  - _κ_ _[fom]_
_dac_ [: Fixed O&M cost of DAC] _[ dac]_ [, yuan/(MtCO] [2] [/yr)][·][yr.]


  - _κ_ _[vom]_ _pt_ : Variable O&M cost of power _pt_, yuan/GWh.


  - _κ_ _[vom]_ _st_ : Variable O&M cost of storage technology _st_, yuan/GWh.


  - _κ_ _[vom]_ _l_ : Variable O&M cost of transmission line _l_, yuan/GWh.


  - _κ_ _[vom]_ _dac_ [: Variable O&M cost of DAC] _[ dac]_ [, yuan/MtCO] [2] [.]


  - _κ_ _[capture]_ _ccs_ : Cost of capturing CO 2 in carbon source, yuan/MtCO 2 .


  - _κ_ _[inject]_ _ccs_ : Cost of injecting CO 2 to storage well, yuan/MtCO 2 .


  - _κ_ _[transport]_ _ccs_ : Cost of transporting CO 2 from source to sink, yuan/km·MtCO 2 .


69


S4 FORMULA OF THE POWER-SYSTEM OPTIMIZATION MODEL


**S4.2** **Objective function**


The optimization objective of CISPO is to minimize the annual system-wide cost subject to sets


of engineering, economics, and policy-related constraints, similar to [[6,8,10,15,131,132]], including 1)


annual investment and O&M costs of wind, solar PV, CSP, hydropower, thermal and nuclear


power, energy storage and transmission line; 2) ramp, start-shut and fuel-consumption costs of


thermal and nuclear power; 3) CO 2 capture, transport and storage costs; and 4) costs of equipped


other technology in specific scenarios. Formally, the objective function is as follows:



min : _f_ = �


_g_


+
�


_g_


+
�


_g_


+
�


_g_


+
�


_g_


+
�


_g_


+
�


_g_



�

_pt_

�

_pt_

�



�( _χ_ _pt_ _× κ_ _[cap]_ _pt_ [+] _[ κ]_ _[fom]_ _pt_ ) _× p_ _g,z,pt_ (S4-1a)


_z_



_κ_ _[vom]_ _pt_ _× u_ _[load]_ _g,pt,t_ _[×][ ϱ]_ _[pt]_ []] (S4-1c)

_t_




[( _χ_ _pt_ _× κ_ _[cap]_ _pt_ [+] _[ κ]_ _[fom]_ _pt_ ) _× p_ _g,z,pt_ + �

_z_ _t_



�



_κ_ _[vom]_ _pt_ _× I_ _g,pt,t_ ] (S4-1b)

_t_



�[( _χ_ _pt_ _× κ_ _[cap]_ _pt_ [+] _[ κ]_ _[fom]_ _pt_ ) _× u_ _[tot]_ _g,pt_ _[×][ ϱ]_ _[pt]_ [+] �

_pt_ _t_



�

_pt_

�

_pt_

�

_pt_

�



�[ _κ_ _[fuel]_ _pt_ _×_ ( _f_ _pt_ _[load]_ _× u_ _[load]_ _g,pt,t_ [+] _[ f]_ _pt_ _[on]_ _[×][ u]_ _[on]_ _g,pt,t_ [)] _[ ×][ ϱ]_ _[pt]_ _[×]_ [ ∆] _[t]_ []] (S4-1d)


_t_


�( _κ_ _[su]_ _pt_ _[×][ u]_ _[su]_ _g,pt,t_ [+] _[ κ]_ _[sd]_ _pt_ _[×][ u]_ _[sd]_ _g,pt,t_ [)] (S4-1e)


_t_




[ _κ_ _[fuel]_ _pt_ _×_ ( _f_ _pt_ _[load]_ _× u_ _[load]_ _g,pt,t_ [+] _[ f]_ _pt_ _[on]_ _[×][ u]_ _[on]_ _g,pt,t_ [)] _[ ×][ ϱ]_ _[pt]_ _[×]_ [ ∆] _[t]_ []] (S4-1d)

_t_



_κ_ _[vom]_ _st_ _×_ ( _sto_ _[char]_ _g,st,t_ [+] _[ sto]_ _[dis]_ _g,st,t_ [)]] (S4-1g)

_t_



�



�( _κ_ _[up]_ _pt_ _[×][ ramp]_ _[up]_ _g,pt,t_ [+] _[ κ]_ _[dn]_ _pt_ _[×][ ramp]_ _[dn]_ _g,pt,t_ [)] (S4-1f)


_t_



�[( _χ_ _st_ _× κ_ _[cap]_ _st_ [+] _[ κ]_ _[fom]_ _st_ ) _× p_ _g,st_ + �


_st_ _t_



+
�

_l_ _[AC]_
_g,g_ _[′]_


+
�

_l_ _[DC]_
_g,g_ _[′]_


+
�


_g_


+
�


_g_


+
�


_g_


+
�




[( _χ_ _l_ _×_ ( _κ_ _[cap]_ _l_ _× d_ _l_ + _κ_ _[cap]_ _sub_ [) +] _[ κ]_ _l_ _[fom]_ ) _× p_ _l_ + � _κ_ _[vom]_ _l_ _×_ ( _[−→]_ _f_ _[AC]_ _l,t_ [+] _[ ←−][f]_ _[ AC]_ _l,t_ [)]] (S4-1h)


_t_



_u_ _[load]_ _g,pt,t_ (S4-1m)

_t_




[( _χ_ _l_ _×_ ( _κ_ _[cap]_ _l_ _× d_ _l_ + _κ_ _[cap]_ _con_ [) +] _[ κ]_ _[fom]_ _l_ ) _× p_ _l_ + � _κ_ _[vom]_ _l_ _×_ _[−→]_ _f_ _[DC]_ _l,t_ []] (S4-1i)


_t_



�

_pt_



�( _χ_ _pt,spur_ _×_ ( _κ_ _[cap]_ _spur_ _[×][ d]_ _[sub]_ _g,z,pt_ [+] _[ κ]_ _[cap]_ _sub_ [) +] _[ κ]_ _[fom]_ _pt,spur_ [)] _[ ×][ p]_ _g,z,pt_ _[sub]_ (S4-1j)


_z_



�( _χ_ _trunk_ _×_ ( _κ_ _[cap]_ _trunk_ _[×][ d]_ _[lc]_ _g,sub_ [+] _[ κ]_ _[cap]_ _sub_ [) +] _[ κ]_ _[fom]_ _trunk_ [)] _[ ×][ p]_ _[lc]_ _g,sub_ (S4-1k)


_sub_

�[( _χ_ _dac_ _× k_ _dac_ _[cap]_ [+] _[ k]_ _dac_ _[fom]_ [)] _[ ×][ p]_ _[g,dac]_ [ +] _[ k]_ _dac_ _[vom]_ _[×][ m]_ _[g,dac]_ []] (S4-1l)


_dac_



_κ_ _[capture]_ _ccs_ _× η_ _pt_ _[ccs]_ _[×][ ε]_ _[pt]_ _[×]_ [ ∆] _[t]_ _[×]_ �
_g_ _t_



+ � ( _κ_ _[inject]_ _ccs_ + _κ_ _[transport]_ _ccs_ _× d_ _r_ _g,c_ ) _× m_ _r_ _g,c_ (S4-1n)


_<g,c>_



+ _O._ (S4-1o)


In the objective function:


- _g ∈_ _G, z ∈_ _Z_ _g,pt_ _, t ∈_ _T_ ;


- Formula S4-1a is the annual cost of wind, solar PV, and CSP installation, _pt ∈_ _WE ∪_ _PV ∪_


_CSP_ ;


70


S4 FORMULA OF THE POWER-SYSTEM OPTIMIZATION MODEL


  - Formula S4-1b is the annual cost of hydropower installation, _pt ∈_ _HP_ ;


  - Formula S4-1c is the annual investment cost and O&M cost of thermal and nuclear power,


_pt ∈_ _TP ∪_ _NP_ ;


  - Formula S4-1d is the yearly fuel cost of thermal and nuclear power, _pt ∈_ _TP ∪_ _NP_ ;


  - Formula S4-1e is the yearly start-up and shut-down cost of thermal and nuclear power,


_pt ∈_ _TP ∪_ _NP_ ;


  - Formula S4-1f is the yearly ramping cost of thermal and nuclear power, _pt ∈_ _TP ∪_ _NP_ ;


  - Formula S4-1g is the annual investment and O&M cost of energy storage, _st ∈_ _ST_ ;


  - Formula S4-1h and S4-1i is the annual investment and O&M cost of inter-grid transmission


lines, where _k_ _l_ _[vom]_ is a slight value (0.001 yuan/kWh in this study) introduced to avoid


hourly bidirectional power flows [[15]], which is also imposed in DC lines to avoid artificial


preference in AC/DC line;


  - Formula S4-1j is the annual investment cost of spur line, where _p_ _[sub]_ _g,z,pt_ [represents the capacity]


of spur line connecting power _pt_ at site _z_ to substation _sub_ . The capacity constraint can be


found in formula S4-18, _pt ∈_ _WE ∪_ _PV ∪_ _CSP ∪_ _HP_ ;


  - Formula S4-1k is the annual investment cost of reinforced trunk line, where _p_ _[lc]_ _g,sub_ [represents]


the capacity of trunk line connecting substation _sub_ to the nearest load center. The capacity


constraint can be found in formula S4-19, _sub ∈_ _SUB_ _g_ _[lc]_ [;]


  - Formula S4-1l is the annual investment and maintenance cost of DAC, _dac ∈_ _DAC_ ;


  - Formula S4-1m and S4-1n is the yearly cost of capturing, transporting and injecting CO 2


from grid _g_ to carbon sequestration site _c_ ;


  - Formula S4-1o is the annual cost when applying other technologies in specific scenarios,


such as demand response, etc.


**S4.3** **Constraints**


**S4.3.1** **Wind and solar PV power output**


Wind and solar PV power generation at each timestep _t_ depends on the installation capacity and


the capacity factor at _t_ . The installation capacity of wind and solar PV is constrained by the


installation capacity potential and installed capacity before the optimization year. When CISPO


optimizes the power system in 2060, the installed capacity before 2060 (2050 in this study)


considering retirement is the lower installation bound. For 2030, CISPO takes the installed


71


S4 FORMULA OF THE POWER-SYSTEM OPTIMIZATION MODEL


capacity by the end of 2022 as the lower bound and assumes these installations will be replaced


if retired. See Section S2.1 for the installation capacity potential and hourly capacity factor, and


Section S3.3.1 for the installed capacity determination for each site by the end of 2022. The


constraints are:


_p_ ~~_g_~~ _,z,pt_ _≤_ _p_ _g,z,pt_ _≤_ ~~_p_~~ _g,z,pt_ _,_ _∀g, pt ∈_ _WE ∪_ _PV, z ∈_ _Z_ _g,pt_ _,_ (S4-2)

_I_ _g,pt,t_ _≤_ � _cf_ _g,z,pt,t_ _× p_ _g,z,pt_ _,_ _∀g, pt ∈_ _WE ∪_ _PV, t,_ (S4-3)

_z∈Z_ _g,pt_


where _p_ ~~_g_~~ _,z,pt_ denotes the installed capacity before optimization year at site _z_ ; ~~_p_~~ _g,z,pt_ denotes


the installation capacity potential of renewable energy _pt_ ; _I_ _g,pt,t_ is the integrated capacity from


renewable energy source _gt_ (i.e. wind or solar PV) to grid _g_ at timestep _t_ .


**S4.3.2** **Concentrating solar power output**


Concentrating solar power (CSP) in CISPO is modeled considering the solar heat collection


module, the energy storage module, and the power generation module. Similar to wind and


solar PV, CISPO optimizes CSP’s capacity expansion and power output at the cell level. The


charging/discharging route and energy stored in the CSP energy storage module are optimized


at the grid level. CISPO constraints the installation and operation of CSP as follows:


_p_ _g,z,pt_ _≤_ _p_ _g,z,pt_ _≤_ ~~_p_~~ _g,z,pt_ _,_ _∀g, pt ∈_ _CSP_ _, z ∈_ _Z_ _g,pt_ _,_ (S4-4)

( _sto_ _[e]_ _g,csp,t_ _[−]_ _[sto]_ _[e]_ _g,csp,t−_ 1 [) +] _[ I]_ _[g,pt,t]_ _[≤]_ � _cf_ _g,z,pt,t_ _× p_ _g,z,pt_ _,_ _∀g, pt ∈_ _CSP_ _, t,_ (S4-5)

_z∈Z_ _g,pt_


_sto_ _[e]_ _g,csp,t_ 0 [=] _[ sto]_ _[e]_ _g,csp,t_ _T_ _[,]_ _∀g,_ (S4-6)


_sto_ _[e]_ _g,csp,t_ _[≤]_ _[sto]_ _[dur]_ _csp_ _[×]_ � _p_ _g,z,pt_ _,_ _∀g, t,_ (S4-7)

_z∈Z_ _g,pt_


where _p_ _g,z,pt_ is the installation capacity of CSP in grid _g_ at site _z_ ; _cf_ _g,z,pt,t_ is the capacity factor


of CSP in grid at site _z_ at timestep _t_, _cf_ _g,z,pt,t_ _× p_ _g,z,pt_ is the maximum electric power generation

from CSP with the Rankine cycle considered in resource assessment [[25]] ; _sto_ _[dur]_ _csp_ [is the duration]

of electric storage, _sto_ _[dur]_ _csp_ _[×]_ [ �] _z∈Z_ _g,pt_ _[p]_ _[g,z,pt]_ [ determines the maximum energy storage capacity for]


CSP in grid _g_ ; formula S4-5 and S4-6 constraint the hourly charge, discharge route and power


output of CSP, _t_ 0 and _t_ _T_ are the beginning and end of the optimization period of each year.


**S4.3.3** **Hydropower output**


**Run-of-River hydropower**


The maximum power output of run-of-river hydropower at each timestep _t_ is less than or equal


to the installation capacity multiplied by the capacity factor (determined by the river flow, as


discussed in Section S2.2). The installation capacity of run-of-river hydro at site _z_ is constrained


72


S4 FORMULA OF THE POWER-SYSTEM OPTIMIZATION MODEL


to be within the bounds of the existing installed capacity and the assessed maximum installation


capacity potential. The constraints are as follows:


_p_ ~~_g_~~ _,z,ror_ _≤_ _p_ _g,z,ror_ _≤_ ~~_p_~~ _g,z,ror_ _,_ _∀g, z ∈_ _Z_ _g,ror_ _,_ (S4-8)

_I_ _g,ror,t_ _≤_ � _cf_ _g,z,ror,t_ _× p_ _g,z,ror_ _,_ _∀g, t._ (S4-9)

_z∈Z_ _g,ror_


**Reservoir hydropower**


The installation capacity of reservoir hydropower at site _z_ should be larger than or equal to the


existing installed capacity and less than or equal to the installation capacity potential, as follows:


_p_ ~~_g_~~ _,z,resvor_ _≤_ _p_ _g,z,resvor_ _≤_ ~~_p_~~ _g,z,resvor_ _,_ _∀g, z ∈_ _Z_ _g,resvor_ _,_ (S4-10)


where _p_ ~~_g_~~ _,z,resvor_ is the installed capacity before optimization year; ~~_p_~~ _g,z,resvor_ is the installation

capacity potential estimated in the hydropower resource assessment; _p_ ~~_g_~~ _,z,resvor_ = _p_ _g,z,resvor_ =

~~_p_~~ _g,z,resvor_ for those installed reservoir hydropower by 2022 because their capacity are fixed.


Reservoir hydropower differs from run-of-river hydropower in that it is equipped with a reser

voir, allowing for water storage. For a specific reservoir hydropower site, CISPO requires the


reservoir capacity to remain consistent at the beginning and end of the optimization period. At


each timestep _t_, the reservoir level must be larger than or equal to the minimum safe level and


less than or equal to the maximum designed level. The constraints are as follows:


_v_ _g,z,resvor,t_ 0 = _v_ _g,z,resvor,t_ _T_ _,_ _∀g, z, t,_ (S4-11)


_V_ ~~_g_~~ _,z,resvor_ _≤_ _v_ _g,z,resvor,t_ _≤_ _V_ _g,z,resvor_ _,_ _∀g, z, t,_ (S4-12)


where _v_ _g,z,resvor,t_ is the reservoir level of hydropower _z_ at timestep _t_, and _t_ 0 and _t_ _T_ are the


beginning and end of the optimization period, respectively; _V_ ~~_g_~~ _,z,resvor_ and _V_ _g,z,resvor_ are minimum


safe and maximum designed reservoir level.


The maximum power output for reservoir hydropower at each timestep _t_ is subject to two


constraints: 1) less than the installation capacity, and 2) the water consumption for power gener

ation must be less than or equal to the usable water volume (the gap between reservoir capacity


at timestep _t −_ 1 and the minimum safe capacity plus natural infow), according to:


_I_ _g,z,resvor,t_ = _q_ _g,z,resvor,t_ _[gen]_ _[×][ H]_ _[g,z]_ _[ ×][ η]_ _[resvor]_ _[ ×][ g]_ _[e]_ _[ ×][ ρ]_ _[h]_ _[,]_ _∀g, z, t,_ (S4-13)


_I_ _g,z,resvor,t_ _≤_ _p_ _g,z,resvor_ _,_ _∀g, z, t,_ (S4-14)


( _q_ _g,z,resvor,t_ _[gen]_ [+] _[ q]_ _g,z,resvor,t_ _[spill]_ [)] _[ ×]_ [ ∆] _[t]_ _[ ≤]_ _[q]_ _g,z,resvor,t_ _[in]_ _[×]_ [ ∆] _[t]_ [+] _[ v]_ _[g,z,resvor,t][−]_ [1] _[−]_ _[V]_ ~~_g_~~ _,z,resvor_ _[,]_ _∀g, z, t,_ (S4-15)


where _I_ _g,z,resvor,t_ is the generation from reservoir hydropower _z_ in grid _g_ at timestep _t_ ; _q_ _g,z,resvor,t_ _[gen]_

is the generation flow, _q_ _g,z,resvor,t_ _[spill]_ [is spillage flow,] _[ H]_ _[g,z]_ [ is the water head of reservoir hydropower,]


73


S4 FORMULA OF THE POWER-SYSTEM OPTIMIZATION MODEL


_η_ _resvor_ is the generating efficiency of reservoir hydropower (0.85 in this study), _g_ _e_ is the acceler

ation of earth gravity, _ρ_ _h_ is the density of water; ∆ _t_ is time length of timestep, 3600 seconds. In


power grid _g_, the integrated capacity from reservoir hydropower is the sum of each site:


_I_ _g,resvor,t_ = � _I_ _g,z,resvor,t_ _,_ _∀g, t._ (S4-16)

_z∈Z_ _g,resvor_


The reservoir capacity at each timestep _t_ is from water stored at the previous timestep _t −_ 1


plus net-in water volume at timestep _t_, which is the volume of inflow water minus used water (for


generation and spillage), which is:


_v_ _g,z,resvor,t_ = _v_ _g,z,resvor,t−_ 1 + ( _q_ _g,z,resvor,t_ _[in]_ _[−]_ _[q]_ _g,z,resvor,t_ _[gen]_ _[−]_ _[q]_ _g,z,resvor,t_ _[spill]_ [)] _[ ×]_ [ ∆] _[t]_ _[,]_ _∀g, z, t._ (S4-17)


**S4.3.4** **Intra-grid transmission**


In the CISPO model, integration of wind, solar PV, CSP, and hydropower into the power grid


is represented through a two-stage approach: 1) spur lines connecting generation sites _z_ to sub

stations _sub_, and 2) trunk line reinforcements from substations _sub_ to load centers _lc_ . See the


match procedure of sites, substations, and load centers in Section S7.9, and the configuration of


lines in Section S7.8. Spur line capacities must meet or exceed the maximum potential wind and


solar PV power output to preclude congestion, constrained as:


_p_ _[sub]_ _g,z,pt_ _[≥]_ _[p]_ _[g,z,pt]_ _[×]_ [ max] _t_ [[] _[cf]_ _[g,z,pt,t]_ []] _[,]_ _∀g, pt ∈_ _WE ∪_ _PV, z ∈_ _Z_ _g,pt_ _,_ (S4-18)


where _p_ _[sub]_ _g,z,pt_ [is the spur line capacity connecting the renewable energy] _[ pt]_ [ at site] _[ z]_ [ to the cor-]

responding substation _sub_ ; max _t_ [[] _[cf]_ _[g,z,pt,t]_ [] is the historical maximum capacity factor (1980–2019)]


of the renewable energy _pt_ at site _z_, which determines the minimum capacity of the spur line


together with the decision variable _p_ _g,z,pt_ . For CSP and hydropower, the spur line capacity is


larger than or equal to the installation capacity for their energy storage module, reservoir storage,


or designed flow requirement [[54]] .


The temporal complementarities between wind and solar PV generation suggest that sim

ply summing their historical peak capacities overestimates the required capacity for trunk line


reinforcement after integrating wind and solar PV generation at the same substation. This is


because the intermittent nature of wind and solar resources can lead to non-coincident peaks,


where the maximum output from the two generation sources may not occur simultaneously, i.e.,


max
_t_ [[] _[cf]_ _[we]_ [(] _[t]_ [) +] _[ cf]_ _[pv]_ [(] _[t]_ [)]] _[ ≤]_ [max] _t_ [[] _[cf]_ _[we]_ [(] _[t]_ [)] + max] _t_ [[] _[cf]_ _[pv]_ [(] _[t]_ [)]. To address this, CISPO first aggregates]


the potential capacities of all wind and solar PV connected to each substation, effectively cre

ating a virtual renewable generation source, which enables calculating an equivalent historical


peak capacity. Subsequently, the ratio between planned and historical peak capacities is used to


74


S4 FORMULA OF THE POWER-SYSTEM OPTIMIZATION MODEL


determine the minimum trunk line capacity needed, according to:



�



_cf_ _g,z,pt,t_ _×_ ~~_p_~~ _g,z,pt_
_z_

~~�~~ ~~�~~ ~~_p_~~





_,_ _∀g, sub ∈_ _SUB_ _g_ _[lc]_ _[,][ pt][ ∈]_ _[WE][ ∪]_ _[PV][,]_




�
_p_ _[lc]_ _g,sub_ _[≥]_ [(] �

_pt_



�

_z∈Z_ _[sub]_
_g,pt_



_pt_



_z_



_p_ _g,z,pt_ ) _×_ max
_t_









�

_pt_



~~�~~



~~_p_~~ _g,z,pt_



(S4-19)


where, � _p_ _[lc]_ _g,sub_ [is an intermediate variable representing the trunk reinforcement capacity connecting]


wind and solar PV from the substation _sub_ to the load center _lc_ . The right-hand side is the


combined formula of the planning capacity ratio, the installation capacity potential, and the



historical maximum equivalent capacity factor (1980–2019). [�]

_pt_



� _p_ _g,z,pt_ is equivalent to:

_z∈Z_ _[sub]_
_g,pt_



_p_ _g,z,pt_
_z∈Z_ _[sub]_
_g,pt_



�



~~_p_~~ _g,z,pt_ _._ (S4-20)



�


_z_



�

_pt_



�

_z∈Z_ _[sub]_
_g,pt_



�

_pt_



_p_ _g,z,pt_ =



~~�~~

_pt_



~~�~~


_z_



_×_
�
~~_p_~~ _g,z,pt_



_pt_



Substations also need to transfer power from connected CSP and hydropower to load centers,


therefore the total reinforcement capacity for a substation is:



_p_ _[lc]_ _g,sub_ _[≥]_ _[p]_ [�] _[lc]_ _g,sub_ [+] �

_pt_



�

_z∈Z_ _[sub]_
_g,pt_



_p_ _g,z,pt_ _,_ _∀g, sub, pt ∈_ _HP_ _, CSP_ _,_ (S4-21)



where the trunk line reinforcement for hydropower and CSP is larger than or equal to the instal

lation capacity similar to the spur line capacity requirement.


**S4.3.5** **Thermal and nuclear power**


Unit commitment algorithms are often used for thermal and nuclear power dispatch, employing


mixed-integer optimization to determine the commitment status of individual generating units.


However, unit commitment is a non-deterministic polynomial (NP)-hard problem, limiting the


solvable scale. In long-term power system planning studies encompassing larger scales, the com

putational burden of unit commitment renders solution times unacceptably long. Studies have


shown that relaxing the integer unit status variables to continuous values dramatically improves


solve times with negligible loss of accuracy ( _<_ 1%) compared to strict integer unit commitment [[81]] .


CISPO adopts this relaxed formulation, referred to as Relaxed Unit Commitment (RUC), enabling


tractable inclusion of detailed operational constraints while well-approximating rigorous unit-level


optimization.


In CISPO, coal, natural gas, and biomass power, including units equipped with carbon capture


and storage (CCS) technology and combined heat and power (CHP), are collectively modeled as


thermal power. Within each grid, generating units of the same thermal technology are grouped


together, with the model distinguishing: coal, coal CCS, coal CHP, coal CHP CCS, gas, gas CCS,


gas CHP, gas CHP CCS, biomass, biomass CCS, and nuclear. Detailed information on existing


75


S4 FORMULA OF THE POWER-SYSTEM OPTIMIZATION MODEL


installed capacity and maximum installation capacity potential (for biomass and nuclear power)


in each grid is provided in Section S3.5.


For each power group, the RUC represents unit status over time using a typical per-unit


capacity and three continuous decision variables: the number of online units, startup units, and


shutdown units in each timestep, as shown in Fig. S45.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-82-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-82-2.png)


Figure S45: Commitment status at each timestep. The number of online units at timestep _t_
equals the pre-timestep ( _t −_ 1) online units plus start-up and minus shut-down units in timestep

_t_


**Operate constraints**


For each timestep _t_, the online units of each power group are determined by the online unit at


timestep _t_ _−_ 1 plus start-up and minus shut-down unit at timestep _t_, and the unit number of each


state can’t exceed the total units, according to :


0 _≤_ _u_ _[on]_ _g,pt,t_ _[, u]_ _[su]_ _g,pt,t_ _[, u]_ _[sd]_ _g,pt,t_ _[≤]_ _[u]_ _[tot]_ _g,pt_ _[,]_ _∀g, pt ∈_ _TP ∪_ _NP_ _, t,_ (S4-22)


_u_ _[on]_ _g,pt,t_ [=] _[ u]_ _[on]_ _g,pt,t−_ 1 [+] _[ u]_ _[su]_ _g,pt,t_ _[−]_ _[u]_ _[sd]_ _g,pt,t_ _[,]_ _∀g, pt ∈_ _TP ∪_ _NP_ _, t,_ (S4-23)


where _u_ _[on]_ _g,pt,t_ _[, u]_ _[su]_ _g,pt,t_ _[, u]_ _[sd]_ _g,pt,t_ [represent the online unit, start-up unit and shut-down unit of power] _[ pt]_


in grid _g_ at timestep _t_ ; and _u_ _[tot]_ _g,pt_ [is a decision variable which reflects the optimized installation]


unit of power _pt_ in grid _g_ .


Thermal generating units require a minimum offline time after shutdown before being available


to start up again. Similarly, newly started-up units must remain online for a minimum up-time


before shutdown is permissible. These constraints are formulated as:



_u_ _[on]_ _g,pt,t_ _[≤]_ _[u]_ _[tot]_ _g,pt_ _[−]_ _[u]_ _[su]_ _g,pt,t_ +1 _[−]_



_t_
� _u_ _[sd]_ _g,pt,k_ _[,]_ _∀g, pt ∈_ _TP ∪_ _NP_ _, t,_ (S4-24)

_k_ =max( _t−τ_ _pt_ _[up]_ [+2] _[,]_ [1)]



_u_ _[on]_ _g,pt,t_ _[≥]_ _[u]_ _[sd]_ _g,pt,t_ +1 [+]



_t_
� _u_ _[su]_ _g,pt,k_ _[,]_ _∀g, pt ∈_ _TP ∪_ _NP_ _, t,_ (S4-25)

_k_ =max( _t−τ_ _pt_ _[dn]_ [+2] _[,]_ [1)]



where _τ_ _pt_ _[up]_ [and] _[ τ]_ _[ dn]_ _pt_ [are the required minimum online/offline time length after start up/shut down,]



_t_
respectively; �



_t_ _t_
� _u_ _[sd]_ _g,pt,k_ [and] �

_k_ =max( _t−τ_ _pt_ _[up]_ [+2] _[,]_ [1)] _k_ =max( _t−τ_



� _u_ _[su]_ _g,pt,k_ [indicate the shut down units and]

_k_ =max( _t−τ_ _pt_ _[dn]_ [+2] _[,]_ [1)]



76


S4 FORMULA OF THE POWER-SYSTEM OPTIMIZATION MODEL


start up units in timestep period [ _t −_ _τ_ _pt_ _[dn/up]_ + 2 _, t_ ], and the summation item is zero when _t_ = 0


which represents the specific requirement at the beginning of the optimization.


Functionally, online thermal units can directly serve load demand and provide upward spinning


reserve. Thermal units have minimum stable generation levels required for normal operation.


Additionally, combined heat and power (CHP) units must reserve some capacity for heat supply,


imposing maximum output constraints [[133]] . These are formulated as:


_φ_ _pt_ _× u_ _[on]_ _g,pt,t_ _[≤]_ _[u]_ _[load]_ _g,pt,t_ _[≤]_ ~~_[φ]_~~ _pt_ _[×][ u]_ _[on]_ _g,pt,t_ _[,]_ _∀g, pt ∈_ _TP ∪_ _NP_ _, t,_ (S4-26)


where ~~_φ_~~ _pt_ and _φ_ _pt_ are the maximum and minimum output rate for online unit, see Table Sx for


detailed values.


The operating characteristics of thermal and nuclear power impose ramp rate limits on the


amount of load changes that can be accommodated over time. CISPO represents two types of


ramping constraints:


_u_ _[load]_ _g,pt,t_ _[−]_ _[u]_ _[load]_ _g,pt,t−_ 1 _[≤]_ _[δ]_ _pt_ _[up]_ _[×]_ [ (] _[u]_ _[on]_ _g,pt,t_ _[−]_ _[u]_ _[su]_ _g,pt,t_ _[−]_ _[u]_ _[sd]_ _g,pt,t_ +1 [) +] _[φ]_ _pt_ _[×]_ [ (] _[u]_ _g,pt,t_ _[su]_ _[−]_ _[u]_ _[sd]_ _g,pt,t_ [)] _[,]_ _∀g, pt ∈_ _TP ∪_ _NP_ _, t ≥_ _1_ _,_

(S4-27)

_u_ _[load]_ _g,pt,t−_ 1 _[−]_ _[u]_ _[load]_ _g,pt,t_ _[≤]_ _[δ]_ _pt_ _[dn]_ _[×]_ [ (] _[u]_ _[on]_ _g,pt,t_ _[−]_ _[u]_ _[su]_ _g,pt,t_ _[−]_ _[u]_ _[su]_ _g,pt,t−_ 1 [)] _[ −]_ _[φ]_ _pt_ _[×]_ [ (] _[u]_ _g,pt,t_ _[su]_ _[−]_ _[u]_ _[sd]_ _g,pt,t_ [)] _[,]_ _∀g, pt ∈_ _TP ∪_ _NP_ _, t ≥_ _1_ _,_

(S4-28)


where _δ_ _pt_ _[up]_ [and] _[ δ]_ _pt_ _[dn]_ [are the maximum ramp-up and ramp-down rates of power] _[ pt]_ [, respectively.]


These constraints enforce limits on the change in power generation between time steps based on


the inherent ramp rate capabilities of each thermal technology. This represents the operational


flexibility of thermal fleets to accommodate load variations. With ramping constraints introduced,


thermal and nuclear power output in each timestep must also satisfy an additional constraint [[6]] :


_u_ _[load]_ _g,pt,t_ _[≤]_ ~~_[φ]_~~ _pt_ _[×]_ [ (] _[u]_ _[on]_ _g,pt,t_ _[−]_ _[u]_ _[su]_ _g,pt,t_ _[−]_ _[u]_ _[sd]_ _g,pt,t_ +1 [) +] _[φ]_ _pt_ _[×]_ [ (] _[u]_ _g,pt,t_ _[su]_ [+] _[ u]_ _[sd]_ _g,pt,t_ +1 [)] _[,]_ _∀g, pt ∈_ _TP ∪_ _NP_ _, t._

(S4-29)


Additionally, ramping incurs incremental costs that must be accounted for. CISPO introduces


two non-negative continuous variables ( _ramp_ _[up]_ _g,pt,t_ _[∈R]_ [+] [,] _[ ramp]_ _[dn]_ _g,pt,t_ _[∈R]_ [+] [) along with constraints]


to represent ramping quantities in each timestep _t_ :


_ramp_ _[up]_ _g,pt,t_ _[≥]_ [(] _[u]_ _[load]_ _g,pt,t_ _[−]_ _[u]_ _[load]_ _g,pt,t−_ 1 [)] _[ ×][ ϱ]_ _[pt]_ _[,]_ _∀g, pt ∈_ _TP ∪_ _NP_ _, t,_ (S4-30)


_ramp_ _[dn]_ _g,pt,t_ _[≥]_ [(] _[u]_ _[load]_ _g,pt,t−_ 1 _[−]_ _[u]_ _[load]_ _g,pt,t_ [)] _[ ×][ ϱ]_ _[pt]_ _[,]_ _∀g, pt ∈_ _TP ∪_ _NP_ _, t._ (S4-31)


These variables measure the magnitude of ramp-up and ramp-down between sequential timesteps.


These measurements are then used within the objective function.


In provinces deployed combined heat and power units, these units are required to supply heat


therefore all the units are required to keep online during the winter season. In CISPO, coal-fired


77


S4 FORMULA OF THE POWER-SYSTEM OPTIMIZATION MODEL


and gas-fired CHP power are considered, see Section S3.5 for detailed installation information


and technical-economic parameters. The expressions are as follows:


_u_ _[on]_ _g,pt,t_ [=] _[ u]_ _[tot]_ _g,pt_ _[,]_ _∀g, pt ∈_ _CCHP ∪_ _GCHP_ _, t ∈_ _T_ _w_ _,_ (S4-32)


where _T_ _w_ represents the timestep during the winter heating period, which includes January to


March-mid, and November-mid to December of each year.


Biomass energy is a zero-emissions power generation technology employed in power systems


where fuel costs exceed those of coal or natural gas. Given the annual energy generation of 182


TWh in 2022 from the installed capacity of approximately 41 GW, we have established a lower


bound for the number of online units for both biomass power and biomass carbon capture and


storage (BECCS) power, based on the following:


_u_ _[on]_ _g,pt,t_ _[≥]_ _[σ]_ _pt_ _[on]_ _[×][ u]_ _[tot]_ _g,pt_ _[,][ ∀][g, pt][ ∈]_ _[BP]_ _[,][ t][,]_ (S4-33)


where _σ_ _pt_ _[on]_ [is the online unit requirement factor (0.5 in this study).]


**Capacity Constraints**


The available biomass fuel supply constrains the deployable capacity for biomass and BECCS


power. In the CISPO model, the biomass feedstock is limited to agricultural and forestry residues


and dedicated energy crops grown on abandoned land. In Section S2.3, we introduced the annual


available biomass fuel resource amount (in GJ/yr) for each province. Based on assumptions of


a 0.35 thermal efficiency [[59]] for biomass power generation units and 6,132 equivalent hours of


annual average power generation, we calculate the maximum installable biomass capacity for


each province and apply this as a constraint:


� _u_ _[tot]_ _g,pt_ _[≤]_ ~~_[u]_~~ ~~_[t]_~~ _g,bio_ _[ot]_ _[,]_ _∀g,_ (S4-34)

_pt∈BP_


where the set _BP_ includes both biomass and BECCS power. Besides the installable capacity


constraints, the yearly fuel consumed by biomass and BECCS power can not exceed the available


fuel supply in each province:


~~_c_~~ _al_

� ( _f_ _pt_ _[load]_ _× u_ _[load]_ _g,pt,t_ [+] _[ f]_ _pt_ _[on]_ _[×][ u]_ _[on]_ _g,pt,t_ [)] _[ ×][ ϱ]_ _[pt]_ _[×]_ [ ∆] _[t]_ _[≤]_ _[therm]_ _g,bio_ _[,]_ _∀g,_ (S4-35)

_t∈T,pt∈BP_


where, ∆ _t_ is the time length for each timestep, which is 1 hour; _f_ _pt_ _[load]_ is the fuel consumption by


_cal_
load unit; _f_ _pt_ _[on]_ [is the fuel consumption required to keep the units online;] _[ therm]_ _g,bio_ [is the annual]


available biomass fuel (GJ/yr) in grid _g_ .


Regarding nuclear power, the optimized installation capacity should be larger than or equal


to the existing installed capacity. The upper bound for the installation capacity on each grid is


78


S4 FORMULA OF THE POWER-SYSTEM OPTIMIZATION MODEL


set using the projections outlined in [[5]] . In this study, we assume the coastal planning plus under

construction nuclear units as the potential installation capacity, which totals approximately 230


GW including the existing installed capacity by 2022. Detailed information can be found in


Section S3.5, and the constraints are as follows:


_u_ _[tot]_ ~~_g_~~ _,pt_ _[≤]_ _[u]_ _[tot]_ _g,pt_ _[≤]_ ~~_[u]_~~ ~~_[t]_~~ _g,pt_ _[ot]_ _[,]_ _∀g, pt ∈_ _NP_ _._ (S4-36)


Thermal power generation such as coal power, natural gas power, and biomass power can be


retrofitted with carbon capture, and storage (CCS) devices to reduce or achieve negative carbon


emissions. For units that have already been equipped with CCS before the optimization year


(e.g., 2050), they will continue to keep the CCS equipment. Therefore, additional constraints are


designed to allow the model to endogenously determine how many non-CCS units in each grid


can be retrofitted to CCS-equipped units. Here, we group thermal power generation technologies


by whether they are equipped with CCS, i.e. coal and coal CCS as one group, coal CHP and coal


CHP CCS as one group, etc. For each group, we require the total optimized installation capacity


to be larger than or equal to the existing installed capacity before the planning year, and the


CCS-equipped capacity to be larger than or equal to the installed capacity. That is:



_u_ _[tot]_ _g,pt_ _[≥]_ _[u]_ _[tot]_ ~~_g_~~ _,pt_ _[,]_ _∀g, pt ∈_ _CCS_ _,_ (S4-37)

� _u_ _[tot]_ _g,pt_ _[≥]_ � _u_ _[tot]_ ~~_g_~~ _,pt_ _[,]_ _∀g, Gen ∈_ _CCS_ _pair_ _,_ (S4-38)



� _u_ _[tot]_ _g,pt_ _[≥]_ �

_pt∈Gen_ _pt∈_



� _u_ _[tot]_ ~~_g_~~ _,pt_ _[,]_ _∀g, Gen ∈_ _CCS_ _pair_ _,_ (S4-38)

_pt∈Gen_



where the capacity without CCS can be less than the existing installed capacity, but within


constraint S4-38, the reduced capacity will be shifted to the capacity with CCS equipment. For


CHP units, CISPO assumes the total installed capacity remains unchanged, but retrofitting with


CCS is still possible. That is, constraint S4-38 becomes:



� _u_ _[tot]_ _g,pt_ [=] �

_pt∈Gen_ _pt∈_



�



� _u_ _[tot]_ ~~_g_~~ _,pt_ _[,]_ _∀g, Gen ∈_ _CHP_ _._ (S4-39)

_pt∈Gen_



**S4.3.6** **Energy storage**


In large-scale power system planning models, energy storage systems are typically represented at


the grid level (province in CISPO), with optimal capacity expansion decisions and hourly charg

ing/discharging scheduling [[6,8,10,15,131,132]] . Generally, representative technologies are selected to


characterize each storage type in the model, for example, lithium-ion battery and pumped hy

dropower storage for electrochemical and mechanical storage, respectively. Different storage tech

nologies are distinguished through key techno-economic parameters including charge/discharge


efficiency, energy-to-power ratio (duration), self-discharge rate, and investment costs. See Section


S3.6 for detailed parameters used in this study.


79


S4 FORMULA OF THE POWER-SYSTEM OPTIMIZATION MODEL


**Capacity constraints**


Similar to formula S4-2, the installed power capacity of storage can be constrained to lie within


the lower and upper limits:


_p_ ~~_g_~~ _,st_ _≤_ _p_ _g,st_ _≤_ ~~_p_~~ _g,st_ _,_ _∀g, st,_ (S4-40)


where _p_ _g,st_ is the decision variable representing the optimized installation capacity of storage _st_


in grid _g_ ; _p_ ~~_g_~~ _,st_ is the installed capacity before each optimization year, and ~~_p_~~ _g,st_ represents the


maximum storage installation potential in grid _g_ . The upper deployment constraints are only


used for PHS in each grid, see Section S3.6 for the detailed installation potential.


**Operational constraints**


CISPO represents energy storage operation at an hourly resolution using decision variables in

cluding charging/discharging capacity from/to the grid, and upward/downward spinning reserve


capacity provided during the charging/discharging process. For each energy storage technology,


the charging capacity plus downward spinning reserve capacity from charging at each timestep


should be less than the installed capacity, and the energy (post-loss) charged into the storage


system can’t exceed the remaining energy capacity. The upward spinning reserve capacity from


charging is constrained to be less than the charge capacity at each timestep _t_ . These constraints


are as follows:


_sto_ _[char]_ _g,st,t_ [+] _[ sr]_ _g,st,t_ _[−][,char]_ _≤_ _p_ _g,st_ _,_ _∀g, st, t,_ (S4-41)

( _sto_ _[char]_ _g,st,t_ [+] _[ sr]_ _g,st,t_ _[−][,char]_ [)] _[ ×]_ [ ∆] _[t]_ _[ ×][ η]_ _st_ _[char]_ _≤_ _p_ _g,st_ _× sto_ _[dur]_ _st_ _−_ _sto_ _[e]_ _g,st,t−_ 1 _[,]_ _∀g, st, t,_ (S4-42)

_sr_ _g,st,t_ [+] _[,char]_ _≤_ _sto_ _[char]_ _g,st,t_ _[,]_ _∀g, st, t,_ (S4-43)


where _sto_ _[char]_ _g,st,t_ [is the charge capacity of storage] _[ st]_ [ from grid] _[ g]_ [ at timestep] _[ t]_ [; ∆] _[t]_ [is the length of]

each timestep, which is 1 hour, and together with the charging efficiency _η_ _st_ _[char]_ of storage _st_, the


item _sto_ _[char]_ _g,st,t_ _[×]_ [∆] _[t]_ _[×]_ _[η]_ _st_ _[char]_ represents the post-loss energy charged into storage; _sto_ _[dur]_ _st_ indicates the

energy-to-power ratio (duration) of storage _st_, and thereby _p_ _g,st_ _× sto_ _[dur]_ _st_ is the maximum energy


that can be stored; _sto_ _[e]_ _g,st,t−_ 1 [refers to the energy stored in storage] _[ st]_ [ at the end of timestep] _[ t][ −]_ [1]

in grid _g_ ; _sr_ _g,st,t_ [+] _[,char]_ and _sr_ _g,st,t_ _[−][,char]_ are the upward and downward spinning reserve capacity can be


provided from the charging process of storage _st_ at timestep _t_, respectively.


For each timestep _t_ and storage technology _st_, the sum of discharging and upward spinning


reserve capacity from discharging can not exceed the installation power capacity or state of


charge of storage _st_, considering the discharge efficiency. Provided downward spinning reserve


80


S4 FORMULA OF THE POWER-SYSTEM OPTIMIZATION MODEL


while discharging is bounded by the discharging amount.


_sto_ _[dis]_ _g,st,t_ [+] _[ sr]_ _g,st,t_ [+] _[,dis]_ _[≤]_ _[η]_ _st_ _[dis]_ _[×][ p]_ _[g,st]_ _[,]_ _∀g, st, t,_ (S4-44)

( _sto_ _[dis]_ _g,st,t_ [+] _[ sr]_ _g,st,t_ [+] _[,dis]_ [)] _[ ×]_ [ ∆] _[t]_ _[ ≤]_ _[η]_ _st_ _[dis]_ _[×][ sto]_ _[e]_ _g,st,t−_ 1 _[,]_ _∀g, st, t,_ (S4-45)

_sr_ _g,st,t_ _[−][,dis]_ _[≤]_ _[sto]_ _[dis]_ _g,st,t_ _[,]_ _∀g, st, t,_ (S4-46)


where _sto_ _[dis]_ _g,st,t_ [is the discharge capacity of storage] _[ st]_ [ to grid] _[ g]_ [ at timestep] _[ t]_ [;] _[ η]_ _st_ _[dis]_ [is the discharge]

efficiency of storage _st_ ; and _sr_ _g,st,t_ [+] _[,dis]_ [and] _[ sr]_ _g,st,t_ _[−][,dis]_ [are the upward and downward spinning reserve]


capacity while discharging at timestep _t_ .


Finally, the total upward spinning reserve capacity provided by charging/discharging of stor

age _st_ in grid _g_ is also constrained by the installation capacity with discharge efficiency:


_sr_ _g,st,t_ [+] _[,char]_ + _sr_ _g,st,t_ [+] _[,dis]_ _[≤]_ _[η]_ _st_ _[dis]_ _[×][ p]_ _[g,st]_ _[,]_ _∀g, st, t._ (S4-47)


**Energy constraints**


For each timestep _t_, the energy (in GWh) stored is bounded by the energy capacity, which is


calculated by multiplying installed capacity (in GW) and duration (in hours). Additionally,


CISPO requires that the energy stored in each type of storage system remains consistent at the


beginning and end of the optimization period. The constraints are as follows:


_sto_ _[e]_ _g,st,t_ _[≤]_ _[p]_ _[g,st]_ _[×][ sto]_ _[dur]_ _st_ _[,]_ _∀g, st, t,_ (S4-48)


_sto_ _[e]_ _g,st,t_ 0 [=] _[ sto]_ _[e]_ _g,st,t_ _T_ _[,]_ _∀g, st,_ (S4-49)


where _sto_ _[e]_ _g,st,t_ 0 [and] _[ sto]_ _[e]_ _g,st,t_ _T_ [are the energy stored in storage] _[ st]_ [ at the very beginning and end of]


the optimization period in this study, respectively.


For each storage technology _st_, the energy stored at timestep _t_ is from the energy stored


in previous timestep _t −_ 1 and post-loss energy charged in, and additionally minus net energy


discharged, constrained as follows:


_sto_ _[e]_ _g,st,t_ [= (1] _[ −]_ _[ζ]_ _st_ _[self]_ ) _× sto_ _[e]_ _g,st,t−_ 1 [+] _[ η]_ _st_ _[char]_ _× sto_ _[char]_ _g,st,t_ _[×]_ [ ∆] _[t]_ _[−]_ _[sto]_ _[dis]_ _g,st,t_ _[×]_ [ ∆] _[t]_ _[/η]_ _st_ _[dis]_ _[,]_ _∀g, st, t,_ (S4-50)


where _ζ_ _st_ _[self]_ is the hourly self-discharge rate (%/h) of storage _st_, which can be derived from daily

self-discharge rate by _ζ_ _st_ _[self]_ = 1 _−_ 24 _[√]_ _ζ_ ~~_′_~~ .


**S4.3.7** **Inter-grid transmission**


The CISPO model applies the pipeline (or transportation) model to simulate inter-grid trans

mission flow, avoiding the introduction of binary variables. This approach has been validated in


reference [[125]] and is commonly used in mid-long term power planning models [[6,8,10,15,131,132]] . A


pre-optimized procedure considering distance and unit cost is employed to determine whether AC


81


S4 FORMULA OF THE POWER-SYSTEM OPTIMIZATION MODEL


or DC and which voltage level should be chosen for each line. Refer to Section S7.8 for detailed


information.


The optimized installation capacity of each inter-grid transmission line should be larger than


the installed capacity before the optimization year, constraints are as follows:


_p_ ~~_l_~~ _AC_ _∀l_ _g,g_ _[AC]_ _[′]_ _[,]_ (S4-51)
_g,g_ _[′]_ _[ ≤]_ _[p]_ _[l]_ _g,g_ _[AC][′]_ _[,]_

_p_ ~~_l_~~ _DC_ _∀l_ _g,g_ _[DC]_ _[′]_ _[,]_ (S4-52)
_g,g_ _[′]_ _[ ≤]_ _[p]_ _[l]_ _g,g_ _[DC][′]_ _[,]_


where _p_ _l_ _AC_
_g,g_ _[′]_ [ and] _[ p]_ _[l]_ _g,g_ _[DC][′]_ [ are the optimized capacity of AC and DC transmission line within the]

model; _p_ ~~_l_~~ _AC_ _[p]_ ~~_[l]_~~ _[DC]_
_g,g_ _[′]_ [ and] _g,g_ _[′]_ [ are the installed capacity before the optimization year.]

The power capacity transmitted along with line from _g_ to _g_ _[′]_ and the reverse direction at


each timestep _t_ in AC line should be less than or equal to the optimized installation capacity,


constrained as:


_−→_
_f_ _l_ _AC_ _∀l_ _g,g_ _[AC]_ _[′]_ _[, t,]_ (S4-53)
_g,g_ _[′]_ _[,t]_ _[ ≤]_ _[p]_ _[l]_ _g,g_ _[AC][′]_ _[,]_
_←−_
_f_ _l_ _AC_ _∀l_ _g,g_ _[AC]_ _[′]_ _[, t,]_ (S4-54)
_g,g_ _[′]_ _[,t]_ _[ ≤]_ _[p]_ _[l]_ _g,g_ _[AC][′]_ _[,]_
_−→_
_f_ _l_ _DC_ _∀l_ _g,g_ _[DC]_ _[′]_ _[, t.]_ (S4-55)
_g,g_ _[′]_ _[,t]_ _[ ≤]_ _[p]_ _[l]_ _g,g_ _[DC][′]_ _[,]_


For each timestep _t_, the bidirectional power transmitted through a line is not allowed [[15]] . DC


line meets this constraint as it is modeled with a fixed transmission direction [[6,9]] . For the AC

line, in modeling practice, if no additional constraints are imposed, _[−→]_ _f_ _l_ _AC_
_g,g_ _[′]_ _[,t]_ _[ >]_ [ 0 and] _[ ←−][f]_ _[ l]_ _g,g_ _[AC][′]_ _[,t]_ _[ >]_ [ 0]

can be true at the same timestep. To avoid this, a slight variable cost (0.001 yuan/kWh) can be


added to the objective function, and an additional constraint can be introduced [[15]] :


_−→_
_f_ _l_ _AC_ _∀l_ _g,g_ _[AC]_ _[′]_ _[, t.]_ (S4-56)
_g,g_ _[′]_ _[,t]_ [ +] _[ ←−][f]_ _[ l]_ _g,g_ _[AC][′]_ _[,t]_ _[ ≤]_ _[p]_ _[l]_ _g,g_ _[AC][′]_ _[,]_


**S4.3.8** **Power demand balance**


The power demand of each grid _g_ in each timestep _t_ must be strictly satisfied. In CISPO, there are


three parts of the power supply side, including power integrated from the local power generator,


power discharged from the energy storage system, and power transmitted in through inter-grid


transmission line. And the sum of these three parts should be equal to the power demand for


each timestep, constrained as:



_I_ _[local]_ +
_g,t_ �



_sto_ _[dis]_ _g,st,t_ [+] �
_st_ _[′]_



_η_ _l_ _AC_ �
_g,g_ _[′]_ _[ × ←−][f]_ _[ l]_ _g,g_ _[AC][′]_ _[,t]_ [ +]
_g_ _[′]_ _g_ _[′]_



_η_ _l_ _gDC_ _[′]_ _,g_ _[× −→][f]_ _[ l]_ _g_ _[DC][′]_ _,g_ _[,t]_ [ =] _[ dem]_ _[g,t]_ [ +] �
_g_ _[′]_ _dac_



_ele_ _g,dac_ _,_ _∀g, t,_

_dac_



(S4-57)


where _I_ _g,t_ _[local]_ is a intermediate variable representing the power integrated from local generators

in grid _g_ ; [�] _sto_ _[dis]_ _g,st,t_ [is total post-loss power discharged from energy storage system in grid] _[ g]_ [;]

_st_


82


S4 FORMULA OF THE POWER-SYSTEM OPTIMIZATION MODEL



�

_g,g_ _[′]_ _[ × ←−][f]_ _[ l]_ _g,g_ _[AC][′]_ _[,t]_ [ +][ �]
_g_ _[′]_ _[ η]_ _[l]_ _[AC]_ _g_ _[′]_



�



_g_ _[′]_ _,g_ _[× −→][f]_ _[ l]_ _g_ _[DC][′]_ _,g_ _[,t]_ [ indicates the total post-loss power transmitted from other]
_g_ _[′]_ _[ η]_ _[l]_ _[DC]_



grid _g_ _[′]_, and the transmission efficiency _η_ _l_ _g,g′_ is related to transmission distance (km) and power loss

rate (%/km) by _η_ _l_ _g,g′_ = (1 _−_ _ξ_ _l_ ) _[d]_ _[l]_ ; _dem_ _g,t_ is the electricity demand in grid _g_ at each timestep _t_, see


Section S3.1 for the hourly demand data collection and processing; and _ele_ _g,dac_ is a intermediate


variable indicating the electricity demand by DAC, see equation S4-75.


The power capacity integrated from wind, solar PV, hydropower, CSP, thermal, and nuclear


sources into grid g in CISPO can be used in three distinct ways: meeting local power demand,


charging energy storage systems, and transmitting to other grid _g_ _[′]_, as follows:



� (1 _−_ _ξ_ _pt_ ) _× u_ _[load]_ _g,pt,t_ _[×][ ϱ]_ _[pt]_ _[,]_ _∀g, t,_

_pt∈TP_ _∪NP_



� _I_ _g,pt,t_ + �

_pt∈WE_ _∪PV_ _pt∈TP_



_HP_ _∪CSP_
�



_sto_ _[char]_ _g,st,t_ [+] �
_st_ _[′]_



_−→_
_f_ _l_ _AC_ _∪DC_ _,t_ [=]
_g,g_ _[′]_



_I_ _[local]_ +
_g,t_ �



_g_ _[′]_



(S4-58)




[�] _sto_ _[char]_ _g,st,t_ [represents the aggregated power charged into the energy storage system;][ �]

_st_ _[′]_



where [�]



_−→_
_f_ _l_ _AC_ _∪DC_ _,t_
_g,g_ _[′]_
_g_ _[′]_



denotes the cumulative power exported to other grids; _ξ_ _pt_ is power loss (%) for thermal power


equipped with CCS technology (equals to 0 if not equipped).


**S4.3.9** **Reserve requirement**


Reserve requirements in the CISPO model include spinning reserve, and power reserve (also called


marginal reserve in some models [[132]] ). The spinning reserve considered in CISPO contains upward


and downward operating reserves during each timestep to meet the forecast error or unexpected


contingency or power output uncertainty from VRE. The spinning reserve demand in grid _g_ is a


fraction of power demand and integrated power capacity from VRE. The spinning reserve capacity


is from thermal and nuclear power, curtailed VRE and hydropower, and energy storage systems,


as constrained in the corresponding formula. The upward spinning reserve capacity requirement


constraint is as follows:



_sr_ _g,pt,t_ [+] [+] �
_pt_ _st_



� _I_ _g,pt,t_ _,_ _∀g, t,_ (S4-59)

_pt∈WE_ _∪PV_



�



_sr_ _g,st,t_ [+] _[≥]_ _[ρ]_ _sr_ [+] _[×][ dem]_ _[g,t]_ [+] _[ ρ]_ [+] _vre_ _[×]_ �
_st_ _∈WE_



_sr_ _g,pt,t_ [+] _[≤]_ [(1] _[ −]_ _[ξ]_ _[pt]_ [)] _[ ×]_ ~~[(]~~ ~~_[φ]_~~ _pt_ _[×][ u]_ _[on]_ _g,pt,t_ _[−]_ _[u]_ _[load]_ _g,pt,t_ [)] _[ ×][ ϱ]_ _[pt]_ _[,]_ _∀pt ∈_ _TP ∪_ _NP_ (S4-60)


_sr_ _g,pt,t_ [+] _[≤]_ � _cf_ _g,z,pt,t_ _× p_ _g,z,pt_ _−_ _I_ _g,pt,t_ _,_ _∀pt ∈_ _WE ∪_ _PV,_ (S4-61)

_z∈Z_ _g,pt_

_sr_ _g,pt,t_ [+] _[≤]_ � _cf_ _g,z,pt,t_ _× p_ _g,z,pt_ _−_ _I_ _g,pt,t_ _−_ ( _sto_ _[e]_ _g,csp,t_ _[−]_ _[sto]_ _[e]_ _g,csp,t−_ 1 [)] _[,]_ _∀pt ∈_ _CSP_ _,_ (S4-62)

_z∈Z_ _g,pt_

_sr_ _g,ror,t_ [+] _[≤]_ � _cf_ _g,z,or,t_ _× p_ _g,z,resvor_ _−_ _I_ _g,resvor,t_ _,_ _∀g, t,_ (S4-63)

_z∈Z_ _g,ror_

_sr_ _g,resvor,t_ [+] _[≤]_ � _p_ _g,z,resvor_ _−_ _I_ _g,z,resvor,t_ _,_ _∀g, t,_ (S4-64)

_z∈Z_ _g,resvor_

_sr_ _g,st,t_ [+] [=] _[ sr]_ _g,st,t_ [+] _[,char]_ + _sr_ _g,st,t_ [+] _[,dis]_ _[,]_ _∀st ∈_ _ST_ _, t,_ (S4-65)


83


S4 FORMULA OF THE POWER-SYSTEM OPTIMIZATION MODEL


where _ρ_ [+] _sr_ [and] _[ ρ]_ [+] _vre_ [denote the upward spinning reserve requirement rates caused by the forecast]


error and power output uncertainty of VRE, which is 5% in this study.


Downward spinning reserve capacity provided from committed thermal and nuclear power,


hydropower, and energy storage systems, according to:



�



� _sr_ _g,pt,t_ _[−]_ [+] �

_pt_ _st_



_sr_ _g,st,t_ _[−]_ _[≥]_ _[ρ]_ _sr_ _[−]_ _[×][ dem]_ _[g,t]_ [+] _[ ρ]_ _[−]_ _vre_ _[×]_ �
_st_ _∈WE_



� _I_ _g,pt,t_ _,_ _∀g, t,_ (S4-66)

_pt∈WE_ _∪PV_



_sr_ _g,pt,t_ _[−]_ _[≤]_ [(1] _[ −]_ _[ξ]_ _[pt]_ [)] _[ ×]_ [ (] _[u]_ _[load]_ _g,pt,t_ _[−]_ _[φ]_ _pt_ _[×][ u]_ _g,pt,t_ _[on]_ [)] _[ ×][ ϱ]_ _[pt]_ _[,]_ _∀pt ∈_ _TP ∪_ _NP_ _,_ (S4-67)


_sr_ _g,pt,t_ _[−]_ _[≤]_ _[I]_ _[g,pt,t]_ _[,]_ _∀pt ∈_ _HP_ _,_ (S4-68)

_sr_ _g,st,t_ _[−]_ [=] _[ sr]_ _g,st,t_ _[−][,char]_ + _sr_ _g,st,t_ _[−][,dis]_ _[,]_ _∀st ∈_ _ST_ _, t,_ (S4-69)


where _ρ_ _[−]_ _sr_ [and] _[ ρ]_ _[−]_ _vre_ [are the downward spinning reserve requirement rates (5% in this study).]


If needed, power reserve requires the installed capacity of generators and storage to be larger


than the peak demand within a year in each grid _g_ to guarantee system reliability. This constraint


is formulated as:



� _λ_ _pt_ _× p_ _g,pt_ +

_pt∈TP_ _∪NP_



_CSP_ _∪HP_
�

_pt∈WE_ _∪PV_



� _λ_ _pt_ _× p_ _g,z,pt_ + �

_z∈Z_ _st∈_



�



� _λ_ _st_ _× p_ _g,st_ _≥_ (1 + _ρ_ _cap_ ) _× dem_ _[peak]_ _g_ _,_ _∀g,_

_st∈ST_



(S4-70)


where _λ_ _pt_ and _λ_ _st_ are capacity credits for power generator _pt_ and storage _st_, _ρ_ _cap_ is power capacity

requirement for peak demand (set as 5% in this study), and _dem_ _[peak]_ _g_ is the peak demand of grid


_g_ within the planning year, which is the maximum demand during this year.


**S4.3.10** **Inertia requirement**


Maintaining the stable operation of the power system also necessitates fulfilling minimum in

ertia requirements. That is, the aggregate inertia provided by thermal power, nuclear power,


hydropower, and energy storage systems must exceed the minimum inertia demand. The inertia


demand is a specific percentage of the power demand, where this percentage is determined based


on statistical analyses of the current power system operation [[9]] . Because of the growing integra

tion of renewable energy sources into the power system, future inertia levels may not fully meet


the requirements. Consequently, the CISPO model introduces a factor to reflect the tolerance of


system inertia drops. This constraint is expressed as:



� _ι_ _pt_ _× u_ _[on]_ _g,pt,t_ [+]

_pt∈TP_ _∪NP_



_CSP_
�

_pt∈HP_



�



_ι_ _pt_ _× p_ _g,z,pt_ + �

_z_ _st_



_ι_ _st_ _× p_ _g,st_ _≥_ _ι_ _tol_ _× ι_ 0 _× dem_ _g,t_ _,∀g, t._ (S4-71)

_st_



where _ι_ 0 is the current inertia level (3.5 in this study) and _ι_ _tol_ _∈_ [0 _,_ 1] is the inertia tolerance factor,


which determines the minimum inertia requirement together with power demand; _ι_ _pt_ and _ι_ _st_ are


inertia constant of power _pt_ and storage _st_, see Section S3.4–S3.6 for detailed values. Thermal


and nuclear power can only provide inertia from the online capacity. The inertia provided by


84


S4 FORMULA OF THE POWER-SYSTEM OPTIMIZATION MODEL


CSP, hydropower and energy storage is the product of inertia constant and installed capacity for


their fast grid-connection ability.


**S4.3.11** **Carbon emissions limitations**


To align with decarbonization mandates, CISPO optimizes the power system’s operation within


an upper limit on annual carbon emissions, as stipulated by exogenous factors. Among thermal


power generators, coal-fired and gas-fired power plants are the primary contributors to carbon


emissions. In contrast, biomass power generation is regarded as a zero-emission technology and


can even achieve negative emissions if equipped with carbon capture and storage (CCS) systems.


The constraint is as follows: [2]



�

_g∈G_



� (1 _−_ _η_ _pt_ _[ccs]_ [)] _[ ×][ ef]_ _[pt]_ _[×]_ �

_pt∈TP_ _t∈T_



�



� _u_ _[load]_ _g,pt,t_ _[×][ ϱ]_ _[pt]_ _[×]_ [ ∆] _[t]_ _[−]_ �

_t∈T_ _∈G_



_g∈G_



� _η_ _dac_ _× m_ _g,dac_ _≤_ _E,_ (S4-72)

_dac∈DAC_



where _η_ _pt_ _[ccs]_ _∈_ [0 _,_ 1] is the carbon capture rate of thermal power when equipped with CCS, and


_η_ _pt_ _[ccs]_ [= 0 if CCS is not installed;] _[ ef]_ _[pt]_ [is carbon emissions factor of power] _[ pt]_ [, MtCO] [2] [/GWh;] _[ E]_ [ is]


the upper limit of annual carbon emissions, which is an exogenous parameter.


**S4.3.12** **Direct air capture**


For each direct air capture technology, the installed capacity in the planning year needs to be


larger than the previous year, and the carbon captured in a year should be less than the installed


capacity:


_p_ ~~_g_~~ _,dac_ _≤_ _p_ _g,dac_ _,_ _∀g, dac,_ (S4-73)


_m_ _g,dac_ _≤_ _p_ _g,dac_ _,_ _∀g, dac._ (S4-74)


Direct air capture consumes electricity and heat energy when capturing CO 2 from the air. In


the CISPO, we assume the heat energy is from heat pumps that transform electricity to heat.


Therefore, the electricity used by each DAC technology can be expressed as:



_× m_ _g,dac_ _,_ _∀g,_ (S4-75)
�



_ele_ _g,dac_ = [1]

_|T_ _|_



_e_ _dac_ + _[h]_ _[dac]_
� _cop_



where _e_ _dac_ (GWh/MtCO 2 ) and _h_ _dac_ (GWh/MtCO 2 ) are electricity and heat consumed by _dac_


for unit carbon captured, respectively, and _cop_ is the coefficient of performance (COP) for heat


pump, which is a ratio between the rate at which the heat pump transfers thermal energy, and the


amount of electrical power required to do the pumping. As _m_ _g,dac_ is the total carbon captured


during the modeling period (typically one year), we assume the carbon is equally captured hour


by hour. Therefore, the hourly electricity demand for DAC is calculated by multiplying a factor


1
_|T_ _|_ [, where] _[ |][T]_ _[|]_ [ is the period length (hours). See Section S3.9 for detailed parameters of DAC used]


in this model.


2 This constraint is not modeled when in the specific scenario, e.g., without emissions limitation scenario.


85


S5 MODEL IMPLEMENTATION AND DYNAMIC OPTIMIZATION


**S4.3.13** **Carbon source-sink match**


Carbon dioxide emitted from power plant units can be captured and transported for storage in


carbon sequestration sites. Onshore deep saline aquifers are considered potential carbon storage


locations in CISPO. Assessments in Section S2.4 show the overall carbon sequestration potential


is around 120 Gt. For each storage site, the annual injection rate is calculated based on a full


lifetime of 65 years. The shortest geographical distance from grid center points to storage sites


approximates the carbon transport distance. In the CISPO model, each grid can transport CO 2


to all potential storage sites. The constraints are expressed as:


� _m_ _g,c_ _≤C_ _c_ _,_ _∀c,_ (S4-76)


_g_



� _u_ _[load]_ _g,pt,t_ _[×][ ϱ]_ _[pt]_ _[,]_ _∀g,_ (S4-77)


_t∈T_



�



_m_ _g,c_ _≥_ �

_c_ _dac_



� _m_ _g,dac_ + �

_dac_ _∈_



� _η_ _pt_ _[ccs]_ _[×][ ef]_ _[pt]_ _[×]_ [ ∆] _[t]_ _[×]_ �

_pt∈TP_ _t∈T_



where _m_ _g,c_ is the annual carbon transported from grid _g_ to carbon sequestration site _c_ ; _C_ _c_ is the


annual inject rate (MtCO 2 /yr) at carbon sequestration site _c_, formula S4-76 constrains the total


carbon stored in site _c_ can’t exceed the injection rate; and formula S4-77 requires that the carbon


captured in grid _g_ should be all stored.

## **S5 Model implementation and dynamic optimization**


The CISPO model’s implementation leverages the Gurobi optimizer, a robust optimization tool,


in conjunction with Python’s programming capabilities. This model simultaneously optimizes


the capacity expansion of both generation and transmission infrastructures, while also account

ing for the hourly operation spanning the entirety of the nationwide power system’s 8,760 hours.


Furthermore, the spatial optimization resolution is granular, with onshore wind, offshore wind,


utility-scale solar photovoltaic (PV), distributed solar PV, and concentrating solar power being


optimized at each 0.25° _×_ 0.25° grid cell (approximately 60,000 decision points in total), while


hydropower optimization occurs at the dam site level. Consequently, the model’s scale reaches


approximately 5 _×_ 10 [7] constraints and 4 _×_ 10 [8] non-zero elements after the pre-solve procedure exe

cuted by Gurobi for each optimization year (e.g., 2030). This enormous scale optimization model


is meticulously designed to optimize the dynamic advancement of sustainable power systems in


China. Underpinning this model’s scale is the heightened generation uncertainty faced by high


renewable energy penetration power systems, stemming from the temporal and spatial hetero

geneity inherent in the generation nature of renewable energy sources.


The dynamic optimization process toward 2060 is facilitated by the exchange of installed


and retired capacity of generation and transmission infrastructure across planning years. For


instance, the existing installed capacity, coupled with the optimized capacity at each grid cell


86


S6 SCENARIO DESIGN


for onshore wind in 2030, serves as an input for the existing installed capacity in the subsequent


year (2040). Given the long-term planning horizon, the retired capacity is subtracted from the


existing installation upon reaching its assumed lifetime. As an illustration, all the capacity of


onshore wind optimized in 2030 will be retired in 2060 due to its 25-year lifetime assumption.


Based on the model’s assumptions, the existing installed capacity of variable renewable energy


sources (replaced if retired), combined heat and power plants, and hydropower installations are


operational until 2060.

## **S6 Scenario design**


In this study, we design nine parallel optimization pathways toward 2060 with a 10-year dynamic


step, according to the annual emissions limitations, investment cost and electricity demand pro

jections, NET availability, and land availability for VRE. The definitions of scenarios are as


follows:


  - Annual emissions targets:


**–** _−_
“Base ( 550Mt)”: The base scenario in this study, with the target annual emissions


in 2060 is about _−_ 550 Mt/yr;


**–**
“NegEmis200Mt”: A looser annual emissions limitations than the base scenario, with


net-zero emissions in 2050, and _−_ 200 MtCO 2 /yr negative emissions in 2060;


**–**
“NegEmis700Mt”: A stricter annual emissions limitations than the base scenario, with


about _−_ 400 Mt/yr in 2050 and _−_ 700 MtCO 2 /yr in 2060;


**–**
“NoEmisCap”: No annual emissions limitation in this scenario, designed as a control


scenario for comparison.


  - Investment cost projection:


**–**
“Capex1.25X”: The investment costs of technologies with higher uncertainty in indus

try advancement are 1.25 times that of the base scenario, technologies including wind


(onshore + offshore), solar PV (utility-scale + distributed), concentrating solar power,


pumped hydro storage, battery storage, and biomass energy with carbon capture and


sequestration.


  - Electricity demand projection:


**–**
“Demand1.25X”: The annual electricity demand in China is from 15.6 PWh/yr in


2030 increasing to 2060 (20.0 PWh/yr) linearly, 1.25 times that of the base scenario.


  - Negative emissions technology availability:


87


S6 SCENARIO DESIGN


**–**
“LimitedBECCS”: Annual bio-fuel potential can be used in China’s power system is


one quarter of the base scenario, totaling about 7.45 EJ/yr.


**–**
“WithoutBECCS”: Biomass energy with carbon capture and sequestration is not an


option to be deployed, therefore, NET in this scenario is only direct air capture with


carbon sequestration.


  - Land availability for variable renewable energy:


**–**
“VRELandOpen”: The installation capacity potential used as upper deployment bound


for wind (onshore + offshore) and solar PV (utility-scale + distributed) is from the


“Open” scenario in energy resource assessment, see Section S2.1.6.


**–**
“VRELandConservative”: The installation capacity potential used as upper deploy

ment bound for wind (onshore + offshore) and solar PV (utility-scale + distributed)


is from the “Conservative” scenario in energy resource assessment, see Section S2.1.6.


The annual emissions limitations are derived from an economy-wide carbon neutrality study


that reflects key economic trade-offs in mitigation options of the electricity sector and non

electricity sectors [[67]] . We show the annual emissions limitation of these parallel pathways in


Figure S46.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-94-0.png)


Figure S46: Annual emissions limitations in each year toward 2060. The “NoEmisCap” scenario
is not modeled with an annual emissions limitation.


In addition to the scenarios on China’s transition pathway, we also explore the impacts of


spatiotemporal resolution on simulation results in PSEMs when the power system integrates


high-penetration renewable energies, e.g., in 2060. For spatial optimization resolution of wind


and solar PV, we combine grid cells into several resource clusters for each VRE type according


to their hourly CF profiles and distances to load centers using the k-means algorithm in the


“VRECluster” scenario, see Section S3.3.3 for details.


88


S7 SUPPLEMENTARY RESULTS


The temporal coverage of scenarios based on representative hours ranges from 288 hours (12


days) to 4,320 hours (180 days). To evaluate the impact of representative hour selection on ca

pacity expansion in PSEMs, we define the minimum time window as a “period”. A “period”


represents a continuous time interval spanning tens to hundreds of hours. For instance, a “Day


period” comprises 24 hours, a “Week period” includes 168 hours, and a “Month period” en

compasses 720 hours. Four distinct “period”s are considered in the temporal coverage scenarios:


“Day”, “Week”, “TwoWeeks”, and “Month”. Notably, capacity expansion outcomes may vary


depending on the seasonal coverage of representative hours, even when the total duration re

mains constant (e.g., 4,320 hours). To address this, we design scenarios using “period” steps with


identical total durations. In this study, we examine 22 temporal coverage scenarios, including


“12Days”, “24Days”, “4Weeks”, “12Weeks”, “4TwoWeeks”, “4Months”, and “6Months”. We


show the temporal coverage of these scenarios in Figure S47.



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-95-4.png)





Figure S47: Temporal coverage in different representative hour selections.

## **S7 Supplementary results**


In this section, we present more supplementary results from the CISPO model. System cost


of electricity (excluding distribution and administration costs) across scenarios are shown in


Section S7.1. National capacity mix and generation mix across scenarios are shown in Section


S7.2 and S7.3. Optimized carbon emissions and source-sink matching are shown in Section S7.4.


Optimized capacity and/or cell-level distribution results of wind and solar power under the base


and “VRECluster” are shown in Section S7.5. The optimized capacity of hydropower at the dam

site level from 2030 to 2060 in the base scenario is shown in Section S7.6. Charging/discharging


routes of energy storage from 2030 to 2060 at the national level are shown in Section S7.7,


representing the aggregated values across all provincial power grids. Results of inter-regional and


intra-regional transmission are shown in Section S7.8 and S7.9. Distribution of hourly marginal


cost of generation from 2030 to 2060 across scenarios are shown in Section S7.10. The hourly


demand load dispatch profile over 8,760 hours in the base scenario from 2030 to 2060 is shown in


89


S7 SUPPLEMENTARY RESULTS


Section S7.11, which is aggregated by values of provincial grids.


**S7.1** **System cost of electricity**





![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-96-4.png)

_−_
(a) Base ( 550Mt)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-96-10.png)


(c) NegEmis700Mt

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-96-16.png)


(e) LimitedBECCS

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-96-22.png)


(g) VRELandOpen

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-96-28.png)


(i) Capex1.25X



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-96-5.png)

(b) NegEmis200Mt

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-96-11.png)


(d) NoEmisCap

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-96-17.png)


(f) WithoutBECCS

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-96-23.png)


(h) VRELandConservative

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-96-29.png)


(j) Demand1.25X











Figure S48: System cost of electricity (SCOE, yuan/kWh) from 2030 to 2060 across scenarios,
excluding distribution and administration costs.


90


S7 SUPPLEMENTARY RESULTS


**S7.2** **National capacity mix to 2060**























![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-97-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-97-5.png)

_−_
(a) Base ( 550Mt)



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-97-1.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-97-6.png)

(b) NegEmis200Mt



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-97-2.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-97-7.png)

(c) NegEmis700Mt



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-97-3.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-97-8.png)

(d) NoEmisCap





![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-97-4.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-97-9.png)

(e) LimitedBECCS





















![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-97-10.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-97-15.png)

(f) WithoutBECCS



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-97-11.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-97-16.png)

(g) VRELandOpen



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-97-12.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-97-17.png)

(h) VRELandConservative





![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-97-13.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-97-18.png)

(i) Capex1.25X





![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-97-14.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-97-19.png)

(j) Demand1.25X



Figure S49: Nationwide optimized capacity mix from 2030 to 2060 across scenarios.


91


S7 SUPPLEMENTARY RESULTS


**S7.3** **National generation mix to 2060**



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-98-1.png)



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-98-0.png)



_−_
(a) Base ( 550Mt)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-98-2.png)


(c) NegEmis700Mt

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-98-4.png)


(e) LimitedBECCS

|15000 (TWh/yr)<br>10000<br>Generation<br>5000<br>0<br>2022 2030 2040 2050 2060<br>Year|Col2|Col3|
|---|---|---|
|2022<br>2030<br>2040<br>2050<br>2060<br>Year<br>0<br>5000<br>10000<br>15000<br>Generation (TWh/yr)|||



(g) VRELandOpen

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-98-6.png)


(i) Capex1.25X



(b) NegEmis200Mt

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-98-3.png)


(d) NoEmisCap

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-98-5.png)


(f) WithoutBECCS

|15000 (TWh/yr)<br>10000<br>Generation<br>5000<br>0<br>2022 2030 2040 2050 2060<br>Year|Col2|Col3|
|---|---|---|
|2022<br>2030<br>2040<br>2050<br>2060<br>Year<br>0<br>5000<br>10000<br>15000<br>Generation (TWh/yr)|||



(h) VRELandConservative

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-98-7.png)


(j) Demand1.25X







Figure S50: Nationwide optimized generation mix from 2030 to 2060 across scenarios.


92


S7 SUPPLEMENTARY RESULTS


**S7.4** **Carbon emissions and source-sink matching**

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-99-0.png)


Figure S51: Annual net carbon emissions (MtCO 2 /yr) in each provincial grid from 2030 to 2060
in the base scenario.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-99-1.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-99-2.png)


(a) 2050 (b) 2060


Figure S52: Annual carbon (MtCO 2 /yr) captured and injected in each province and CO 2 sequestration site, and transportation volume of each capture-sequestration link from 2050 to 2060 in
the “NegEmis200Mt” scenario.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-99-3.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-99-4.png)


(a) 2050 (b) 2060


Figure S53: Annual carbon (MtCO 2 /yr) captured and injected in each province and CO 2 sequestration site, and transportation volume of each capture-sequestration link from 2050 to 2060 in
the “NegEmis700Mt” scenario.


93


S7 SUPPLEMENTARY RESULTS

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-100-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-100-1.png)


(a) 2050 (b) 2060


Figure S54: Annual carbon (MtCO 2 /yr) captured and injected in each province and CO 2 sequestration site, and transportation volume of each capture-sequestration link from 2050 to 2060 in
the “LimitedBECCS” scenario.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-100-2.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-100-3.png)


(a) 2050 (b) 2060


Figure S55: Annual carbon (MtCO 2 /yr) captured and injected in each province and CO 2 sequestration site, and transportation volume of each capture-sequestration link from 2050 to 2060 in
the “WithoutBECCS” scenario.


94


S7 SUPPLEMENTARY RESULTS

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-101-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-101-1.png)


(a) 2050 (b) 2060


Figure S56: Annual carbon (MtCO 2 /yr) captured and injected in each province and CO 2 sequestration site, and transportation volume of each capture-sequestration link from 2050 to 2060 in
the “VRELandOpen” scenario.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-101-2.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-101-3.png)


(a) 2050 (b) 2060


Figure S57: Annual carbon (MtCO 2 /yr) captured and injected in each province and CO 2 sequestration site, and transportation volume of each capture-sequestration link from 2050 to 2060 in
the “VRELandConservative” scenario.


95


S7 SUPPLEMENTARY RESULTS

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-102-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-102-1.png)


(a) 2050 (b) 2060


Figure S58: Annual carbon (MtCO 2 /yr) captured and injected in each province and CO 2 sequestration site, and transportation volume of each capture-sequestration link from 2050 to 2060 in
the “Capex1.25X” scenario.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-102-2.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-102-3.png)


(a) 2050 (b) 2060


Figure S59: Annual carbon (MtCO 2 /yr) captured and injected in each province and CO 2 sequestration site, and transportation volume of each capture-sequestration link from 2050 to 2060 in
the “Demand1.25X” scenario.


96


S7 SUPPLEMENTARY RESULTS


**S7.5** **Optimized capacity of wind and solar power**



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-103-1.png)









![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-103-3.png)



Figure S60: National capacity (GW) of wind (onshore + offshore) and solar PV (utility-scale +
distributed) across scenarios from 2030 to 2060.


97


S7 SUPPLEMENTARY RESULTS

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-104-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-104-1.png)


(a) 2030 (b) 2040

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-104-2.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-104-3.png)


(c) 2050 (d) 2060


Figure S61: Cell-level capacity expansion of wind (onshore and offshore) in the base scenario
from 2030 to 2060.


98


S7 SUPPLEMENTARY RESULTS

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-105-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-105-1.png)


(a) 2030 (b) 2040

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-105-2.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-105-3.png)


(c) 2050 (d) 2060


Figure S62: Cell-level capacity expansion of utility-scale solar PV in the base scenario from 2030

to 2060.


99


S7 SUPPLEMENTARY RESULTS

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-106-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-106-1.png)


(a) 2030 (b) 2040

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-106-2.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-106-3.png)


(c) 2050 (d) 2060


Figure S63: Cell-level capacity expansion of distributed solar PV in the base scenario from 2030

to 2060.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-106-4.png)


Figure S64: Provincial installations (GW) of concentrating solar power in 2060 in the base scenario.


100


S7 SUPPLEMENTARY RESULTS

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-107-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-107-1.png)


(a) Wind (b) Solar PV


Figure S65: Installed capacity (GW) of wind and solar PV in the “VRECluster” scenario.


**S7.6** **Optimized capacity of hydropower**

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-107-2.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-107-3.png)


(a) 2030 (b) 2040

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-107-4.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-107-5.png)


(c) 2050 (d) 2060


Figure S66: Capacity expansion of hydropower in the base scenario from 2030 to 2060.


101


S7 SUPPLEMENTARY RESULTS


**S7.7** **Charging and discharging route of energy storage**





Figure S67: Energy stored (GWh) at the national level from 2030 to 2060 in the base scenario.


102


S7 SUPPLEMENTARY RESULTS


**S7.8** **Inter-regional transmission**

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-109-0.png)


Figure S68: Net energy flow (TWh/yr) alongside inter-regional transmission line from 2030 to
2060 in the base scenario.


103


S7 SUPPLEMENTARY RESULTS


**S7.9** **Intra-regional transmission**

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-110-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-110-1.png)


(a) 2030 (b) 2040

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-110-2.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-110-3.png)


(c) 2050 (d) 2060


Figure S69: Capacity (GW) of trunk lines connecting substations to load centers for VRE integration in the base scenario from 2030 to 2060.











|1.0|Col2|Col3|Col4|Col5|Col6|Col7|Col8|Col9|Col10|Col11|Col12|Col13|Col14|Col15|Col16|Col17|Col18|Col19|Col20|Col21|Col22|Col23|Col24|Col25|Col26|Col27|Col28|
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
|10<br>1<br>10<br>0<br>10<br>1<br>10<br>2<br>10<br>3<br>Distance (km)<br>0.0<br>0.2<br>0.4<br>0.6<br>0.8<br><br>Proportion (0~1)<br>2030<br>2040<br>2050<br>2060|||||||||||||||||||||||||||2030<br>2040<br>2050<br>2060|
|10<br>1<br>10<br>0<br>10<br>1<br>10<br>2<br>10<br>3<br>Distance (km)<br>0.0<br>0.2<br>0.4<br>0.6<br>0.8<br><br>Proportion (0~1)<br>2030<br>2040<br>2050<br>2060||||||||||||||||||||||||||||
|10<br>1<br>10<br>0<br>10<br>1<br>10<br>2<br>10<br>3<br>Distance (km)<br>0.0<br>0.2<br>0.4<br>0.6<br>0.8<br><br>Proportion (0~1)<br>2030<br>2040<br>2050<br>2060||||||||||||||||||||||||||||


Figure S70: Cumulative distribution of total distance (km) of spur and trunk lines that connect
solar cells (weighted by planned capacity) to their corresponding load center (distributed solar is
modeled with zero connection distance) from 2030 to 2060 in the base scenario.


104


S7 SUPPLEMENTARY RESULTS


**S7.10** **Distribution of hourly marginal cost of generation**

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-111-0.png)


Figure S71: Density distribution of hourly marginal cost (yuan/kWh) of generation from 2030 to
2060 in the base scenario.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-111-1.png)


Figure S72: Density distribution of hourly marginal cost (yuan/kWh) of generation from 2030 to
2060 in the “NegEmis200Mt” scenario.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-111-2.png)


Figure S73: Density distribution of hourly marginal cost (yuan/kWh) of generation from 2030 to
2060 in the “NegEmis700Mt” scenario.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-111-3.png)


Figure S74: Density distribution of hourly marginal cost (yuan/kWh) of generation from 2030 to
2060 in the “NoEmisCap” scenario.


105


S7 SUPPLEMENTARY RESULTS

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-112-0.png)


Figure S75: Density distribution of hourly marginal cost (yuan/kWh) of generation from 2030 to
2060 in the “LimitedBECCS” scenario.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-112-1.png)


Figure S76: Density distribution of hourly marginal cost (yuan/kWh) of generation from 2030 to
2060 in the “WithoutBECCS” scenario.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-112-2.png)


Figure S77: Density distribution of hourly marginal cost (yuan/kWh) of generation from 2030 to
2060 in the “VRELandOpen” scenario.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-112-3.png)


Figure S78: Density distribution of hourly marginal cost (yuan/kWh) of generation from 2030 to
2060 in the “VRELandConservative” scenario.


106


S7 SUPPLEMENTARY RESULTS

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-113-0.png)


Figure S79: Density distribution of hourly marginal cost (yuan/kWh) of generation from 2030 to
2060 in the “Capex1.25X” scenario.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-113-1.png)


Figure S80: Distribution of hourly marginal cost (yuan/kWh) of generation from 2030 to 2060
in the “Demand1.25X” scenario.


107


S7 SUPPLEMENTARY RESULTS


**S7.11** **Hourly load dispatch**



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-114-0.png)















































































































Figure S81: Nationwide power demand and supply profile in the base scenario from 2030 to 2060.


108


REFERENCES

## **References**


[1] National Renewable Energy Laboratory. 2020 annual technology baseline: Nrel refer

ence 7mw. `[https://nrel.github.io/turbine-models/2020ATB_NREL_Reference_7MW_](https://nrel.github.io/turbine-models/2020ATB_NREL_Reference_7MW_200.html)`


`[200.html](https://nrel.github.io/turbine-models/2020ATB_NREL_Reference_7MW_200.html)`, 2020.


[2] Parthiv Kurup and Craig S Turchi. Parabolic trough collector cost update for the system


advisor model (sam). Technical report, National Renewable Energy Lab.(NREL), Golden,


CO (United States), 2015.


[3] National Development and Reform Comisions (NDRC). Typical power load curves in each


provincial power system (in Chinese). `[https://www.ndrc.gov.cn/xxgk/zcfb/tz/201912/](https://www.ndrc.gov.cn/xxgk/zcfb/tz/201912/P020191230336066090861.pdf)`


`[P020191230336066090861.pdf](https://www.ndrc.gov.cn/xxgk/zcfb/tz/201912/P020191230336066090861.pdf)`, 2019.


[4] Global Energy Monitor (GEM). Renewable Energy and Other Power, 2023. `[https://](https://globalenergymonitor.org/)`


`[globalenergymonitor.org/](https://globalenergymonitor.org/)` .


[5] Xin-Jian Xiao and Ke-Jun Jiang. China’s nuclear power under the global 1.5 c target: Pre

liminary feasibility study and prospects. _Advances in Climate Change Research_, 9(2):138–


143, 2018.


[6] Xinyu Chen, Yaxing Liu, Qin Wang, Jiajun Lv, Jinyu Wen, Xia Chen, Chongqing Kang,


Shijie Cheng, and Michael B. McElroy. Pathway toward carbon-neutral electrical systems


in China by mid-century with negative CO2 abatement costs informed by high-resolution


modeling. _Joule_, 5(10):2715–2741, 2021.


[7] National Energy Administration (NEA). Construction and operation of photovoltaic power


generation in 2022, 2023. `[http://www.nea.gov.cn/2023-02/17/c_1310698128.htm](http://www.nea.gov.cn/2023-02/17/c_1310698128.htm)` .


[8] Da Zhang, Ziheng Zhu, Shi Chen, Chongyu Zhang, Xi Lu, Xiliang Zhang, Xiaoye Zhang,


and Michael R Davidson. Spatially resolved land and grid model of carbon neutrality in


china. _Proceedings of the National Academy of Sciences_, 121(10):e2306517121, 2023.


[9] Zhenyu Zhuo, Ershun Du, Ning Zhang, Chris P Nielsen, Xi Lu, Jinyu Xiao, Jiawei Wu,


and Chongqing Kang. Cost increase in the electricity supply to achieve carbon neutrality


in china. _Nature Communications_, 13(1):1–13, 2022.


[10] Mingquan Li, Rui Shan, Ahmed Abdulla, Edgar Virguez, and Shuo Gao. The role of


dispatchability in china’s power system decarbonization. _Energy & Environmental Science_,


2024.


109


REFERENCES


[11] Gang He, Jiang Lin, Froylan Sifuentes, Xu Liu, Nikit Abhyankar, and Amol Phadke. Rapid


cost decrease of renewables and storage accelerates the decarbonization of China’s power


system. _Nature Communications_, 11(1):2486, 2020.


[12] Jing-Li Fan, Jingying Fu, Xian Zhang, Kai Li, Wenlong Zhou, Klaus Hubacek, Johannes


Urpelainen, Shuo Shen, Shiyan Chang, Siyue Guo, et al. Co-firing plants with retrofitted


carbon capture and storage for power-sector emissions mitigation. _Nature Climate Change_,


13(8):807–815, 2023.


[13] Hongyu Zhang, Da Zhang, and Xiliang Zhang. The role of output-based emission trading


system in the decarbonization of china’s power sector. _Renewable and Sustainable Energy_


_Reviews_, 173:113080, 2023.


[14] Hans Hersbach, Bill Bell, Paul Berrisford, Shoji Hirahara, Andr´as Hor´anyi, Joaqu´ın Mu˜noz

Sabater, Julien Nicolas, Carole Peubey, Raluca Radu, Dinand Schepers, Adrian Simmons,


Cornel Soci, Saleh Abdalla, Xavier Abellan, Gianpaolo Balsamo, Peter Bechtold, Gionata


Biavati, Jean Bidlot, Massimo Bonavita, Giovanna Chiara, Per Dahlgren, Dick Dee, Michail


Diamantakis, Rossana Dragani, Johannes Flemming, Richard Forbes, Manuel Fuentes, Alan


Geer, Leo Haimberger, Sean Healy, Robin J. Hogan, El´ıas H´olm, Marta Janiskov´a, Sarah


Keeley, Patrick Laloyaux, Philippe Lopez, Cristina Lupu, Gabor Radnoti, Patricia Rosnay,


Iryna Rozum, Freja Vamborg, Sebastien Villaume, and Jean-No¨el Th´epaut. The ERA5


global reanalysis. _Quarterly Journal of the Royal Meteorological Society_, 146(730):1999–


2049, 2020.


[15] Patrick R. Brown and Audun Botterud. The Value of Inter-Regional Coordination and


Transmission in Decarbonizing the US Electricity System. _Joule_, 5(1):115–134, January


2021.


[16] Oleg A. Alduchov and Robert E. Eskridge. Improved Magnus Form Approximation of


Saturation Vapor Pressure. _Journal of Applied Meteorology and Climatology_, 35(4):601–


609, April 1996.


[17] Tianguang Lu, Peter Sherman, Xinyu Chen, Shi Chen, Xi Lu, and Michael McElroy. India’s


potential for integrating solar and on- and offshore wind power into its energy system.


_Nature Communications_, 11(1):4750, 2020.


[18] Qiang Wang, Kun Luo, Renyu Yuan, Sanxia Zhang, and Jianren Fan. Wake and perfor

mance interference between adjacent wind farms: Case study of xinjiang in china by means


of mesoscale simulations. _Energy_, 166:1168–1180, 2019.


110


REFERENCES


[19] Marine Regions. The intersect of the Exclusive Economic Zones and IHO areas, 2023.


`[https://www.marineregions.org/sources.php#ihoeez](https://www.marineregions.org/sources.php#ihoeez)` .


[20] Shi Chen, Xi Lu, Yufei Miao, Yu Deng, Chris P. Nielsen, Noah Elbot, Yuanchen Wang,


Kathryn G. Logan, Michael B. McElroy, and Jiming Hao. The Potential of Photovoltaics


to Power the Belt and Road Initiative. _Joule_, 3(8):1895–1912, August 2019.


[21] Patrick R Brown and Francis M O’Sullivan. Spatial and temporal variation in the value


of solar power across united states electricity markets. _Renewable and Sustainable Energy_


_Reviews_, 121:109594, 2020.


[22] A. P. Dobos. PVWatts Version 5 Manual. Technical Report NREL/TP-6A20-62641, Na

tional Renewable Energy Lab. (NREL), Golden, CO (United States), 2014.


[23] Sonia Jerez, Isabelle Tobin, Robert Vautard, Juan Pedro Mont´avez, Jose Mar´ıa L´opez

Romero, Fran¸coise Thais, Blanka Bartok, Ole Bøssing Christensen, Augustin Colette,


Michel D´equ´e, Grigory Nikulin, Sven Kotlarski, Erik van Meijgaard, Claas Teichmann,


and Martin Wild. The impact of climate change on photovoltaic power generation in Eu

rope. _Nature Communications_, 6(1):10014, December 2015. Number: 1 Publisher: Nature


Publishing Group.


[24] Yadong Lei, Zhili Wang, Deying Wang, Xiaoye Zhang, Huizheng Che, Xu Yue, Chenguang


Tian, Junting Zhong, Lifeng Guo, Lei Li, et al. Co-benefits of carbon neutrality in enhancing


and stabilizing solar and wind energy. _Nature Climate Change_, pages 1–8, 2023.


[25] David E. H. J. Gernaat, Harmen Sytze de Boer, Vassilis Daioglou, Seleshi G. Yalew,


Christoph M¨uller, and Detlef P. van Vuuren. Climate change impacts on renewable en

ergy supply. _Nature Climate Change_, 11(2):119–125, February 2021. Number: 2 Publisher:


Nature Publishing Group.


[26] Julia A Crook, Laura A Jones, Piers M Forster, and Rolf Crook. Climate change impacts on


future photovoltaic and concentrated solar power energy output. _Energy & Environmental_


_Science_, 4(9):3101–3109, 2011.


[27] Shi Chen, Xi Lu, Yufei Miao, Yu Deng, Chris P. Nielsen, Noah Elbot, Yuanchen Wang,


Kathryn G. Logan, Michael B. McElroy, and Jiming Hao. The potential of photovoltaics


to power the belt and road initiative. _Joule_, 3:1895–1912, 8 2019.


[28] Xi Lu, Michael B McElroy, Chris P Nielsen, Xinyu Chen, and Junling Huang. Optimal inte

gration of offshore wind power for a steadier, environmentally friendlier, supply of electricity


in china. _Energy Policy_, 62:131–138, 2013.


111


REFERENCES


[29] Xinliang Xu, Jiyuan Liu, Shuwen Zhang, Rendong Li, Changzhen Yan, and Shixin Wu.


China Multi-Temporal Land Use/Cover Dataset (CNLUCC) (in Chinese). Resource and


Environmental Science Data Center, 2018. `[https://www.resdc.cn/DOI/DOI.aspx?DOIID=](https://www.resdc.cn/DOI/DOI.aspx?DOIID=54)`


`[54](https://www.resdc.cn/DOI/DOI.aspx?DOIID=54)` .


[30] State Forestry Administration (SFA) of the People’s Republic of China. Notice on issues re

lated to the use of forest land in the construction of photovoltaic power stations (in Chinese),


12 2015. `[https://www.forestry.gov.cn/main/5925/20200414/090421953922884.html](https://www.forestry.gov.cn/main/5925/20200414/090421953922884.html)` .


[31] State Forestry and Grassland Administration (SFGA) of the People’s Republic of


China. Notice on regulating the use of forest land in the construction of wind farm


projects. [EB/OL], 02 2019. `[http://www.gov.cn/zhengce/zhengceku/2019-09/30/](http://www.gov.cn/zhengce/zhengceku/2019-09/30/content_5435366.htm)`


`[content_5435366.htm](http://www.gov.cn/zhengce/zhengceku/2019-09/30/content_5435366.htm)` .


[32] OpenTopography - Copernicus GLO-30 Digital Elevation Model, 2023. `[https://portal.](https://portal.opentopography.org/raster?opentopoID=OTSDEM.032021.4326.1)`


`[opentopography.org/raster?opentopoID=OTSDEM.032021.4326.1](https://portal.opentopography.org/raster?opentopoID=OTSDEM.032021.4326.1)` .


[33] D. Yamazaki. MERIT DEM: A new high accuracy global digital elevation model, 2023.


`[http://hydro.iis.u-tokyo.ac.jp/~yamadai/MERIT_DEM/index.html](http://hydro.iis.u-tokyo.ac.jp/~yamadai/MERIT_DEM/index.html)` .


[34] General Bathymetric Chart of the Oceans (GEBCO). Gebco gridded bathymetry data,


2023. `[https://www.gebco.net/data_and_products/gridded_bathymetry_data/](https://www.gebco.net/data_and_products/gridded_bathymetry_data/)` .


[35] World Bank. Global Shipping Traffic Density, 2023. `[https://datacatalog.worldbank.](https://datacatalog.worldbank.org/search/dataset/0037580/Global%20Shipping%20Traffic%20Density?version=4)`


`[org/search/dataset/0037580/Global%20Shipping%20Traffic%20Density?version=4](https://datacatalog.worldbank.org/search/dataset/0037580/Global%20Shipping%20Traffic%20Density?version=4)` .


[36] Matthieu Le Tixerant, Damien Le Guyader, Fran¸coise Gourmelon, and Betty Queffelec.


How can automatic identification system (ais) data be used for maritime spatial planning?


_Ocean & Coastal Management_, 166:18–30, 2018.


[37] Diego Silva Herran, Hancheng Dai, Shinichiro Fujimori, and Toshihiko Masui. Global as

sessment of onshore wind power resources considering the distance to urban areas. _Energy_


_Policy_, 91:75–86, 2016.


[38] David EHJ Gernaat, Harmen Sytze de Boer, Vassilis Daioglou, Seleshi G Yalew, Christoph


M¨uller, and Detlef P van Vuuren. Climate change impacts on renewable energy supply.


_Nature Climate Change_, 11(2):119–125, 2021.


[39] Ministry of Natural Resources of the People’s Republic of China. Guidelines for industrial


land use policy (2019 version) (in Chinese). [EB/OL], 04 2019. `[http://www.gov.cn/](http://www.gov.cn/zhengce/zhengceku/2019-10/14/content_5439551.htm)`


`[zhengce/zhengceku/2019-10/14/content_5439551.htm](http://www.gov.cn/zhengce/zhengceku/2019-10/14/content_5439551.htm)` .


112


REFERENCES


[40] Ministry of Land and Resources (MLR) of the People’s Republic of China. Notice on


supporting the development of photovoltaic power generation industry to regulate land


management related work (in Chinese). [EB/OL], 2023. `[https://www.gov.cn/zhengce/](https://www.gov.cn/zhengce/zhengceku/2023-04/03/content_5749824.htm)`


`[zhengceku/2023-04/03/content_5749824.htm](https://www.gov.cn/zhengce/zhengceku/2023-04/03/content_5749824.htm)` .


[41] Fuying Chen, Qing Yang, Niting Zheng, Yuxuan Wang, Junling Huang, Lu Xing, Jianlan


Li, Shuanglei Feng, Guoqian Chen, and Jan Kleissl. Assessment of concentrated solar power


generation potential in china based on geographic information system (gis). _Applied Energy_,


315:119045, 2022.


[42] The National People’s Congress of the People’s Republic of China. Land Administration


Law of the People’s Republic of China. [EB/OL], 9 2019. `[http://www.npc.gov.cn/npc/](http://www.npc.gov.cn/npc/c30834/201909/d1e6c1a1eec345eba23796c6e8473347.shtml)`


`[c30834/201909/d1e6c1a1eec345eba23796c6e8473347.shtml](http://www.npc.gov.cn/npc/c30834/201909/d1e6c1a1eec345eba23796c6e8473347.shtml)` .


[43] Grassland Law of the People’s Republic of China (in Chinese). [EB/OL], 4 2021. `[https:](https://flk.npc.gov.cn/detail2.html?ZmY4MDgxODE3YWIyMmI4YTAxN2FiZDVhZDI4NjA1N2E)`


`[//flk.npc.gov.cn/detail2.html?ZmY4MDgxODE3YWIyMmI4YTAxN2FiZDVhZDI4NjA1N2E](https://flk.npc.gov.cn/detail2.html?ZmY4MDgxODE3YWIyMmI4YTAxN2FiZDVhZDI4NjA1N2E)` .


[44] Guiding Opinions of the Ministry of Water Resources on Strengthening the control of shore

line space of river and lake waters (in Chinese). [EB/OL], 5 2022. `[https://www.gov.cn/](https://www.gov.cn/gongbao/content/2022/content_5701574.htm)`


`[gongbao/content/2022/content_5701574.htm](https://www.gov.cn/gongbao/content/2022/content_5701574.htm)` .


[45] Zhixin Zhang, Zhen Qian, Teng Zhong, Min Chen, Kai Zhang, Yue Yang, Rui Zhu, Fan


Zhang, Haoran Zhang, Fangzhuo Zhou, Jianing Yu, Bingyue Zhang, Guonian L¨u, and


Jinyue Yan. Vectorized rooftop area data for 90 cities in China. _Scientific Data_, 9(1):66,


March 2022.


[46] Siddharth Joshi, Shivika Mittal, Paul Holloway, Priyadarshi Ramprasad Shukla, Brian


´O Gallach´oir, and James Glynn. High resolution global spatiotemporal assessment of rooftop


solar photovoltaics potential for renewable electricity generation. _Nature Communications_,


12(1):5738, October 2021.


[47] Zhixin Zhang, Min Chen, Teng Zhong, Rui Zhu, Zhen Qian, Fan Zhang, Yue Yang, Kai


Zhang, Paolo Santi, Kaicun Wang, Yingxia Pu, Lixin Tian, Guonian L¨u, and Jinyue Yan.


Carbon mitigation potential afforded by rooftop photovoltaic in China. _Nature Communi-_


_cations_, 14(1):2347, April 2023.


[48] Krishna Karra, Caitlin Kontgis, Zoe Statman-Weil, Joseph C. Mazzariello, Mark Mathis,


and Steven P. Brumby. Global land use / land cover with Sentinel 2 and deep learning.


In _2021 IEEE International Geoscience and Remote Sensing Symposium IGARSS_, pages


4704–4707, July 2021.


113


REFERENCES


[49] Forrest R. Stevens, Andrea E. Gaughan, Catherine Linard, and Andrew J. Tatem. Disag

gregating census data for population mapping using random forests with remotely-sensed


and ancillary data. _PloS One_, 10(2):e0107042, 2015.


[50] Christopher D. Elvidge. Annual Time Series of Global VIIRS Nighttime Lights Derived


from Monthly Averages: 2012 to 2019. _Remote Sensing._, 2021.


[51] Microsoft. Global Building ML Footprints. `[https://github.com/microsoft/](https://github.com/microsoft/GlobalMLBuildingFootprints)`


`[GlobalMLBuildingFootprints](https://github.com/microsoft/GlobalMLBuildingFootprints)`, 2023.


[52] OpenStreetMapContributors. Openstreetmap data. `[https://www.openstreetmap.org](https://www.openstreetmap.org)`,


2022.


[53] Xinyang Guo, Xinyu Chen, Xia Chen, Peter Sherman, Jinyu Wen, and Michael McElroy.


Grid integration feasibility and investment planning of offshore wind power under carbon

neutral transition in China. _Nature Communications_, 14(1):2447, 2023.


[54] Rongrong Xu, Zhenzhong Zeng, Ming Pan, Alan D Ziegler, Joseph Holden, Dominick V


Spracklen, Lee E Brown, Xinyue He, Deliang Chen, Bin Ye, et al. A global-scale framework


for hydropower development incorporating strict environmental constraints. _Nature Water_,


1(1):113–122, 2023.


[55] David E. H. J. Gernaat, Patrick W. Bogaart, Detlef P. van Vuuren, Hester Biemans, and


Robin Niessink. High-resolution assessment of global technical and economic hydropower


potential. _Nature Energy_, 2(10):821–828, October 2017. Number: 10 Publisher: Nature


Publishing Group.


[56] Yamazaki Dai. MERIT Hydro: global hydrography datasets. `[http://hydro.iis.u-tokyo.](http://hydro.iis.u-tokyo.ac.jp/~yamadai/MERIT_Hydro/)`


`[ac.jp/~yamadai/MERIT_Hydro/](http://hydro.iis.u-tokyo.ac.jp/~yamadai/MERIT_Hydro/)`, 6 2019. Accessed: 27 February 2024.


[57] Peirong Lin, Ming Pan, Hylke E Beck, Yuan Yang, Dai Yamazaki, Renato Frasson, C´edric H


David, Michael Durand, Tamlin M Pavelsky, George H Allen, et al. Global reconstruction of


naturalized river flows at 2.94 million reaches. _Water resources research_, 55(8):6499–6516,


2019.


[58] Rui Wang, Wenjia Cai, Le Yu, Wei Li, Lei Zhu, Bowen Cao, Jin Li, Jianxiang Shen, Shihui


Zhang, Yaoyu Nie, et al. A high spatial resolution dataset of China’s biomass resource


potential. _Scientific Data_, 10(1):1–15, 2023.


[59] Xi Lu, Liang Cao, Haikun Wang, Wei Peng, Jia Xing, Shuxiao Wang, Siyi Cai, Bo Shen,


Qing Yang, Chris P Nielsen, et al. Gasification of coal and biomass as a net carbon

114


REFERENCES


negative power source for environment-friendly electricity generation in china. _Proceedings_


_of the National Academy of Sciences_, 116(17):8206–8213, 2019.


[60] Yi-Ming Wei, Jia-Ning Kang, Lan-Cui Liu, Qi Li, Peng-Tao Wang, Juan-Juan Hou, Qiao

Mei Liang, Hua Liao, Shi-Feng Huang, and Biying Yu. A proposed global layout of carbon


capture and storage in line with a 2 c climate target. _Nature Climate Change_, 11(2):112–118,


2021.


[61] LGH Van der Meer. The conditions limiting co2 storage in aquifers. _Energy Conversion_


_and Management_, 34(9-11):959–966, 1993.


[62] Angela Goodman, Alexandra Hakala, Grant Bromhal, Dawn Deel, Traci Rodosta, Scott


Frailey, Mitchell Small, Doug Allen, Vyacheslav Romanov, Jim Fazio, et al. Us doe method

ology for the development of geologic storage potential for carbon dioxide at the national


and regional scale. _International Journal of Greenhouse Gas Control_, 5(4):952–965, 2011.


[63] Rui Wang, Haoran Li, Wenjia Cai, Xueqin Cui, Shihui Zhang, Jin Li, Yuwei Weng, Xinke


Song, Bowen Cao, Lei Zhu, Le Yu, Wei Li, Lin Huang, Binbin Qi, Weidong Ma, Jiang Bian,


Jia Zhang, Yaoyu Nie, Jingying Fu, Jiutian Zhang, and Can Wang. Alternative Pathway to


Phase Down Coal Power and Achieve Negative Emission in China. _Environmental Science_


_& Technology_, 56(22):16082–16093, November 2022.


[64] National Energy Administration (NEA). In 2023, the total electricity consumption in so

ciety increased by 6.7% year-on-year, 2024. `[https://www.nea.gov.cn/2024-01/18/c_](https://www.nea.gov.cn/2024-01/18/c_1310760885.htm)`


`[1310760885.htm](https://www.nea.gov.cn/2024-01/18/c_1310760885.htm)` .


[65] Yinbiao Shu, Yong Zhao, Liang Zhao, Bo Qiu, Mei Liu, and Yang Yang. Study on Low Car

bon Energy Transition Path Toward Carbon Peak and Carbon Neutrality (in Chinese). _Pro-_


_ceedings of the CSEE_, 43(5):1663–1671, 2023. `[http://ntps.epri.sgcc.com.cn/djgcxb/](http://ntps.epri.sgcc.com.cn/djgcxb/CN/10.13334/j.0258-8013.pcsee.221407)`


`[CN/10.13334/j.0258-8013.pcsee.221407](http://ntps.epri.sgcc.com.cn/djgcxb/CN/10.13334/j.0258-8013.pcsee.221407)` .


[66] Hui Li, Dong Liu, and Danyang Yao. Analysis and Reflection on the Development of


Power System Towards the Goal of Carbon Emission Peak and Carbon Neutrality (in


Chinese). _Proceedings of the CSEE_, 41(18):6245–6258, 2021. `[http://ntps.epri.sgcc.](http://ntps.epri.sgcc.com.cn/djgcxb/CN/10.13334/j.0258-8013.pcsee.210050)`


`[com.cn/djgcxb/CN/10.13334/j.0258-8013.pcsee.210050](http://ntps.epri.sgcc.com.cn/djgcxb/CN/10.13334/j.0258-8013.pcsee.210050)` .


[67] Xiliang Zhang, Xiaodan Huang, Da Zhang, Yong Geng, Lixin Tian, Ying Fan, and Wenyin


Chen. Research on the pathway and policies for China’s energy and economy transformation


toward carbon neutrality (in Chinese). _Journal of Management World_, 38 (01)(1):35–66,


2022.


115


REFERENCES


[68] National Energy Administration (NEA). In 2022, the total electricity consumption in so

ciety increased by 3.6% year-on-year, 2023. `[https://www.gov.cn/xinwen/2023-01/18/](https://www.gov.cn/xinwen/2023-01/18/content_5737694.htm)`


`[content_5737694.htm](https://www.gov.cn/xinwen/2023-01/18/content_5737694.htm)` .


[69] National Energy Administration (NEA). National power industry statistics in 2022, 2023.


`[http://www.nea.gov.cn/2023-01/18/c_1310691509.htm](http://www.nea.gov.cn/2023-01/18/c_1310691509.htm)` .


[70] OpenInfraMap. Open Infrastructure Map, 2023. `[https://openinframap.org/#3.57/44.](https://openinframap.org/#3.57/44.73/85.94)`


`[73/85.94](https://openinframap.org/#3.57/44.73/85.94)` .


[71] Natural Earth. 1:50m Cultural Vectors, 2023. `[https://www.naturalearthdata.com/](https://www.naturalearthdata.com/downloads/50m-cultural-vectors/)`


`[downloads/50m-cultural-vectors/](https://www.naturalearthdata.com/downloads/50m-cultural-vectors/)` .


[72] Jin Xu, Guiqiang Wei, Yi Jin, Guangzhou Zhang, Ke Zhang, and Haishun Sun. Economic


Analysis on Integration Topology of Rudong Offshore Wind Farm in Jiangsu Province. _High_


_Voltage Engineering_, 43(1):74–81, 2017.


[73] Yue Zhang and Zijian Cao. _Analysis report on domestic and foreign power grid development_


_in 2020 (in Chinese)_ . China Electric Power Press, 11 2020.


[74] China Renewable Energy Engineering Institute. China Renewable Energy Development


Report 2022 (in Chinese), 6 2023.


[75] Qixing Sun, Chao Zhang, Chengren Li, Peipei You, Xiao Gao, Qian Zhao, Zhao Xu, Sijia


Liu, and Li Yanlin. Prediction of Power System Cost and Price Level Under the Goal of


“Carbon Peak and Carbon Neutralization” (in Chinese). _ELECTRIC POWER_, 56(1):9–16,


2023.


[76] National Renewable Energy Laboratory. 2024 electricity annual technology baseline (atb)


technologies. `[https://www.nrel.gov/analysis/atb/technologies/electricity.html](https://www.nrel.gov/analysis/atb/technologies/electricity.html)`,


2024. Accessed: 2025-01-04.


[77] Yijing Wang, Rong Wang, Katsumasa Tanaka, Philippe Ciais, Josep Penuelas, Yves Balka

nski, Jordi Sardans, Didier Hauglustaine, Wang Liu, Xiaofan Xing, et al. Accelerating the


energy transition towards photovoltaic and wind in china. _Nature_, 619(7971):761–767, 2023.


[78] Junhao Tian, Sheng Zhou, and Yu Wang. Assessing the technical and economic poten

tial of wind and solar energy in china—a provincial-scale analysis. _Environmental Impact_


_Assessment Review_, 102:107161, 2023.


[79] Wenhua Wan, Petra D¨oll, and Hang Zheng. Risk of climate change for hydroelectricity


production in china is small but significant reductions cannot be precluded for more than


a third of the installed capacity. _Water Resources Research_, 58(8):e2022WR032380, 2022.


116


REFERENCES


[80] China Electric Power Planning and Engineering Institute (CEPPEI). Report on China


Electric Power Development 2023 (in Chinese), 8 2023.


[81] Xingning Han, Xinyu Chen, Michael B McElroy, Shiwu Liao, Chris P Nielsen, and Jinyu


Wen. Modeling formulation and validation for accelerated simulation and flexibility assess

ment on large scale power systems under higher renewable penetrations. _Applied energy_,


237:145–154, 2019.


[82] Yuanzhe Yang, Hongyu Zhang, Weiming Xiong, Da Zhang, and Xiliang Zhang. Regional


power system modeling for evaluating renewable energy development and co2 emissions


reduction in china. _Environmental Impact Assessment Review_, 73:142–151, 2018.


[83] Huanfen Zhan. Thermal Economics Analysis on the System of the Thermal Power Plant


Combined with Nuclear Power Plant (in Chinese). Master’s thesis, North China Electric


Power University, 3 2017.


[84] Shu Zhang and Wenying Chen. Assessing the energy transition in china towards carbon


neutrality with a probabilistic framework. _Nature communications_, 13(1):1–15, 2022.


[85] Bowen Matt, Ochu Emeka, and Glynn James. The Uncertain Costs of New Nu

clear Reactors: What Study Estimates Reveal about the Potential for Nuclear in


a Decarbonizing World. Technical report, Columbia University CGEP, Columbia,


12 2023. `[https://www.energypolicy.columbia.edu/wp-content/uploads/2023/12/](https://www.energypolicy.columbia.edu/wp-content/uploads/2023/12/QCFNuclearCosts-CGEP_Report_112923.pdf)`


`[QCFNuclearCosts-CGEP_Report_112923.pdf](https://www.energypolicy.columbia.edu/wp-content/uploads/2023/12/QCFNuclearCosts-CGEP_Report_112923.pdf)` .


[86] Binjun Ai. Optimization of Coal-fired Power Considering Water Resources Constraints (in


Chinese). Master’s thesis, North China Electric Power University, 6 2021.


[87] Price Testing Center of the National Development and Reform Commission (NRDC). China


Thermal Coal Index for January 2020 (in Chinese). [EB/OL] `[https://www.sci99.com/](https://www.sci99.com/bigdata/coal/)`


`[bigdata/coal/](https://www.sci99.com/bigdata/coal/)`, 01 2020.


[88] China National Coal Association (CNCS). 2020 Annual Report on the Development of


the Coal Industry (in Chinese). [EB/OL] `[https://www.coalchina.org.cn/uploadfile/](https://www.coalchina.org.cn/uploadfile/2021/0303/20210303022435291.pdf)`


`[2021/0303/20210303022435291.pdf](https://www.coalchina.org.cn/uploadfile/2021/0303/20210303022435291.pdf)`, 03 2021.


[89] China National Coal Association (CNCS). 2022 Annual Report on the Development of


the Coal Industry (in Chinese). [EB/OL] `[https://www.coalchina.org.cn/index.php?a=](https://www.coalchina.org.cn/index.php?a=show&catid=464&id=146683)`


`[show&catid=464&id=146683](https://www.coalchina.org.cn/index.php?a=show&catid=464&id=146683)`, 03 2023.


[90] National Development and Reform Commission (NRDC). Price List of


Natural Gas Benchmark Stations by Province (Region, City)(in Chinese).


117


REFERENCES


[EB/OL] `[https://www.gov.cn/zhengce/zhengceku/2019-09/27/5433961/files/](https://www.gov.cn/zhengce/zhengceku/2019-09/27/5433961/files/766dd55e2e0b48be88a0748c5f13e0c0.pdf)`


`[766dd55e2e0b48be88a0748c5f13e0c0.pdf](https://www.gov.cn/zhengce/zhengceku/2019-09/27/5433961/files/766dd55e2e0b48be88a0748c5f13e0c0.pdf)`, 09 2019.


[91] Zheng Baojun and Li Shiran. Study on Effective Elements and Improvement Measures for


Nuclear Power Project (in Chinese). _Nuclear Power Exploration_, 16(03):327–332, 06 2023.


[92] MIT Energy Initiative and Princeton University ZERO lab. GenX: a configurable power


system capacity expansion model for studying low-carbon energy futures. `[https://github.](https://github.com/GenXProject/GenX)`


`[com/GenXProject/GenX](https://github.com/GenXProject/GenX)`, 2022. Accessed: 2024-02-25.


[93] National Energy Administration (NEA). Medium and long-term development plan of


pumped storage (2021-2035) (Draft for soliciting opinions) (in Chinese). [EB/OL], 8 2021.


`[https://m.sohu.com/a/483866703_121123711/?pvid=000115_3w_a](https://m.sohu.com/a/483866703_121123711/?pvid=000115_3w_a)` .


[94] Anhui Energy Administration. Anhui Province New Energy Storage De

velopment Plan (2022-2025). `[https://fzggw.ah.gov.cn/group6/M00/06/29/](https://fzggw.ah.gov.cn/group6/M00/06/29/wKg8BmMFft2AN0gHAAXBQOST-d0441.pdf?eqid=b570814a00000865000000066466e8cc)`


`[wKg8BmMFft2AN0gHAAXBQOST-d0441.pdf?eqid=b570814a00000865000000066466e8cc](https://fzggw.ah.gov.cn/group6/M00/06/29/wKg8BmMFft2AN0gHAAXBQOST-d0441.pdf?eqid=b570814a00000865000000066466e8cc)`, 08


2022. Accessed: 2024-02-25.


[95] The People’s Government of the Guizhou Province. Implementation Plan for Carbon


Peaking in Guizhou Province. `[https://www.guizhou.gov.cn/zwgk/zcjd/mtsj/202212/](https://www.guizhou.gov.cn/zwgk/zcjd/mtsj/202212/t20221206_77351479.html)`


`[t20221206_77351479.html](https://www.guizhou.gov.cn/zwgk/zcjd/mtsj/202212/t20221206_77351479.html)`, 12 2022. Accessed: 2024-02-25.


[96] The People’s Government of the Jiangxi Province. Implementation Plan for Carbon Peaking


in Jiangxi Province. `[https://www.jiangxi.gov.cn/art/2022/7/18/art_396_4033340.](https://www.jiangxi.gov.cn/art/2022/7/18/art_396_4033340.html)`


`[html](https://www.jiangxi.gov.cn/art/2022/7/18/art_396_4033340.html)`, 7 2022. Accessed: 2024-02-25.


[97] Shanxi Energy Administration. Implementation Plan for the Development


of New Energy Storage in Shanxi Province during the 14th Five Year Plan.

```
   https://www.shanxi.gov.cn/ywdt/sxyw/202211/t20221112_7411409.shtml?eqid=

```

`[feed63c2000623080000000364531ffe](https://www.shanxi.gov.cn/ywdt/sxyw/202211/t20221112_7411409.shtml?eqid=feed63c2000623080000000364531ffe)`, 12 2021. Accessed: 2024-02-25.


[98] DRC of the Beijing City. Implementation Plan for Carbon Peaking in Bei

jing City. `[https://fgw.beijing.gov.cn/fgwzwgk/zcgk/sjbmgfxwj/bjszfwj/202210/](https://fgw.beijing.gov.cn/fgwzwgk/zcgk/sjbmgfxwj/bjszfwj/202210/t20221014_2836238.htm)`


`[t20221014_2836238.htm](https://fgw.beijing.gov.cn/fgwzwgk/zcgk/sjbmgfxwj/bjszfwj/202210/t20221014_2836238.htm)`, 10 2022. Accessed: 2024-02-25.


[99] The People’s Government of the Jilin Province. Implementation Plan for Carbon Peaking in


Jilin Province. `[https://www.jl.gov.cn/szfzt/jlssxsxnyxdh/zcyt/202208/t20220809_](https://www.jl.gov.cn/szfzt/jlssxsxnyxdh/zcyt/202208/t20220809_8534542.html)`


`[8534542.html](https://www.jl.gov.cn/szfzt/jlssxsxnyxdh/zcyt/202208/t20220809_8534542.html)`, 08 2022. Accessed: 2024-02-25.


118


REFERENCES


[100] The People’s Government of the Sichuan Province. Development Plan for Power Grid


in Sichuan Province (2022-2025). `[https://www.sc.gov.cn/10462/zfwjts/2022/12/6/](https://www.sc.gov.cn/10462/zfwjts/2022/12/6/7a64ec5abcc447ad9632d39ab8bebafb.shtml)`


`[7a64ec5abcc447ad9632d39ab8bebafb.shtml](https://www.sc.gov.cn/10462/zfwjts/2022/12/6/7a64ec5abcc447ad9632d39ab8bebafb.shtml)`, 12 2022. Accessed: 2024-02-25.


[101] DRC of the Hebei Province. Hebei Province’s 14th Five Year Plan for the Development


of New Energy Storage. `[https://hbdrc.hebei.gov.cn/xxgk_2232/fdzdgknr/ghjh/gh/](https://hbdrc.hebei.gov.cn/xxgk_2232/fdzdgknr/ghjh/gh/202309/t20230907_87227.html)`


`[202309/t20230907_87227.html](https://hbdrc.hebei.gov.cn/xxgk_2232/fdzdgknr/ghjh/gh/202309/t20230907_87227.html)`, 04 2022. Accessed: 2024-02-25.


[102] The People’s Government of the Liaoning Province. Liaoning Province’s 14th Five Year


Plan for Energy Development. `[https://www.ln.gov.cn/web/zwgkx/lnsrmzfgb/2022n/](https://www.ln.gov.cn/web/zwgkx/lnsrmzfgb/2022n/qk/2022n_dsqq/szfbgtwj/00492D1A9B324E6BA9776EBF8CFA294C/index.shtml)`


`[qk/2022n_dsqq/szfbgtwj/00492D1A9B324E6BA9776EBF8CFA294C/index.shtml](https://www.ln.gov.cn/web/zwgkx/lnsrmzfgb/2022n/qk/2022n_dsqq/szfbgtwj/00492D1A9B324E6BA9776EBF8CFA294C/index.shtml)`, 7 2022.


Accessed: 2024-02-25.


[103] The People’s Government of the Tianjin City. Implementation Plan for Carbon Peak

ing in Tianjin City. `[https://www.tj.gov.cn/zwgk/szfwj/tjsrmzf/202209/t20220914_](https://www.tj.gov.cn/zwgk/szfwj/tjsrmzf/202209/t20220914_5987984.html)`


`[5987984.html](https://www.tj.gov.cn/zwgk/szfwj/tjsrmzf/202209/t20220914_5987984.html)`, 08 2022. Accessed: 2024-02-25.


[104] DRC of the Inner Mongolia Autonomous Region. Inner Mongolia Autonomous Region’s


14th Five Year Plan for Electric Power Development. `[https://nyj.nmg.gov.cn/zwgk/](https://nyj.nmg.gov.cn/zwgk/zfxxgkzl/fdzdgknr/ghjh/202203/t20220329_2024984.html)`


`[zfxxgkzl/fdzdgknr/ghjh/202203/t20220329_2024984.html](https://nyj.nmg.gov.cn/zwgk/zfxxgkzl/fdzdgknr/ghjh/202203/t20220329_2024984.html)`, 03 2022. Accessed: 2024

02-25.


[105] DRC of the Ningxia Hui Autonomous Region. Implementation Plan for the Development of


New Energy Storage in Ningxia during the 14th Five Year Plan period. `[https://fzggw.nx.](https://fzggw.nx.gov.cn/zcgh/fgwwj/202302/t20230223_3972125.html)`


`[gov.cn/zcgh/fgwwj/202302/t20230223_3972125.html](https://fzggw.nx.gov.cn/zcgh/fgwwj/202302/t20230223_3972125.html)`, 02 2023. Accessed: 2024-02-25.


[106] The People’s Government of the Fujian Province. Fujian Province Action Plan for Promot

ing Green Economy Development (2022-2025). `[https://www.fujian.gov.cn/zwgk/ztzl/](https://www.fujian.gov.cn/zwgk/ztzl/tjzfznzb/zcwj/fj/202209/t20220909_5989918.htm)`


`[tjzfznzb/zcwj/fj/202209/t20220909_5989918.htm](https://www.fujian.gov.cn/zwgk/ztzl/tjzfznzb/zcwj/fj/202209/t20220909_5989918.htm)`, 08 2022. Accessed: 2024-02-25.


[107] The People’s Government of Henan Province. Implementation Plan for Carbon Peaking


in Jiangxi Province. `[https://fgw.henan.gov.cn/2022/08-02/2553184.html](https://fgw.henan.gov.cn/2022/08-02/2553184.html)`, 02 2023.


Accessed: 2024-02-25.


[108] DRC of the Qinghai Province, Qinghai Energy Administration. Implementation Plan


for Carbon Peak in Qinghai Province. `[http://fgw.qinghai.gov.cn/zfxxgk/sdzdgknr/](http://fgw.qinghai.gov.cn/zfxxgk/sdzdgknr/fgwwj/202308/t20230823_85120.html)`


`[fgwwj/202308/t20230823_85120.html](http://fgw.qinghai.gov.cn/zfxxgk/sdzdgknr/fgwwj/202308/t20230823_85120.html)`, 08 2023. Accessed: 2024-02-25.


[109] The People’s Government of Gansu Province. Gansu Province’s 14th Five Year Plan


for Energy Development. `[https://www.gansu.gov.cn/gsszf/c100055/202201/1947911/](https://www.gansu.gov.cn/gsszf/c100055/202201/1947911/files/b0b48c283cb542039d600685fb3411c8.pdf)`


`[files/b0b48c283cb542039d600685fb3411c8.pdf](https://www.gansu.gov.cn/gsszf/c100055/202201/1947911/files/b0b48c283cb542039d600685fb3411c8.pdf)`, 12 2021. Accessed: 2024-02-25.


119


REFERENCES


[110] The People’s Government of Hubei Province. The 14th Five Year Plan for Energy Devel

opment in Hubei Province. `[https://www.hubei.gov.cn/zfwj/ezf/202205/t20220519_](https://www.hubei.gov.cn/zfwj/ezf/202205/t20220519_4134056.shtml)`


`[4134056.shtml](https://www.hubei.gov.cn/zfwj/ezf/202205/t20220519_4134056.shtml)`, 04 2022. Accessed: 2024-02-25.


[111] The People’s Government of Guangdong Province. Guiding Opinions on Promoting High


Quality Development of New Energy Storage Industry in Guangdong Province. `[https://](https://www.gd.gov.cn/zwgk/gongbao/2023/8/content/post_4137229.html)`


`[www.gd.gov.cn/zwgk/gongbao/2023/8/content/post_4137229.html](https://www.gd.gov.cn/zwgk/gongbao/2023/8/content/post_4137229.html)`, 03 2023. Accessed:


2024-02-25.


[112] DRC of the Hunan Province. Hunan Power Support Capacity Improvement Action


Plan (2022-2025). `[https://fgw.hunan.gov.cn/fgw/xxgk_70899/gzdtf/gzdt/202210/](https://fgw.hunan.gov.cn/fgw/xxgk_70899/gzdtf/gzdt/202210/t20221013_29053290.html)`


`[t20221013_29053290.html](https://fgw.hunan.gov.cn/fgw/xxgk_70899/gzdtf/gzdt/202210/t20221013_29053290.html)`, 10 2022. Accessed: 2024-02-25.


[113] Shandong Energy Administration. Action Plan for the Development of New Energy Stor

age Projects in Shandong Province. `[http://nyj.shandong.gov.cn/art/2022/12/30/art_](http://nyj.shandong.gov.cn/art/2022/12/30/art_100393_10295573.html?eqid=e5a9b5860016efc000000002643790d6)`


`[100393_10295573.html?eqid=e5a9b5860016efc000000002643790d6](http://nyj.shandong.gov.cn/art/2022/12/30/art_100393_10295573.html?eqid=e5a9b5860016efc000000002643790d6)`, 12 2022. Accessed:


2024-02-25.


[114] The People’s Government of the Yunnan Province. Yunnan Province’s Climate Change


Response Plan (2021-2025). `[https://sthjt.yn.gov.cn/dqhjzlgl/wsqtyydqh/202211/](https://sthjt.yn.gov.cn/dqhjzlgl/wsqtyydqh/202211/t20221129_232270.html)`


`[t20221129_232270.html](https://sthjt.yn.gov.cn/dqhjzlgl/wsqtyydqh/202211/t20221129_232270.html)`, 11 2022. Accessed: 2024-02-25.


[115] The People’s Government of the Guangxi Zhuang Autonomous Region. Implementation


Plan for Carbon Peaking in Guangxi Autonomous. `[http://www.gxzf.gov.cn/zfwj/](http://www.gxzf.gov.cn/zfwj/zzqrmzfwj_34845/t15690666.shtml?eqid=9e7cd2f900223bee0000000464845370)`


`[zzqrmzfwj_34845/t15690666.shtml?eqid=9e7cd2f900223bee0000000464845370](http://www.gxzf.gov.cn/zfwj/zzqrmzfwj_34845/t15690666.shtml?eqid=9e7cd2f900223bee0000000464845370)`, 12


2022. Accessed: 2024-02-25.


[116] DRC of the Jiangsu Province. Implementation Plan for the Development of


New Energy Storage in Jiangsu Province during the 14th Five Year Plan.

```
   https://fzggw.jiangsu.gov.cn/art/2022/8/8/art_83783_10566543.html?eqid=

```

`[aa7fb89b00035bc20000000464917282](https://fzggw.jiangsu.gov.cn/art/2022/8/8/art_83783_10566543.html?eqid=aa7fb89b00035bc20000000464917282)`, 08 2022. Accessed: 2024-02-25.


[117] DRC of the Zhejiang Province, Zhejiang Energy Administration. Zhejiang Province’s 14th


Five Year Plan for the Development of New Pig Energy. `[https://www.zj.gov.cn/art/](https://www.zj.gov.cn/art/2022/6/6/art_1229540815_4933249.html)`


`[2022/6/6/art_1229540815_4933249.html](https://www.zj.gov.cn/art/2022/6/6/art_1229540815_4933249.html)`, 06 2022. Accessed: 2024-02-25.


[118] D Han, Z Hi ZHAO, BZ Yan, et al. Status and prospect of china’s pumped storage devel

opment in 2021. _Water Power_, 48(5):1–4, 2022.


[119] Oliver Schmidt, Sylvain Melchior, Adam Hawkes, and Iain Staffell. Projecting the future


levelized cost of electricity storage technologies. _Joule_, 3:81–100, 1 2019.


120


REFERENCES


[120] Fei Cao, Xiangyu Niu, Huijun Li, and Jiejun Zhao. Analysis on operation and maintenance


cost of variable speed pumped-storage unit. _Water Power_, 44:96–99, 6 2018.


[121] Behnam Zakeri and Sanna Syri. Electrical energy storage systems: A comparative life cycle


cost analysis. _Renewable and sustainable energy reviews_, 42:569–596, 2015.


[122] Haisheng Chen, Thang Ngoc Cong, Wei Yang, Chunqing Tan, Yongliang Li, and Yulong


Ding. Progress in electrical energy storage system: A critical review. _Progress in Natural_


_Science_, 19:291–312, 2009.


[123] Li Hong and Lyu Yingchun. A review on electrochemical energy storage. _Journal of_


_Elctrochem_, 21:412–424, 10 2015.


[124] Guannan He, Jeremy Michalek, Soummya Kar, Qixin Chen, Da Zhang, and Jay F.


Whitacre. Utility-scale portable energy storage systems. _Joule_, 5:379–392, 2 2021.


[125] Rub´en Romero, A Monticelli, Ae Garcia, and S´ergio Haffner. Test systems and mathe

matical models for transmission network expansion planning. _IEE Proceedings-Generation,_


_Transmission and Distribution_, 149(1):27–36, 2002.


[126] Fangxu Gui, Heng Chen, Xinyue Zhao, Peiyuan Pan, Cheng Xin, and Xue Jiang. Life Cycle


Cost Analysis and Optimization of Transformers (in Chinese). _Guangdong Electric Power_,


37(3):44–53, 2024.


[127] Friedrich Kiessling, Peter Nefzger, Joao Felix Nolasco, and Ulf Kaintzyk. _Overhead Power_


_Lines Planning, Design, Construction_ . Springer, 2003.


[128] Xiaonan Han, Qiushi Li, and Ke Sun. Comparison Study on Enegy Saving and Consump

tion Reduction of UHV AC and EHV AC Transmission Technology (in Chinese). _Electric_


_Technology_, 47:145–149, 2 2021.


[129] John Young, Noah McQueen, Charithea Charalambous, Spyros Foteinis, Olivia Hawrot,


Manuel Ojeda, H´el`ene Pilorg´e, John Andresen, Peter Psarras, Phil Renforth, Susana Garcia,


and Spek Mijndert van der. The cost of direct air capture and storage can be reduced via


strategic deployment but is unlikely to fall below stated cost targets. _One Earth_, 6(7):899–


917, 2023.


[130] Zhanwei Liu and Xiaogang He. Balancing-oriented hydropower operation makes the clean


energy transition more affordable and simultaneously boosts water security. _Nature Water_,


1(9):778–789, 2023.


121


REFERENCES


[131] Qingyu Xu, Wilson Ricks, Aneesha Manocha, Neha Patankar, and Jesse D Jenkins. System

level impacts of voluntary carbon-free electricity procurement strategies. _Joule_, 2024.


[132] Philipp Beiter, Trieu Mai, Matthew Mowers, and John Bistline. Expanded modelling sce

narios to understand the role of offshore wind in decarbonizing the united states. _Nature_


_Energy_, pages 1–10, 2023.


[133] Michael R Davidson and J Ignacio P´erez-Arriaga. Modeling unit commitment in political


context: Case of china’s partially restructured electricity sector. _IEEE Transactions on_


_Power Systems_, 33(5):4889–4901, 2018.


122




---

# Energy & Environmental Science




|Col1|Col2|Col3|
|---|---|---|
||APER<br>**View Article Online**<br>||
||**View Journal | View Issue**||



Cite this: _Energy Environ. Sci._,

2025, 18, 3699


Received 19th January 2025,
Accepted 17th February 2025


DOI: 10.1039/d5ee00355e


[rsc.li/ees](https://rsc.li/ees)



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-0-0.png)
## Integrated modeling for the transition pathway of China’s power system†

Ziheng Zhu, a Da Zhang,* a Xiaoye Zhang* b and Xiliang Zhang* a


Accelerating decarbonization of the power system is at the heart of achieving China’s carbon neutrality


goal and mitigating global climate change. However, deploying multi-terawatts of variable renewable

energy (VRE) may result in substantial system volatility. Here, using a temporally and spatially resolved


model co-optimizing capacity expansion and system operation throughout the full 8760 hours in a


planning year, we show that achieving �550 MtCO 2 per year of negative emissions is feasible for China’s


power system by 2060 with 6000 GW of VRE, 5800 GW h of energy storage, and 850 MtCO 2 per year


of carbon capture and sequestration (CCS), at the marginal carbon abatement cost of 750–1100 yuan

per tCO 2 (about 108–157 $ per tCO 2 ). Multi-millions of hectares of land areas are necessary to accom

modate the TW-scale installation of solar photovoltaic panels, with restricted land policies resulting in a


3.3% increase in electricity costs. System volatility also surges with higher penetration of VRE, repre
sented by increasing variability in hourly marginal demand cost, necessitating firm resources to ensure


capacity adequacy. Although these firm sources can earn higher generation revenues in peak hours,

capacity compensation amounting to hundreds of yuan per kW (about tens of $ per kW) per year is still


needed. Effective planning and policy formulation are essential to support China’s decarbonization effort


for its power sector.



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-0-2.png)


### 1 Introduction

With China’s announced goal of achieving carbon neutrality
before 2060 to mitigate global climate change, [1] many studies
have proposed plans for profound decarbonization of the


a Institute of Energy, Environment and Economy, Tsinghua University, Beijing
100084, China. E-mail: <EMAIL>, <EMAIL>
b State Key Laboratory of Severe Weather, Chinese Academy of Meteorological
Sciences, Beijing 100081, China. E-mail: <EMAIL>

[† Electronic supplementary information (ESI) available. See DOI: https://doi.org/](https://doi.org/10.1039/d5ee00355e)

[10.1039/d5ee00355e](https://doi.org/10.1039/d5ee00355e)



power sector and accelerated electrification of other
sectors. [2–9] In 2023, China’s electricity consumption stood at
approximately 9.2 petawatt hours (PW h), [10] constituting over
30% of global power demand. [11] Despite a significant increase
in renewable energy installations surpassing 1500 gigawatts
(GW), [12] China’s coal-intensive power system emitted around
5.6 gigatons (Gt) of CO 2 in 2023, contributing to more than 40%
of the world’s electricity-related emissions. [11] As the electrification of various sectors advances, establishing net-zero or negative emissions power systems becomes central to China’s
climate targets, with projections indicating that the country’s
electricity demand will rise to over 15 PW h per year by



This journal is © The Royal Society of Chemistry 2025 _Energy Environ. Sci._, 2025, 18, 3699–3717 | 3699


**[View Article Online](https://doi.org/10.1039/d5ee00355e)**


Paper Energy & Environmental Science



mid-century. [3–6,13,14] Although China possesses abundant variable renewable energy (VRE) resources, such as wind and
solar, [15–17] the transition to a 100% VRE system that achieves
net-zero or negative emissions is approached with caution. [18,19]

This caution mainly arises from the uneven distribution of
VRE resources [20] and the mismatch between generation and
demand. [21] Therefore, integrated modeling to understand the
carbon abatement pathway and associated strategies is of
significant policy relevance, given that the transition necessitates deploying large-scale VRE, energy storage systems, interregional transmission infrastructure, and negative emissions
technologies (NETs).
Power system expansion models (PSEMs) are extensively
used to explore solutions for transitioning energy systems. [22]

Generally, long-term PSEMs are formulated as linear programming (LP) that integrate a variety of socioeconomic, technical,
and meteorological parameters. [22,23] Modeling zero- or negativeemissions electricity systems, which rely heavily on large-scale
deployments of renewable energies, storage, transmission,
carbon capture and sequestration (CCS), and/or direct air
capture with carbon sequestration (DACCS) technologies, presents significant challenges for large countries like China or the
United States. These challenges stem primarily from the complexity of various energy resource limitations and the computational difficulties in solving large-scale LP problems. [4,23,24] First,
continent-scale geographic coverage is necessary to account for
the spatiotemporal correlations in weather systems that impact
the power supply from wind, solar, and hydropower. Second,
full-year temporal coverage (i.e., 8760 hours) and fine temporal
resolution (r1 hour) are required to ensure a better presentation of resource adequacy and accurately capture the variability
of VRE, storage/transmission operations, thermal unit commitments, and electricity demand. [4,22,23] Third, resolved capacity
expansion resolution (grid cell or dam site level) is critical to
expressing the spatial heterogeneity and regional disparities of
VRE and hydropower for their integration distance and generation differentiation. [4,25] Fourth, possible large-scale deployment
of NETs also requires an endogenous formulation of carbon
sequestration availability and optimal carbon source-sink linkage based on the uneven distribution of carbon sources and
sinks. [26]


Models designed to analyze the massive decarbonization of
nationwide power systems usually adopt the two-stage methodology with limited representative hours [27] (ranging from tens
to hundreds) first to optimize capacity expansion and then the
long-chronological hourly operation. [2,28–30] In the context of
power-system expansion, previous research has indicated that
inadequate temporal coverage may lead to significant differences in optimal capacity deployment. [22] For example, models
using the two-stage methodology (or solely capacity expansion
with limited temporal coverage) have large variations in pre
dicted VRE installation and carbon abatement cost for China’s

power system by mid-century, ranging from 500–2300 GW for
wind, 250–5000 GW for solar photovoltaic (PV), and about 150–
1400 yuan per tCO 2 for carbon abatement cost. [2,3,31,32] Although
carbon capture, transportation, and storage, alongside biomass



energy, are significant for carbon reduction and climate change
mitigation, [6,33] these factors are often simplified without considering resource availability, [2–4,28,29] or using a soft-link
approach between carbon source-sink matching models and
PSEM. [30,34] Therefore, a refined dynamic high spatiotemporal
resolution and explicit representation of the carbon source-sink
matching model is needed to address several important interrelated questions: Is it feasible for China’s power system to
achieve negative emissions? If so, what is the pathway to

achieve it? And what are the associated costs?

In this study, we develop a temporally and spatially resolved
PSEM, the China Integrated Sustainable Power-system Optimization (CISPO) model, with hourly resolution over a full year
(8760 hours) to offer a detailed pathway for the transition of
China’s electricity system with emissions cap constraints
adopted from economy-wide models, [6,35] using 2022 as the base
year and optimizing from 2030 to 2060 (10 years as a step). The
CISPO model fundamentally improves the modeling approach
and energy-related data integration to better understand China’s power-system transition pathway compared to a recent
precedent, the RESPO model. [4] First, the CISPO co-optimizes
the expansion for all the generation capacity (compared to VRE
only in the RESPO) and hourly dispatch operation (compared to
a simplified layer model in the RESPO) subject to projected
hourly demand balance, generation output constraints, and
emissions constraints. Second, hydropower operation is optimized further at the dam site level (compared to the provincial
level in the RESPO). Third, the CISPO explicitly includes carbon
sources (thermal power with capture, or direct air capture) and
sinks (deep saline aquifer sequestration) and endogenously
optimizes the match through transportation routes between
any provinces (not considered in the RESPO). Combining fineassessed energy resource potential and the high-resolution
optimization model, we reveal many facets of challenges and
opportunities facing China’s energy planners on the road to
carbon neutrality, including siting large-scale wind, solar, and
hydropower facilities, deploying technologies to achieve capture and sequestration of hundreds of megatons (Mt) of CO 2,
and introducing capacity payments to firm power to regulate
the power system.

### 2 Results


2.1 Power system expansions toward 2060


Fig. 1 shows the optimized power system expansion toward
2060 in the base scenario, including capacity mix, generation
mix, system cost of electricity (SCOE, defined as the total
annualized capital expenditure, and operational costs of generation, storage, transmission, and other technologies divided
by the yearly system-wide demand; distribution and administration costs are not included [23] ). We find a negative-emissions
electricity system for China is feasible and will not increase the
SCOE compared to the 2022 level under our cost assumptions;
see the ESI,† Section S3 for details. Notably, more stringent
emissions constraints necessitate larger-scale deployment of



3700 | _Energy Environ. Sci._, 2025, 18, 3699–3717 This journal is © The Royal Society of Chemistry 2025


**[View Article Online](https://doi.org/10.1039/d5ee00355e)**


Energy & Environmental Science Paper

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-2-0.png)


Fig. 1 Optimized results for China’s power system toward 2060. (A) Projected capacity (GW) mix of generators. (B) Provincial capacity (GW) breakdown
of generator and storage, with Inner Mongolia divided into western (Mengxi) and eastern (Mengdong) regions because they are separate balancing
regions. (C) Optimized deployment (GW) of PHS and BAT. (D) Electricity generation mix (TW h per year). (E) System cost of electricity (SCOE, yuan per kW
h), excluding distribution and administration costs.



biomass energy with carbon capture and sequestration
(BECCS), driving a substantial SCOE increase in later periods.
Accelerated deployment of renewable energy facilitates the
decarbonization and transition of China’s power system. We
find installed capacities of 2.1 terawatts (TW) from wind energy
and 3.8 TW from solar photovoltaics (PV) in 2060, increasing



from values of around 1.0 TW and 1.8 TW, respectively, in 2030.
These wind and solar PV installations generate, respectively,
about 6.3 and 5.3 PW h per year of electricity, contributing to
about 72% of total annual generation in 2060. Concentrating
solar power (CSP) systems, modeled with an 8-hour energy
storage capacity, are projected to reach an installed capacity



This journal is © The Royal Society of Chemistry 2025 _Energy Environ. Sci._, 2025, 18, 3699–3717 | 3701


**[View Article Online](https://doi.org/10.1039/d5ee00355e)**


Paper Energy & Environmental Science



of approximately 127 GW and generate 332 TW h annually in
2060. The bulk of these CSP installations (ESI,† Fig. S64) are
situated in the provinces of Mengxi (40 GW, 100 TW h), Gansu
(31 GW, 79 TW h), Xinjiang (30 GW, 83 TW h), and Qinghai
(26 GW, 70 TW h), owing to their favorable solar resource
endowment and land availability.
Hydropower, a longstanding renewable energy source, has
undergone rapid expansion in China over the past few decades,
achieving a total installed capacity of 367 GW (excluding
pumped storage) by 2022. [36] This study optimizes hydropower
capacity expansion and reservoir operation at the dam site
level, based on a comprehensive assessment of the economic
potential of China’s hydropower, estimated at approximately
700 GW in total. Our findings suggest that by 2060, the
installed capacity of hydropower will reach 600 GW, generating
approximately 2.2 PW h per year of electricity. This capacity is
predominantly concentrated in China’s southwestern provinces, with the top three being Sichuan (182 GW), Yunnan
(108 GW), and Xizang (73 GW), accounting for 60% of the
installed capacity. See the ESI,† Fig. S66, for hydropower’s
spatial expansion from 2030 to 2060.
Energy storage has proven beneficial in managing the
uncertainty and variability in power system arising from the
large-scale deployment of VRE sources. [37,38] In this study, we
model two representative storage technologies: pumped hydro
storage (PHS) with an 8-hour duration, and lithium-ion batteries (BAT) with a 4-hour duration. By 2060, our model
indicates a deployment of 437 GW (3496 GW h) from PHS,
and 572 GW (2288 GW h) from BAT (Fig. 1). In this analysis, we
use the values reported in the latest PHS resource assessment [39]

to model PHS’s deployment potential, which is constrained by
the geographic availability. [37] Results show that the leading
regions for PHS installation in 2060 are Xizang (42 GW),
Qinghai (40 GW), Xinjiang (39 GW), and Gansu (33 GW). In
contrast, battery storage sees large-scale capacity installations
in Jiangsu (68 GW), Shandong (62 GW), Zhejiang (44 GW), and
Guangdong (40 GW), where substantial VRE capacities exist,
but lack sufficient PHS potential. In 2030, we observe installations of 120 GW PHS and 248 GW BAT, primarily located in
Shandong (8 GW PHS and 33 GW BAT), Anhui (6 GW PHS and
24 GW BAT), and Guangdong (10 GW PHS and 16 GW BAT).
Firm power sources have been shown to play an important
role in decarbonized power systems by meeting peak demand,
providing inertia and spinning reserve requirements, and
enhancing the system’s stability to integrate more VRE. [3,4,28,40]

In the CISPO model, operational and under-construction traditional coal and natural gas power plants that existed in the base
year will be phased out upon reaching their designed lifetime,
or retrofitted with carbon capture equipment to transform into
firm low-carbon power sources. Existing biomass, nuclear, and
combined heat and power (CHP) units will undergo refurbishment as needed to extend their operational lifetimes rather
than retiring. For new builds of thermal power, the model will
endogenously optimize the installed capacity along with determining whether CCS implementation is warranted. Simulated
results by the model show that there are no new capacities



added for traditional coal and gas power; see the detailed
capacity retirement from 2030 to 2060 in the ESI,† Section
S3.5. In 2060, we find an installed capacity of 181 GW for
bioenergy (168 GW with CCS), with 297 GW from coal (including CHP units) and 120 GW from gas (including CHP units),
generating about 1.42 PW h per year of electricity.
The most uncertain factor for the long-term nuclear development plan in China is whether nuclear reactors can be
deployed inland. We assume newly built nuclear power plants
will only be located in the coastal provinces, with the installation capacity potential reported by Xiao and Jiang [41] (about an
additional 170 GW) and moderate flexibility (minimum outputs
of 85%). [4,23] These nuclear power plants will be fully installed by
2050, with the nuclear capacity potentials totaling 230 GW
when including the capacity from existing and currently
under-construction nuclear power plants. This highlights the
vital role of nuclear power in achieving a low-carbon electricity
system by providing 1.7 PW h of annual generation thereafter.
The annual capacity factor of nuclear power peaks in 2040 at

0.96 and declines to 0.85 in 2050 and 2060 with the increased

integration of VRE, a higher CCS generation requirement for
meeting carbon emissions limits, and the relatively inflexible
nature of nuclear plants.


2.2 Emissions and carbon source-sink matching in power


Given the technologies modeled here, a negative emissions
power system for China around 2060 will increase system costs
by an overall 7–19% (Fig. 2) in scenarios with emissions control
compared to the ‘‘NoEmisCap’’ scenario, where carbon emissions constraints are not imposed. We adopt several exogenous
emissions pathways from economy-wide studies [6,35] for the base
scenario (�550 MtCO 2 per year), ‘‘NegEmis200Mt’’ (�200
MtCO 2 per year), and ‘‘NegEmis700Mt’’ (�700 MtCO 2 per year)
toward 2060 (see Fig. 3). If no emissions constraints are in
place, the total emissions from the electricity sector will
decrease from approximately 4.5 Gt per year in 2030 to 0.8 Gt

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-3-0.png)


Fig. 2 Increase in system cost of electricity (SCOE, %) compared to the
‘‘NoEmisCap’’ scenario.



3702 | _Energy Environ. Sci._, 2025, 18, 3699–3717 This journal is © The Royal Society of Chemistry 2025


**[View Article Online](https://doi.org/10.1039/d5ee00355e)**


Energy & Environmental Science Paper

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-4-0.png)


Fig. 3 Results of carbon capture and sequestration deployment, carbon emissions, and abatement costs. (A: Base scenario) and (B: ‘‘WithoutBECCS’’ scenario):
Annual carbon (MtCO 2 per year) captured and injected in each province and CO 2 sequestration site, and transportation volume of each capture-sequestration link
in 2050 and 2060. Provincial polygons with blue color bar (left) are annual CO 2 captured from thermal plants or DAC. Circles indicate annual CO 2 injected into
each sequestration site. Arrows with shades of color represent the carbon source-sink route and CO 2 transported along this route (right rainbow color bar).
(C) Annual carbon emissions from China’s power sector in emissions-related scenarios. (D) Marginal carbon abatement cost (yuan per tCO 2 ). (E) Annual generation
(TW h per year) from thermal generation. (F) Carbon captured (MtCO 2 per year) by DAC in 2050 and 2060 for the ‘‘WithoutBECCS’’ and ‘‘LimitedBECCS’’ scenarios.



per year in 2060, primarily driven by VRE deployment. Across
the emissions control scenarios, the highest system cost
increase (+19%) occurs in 2050 under the ‘‘NegEmis700Mt’’



scenario, equivalent to about 900 billion yuan per year (about
128 billion $ per year), compared to the ‘‘NoEmisCap’’ scenario.
This significant cost increase is due to the deployment of 126



This journal is © The Royal Society of Chemistry 2025 _Energy Environ. Sci._, 2025, 18, 3699–3717 | 3703


**[View Article Online](https://doi.org/10.1039/d5ee00355e)**


Paper Energy & Environmental Science



GW BECCS, which is required to meet the �400 MtCO 2 per
year emissions target (see Fig. 2 and the ESI,† Fig. S48, for
SCOE breakdowns). More stringent/lenient emissions pathways (‘‘NegEmis700Mt’’/‘‘NegEmis200Mt’’) see similar wind

� �
( 1.0%/+2.8%) and solar PV ( 0.7%/+3.0%) installed capacities
in 2060, compared to the base scenario. Even in the ‘‘NoEmisCap’’ scenario, 2.1 TW installed capacity from wind energy and
3.6 TW installed capacity from solar PV energy are observed in
2060, showing the robustness of large-scale VRE deployment in
the later period of China’s pathway to carbon neutrality. Before
2060, there are notable VRE installed capacity differences
among emissions-related scenarios. For example, the installed
capacity of wind and solar PV energy is 4692 GW in the
‘‘NegEmis700Mt’’ scenario, while 3300 GW is seen in the
‘‘NoEmisCap’’ scenario in 2040 (see the ESI,† Fig. S60).
Negative emissions technologies are expected to have significant carbon dioxide removal potential for limiting global
warming. [42] In the CISPO model, NETs include bioenergy with
carbon capture and sequestration (BECCS) and direct air capture with carbon sequestration (DACCS), which are the most
prominent NETs. [42] In this study, we assume the biofuel combusted for BECCS in power systems could only be from residues
and energy crops on abandoned land to avoid its possible sideeffects, [42] with the resource potential at the grid-cell level
obtained from Wang et al. [43] For optimization of this model,
DACCS is integrated with four technologies, including KOH
absorption paired with regeneration via calcium looping (KOHCa looping), KOH absorption paired with regeneration via
bipolar membrane electrodialysis (BPMED), solid sorbent
direct air capture using temperature vacuum swing adsorption,
and MgO ambient weathering with regeneration via calcination, with cost projection and key parameters obtained from
Young et al. [44] The carbon captured from thermal power plants
and the air is endogenously optimized to be transported and
sequestrated to deep saline aquifer (DSA) carbon sequestration
sites, with cost assumptions obtained from Wang et al. [45] (see
the ESI,† Sections S3.8 and S3.9 for input assumptions, and

Section S4.3 for the model formula of NETs in the CISPO

model).
If available, BECCS is selected by the CISPO to achieve
negative emissions around the mid-century. In the base scenario, we find a large-scale deployment of BECCS from 2050,
with a capacity of 81 GW and a generation of 409 TW h per year
(Fig. 3E). This BECCS generation brings about negative emissions of 495 Mt annually. By 2060, an additional 87 GW of
installed capacity and 283 TW h per year electricity generation
for BECCS (totaling 168 GW and 773 TW h per year) is required
to offset around �850 MtCO 2 per year of emissions, based on
an emissions factor assumption of �1.1 kg kW [�][1] h [�][1] for
BECCS. [34] More stringent/lenient emissions constraints result
in higher/lower BECCS installed capacity and electricity generation. In the ‘‘NegEmis700Mt’’ scenario, BECCS installed
capacity and electricity generation increase to 126 GW (+56%)
and 629 TW h per year (+54%), respectively, in 2050, while in

�
the ‘‘NegEmis200Mt’’ scenario, they decrease to 67 GW ( 17%)

�
and 338 TW h per year ( 17%), respectively. In 2060, these



figures increase to 191 GW (+14%) and 891 TW h per year

�
(+15%), and decrease to 102 GW ( 39%) and 462 TW h per year

�
( 40%), respectively. More stringent emissions targets after
2050 (‘‘NegEmis700Mt’’) may lead to a slight reduction in
renewable energy installed capacity, as more electricity generation from BECCS must be integrated, resulting in decreases of
115 GW installed capacity and 216 TW h per year electricity
generation for wind and solar (including CSP) power in 2050,
and decreases of 72 GW and 166 TW h per year in 2060 (see the
ESI,† Fig. S49 and S50).
We also consider a scenario that limits the biomass supply
(‘‘LimitedBECCS’’ with annual biofuel potential only one-fourth
of the base scenario) and find that the system cost increases
slightly in 2060 (+2%) for the 50 MtCO 2 deployment of DACCS

�
to compensate for the installation gap ( 40 GW) of BECCS (see
Fig. 3E and F). We further consider an extreme scenario that
excludes BECCS (‘‘WithoutBECCS’’ scenario). Results show that
about 228 MtCO 2 per year and 779 MtCO 2 per year of DACCS
are necessary in 2050 and 2060, respectively. Based on the cost
projections from Young et al., [44] we find carbon-capture figures
of 228 MtCO 2 per year with KOH-Ca looping and 551 MtCO 2 per
year with MgO ambient weathering for 2060 (Fig. 3F). These
DACCS installations increase the system costs by 3.6% in 2050
and 5.3% in 2060, as shown in Fig. 2.
We employ the marginal abatement cost of CO 2 emissions
(MAC, yuan per tCO 2 ) as the primary metric for assessing the
cost of achieving different emissions targets for China’s power
system. The MAC, or the shadow price of the annual carbon
emissions constraint, represents the additional cost required to
abate one extra unit of carbon emissions. In the base scenario,
the MAC shows a significant surge between 2040 and 2050,
rising by approximately 350–450 yuan per tCO 2 from 206 yuan
per tCO 2 in 2040 to 622 yuan per tCO 2 in 2050. In contrast, the
increase is 150–200 yuan per tCO 2 between 2030 and 2040, and
30–80 yuan per tCO 2 between 2050 and 2060 (see Fig. 3D).
Before 2050, abating carbon emissions is primarily achieved
through increasing carbon-free generation to substitute for the
corresponding thermal power generation. From 2050 onwards,
attaining net-zero or negative emissions necessitates investment in NETs, resulting in a substantial increase in the MAC. In
the ‘‘WithoutBECCS’’ scenario, the MAC increases by 80% to
1120 yuan per tCO 2 in 2050 compared to the base scenario, and
43% to 1008 yuan per tCO 2 in 2060 for deploying DACCS as a

NET. When BECCS is modeled with a restricted biofuel avail
ability (‘‘LimitedBECCS’’), the MACs before 2050 are close to
the base scenario. However, this value also increases by 43%

due to additional DACCS installations in 2060.

Fig. 3A presents the optimal transportation routes for injecting the captured 497 and 853 MtCO 2 into sequestration sites in
2050 and 2060, respectively, in the base scenario. The captured
CO 2 is primarily transported to and stored in the Songliao
Basin, Bohai Bay Basin, Jianghan Basin, and Sichuan Basin due
to their abundant surrounding biofuel resources and DSA
sequestration potential. These carbon sinks collectively accommodate 45% and 58% of the CO 2 captured in 2050 and 2060.
Within the CISPO model, the electricity consumed by direct air



3704 | _Energy Environ. Sci._, 2025, 18, 3699–3717 This journal is © The Royal Society of Chemistry 2025


**[View Article Online](https://doi.org/10.1039/d5ee00355e)**


Energy & Environmental Science Paper



carbon capture for operations (e.g., heating) is assumed to be
sourced from the provincial grid. Unlike BECCS, the deployment and operation of DACCS are not constrained by fuel
resource availability, but are more cost-effective in regions with
lower electricity generation costs. In the absence of BECCS as a
NET, our findings indicate that DACCS predominantly occurs
in Mengxi, Ningxia, Xinjiang, and Liaoning, with captured CO 2
transported to nearby sequestration sites (Fig. 3B). Results for
other scenarios are presented in the ESI,† Fig. S52–S59. We
find that Heilongjiang (�120 MtCO 2 per year), Hebei
(�90 MtCO 2 per year), Shaanxi (�75 MtCO 2 per year), and
Sichuan (�71 MtCO 2 per year) contribute the most significant
negative carbon emissions from the electricity sector in 2060
(ESI,† Fig. S51). While the CISPO model allows for retrofitting
conventional thermal power plants with carbon capture equipment, the results demonstrate that the retrofitted capacity is
minimal (o1%). This limited retrofitting is primarily attributed
to the positive residual emissions associated with this technology (90% capture rate [45] ), which result in a lower annual
capacity factor and consequently higher abatement costs compared to BECCS.


2.3 Spatial siting and land-use requirement of VRE


Cell-level information of VRE sources incorporated into the
CISPO model includes the installation capacity potential and
hourly available output over a year, which are related to suitable
area and meteorological factors (the year 2019 is used in the
base scenario), for the spatial modeling (0.251 � 0.251) of
optimal capacity deployment, electricity supply, and integration
with load centers. [4,23] In this study, we first consider different
land-use types, available rooftop areas, and technical limitations to estimate the suitable areas (km [2] ) for VRE
deployment [4,16] and then assess the installation capacity
potential using density (MW km [�][2] ) for each VRE type. Integration of wind (onshore and offshore) and utility-scale solar PV is
modeled by first connecting to a substation and then to a load

center based on the minimum connection distance rule following Brown and Botterud, [23] and Zhang et al. [4] See the ESI,†

Sections S2.1 and S3.3 for details. Within the context of this

optimization technique, we observe distinct expansion patterns
for wind and solar PV installations from 2030 to 2060, and
higher capacity of distributed solar PV than that of utility-scale
solar PV in 2030, as shown in Fig. 4A and B. See the ESI,† Fig.
S62–S64, for VRE’s spatial expansion in detail.
Wind power installations are rapidly expanding across
regions rich in wind resources, which roughly exhibit two
nearly parallel belts. The first belt, located in North, Northeast,
and Northwest China, possesses substantial potential for
onshore wind, accounting for approximately 75% of the
national wind installed capacity and electricity generation
(see Fig. 4C and D). The highest wind installation factor,
defined as the ratio of planned capacity to the installation
capacity potential, is found in Mengdong and Mengxi for 2060,
with a value of 0.41 (see Fig. 4E). In other provinces of these
regions (North, Northeast, and Northwest China), installation
factors range between 0.2 and 0.3, indicating significant



untapped wind potential. The second belt, situated along the
eastern and southern coastal areas including Jiangsu, Shanghai, Zhejiang, Fujian, and Guangdong, boasts the highest offshore wind-generation potential within China’s exclusive
economic zone (EEZ). The total installed capacity and electricity
generation from wind power in East and South China account
for approximately 20% of the national total, primarily driven by
offshore wind. It is worth noting that the eastern coastal
provinces use approximately 50% of their total potential capacity, with Shanghai at 55%, Zhejiang at 55%, and Fujian

at 49%.

Solar PV installations are gradually expanding outward from
urban load centers (see definition in the ESI,† Section S3.3.2)
due to land availability constraints and an overall lower capacity factor, resulting in greater sensitivity to integration distances (spur and trunk lines, see the ESI,† Fig. S69). From 2030
to 2060, 80% of the cumulative installed capacity extends from
within 60 km of load centers to within approximately 75 km
(see the ESI,† Fig. S70). The primary regions for solar PV
deployment are North, East, and South China, which together
account for 60% of the nation’s installed capacity and electricity generation. By 2060, stricter emissions constraints will lead
provinces with limited suitable land and high electricity
demand to nearly deplete their solar PV potential. For instance,
solar PV installations will exhaust over 80% of the potential
capacity in Shanghai (99%), Guangdong (92%), Zhejiang (89%),
Beijing (88%), Tianjin (84%), and Jiangsu (83%), as illustrated
in Fig. 4E. After 2050, significant growth occurs in large-scale
solar PV bases in Mengxi and Northwest China, as shown in
Fig. 4B, with more concentrated installations and higher
installed capacity per grid cell. This expansion leverages the
abundant unused land in these regions suitable for utility-scale
solar PV development. Xizang, which possesses the richest solar
irradiance across China but is far from demand centers, sees
only 113 GW of installed capacity and 218 TW h per year of
electricity generation from solar PV installation in 2060.
We developed an XGBoost model to estimate the rooftop
area potential [46,47] and adopt the optimal fixed-tilt photovoltaic
model [16] to assess the installation capacity potential for distributed solar PV (DPV) systems in each grid cell (see the ESI,†
Section S2.1.5 for details). In the CISPO model, distributed
solar PV systems are analyzed assuming zero transmission
costs for local integration and higher investment costs than
utility-scale solar PV (UPV) systems for rooftop rentals. [4] By
2030, DPV installations are projected to reach 903 GW, an
increase of approximately 750 GW from 2022, generating
1.15 PW h per year, and UPV is expected to reach 885 GW,
producing 1.27 PW h per year. These installations will occupy
more than 10 000 km [2] of rooftop area for DPV and 11 000 km [2]

of land area for UPV, approximately 3–4 times the land area of
Shanghai city (about 6300 km [2] ), as shown in Fig. 5. Post-2030,
DPV installations will have a mild increase, reaching 930 GW by
2040 and 1008 GW by 2050. However, by 2060, the capacity of
DPV is projected to decline significantly to 489 GW, reducing
the occupied rooftop area by 50% to 5387 km [2] (see the ESI,†
Fig. S63, for the spatial distribution of DPV from 2030 to 2060).



This journal is © The Royal Society of Chemistry 2025 _Energy Environ. Sci._, 2025, 18, 3699–3717 | 3705


**[View Article Online](https://doi.org/10.1039/d5ee00355e)**


Paper Energy & Environmental Science

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-7-0.png)


Fig. 4 Optimized deployment of VRE to 2060. (A and B) Capacity (GW, new + existing) of wind (onshore + offshore) and solar PV (utility-scale +
distributed) in each cell. (C and D) Annual power generation (PW h per year) and installed capacity (GW) of wind (left) and solar PV (right) in each grid
region. (E) Provincial installation factor (0–1), defined as the ratio of planned capacity to the installation capacity potential, from 2030 to 2060.


3706 | _Energy Environ. Sci._, 2025, 18, 3699–3717 This journal is © The Royal Society of Chemistry 2025


**[View Article Online](https://doi.org/10.1039/d5ee00355e)**


Energy & Environmental Science Paper

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-8-0.png)


Fig. 5 Installation capacity potential and optimized results for utility-scale and distributed solar PV systems. Cell-level installation capacity (MW)
potential (A1 and A2), provincial installation area (km [2] ) in 2030 (B1and B2), and 2060 (C1 and C2) for UPV (upper) and DPV (lower) systems. Annual
generation (D) and installed capacity (E) for UPV (left) and DPV (right) systems from 2030 to 2060.



This notable decrease is primarily attributed to the retirement
of DPV installations in 2030, the adoption of other renewable
energy sources (e.g., offshore wind in Jiangsu, Shanghai, and
Zhejiang), and the long-distance transmission delivering power
from the west and north to the east and south, which are more
competitive under our cost assumptions in 2060. Instead, UPV
installations are expected to quadruple compared to the 2030
level, reaching 3359 GW and occupying 43 914 km [2] of land area
by 2060.
Land-use tension between energy generation and other
sectors (e.g., agriculture) is a critical challenge for the successful deployment of TW-scale VRE. This study evaluates the
installation capacity potential of wind and solar PV systems
across three land-use scenarios: open, base, and conservative.
These scenarios reflect varying levels of land-use restrictions,
with suitability factors for each land-use type assigned to
prioritize government objectives. A detailed assessment of
VRE potential is provided in the ESI,† Section S2.1. Under the
base land-use scenario, the estimated installation capacity
potentials are approximately 14 000 GW for onshore wind,
1500 GW for offshore wind, 46 000 GW for utility-scale solar



PV, and 1500 GW for distributed solar PV. Expanding suitable
land availability under the open scenario increases potential by
21%, 74%, 41%, and 6.7% for onshore wind, offshore wind,
utility-scale solar PV, and distributed solar PV, respectively,
reaching 17 000 GW, 2700 GW, 65 000 GW, and 1600 GW.
Conversely, adopting more conservative assumptions reduces
potential by 39%, 33%, 41%, and 16.7%, respectively, yielding
8600 GW, 1000 GW, 27 000 GW, and 1250 GW.
System impacts of the available land area are assessed by
incorporating the installation capacity potential under different
land-use scenarios (‘‘VRELandOpen’’ and ‘‘VRELandConservative’’) as upper deployment limits for each VRE grid cell within
model constraint S4-2 (see ESI,† Section S4.3.1). From 2030 to
2060, power system costs decrease (increase) by 0.1–2.5%
(1.6–3.3%) under the open (conservative) land-use assumptions
for VRE, as shown in Fig. 2. At the national level, the total
installed capacity of wind and solar power (including CSP)

exhibits minor variations across the land-use scenarios. For

instance, in 2060, the base scenario features 6132 GW of
installed capacity, while the ‘‘VRELandOpen’’ and ‘‘VRELand
Conservative’’ scenarios result in 5768 GW and 5780 GW of



This journal is © The Royal Society of Chemistry 2025 _Energy Environ. Sci._, 2025, 18, 3699–3717 | 3707


**[View Article Online](https://doi.org/10.1039/d5ee00355e)**


Paper Energy & Environmental Science



installed capacities, respectively. Under more restricted landuse assumptions, distributed solar PV and CSP installed capacities increase twofold from the base scenario, reaching 1044
GW and 322 GW in 2060, respectively, highlighting the
potential of these technologies to mitigate land-use pressures.


2.4 Supporting role of energy storage and inter-regional

transmission


Fig. 6 shows the hourly energy stored in PHS and BAT storage
systems over 8760 hours at the national level, representing the
aggregated values across all regions. In this figure, the horizontal axis represents each day of the year, and the vertical axis
denotes the hours from 0 to 23 for each day. The color shades
indicate the energy stored at the national level, with darker
shades representing higher values. Generally, these energy
storage systems’ charging/discharging patterns exhibit a strong
correlation with the temporal characteristics of solar PV generation. Specifically, electricity is stored during peak PV generation periods (typically 12:00–18:00 with gradually deepening
color) and discharged primarily during night hours (as the
color gradually lightens) (see Fig. 6 and the national power
dispatch profiles in the ESI,† Fig. S81). PHS and BAT exhibit



distinct operational patterns as the proportion of integrated
VRE varies. PHS exhibits a more significant role in long-term
regulation in 2030 compared to 2060, as evidenced by sustained
multiple-day charging periods (color intensifies and remains)
followed by multiple-day discharging periods (color gradually
fades), for example, from days 210 to 230 in Fig. 6. This
difference is attributable to the higher VRE penetration in
2060, which increases the frequency of energy storage cycles.
BATs, because of their shorter discharge duration (typically
4 hours), primarily serve a rapid balancing function. However,
when the installed capacity exceeds 2000 GW h in 2060, BAT
storage can also provide longer-span balancing to some extent,
as depicted in Fig. 6 between days 270 and 300.
Inter-regional ultra-high voltage (UHV) transmission lines
are essential for addressing the resource–demand mismatch in
China. [2–4] The CISPO model optimizes the construction and
reinforcement of UHV lines with voltages larger than or equal
to 500 kV between any pair of provinces, as detailed in the ESI,†
Section S3.7. Our findings indicate that by 2060, the nationwide
capacity of inter-regional transmission lines needs to triple from
the 2022 level, reaching approximately 2.0 TW. Fig. 7 shows the
annual inter-provincial electricity flows (TW h per year) along these



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-9-0.png)

Fig. 6 Results of the total energy stored (GW h) in PHS and BAT storage systems at the national level for each hour in 2030 and 2060.


3708 | _Energy Environ. Sci._, 2025, 18, 3699–3717 This journal is © The Royal Society of Chemistry 2025


**[View Article Online](https://doi.org/10.1039/d5ee00355e)**


Energy & Environmental Science Paper

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-10-0.png)


Fig. 7 Results of yearly energy transmitted (TW h per year) through inter-regional transmission from 2030 to 2060. The left axis is the power output
province.



transmission lines, with the left vertical axis representing the
exporting end. The results demonstrate that the inter-regional
transmission lines serve two primary functions. First, they enable
the transfer of wind and solar power from resource-rich regions to
areas with high electricity demand, such as the transmission of
multi-petawatt hours of electricity from Mengxi and Mengdong to
provinces like Jiangsu and Anhui, while having minimal reverse
flows (see the net transmission flow (TW h per year) maps in the
ESI,† Fig. S68). Second, they play a balancing role in regions with
high electricity demand, such as East, Central, and South China, as
evidenced by bidirectional power flows along lines like Anhui–
Jiangsu, Anhui–Jiangxi, Chongqing–Hubei, and Guangdong–

Yunnan.


2.5 System volatility and capacity payment mechanism


In a competitive setting, the market clearing price would equal
the marginal cost (MC) of generation, when plants earn returns
for supplying electricity. [48] Under the linear optimization
scheme, the hourly MCs of generation for each province are
derived from the shadow prices of the power demand balance
constraints. With the increasing integration of VRE sources
from 2030 to 2060, the system MCs exhibit an overall trend of
decreasing prices accompanied by heightened variability,
owing to the zero marginal cost and intermittent nature of
VRE generation, as shown in Fig. 8. In 2030, most MCs fall in
the range of 0.2–0.4 yuan per kW h, with a small proportion in a
comparably lower interval from 0.0 to 0.1 yuan per kW h. By
2040, a greater MC share is found in the range of 0.0–0.1 yuan
per kW h, while the highest MC reaches approximately 1.0 yuan
per kW h. Beyond 2050, we see an increasing number of low
MCs and instances of extremely high MCs, ranging from several
to tens of yuan per kW h. Statistically, the mean and standard
deviation of the hourly MCs in 2030 are 0.28 and 0.09, respectively, shifting to 0.31 and 0.6 by 2060. These results indicate
that the power system volatility becomes increasingly pronounced for large-scale VRE deployment (see the hourly MC
distribution of other scenarios in the ESI,† Fig. S71–S80).
Firm resources (e.g., thermal and nuclear power) are the key
electricity generation systems that support a more stable power



system when large-scale weather-dependent energy is integrated. Our results show that the coal power plant is the main
supplemental capacity to enhance the system’s stability when
the MCs are high, indicating inadequate capacity supply in the
power system. For example, we observe peak MC periods for
insufficient renewable energy supply (particularly in winter
when hydro discharge is limited) and peak power demand (in
summer) in 2050 and 2060 (Fig. 8), at which point coal power
fills the capacity gaps (see the national hourly power supplydemand profile in the ESI,† Fig. S81).
China implemented a capacity payment mechanism to
compensate coal power plants [49] in 2022, as a response to the
decreasing revenue from the electricity market and increasing
expenditure of more frequent shut-downs, start-ups, and ramping. In this study, the net profit of power generators is determined by first computing annual revenue as the cumulative
sum of hourly electricity generation (kW h) multiplied by the
system marginal generation cost (yuan per kW h), then subtracting annualized capital expenditures and operational
expenditures. When the net profit is negative, a capacity payment is required to maintain market viability. In 2030, 25 out of
32 provinces need the capacity payment for coal plants (161–
426 yuan per kW year), with most provinces exhibiting values
between 300 and 400 yuan per kW year (see Fig. 9A). From 2030
to 2050, most provinces will see a slight decrease in coal
capacity prices compared to those in 2030, while several provinces experience increases, e.g., Yunnan and Hebei.
Similar to the firm generators, energy storage systems (ESSs)
can also help balance the electricity supply-demand in a highpenetration VRE power system [38] and provide essential services,
such as inertia support [3] and spinning reserves. [2–4,28] Our results
show that while approximately 5800 GW h of energy capacity is
recommended to be deployed in 2060, 80% of the ESSs have
negative profits if there is only revenue from arbitrage in the
electricity market. In 2030, among the nearly 2000 GW h of ESS
capacity, PHS is projected to incur losses ranging from 250 to
400 yuan per kW year in most provinces. Battery storage losses
in 2030 are expected to range between 200 and 300 yuan per kW
year. From 2040 onwards, the losses associated with ESSs are



This journal is © The Royal Society of Chemistry 2025 _Energy Environ. Sci._, 2025, 18, 3699–3717 | 3709


**[View Article Online](https://doi.org/10.1039/d5ee00355e)**


Paper Energy & Environmental Science

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-11-0.png)


Fig. 8 Results of the hourly provincial marginal cost of generation (MC, yuan per kW h) from 2030 to 2060. We divide the MC into eight intervals (0.0–
0.1, 0.1–0.2, . . ., 0.7–0.8), and count the number of provinces located in each interval for 8760 hours. MCs higher than 0.8 yuan per kW h are also

counted in the 0.7–0.8 interval.



found to narrow, primarily benefiting from the larger MC
ranges and arbitrage opportunities. PHS is expected to exhibit
superior profitability, with losses decreasing to around 120
yuan per kW year (about �52 to �70%) in 2050 and 50 yuan
per kW year (about �80 to �87.5%) in 2060 in most provinces.
By 2060, we observe that pumped hydro storage may earn
positive profits in provinces such as Ningxia and Qinghai (see
Fig. 9B). For battery storage, losses could decrease from around
150 yuan per kW year in 2040 to approximately 50 yuan per kW
year in 2060, but positive profitability remains elusive. This
finding hints that energy storage technologies with lower
energy costs and higher duration hours might be relatively
more competitive in the future power system landscape.


2.6 Sensitivity of cost and electricity demand projection


We investigate the impact of slower investment cost reductions
for key technologies – wind and solar power, PHS, BAT, and
BECCS – on the transition of China’s power system. In the
‘‘Capex1.25X’’ scenario, the capital expenditures and associated
fixed operation and maintenance (O&M) costs of these technologies are increased by 25% compared to the base scenario,



reflecting potential variations in technological advancements
across key energy sectors. System costs increase by around 10%
(approximately 500 billion yuan per year) in 2060 under these
higher cost assumptions (Fig. 2). In the base scenario, annual
electricity demand is projected to rise from 12.5 PW h per year
in 2030 to 16.0 PW h per year in 2060, aligned with forecasts
from economy-wide models [6,35] and expert assessments. [13,14] To
assess the sensitivity of higher electricity demand, we introduce
the ‘‘Demand1.25X’’ scenario, where annual demand is 1.25
times that of the base scenario, increasing from 15.6 PW h per
year in 2030 to 20.0 PW h per year in 2060, accommodating
potential growth in carbon-free electricity applications such as
power-to-gas. Under these demand assumptions, the total
installed capacity of wind and solar PV energy expands significantly, reaching 7853 GW (+31% compared to the base scenario) in 2060. Concentrating solar power causes a threefold
increase compared to the base scenario, with the installed
capacity reaching 380 GW and annual generation reaching
1.0 PW h per year by 2060. Despite the substantial increase in
annual demand, the system cost of electricity remains comparable to the base scenario (Fig. 2).



3710 | _Energy Environ. Sci._, 2025, 18, 3699–3717 This journal is © The Royal Society of Chemistry 2025


**[View Article Online](https://doi.org/10.1039/d5ee00355e)**


Energy & Environmental Science Paper

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-12-0.png)


Fig. 9 Results of capacity payment (yuan per kW year) to coal power (A), and net profit (yuan per kW year) for PHS and BAT storage (B) from 2030 to

2060.



2.7 Sensitivity of PSEM to spatiotemporal resolution


The increasing penetration of variable renewable energy in
power systems requires higher spatial resolution and broader
temporal coverage for renewable energy and energy storage
deployment decisions in PSEMs. [4,22,23,25] To illustrate this fact,
we introduce variants of the CISPO model by employing traditional modeling approaches to examine the impacts of spatial
and temporal resolution in 2060, aggregating renewable
resource profiles into a limited number of clusters per load
zone or optimizing across representative time slices. [2,3,50–52] The
objective is to demonstrate biases arising from simulation
granularity. First, the ‘‘VRECluster’’ scenario integrates each
0.251 � 0.251 grid cell within provinces into resource clusters
stratified by VRE types, hourly capacity factors, and proximity to
load centers (detailed methodology available in the ESI,† Section S3.3.3). Second, for different temporal coverages, we
designed a total of 22 distinct temporal coverage scenarios,
with the number of hours varying from 288 to 4320. The
different ‘‘period’’ intervals are designed to account for representativeness across months or seasons. Scenario design and
temporal coverage details are provided in the ESI,† Section S6
and Fig. S47. Significant biases emerge for VRE installations in
scenarios with lower spatiotemporal modeling resolution.
Capacity deviations range from �28.0% to ****% for onshore
wind, �36.8% to +46.6% for offshore wind, �8% to +17% for
utility-scale solar PV, and �64% to +76% for distributed solar
PV in 2060 (Fig. 10A and B). Compared to cell-level optimization, the clustered VRE scenario concentrates development in



areas with higher resource potential, while some are very far
from load centers (see the ESI,† Fig. S65). By omitting the
integration costs within a cluster, the ‘‘VRECluster’’ scenario
underestimates system costs by 9% in 2060.
For scenarios with temporal coverage limitations, the optimal deployment of energy storage systems, which are highly
sensitive to chronological strategies for balancing weatherdependent renewable energies, is particularly biased. Discontinuous temporal representations – characterized by skipping
months or seasons in representative hours – disrupt the continuity of actual weather conditions, and consequently misestimate the role of energy storage technologies with varying
temporal durations. For instance, solar PV output exhibits
substantial variability between seasons, such as spring and
summer, [30,53] so models may overestimate battery capacity
needs for balancing solar PV output when inappropriately
concatenating temporally distant typical days. The analysis of
different temporal coverage scenarios reveals significant overestimation in battery storage deployment at the national level:
installed capacities are higher in all of these scenarios, with the
largest overestimate up to +50% in 2060, compared to the base

scenario.

### 3 Discussion


In this study, we developed a temporally and spatially resolved
optimization model to explore the transition pathway of China’s power system toward negative carbon emissions. Our



This journal is © The Royal Society of Chemistry 2025 _Energy Environ. Sci._, 2025, 18, 3699–3717 | 3711


**[View Article Online](https://doi.org/10.1039/d5ee00355e)**


Paper Energy & Environmental Science

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/zhuIntegratedModelingTransition2025/zhuIntegratedModelingTransition2025.pdf-13-0.png)


Fig. 10 Sensitivity analysis of the PSEM for results in 2060 under 23 different spatiotemporal resolution scenarios. (A) Capacity (GW) of onshore wind
(left) and offshore wind (right). (B) Capacity (GW) of utility-scale (left) and distributed (right) solar PV. (C) Capacity (GW) of pumped hydro storage (PHS,
left) and battery storage (BAT, right).



results show strategies of the electricity sector to attain negative
200–700 MtCO 2 per year by 2060, which entails a large-scale
deployment of approximately 6000 GW of wind and solar
power, 5800 GW h of energy storage, 2000 GW of interregional transmission, and 850 MtCO 2 per year of carbon
capture and sequestration. These findings have several policy
implications for both near- and long-term planning. First,
distributed solar PV power generation is projected to experience substantial installation growth, reaching 930 GW by 2030.
However, an effective integration guarantee or power compensation mechanism needs to be designed and implemented to
encourage integrating generation from distributed solar PV
power generation because of the high penetration of VRE
presenting significant challenges for the grid. [54,55]

Second, our results indicate that the pathway of China’s
power system toward negative emissions can be divided into
two distinct phases. The first phase, spanning from the present
(2022) to around 2045, is characterized by sustained and largescale deployment of carbon-free energy sources to substitute
fossil fuels. The second phase, from 2045 to 2060, necessitates
the implementation of BECCS or DACCS at a scale of 100–200
GW or 500–1000 MtCO 2 per year to attain net-zero and ultimately negative emissions. Although BECCS is more costeffective than DACCS as a NET in China’s power system, the
potential barriers, such as land-use availability, water resource
constraints, and public acceptance, [42,56] may limit the deployment of BECCS. For this consideration, DACCS is another
possible option for negative emissions in areas where deploying
BECCS is not feasible. Additionally, while the model suggests



that deploying CCS technology in traditional thermal power
plants is not immediately necessary, the large-scale deployment
of BECCS or DACCS requires technology maturity and cost
reduction in the related carbon capture and sequestration
industry. Therefore, it is essential to formulate planning for
piloting carbon capture in thermal power units, transportation,
and sequestration to foster technological advancement and

readiness.

Third, our results demonstrate that carbon prices and
complementary policy instruments would be crucial to promote
the low-carbon transition of the power system. China’s national
emissions trading scheme (ETS) was launched in 2021 with a
current carbon price level of around 100 yuan per tCO 2 . With a
decreasing supply of allowances and increasing share of allowances being auctioned, the ETS will introduce a growing carbon
price, [57] which is expected to increase the cost of fossil fuel
generation and enhance the competitiveness of renewable
energy, abated fossil fuel generation (coal or gas power plants
with CCS), and ultimately, BECCS in the wholesale power
market. At the same time, our modeling analyses point out
that revenue from the wholesale market cannot always ensure
the cost recovery for (abated) coal power plants and battery
storage, prompting the need for introducing capacity mechanisms or subsidies to battery storage. Therefore, policymakers
should also be aware of these important interactions and
coordinate the design of complementary policy instruments
effectively. Modeling tools like the CISPO model will help
policymakers in China and beyond analyze these interactions
quantitatively.



3712 | _Energy Environ. Sci._, 2025, 18, 3699–3717 This journal is © The Royal Society of Chemistry 2025


**[View Article Online](https://doi.org/10.1039/d5ee00355e)**


Energy & Environmental Science Paper



This study has several limitations, though we believe they do
not alter the main findings. First, technologies not explicitly
modeled include geothermal power, floating offshore
solar PV, and offshore carbon sequestration. Second, the model
employed here aims to simulate long chronological
x(8760 hours) variability from renewable energy sources and
power demand at the hourly resolution. Non-linearities arising
from unit commitment, optimal power flow, and other factors
are modeled using simplified approaches that have been validated with negligible loss of fidelity in previous
work. [2,3,23,29,51,58,59] Third, analyses of additional environmental
impacts, such as land-use changes and water consumption, as well
as other important socioeconomic factors, would enable a more
comprehensive perspective on the multifaceted challenges and
trade-offs inherent in the transition to a net-zero society.
In summary, the CISPO model developed in this study
provides detailed representations of power system expansion
and operations, offering valuable insights into pathways for
decarbonizing China’s power sector to mitigate global climate
change. The integrated modeling approach, combining high
spatial and temporal resolution with a diverse portfolio of
technologies, is highly adaptable and can be readily applied
to other regions exploring possible strategies to transition from
carbon-intensive [28] to sustainable energy systems. First, the
imbalance between renewable energy resource availability and
power demand is a global challenge, [21] observed in regions such
as the United States, India, Europe, and South America. This
underscores the critical need for integrated transition analyses
that encompass variable renewable energy, hydropower, biomass energy, conventional thermal power, negative emissions
technologies, energy storage, and long-distance transmission.
Second, the comprehensive assessment of energy-related
resources leverages widely utilized open-source datasets,
including elevation, slope, water discharge, and reanalysis data
of meteorological factors, providing global coverage. The methodologies employed in this study can be easily expanded to
other countries using these readily available datasets. Third,
the high spatiotemporal resolution power system model incorporates a flexible framework that can be readily adapted to
other regions or even the global scale. This adaptation can be
achieved through the use of localized parameters, such as
investment costs, existing power infrastructure, and regionspecific power demand profiles. Looking ahead, the model
can be expanded to incorporate analyses of geospatial constraints, such as trade-offs between land use and renewable
energy deployment, impacts of long-chronological renewable
energy supply uncertainties (e.g., extreme weather) on power
system operation, and involving global renewable energy supply
chain and critical materials challenges.

### 4 Materials and methods


4.1 Energy resource assessment


We assess the resources of wind (onshore and offshore) and
solar (utility-scale PV, distributed PV, and CSP) power by hourly



capacity factor (CF, 0–1) and installation capacity potential
(MW). The meteorological data including wind speed (10 m,
and 100 m), surface short-wave radiation, temperature, and so
on, are derived from the European Centre for Medium-Range
Weather Forecasts Reanalysis Version 5 (ERA5) dataset, which
provides a record of global climate and weather spanning over
eight decades with a spatial resolution of 0.251 � 0.251 and
hourly temporal resolution. [60] With these high-resolution
meteorological data, we adopt the wind turbine model parameters from the National Renewable Energy Laboratory
(NREL) [61] and the generation model widely used [20,23,62–64] to
calculate the hourly CF of wind and solar power in each grid
cell, respectively. The maximum installation capacity potential
of onshore wind, offshore wind, utility-scale solar PV, and CSP
systems is estimated first with the suitable area constrained by
natural/biodiversity reserves, technical conditions, and landuse type. [4] We collect as much vectorized rooftop data as
possible from around the world, [65] which is recognized by
satellite images and computer vision algorithms, and then
train an XGBoost regression model using population, road
length, night light, and built-up area as independent variables,
and rooftop area as dependent variables, in cells with fine
rooftop data to predict those cells without reliable vectorized
rooftop data. [46,47] The suitable area in each cell to deploy
distributed solar PV power is determined as a fraction of the
rooftop area. Within the suitable area (km [2] ) in each cell, the
installation capacity potential is derived by multiplying the
installation density (MW km [�][2] ) with assumptions for wind
power using 7 � 7 rotor diameters spacing, [29,66] and for solar
PV power using the fixed-tilt model. [16] See the ESI,† Section
S2.1, for the detailed methodology, dataset, and results.
Other energy-related resources are also assessed in this
study, including hydropower installation and output potential,
available biofuel resources, and carbon sequestration limitations. We assess the installation capacity potential of hydropower in China through an analysis of the least levelized cost of
energy (LCOE), taking into account factors including the inundated area, environmental conservation, population displacement, and integration distance, as documented in previous
studies. [67,68] Under this assessment framework, the optimal
dam height (m), reservoir capacity (m [3] ), and installation capacity potential (MW) are determined at the least LCOE, which are
key inputs to model hydropower deployment. With this installation capacity potential, we use the discharge data (m [3] s [�][1] ) of a
3-hour temporal resolution in 2019 [69] to simulate the generation of run-of-river and reservoir hydropower, in which the
hourly discharge is assumed to be the same in each 3-hour
interval. See the ESI,† Section S2.2, for details on hydropower
potential assessment. We use the agricultural residues, forest
residues, and energy crops on abandoned cropland with quantified potential in TJ per year for each 1 km � 1 km grid cell
from Wang et al. [43] to aggregate these potentials at the provincial level and then estimate the installation capacity potential
using assumptions for thermal efficiency at 0.35 [70] and annual
equivalent hours at 6132 hours. The installation capacity
potential of biomass under this estimate is about 470 GW



This journal is © The Royal Society of Chemistry 2025 _Energy Environ. Sci._, 2025, 18, 3699–3717 | 3713


**[View Article Online](https://doi.org/10.1039/d5ee00355e)**


Paper Energy & Environmental Science



(see the ESI,† Section S2.3, for details on biofuel resource
distribution). In this study, we primarily focus on onshore deep
saline aquifers (DSA) as a preferred option for carbon storage
due to their substantial capacity within China. [26] The World
Geologic Provinces dataset with further adjustment based on
the findings of Wei et al. [26] and Wang et al. [15] is used to estimate
the surface area encompassing all sedimentary basins. Nationally, we find approximately 120 Gt CO 2 storage potential of
onshore deep saline aquifers. See the ESI,† Section S2.4, for the

estimate formula and results.


4.2 Integrated sustainable power system optimization model


The CISPO model developed here minimizes the sum of the
annualized capital cost of investments and operational costs
over a full year (8760 hours) of hourly operation using 2019
meteorological data, subject to meeting hourly electricity
demand and a variety of engineering, economic, and policyrelated constraints in a planning year. Technologies optimized
in the CISPO model include wind (onshore and offshore), solar
(utility-scale PV, distributed PV, and CSP), hydropower (run-ofriver and reservoir), firm generator (coal, gas, nuclear, and
bioenergy), energy storage (BAT and PHS), CCS, direct air
capture, and transmission (intra- and inter-region). In each
planning year, the CISPO model outputs these technologies’
optimized deployment and hourly operational status. The
spatial installation resolutions for VRE, hydropower, and the
remaining generators are grid cell (0.251 � 0.251), dam site, and
province, respectively. The information transferred between
different planning years (e.g., from 2050 to 2060) covers newly
built and retired generation capacities. We model the hourly
operation considering available VRE output, reservoir capacity
and output of hydropower, unit-commitment of thermal and
nuclear power, charge/discharge profiles of energy storage,
spinning reserve, inertia requirement, and power transmission
flow through the UHV lines. Within the high spatial and
temporal resolution for capacity expansion and power system
operation, the CISPO’s scale comes to about 4 � 10 [7] rows, 3 �
10 [7] columns, and 5 � 10 [8] non-zero elements after pre-solve. See
model comparisons of the CISPO with previous PSEMs in the
ESI,† Section S1.
The grid integration distance and capacity of renewable
energies include spur and trunk lines, which are associated
with the decision variables of deployment following the methodology used in ref. 4 and 23. We collect the location information of substations with voltage levels larger than 220 kV from
Open Street Map (OSM) [71] as the spur line terminates. Major
load centers are recognized as urban areas under 1 : 50 M
zooming recognition level, [72] and the additional load center in
Xizang is added from the 1 : 10 M level to guarantee that there is
at least one major node designated in Xizang. Within the
location information of renewable energies, substations, and
load centers, we adopt a minimize-integration-distance algorithm to determine the connecting pair. See the ESI,† Section
S3.3.2, for detailed results. We endogenously model the optimization matching of carbon capture and sequestration and
allow each province to transport the captured CO 2 to any



sequestration sites, with the transportation distance estimated
using the geographical distance between province centers and
sequestration locations. See the ESI,† Section S3.8, for the unit
capture, transportation, and injection costs. The relaxed unit
commitment algorithm for thermal and nuclear dispatch is
adopted in the CISPO model as previous studies have proved
this approach runs with much higher solving speed and
negligible loss (o1%). [2,58] Similarly, we use the transportation
(or pipeline) model to optimize the operation of inter-regional
transmission which is validated [59] and used in many other
PSEMs. [2–4,23,28,51,52] Detailed model formulas are shown in the

ESI,† Section S4.


4.3 Input data and assumptions


The CISPO model covers 32 provincial load areas of mainland
China, with Inner Mongolia divided into west (Mengxi) and east
(Mengdong) regions because they belong to different power
grids. These load areas are modeled for demand balance, interregional transmission flow, and policy requirements. See the
ESI,† Table S1 and Fig. S2, for detailed region divisions.
In this study, we take 2022 as the base year and optimize
China’s power system from 2030 to 2060 (10 years as a step).
The installed capacities of coal, gas, nuclear, wind, and solar
power by 2022 are derived from the Global Energy Monitor. [73]

This dataset reports the unit-level information of installed
capacity, including operation status (operational or under construction), remaining lifetime, and locations. By 2022, we find
that the total operational capacity is 1108 GW for coal power
(including combined heat and power plant), 108 GW for gas
power, 56 GW for nuclear power, 278 GW for onshore wind
power, 30 GW for offshore wind power, and 228 GW for utilityscale solar PV power. We have collected a total of approximately
360 GW of installed hydropower (run-of-river and reservoir) at
the dam site level from Wan et al., [74] 46 GW of installed pumped
hydro storage capacity from the National Energy Administration (NEA), [39] 156 GW of installed distributed solar PV power
from NEA, [75] and 41 GW of installed biomass power from China
Electric Power Planning & Engineering Institute. [76] Underconstruction coal, gas, nuclear, and PHS projects are assumed
to be in operation by the year 2030. In each planning year (i.e.,
2030, 2040, 2050, and 2060), the installed capacity of coal and
gas power will be retired if reaching the designed lifetime, or
retrofitted to be equipped with carbon capture equipment if
needed. We assume that the installed wind power, solar power,
hydropower (including PHS), and biomass power will be
renewed if retired. See the ESI,† Section S3.3, for details of
the generator installations, and the capacity allocation to each
grid cell for VRE.
To generate the 8760-hour electricity demand profiles of the
32 provinces, we first collect data on the typical hourly demand
loads for weekdays and non-workdays, as well as the daily
maximum and minimum loads observed throughout 2019 as
detailed in ref. 77. Subsequently, we adjust the hourly demand
load for each day based on whether it falls on a weekday or nonworkday, and the corresponding maximum and minimum
demand levels. The resulting hourly demand for each day is



3714 | _Energy Environ. Sci._, 2025, 18, 3699–3717 This journal is © The Royal Society of Chemistry 2025


**[View Article Online](https://doi.org/10.1039/d5ee00355e)**


Energy & Environmental Science Paper



then concatenated sequentially to construct the annual 8760hour profile. Following this, we scale up the hourly demand
profile for the base year (2022) using the annual demand
increase rates relative to 2019 in each province, as outlined in
the ESI,† Section S3.1, with Fig. S25–S28 illustrating the normalized load profile. Provincial 8760-hour demand profiles are
further scaled using the annual nationwide demand increase
rate relative to the base year (2022) for each planning year
(2030–2060). For 2030, we assume an annual nationwide
demand of 12.5 PW h per year (approximately 1.36 times the
2023 levels) considering the recent rapid demand growth,
which is also close to the expert assessments. [13,14] By 2060, an
economy-wide integrated model predicts China’s annual
demand will reach around 15.4 PW h per year, [4,6] aligned closely
with projections from Shu et al. [13] and Li et al. [14] of 15.7 PW h
per year. Within these analyses, we adopt the annual electricity
demand of 16.0 PW h per year in 2060 for the base scenario.
Based on these assumptions, the annual demand increase rates
for 2030 and 2060 serve as the basis for projecting nationwide
yearly demand for the intervening planning years.


4.4 Scenario design


We design ten parallel optimization pathways toward 2060 with
a 10-year step, according to the assumptions for annual emissions limitations, investment cost and electricity demand projections, biomass availability, and land availability for VRE.
Scenarios considering carbon emissions only differ in the
annual emissions limitations to the base scenario (see the ESI,†
Section S6, for emissions targets), while other input data and
parameters are consistent. Definitions of these scenarios are as
follows. The base scenario is the center case with target annual
emissions in 2060 of about �550 MtCO 2 per year; the ‘‘NegE
mis200Mt’’ is a scenario with more relaxed annual emissions

limitations than the base scenario, targeting net-zero emissions
in 2050, and slightly negative emissions (�200 MtCO 2 per year)
in 2060; the ‘‘NegEmis700Mt’’ is a scenario with stricter annual
emissions limitations than the base scenario, targeting about
�400 MtCO 2 per year in 2050 and �700 MtCO 2 per year in 2060;
the ‘‘NoEmisCap’’ scenario is designed as a control scenario
without an emissions limitation for comparison.
In the ‘‘Capex1.25X’’ scenario, the investment costs of
technologies with higher uncertainty in industry advancement
are 1.25 times that of the base scenario, including wind, solar
PV, CSP, pumped hydro storage, battery storage, and biomass
energy with carbon capture and sequestration. For the higher
electricity demand projection scenario (‘‘Demand1.25X’’), we
increase the annual electricity demand by 25% compared to the
base scenario from 2030 to 2060. Negative emissions technologies in power are modeled as BECCS and direct air capture
with carbon sequestration in the CISPO model. To test the
impacts of restricted use of BECCS, we design a limited annual
biofuel availability scenario (‘‘LimitedBECCS’’, about 7.45 EJ
per year) with a quarter of the base scenario, and a scenario
without BECCS (‘‘WithoutBECCS’’) only using DACCS as the
NET. The ‘‘VRELandOpen’’ and ‘‘VRELandConservative’’ scenarios adopt the installation capacity potential for wind and



solar PV power assessed under ‘‘Open’’ and ‘‘Conservative’’
suitable area assumptions as the upper deployable bound for
each grid cell (see the ESI,† Section S2, for a detailed land
availability assessment).

### Author contributions


Ziheng Zhu: conceptualization, methodology, software, data collection and calibration, formal analysis, visualization, writing – original
draft preparation; Da Zhang: conceptualization, supervision, writing –
review & editing, funding acquisition; Xiaoye Zhang: supervision,
funding acquisition; Xilaing Zhang: supervision, funding acquisition.

### Data availability


All data used in this study are reported in the paper or ESI.†

### Conflicts of interest


There are no conflicts of interest to declare.

### Acknowledgements


We acknowledge the support of the National Natural Science
Foundation of China (no. 42341202 and 72140005), the Tsinghua University Initiative Scientific Research Program, the
Environmental Defense Fund, the Economics of Energy Innovation and System Transition (EEIST) project, and the Sino
German Center Mobility Programme (M-0708). We thank Hanjie Mao for her excellent research assistance.

### References


1 United Nations (UN), Statement by H.E. Xi Jinping President
of the People’s Republic of China at the General Debate of
the 75th Session of The United Nations General Assembly,

[[EB/OL], 2020, https://estatements.unmeetings.org/estatements/](https://estatements.unmeetings.org/estatements/10.0010/20200922/cVOfMr0rKnhR/qR2WoyhEseD8_en.pdf)
[10.0010/20200922/cVOfMr0rKnhR/qR2WoyhEseD8_en.pdf.](https://estatements.unmeetings.org/estatements/10.0010/20200922/cVOfMr0rKnhR/qR2WoyhEseD8_en.pdf)
2 X. Chen, Y. Liu, Q. Wang, J. Lv, J. Wen, X. Chen, C. Kang,
S. Cheng and M. B. McElroy, Joule, 2021, 5, 2715–2741.
3 Z. Zhuo, E. Du, N. Zhang, C. P. Nielsen, X. Lu, J. Xiao, J. Wu
and C. Kang, Nat. Commun., 2022, 13, 3172.
4 D. Zhang, Z. Zhu, S. Chen, C. Zhang, X. Lu, X. Zhang,
X. Zhang and M. R. Davidson, Proc. Natl. Acad. Sci. U. S. A.,

2024, 121, e2306517121.
5 M. Li, R. Shan, A. Abdulla, E. Virguez and S. Gao, Energy
Environ. Sci., 2024, 17, 2193–2205.
6 X. Zhang, X. Huang, D. Zhang, Y. Geng, L. Tian, Y. Fan and
W. Chen, J. Manage. World, 2022, 38(01), 35–66.
7 Ministry of Industry and Information Technology (MIIT),
xIndustrial Energy Efficiency Improvement Action Plan, 2022,
[https://www.gov.cn/zhengce/zhengceku/2022-06/29/5698410/](https://www.gov.cn/zhengce/zhengceku/2022-06/29/5698410/files/e5be6f181bce4f89acb0b60d18d1acf6.pdf)
[files/e5be6f181bce4f89acb0b60d18d1acf6.pdf.](https://www.gov.cn/zhengce/zhengceku/2022-06/29/5698410/files/e5be6f181bce4f89acb0b60d18d1acf6.pdf)
8 Ministry of Industry and Information Technology (MIIT),
National Development and Reform Commission (NDRC)



This journal is © The Royal Society of Chemistry 2025 _Energy Environ. Sci._, 2025, 18, 3699–3717 | 3715


**[View Article Online](https://doi.org/10.1039/d5ee00355e)**


Paper Energy & Environmental Science



and Ministry of Ecology and Environment (MEE), Implementation Plan for Carbon Peaking in the Industrial Sector, 2022,
[https://www.gov.cn/zhengce/zhengceku/2022-08/01/5703910/](https://www.gov.cn/zhengce/zhengceku/2022-08/01/5703910/files/f7edf770241a404c9bc608c051f13b45.pdf)
[files/f7edf770241a404c9bc608c051f13b45.pdf.](https://www.gov.cn/zhengce/zhengceku/2022-08/01/5703910/files/f7edf770241a404c9bc608c051f13b45.pdf)
9 The State Council of the People’s Republic of China, Action
[Plan for Peaking Carbon Emissions Before 2030, 2021, https://](https://www.gov.cn/gongbao/content/2021/content_5649731.htm)
[www.gov.cn/gongbao/content/2021/content_5649731.htm.](https://www.gov.cn/gongbao/content/2021/content_5649731.htm)
10 C. E. C. (CEC), Analysis and Prediction Report on the National
Electricity Supply and Demand Situation from 2023 to 2024,
[2024, https://www.cec.org.cn/detail/index.html?3-330280.](https://www.cec.org.cn/detail/index.html?3-330280)
11 IEA, Electricity 2024, Iea, Paris technical report, 2024.
12 N. E. A. (NEA), The National Energy Administration releases
statistical data on the national power industry for 2023, 2024,
[https://www.nea.gov.cn/2024-01/26/c_1310762246.htm.](https://www.nea.gov.cn/2024-01/26/c_1310762246.htm)
13 Y. Shu, Y. Zhao, L. Zhao, B. Qiu, M. Liu and Y. Yang, Proc.

CSEE, 2023, 43, 1663–1671.
14 H. Li, D. Liu and D. Yao, Proc. CSEE, 2021, 41, 6245–6258.
15 Y. Wang, Q. Chao, L. Zhao and R. Chang, Carbon Neutrality,

2022, 1, 15.
16 S. Chen, X. Lu, Y. Miao, Y. Deng, C. P. Nielsen, N. Elbot, Y. Wang,
K. G. Logan, M. B. McElroy and J. Hao, Joule, 2019, 3, 1895–1912.
17 J. Tian, S. Zhou and Y. Wang, Environ. Impact Assess. Rev.,

2023, 102, 107161.
18 J. A. de Chalendar and S. M. Benson, Joule, 2019, 3, 1389–1393.
19 J. E. Bistline and G. J. Blanford, Proc. Natl. Acad. Sci. U. S. A.,

2016, 113, E3988.
20 Y. Lei, Z. Wang, D. Wang, X. Zhang, H. Che, X. Yue, C. Tian,
J. Zhong, L. Guo and L. Li, et al., Nat. Clim. Change, 2023, 13,

693–700.

21 L. Liu, G. He, M. Wu, G. Liu, H. Zhang, Y. Chen, J. Shen and
S. Li, Nat. Energy, 2023, 8, 870–880.
22 B. U. Schyska, A. Kies, M. Schlott, L. von Bremen and
W. Medjroubi, Joule, 2021, 5, 2606–2624.
23 P. R. Brown and A. Botterud, Joule, 2021, 5, 115–134.

24 Y. Ou, Nat. Energy, 2023, 8, 1309–1310.
25 A. Millot, P. Lubello, E. M. Tennyson, M. Mutembei,
M. Akute, D. Mentis, S. Pye, A. Hawkes and S. Sterl, Cell
Rep. Sustainability, 2024, 1, 100222.
26 Y.-M. Wei, J.-N. Kang, L.-C. Liu, Q. Li, P.-T. Wang, J.-J. Hou,
Q.-M. Liang, H. Liao, S.-F. Huang and B. Yu, Nat. Clim.
Change, 2021, 11, 112–118.
27 S. Collins, J. P. Deane, K. Poncelet, E. Panos, R. C. Pietzcker,
E. Delarue and B. P. O [´] . Gallacho´ir, Renewable Sustainable

Energy Rev., 2017, 76, 839–856.
28 M. Li, R. Shan, A. Abdulla, E. Virguez and S. Gao, Energy
Environ. Sci., 2024, 17, 2193–2205.
29 X. Guo, X. Chen, X. Chen, P. Sherman, J. Wen and
M. McElroy, Nat. Commun., 2023, 14, 2447.
30 J.-L. Fan, Z. Li, X. Huang, K. Li, X. Zhang, X. Lu, J. Wu,
K. Hubacek and B. Shen, Nat. Commun., 2023, 14, 5972.
31 Y. Wang, R. Wang, K. Tanaka, P. Ciais, J. Penuelas,
Y. Balkanski, J. Sardans, D. Hauglustaine, W. Liu and
X. Xing, et al., Nature, 2023, 619, 761–767.
32 S. Zhang and W. Chen, Engineering, 2022, 14, 64–76.
33 X. Deng, F. Teng, M. Chen, Z. Du, B. Wang, R. Li and

P. Wang, Nat. Commun., 2024, 15, 1085.



34 J.-L. Fan, J. Fu, X. Zhang, K. Li, W. Zhou, K. Hubacek,
J. Urpelainen, S. Shen, S. Chang and S. Guo, et al., Nat.
Clim. Change, 2023, 13, 807–815.
35 D. Zhang, X.-D. Huang, J.-T. Zhong, L.-F. Guo, S.-Y. Guo,
D.-Y. Wang, C.-H. Miao, X.-L. Zhang and X.-Y. Zhang, Adv.
Clim. Change Res., 2023, 14, 941–951.
36 China Renewable Energy Engineering Institute, China Renewable Energy Development Report 2022 (in Chinese), 2023.
37 M. S. Ziegler, J. M. Mueller, G. D. Pereira, J. Song, M. Ferrara,
Y.-M. Chiang and J. E. Trancik, Joule, 2019, 3, 2134–2153.
38 T. Levin, J. Bistline, R. Sioshansi, W. J. Cole, J. Kwon,
S. P. Burger, G. W. Crabtree, J. D. Jenkins, R. O’Neil and
M. Korpås, et al., Nat. Energy, 2023, 8, 1199–1208.
39 National Energy Administration (NEA), Medium and longterm development plan of pumped storage (2021-2035) (Draft
[for soliciting opinions) (in Chinese), [EB/OL], 2021, https://m.](https://m.sohu.com/a/483866703_121123711/?pvid=000115_3w_a)
[sohu.com/a/483866703_121123711/?pvid=000115_3w_a.](https://m.sohu.com/a/483866703_121123711/?pvid=000115_3w_a)
40 N. A. Sepulveda, J. D. Jenkins, F. J. De Sisternes and
R. K. Lester, Joule, 2018, 2, 2403–2420.
41 X.-J. Xiao and K.-J. Jiang, Adv. Clim. Change Res., 2018, 9,

138–143.

42 S. Cobo, A. Galan-Martin, V. Tulus, M. A. J. Huijbregts and
G. Guillen-Gosalbez, Nat. Commun., 2022, 13, 2535.
43 R. Wang, W. Cai, L. Yu, W. Li, L. Zhu, B. Cao, J. Li, J. Shen,
S. Zhang and Y. Nie, et al., Sci. Data, 2023, 10, 384.
44 J. Young, N. McQueen, C. Charalambous, S. Foteinis,
O. Hawrot, M. Ojeda, H. Pilorge´, J. Andresen, P. Psarras,
P. Renforth, S. Garcia and S. Mijndert van der, One Earth,

2023, 6, 899–917.
45 R. Wang, H. Li, W. Cai, X. Cui, S. Zhang, J. Li, Y. Weng,
X. Song, B. Cao, L. Zhu, L. Yu, W. Li, L. Huang, B. Qi, W. Ma,
J. Bian, J. Zhang, Y. Nie, J. Fu, J. Zhang and C. Wang,
Environ. Sci. Technol., 2022, 56, 16082–16093.
46 Z. Zhang, M. Chen, T. Zhong, R. Zhu, Z. Qian, F. Zhang,
Y. Yang, K. Zhang, P. Santi, K. Wang, Y. Pu, L. Tian, G. Lu¨
and J. Yan, Nat. Commun., 2023, 14, 2347.
47 S. Joshi, S. Mittal, P. Holloway, P. R. Shukla, B. O [´] . Gallacho´ir
and J. Glynn, Nat. Commun., 2021, 12, 5738.
48 S. Srinivasan, Technology and Economics of Smart Grids and
Sustainable Energy, 2019, vol. 4, p. 13.
49 NEA and NDRC, Notice on Establishing a Coal Power Capacity
[Payment Mechanism, 2023, https://www.ndrc.gov.cn/xxgk/](https://www.ndrc.gov.cn/xxgk/zcfb/tz/202311/t20231110_1361897.html)
[zcfb/tz/202311/t20231110_1361897.html.](https://www.ndrc.gov.cn/xxgk/zcfb/tz/202311/t20231110_1361897.html)

50 P. Beiter, T. Mai, M. Mowers and J. Bistline, Nat. Energy,

2023, 8, 1240–1249.
51 MIT Energy Initiative and Princeton University ZERO lab,
GenX: a configurable power system capacity expansion model
for studying low-carbon energy futures, 2022, Accessed: 2024[02-25, https://github.com/GenXProject/GenX.](https://github.com/GenXProject/GenX)
52 H. Zhang, D. Zhang and X. Zhang, Renewable Sustainable

Energy Rev., 2023, 173, 113080.
53 J. A. Dowling, K. Z. Rinaldi, T. H. Ruggles, S. J. Davis,
M. Yuan, F. Tong, N. S. Lewis and K. Caldeira, Joule, 2020,

4, 1907–1928.
54 Development and Reform Commission of Henan Province,
Notice on Promoting the Healthy and Sustainable Development



3716 | _Energy Environ. Sci._, 2025, 18, 3699–3717 This journal is © The Royal Society of Chemistry 2025


**[View Article Online](https://doi.org/10.1039/d5ee00355e)**


Energy & Environmental Science Paper



[of Distributed Photovoltaic Power Generation, 2023, https://](https://fgw.henan.gov.cn/2023/11-02/2840540.html)
[fgw.henan.gov.cn/2023/11-02/2840540.html.](https://fgw.henan.gov.cn/2023/11-02/2840540.html)
55 Energy Administration of Shandong Province, Announcement
on the release of the assessment results of the carrying capacity
of distributed photovoltaic access to the power grid, 2024,
[https://www.shandong.gov.cn/jpaas-jpolicy-web-server/front/](https://www.shandong.gov.cn/jpaas-jpolicy-web-server/front/info/declaresource?iid=3003055447b9442a87bb4f1d7cf8f396)
[info/declaresource?iid=3003055447b9442a87bb4f1d7cf8f396.](https://www.shandong.gov.cn/jpaas-jpolicy-web-server/front/info/declaresource?iid=3003055447b9442a87bb4f1d7cf8f396)

56 M. Fajardy and N. Mac Dowell, Energy Environ. Sci., 2017, 10,

1389–1426.

57 L. H. Goulder, X. Long, C. Qu and D. Zhang, China’s
Nationwide CO2 Emissions Trading System: A General Equilibrium Assessment, NBER Working Paper No. 31809, 2023,
[https://www.nber.org/papers/w31809.](https://www.nber.org/papers/w31809)
58 X. Han, X. Chen, M. B. McElroy, S. Liao, C. P. Nielsen and
J. Wen, Appl. Energy, 2019, 237, 145–154.
59 R. Romero, A. Monticelli, A. Garcia and S. Haffner, IEE Proc.
Gener. Transm. Distrib., 2002, 149, 27–36.
60 H. Hersbach, B. Bell, P. Berrisford, S. Hirahara, A. Hora´nyi,
J. Mun˜oz-Sabater, J. Nicolas, C. Peubey, R. Radu,
D. Schepers, A. Simmons, C. Soci, S. Abdalla, X. Abellan,
G. Balsamo, P. Bechtold, G. Biavati, J. Bidlot, M. Bonavita,
G. Chiara, P. Dahlgren, D. Dee, M. Diamantakis, R. Dragani,
J. Flemming, R. Forbes, M. Fuentes, A. Geer, L. Haimberger,
S. Healy, R. J. Hogan, E. Ho´lm, M. Janiskova´, S. Keeley,
P. Laloyaux, P. Lopez, C. Lupu, G. Radnoti, P. Rosnay,
I. Rozum, F. Vamborg, S. Villaume and J.-N. The´paut,
Q. J. R. Meteorol. Soc., 2020, 146, 1999–2049.
61 N. R. E. Laboratory, 2020 Annual Technology Baseline: NREL
[Reference 7MW, 2020, https://nrel.github.io/turbine-models/](https://nrel.github.io/turbine-models/2020ATB_NREL_Reference_7MW_200.html)
[2020ATB_NREL_Reference_7MW_200.html.](https://nrel.github.io/turbine-models/2020ATB_NREL_Reference_7MW_200.html)

62 P. R. Brown and F. M. O’Sullivan, Renewable Sustainable

Energy Rev., 2020, 121, 109594.
63 S. Jerez, I. Tobin, R. Vautard, J. P. Monta´vez, J. M. Lo´pezRomero, F. Thais, B. Bartok, O. B. Christensen, A. Colette,
M. De´que´, G. Nikulin, S. Kotlarski, E. van Meijgaard,
C. Teichmann and M. Wild, Nat. Commun., 2015, 6, 10014.



64 D. E. H. J. Gernaat, H. S. de Boer, V. Daioglou, S. G. Yalew,
C. Mu¨ller and D. P. van Vuuren, Nat. Clim. Change, 2021, 11,

119–125.

[65 Microsoft, Global Building ML Footprints, 2023, https://](https://github.com/microsoft/GlobalMLBuildingFootprints)
[github.com/microsoft/GlobalMLBuildingFootprints.](https://github.com/microsoft/GlobalMLBuildingFootprints)
66 T. Lu, P. Sherman, X. Chen, S. Chen, X. Lu and M. McElroy,

Nat. Commun., 2020, 11, 4750.
67 R. Xu, Z. Zeng, M. Pan, A. D. Ziegler, J. Holden,
D. V. Spracklen, L. E. Brown, X. He, D. Chen and B. Ye,
et al., Nat. Water, 2023, 1, 113–122.

68 D. E. H. J. Gernaat, P. W. Bogaart, D. P. V. Vuuren,
H. Biemans and R. Niessink, Nat. Energy, 2017, 2, 821–828.
69 P. Lin, M. Pan, H. E. Beck, Y. Yang, D. Yamazaki, R. Frasson,
C. H. David, M. Durand, T. M. Pavelsky and G. H. Allen,
et al., Water Resour. Res., 2019, 55, 6499–6516.
70 X. Lu, L. Cao, H. Wang, W. Peng, J. Xing, S. Wang, S. Cai,
B. Shen, Q. Yang and C. P. Nielsen, et al., Proc. Natl. Acad.
Sci. U. S. A., 2019, 116, 8206–8213.
71 OpenStreetMapContributors, OpenStreetMap data, 2022,
[https://www.openstreetmap.org.](https://www.openstreetmap.org)
[72 Natural Earth, 1:50m Cultural Vectors, 2023, https://www.](https://www.naturalearthdata.com/downloads/50m-cultural-vectors/)
[naturalearthdata.com/downloads/50m-cultural-vectors/.](https://www.naturalearthdata.com/downloads/50m-cultural-vectors/)

73 Global Energy Monitor (GEM), Renewable Energy and Other
[Power, 2023, https://globalenergymonitor.org/.](https://globalenergymonitor.org/)
74 W. Wan, P. Do¨ll and H. Zheng, Water Resour. Res., 2022,

58, e2022WR032380.
75 National Energy Administration (NEA), Construction and
operation of photovoltaic power generation in 2022, 2023,
[https://www.nea.gov.cn/2023-02/17/c_1310698128.htm.](https://www.nea.gov.cn/2023-02/17/c_1310698128.htm)
76 China Electric Power Planning and Engineering Institute
(CEPPEI), Report on China Electric Power Development 2023
(in Chinese), 2023.
77 National Development and Reform Commission (NDRC),
Typical power load curves in each provincial power system (in
[Chinese), 2019, https://www.ndrc.gov.cn/xxgk/zcfb/tz/201912/](https://www.ndrc.gov.cn/xxgk/zcfb/tz/201912/P020191230336066090861.pdf)
[P020191230336066090861.pdf.](https://www.ndrc.gov.cn/xxgk/zcfb/tz/201912/P020191230336066090861.pdf)



This journal is © The Royal Society of Chemistry 2025 _Energy Environ. Sci._, 2025, 18, 3699–3717 | 3717


