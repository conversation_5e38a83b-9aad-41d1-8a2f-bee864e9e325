# Citation Key: galan-martinDelayingCarbonDioxide2021

---

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-0-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-0-1.png)
### ARTICLE

https://doi.org/10.1038/s41467-021-26680-3 **OPEN**

## Delaying carbon dioxide removal in the European Union puts climate targets at risk


Ángel <PERSON>-<PERSON> 1,2,3,7, <PERSON> 1,4,7, <PERSON><PERSON> 1, <PERSON> 5,6,
<PERSON> 4 & <PERSON><PERSON><PERSON>álbez 1 ✉


Carbon dioxide removal (CDR) will be essential to meet the climate targets, so enabling its

deployment at the right time will be decisive. Here, we investigate the still poorly understood

implications of delaying CDR actions, focusing on integrating direct air capture and bioenergy

with carbon capture and storage (DACCS and BECCS) into the European Union power mix.

Under an indicative target of −50 Gt of net CO 2 by 2100, delayed CDR would cost an extra of

0.12−0.19 trillion EUR per year of inaction. Moreover, postponing CDR beyond mid-century

would substantially reduce the removal potential to almost half (−35.60 Gt CO 2 ) due to the

underused biomass and land resources and the maximum technology diffusion speed. The

effective design of BECCS and DACCS systems calls for long-term planning starting from now

and aligned with the evolving power systems. Our quantitative analysis of the consequences

of inaction on CDR—with climate targets at risk and fair CDR contributions at stake—should

help to break the current impasse and incentivize early actions worldwide.


1 Institute for Chemical and Bioengineering, Department of Chemistry and Applied Biosciences, ETH Zürich, Vladimir-Prelog-Weg 1, 8093 Zürich, Switzerland.
2 Department of Chemical, Environmental and Materials Engineering, University of Jaén, Campus Las Lagunillas s/n, 23071 Jaén, Spain. 3 Center for
Advanced Studies in Earth Sciences, Energy and Environment (CEACTEMA), University of Jaén, Campus Las Lagunillas s/n, 23071 Jaén, Spain. [4] Institute of
Chemical Process Engineering, University of Alicante, PO 99, E-3080 Alicante, Spain. [5] Centre for Environmental Policy, Imperial College London, Exhibition
Road, London SW7 1NA, UK. [6] Centre for Process Systems Engineering, Imperial College London, Exhibition Road, London SW7 2AZ, UK. [7] These authors
contributed equally: Ángel Galán-Martín, Daniel Vázquez. [✉] [email: <EMAIL>](mailto:<EMAIL>)


NATURE COMMUNICATIONS | (2021) 12:6490 | https://doi.org/10.1038/s41467-021-26680-3 | www.nature.com/naturecommunications 1


### ARTICLE NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-021-26680-3



ue to the growing carbon emissions and rising global
temperatures, carbon dioxide removal (CDR) will become
# D essential to combat climate change [1][–][4] . According to the

most recent integrated assessment modeling scenarios (IAMs),
limiting global warming to 1.5 °C will require deploying CDR to
remove 10-20 Gt/yr of CO 2 over the 21 [st] century, and cutting
emissions sharply to reach carbon neutrality around mid-century
and ultimately become carbon-negative [2][,][5][,][6] .
CDR technologies and practices deliver net negative emissions
by removing and sequestering CO 2 from the atmosphere [7][,][8] .
Nature-based strategies sequester the CO 2 in natural sinks (e.g.,
afforestation/reforestation, AR, and tailored agricultural practices), while engineered CDR stores the CO 2 either in geological
sites or minerals (e.g., enhanced weathering, Bio-Energy with
Carbon Capture and Storage, BECCS, and Direct Air Carbon
Capture and Storage, DACCS) [8][–][10] . To date, most IAMs already
include AR and BECCS [11], while other CDR options with low
technology readiness levels and limited removal potential are
often omitted [1][,][5][,][8] . BECCS is particularly appealing because it
removes CO 2 while delivering renewable and reliable energy [12] ;
however, it can lead to large impacts on ecosystems and biodiversity, which could be alleviated by resorting to DACCS [13][–][16] .
DACCS shows a large removal potential limited mainly by the
storage capacity [5][,][17], yet its large heat and power requirements
hamper its large-scale adoption [16][,][18][,][19], suggesting that an optimal
regionalized portfolio of CDR strategies should be sought.
The CDR deployment to date has been minimal [20][,][21] with only
1.5 million t CO 2 /yr removed via BECCS [9][,][22] and around 0.01
million t CO 2 /yr [18] with DAC technologies, often deployed
without long term CO 2 storage. The main barriers for CDR
deployment include the lack of consensus on the need to start

— —
CDR today as it is often perceived as a problem for later, the
absence of market incentives and strong political drivers, and
governance challenges. Moreover, debates on the ethics surrounding CDR have also emerged over the so-called moral hazard
and betting concerns about negative emissions [1][,][23][–][26] . These
concerns refer to the risk of obstructing emissions cuts and
delaying CDR deployment [27][,][28] under the assumption that CDR
could be adopted to the extent and with the motion needed to
compensate ongoing emissions and meet the climate goals. In
practice, however, future technological, social, and environmental
barriers that remain largely unexplored [29][–][31] may hinder the
implementation of CDR and the attainment of the long-term
temperature targets [26][,][32][–][35] .
Deterring mitigation actions and delaying CDR is already
perceived as risky [26][,][36], with ongoing discussions advocating the
definition of separate mitigation and removal targets to promote
CDR [30][,][36][–][39] . The consequences of delaying mitigation actions
have already been studied [40][–][49], while the implications of postponing CDR remain unclear [19][,][36][,][50][,][51] . Hence, expanding our
currently limited knowledge on the perils of CDR inaction could
help break the current deadlock, expedite CDR measures, ensure
ambitious contributions consistent with fair responsibilities [37] and
delineate the best plan forward to combat climate change.
Here we fill this gap by studying the implications of CDR
inaction, focusing on BECCS and DACCS deployment in the
European Union (EU) as key engineered CDR strategies strongly
linked to the energy sector [52][,][53] . The EU is expected to play a vital
role in future CDR actions [54][–][56] and has proposed a legally
binding climate-neutrality target by 2050 [57][–][59] that will require
CDR measures yet to be defined [56] . Applying a tailored energy
systems model, we find that postponing CDR actions could
increase the removal costs quite substantially (e.g., 0.12–0.19
trillion EUR2015 per year of inaction to deliver −50 Gt of net
CO 2 emissions) and drastically reduce the removal potential,
putting the EU at risk of missing targets (e.g., from −73.73 to



−35.60 Gt CO 2 by delaying CDR action from 2020 to 2050,
respectively). Our results highlight the urgent need to deploy
CDR early to meet the climate goals on time and in a costeffective manner.


Results
Consequences of delayed CDR actions: extra-costs and underexploitation of resources. We start by analyzing the implications
on costs and emissions of delaying the large-scale deployment of
BECCS and DACCS. To this end, we apply an energy systems
model (i.e., RAPID) that captures the interplay between the CDR
technologies and the power sector to identify the minimum cost
(Fig. 1a) and maximum net negative emissions potential (Fig. 1b)
roadmaps, starting the CDR deployment at different points in
time in 2020–2100 (optimal solutions every five years depicted
with markers in Fig. 1). RAPID identifies the optimal portfolio of
power technologies, BECCS and DACCS, including their location
and installed capacities, which may vary over time to meet a given
energy demand pattern and CO 2 removal target (details in
Methods).
We first investigate the economic implications of delaying CDR
for various net CO 2 emissions targets, finding that postponing
CDR increases the removal cost, first smoothly and then sharply
as we further postpone actions (Fig. 1a). For example, reaching
carbon neutrality by 2100 by deploying BECCS and DACCS from

–
2020 would cost 22.8 trillion EUR2015 (in the range 18.6 26.6,

–
uncertainty analysis in the Supplementary Tables 43 50), and

–
24.1 when starting in 2075 (18.2 29.5), while it would become
infeasible when starting after 2080. Delaying actions would
reduce the amount of cheap biomass available, making it
necessary to resort to the more expensive DACCS to attain the
CDR target (109–155 EUR2015 t [−][1] CO 2 for DACCS considering
optimistic prospects vs. 61–86 EUR2015 t [−][1] CO 2 with BECCS).
For example, to meet a target of −50 Gt net of CO 2, DACCS
would have to remove 9% of the cumulative gross negative
emissions needed by 2100 when starting in 2020, and up to 40%
for a delayed start in 2050, after which this indicative CDR target
would be unattainable. For the same CDR target, each year of
CDR inaction would increase the removal cost in the range of
0.12-0.19 trillion EUR2015 by 2100. The opportunity cost of
delaying CDR would vary over time, depending on the gradual
mix decarbonization and improvements attained via learning
curves (e.g., CAPEX of DACCS expected to become c.a. 70%
cheaper in 2050 relative to 2020, Supplementary Table 14).
We next focus on the feasibility of the removal roadmaps,
assuming an emergency plan to maximize CDR, starting actions
in different years. We find that delaying CDR constrains the total
removal potential substantially (Fig. 1b), again, first smoothly and
then sharply. Notably, postponing the deployment of BECCS and
DACCS beyond 2050 might prevent the EU from removing −50
Gt CO 2 before 2100 (i.e., EU emissions emitted in the last
decade [56][,][60] ), while delays beyond 2080 might even impede
reaching carbon neutrality in the power sector. The EU would
maximize its CDR potential by deploying BECCS and DACCS
from today (i.e., net −73.73 Gt CO 2 by 2100 starting from 2020,
Fig. 1b). This maximum CDR potential would be constrained by
the geological storage capacity in the EU, estimated at 90.53 Gt
CO 2 for hydrocarbon reservoirs and aquifers [61] . In practice,
however, the final amount of CDR that could be delivered will be
subject to social acceptance issues, regulatory limitations,
competition for resources, and economic feasibility challenges.
For example, deploying BECCS at scale will be challenging due to
the competition for land and water with food production and
other sustainability concerns, including the high demand for
fertilizers required to sustain the bioenergy crops [62] . These issues



2 NATURE COMMUNICATIONS | (2021) 12:6490 | https://doi.org/10.1038/s41467-021-26680-3 | www.nature.com/naturecommunications


### NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-021-26680-3 ARTICLE



**a**


**b**



26


24


7



Minimizing cost - Equipotential CDR curves



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-2-0.png)

Year of CDR deployment start





36


34


32


30


28






























|Maximizing net negative CO2 emissions|Col2|
|---|---|
|Net zero CO2 emissions<br>2080|Net zero CO2 emissions<br>2080|
|Opportunity cost<br>of underuse<br>of biomass<br>Decline due to<br>DACCS<br>diffusion rate<br>23%<br>77%<br>41%<br>59%<br>42%<br>58%<br>13%<br>87%<br>2020<br>2040<br>2050<br>2060<br>2%<br>98%<br>2045<br>46%<br>54%|only BECCS<br>only DACCS<br>BECCS +DACCS<br>Net negative<br>emissions from<br>the EU power system<br>BECCS<br>DACCS|



Year of CDR deployment start


Fig. 1 Implications on costs and emissions of delayed-actions on carbon dioxide removal (CDR) considering different starting points for bioenergy with
carbon capture and storage (BECCS) and direct air carbon capture and storage (DACCS) deployment until 2100 (x-axis). Subplot (a) shows the
minimum costs of the European power system associated with increasing CDR targets. Subplot (b) shows the maximum cumulative net CDR that could be
attained considering the deployment of BECCS and DACCS from a particular point in time onwards (green profile only with BECCS, blue with DACCS, and
yellow considering both BECCS and DACCS). Dots correspond to the optimal solutions for the 5-year time steps starting in 2020 and ending in 2100. The
shaded area in subplot (b) indicates the uncertainty in the life cycle CO 2 emissions (i.e., μ ± 2σ, Methods for details on the uncertainty analysis). The pie
charts illustrate the proportion of gross CDR provided with BECCS and DACCS, respectively.



could be addressed (to some extent) by resorting to marginal land
and residues and implementing sustainable management
practises [62][–][64] . Hence, the biomass potentials linked to the
availability of residues and marginal land are affected by
uncertainties. Notably, competition for the limited biomass
resources available will likely emerge due to the biomass
versatility to decarbonize different sectors (e.g., transport), while
marginal land availability might vary greatly due to improvements in agriculture or dietary changes [65] . We performed a
sensitivity analysis to study the effects of these uncertainties,
finding that reducing the biomass availability (i.e., −25% of the
original estimates, Supplementary Fig. 2), would not change the
maximum CDR substantially starting actions today (−70.04 Gt
CO 2 by 2100). This is because the storage capacity would still act



as the main bottleneck. However, when CDR actions are delayed
beyond 2050, the reduced availability of biomass would result in a
significant drop in the maximum CO 2 removed (e.g., −17% and

−
33% starting in 2050 and 2060, respectively). The costs of the
power system would also increase when biomass availability is
constrained further due to the need to resort to DACCS from the
early years. Note that our results only consider domestic biomass
resources and onshore geological sites. However, imported
biomass and CO 2 storage capacity beyond the EU borders and
offshore storage sites may substantially increase the potential
despite facing international governance issues while posing
sustainability and social justice questions [66][,][67] . Hence, considering
other CO 2 storage options (e.g., mineral trapping) or other
strategies beyond the power sector (e.g., planting trees or



NATURE COMMUNICATIONS | (2021) 12:6490 | https://doi.org/10.1038/s41467-021-26680-3 | www.nature.com/naturecommunications 3


### ARTICLE NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-021-26680-3



improving soil carbon sequestration) could further increase the
EU’s CDR ambition. For example, leaving aside the shortcomings
related to permanence and vulnerability of the carbon sequestration in trees, reforesting the EU countries could provide an
additional removal potential of 0.91 Gt CO 2 per year until the
sink saturates (i.e., 30 years when the forest reaches the steadystate). Similarly, improved management practices such as
delaying harvests or adopting reduced-impact logging would
further remove 0.07 Gt CO 2 per year by 2100 [37][,][68] . The gradual
temporal decline in CDR potential is due to the underused
resources (i.e., biomass residues and unexploited marginal land to
grow energy crops) and the maximum diffusion rates of the CDR
technologies. Lacking sufficient BECCS capacity to process the
biomass residues, they would degrade and eventually release the
biogenic carbon to the air in the form of CO 2, while the unused
marginal land would represent an opportunity lost. Furthermore,
the technology diffusion factor constraining the deployment
speed (e.g., 20% of annual capacity up-scaling [69] ) critically limits
the maximum attainable DACCS capacity. The potential reduction varies linearly over time for BECCS (green curve in Fig. 1b)
and follows a sigmoidal shape for DACCS, with a critical point
around 2045. This behavior is due to the unused biomass and
land resources, the main factors constraining BECCS, which
accumulate almost linearly over time. In contrast, the maximum
attainable DACCS capacity, only limited by the diffusion rate,
increases exponentially over time; consequently, further delays
result in much fewer DACCS plants ready to be deployed
on time.
Overall, in both the cost-optimal and the maximum removal
roadmap, BECCS emerges as predominant regardless of the
starting year, providing double benefits by contributing to CDR
while delivering reliable power to meet the demand (CDR
contribution breakdown in pie charts in Fig. 1a, b). When
maximizing the CDR potential, the DACCS contribution
increases with later starting years, while the BECCS contribution
declines due to the loss of biomass potential (e.g., DACCS from
23% starting in 2020 to 46% in 2045, Fig. 1b for the yellow
profile). However, the initial capacity for DACCS of 1 Mt of CO 2
captured per year—reflecting the current scale ambition—and the
maximum growth rate of 20%/yr observed in the historical
deployment of power technologies [19][,][51][,][69] would strongly limit the
maximum DACCS capacity when actions are further delayed.
Hence, deploying DACCS from 2050 would result in a
contribution of −30.57 Gt of CO 2 by 2100 (i.e., 42% of the total
gross −72.94 Gt of CO 2 removed), while the DACCS share
starting after 2080 would become negligible (i.e., <2%). In
contrast, the BECCS initial capacity set to 250 MW—reflecting
the state-of-the-art largest biomass-fired power plants—results in
the deployment pace being limited mainly by the geological
capacity, except when starting actions near 2100, where the
diffusion rate constraint becomes the bottleneck. Notably, the
synergetic integration of BECCS with DACCS into the energy
system emerges as an appealing option to enhance the CO 2
removal capacity (yellow curve showing always higher net CO 2
removal than the green and blue profiles). Therefore, deploying
DACCS appears as a complementary option under a rapid
emergency deployment, lacking biomass resources or if BECCS
deployment is constrained by the diffusion speed, being
environmentally benefited from the carbon-negative electricity
supplied by BECCS at the expense of increasing costs [19][,][52][,][70] .
Our findings are particularly relevant in the context of the EU
Green Deal aiming at climate neutrality by 2050. The EU Climate
Law fails to explicitly discuss the role of CDR technologies (other
than land sink removals) to meet such a goal. However, to
become climate neutral in 2050 (and provide negative emissions
beyond then), it seems clear that some countries and sectors will



rely on CDR to offset emissions. In this context, the EU power
sector will likely play a key role in meeting the climate neutrality
target by reaching net-zero emissions before 2050 and then
becoming carbon negative to compensate emissions from hardto-abate sectors. Considering the 2020 to 2050 horizon, delaying
the deployment of BECCS and DACCS beyond 2040 might
prevent the EU power sector from reaching carbon neutrality in
2050 (e.g., –0.85 Gt CO 2 by 2050 starting in 2040) while

–
increasing the costs in the range of 0.04 0.10 trillion EUR per
year of inaction (Supplementary Fig. 3). Therefore, promoting the
near-term integration of those technologies at the earliest is vital
to ensure that they can be deployed to the extent required to meet
the long-term goals.


Emissions pathways through 2100 for delayed CDR actions.
We next analyze the emission pathways, considering three
representative scenarios, namely, NOW, SLOW, and LATE, which
maximize the removal potential starting CDR actions in years
2020, 2055, and 2085, respectively (Methods section). The net CO 2
emissions balance accounts for the: i) gross removal, i.e., total CO 2
removed from the air, either via photosynthesis (BECCS) or
through physicochemical processes (DACCS); ii) anthropogenic
life cycle emissions embodied in the power technologies, BECCS
and DACCS supply chains; and, iii) CO 2 emissions during biomass/natural gas combustion in BECCS/DACCS facilities,
respectively, due to capture efficiencies below 100%. These emissions contributions vary across pathways, which differ in the
availability of resources and maximum BECCS and DACCS
capacities (constrained by the technology diffusion rates).
In the NOW scenario (Fig. 2a), starting CDR actions in 2020,
the gross negative emissions would amount to −94.05 Gt CO 2 by
2100, mostly provided by BECCS (77% in absolute terms) and a
small contribution by DACCS (23%), yielding −73.73 Gt of net
CO 2 removed from the atmosphere. This scenario shows the
largest positive residual emissions (+20.31 Gt CO 2 ), 63%
attributed to the uncaptured biogenic CO 2 and the life cycle
emissions linked to BECCS supply chains (+7.11 and +5.74 Gt
CO 2, respectively), 30% due to the life cycle emissions of the other
power technologies (excluding BECCS), 5% associated with the
life cycle emissions from DACCS and, finally, a marginal
contribution from the CO 2 transportation and storage infrastructure (<+1%). This scenario would fully exhaust the domestic
storage capacity in the EU, which would act as a bottleneck for
CDR. Furthermore, almost all the biomass resources available
would be exploited (i.e., 95% of the biomass residues and 94% of
marginal land), with a small amount lost due to the limited rate at
which BECCS could be scaled up during the first years
(Supplementary Fig. 4). The overall storage efficiency —i.e., total
net CO 2 removed per kg of CO 2 stored— would reach 81%, where
most geological sites would store the biogenic CO 2 captured via
BECCS (71%), a smaller amount of atmospheric CO 2 captured
with DACCS (24%), and finally the captured emissions linked to
the heating needs of DACCS (5%). Notably, when starting CDR
actions today, the geological capacity needed to store the CO 2
captured in fossil power plants with CCS would be negligible,
thus delivering the maximum CDR constrained by the domestic
storage capacity (Fig. 3). DACCS would play a role in
complementing BECCS and ultimately helping to remove CO 2
at the pace required, benefiting from the carbon-negative
electricity delivered in the system, and exploiting its flexibility
to be located closer to the geological sites in countries with scarce
biomass resources [52][,][70] .
In the SLOW scenario starting CDR actions in 2055, the
maximum gross and net removal potential would drop
considerably (−49.61 Gt and −35.60 Gt, respectively, vs.



4 NATURE COMMUNICATIONS | (2021) 12:6490 | https://doi.org/10.1038/s41467-021-26680-3 | www.nature.com/naturecommunications


### NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-021-26680-3 ARTICLE



**a**


**b**


**c**



40


20


0


-20


-40


-60


-80


-100


20


10


0


-10


-20


-30


-40


-50


-60


7.5


5.0


2.5


|Col1|Col2|Col3|
|---|---|---|
||||
||-72.59<br>-21.46<br>CO2 stored||
||-72.59<br>-21.46<br>CO2 stored||
||||
||||


|Col1|NOW scenario<br>For maximum CDR +0.23 CR Oe ms id u a iol fo s s il<br>delivered starting +20.31 +0.99 e is s n s<br>from 2020 +6.17 2<br>+12.85<br>Net zero emissions|
|---|---|
|||
||**2020**<br>2040<br>2060<br>2080<br>**2100**<br>**-73.73**<br>-94.05<br>-90.53<br>**Net  negative**<br>**CO2 emissions**<br>-72.59<br>-21.46<br>-64.03<br>-21.46<br>-5.05<br>Net CO2<br>emissions<br>Gross negative<br>CO2 emissions<br>Geological<br>CO2 storage<br>Net CO2 emissions<br>Gross CO2 emissions<br>CO2 emissions stored<br>Residual CO2 emissions<br>BECCS<br>DACCS<br>Heating<br>Power w/o BECCS<br>CO2 storage<br>Gross CO2 removed<br>CO2 stored|
|||
|||
|||
|||
|||




|Col1|Col2|Col3|
|---|---|---|
||||
||CO2 stored<br>-37.33<br>-12.28||
||CO2 stored<br>-37.33<br>-12.28||
||||
||||


|Col1|SLOW scenario<br>For maximum CDR +14.02 +0.09<br>delivered starting +0.58 from 2055 +6.11 CR Oe e ms id u a is s io n s l fo s s il<br>2<br>2055<br>+7.08<br>Net zero emissions|
|---|---|
|||
|||
||Gross CO2 removed<br>CO2 stored<br>2020<br>2040<br>2060<br>2080<br>**2100**<br>**-35.60**<br>-49.61<br>-51.41<br>**Net  negative**<br>**CO2 emissions**<br>Gross negative<br>CO2 emissions<br>Geological<br>CO2 storage<br>-37.33<br>-12.28<br>-32.92<br>-12.28<br>-2.89<br>-3.32<br>Net CO2<br>emissions<br>Net CO2 emissions<br>Gross CO2 emissions<br>CO2 emissions stored<br>Residual CO2 emissions<br>BECCS<br>DACCS<br>Heating<br>Power w/o BECCS<br>CO2 storage|
|||
|||
|||
|||
|||

















































0



-2.5


-5.0


-7.5


-10












|Col1|Col2|
|---|---|
|||
|||
|**00**<br>-7.09<br><br>Gross CO2 removed||


|Col1|LATE scenario<br>+8.63 +0.01<br>+1.17<br>CR Oe es id mu ioa l fo s ns il<br>is s s<br>For maximum CDR 2<br>Net CO<br>2 delivered starting<br>emissions from 2085<br>+7.45<br>2085 +1.54<br>Net zero emissions|
|---|---|
|||
|||
|||
||2020<br>2040<br>2060<br>2080<br>**2100**<br>-7.09<br>-8.27<br>Gross negative<br>CO2 emissions<br>Geological<br>CO2 storage<br>-7.05<br>-0.05<br>-6.21<br>-0.05<br>-0.01<br>-2.00<br>BECCS<br>DACCS<br>Heating<br>Power w/o BECCS<br>CO2 storage<br>Net CO2 emissions<br>Gross CO2 emissions<br>CO2 emissions stored<br>Residual CO2 emissions<br>Gross CO2 removed<br>CO2 stored|
|||
|||
|||
|||



Fig. 2 CO 2 emission pathways and breakdown in the European energy sector considering the three illustrative scenarios with different starting points
for the deployment of bioenergy with carbon capture and storage (BECCS) and direct air carbon capture and storage (DACCS) by 2100. Subplot
(a) corresponds to the NOW scenario starting carbon dioxide removal (CDR) in 2020. Subplot (b) corresponds to the SLOW scenario starting CDR in
2055. Subplot (c) corresponds to the LATE scenario starting CDR in 2085. Stacked bars on the right show the breakdown by the source of the life cycle
residual emissions, gross negative emissions, and stored emissions.


NATURE COMMUNICATIONS | (2021) 12:6490 | https://doi.org/10.1038/s41467-021-26680-3 | www.nature.com/naturecommunications 5


### ARTICLE NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-021-26680-3





![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-5-0.png)



































**b**

18


16


14



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-5-1.png)









12


10


8





6


4


2


0


Fig. 3 Regional implications for the European energy system starting the deployment of bioenergy with carbon capture and storage (BECCS) and
direct air carbon capture and storage (DACCS) in 2020 (NOW scenario). Subplot (a) corresponds to the optimal electricity generation by 2100 in each
European country. The pie charts show the share of generation per electricity technology depicted with different colors, while the size of the pie charts is
proportional to the generation by 2100 (TWh). CCS, PV and CSP stand for carbon capture and storage, solar photovoltaic and concentrated solar power,
respectively. Each country is colored according to the CO 2 stored in the geological sites; the darker the shade, the greater the CO 2 stored. Subplot
(b) shows the breakdown by country of the gross CO 2 removed from the atmosphere considering the different biomass resources for BECCS and DACCS
technologies. Countries in subplot (b) are labeled according to the ISO3 code abbreviation. The map in subplot (a) was created using ArcGIS®
10.7.1 software by Esri [82] ; no copyrighted material was used.



−94.05 Gt and −73.73 Gt in the NOW scenario), while the
storage capacity would not be fully exhausted (only 57% of the
capacity utilized). Notably, net negative CO 2 emissions would not
be achieved until 2070 due to the need to offset the residual
emissions taking place until that year. Before 2080, the
deployment rate would limit the BECCS removal capacity, while
beyond 2080, biomass resources would become the bottleneck
(residues and land). 86% of the residues and 90% of the marginal
land available from 2055 to 2100 would be exploited, representing
only 63% and 57% of their respective total potentials (if actions
were started in 2020 and continued until 2100). In contrast, the



maximum deployment rate would constrain the DACCS capacity
(Supplementary Fig. 4). Furthermore, the storage efficiency would
be reduced to 69%, with 88% of the storage devoted to
atmospheric carbon and the remaining part storing fossil carbon
(i.e., from natural gas combustion powering DACCS and power
plants with CCS). The global EU geological storage capacity
would not be fully depleted [5], yet competition between fossil and
atmospheric captured CO 2 for the sites available at the regional
level could become an issue, particularly given the increasing
policy support for CCS in fossil-fired power plants in many
countries [71] .



6 NATURE COMMUNICATIONS | (2021) 12:6490 | https://doi.org/10.1038/s41467-021-26680-3 | www.nature.com/naturecommunications


### NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-021-26680-3 ARTICLE



Finally, in the LATE scenario, the maximum gross negative
emissions would be substantially reduced to −7.09 Gt CO 2 . The
removal potential would be limited by the maximum diffusion
rates of BECCS and DACCS, which would even impede reaching
CO 2 neutrality in the EU power sector (+1.54 Gt of net CO 2
emissions by 2100) and constrain the use of residues and land to
40% and 20% of their maximum availability from 2020 to 2100,
respectively. Most of the biomass resources would be consumed
by biomass power without CCS during the inaction periods (only
8% of the total residues and 14% of the land available in the

–
period 2020 2100 would be exploited with BECCS after 2085).
Here, DACCS would play a minor role, removing only −0.05 Gt
of CO 2 by 2100 due to the slow speed at which it could be scaled
up. The storage efficiency would substantially decrease, with 25%
of the total capacity available devoted to fossil CO 2 emissions.
Overall, delaying the CDR deployment would lead to the
underuse of biomass and land resources, tighter bounds on the
BECCS and DACCS facilities, and domestic storage sites depleted
with fossil carbon, which altogether would reduce the future
ability of individual countries on CDR. However, transboundary
agreements enabling import/exports of biomass and CO 2, new
estimates of suitable geological sites, less conservative biomass
potentials, and a broader CDR portfolio beyond the energy sector

’
could enhance the EU s ability to deliver net negative emissions [37] .


Regional implications for the energy systems. The carbonnegative electricity supplied by BECCS and the large electricity
and heating requirements of DACCS create strong links between
them and the power system. Therefore, their deployment would
require long-term planning, ensuring their effective integration
into the evolving portfolio of power technologies starting at the
earliest. In the NOW scenario (shown in Fig. 3, SLOW and LATE
scenarios in Supplementary Figs. 5 and 6, respectively), the total
electricity generated in the EU by 2100 would be produced mostly
by wind onshore (41%), followed by nuclear (23%), BECCS (8%),
hydropower run-of-river and wind offshore (both with 7%),
concentrated solar power (6%), hydropower from reservoirs (6%),
and marginal contributions from natural gas, solar photovoltaic
open ground, biomass w/o CCS, and geothermal (<1%) (Fig. 3a).
Notably, BECCS becomes relevant in the generation portfolio,
providing firm capacity and ancillary services to support the high
penetration of intermittent technologies with dispatchable
carbon-negative electricity. Overall, a handful of countries would
shoulder most of the CDR efforts. Only four countries would
deliver almost half of the gross removal by 2100, with France and
Spain at the top deploying both BECCS and DACCS, followed by
Germany and Sweden deploying only BECCS (i.e., 44.37 Gt out of
94.05 Gt of gross CO 2 removed, Fig. 3b).
Most of the BECCS capacity would be installed in Germany,
Poland, the Netherlands, Spain, and Finland (69% of the total),
exploiting their abundant biomass resources and also taking
advantage of their central location (in the case of Germany and
Poland). Spain, France, Germany, Sweden, and Poland would
provide most of the biomass resources, i.e., 54% of the total gross
CO 2 removed via BECCS (−38.99 out of −72.59 Gt of CO 2
removed with BECCS, Fig. 3b). Both forestry and agricultural
residues would be fully exploited in all countries starting BECCS
deployment from today. Forestry residues would contribute the
most to the CO 2 removal (i.e., 45% of the total gross CO 2
removed by 2100), while miscanthus production would occupy all
the marginal land available due to its overall superior carbon
sequestration potential, removing −15.87 Gt CO 2 by 2100 (17%
of the total gross removed) and becoming the main carbon sink in
some countries (i.e., −6.16 Gt CO 2 removed in Spain). Switchgrass would also be cultivated in some regions, and the storage



capacity would no longer be the bottleneck if actions were further
delayed (SLOW and LATE scenarios in Supplementary Figs. 5b
and 6b, respectively). This is due to its relatively higher carbon
removal capacity (i.e., CO 2 uptake per kg of pellets), which
provides more CDR in the remaining time but at the expense of
reducing the electricity delivered owing to its lower energy
density.
Regarding DACCS, the configuration relying on electricity and
heating would be the only one installed, benefiting from the
decarbonized electricity mix. In the NOW scenario, DACCS
would be established in eleven countries, with France, Spain, the
United Kingdom, Italy, and Romania providing 97% of the gross
removal from DACCS (i.e., −18,72 out of the −21.46 Gt CO 2 by
2100), all of them with enough geological sites for storing the
captured CO 2 domestically (Fig. 3b). For example, in France,
−6.65 Gt CO 2 would be removed via DACCS by 2100, taking
advantage of its abundant saline aquifers and decarbonized mix
dominated by wind onshore and nuclear (Fig. 3a). Similarly, in
the United Kingdom, which lacks enough biomass resources to
exploit its storage capacity only with BECCS, −3.64 Gt CO 2
would be removed with DACCS and stored in domestic
geological sites. In practice, this roadmap would require a
substantial number of DACCS facilities across the EU, i.e., around
268, with a capacity of 1 Mt CO 2 /yr (i.e., the largest DAC plant
under development today), out of which 83 would be installed in
France, 61 in Spain and 46 in the United Kingdom. These
DACCS plants would make the said countries incur extra costs
and suffer adverse environmental impacts, such as those linked to
the land requirements of the air contactors and the energy
technologies powering them. Delaying actions until 2055 (SLOW
scenario) would imply that less biomass is available, so the
BECCS capacity would diminish accordingly, and additionial
DACCS facilities would be deployed in Czech Republic, Denmark, and Slovakia to maximize CDR, taking advantage of the
geological storage available (Supplementary Fig. 5).
Wind capacity would be massively deployed in most countries,
becoming the dominant source in France, Spain, Italy, Germany,
United Kingdom, Sweden, and Finland (NOW scenario in
Fig. 3a). Notably, offshore wind would become predominant in
some countries, with 82% of the EU capacity located in the
United Kingdom, Finland, Germany, and Sweden. Solar power
plants would be deployed mostly in southern locations (i.e.,
Spain, Italy, and Greece), with substantial capacities of concentrating solar thermal power installed in high irradiation areas
providing dispatchable renewable electricity. Some countries
would also exploit their hydropower capacities, such as Austria,
France, Italy, Sweden, and Spain. The contribution of nuclear by
2100 would fall in the range 0.28-35.84 TWh, with France
showing the largest shares and the Netherlands the lowest. No
single new nuclear plant would be installed because we assume
that the capacity of this technology cannot be expanded. The
existing coal plants would be completely phased out. Natural gas
plants w/o CCS would still be required in United Kingdom,
Germany, the Netherlands, Romania, and the islands (Ireland,
Malta, and Cyprus), while playing a marginal role in the others.
Overall, the integrated power-CDR system would result in
carbon-negative electricity due to the high penetration of BECCS
in the EU power system (i.e., −0.24 t CO 2 /MWh), yet it would
unavoidably increase the Levelized Cost of Electricity (LCOE) to
113.03 €/MWh.
The optimal roadmap, assuming full cooperation among EU
countries, would entail an intensive trade of biomass, CO 2, and
electricity (Fig. 4 for the NOW scenario and SLOW and LATE
scenarios in Supplementary Figs. 7 and 8). However, to minimize
the transport flows, the vast majority of the biomass demand
would still be supplied domestically, the captured CO 2 stored



NATURE COMMUNICATIONS | (2021) 12:6490 | https://doi.org/10.1038/s41467-021-26680-3 | www.nature.com/naturecommunications 7


### ARTICLE NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-021-26680-3



**a**

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-7-0.png)


**b**





SVN

SVK


ROU


PRT















POL


ESP


SVN

SVK


ROU



ITA





![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-7-1.png)



FRA


**c**















![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-7-2.png)



SVN

SVK


ROU


PRT





IRL


locally, and the electricity generated consumed on-site (domestic
consumption in Fig. 4 depicted with the chords leaving and
entering the same country). Some regions would be net exporters
of biomass (e.g., France or Sweden) and some net importers (e.g.,
Netherland, Germany, or Denmark). The same would apply to
CO 2, with, for example, Sweden, the United Kingdom, and
Romania acting as net importers and the Netherlands, Poland,



Fig. 4 Biomass trade, CO 2 flows, and electricity transmission in the NOW
scenario by 2100. Subplot a shows the biomass traded in the form of
pellets between European countries. Subplot (b) shows the CO 2
transported via a pipeline between European countries. Subplot c shows the
electricity traded between European countries. In the chord diagrams
produced using Circos [83], the European countries are depicted by arcs on
the outer part of the circular layout, where the arc length provides the total
biomass (subplot a), CO 2 (subplot b), and electricity (subplot c) imported,
exported and consumed/stored domestically (the latter refers to chords
leaving and entering the same country). Each chord represents a flow,
where its thickness is proportional to the magnitude of the trade (some
values are indicated for illustrative purposes). Chords directly connected to
the countries’ arcs represent an export (i.e., exporter country) while those
non-connected (separated by a white layer) correspond to imports.
Countries are labeled according to the ISO3 code abbreviation.


Finland, or Portugal becoming net exporters. Regarding electricity trade, countries such as France, Spain, and Sweden would
emerge as pivotal in the power system, acting as net exporters of
electricity to exploit their abundant low-carbon intensity
resources (e.g., electricity trades from France to Germany, Italy,
Netherlands, Belgium, and the United Kingdom, Fig. 4c).
The largest exchanges of biomass and CO 2 would occur
between France-the Netherlands, and the Netherlands-the United
Kingdom, respectively (Fig. 4a, b). Notably, Sweden would export
biomass resources to Germany and Denmark to fully exploit its
abundant forestry residues (i.e., 1.34 and 1.15 Gt of pellets on a
dry basis, respectively, Fig. 4a). Other countries would export
CO 2, e.g., Finland would send 2.21 Gt CO 2 via pipeline to the
abundant deep saline aquifers and hydrocarbon fields in Sweden
(CO 2 trades in Fig. 4b). Some countries would be almost selfsufficient in terms of biomass resources, like Portugal, which
would transport CO 2 to the Spanish geological sites due to its low
geological capacity. Overall, the transport of electricity (and CO 2 )
would be prioritized over the transport of biomass due to the
larger emissions of the latter considering a given electricity
demand (e.g., 0.01 vs. 0.11 kg CO 2 to satisfy one kWh in an
importing country, respectively, considering miscanthus as
biomass source and a distance of 800 km in both cases). Hence,
BECCS plants would be mostly installed near the biomass
sources, leading to decentralized supply chains spread across the
EU territory. However, some countries would still import biomass
pellets to reduce their reliance on energy from outside the
country’s borders and to support the high penetration of
intermittent wind and solar with dispatchable carbon-negative
electricity from BECCS (e.g., the Netherlands importing pellets
from France or Denmark from Sweden, Fig. 4a).
The uneven distribution of domestic capacities (i.e., biomass
resources, storage sites, and renewable resources) would make
national and transnational collaboration essential to exploit biogeophysical endowments [67][,][72] and remove CO 2 to the extent
required. Hence, new agreements and regulatory frameworks will
be needed, and shaping them may further delay CDR
deployment.


Discussion
Here we studied the implications of delaying the roll-out of CDR
to raise concerns on the need to set effective plans to promote its
large-scale deployment at the right time to avoid extra costs and
miss climate targets.
To shed light on the economic, environmental, and technical
implications of the prolonged delay of CDR actions, we focused
on the deployment of BECCS and DACCS in the EU as



8 NATURE COMMUNICATIONS | (2021) 12:6490 | https://doi.org/10.1038/s41467-021-26680-3 | www.nature.com/naturecommunications


### NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-021-26680-3 ARTICLE



prominent strategies intrinsically linked to the power system. We
found that postponing CDR could substantially increase the total
cost of the power system, with each year of inaction translating
into 0.12–0.19 trillion EUR2015 of extra cost to meet the -50 Gt
of net CO 2 target. Notably, this extra cost, which could be avoided
in the EU by starting actions from today, compares to the estimates of the additional annual investments required globally by
mid-century in the energy systems to limit global warming to
1.5 °C (i.e., between 0.15–1.70 trillion USD2010 [2] ). Moreover,
delaying CDR actions would critically limit the removal potential
(e.g., −73.73 Gt CO 2 starting in 2020 and −35.60 Gt CO 2 after
2055) due to the underuse of biomass residues and marginal land
and the maximum diffusion rates of BECCS and DACCS. Hence,
postponing CDR deployment beyond mid-century might prevent
the EU from delivering a CDR level aligned with its fair
responsibility and its expected leading role [37][,][56] . After 2050, the
maximum CDR potential would be reduced to −56.35 Gt CO 2,
representing around the CO 2 emitted by the EU only during the
last decade and less than 10% of the global CDR required in
pathways showing limited overshoot of the 1.5 °C target [2] . With
the EU contributing short and its leadership efforts questioned,
there might be a row of dominoes with other countries remaining
impasse on CDR, which ultimately may impede a fair and rapid
transition for effective climate change mitigation.
Furthermore, we found that delaying BECCS and DACCS may
result in the inefficient use of the available domestic geological
storage. Retrofitting the existing coal and gas-fired power plants
with CCS to decarbonize the power sector would limit the storage
capacity available, potentially raising competition issues with
atmospheric CO 2 sequestration. Although the global geological
capacity is deemed sufficient to meet the climate targets, it could
become a bottleneck at the regional level due to the asymmetric
distribution of storage sites. With policies promoting CCS in
fossil fuel power plants, some countries might exhaust the most
suitable geological sites with fossil CO 2, which ultimately will
compromise their future CDR commitments. This reinforces the
need to phase out coal and natural gas for a full transition
towards renewable energy systems. Nevertheless, these policies
could offer a testbed to support CCS projects, including the
development of the CO 2 transportation and storage infrastructure
needed to support CDR schemes. Without a supportive policy
framework, CDR will never take off in the short term despite
being imperative to offset hard to avoid energy and non-energy
emissions. In this context, governance issues should also be
considered to ensure that BECCS and DACCS will remove CO 2 at
the scale and pace required. BECCS and DACCS supply chains
involving multiple countries and stakeholders will encompass a
wide range of activities (e.g., biomass growth, storage, transportation and processing, and CO 2 transportation and storage). In
the absence of well-designed policy mechanisms rewarding all
CDR actors or direct incentives to engage with BECCS and
DACCS, their large-scale deployment is unlikely to occur. Hence,
integrating BECCS and DACCS into the evolving power systems
must be done at the earliest, ensuring the best use of the existing
resources through effective long-term strategic decisions and
planning.
CDR is gaining increasing attention in the political agendas of
governments, stakeholders, businesses, and industry. The recent
EU climate neutrality target by mid-century puts CDR back on
the table of reticent countries [59] . Hence, this study provides
valuable insight for CDR policymaking and future roadmaps
aiming to integrate BECCS and DACCS value chains into the EU
power systems. At the regional level, the least-cost distribution of
efforts may differ greatly from equitable shares and the willingness to act in different countries [37][,][73] . In practice, key countries
might still be reluctant concerning CDR. These nations might not



fully participate and fail to shoulder their fair share of the EU
CDR burden, which will result in some countries having to do
more and faster and others contributing less and going slowly
towards the CDR required. Nevertheless, our findings demonstrate that failure to start CDR at the right time would increase
the total costs and make climate targets slipping out of reach.
Overall, our work underscores the importance of taking early
actions on CDR. The potential economic and environmental
benefits that would be missed if we delayed action may act as an
incentive to spur CDR efforts worldwide. We focused on BECCS
and DACCS and the EU, yet other technologies should also be
considered and evaluated at the regional and global levels to
further motivate early CDR deployment. Regardless of the technological and spatial scope of the analysis, the underuse of
resources and the diffusion rates will limit our ability to remove
atmospheric carbon and increase the associated costs. We hope
that this quantitative analysis will help to break the current climate impasse, accelerate the take-off of CDR and get CDR
deployed at the right time to avoid missing the climate targets.


Methods
The RAPID model. We developed a bottom-up multi-period linear programming
model to explore the consequences of delaying CDR actions, referred to as the
RAPID model (RemovAl oPtImization moDel). RAPID is an energy systems model
that identifies the most cost-effective emissions and technology pathways by jointly
optimizing the power mix and the deployment of BECCS and DACCS, where CDR
is assumed to start from a particular year within the time horizon. RAPID can besolved in two alternative ways, i.e., by minimizing the power system’s costs to meet
a net CDR target or by maximizing the net negative emissions balance. RAPID
includes a set of technical, cost, and emissions-related constraints that the solution
sought should satisfy. In each run, the model can retrofit the power mix from 2020.
However, it can only deploy BECCS and DACCS from a given year defined
beforehand and varied iteratively across runs. RAPID is implemented assuming
perfect foresight for the modeling timeframe, i.e., the model optimizes decisions for
every period with full visibility of the entire time horizon. For simplicity, the model
considers five-year intervals between 2020 to 2100. Note that RAPID is flexible, and
both the duration of the intervals and the horizon could be modified depending on
the needs.
RAPID is mathematically formulated in compact form as follows.

min x TC ¼ ∑ i2I j [∑] 2J t [∑] 2T [C] [ijt] [x] [ijt] ð1Þ


min x NE ¼ ∑ i2I j [∑] 2J t [∑] 2T [E] [ijt] [x] [ijt] ð2Þ


A ijt x ijt ≤ W ijt 8i; j; t ð3Þ


x ijt 2 R ð4Þ


where continuous variables x ijt denote technical decisions, e.g., the area of land
devoted to a dedicated energy crop, the amount of residues exploited, thetechnologies’ capacities, the transportation flows between EU countries (of biomass
resources and CO 2 ), and the amount of electricity generated. These decisions are
optimized for every country j and time period t, considering a set of technologies i.
The model can be solved by minimizing the total cost (TC) as in Eq. 1, for a given
CDR target, or by minimizing the net emissions (NE), as in Eq. 2. C ijt are cost
parameters for each technology i in each region j and year t, while E ijt are emission
coefficients for each technology i in each region j and period t. Net CO 2 targets are
here imposed jointly on all countries by assuming a cooperative strategy, i.e.,
∑
i2I j [∑] 2J t [∑] 2T [E] [ijt] [x] [ijt] [ ≤] [α][ where][ α][ denotes the joint CDR target. Equation][ 3][ represents]

technical constraints (e.g., limits to the penetration of intermittent technologies,
demand satisfaction constraints, and technology diffusion constraints) as well as
equations that quantify the economic and CDR potential (Supplementary
Information). Parameters A ijt denote cost and emissions data as well as
technological parameters, while W ijt are parameters appearing on the right-hand
side of the constraints.
RAPID covers 15 state-of-the-art power technologies and the most prominent
CDR engineered options, including (i) conventional fossil power generation
technologies and their retrofit with CCS systems (coal and natural gas); (ii) firm
clean technologies, such as nuclear, biomass, hydropower, and geothermal; (iii)
intermittent technologies, such as wind onshore, wind offshore, solar photovoltaic
open-ground and solar photovoltaic flat roof installation; and (iv) CDR
technologies, i.e., BECCS based on six different types of biomass resources and two
DACCS technologies. RAPID models the entire supply chain of BECCS, i.e., from
the cultivation, harvesting, and pelletizing of the biomass resources to road



NATURE COMMUNICATIONS | (2021) 12:6490 | https://doi.org/10.1038/s41467-021-26680-3 | www.nature.com/naturecommunications 9


### ARTICLE NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-021-26680-3



transportation and conversion in power plants with CCS systems. The model
considers that biomass-based power technologies compete for the secondgeneration biomass resources available, including high-productivity energy crops
grown on marginal land (i.e., short-rotation woody willow and two perennial grass
sources, miscanthus, and switchgrass) and residues from agriculture and forestry
activities. Regarding DACCS, RAPID includes proven technology based on hightemperature aqueous sorbents, fully powered by natural gas or with both electricity
and heating from natural gas. The model considers the transportation of CO 2 via
pipelines and its injection in geological sites (i.e., deep saline formations, depleted
hydrocarbon fields, and coal fields). For all these technologies, the model considers
exogenous learning costs curves as well as realistic diffusion rates limiting their
deployment.
RAPID optimizes decisions at the country level taken from 2020 to 2100 (fiveyear periods) in the EU, divided into 28 countries. These temporal and spatial
scales are consistent with the scope of the analysis. Each country is modeled as a
load node with specific temporal patterns of demand and resource availability.
Distances between countries are quantified based on their centroids. Domestic
transport of biomass and CO 2 within countries considers a standard distance of
100 km. Note that BECCS and DACCS supply chains may span several countries,
e.g., miscanthus cultivated in country A, transported to country B in the form of
pellets to be combusted, and the captured CO 2 transported by pipeline and injected
in a saline aquifer in country C.
RAPID was implemented in the General Algebraic Modelling System (GAMS)
software [74] version 32.2.0 and solved with the CPLEX solver on an Intel i9-9900
CPU, 3.10 GHz computer with 32 GB RAM. RAPID features 305,314 continuous
variables and 109,068 equations; the solution time is always in the range 10120 min depending on the instance solved. The computer code supporting our
analysis is available from the corresponding author upon request. A detailed
mathematical description of the RAPID model and the underlying assumptions canbe found in Supplementary Information Section 1 on “The RAPID model”
(Supplementary Equations 1-53). Data inputs are described and presented in
Supplementary data (Supplementary Tables 5–42).


Scenario definition and solution approach. We define three different scenarios,
labelled as NOW, SLOW and LATE, differing in the starting year for the CDR
deployment. The NOW scenario considers immediate CDR action starting in 2020.
The SLOW scenario assumes that CDR action starts in 2055, while the LATE
scenario delays CDR until 2085.
We solve RAPID considering the time horizon 2020-2100, divided into 16
periodsdeployed from a particular period t of five years length each, assuming that BECCS and DACCS can only be t’ onwards. For example, when RAPID is solved
for 2050, we assume that CDR can only be deployed from 2050 to 2100, but not
before. Therefore, not deploying BECCS during inaction periods implies that
biomass residues are not mobilized, and the marginal land remains unexploited,
resulting in a CDR potential loss. In contrast, changes in the power mix can occur
at any year within the time horizon, assuming perfect foresight modeling. Power
plants and BECCS and DACCS facilities installed in period t operate until the end
of their useful life (i.e., from t to t þ UL=δ, where UL corresponds to the operating
lifetime of the technology expressed in years and δ represents the length of one
time period, i.e., five years). By running RAPID for different starting years for
CDR, we quantify the impact of delaying its deployment to various extents.
Delaying actions in time results in more constrained optimization problems, i.e.,
tighter feasible regions, as the capacities of BECCS and DACCS up to the
investigated period are fixed to zero. Consequently, the optimal solution worsens as
we solve RAPID for later starting years, as the level of flexibility in the optimization
diminishes accordingly (e.g., loss of biomass potential due to the underuse of land).
RAPID is deterministic, as it assumes that all model parameters are perfectly
known in advance. Nevertheless, we analyzed the effects of key uncertainties on our
results by defining three scenarios considering nominal, optimistic, and pessimistic
estimates (Uncertainty analysis section in Methods).


CO 2 emissions balance. A life-cycle thinking approach was followed to estimate all
the emissions throughout the life cycle of all the activities involved in the integrated
system (power mix and CDR options). We applied life cycle assessment (LCA)
principles aligned with the ISO 14040 and ISO 14044 standards [75][–][77] . We adopted a
cradle-to-gate scope where all the emissions data (E ijt in Eqs. 2 and 3) for both the
foreground system (power mix and CDR facilities) and the background system
(surrounding activities linked to the foreground system) were retrieved from
Ecoinvent v3.5 [78] (except for the crops cultivation phase, for which we used data
from the FEAT database [79] ). The Ecoinvent database distinguishes between biogenic
and fossil CO 2, both included in the life cycle inventory (LCI). The biogenic carbon
uptake and the biogenic carbon releases are often unbalanced at the level of activities
due to some allocation choices. Our integrated system consumes biomass resources
as the main feedstock for the BECCS and biomass power plants; consequently, we
need to manually adjust the CO 2 balance to quantify precisely the CO 2 removed
from the atmosphere, whose storage is ensured in the long term. This adjustment
applies as well to the DACCS, which also captures atmospheric CO 2 .
Bearing the above in mind, the biogenic carbon and the CO 2 captured with
DACCS were tracked manually to adjust the CO 2 balance. Hence, we first excluded all
the biogenic CO 2 from the LCI of all the supply chain activities, thereby considering



only the non-biogenic emissions to air (list of elementary flows in Supplementary
Information section 2.2.4). Note that omitting the biogenic carbon is a common
practice in most LCIA methods focused on climate change under the assumption that
the CO 2 uptake by biomass via photosynthesis will be eventually released again into
the air. Second, the CO 2 uptake from the atmosphere via photosynthesis or chemical
reactions is modeled as a negative flow of CO 2 entering the system. For the biomass
resources (i.e., energy crops and residues from agriculture and forestry activities), the
CO 2 uptake is estimated from the carbon and water content (Supplementary
Table 25). Finally, these negative CO 2 flows are tracked along the supply chains by
accounting for the flows leaving the system as positive flows (e.g., biomass losses,
uncaptured CO 2, or other leakages), to precisely establish the CO 2 emissions balance.
More details on the emissions data and associated references are presented in the
Supplementary Information in section 2.2.4.


Technology deployment. Diffusion rate constraints preclude solutions that are not
consistent with the deployment rates often found in similar technologies. BECCS is
constrained by both the availability of biomass resources and the maximum
deployment rate (as well as by the storage capacity). DACCS, however, is only
limited by the maximum diffusion rate (and the storage capacity). All power
technologies are constrained by the resource availability and diffusion rate, while the
model limits, in turn, the penetration of intermittent renewables to ensure the grid’s
reliability. In essence, the diffusion constraints (Supplementary Equations 24–29)
impose limits to the rate at which technologies can scale up under the assumption
that external factors constrain the speed of deployment, e.g., market forces, competition issues, infrastructure adaptation, learning rates, or social acceptance [51][,][69] .
The diffusion constraint, therefore, relates the capacity in period t to the capacity
already installed in the previous period t-1, i.e., cap [Avail] t ≤ cap [Avail] t�1 [ð][1][ þ][ γ][Þ] [δ] [ where][ γ][ is]
the maximum annual growth rate (γ 2 ½0; 1�) and δ is the duration of the period. In
RAPID, we assumed an annual growth rate of 20% (γ equals 0.2 and δ equals 5
years) based on the historical diffusion rates of energy-related technologies [69] .
According to the diffusion constraint, technologies with larger installed capacities
can diffuse faster. The maximum deployment rates of technologies over time and
their initial capacity are shown in Supplementary Fig. 4.


Uncertainty analysis. We carry out an uncertainty analysis to quantify how
uncertainties in the economic and emission parameters affect the outcome of
RAPID. In particular, we perform an a posteriori analysis of these uncertainties to
establish confidence intervals for the costs and CO 2 emissions in each solution.
Hence, besides the base case scenario considering nominal values, we define bestcase and worst-case scenarios assuming optimistic and pessimistic values for the
parameters, respectively.
Uncertainties in the inventory data are modeled based on Ecoinvent [80] . We used
Simapro v9.0 [81] to generate 1,000 samples from the probability functions of the
uncertain emissions via Monte Carlo sampling; from these samples, we defined the
optimistic and pessimistic scenarios, which consider the mean value of the
emissions parameters minus and plus two times the standard deviation,
respectively (Supplementary Table 27). For the cost uncertainty, we defined the
optimistic and pessimistic scenarios considering low and high estimates,
respectively, for the CAPEX and OPEX parameters sourced from the literature
(Supplementary Tables 10 and 11). The uncertainty analysis results for the costs are
provided in Supplementary Tables 43-50, while for the emissions, the results are
depicted in Fig. 1b with a shaded area covering the best- and worst-case scenarios.
We also performed a sensitivity analysis to study the effects of uncertainties
affecting the availabilities of marginal land and biomass residues on the results. We
defined various scenarios with increased and reduced potentials considering
different percentages over the central estimates shown in Supplementary Tables
S37 and S38 for marginal land and biomass residues (i.e., ±50, ±25%, ±10%). The
RAPID model was then solved for the said scenarios to evaluate the impact of these
uncertainties on the outcome of the optimization. The results of the sensitivity
analysis on the biomass potentials are shown in Supplementary Fig. 2.


Data availability
The data supporting the findings of this study are available within the supplementary
information document and supplementary data files.


Code availability
The computer code supporting our analysis is available from the corresponding author

upon request.


Received: 14 April 2021; Accepted: 13 October 2021;


References
1. Lawrence, M. G. et al. Evaluating climate geoengineering proposals in the context
of the Paris Agreement temperature goals. Nat. Commun. 9, 1–19 (2018).



10 NATURE COMMUNICATIONS | (2021) 12:6490 | https://doi.org/10.1038/s41467-021-26680-3 | www.nature.com/naturecommunications


### NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-021-26680-3 ARTICLE



2. IPCC. Global Warming of 1.5 °C: An IPCC Special Report on the Impacts of
Global Warming of 1.5 °C Above Pre-industrial Levels and Related Global
Greenhouse Gas Emission Pathways, in the Context of Strengthening the Global
Response to the Threat of Climate Chang. (Intergovernmental Panel on
Climate Change, 2018).
3. Rogelj, J. et al. Scenarios towards limiting global mean temperature increase
below 1.5 C. Nat. Clim. Chang. 8, 325 (2018).
4. Peters, G. P. et al. Carbon dioxide emissions continue to grow amidst slowly
emerging climate policies. Nat. Clim. Chang. 10, 3–6 (2020).
5. Fuss, S. et al. Negative emissions-Part 2: Costs, potentials and side effects.
Environ. Res. Lett. 13, 63002 (2018).
6. Davis, S. J. et al. Net-zero emissions energy systems. Science 360, 6396 (2018).
7. National Academies of Sciences Engineering and Medicine. Negative emissions
technologies and reliable sequestration: A research agenda. (National
Academies Press, 2019).
8. Minx, J. C. et al. Negative emissions—Part 1: Research landscape and
synthesis. Environ. Res. Lett. 13, 63001 (2018).
9. Bui, M. et al. Carbon capture and storage (CCS): the way forward. Energy
Environ. Sci. 11, 1062–1176 (2018).
10. Romanov, V. et al. Mineralization of carbon dioxide: a literature review.
ChemBioEng Rev. 2, 231–256 (2015).
11. van Vuuren, D. P. et al. Alternative pathways to the 1.5 °C target reduce the
need for negative emission technologies. Nat. Clim. Chang. 8, 391–397 (2018).
12. Hanssen, S. V. et al. The climate change mitigation potential of bioenergy with
carbon capture and storage. Nat. Clim. Chang. 10, 1023–1029 (2020).
13. Mander, S., Anderson, K., Larkin, A., Gough, C. & Vaughan, N. The role of
bio-energy with carbon capture and storage in meeting the climate mitigation
challenge: A whole system perspective. Energy Procedia 114, 6036–6043
(2017).
14. Galik, C. S. A continuing need to revisit BECCS and its potential. Nat. Clim.
Chang. 10, 2–3 (2020).
15. Heck, V., Gerten, D., Lucht, W. & Popp, A. Biomass-based negative emissions
difficult to reconcile with planetary boundaries. Nat. Clim. Chang. 8, 151–155
(2018).
16. Fuhrman, J. et al. Food–energy–water implications of negative emissions
technologies in a +1.5 °C future. Nat. Clim. Chang. 10, 920–927 (2020).
17. Zahasky, C. & Krevor, S. Global geologic carbon storage requirements of
climate change mitigation scenarios. Energy Environ. Sci. 13, 1561–1567
(2020).
18. Fasihi, M., Efimova, O. & Breyer, C. Techno-economic assessment of CO 2
direct air capture plants. J. Clean. Prod. 224, 957–980 (2019).
19. Realmonte, G. et al. An inter-model assessment of the role of direct air capture
in deep mitigation pathways. Nat. Commun. 10, 1–12 (2019).
20. Bednar, J., Obersteiner, M. & Wagner, F. On the financial viability of negative
emissions. Nat. Commun. 10, 1783 (2019).
21. Honegger, M. & Reiner, D. The political economy of negative emissions
technologies: consequences for international policy design. Clim. Policy 18,
306–321 (2018).
22. Global CCS Institute. Institute. CO2RE Facilities Database. Available at:

[https://co2re.co/FacilityData. (Accessed: 19th September 2020).](https://co2re.co/FacilityData)
23. Lenzi, D. The ethics of negative emissions. Glob. Sustain 1, 1–8 (2018). e7.
24. Fuss, S. et al. Betting on negative emissions. Nat. Clim. Chang. 4, 850 (2014).
25. Anderson, K. & Peters, G. The trouble with negative emissions. Science 354,
182–183 (2016).
26. Morrow, D. R. et al. Principles for Thinking about Carbon Dioxide Removal
in Just Climate Policy. One Earth 3, 150–153 (2020).
27. Geden, O. Policy: Climate advisers must maintain integrity. Nature 521, 27–28
(2015).
28. Kriegler, E., Edenhofer, O., Reuster, L., Luderer, G. & Klein, D. Is atmospheric
carbon dioxide removal a game changer for climate change mitigation? Clim.
Change 118, 45–57 (2013).
29. Lawrence, M. G. & Schäfer, S. Promises and perils of the Paris Agreement.
Science 364, 829–830 (2019).
30. Strefler, J. et al. Between Scylla and Charybdis: delayed mitigation narrows the
passage between large-scale CDR and high costs. Environ. Res. Lett. 13, 44015
(2018).
31. Field, C. B. & Mach, K. J. Rightsizing carbon dioxide removal. Science 356,
706–707 (2017).
32. Mace, M. J., Fyson, C. L., Schaeffer, M. & Hare, W. L. Governing large-scale
carbon dioxide removal: are we ready? Carnegie Climate Geoengineering
Governance Initiative (C2G2), November 2018, New York, US.
33. van Vuuren, D. P., Hof, A. F., van Sluisveld, M. A. E. & Riahi, K. Open
discussion of negative emissions is urgently needed. Nat. Energy 2, 902–904
(2017).
34. The Royal Society & Royal Academy of Engineering. Greenhouse Gas
Removal. (Royal Society, 2018).
35. Smith, P. et al. Biophysical and economic limits to negative CO 2 emissions.
Nat. Clim. Chang. 6, 42–50 (2016).



36. Obersteiner, M. et al. How to spend a dwindling greenhouse gas budget. Nat.
Clim. Chang. 8, 7–10 (2018).
37. Pozo, C., Galán-Martín, Á., Reiner, D. M., Mac Dowell, N. & GuillénGosálbez, G. Equity in allocating carbon dioxide removal quotas. Nat. Clim.
Chang. 10, 640–646 (2020).
38. Fyson, C. L., Baur, S., Gidden, M. & Schleussner, C.-F. Fair-share carbon
dioxide removal increases major emitter responsibility. Nat. Clim. Chang. 10,
836–841 (2020).
39. McLaren, D. P., Tyfield, D. P., Willis, R., Szerszynski, B. & Markusson, N. O.
Beyond ‘Net-Zero’: A case for separate targets for emissions reduction and
negative emissions. Front. Clim. 1, 4 (2019).
40. Luderer, G. et al. Economic mitigation challenges: how further delay closes the
door for achieving climate targets. Environ. Res. Lett. 8, 34033 (2013).
41. Sanderson, B. M. & Knutti, R. Delays in US mitigation could rule out Paris
targets. Nat. Clim. Chang. 7, 92–94 (2017).
42. Heuberger, C. F., Staffell, I., Shah, N. & Mac Dowell, N. Impact of myopic
decision-making and disruptive events in power systems planning. Nat.
Energy 3, 634–640 (2018).
43. Luderer, G., Bertram, C., Calvin, K., De Cian, E. & Kriegler, E. Implications of
weak near-term climate policies on long-term mitigation pathways. Clim.
Change 136, 127–140 (2016).
44. von Stechow, C. et al. 2 °C and SDGs: united they stand, divided they fall?
Environ. Res. Lett. 11, 34022 (2016).
45. Gambhir, A. et al. Assessing the feasibility of global long-term mitigation
scenarios. Energies 10, 89 (2017).
46. Jakob, M., Luderer, G., Steckel, J., Tavoni, M. & Monjon, S. Time to act now?
Assessing the costs of delaying climate measures and benefits of early action.
Clim. Change 114, 79–99 (2012).
47. Sanderson, B. M. & O’Neill, B. C. Assessing the costs of historical inaction on
climate change. Sci. Rep. 10, 1–12 (2020).
48. Schaeffer, M. et al. Mid-and long-term climate projections for fragmented and
delayed-action scenarios. Technol. Forecast. Soc. Change 90, 257–268 (2015).
49. Victoria, M., Zhu, K., Brown, T., Andresen, G. B. & Greiner, M. Early
decarbonisation of the European energy system pays off. Nat. Commun. 11,
1–9 (2020).

’
50. Hansen, J. et al. Young people s burden: requirement of negative CO 2
emissions. Earth Syst. Dyn. 8, 577–616 (2017).
51. Hanna, R., Abdulla, A., Xu, Y. & Victor, D. G. Emergency deployment of
direct air capture as a response to the climate crisis. Nat. Commun. 12, 368
(2021).
52. Creutzig, F. et al. The mutual dependence of negative emission technologies
and energy systems. Energy Environ. Sci. 12, 1805–1817 (2019).
53. Breyer, C. 34 - A Global Overview of Future Energy. in (ed. Letcher, T. M. B.
T.-F. E. (Third E.) 727–756 (Elsevier, 2020).
54. Solano Rodriguez, B., Drummond, P. & Ekins, P. Decarbonizing the EU
energy system by 2050: an important role for BECCS. Clim. Policy 17,
S93–S110 (2017).
55. Geden, O., Peters, G. P. & Scott, V. Targeting carbon dioxide removal in the
European Union. Clim. policy 19, 487–494 (2019).
56. Geden, O., & Schenuit, F. (2020). Unconventional mitigation: carbon dioxide
removal as a new approach in EU climate policy. (SWP Research Paper, 8/
2020). Berlin: Stiftung Wissenschaft und Politik -SWP- Deutsches Institut für
Internationale Politik und Sicherheit
57. Commission, E. A European Green Deal: striving to be the first climateneutral continent. (2019).
58. Commission, E. Proposal for a Regulation of the European Parliament and of
the Council establishing the framework for achieving climate neutrality and
amending. Regulation (EU) 2018/1999 (European Climate Law) COM(2020)
80 final 2020. (2020).
59. Schenuit, F. et al. Carbon Dioxide Removal policy in the making: Assessing
developments in 9 OECD cases. Front. Clim. 3, 7 (2021).
60. Peters, G. P. & Geden, O. Catalysing a political shift from low to negative
carbon. Nat. Clim. Chang. 7, 619 (2017).
61. Vangkilde-Pedersen, T. et al. Assessing European capacity for geological
storage of carbon dioxide–the EU GeoCapacity project. Energy Procedia 1,
2663–2670 (2009).
62. Wei, L. I. et al. Bioenergy Crops for Low Warming Targets Require Half of the
Present Agricultural Fertilizer Use. Environ. Sci. Technol. 55, 10654–10661
(2021).
63. Rogelj, J. et al. Paris Agreement climate proposals need a boost to keep
warming well below 2 °C. Nature 534, 631 (2016).
64. Creutzig, F. et al. Bioenergy and climate change mitigation: an assessment.
GCB Bioenergy 7, 916–944 (2015).
65. Röös, E. et al. Greedy or needy? Land use and climate impacts of food in 2050
under different livestock futures. Glob. Environ. Chang 47, 1–12 (2017).
66. Fajardy, M., Chiquier, S. & Mac Dowell, N. Investigating the BECCS resource
nexus: delivering sustainable negative emissions. Energy Environ. Sci. 11,
3408–3430 (2018).



NATURE COMMUNICATIONS | (2021) 12:6490 | https://doi.org/10.1038/s41467-021-26680-3 | www.nature.com/naturecommunications 11


### ARTICLE NATURE COMMUNICATIONS | https://doi.org/10.1038/s41467-021-26680-3



67. Fajardy, M. & Mac Dowell, N. Recognizing the Value of Collaboration in
Delivering Carbon Dioxide Removal. One Earth 3, 214–225 (2020).
68. Griscom, B. W. et al. Natural climate solutions. Proc. Natl Acad. Sci. 114,
11645–11650 (2017).
69. Iyer, G. et al. Diffusion of low-carbon technologies and the feasibility of longterm climate targets. Technol. Forecast. Soc. Change 90, 103–118 (2015).
70. Sagues, W. J., Park, S., Jameel, H. & Sanchez, D. L. Enhanced carbon dioxide

–
removal from coupled direct air capture bioenergy systems. Sustain. Energy
Fuels 3, 3135–3146 (2019).
[71. IEA (2020). CCUS in Power, IEA, Paris https://www.iea.org/reports/ccus-in-](https://www.iea.org/reports/ccus-in-power)

[power](https://www.iea.org/reports/ccus-in-power)
72. Galán-Martín, A. et al. Time for global action: An optimised cooperative
approach towards effective climate change mitigation. Energy Environ. Sci. 11,
572–581 (2018).
73. van Soest, H. L. et al. Net-zero emission targets for major emitting countries
consistent with the Paris Agreement. Nat. Commun. 12, 1–9 (2021).
74. Brooke, A., Kendrick, D., Meeraus, A. & Raman, R. GAMS—A User’sManual.
(GAMS Development Corporation, 1998).
75. ISO 14040. Environmental management — Life Cycle Assessment —
Principles and Framework. (2006).
76. ISO. ISO 14044. Environmental management — Life Cycle Assessment —
Requirements and guidelines. (2006).
77. Meinrenken, C. J. et al. Carbon emissions embodied in product value chains
and the role of Life cycle Assessment in curbing them. Sci. Rep. 10, 1–12
(2020).
78. Steubing, B., Wernet, G., Reinhard, J., Bauer, C. & Moreno-Ruiz, E. The
ecoinvent database version 3 (part I): overview and methodology. Int. J. Life
Cycle Assess. 21, 1269–1281 (2016).
79. Camargo, G. G. T., Ryan, M. R. & Richard, T. L. Energy Use and Greenhouse
Gas Emissions from Crop Production Using the Farm Energy Analysis Tool.
Bioscience 63, 263–273 (2013).
80. Huijbregts, M. A. J. et al. Framework for modelling data uncertainty in life
cycle inventories. Int. J. Life Cycle Assess. 6, 127 (2001).
81. Goedkoop, M., Oele, M., Leijting, J., Ponsioen, T. & Meijer, E. Introduction to
LCA with SimaPro. PRé. (2016).
[82. ESRI (2019). ArcGIS Desktop: Release 10.7.1. Redlands, CA (https://](https://desktop.arcgis.com/en/arcmap)
[desktop.arcgis.com/en/arcmap).](https://desktop.arcgis.com/en/arcmap)
83. Krzywinski, M. et al. Circos: an information aesthetic for comparative
genomics. Genome Res. 19, 1639–1645 (2009).


Acknowledgements
Á.G-M thanks the Spanish Ministry of Science, Innovation, and Universities for
the financial support through the Beatriz Galindo Program (BG20/00074). J.A.C



acknowledges financial support from the Generalitat Valenciana under project PROMETEO 064/2020.


Author contributions
Á.G.-M. conceived the research. D.V., Á.G.-M. and G.G.-G. designed the study. D.V. and
Á.G.-M. developed the model formulation, carried out the analyses, and created the
illustrations. Á.G.-M. and G.G.-G. wrote the paper. Á.G.-M., D.V., S.C., N.M.D., J.A.C.
and G.G.-G. contributed to identifying data, interpreting the results, and revising.


Competing interests
The authors declare no competing interests.


Additional information

Supplementary information The online version contains supplementary material
[available at https://doi.org/10.1038/s41467-021-26680-3.](https://doi.org/10.1038/s41467-021-26680-3)


Correspondence and requests for materials should be addressed to Gonzalo GuillénGosálbez.


Peer review information Nature Communications thanks Wil Burns and the other,
anonymous, reviewer(s) for their contribution to the peer review of this work. Peer
reviewer reports are available.


[Reprints and permission information is available at http://www.nature.com/reprints](http://www.nature.com/reprints)


Publisher’s note Springer Nature remains neutral with regard to jurisdictional claims in
published maps and institutional affiliations.


Open Access This article is licensed under a Creative Commons
Attribution 4.0 International License, which permits use, sharing,
adaptation, distribution and reproduction in any medium or format, as long as you give
appropriate credit to the original author(s) and the source, provide a link to the Creative
Commons license, and indicate if changes were made. The images or other third party
material in this article are included in the article’s Creative Commons license, unless

indicated otherwise in a credit line to the material. If material is not included in the

article’s Creative Commons license and your intended use is not permitted by statutory
regulation or exceeds the permitted use, you will need to obtain permission directly from
[the copyright holder. To view a copy of this license, visit http://creativecommons.org/](http://creativecommons.org/licenses/by/4.0/)
[licenses/by/4.0/.](http://creativecommons.org/licenses/by/4.0/)


© The Author(s) 2021



12 NATURE COMMUNICATIONS | (2021) 12:6490 | https://doi.org/10.1038/s41467-021-26680-3 | www.nature.com/naturecommunications




---

## **Supplementary information**

# **Delaying carbon dioxide removal in the European Union puts** **climate targets at risk**

**Ángel Galán-Martín** **[a,b,c,]** - **, Daniel Vázquez** **[a,d,]** - **, Selene Cobo** **[a]** **, Niall Mac Dowell** **[e,f]** **, José Antonio**
**Caballero** **[d]** **, Gonzalo Guillén-Gosálbez** **[a,*]**


_a_ _Institute for Chemical and Bioengineering, Department of Chemistry and Applied Biosciences, ETH_


_Zürich, Vladimir-Prelog-Weg 1, 8093 Zürich, Switzerland_


_b_ _Department of Chemical, Environmental and Materials Engineering, University of Jaén, Campus Las_
_Lagunillas s/n, 23071 Jaén, Spain_


_c_ _Center for Advanced Studies in Earth Sciences, Energy and Environment (CEACTEMA), University of Jaén,_


_Campus Las Lagunillas s/n, 23071 Jaén, Spaind_


_d_ _Institute of Chemical Process Engineering, University of Alicante, PO 99, E-3080 Alicante, Spain_


_e_ _Centre for Environmental Policy, Imperial College London, Exhibition Road, London SW7 1NA, UK_


_f_ _Centre for Process Systems Engineering, Imperial College London, Exhibition Road, London SW7 2AZ, UK_


- Ángel Galán-Martín and Daniel Vázquez contributed equally.


*Correspondence to: <EMAIL>


This document contains the supplementary information of the article “Delaying carbon dioxide

removal in the European Union puts climate targets at risk”, which is structured in four sections.

First, the RAPID model is described. In the second section, all the data employed are presented.

The third section provides some additional results. The fourth section discusses the main

methodological assumptions and limitations. Finally, some supplementary references are

included.


S1


# **1. RAPID model**

Our work explores the technical, economic, and environmental consequences of delaying CDR

actions. To carry out our analysis, we developed a multi-period linear programming model

named RAPID (as the acronym for RemovAl oPtImization moDel). RAPID is an energy system

model focused on integrating BECCS and DACCS into the energy sector as key engineered CDR

options to achieve the climate goals. In essence, RAPID identifies the most cost-effective

emissions pathways by simultaneously modifying the power mix and deploying BECCS and

DACCS from a particular year onwards. Although we focus on the European Union context for

our analysis, RAPID could be easily extrapolated to other regions.

The mathematical formulation of RAPID is described below. First, we present the nomenclature

(i.e., sets, parameters, and variables) and then describe the main equations.

### **1.1. Nomenclature**


1.1.1. **Sets**

Four main sets are defined:


� ≔ { _t_ : Time periods of five years}
� ≔ { _j_ : Countries}
� ≔ { _i_ : Electricity generation technologies}
� ≔ { _s_ : DACCS technologies}
� ≔ { _b_ : Types of biomass}


From these main sets, we derive the following subsets:


�� � ≔ { _t_ : Periods of inactivity}
�� � ≔ { _j_ : Countries with geological storage capacity}
�� � ≔ { _i_ : Dispatchable technologies}
�� � ≔ { _i_ : Renewable technologies}
���� � ≔ { _i_ : Technologies that include carbon capture and storage}
�� � ≔ { _b_ : Bioenergy crops}
�� � ≔ { _b_ : Biomass residues}
�� � ≔ { _i_ : Bio-based electricity technologies}


1.1.2. **Parameters**

The parameters employed in the model are shown in **Supplementary Table 1** .


_**Supplementary Table 1**_ _._ Parameters used in the model


**Distance parameters** **Description** **Units**


DC Distance between country _j_ and country _j’_ . km
�,�’

Distance between country _j_ and the
DGS km
�,�’
geological storage in country _j’_ .

DI � Distance from Russia to country _j_ km
**DAC parameters** **Description** **Units**

Heating requirements to capture 1 Gt of CO 2
HEAT � [�� ] TWh/GtCO 2
for the DACCS technology _s_ .

Electricity consumption to capture 1 Gt of
ELEC � [�� ] TWh/GtCO 2
CO 2 for DACCS technology _s_ .


S2


CO 2 capture efficiency for the heating system
HCAP %
in the DACCS plant.


CO 2 emissions factor for the supply of heating
DAC [#$%&''&()'] Gt CO 2 /TWh
from natural gas in the DACCS plant.


Life cycle CO 2 emissions intensity for the
DAC [)*$+,-�,] Gt CO 2 /TWh
natural gas powering the DACCS plant.

DAC [.&/$] Expected lifetime for DACCS technology _s_ . Time periods
**Cost parameters*** **Description** **Units**

CAPEX �� �,1 Capital cost of the DACCS configuration _s_ in B€/(Gt/yr)

period _t_ .

OPEX �,1�� Operating cost of DACCS configuration _s_ in B€/GtCO 2

period _t_ .

COST �#341 Cost of natural gas heating in country _j_ . B€/TWh

Capital cost of electricity technology _i_ in
CAPEX 5,1 B€/TW
period _t_ .

OPEX 5,1/56 Fixed operating cost of electricity technology _i_ in period _t_ . B€/TW

OPEX 5,1748 Variable operating cost of electricity technology _i_ in period _t_ . B€/TWh



FC :;5< Fuel costs of biomass type _b._ B€/Gt(db)

FC 5/>3? Fuel costs of coal, natural gas, and uranium. B€/TWh



FC :



/>3? Fuel costs of coal, natural gas, and uranium. B€/TWh



Cost for transporting natural gas and CO 2 via
COST [@5A3184B�] B€/Gt/km
pipeline.

COST [;,84B�] Biomass transport cost. B€/Gt/km (db)
COST [&B�3C1] CO 2 injection cost. B€/Gt

Capital recovery factor for technology _i_ in
CRF �,5 country _j._


                                                   CRF [�� ] Capital recovery factor for DACCS.

IF Inflation factor. 
**CO** **2** **emission**
**Description** **Units**
**parameters** **[#]**

EM �,5$?3C Life cycle COtechnology _i_ in country 2 emission intensity of electricity _j_ . Gt CO 2 /TWh

Life cycle CO 2 emission intensity of the CO 2
EM ['1<] Gt CO 2 /Gt/km
transportation and storage.

Life cycle CO 2 emission intensity for natural
EM [)*,] Gt CO 2 /Gt/km
gas transportation.

EM �,:;@ CObiomass type 2 emission intensity for the cultivation of _b_ in country _j_ . Gt CO 2 /Gt (wb)

EM :;;5< Direct emissions from burning pellets of type _b_ in a Biomass w/o CCS plant. Gt CO 2 /Gt (db)

EM :;;$ ' Direct emissions from burning pellets of type _b_ in a BECCS plant. Gt CO 2 /Gt (db)

Life cycle CO 2 emission intensity of drying and
EM [;@3] Gt CO 2 /Gt (db)
pelletizing biomass.

Life cycle CO 2 emission intensity of biomass Gt CO 2 /Gt/km
EM [;,]
pellets transportation. (wb)

**Biomass parameters** **[#]** **Description** **Units**
CC : Carbon content of biomass type _b_ . kg/kg (wb)
HUM : Water content of biomass _b_ . kg/kg (wb)


S3


BVAL ;5<I4��: Electricity conversion efficiency of Biomass TWh/Gt (db)

power plants w/o CCS per type of biomass _b_ .

BVAL ;$ ': Electricity conversion efficiency of BECCS TWh/Gt (db)

plants per type of biomass _b_ .

BREM : CO 2 removed with biomass type _b_ . GtCO 2 /Gt (db)



Yield of the biomass deployed by biomass
PY
�,:
type _b_ in country _j_ in one period.



Gt/Mha/period
(db)



LOSS [ >?]


LOSS [@3??]



Biomass losses due to poor harvest practices,
inappropriate harvest technology, and %
inadequate scheduling.

Biomass losses at the pelleting plant due to
inadequate handling and poor storage %

conditions.



**Storage parameters** **Description** **Units**



STO $?3C
�,5



CO 2 post-combustion captured in fossil-fuel
electricity technology _i_ with CCS in country _j_ GtCO 2 /TWh
(only Coal CCS and NG CCS).



STO ;5 CObased electricity technology 2 post-combustion captured with bio- _i_ . GtCO 2 /Gt (db)



4A

STO CO 2 geological storage capacity in country _j_ . GtCO 2
�

**Demand parameters** **Description** **Units**
D $?3C�,1 Electricity demand in country _j_ in period _t_ . TWh



STO
�



D $?3C�,1 Electricity demand in country _j_ in period _t_ . TWh

**Limit parameters** **Description** **Units**



LIM �#341 Upper bound on the heat generated in country _j_ . TWh/yr

GEN �,5@<1 Upper bound on the electricity generatedwith technology _i_ in country _j_ . TWh/yr



LIM <4? Limit on the capacity of technologies that use TW
� coal in country _j._

LIM )* Limit on the capacity of technologies that use TW
� natural gas in country _j._

LIM )>C?348 Limit on the nuclear power capacity in TW
�

country _j._



LIM �834 Area of marginal land available for energy Mha
� crops cultivation in country _j_ .



LIM �,:;- Availability of biomass residues of type country _j_ in one period. _b_ in Gt/period (wb)

**Capacity today**
**Description** **Units**
**parameters**

,<L4M Current capacity installed of technology _i_ in

CAP TW
�,5 country _j._



,<L4M

PARCAP
5,1



Binary parameter ( 0 if today’s capacity of
technology _i_ is still active in period _t_, 0 otherwise)



**Other parameters** **Description** **Units**



BUC



Backup coefficient denoting the minimum
capacity of dispatchable technologies
required to compensate each MW of
intermittent technologies.







CF 5,1 Capacity factor of technology _i_ in period _t._ DPER Duration of a period. y



S4


YH Hours in a year. h/yr
UL 5 Useful life of technology _i_ . period


E [.<��] Electricity transmissions losses. %/km


                                                   CAP [$/] Maximum diffusion rate of technologies
HHV [)*] Higher heating value of natural gas MJ/kg

Maximum initial DAC capacity that can be
INITIAL [�� ] Gt/yr
installed.


Maximum initial BECCS capacity that can be
INITIAL [;$ '] TW
installed.



OPEN [,3CN]



Maximum power generation capacity that
can be expanded in Europe every period for
each energy generation technology – except

BECCS – in addition to the assumed

exponential growth.



TW



*B€ stands for Billion euros, which corresponds to 10 [9] euros.


# The biomass parameters refer to either wet or dry basis, i.e., wb and db, respectively.


1.1.3. **Variables**

The variables used in the model are shown in **Supplementary Table 2** .


_**Supplementary Table 2**_ _._ Variables used in the model.

**Removal variables** **Description** **Units**



QRS

OO �,�,P


TU�VW
O �,�



Amount of CO 2 removed from the atmosphere
by DACCS in country _j_ and period _t_ and with GtCO 2
technology _s_ .



Total amount of CO 2 removed from the

atmosphere via photosynthesis (BECCS and
biomass) or through chemical reactions (DACCS)
in country _j_ in period _t_ .



GtCO 2



**Total emissions and cost**
**Description** **Units**
**variables**


SUYZ�[\

X �,� Total cost of country _j_ in period _t_ . B€

SUYZ�[\

] �,� Total emissions of country _j_ in period _t_ . GtCO 2

^U_`[ Emissions from the power sector (except BECCS)

] �,� in country _j_ and period _t._ GtCO 2

QRSSa Emissions from the DACCS system in country _j_

] �,� and period _t._ GtCO 2

bT Emissions from BECCS and Biomass in country _j_

] �,� and period _t._ GtCO 2



c�

] �,�



Sde�

] �,�



Emissions associated with the natural gas
transportation for heating in country _j_ and GtCO 2
period _t._

Emissions associated with the CO 2

transportation and injection in geological sites GtCO 2
in country _j_ and period _t._



^U_`[ Costs from the power sector (except BECCS) in

X �,� country _j_ and period _t._ GtCO 2


QRSSa

X �,� Costs of DACCS in country _j_ and period _t._ GtCO 2



S5


bT Costs of BECCS and Biomass in country _j_ and

X �,� period _t._ GtCO 2



c�

X �,�



Sde�

X �,�



Costs associated with the natural gas
transportation from Russia (for heating) in GtCO 2
country _j_ and period _t._

Costs associated with the CO 2 transportation
and injection in geological sites in country _j_ and GtCO 2
period _t._



**Electricity generation**
**Description** **Units**
**and capacity variables**

f]g �,�TU�VW Amount of electricity generated in country period _t_ . _j_ in TWh

f]g �,�,�T`hc Amount of electricity generated in country using technology _i_ in period _t_ . _j_ TWh

f]g �,�,�aT Amount of standard electricity produced in country _j_ with technology _i_ in period _t_ . TWh

f]g �,�,�bi Amount of backup electricity produced in country _j_ using technology _i_ in period _t_ . TWh

Xjk �,�,�RlV�W Capacity available in country period _t_ . _j_ of technology _i_ in TW

mno Expansion in capacity in country _j_ with

Xjk �,�,� technology _i_ in period _t_ . TW

Xjk �,�,�aT Standard power capacity in country technology _i_ in period _t_ . _j_ of a TW

Xjk �,�,�bi Backup power capacity in country technology _i_ in period _t_ . _j_ of a TW

pjX �,P,�RlV�W Removal capacity with DACCS in country period _t_ . _j_ in GtCO 2 /yr

mno Expansion in DACCS capacity in country _j_ in

pjX �,P,� period _t_ . GtCO 2 /yr

**Heating generation**
**Description** **Units**
**variables**

ℎ]jr s`Z Heating from natural gas produced in country _j_ TWh
�,� in period _t_ .

tukvOr �,�wYPP�V Natural gas for heating imported from Russia in country _j_ and period _t._ TWh

**Transport variables** **Description** **Units**

T[VZP Electricity traded from country _j_ to country _j’_ in

f]g �,�’,� period _t_ . TWh

ℎ]jr T[VZP Heating transported from country _j_ to country _j’_ TWh
�,�’,� in period _t_ .

a�U Amount of CO 2 transported from country _j_ and

O �,�’,� stored in _j’_ in period _t_ . GtCO 2

**Area variables** **Description** **Units**

Area dedicated to grow bioenergy crop _b_ in
jO]j Mha
�,�,�
country _j_ in period _t_ .

**Biomass variables** **Description** **Units**

Wet biomass produced in country _j_ with
xy �,�,� Gt (wb)
biomass type _b_ in period _t_ .

yy �,�,�TU�VW Total pellets of biomass typecountry _j_ in period _t_ . _b_ combusted in Gt (db)



S6


yy b�UzVPP
�,�,�



Amount of pellets of biomass type _b_ combusted
in Biomass w/o CCS plants in country _j_ in period Gt (db)

_t_ .



yy �,�,�bmSSa Amount of pellets of biomass typein BECCS plants in country _j_ in period _b_ combusted _t_ . Gt (db)



Dry biomass (pellets) produced in country _j_ with
py �,�,� Gt (db)
biomass type _b_ in period _t_ .

Amount of biomass of type _b_ transported from _j_
ry �,�’,�,� _’_ Gt (db)
to _j_ in period _t_ .

**Objective variables** **Description** **Units**

Environmental objective. CO 2 emissions
vy{ [mZl] GtCO 2
balance.

vy{ [mhU] Economic objective. Total costs. B€

### **1.2. The RAPID model: mathematical formulation**


RAPID takes the form of a linear programming (LP) model, which was implemented in the
algebraic modeling system GAMS [1] version 32.2.0. RAPID features in total 305,314 continuous

variables and 109,068 equations and can be solved using standard LP solvers. We next describe

the main equations of RAPID, organized into five main blocks: load-meeting and operational

constraints, emissions-related equations, costs equations, inactivity equations, and objective

function-related equations. Variables are written in italics, while parameters are given in capital

letters.


**1.2.1. Load-meeting and operations constraints**

These constraints model the design, expansion, and operation of the power system, as well as

the generation and transmission of electricity between production and load regions.


The first equation (Eq. 1) computes the total electricity generated in country _j_ in a particular
period _t_ (f]g �,�TU�VW ) from the amount of electricity produced by each power technology _i_ in each

T`hc
country _j_ in period _t_ (f]g �,�,� ).



TU�VW

�,� �,�,�

= } f]g



∀{ ∈�, r ∈� _Eq. 1_



f]g �,�



T`hc



�∈•


Note that the electricity generated can be used for standard consumption or to provide a flexible

backup to handle the intermittency of renewables and ensure the system's reliability. The

relationship between dispatchable and non-dispatchable power technologies is explained later
in this document (Eq. 10). Notably, the electricity generated (f]g �,�,�T`hc ) is modeled as the

summation of two terms, the standard, and backup generation (f]g �,�,�aT and f]g �,�,�bi,
respectively), as shown in Eq. 2.



T`hc aT

�,�,� = f]g �,�,�



f]g �,�,�



�,�,�aT + f]g �,�,�bi



bi ∀{ ∈�, t ∈�, r ∈� _Eq. 2_



The amount of electricity generated is linked to the installed capacities through the capacity
factor parameter (CF 5 ) and the annual operating hours (YH) within each period (Eq. 3 and 4).
Regarding the standard electricity generation and capacity (represented by variables Xjk �,�,�aT and

f]g �,�,�aT, respectively in Eq. 3), we note that generation sources might sometimes operate below
their maximum capacity; consequently, Eq. 3 is imposed as an inequality. Conversely, for the


S7


backup systems, the capacity installed (Xjk �,�,�bi ) must always be active to ensure the system's
reliability as the backstop for the intermittency of the renewable (i.e., Eq. 4 is defined as an

equality constraint).



f]g �,�,�aT ≤Xjk �,�,�aT - YH · DPER · CF 5 ∀{ ∈�, t ∈�, r ∈�

f]g �,�,�bi = Xjk �,�,�bi - YH · DPER · CF 5 ∀{ ∈�, t ∈�, r ∈�



�,�,�aT ≤Xjk �,�,�aT

bi bi

�,�,� = Xjk �,�,�



f]g �,�,�



�,�,� 5 ∀{ ∈�, t ∈�, r ∈� _Eq. 3_

bi - YH · DPER · CF 5 ∀{ ∈�, t ∈�, r ∈� _Eq. 4_



Eq. 5 ensures that, for each period _t_, the domestic electricity generated in each country _j_, plus

the power flows imported from countries _j_ ’ to country _j_, minus the exported power from country

_j_ to countries _j’_ must be enough to fulfill the electricity demand. Note that the electricity demand
in each country _j_ in each period _t_ is given by both the standard electricity demand (D $.$ �,1 ) plus
the energy needed by the DACCS facilities _s_ deployed in _j_ . The latter term is estimated from the

amount of CO 2 removed in country _j_ and period _t_ with all types _s_ of DACCS (provided by the

QRS
variable OO �,�,P ) and the associated electricity requirements (ELEC ��� ).



TU�VW

f]g �,�


T[VZP ˆUPP
+ } f]g � „,�,� …1 −E - DC � „,� ‰

�Š∈‹



T[VZP
−} f]g �,� „,�

�Š∈‹



_Eq.5_



$.$

�,1 + } OO �,�,P



∀{ ∈�, r ∈�



≥D
�,1



QRS ELEC ���



P∈a


Additionally, Equation 6 limits the energy dependency on foreign energy suppliers. Accordingly,

Eq. 6 forces that at least a certain percentage of the total electricity demand in a country _j_ must

be met with electricity generated domestically (e.g., 50%, parameter ED).



TU�VW T[VZP

�,� −} f]g �,� „,�



�,�,P
≥ED •} OO



QRS ELEC ���



+ D $.$
�,1

Ž



_Eq. 6_



f]g �,�



T[VZP



� [„] ∈‹



P∈a



∀{ ∈�, r ∈�


Eq. 7 computes the capacity available for power technology _i_ in country _j_ in period _t_ (Xjk �,�,�RlV�W )

,<L4M mno
from the capacity available today (CAP �,5 ) plus the capacity expansions (Xjk �,�,� ) taking place

in time periods before _t_, in both cases considering their corresponding useful life (modeled via


,<L4M,<L4M
parameters PARCAP 5,1 and UL 5 ). Binary parameter PARCAP 5,1 in Eq.7 takes a value of one
if today’s capacity of _i_ remains open in period _t_ (details in Eq. 48 and Eq. 49), and it is zero

otherwise.



_Eq. 7_



� [„] - �

mno



RlV�W = PARCAP

�,�,� 5,1



,<L4M mno
+ } Xjk �,�,�Š



Xjk �,�,�



,<L4M
CAP
�,5



� [„]                                  - �•‘. ’ “”

∀{ ∈�, t ∈�, r ∈�


Similarly, as with the generation (Eq. 2), the total capacity available of technology _i_ in country _j_

and period _t_ is given by the summation of the standard and backup capacities (Eq. 8). The backup

capacity of the intermittent renewable technologies (not belonging to the subset _TD_ of

dispatchable technologies) is zero, as they cannot act as a backup (Eq. 9).



RlV�W aT

�,�,� = Xjk �,�,�



Xjk �,�,�



�,�,�aT + Xjk �,�,�bi



bi ∀{ ∈�, t ∈�, r ∈� _Eq. 8_



S8


Xjk �,�,�bi = 0 ∀{ ∈�, t ∉��, r ∈� _Eq. 9_


Eq. 10 ensures the system’s reliability by enforcing that the load demand is met at any time.

Under unfavorable weather conditions, the capacity available with intermittent renewable

technologies (i.e., wind onshore, wind offshore, solar PV open-ground, and solar PV rooftop

installation) is always supported by ancillary systems provided by the firm and dispatchable

technologies (i.e., the subset of technologies TD). The ratio between dispatchable and nondispatchable technologies is modeled through the backup coefficient (BUC), which ensures that

the backup is higher than a percentage of the standard capacity of the non-dispatchable

technologies (e.g., 0.5), as shown by Eq. 10.



bi

�,�,�
} Xjk

�∈TQ —



aT

�,�,�
≥BUC } Xjk

�∉TQ —



∀{ ∈�, r ∈�
_Eq. 10_



The following equations impose limits to the capacity installed with each technology _i_ in each

country _j_ and period _t_ . Eq. 11 and 12 apply to the fossil-based power technologies, i.e., coal
power w/ and w/o CCS and natural gas power w/ and w/o CCS, respectively, which compete for
the same resources (i.e., coal and natural gas). These equations impose limits on electricity

generation based on the maximum installed capacity allowed in each country, defined by
parameters LIM � <4? and LIM �)* respectively.



RlV�W RlV�W

�,SUVW,� + Xjk �,SUVWSSa,�



<4?
∀{ ∈�, r ∈� _Eq. 11_



Xjk �,SUVW,�



RlV�W ≤LIM

�,SUVWSSa,� �



RlV�W RlV�W

�,˜™VP,� + Xjk �,˜™VPSSa,�



Xjk �,˜™VP,�



RlV�W ≤LIM

�,˜™VPSSa,� �



)* ∀{ ∈�, r ∈� _Eq. 12_



Similarly, for nuclear power, Eq. 13 enforces that the capacity installed in each country _j_ and
period _t_ cannot exceed a given limit defined by parameter LIM �)>C?348 .



RlV�W ≤LIM

�,ZYhW`V[,� �



Xjk �,ZYhW`V[,�



)>C?348
∀{ ∈�, r ∈� _Eq. 13_



Eq. 14 applies to renewable technologies, excluding bio-based technologies (i.e., wind,

geothermal, hydropower, and solar, **Supplementary Table 4** ). The equation constrains the

total amount of electricity generated in country _j_ with renewable technology _i_ (set of
renewable technologies �� � ), given by the summation of both the standard and back-up
generation (f]g �,�,�T`hc in Eq.2), based on the availability of the corresponding renewable

resource in country _j_ and period _t_ (GEN �,5@<1 ). Note that for intermittent wind and solar PV,
which are not included in the subset of dispatchable technologies ��, the back-up capacity is

set to zero in Eq. 9 and, therefore, only standard generation is considered.



T`hc ≤GEN

�,�,� �,5



f]g �,�,�



@<1 DPER ∀{ ∈�, t ∈�� �, r ∈� _Eq. 14_



For the biomass-based technologies, i.e., BECCS and Biomass w/o CCS power, the maximum

generation is given by the biomass resources availability. In the case of bioenergy crops, the limit

can be defined from the marginal land available to grow crops and, in the case of residues, from

the amount of agricultural and forestry residues available from industrial activities. Hence, the


S9


electricity generated with biomass-based electricity technologies is limited by the availability of

pellets that can be produced from each type of biomass _b_ .


Eq. 15 and Eq. 16 provide the electricity generated with biomass (w/o CCS) and BECCS,
respectively, where yy �,�,�b�UzVPP and yy �,�,�bmSSa denote the mass of dry biomass (in the form of
pellets) of type _b_ burned in Biomass w/o CCS and BECCS power plants in country _j_ and period _t_,

respectively. Note that the amount of pellets that can be produced is ultimately constrained by
the availability of marginal land and biomass residues (Eq. 21 and Eq. 22, respectively).
BVAL ;5<I4��: and BVAL ;$ ': are parameters representing the yield of biomass conversion into
electricity in biomass plants w or w/o CCS (expressed in TWh per Gt of biomass measured on a
dry basis). Note that for the case of BECCS, parameter BVAL ;$ ': considers the energy penalty
linked to the CCS system (calculations in section 2.2.5, Eqs. 59 to 62).



T`hc

�,�,� �,�,�

= } yy



BVAL ;5<I4��: ∀{ ∈�, t = �tvujšš, r ∈� _Eq. 15_



f]g �,�,�



b�UzVPP



�∈b



T`hc

�,�,� �,�,�

= } yy



∀{ ∈�, t = �����, r ∈� _Eq. 16_



f]g �,�,�



bmSSa BVAL :



;$ '



�∈b


The total amount of pellets of biomass type _b_ combusted in country _j_ and period _t_ (yy �,�,�TU�VW ) is

given by the summation of the pellets consumed by the Biomass w/o CCS (yy �,�,�b�UzVPP ) and BECCS

(yy �,�,�bmSSa ) plants, as shown in Eq. 17:



TU�VW = yy

�,�,� �,�,�



yy
�,�,�



b�UzVPP + yy

�,�,� �,�,�



bmSSa ∀{ ∈�, y ∈�, r ∈ _Eq. 17_



The total mass of biomass pellets of type _b_ available to be burned in country _j_ and period _t_
(yy �,�,�TU�VW ) is given by the domestic biomass pellets used (py �,�,� measured on a dry basis) plus
the imports of pellets of type _b_ imported from countries _j’_ (to country _j_ ) minus the exports of

pellets of type _b_ from country _j_ (to countries _j’_ ), as shown in Eq. 18.



TU�VW

yy = py „
�,�,� �,�,� �,�,�,�

+ } ry

� [„] ∈‹



�,� „,�,�
−} ry

� [„] ∈‹



∀{ ∈�, y ∈�, r ∈�
_Eq. 18_



The pelletizing process consists of four main stages: pre-treatment of the raw biomass, drying,

conditioning, and pellets manufacturing. In essence, the biomass is converted from wet raw
material to dry biomass pellets (densified biomass). Hence, Eq. 19 establishes the relationship
between the dry (py �,�,� ) and wet weight biomass (xy �,�,� ) considering the moisture content of
each biomass type _b_ (HUM : ) as well as the losses during the pelleting stage due to, for example,
inadequate handling of biomass resources or poor storage conditions (LOSS [@3??], expressed as a

percentage).


py �,�,� = xy �,�,� ›1 −HUM : œ›1 −LOSS [@3??] œ ∀{ ∈�, y ∈�, r ∈� _Eq. 19_


Two types of second-generation biomass feedstocks are considered, i.e., dedicated bioenergy

crops and biomass residues. Therefore, the amount of biomass feedstock _b_ available in a country

_j_ in period _t_ (either on a wet or dry basis) is given by the energy crops cultivated on marginal

land and the residues available.


S10


For the bioenergy crops (subset �� � ), the amount of biomass growth in each country _j_ and
period _t_ (py �,�,� ) is calculated, as shown in Eq. 20, from the marginal land devoted to each
particular crop (jO]j �,�,� ) and the production yield parameter (PY �,: ). Note that we also consider
biomass losses in the cultivation phase of the bioenergy crops (LOSS [ >?], expressed as a

percentage), which may arise due to poor harvest practices, inappropriate harvest technologies,

or inadequate scheduling and timing of the agricultural activities.


xy �,�,� ›1 −HUM : œ = jO]j �,�,� PY �,: ›1 −LOSS [ >?] œ ∀{ ∈�, y ∈�� �, r ∈� _Eq. 20_


The land area used for growing bioenergy crops in each country _j_ and period _t_ is constrained by
the marginal land available in the country (LIM ��834 ) as in Eq. 21.



�,�,�
} jO]j
�∈bS 


�834 _Eq. 21_

≤LIM � ∀{ ∈�, r ∈�



Concerning the biomass residues (subset �� � ), the mass of wet biomass residues of type _b_ used
in country _j_ and period _t_ is limited by its availability in that country (LIM �,:;- ), as shown in Eq. 22.


xy �,�,� ≤LIM �,:;- ∀{ ∈�, y ∈�� �, r ∈� _Eq. 22_


Finally, Eq. 23 prevents countries from behaving as intermediate traders in biomass markets by
forcing the maximum amount of pellets exported from _j_ to _j’_ (∑ � [„] ∈‹ ry �,� „,�,� ) to be lower than
the biomass produced in the same country _j_ for every period _t_ (py �,�,� ).



py „
�,�,� �,�,�,�
−} ry

� [„] ∈‹



≥0 ∀{ ∈�, y ∈�, r ∈�
_Eq. 23_



The previous equations impose limits on the total capacities installed and the electricity

provided based on the resources available (e.g., wind resource, land). However, other factors

limit the diffusion of existing and new technologies, ultimately constraining their maximum

deployment rate. For instance, the speed of deployment may be affected by market forces,

competition, the adaptation of new infrastructure, learning rates, or social acceptance issues,
among others [2] . Accordingly, we introduced in the model a capacity expansion factor (CAP [$/] )

that imposes a maximum growth rate relative to previous periods.


Eq. 24 applies to the initial period (t = 1, e.g., 2020), which considers the initial installed capacity


,<L4M
(i.e., capacity in 2019, parameter CAP ) plus the expansion in capacity taking place in that
�,5

year. Additionally, parameter OPEN [Tech] ensures that the technologies not deployed today (e.g.,

fossil fuels + CCS) could still be implemented in the future by assuming that a minimum capacity
is already installed. Eq. 25 applies from the initial period onwards. CAP [$/] represents the

maximum annual growth rate (e.g., 20%), while DPER considers the length of the period (i.e.,

five years).



RlV�W

�,�,�
} Xjk

�∈‹



,<L4M

≤} CAP �,5

�∈‹



1 + CAP [$/] ¡ + OPEN [,3CN] ∀t ≠�����, r = 1



_Eq. 24_


S11


RlV�W

�,�,�
} Xjk

�∈‹



RlV�W

�,�,�•”
≤} Xjk

�∈‹



�@$-,3CN _Eq. 25_

1 + CAP [$/] ¡ + OPEN ∀t ≠�����, r > 1



Concerning BECCS and DACCS, their maximum diffusion rate is modeled using Eqs. 26-29, where
we consider an initial installed capacity for DACCS and BECCS (parameters INITIAL [DAC] and
INITIAL [BECCS], respectively in Eq. 26 and 28). Moreover, to model the consequences of inaction

on CDR, we assume that the deployment of DACCS and BECCS starts in the first non-inactive

period. The periods of inactivity are selected manually to control the delay in the CDR actions.

Eqs. 26 and 27 correspond to the capacity expansion of DACCS, and Eqs. 28 and 29 apply to

BECCS. Note that to explore the implications of inaction on DACCS and BECCS, we fix their

capacity to zero during the inactive periods, as explained later in the document (Eqs. 49 and 50).



RlV�W

�,P,�
} } pjX

�∈‹ P∈a



_Eq. 26_
≤INITIAL [�� ] ∀r = |��| + 1



RlV�W

�,P,�
} } pjX

�∈‹ P∈a



RlV�W

�,P,�•”
≤} } pjX

�∈‹ P∈a



�@$
1 + CAP [$/] ¡ ∀r > |��| + 1



RlV�W

�,�,�
} Xjk



≤INITIAL [;$ '] ∀t = �����, r = |��| + 1



�∈‹


RlV�W

�,�,�
} Xjk

�∈‹



RlV�W

�,�,�•”
≤} Xjk

�∈‹



�@$
1 + CAP [$/] ¡ ∀t = �����, r > |��| + 1



_Eq. 27_


_Eq. 28_


_Eq. 29_



Besides the power needs, RAPID also considers the heating requirements for the DACCS

technologies, covered by natural gas. Hence, Eq. 30 defines the natural gas balance for every
period _t_ considering that the amount of heating produced in a country _j_ (ℎ]jr �,�s`Z ), plus the


T[VZP
amount imported from countries _j’_ to country _j_ (∑ � [„] ∈‹ ℎ]jr � „,�,� ), minus the amount exported


T[VZP
to other countries _j’_ (∑ � [„] ∈‹ ℎ]jr �,� „,� ), must equal the demand. The heating demand of DACCS

is computed from the amount of CO 2 removed from the atmosphere with all the configurations

QRS
_s_ (OO �,�,P ) and their heating requirements (HEAT ���, expressed in TWh per Gt of CO 2 removed).



s`Z

�,� + } ℎ]jr � „,�,�



+ tukvOr �,�wYP�V


_Eq. 30_
∀{ ∈�, r ∈�



T[VZP

−} ℎ]jr �,� „,�



ℎ]jr
�,�



T[VZP



� [„] ∈‹



� [„] ∈‹

�,�,PQRS HEAT ���
= } OO



P∈a


Finally, Eq. 31 imposes that the heating provided in each country _j_ in each period _t_ (ℎ]jr �,�s`Z )

should not exceed the natural gas heating resources available in that country (LIM �#341 ). Note
that we consider that natural gas can be imported from Russia, assuming an unlimited supply.



s`Z ≤LIM

�,� �



ℎ]jr
�,�



#341 ∀{ ∈�, r ∈� _Eq. 31_



S12


**1.2.2. Emission-related equations**

These equations model the CO 2 balance, i.e., the life cycle emissions accounting, including the

CO 2 capture, transportation, and storage. The CO 2 emissions balance accounts for both the

positive emissions (life cycle emissions emitted to the atmosphere) and the negative ones
(removals from the atmosphere via BECCS and DACCS).


SUYZ�[\
The total positive life cycle emissions in each country _j_ and period _t_ (] ) are computed in
�,�

Eq. 32 as the summation of the emissions associated with the following terms: electricity

^U_`[
generation (] �,� ), excluding those emissions linked to the biomass-based power technologies


QRSSa
for which a tailored balance is performed, the operation of the DACCS facilities (] �,� ) and the

bT c�
biomass-based technologies (] �,� ), the natural gas transportation (] �,� ) and the CO 2


Sde�
transportation and injection in geological sites (] �,� ).



SUYZ�[\
= ] �,�



c�
+ ]
�,�



bT c�
+ ]
�,�



Sde� ∀{ ∈�, r ∈� _Eq. 32_



]
�,�



^U_`[ + ]
�,�



QRSSa + ]
�,�



Eq. 33 computes the positive emissions of electricity generation for all the technologies except

for the biomass-based ones (i.e., all _i_ that do not belong to the subset _BT_ ) from the electricity
generated (f]g �,�,�T`hc ) and the life cycle emissions intensity (EM �,5$?3C ).



^U_`[ = ∑ �∉bT — f]g �,�,�



] �,�



�∉bT — f]g �,�,�T`hc EM �,5$?3C ∀{ ∈�, r ∈T _Eq. 33_



T`hc EM

�,�,� �,5



The positive emissions attributed to the DACCS facilities installed in each country _j_ and period _t_

QRSSa
(] �,� ) are computed with Eq. 34 from the emissions related to natural gas extraction and the
direct emissions from natural gas combustion not captured in the DACCS facility (determined
from the total emissions DAC [#$%&''&()'], considering the heating requirements, HEAT � [�� ], and
a specific capture efficiency value, HCAP, e.g., 90%).



QRSSa
�,�,P
= } OO



] �,�



QRS HEAT ���



DAC [)*$+,-�,]



P∈a



�,�,PQRS HEAT ��� DAC #$%&''&()' ›1 −HCAPœ
+ } OO

P∈a

∀{ ∈�, r ∈�



_Eq. 34_



The positive emissions from the biomass-based technologies in each country _j_ and period _t_ are

linked to their supply chain activities (Eq. 35). First, the emissions during the
production/cultivation phase of biomass type _b_ are computed considering the amount of wet
biomass produced (xy �,�,� ), together with the emission intensity associated with the crop
production (EM �,:;@ ). Second, the emissions of biomass conversion into pellets are obtained from
the emissions intensity of the pelletizing step (EM [;@3] ) and the biomass processed. The pellets

can be used domestically (in the same country) or transported abroad. The amount of pellets of
type _b_ consumed within a country is, hence, provided by its domestic production (py �,�,� ) minus
the exports to other countries _j’_ (ry �,� „,�,� ). The emissions due to local transportation are
computed considering a constant internal distance from the pelleting to the power plants (DC �,� )
and a given emissions intensity for road transportation via trucks (EM [;,] ). Finally, the emissions
balance considers also the direct emissions at the bio-based power plants (Biomass w/o CCS and


S13


BECCS), computed from the mass of pellets burnt (yy �,�,�b�UzVPP and yy �,�,�bmSSa ) and the post
combustion direct emissions at the plant (EM :;;5< and EM :;;$ ' ).



bT EM ;@
= } xy �,�,� �,:



EM [;@3]
�,�,�
+ } py

�∈b



] �,�



;@



�∈b



„ EM [;,]
�,�,� �,�,�,� �,�
−} ry
+ } ¥py �∈b � [„] ∈‹ ¦ DC



_Eq. 35_



„ DC „ EM [;,]
+ } } ry �,�,�,� �,�

� [„] ∈‹ �∈b



b�UzVPP
�,�,�
+ } yy

�∈b



EM :;;5< �,�,�bmSSa
+ } yy

�∈b



EM :;;$ '



∀{ ∈�, r ∈�


Eq. 36 determines the emissions associated with the transportation of natural gas to cover the

heating needs of DACCS. These are calculated from the amount of natural gas imported from
Russia (tukvOr �,�wYPP�V ), estimated considering the natural gas higher heating value (HHV )* ), the
distance between countries (DI � ) and the emission intensity associated with transportation via
pipelines (EM [)*,] ). Note that natural gas power technologies (w/ or w/o CCS included in the

subset _NG_ ) also consume natural gas as feedstock; however, the life cycle emissions associated
with this fossil feedstock are already accounted for in the electricity generation equation (Eq.33).



wYPP�V



] �,�c� =



tukvOr �,�



DI � EM [)*,] ∀{ ∈�, r ∈� _Eq. 36_
HHV [)*]



Finally, Eq. 37 provides the emissions associated with the transportation and injection of the

Sde�
captured CO 2 (] �,� ). These emissions are determined from the total amount of CO 2 captured


a�U
at the BECCS, DACCS, and fossil-fuel power plants with CCS (O �,� „,� ), the CO 2 transportation

distance from the capture point to the geological sites (DGS �,� „ ) and the emissions intensity
parameter (EM ['1<] ).



Sde� = } O �,�a�U „,�



_Eq. 37_



] �,�



a�U '1<

�,� „,� DGS �,� „ EM ∀{ ∈�, r ∈�



� [„] ∈sa §„


TU�VW
The total amount of CO 2 removed from the atmosphere (O �,� ) is computed from Eq. 38 as the
summation of the CO 2 removed from DACCS and BECCS, modeled as a negative entry in the

QRS
system (minus sign in Eq. 51). Variable OO �,�,P denotes the CO 2 captured via a chemical reaction
in the DACCS plants in each country _j_ and period _t_ and with each technology _s_ . The CO 2 uptake

by the biomass via photosynthesis during its growth is calculated from the biomass types _b_
produced in the country (xy �,�,� ) and their CO 2 uptake per mass of biomass type _b_ (parameter
BREM : ).



TU�VW
�,�,P
= } OO



�,�,� BREM :
+ } xy

�∈b



∀{ ∈�, r ∈� _Eq. 38_



O �,�



QRS



P∈a



Similarly, the total amount of CO 2 stored in country _j_ in period _t_ is given by Eq. 39. This equation

considers the CO 2 captured in all the facilities, i.e., the DACCS plants, the biogenic CO 2 captured

at the BECCS plants, and the fossil CO 2 captured at the coal and natural gas power plants with


S14


CCS, as well as the CO 2 traded from other countries _j’_ . The CO 2 captured at the DACCS facilities

QRS
(first addend in the equation) accounts for the CO 2 removed from the atmosphere (OO �,�,P ) and
the fossil CO 2 captured during natural gas combustion, estimated from the heating requirements
(HEAT � [�� ] ), the capture efficiency (HCAP) and the direct emissions factor (DAC [#$%&''&()'] ). The
CO 2 stored from power plants (BECCS, coal CCS and natural gas CCS) is estimated from the CO 2
captured post-combustion, using parameters STO ;: and STO $?3C�,5 . Finally, the CO 2 captured in

a�U
other countries _j’_ and traded to country _j_ to be geologically stored is provided by variable O �,� „,� .



a�U

} O �,� „,�

� [„] ∈sa §„



�,�,PQRS 1 + HEAT ���         - HCAP · DAC #$%&''&()' ¡ +
= } OO

P∈a


�,�,�bmSSa STO ;: +
+ } yy

�∈b



_Eq. 39_



+ f]g �,�,�
}



$?3C



∀{ ∈�, r ∈�



T`hc STO

�,�,� �,5



�•SUVW SSa ∨˜s SSa


Eq. 40 ensures that the total amount of captured CO 2 sent to the geological sites in country _j_


4A
cannot exceed the geological capacity in each country _j_ (STO ).
�



a�U

} } O � „,�,�

�Š∈‹ �∈T



4A

≤STO � ∀{ ∈�� � _Eq. 40_



The installed capacity of DACCS (pjX �,P,�RlV�W ) is given by the capacity expansions taking place in

mno
previous periods, as shown in Eq. 41 (pjX „ ). This available capacity limits the annual amount
�,P,�


QRS
of CO 2 removed from the atmosphere (OO �,�,P in Gt/yr), as shown in Eq. 42.



∀{ ∈�, š ∈�, r ∈� _Eq. 41_



RlV�W mno

�,P,� = } pjX �,P,� „



pjX
�,P,�



�Š•�

mno



� [„] - �•QRS [©—ª«] “”



QRS ≤pjX

�,�,P �,P,�



OO �,�,P



RlV�W DPER ∀{ ∈�, š ∈�, r ∈� _Eq. 42_



**1.2.3. Cost equations**

Similarly, as with the emissions, Eq. 43 determines the total costs in each country _j_ and period _t_

^U_`[
from the costs of power generation, excluding the biomass-based technologies (X �,� ), plus


QRSSa
the DACCS cost (X �,� ), the costs of the biomass-based technologies (Biomass w/o CCS and

bmSSa c�
BECCS) (X �,� ), and the expenditures linked to natural gas transportation (X �,� ) and CO 2


Sde�
transportation and injection in geological sites (X �,� ).



SUYZ�[\
= X �,�



c�
+ X

�,� �,�



bT c�
+ X

�,� �,�



Sde� ∀{ ∈�, r ∈� _Eq. 43_



X
�,�



^U_`[ + X

�,� �,�



QRSSa + X
�,�



Eq. 44 computes the costs of the power technologies in each county _j_ and period _t_ (excluding
the biomass-based technologies). The capital expenditures consider the expected capital
investment during the horizon (CAPEX 5,1 ), which is annualized using the capital recovery factor
(CRF �,5 )estimated considering uniform weighted average costs of capital (WACC) during the
lifetime of the technology (UL 5 ). The WACC represents the discount rate in the net present value
calculations (Eq. 55 in section 2.2.3 Cost parameters). The operational costs include the fix costs


S15


(OPEX /565,1 ) linked to the capacity installed Xjk �,�,�RlV�W (e.g., refurbishment costs) and the variable

costs (OPEX 5,1748 ). The latter are production-related costs (excluding fuel costs) that depend on

the power generated (f]g �,�,�T`hc ). Finally, the fuel costs (FC 5/>3? ) are linked to electricity

generation (f]g �,�,�T`hc ). Note that this term is zero for renewable power technologies (e.g., zero
fuel costs for wind or solar).



^U_`[ mno

�,� = } ¬CAPEX 5,1 CRF �,5 Xjk �,�,�



X �,�



mno
UL 5 DPER + OPEX 5,1



/56

5,1 Xjk �,�,�



RlV�W



�∉bT —



_Eq. 44_
∀{ ∈�, r ∈�



748

5,1 f]g �,�,�



T`hc FC 5



/>3?



+ OPEX
5,1



T`hc

�,�,� �,�,�

  - + } f]g



�∉bT —


The costs associated with the DACCS facilities (Eq. 45) include the capital expenditures, non
energy operational and maintenance costs, and the cost related to the heating requirements

from natural gas. The capital expenditures for every technology _s_ and period _t_ are based on
projections (CAPEX �,1�� ) that are annualized considering a constant capital recovery factor
(CRF [�� ] ) and the expected lifetime of the DACCS technologies (DAC [.5®3] ). The non-energy
operational expenditures (OPEX �,1�� ) include fix and variable costs (e.g., water, labor, and make
QRS
up chemicals), linked to the amount of CO 2 removed (OO �,�,P ). The variable costs related to the
natural gas consumption are calculated from the heating needs (HEAT � [�� ] ) per mass of CO 2

QRS #341
removed (OO �,�,P ) and the associated unitary cost (COST � ).



QRSSa

�,� �,1

= }¬CAPEX



QRS

�,�,P 


��

�,1 OO �,�,P



X �,�



�� pjX mno

�,1 �,P,�



mno CRF �� DAC .5®3 DPER + OPEX
�,1



P∈a


QRS

�,�,P
+ } OO



#341



_Eq. 45_
∀{ ∈�, r ∈�



�,�,PQRS HEAT ��� COST �



P∈a


The costs for the bio-based technologies (Biomass and BECCS included in the set _BT_ ) are

provided in Eq. 46, which accounts for the capital and operational expenditures, the biomass

raw material costs, and the costs associated with the transportation of pellets within and

between countries. The capital and operational expenditure are calculated as in Eq. 41, similarly

as done for the other power technologies. Here the costs of each biomass feedstock are
determined from the pellets of each type _b_ burnt at both Biomass w/o CCS and BECCS plants
(yy �,�,�TU�VW ), and the unitary costs of biomass feedstock linked to the type of biomass _b_ combusted

(FC :;5< ). Finally, the costs of biomass transportation in country _j_ and period _t_ consider the
imports from other countries _j’_ and the within-country transportation from the field to the

power plant. The former costs are computed from the amount of biomass traded from country
_j’_ to country _j_ (ry � „,�,�,� ), the distance between countries (DC � „,� ) and the unitary cost of the
transportation (COST [;,84B�] ). The latter term considers the biomass pellets produced and

consumed within the country _j_ (e.g., biomass produced minus exports), the internal distance
from the pelleting plant to the power plant (DC �,� ) and the unitary transportation cost
(COST [;,84B�] ).



bT mno

�,� = } ¬CAPEX 5,1 CRF �,5 Xjk �,�,�



X �,�



mno
UL 5 DPER + OPEX 5,1



/56

5,1 Xjk �,�,�



RlV�W



�∈bT —



_Eq. 46_


S16



748

5,1 f]g �,�,�



TU�VW FC :



;5<



+ OPEX
5,1



T`hc

�,�,� �,�,�

  - + } yy



�∈b


„ DC „ COST [;,84B�]
+ } } ry �,�,�,� �,�

� [„] ∈‹ �∈b



�,�,� �,� „,�,�
−} ry
+ } ¥py �∈b � [„] ∈‹



COST [;,84B�]
�,�
¦ DC



�∈b



� [„] ∈‹



∀{ ∈�, r ∈�


Eq. 47 provides the costs associated with the natural gas transportation from Russia to the EU
to cover the heating needs of DACCS and natural gas power plants (w/ and w/o CCS). Note that

the transportation costs of natural gas between EU countries are omitted because they are

included in the fuel costs of natural gas in Eq. 44. These costs are calculated considering the
amount of natural gas traded from Russia to the EU countries (tukvOr �,�wYPP�V ), the higher heating
value of natural gas (HHV [)*] ), the distance between countries (DI � ) and the unitary
transportation cost via pipeline (COST [)*,84B�] ).



wYPP�V



X �,�c� =



tukvOr �,�



COST [@5A3184B�] DI � ∀{ ∈�, r ∈� _Eq. 47_
HHV [)*]



Finally, Eq. 48 provides the costs in each country _j_ and period _t_ associated with the

transportation and injection of the captured CO 2 . The transportation costs consider the total

a�U
CO 2 captured from BECCS, DACCS and power plants with CCS (variable O �,� „,� ), the distance from

the capture plants to the geological sites (DGS �,� „ ) and the unitary costs of transporting CO 2 via
pipelines (COST [)*,84B�] ). The costs related to the CO 2 injection into wells consider the amount
of CO 2 to be stored (O �,�a�U „,� ) and the unitary injection cost (COST &B�3C ).



�,�Sde� = } ¬O �,�a�U „,�



∀{ ∈�, r ∈�
_Eq. 48_



X �,�



a�U „ COST @5A3184B� DGS „ + O a�U „

,� �,� �,�,�



a�U „ COST &B�3C

,�   


� [„] ∈sa §„


**1.2.4. Modeling of inactive periods**

RAPID allows us to explore the consequences of delaying the deployment of BECCS and DACCS.

Hence, Eq. 49 and Eq. 50, respectively, ensure that during inactive periods –selected by the

modeler with the set _PI_ – BECCS and DACCS cannot be deployed.



Xjk �,�,�RlV�W = 0 ∀{ ∈�, t = �����, r ∈�� � _Eq. 49_

pjX �,P,�RlV�W = 0 ∀{ ∈�, š ∈�, r ∈�� � _Eq. 50_



Xjk �,�,�



RlV�W = 0 ∀{ ∈�, š ∈�, r ∈�� � _Eq. 50_



**1.2.5. Objective functions**

RAPID maximizes the net negative emissions balance (M1) or minimizes the system’s costs to
meet a given target on net CDR (M2).


The environmental objective function –to be minimized– accounts for the net balance of CO 2

emissions in the system. In essence, the CO 2 emissions balance subtracts, from the positive life


�v¯grO°
cycle emissions in all countries _j_ and periods _t_ (] ), the CO 2 emissions removed from the
{,r


�vrj±
atmosphere, modeled as a negative entry in the system (O {,r, as determined in Eq. 38).


S17


›²1œ utg vy{ [mZl] = } } ] �,�SUYZ�[\

�∈‹ �∈T


š. r. XvgšrOjtgrš �´š. 1 −50



TU�VW

�,�
−} } O

�∈‹ �∈T



_Eq. 51_



The economic objective function (Eq. 52) quantifies the total costs of the system from the cost


�v¯grO°
in countries _j_ in all periods _t_ in 2020-2100 (X ). We also add half of the CAPEX of the plants
{,r

installed at the beginning of the horizon, assuming their age at that time already matches the

midpoint of their useful life (second addend in the equation). Note that the OPEX expenditures

of the plants already installed are also accounted for through the first term, as defined in Eqs.

43-48. Moreover, Eq. 53 imposes a target ( - ) on the net CO 2 balance to be provided by the

system, which can be either positive, negative (to deliver an amount of CDR), or zero (CO 2 
neutrality).


›²2œ utg vy{ [�Xv]

= X �v¯grO°
} } {,r



{∈� r∈�



�vpj°
�¸�
{,t

2 CAPEX i,p 1 crf j,i UL i DPER
Ž



�vpj°
�¸�
{,t



_Eq. 52_



+
} } • t∈� {∈�



š. r. vy{ [�g¿] ≤¶

_Eq. 53_
XvgšrOjtgrš �´š. 1 −51


# **2. Supplementary data**

This section provides the values of all the parameters and describes some of the modeling

assumptions.

### **2.1. Sets**


The elements of each main set are shown in **Supplementary Table 3** .


_**Supplementary Table 3**_ _._ Elements of the main sets.


**Set** **Elements**

_T_ p 1, …, p 16 .

_J_ Countries of the EU-28.

_I_ Wind onshore, Wind offshore, Hydro run-of-river, Hydro reservoir, Geothermal, Solar
photovoltaic open ground, Solar photovoltaic roof, Solar thermal parabolic, Coal, Coal +
CCS, Natural Gas, Natural Gas + CCS, Nuclear, Biomass, BECCS.
_B_ Miscanthus, Miscanthus + CCS, Switchgrass, Switchgrass + CCS, Willow, Willow + CCS,
Straw Residues, Straw Residues + CCS, Agricultural prunings, Agricultural prunings + CCS,
Forest residues, Forest residues + CCS.
_S_ Type A (only heat), Type C (electricity and heat).


S18


The subsets defined from these sets are shown in **Supplementary Table 4** .


_**Supplementary Table 4**_ _._ Elements of the subsets.


**Set** **Elements**

�� � Number of inactive periods. PI can be an empty set or comprise any number of
elements between p 1 and p 16 in consecutive order, starting with p 1 .
�� � Countries of the EU-28 with CO 2 geological storage capacity.
�� � All technologies excluding Wind Onshore, Wind Offshore, Solar photovoltaic open
ground, Solar photovoltaic roof.
�� � Wind onshore, Wind offshore, Hydro run-of-river, Hydro reservoir, Geothermal, Solar
photovoltaic open ground, Solar photovoltaic roof, Solar thermal parabolic.
�� � Miscanthus, Switchgrass, Willow.
�� � Straw residues, Agricultural prunings, Forest residues.
�� � Biomass, BECCS.

### **2.2. Data description and assumptions**


**2.2.1. Distance Parameters**

Distances are computed based on the centroids of the countries, considering their latitude and
longitude. These data, extracted from developers.google [3], are used to define the values of
parameters DC j,j', DI j and DGS j,j' . A 100 km distance within each country is assumed for domestic
consumption of biomass resources and domestic storage of CO 2 emissions (i.e., biomass
transportation from the field to the power plant, parameter DC j,j, and CO 2 transported from the
capture plant to the geological site, parameter DGS j,j ).


**2.2.2. DAC Parameters**

For the DACCS technology, the following parameters are used ( **Supplementary Table 5** ).


_**Supplementary Table 5**_ _._ DACCS parameters.


**Parameter** **Value** **Source**


Type A: 8.81 GJ/tCO 2
HEAT � [�� ] Keith et al. [4]
Type C: 5.25 GJ/tCO 2

Type A: 0 kWh/tCO 2
ELEC � [�� ] Keith et al. [4]
Type C: 366 kWh/tCO 2

HCAP 90 % Keith et al. [4]

DAC [#$%&''&()'] 4.98x10 [-3] kgCO 2 /MJ # Estimated
DAC [)*$+,-�,] 2.46x10 [-3] kgCO 2 /MJ Wernet et al. [5]

DACLIFE 30 yr Child et al. [6]

INITIAL [DAC] 1 MtCO 2 /yr

*Type A refers to the DACCS technology with only heating requirements, while Type C refers to the DACCS technology

with both heating and power requirements. Both types use an aqueous KOH sorbent.


# The CO 2 emissions released during the combustion of natural gas for heating are estimated in Eq. 54.


The initial capacity of DACCS is set to 1 Mton/yr, reflecting the current ambition of the Carbon

Engineering plant in Texas, still under construction.


Eq. 54 provides the CO 2 emissions linked to the combustion of natural gas to power DACCS
(parameter DAC [HEMISSIONS] ) from the stoichiometric relationship between CH 4 and CO 2 . Here,
MW [CH] [4] and MW [CO] [2] refer to the molecular weights of CH 4 and CO 2, respectively, and HHV [NG]
corresponds to the higher heating value of natural gas, i.e., 55.25 MJ/kg.


S19


MW [ (] [Ã]
DAC [#$%&''&()'] =

HHV [)*]       - MW [ #] [Ä]



_Eq. 54_



**2.2.3. Cost parameters**
The CAPEX values of the power technologies (CAPEX i,t ) are shown in **Supplementary Table 6** .
**Supplementary Table 7** displays the variable operating costs (OPEX i,tVar ) –excluding the costs
associated with fuel consumption, provided in **Supplementary Table** **8** for the technologies, and

in **Supplementary Table 9** for the biomass–. Besides the CAPEX data in **Supplementary Table 6**,
our sensitivity analysis considers the lower and upper bounds [7] in **Supplementary Table 10** and

**Supplementary Table 11**, respectively. We consider learning rates for the CAPEX costs as
estimated in Carlsson et al. [7] based on the technologies' installed capacity; these learning rates

affect the OPEX as well, since they are calculated as a percentage of the CAPEX. To estimate the

Levelized cost of electricity, we assume a fuel consumption rate per kWh of 0.44 kg coal, 0.19
m [3] of natural gas, and 2.46·10 [-6] kg of uranium –taken from the Ecoinvent 3.5 database– [5] . The

coal and natural gas consumption rates for the CCS scenarios assume an increase in fuel
consumption (relative to the non-CCS case) of 31.2% and 16.3%, respectively, based on ref [8] . We
assume a price of 60 2019$/ton for coal [9], 7.60 2019€/GJ for natural gas (HHV) [10], and 73.74
2018€/kg for uranium [11] . Moreover, the biomass costs are sourced from de Wit et al. [12] . Further

details on the biomass sources are given in sections 2.2.4 and 2.2.5, and in **Supplementary Table**

**25** .


The OPEX [FIX] parameter is calculated from the fixed operating costs without refurbishment
( **Supplementary Table 12** ), and the refurbishment fixed operating costs taken from the original

reference, spread over the useful life of the corresponding technology ( **Supplementary Table**

**13** ).


The costs data for the power technologies are taken from Carlsson et al. [7], except for the BECCS
costs which were obtained from Cabezzali et al.; [13] . These data are assumed to remain constant

over time. The cost parameters for those periods missing in the tables are assumed to have the

same values as those reported.


All cost data are updated to 2015, considering a 2% inflation rate. The exchange rate from dollars
to euros is 1.09 $/€.


_**Supplementary Table 6**_ _._ Capital expenditures (CAPEX i,t ) [2013€/KW] [7] _._
**Technology** **2020 (p** **1** **) 2030 (p** **3** **) 2040 (p** **5** **) 2050 (p** **6** **)**

Wind onshore 1,350 1,300 1,200 1,100

Wind offshore 2,880 2,580 2,380 2,280

Hydro run-of-river 5,600 5,620 5,620 5,620

Hydro reservoir 3,360 3,370 3,370 3,370

Geothermal 4,970 4,470 4,020 3,610

Solar photovoltaic open ground 800 640 580 520

Solar photovoltaic roof 1100 990 930 880

Solar parabolic thermal 4,500 3,800 3,500 3,400

Coal 1,600 1,600 1,600 1,600

Natural Gas 850 850 850 850

Nuclear 6,300 5,750 5,350 5,300

Coal + CCS 2,700 2,550 2,550 2,550


S20


Natural Gas + CCS 1,500 1,500 1,500 1,500

Biomass 2,620 2,330 2,060 1,830

BECCS 3,331 3,331 3,331 3,331


_**Supplementary Table 7**_ _._ Operational expenditures (OPEX [VAR] ) [2013€/KWh] [7] _._
**Technology** **OPEX** **[VAR]**

Wind onshore 0

Wind offshore 0

Hydro run-of-river 5.00x10 [-3]


Hydro reservoir 5.00x10 [-3]


Geothermal 0

Solar photovoltaic open ground 0

Solar photovoltaic roof 0

Solar parabolic thermal 8.00x10 [-3]


Coal 3.60x10 [-3]


Natural Gas 2.00x10 [-3]


Nuclear 2.50x10 [-3]


Coal + CCS 5.50x10 [-3]


Natural Gas + CCS 4.00x10 [-3]


Biomass 3.80x10 [-3]


BECCS 9.95x10 [-3]


fuel
**Supplementary** _**Table**_ **8** _**.**_ Fuel contribution to the electricity cost (FC i ) [2015€/kWh].

**Fuel** **w/o CCS with CCS**

Coal 2.17x10 [-2] 2.85x10 [-2]


Natural gas 4.95x10 [-2] 5.81x10 [-2]

Uranium 1.71x10 [-4] 
_*The fuel contribution is calculated considering the Ecoinvent activities “Electricity, high voltage {RoW}| electricity_
_production, hard coal | Cut-off, U”, “Electricity, high voltage {RoW}| electricity production, natural gas, combined_
_cycle power plant | Cut-off, U” and “Nuclear fuel element, for pressure water reactor, UO2 4.2% & MOX {GLO}| market_
_for | Cut-off, U” as well as the fuel price and the increased fuel requirement for the case of the CCS technologies_


_**Supplementary Table 9**_ _._ Fuel contribution to the biomass raw materials (FC bBio ) [2010€/kg
(db)] [12] .
**Fuel** FC bbio

Miscanthus 7.14 x10 [-2]


Switchgrass 5.93 x10 [-2]


Willow 5.99 x10 [-2]


Straw residues 6.40 x10 [-2]


Agricultural prunings 5.46 x10 [-2]


Forest residues 5.46 x10 [-2]


_**Supplementary Table 10**_ _._ Low CAPEX [2013€/kW].

**Technology** **2020** **2030 2040** **2050**

Wind onshore 1,100 1,000 900 800

Wind offshore 2,580 2,280 2,080 1,790


S21


Hydro run-of-river 2,540 2,560 2,560 2,560

Hydro reservoir 1,220 1,230 1,230 1,230

Geothermal 250 2,500 2,500 2,500

Solar photovoltaic open ground 650 520 470 420

Solar photovoltaic roof 950 850 810 760

Solar parabolic thermal 3,300 3,000 2,800 2,600

Coal 1,550 1,550 1,550 1,550

Natural Gas 700 700 700 700

Nuclear 3,850 3,650 3,400 3,350

Coal + CCS 2,340 2,210 2,210 2,210

Natural Gas + CCS 1,250 1,250 1,250 1,250

Biomass 1,540 1,350 1,190 1,040


_**Supplementary**_ _**Table**_ **11** . High CAPEX [2013€/kW].

**Technology** **2020** **2030** **2040** **2050**

Wind onshore 2,000 1,800 1,700 1,700

Wind offshore 4,270 3,970 3,470 3,270

Hydro run-of-river 8,150 8,180 8,180 8,180

Hydro reservoir 4,580 4,600 4,600 4,600

Geothermal 5,370 4,870 4,420 4,010

Solar photovoltaic open ground 900 720 650 580

Solar photovoltaic roof 1250 1120 1060 1000

Solar parabolic thermal 6,000 5,000 4,500 4,000

Coal 1,700 1,700 1,700 1,700

Natural Gas 950 950 950 950

Nuclear 7,750 7,100 6,550 6,500

Coal + CCS 3,020 2,850 2,850 2,850

Natural Gas + CCS 1,750 1,750 1,750 1,750

Biomass 3,170 2,780 2,440 2,140

Fix
_**Supplementary Table 12**_ _._ Fixed operating costs, excluding refurbishment (OPEX i,t )

[2013€/kW/yr] [7,13] _._

**Technology** **2020** **2030** **2040** **2050**

Wind onshore 32.40 28.60 22.80 18.70

Wind offshore 92.16 77.40 66.64 52.44

Hydro run-of-river 84.00 84.30 84.30 84.30

Hydro reservoir 50.40 50.55 50.55 50.55

Geothermal 79.52 80.46 80.40 79.42

Solar photovoltaic open ground 13.60 10.88 9.86 8.84

Solar photovoltaic roof 22.00 19.80 18.60 17.60

Solar parabolic thermal 180.00 152.00 140.00 136.00

Coal 40.00 40.00 40.00 40.00

Natural Gas 21.25 21.25 21.25 21.25

Nuclear 126.00 115.00 107.00 106.00

Coal + CCS 67.50 63.75 63.75 63.75

Natural Gas + CCS 37.50 37.50 37.50 37.50


S22


Biomass 47.16 41.94 37.08 32.94

BECCS 109.92 109.92 109.92 109.92


_**Supplementary Table 13**_ _._ Refurbishment fixed operating costs [2013€/kW/yr].

**Technology** **2020 (p** **1** **) 2030 (p** **3** **) 2040 (p** **5** **) 2050 (p** **6** **)**

Hydro run-of-river 168.00 168.60 168.60 168.60

Hydro reservoir 100.80 101.10 101.10 101.10

Nuclear 0 0 0 106.00

Biomass 23.58 20.97 18.54 16.47


For the DACCS cost, we use data from Keith et al. [4] and apply the learning curve from Child et
al. [6], as shown in **Supplementary Table 14** . Note that these costs omit the cost for transporting

the CO 2 via pipeline and the cost of injection into geological sites, shown in **Supplementary Table**

**18** .


DAC DAC
_**Supplementary Table 14**_ _._ Cost parameters for DACCS, CAPEX s,t [2015$/(t/yr)] and OPEX s,t

[2015$/t] [4,6] .


**Parameter** **2020** **2025** **2030** **2035** **2040** **2045** **2050**

CAPEX [DAC] ( _s=_ A) 1,146 1,016 886 757 627 497 368
CAPEX [DAC] ( _s=_ C) 694 615 537 458 380 301 223
OPEX [DAC] ( _s=_ A) 30 30 30 30 30 30 30
OPEX [DAC] ( _s=_ C) 26 26 26 26 26 26 26

*The tons refer to CO 2 removed from the atmosphere.


The cost of heating was sourced from Eurostat [10], and the data per country is shown in

**Supplementary Table 15** .


_**Supplementary Table 15**_ _._ Cost of the heating from natural gas (COST [HEAT] ) [2019€/kWh] _._

**Country** **COST** **[HEAT]**

Austria 2.64x10 [-2]

Belgium 2.19x10 [-2]

Bulgaria 2.97x10 [-2]

Cyprus 2.79x10 [-2]

Czechia 2.80x10 [-2]

Germany 2.75x10 [-2]

Denmark 2.43x10 [-2]

Spain 2.93x10 [-2]

Estonia 2.88x10 [-2]


Finland 4.69x10 [-2]


France 3.07x10 [-2]

United Kingdom 2.65x10 [-2]

Greece 2.72x10 [-2]

Hungary 2.70x10 [-2]

Ireland 3.11x10 [-2]

Italy 2.77x10 [-2]

Lithuania 2.81x10 [-2]


S23


Luxembourg 3.30x10 [-2]

Latvia 3.04x10 [-2]


Malta 2.79x10 [-2]


Netherlands 2.23x10 [-2]


Poland 3.37x10 [-2]

Portugal 3.17x10 [-2]

Romania 3.10x10 [-2]


Croatia 2.93x10 [-2]


Slovakia 3.29x10 [-2]


Slovenia 2.84x10 [-2]


Sweden 3.15x10 [-2]


The capital recovery factor parameter (CRF) can be obtained from Eq. 55:


CRF = [WACC · ›1 + WACCœ] [)] [Í]

›1 + WACCœ [)] [Í] −1



_Eq. 55_



Where WACC refers to the weighted average cost of capital and N t to the useful life in years.
We consider a WACC of 7% and the lifetime of each technology evaluated. When available,
region-specific data of the CRF was employed [6,14] as shown in **Supplementary Table 16;**

otherwise, values estimated with Eq. 55 were employed instead ( **Supplementary Table 17** ).


_**Supplementary Table 16**_ . Regionalized capital recovery factor (CRF i,j ).



**Wind**
**Country**
**onshore**



**Wind**

**offshore**



**Solar Photovoltaic and Thermal**



**onshore** **offshore** **Parabolic**


Austria 7.75x10 [-2] 9.61x10 [-2] 6.80x10 [-2]



Belgium 5.94x10 [-2] 7.45x10 [-2] 4.78x10 [-2]

Bulgaria 1.05x10 [-1] 9.61x10 [-2] 6.80x10 [-2]

Cyprus 1.05x10 [-1] 9.61x10 [-2] 6.80x10 [-2]

Czechia 8.89x10 [-2] 9.61x10 [-2] 6.80x10 [-2]

Germany 5.74x10 [-2] 7.90x10 [-2] 4.91x10 [-2]

Denmark 7.17x10 [-2] 9.21x10 [-2] 6.80x10 [-2]

Spain 1.05x10 [-1] 9.61x10 [-2] 6.80x10 [-2]

Estonia 1.03x10 [-1] 9.61x10 [-2] 6.80x10 [-2]


Finland 7.75x10 [-2] 9.61x10 [-2] 6.80x10 [-2]


France 7.17x10 [-2] 9.61x10 [-2] 6.80x10 [-2]

United Kingdom 8.01x10 [-2] 1.33x10 [-1] 6.80x10 [-2]

Greece 1.25x10 [-1] 9.61x10 [-2] 1.22x10 [-1]

Hungary 1.16x10 [-1] 9.61x10 [-2] 6.80x10 [-2]

Ireland 9.69x10 [-2] 9.61x10 [-2] 6.80x10 [-2]

Italy 8.89x10 [-2] 9.61x10 [-2] 6.80x10 [-2]

Lithuania 9.29x10 [-2] 9.61x10 [-2] 6.80x10 [-2]

Luxembourg 8.81x10 [-2] 9.61x10 [-2] 6.80x10 [-2]

Latvia 9.93x10 [-2] 9.61x10 [-2] 6.80x10 [-2]


Malta 8.81x10 [-2] 9.61x10 [-2] 6.80x10 [-2]


Netherlands 7.60x10 [-2] 1.09x10 [-1] 6.80x10 [-2]


Poland 9.93x10 [-2] 9.61x10 [-2] 6.80x10 [-2]

Portugal 8.89x10 [-2] 9.61x10 [-2] 6.80x10 [-2]

Romania 1.14x10 [-1] 9.61x10 [-2] 6.80x10 [-2]


Croatia 1.22x10 [-1] 9.61x10 [-2] 6.80x10 [-2]


S24


Slovakia 8.97x10 [-2] 9.61x10 [-2] 6.80x10 [-2]


Slovenia 1.14x10 [-1] 9.61x10 [-2] 6.80x10 [-2]


Sweden 9.05x10 [-2] 9.61x10 [-2] 6.80x10 [-2]


_**Supplementary Table 17**_ _._ CRF considering an average 7% WACC [6] _._

**Technology** **Value**
Hydro 7.12x10 [-2]

Geothermal 7.50x10 [-2]


Coal 7.50x10 [-2]


Natural Gas 7.72x10 [-2]


Nuclear 7.50x10 [-2]


Biomass 8.06x10 [-2]


BECCS 8.06x10 [-2]


DAC 8.06x10 [-2]


The remaining cost parameters are shown in **Supplementary Table 18**, including the cost for

natural gas transportation, biomass transport, injection, and the inflation rate.


_**Supplementary Table 18**_ _._ Other cost parameters.


**Parameter** **Value**

COST [PIPETRANS] 5.1710 [-2] 2010€/tkm* [15]

COST [BTRANS] 2.30x10 [-2] 2015€/tkm* [16,17]

COST [INJEC] 20.00 2015$/tCO 2 [18]


IF 2 %

*tkm is the abbreviation of ton-kilometer, i.e., transport of one ton of goods over one kilometer with a particular

transportation media.


We consider a constant inflation rate (IF) of 2% per year. This value is around the median value
for Europe between 2010 and 2020 [19] . All costs in the manuscript are given in €2015. Whenever
necessary, a conversion factor of 1.09 $/€ from 2015US$ to 2015€ was applied.


**2.2.4. Emission parameters**

The life cycle CO 2 emissions for the power technologies and biomass and CO 2 supply chain
activities are taken from the Ecoinvent v3.5 database [5] . All emissions data were sourced

considering the “Allocation at the point of substitution” (APOS) system model. The Ecoinvent
database v3.5 [5] distinguishes between biogenic and fossil CO 2 flows. The biogenic carbon uptake

and the biogenic carbon releases are often unbalanced at the level of activity due to the

allocation methods implemented.


Our reference system relies on biomass resources as the primary feedstock for the BECCS and

biomass power plants. Hence, we need to adjust the carbon balance so as to provide credits to

the CO 2 removed from the atmosphere, ensuring its long-term storage. Similarly, our work also

considers the direct removal of CO 2 from the atmosphere taking place in the DACCS plants.


Accordingly, the biogenic carbon and the CO 2 captured with DACCS were tracked manually to

carry out a tailored CO 2 balance. Hence, we first excluded all the biogenic carbon from the

inventory data in Ecoinvent to consider only the non-biogenic emissions to air. This is a common

assumption in most LCIA methods, as the CO 2 uptake by biomass via photosynthesis will be

eventually released back into the air. The CO 2 uptake from the atmosphere via photosynthesis


S25


or chemical reactions is modeled as a negative flow of CO 2 entering the system. For the biomass

resources (i.e., energy crops and residues from agriculture and forestry activities), the CO 2

uptake is estimated from the carbon and water content (see **Supplementary Table 28** ). These

CO 2 flows are tracked along the supply chains by accounting for the flows leaving the system as
positive flows (e.g., biomass losses, uncaptured CO 2, or other leakages).


Therefore, we consider only the non-biogenic emissions to air labeled in Ecoinvent v3.5 [5] as

follows:


   Carbon dioxide, from soil or biomass stock, non-urban air or from high stacks.

   Carbon dioxide, fossil, non-urban air or from high stacks.

   Carbon dioxide, fossil, unspecified.

   Carbon dioxide, fossil, urban air close to ground.

   Carbon dioxide, fossil, lower stratosphere + upper troposphere.

   Carbon dioxide, from soil or biomass stock, unspecified.


The names of the activities used in Ecoinvent v3.5 [5] are as follows:


   Wind onshore: electricity, high voltage, electricity production, wind, 1-3MW turbine,

onshore.

   Wind offshore: electricity, high voltage, electricity production, wind, 1-3MW turbine,

offshore.

   Hydro run-of-river: electricity, high voltage, electricity production, hydro, run-of-river.

   Hydro reservoir: electricity, high voltage, electricity production, hydro, reservoir, non
alpine region.

   Geothermal: electricity, high voltage, electricity production, deep geothermal.

   Solar photovoltaic open ground: electricity production, photovoltaic, 570kWp open

ground installation, multi-Si.

   Solar photovoltaic roof: electricity production, photovoltaic, 3kWp flat-roof

installation, multi-Si.

   Solar thermal parabolic: electricity, high voltage, electricity production, solar thermal

parabolic trough, 50 MW.

   Coal: electricity, high voltage, electricity production, hard coal.

   Natural Gas: electricity, high voltage, electricity production, natural gas, combined

cycle power plant.

   Nuclear: electricity, high voltage, electricity production, nuclear, pressure water

reactor.


The adjusted carbon intensity parameters for the power technologies are shown in
**Supplementary Table 19**, where “*” indicates that we considered Rest of the World (RoW) data

in the absence of region-specific data.


_**Supplementary Table 19**_ _._ Life cycle emissions of the electricity generation technologies
(EM j,iElec œ [kgCO 2 /kWh].

**Wind** **Hydro**
**Country** **Wind offshore** **Hydro reservoir**
**onshore** **run-of-river**


Austria 1.53x10 [-2] 1.42x10 [-2] - 3.97x10 [-3] 4.51x10 [-2] Belgium 1.37x10 [-2] 1.42x10 [-2] 3.97x10 [-3] 4.51x10 [-2] Bulgaria 1.53x10 [-2] 1.42x10 [-2] - 3.97x10 [-3] 4.51x10 [-2] Cyprus 2.17x10 [-2] 1.42x10 [-2] - 4.19x10 [-3] - 4.51x10 [-2] Czechia 1.71x10 [-2] 1.42x10 [-2] - 3.97x10 [-3] 4.46x10 [-2]


S26


Germany 1.69x10 [-2] 1.42x10 [-2] 3.97x10 [-3] 4.46x10 [-2]

Denmark 1.11x10 [-2] 1.42x10 [-2] 3.97x10 [-3] 4.51x10 [-2] Spain 1.26x10 [-2] 1.42x10 [-2] 3.97x10 [-3] 4.46x10 [-2]

Estonia 1.70x10 [-2] 1.42x10 [-2] 3.97x10 [-3] 4.51x10 [-2] 
Finland 1.60x10 [-2] 1.42x10 [-2] 3.97x10 [-3] 4.46x10 [-2]


France 1.37x10 [-2] 1.42x10 [-2] 3.97x10 [-3] 4.51x10 [-2] 
United
1.18x10 [-2] 1.42x10 [-2] 3.97x10 [-3] 4.51x10 [-2]          Kingdom

Greece 1.24x10 [-2] 1.42x10 [-2] - 3.97x10 [-3] 4.51x10 [-2] Hungary 1.17x10 [-2] 1.42x10 [-2] - 3.97x10 [-3] 4.51x10 [-2] Ireland 1.19x10 [-2] 1.42x10 [-2] 3.97x10 [-3] 4.51x10 [-2] Italy 1.66x10 [-2] 1.42x10 [-2] - 3.97x10 [-3] 4.51x10 [-2] Lithuania 1.14x10 [-2] 1.42x10 [-2] - 3.97x10 [-3] 4.51x10 [-2] Luxembourg 1.64x10 [-2] 1.42x10 [-2] - 3.97x10 [-3] 4.51x10 [-2] Latvia 1.63x10 [-2] 1.42x10 [-2] - 3.97x10 [-3] 4.51x10 [-2] 
Malta 1.27x10 [-2] - 1.42x10 [-2] - 4.19x10 [-3] - 4.51x10 [-2] 
Netherlands 1.31x10 [-2] 1.42x10 [-2] 3.97x10 [-3] 4.51x10 [-2] 
Poland 1.44x10 [-2] 1.42x10 [-2] 3.97x10 [-3] 4.51x10 [-2] Portugal 1.21x10 [-2] 1.42x10 [-2] 3.97x10 [-3] 4.46x10 [-2]

Romania 1.97x10 [-2] 1.42x10 [-2] - 3.97x10 [-3] 4.51x10 [-2] 
Croatia 1.50x10 [-2] 1.42x10 [-2] - 3.97x10 [-3] 4.51x10 [-2] 
Slovakia 1.37x10 [-2] 1.42x10 [-2] - 3.97x10 [-3] 4.46x10 [-2]


Slovenia 1.27x10 [-2] - 1.42x10 [-2] - 3.97x10 [-3] 4.51x10 [-2] 
Sweden 1.43x10 [-2] 1.42x10 [-2] - 3.97x10 [-3] 4.46x10 [-2]



**Solar photovoltaic**
**Country** **Geothermal**
**open ground**



**Solar**

**photovoltaic**

**roof**



**Solar thermal**

**parabolic**



Austria 6.87x10 [-2] 8.13x10 [-2] 8.13x10 [-2] 6.14x10 [-2] Belgium 6.87x10 [-2] - 9.32x10 [-2] 9.32x10 [-2] 6.14x10 [-2] Bulgaria 6.87x10 [-2] - 5.93x10 [-2] 5.93x10 [-2] 6.14x10 [-2] Cyprus 6.87x10 [-2] - 4.58x10 [-2] 4.58x10 [-2] 6.14x10 [-2] Czechia 6.87 x10 [-2] 9.00x10 [-2] 9.00x10 [-2] 6.14x10 [-2] Germany 6.87x10 [-2] 8.44x10 [-2] 8.44x10 [-2] 6.14x10 [-2] Denmark 6.87x10 [-2] - 6.76x10 [-2] 6.76x10 [-2] 6.14x10 [-2] Spain 6.87x10 [-2] - 5.49x10 [-2] 5.48x10 [-2] 6.19x10 [-2]

Estonia 6.87x10 [-2] - 6.76x10 [-2] - 6.76x10 [-2] - 6.14x10 [-2] 
Finland 6.87x10 [-2] - 6.76x10 [-2] 6.76x10 [-2] 6.14x10 [-2] 
France 6.87x10 [-2] 6.96x10 [-2] 6.96x10 [-2] 6.14x10 [-2] 
United
6.87x10 [-2] 6.76x10 [-2] 6.14x10 [-2]          Kingdom 6.76x10 [-2]

Greece 6.87x10 [-2] - 5.81x10 [-2] 5.81x10 [-2] 6.14x10 [-2] Hungary 6.87x10 [-2] 6.76x10 [-2] 6.76x10 [-2] 6.14x10 [-2] Ireland 6.87x10 [-2] - 6.76x10 [-2] 6.76x10 [-2] 6.14x10 [-2] Italy 6.87x10 [-2] 6.28x10 [-2] 6.28x10 [-2] 6.14x10 [-2] Lithuania 6.87x10 [-2] 8.28x10 [-2] 8.28x10 [-2] 6.14x10 [-2] Luxembourg 6.87x10 [-2] - 6.76x10 [-2] 6.76x10 [-2] 6.14x10 [-2] Latvia 6.87x10 [-2] 6.76x10 [-2] 6.76x10 [-2] 6.14x10 [-2] 
Malta 6.87x10 [-2] - 6.76x10 [-2] 6.76x10 [-2] 6.14x10 [-2] 
Netherlands 6.87x10 [-2] - 8.28x10 [-2] 8.28x10 [-2] 6.14x10 [-2] 
Poland 6.87x10 [-2] 7.57x10 [-2] 7.57x10 [-2] 6.14x10 [-2] Portugal 6.87x10 [-2] 5.34x10 [-2] 5.34x10 [-2] 6.14x10 [-2] 

S27


Romania 6.87x10 [-2] - 6.71x10 [-2] 6.70x10 [-2] 6.14x10 [-2] 
Croatia 6.87x10 [-2] - 5.38x10 [-2] 5.38x10 [-2] 6.14x10 [-2] 
Slovakia 6.87x10 [-2] - 6.91x10 [-2] 6.91x10 [-2] 6.14x10 [-2] 
Slovenia 6.87x10 [-2] - 6.34x10 [-2] 6.34x10 [-2] 6.14x10 [-2] 
Sweden 6.87x10 [-2] - 8.48x10 [-2] 8.48x10 [-2] 6.14x10 [-2] 
**Country** **Coal** **Natural Gas** **Nuclear**
Austria 8.87x10 [-1] 4.40x10 [-1] 1.01x10 [-2] Belgium 1.02 3.78x10 [-1] 1.01x10 [-2]

Bulgaria 1.77 3.61x10 [-1] 1.01x10 [-2]

Cyprus 1.00* 3.47x10 [-1] 1.01x10 [-2] Czechia 1.11 4.23x10 [-1] 1.01x10 [-2]

Germany 9.78x10 [-1] 3.90x10 [-1] 9.27x10 [-3]

Denmark 1.00* 4.10x10 [-1] - 1.01x10 [-2] Spain 1.07 4.45x10 [-1] 1.01x10 [-2]

Estonia 1.05 4.10x10 [-1] - 1.01x10 [-2] 
Finland 9.48x10 [-1] 7.26x10 [-1] 1.01x10 [-2]


France 9.97x10 [-1] 5.05x10 [-1] 9.53x10 [-3]


United
9.88x10 [-1] 3.43x10 [-1] 1.01x10 [-2]
Kingdom

Greece 1.00* 5.10x10 [-1] 1.01x10 [-2] Hungary 1.00* 5.04x10 [-1] 1.01x10 [-2]

Ireland 9.55x10 [-1] 3.65x10 [-1] 1.01x10 [-2] Italy 9.99x10 [-1] 4.16x10 [-1] 1.01x10 [-2] Lithuania 1.00* 4.10x10 [-1] - 1.01x10 [-2]

Luxembourg 1.00* 3.61x10 [-1] 1.01x10 [-2] Latvia 8.95x10 [-1] 3.61x10 [-1] 1.01x10 [-2]


Malta 1.00* 3.61x10 [-1] 1.01x10 [-2] 
Netherlands 9.28x10 [-1] 3.54x10 [-1] 1.01x10 [-2]


Poland 1.00* 3.82x10 [-1] 1.01x10 [-2] Portugal 9.93x10 [-1] 4.12x10 [-1] 1.01x10 [-2] Romania 1.00* 3.61x10 [-1] 1.24x10 [-2]


Croatia 1.04 6.72x10 [-1] 1.01x10 [-2] 
Slovakia 1.00* 4.68x10 [-1] 1.01x10 [-2]


Slovenia 1.00* 3.61x10 [-1] 1.01x10 [-2]


Sweden 1.00* 3.54x10 [-1] 1.01x10 [-2]


*The Rest of the World (RoW) dataset was used due to the activity is not available for the particular location.


To account for the CO 2 captured at fossil fuel power plants with CCS, we considered the direct
emissions of fossil plants without CCS reported in Ecoinvent v3.5 [5] and presented in

**Supplementary Table 20** . The life cycle emissions of coal and natural gas coupled with CCS,

shown in **Supplementary Table 21**, are calculated assuming a CO 2 capture rate of 90% relative

to the direct emissions without CCS and a surplus of fuel –to power the CCS system– of 31.2%
and 16.3% for coal and natural gas plants, respectively [8]


_**Supplementary Table 20**_ _._ Direct post-combustion emissions of fossil-based electricity
technologies [kgCO 2 /kWh].

**Country** **Coal** **Natural Gas**
Austria 8.04x10 [-1] 3.64x10 [-1]

Belgium 9.35x10 [-1] 3.35x10 [-1]

Bulgaria 1.62 3.24x10 [-1]


S28


Cyprus 0.96* 3.24x10 [-1]

Czechia 1.00 3.59x10 [-1]

Germany 8.90x10 [-1] 3.41x10 [-1]

Denmark 0.96* 0.38*
Spain 9.28x10 [-1] 3.78x10 [-1]

Estonia 9.58x10 [-1] 0.38*

Finland 8.63x10 [-1] 6.18x10 [-1]


France 9.12x10 [-1] 4.50x10 [-1]

United Kingdom 8.95x10 [-1] 3.23x10 [-1]

Greece 0.96* 4.05x10 [-1]

Hungary 0.96* 4.03x10 [-1]

Ireland 8.65x10 [-1] 3.50x10 [-1]

Italy 9.10x10 [-1] 3.55x10 [-1]

Lithuania 0.96* 0.38*
Luxembourg 0.96* 3.24x10 [-1]

Latvia 8.17x10 [-1] 3.24x10 [-1]


Malta 0.96* 3.24x10 [-1]


Netherlands 8.43x10 [-1] 3.24x10 [-1]


Poland 0.96* 3.24x10 [-1]

Portugal 9.10x10 [-1] 3.70x10 [-1]

Romania 0.96* 3.24x10 [-1]


Croatia 9.40x10 [-1] 6.03x10 [-1]


Slovakia 0.96* 3.80x10 [-1]


Slovenia 0.96* 3.24x10 [-1]


Sweden 0.96* 3.24x10 [-1]


*Rest of the World (RoW) dataset was used due to the activity is not available for the particular location.


_**Supplementary Table 21**_ _._ Life cycle emission of fossil-based technologies with CCS (EM j,iElec )

[kgCO 2 /kWh]

**Country** **Coal+ CCS Natural Gas+ CCS**
Austria 2.15x10 [-1] 1.32x10 [-1]


Belgium 2.32x10 [-1] 8.99x10 [-2]

Bulgaria 4.15x10 [-1] 8.19x10 [-2]

Cyprus 1.81x10 [-1] 6.53x10 [-2]

Czechia 2.70x10 [-1] 1.18x10 [-1]


Germany 2.33x10 [-1] 9.76x10 [-2]

Denmark 1.81x10 [-1] 7.52x10 [-2]


Spain 3.10x10 [-1] 1.23x10 [-1]

Estonia 2.46x10 [-1] 7.52x10 [-2]


Finland 2.25x10 [-1] 1.99x10 [-1]


France 2.31x10 [-1] 1.17x10 [-1]


United Kingdom 2.39x10 [-1] 6.08x10 [-2]

Greece 1.81x10 [-1] 1.71x10 [-1]


Hungary 1.81x10 [-1] 1.66x10 [-1]

Ireland 2.31x10 [-1] 5.91x10 [-2]


Italy 2.36x10 [-1] 1.13x10 [-1]

Lithuania 1.81x10 [-1] 7.52x10 [-2]


Luxembourg 1.81x10 [-1] 8.19x10 [-2]

Latvia 2.10x10 [-1] 8.19x10 [-2]


S29


Malta 1.81x10 [-1] 8.19x10 [-2]


Netherlands 2.22x10 [-1] 7.27x10 [-2]


Poland 1.81x10 [-1] 1.06x10 [-1]


Portugal 2.28x10 [-1] 9.29x10 [-2]

Romania 1.81x10 [-1] 8.19x10 [-2]


Croatia 2.49x10 [-1] 1.52x10 [-1]


Slovakia 1.81x10 [-1] 1.48x10 [-1]


Slovenia 1.81x10 [-1] 8.19x10 [-2]


Sweden 1.81x10 [-1] 7.28x10 [-2]


**Supplementary Table 22** shows the emissions of transporting natural gas (EM [NGT] ), which were

sourced from the Ecoinvent activity “market for transport, pipeline, long-distance, natural gas
{RER}” [5], together with the life cycle emissions associated with the transportation and injection
of CO 2 (EM [STO] ), which were modeled from Wildbolz [20] .


_**Supplementary Table 22**_ _._ Life cycle emissions associated with the transportation and injection
of CO 2 (EM [STO] ) and the natural transportation via pipeline (EM [NG] _[T]_ ).


**Parameter** **Value**

EM [STO] 8.15×10 [-6] tCO 2 /tkm
EM [NGT] 5.24×10 [-2] kgCO 2 /tkm

*tkm is the abbreviation of ton-kilometer which is a unit representing the transport of one ton of goods over one

kilometer with a particular transportation media.


For the biomass-based power technologies, i.e., bioenergy and BECCS, we used the generic

supply chain shown in **Supplementary Fig. 1** .


Electricity



CO 2 CO 2


Growth / Production



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-29-11.png)



CO 2


Transport


|Turbine|Col2|Col3|Col4|
|---|---|---|---|
|Turbine<br>|Turbine<br>|||
|Turbine<br>|urbine|urbine|urbine|
|Turbine<br>||||
|Turbine<br>|CO2|||
|Turbine<br>|CO2|||



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-29-19.png)

Storage



Furnace





CO 2


CO 2



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-29-10.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-29-17.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-29-18.png)

Wet biomass Drying and Pelleting Dry biomass
(Pellets)



_**Supplementary Fig. 1**_ _._ _**Bioenergy with carbon capture and storage supply chain.**_ _The biomass_
_without carbon capture and storage (CCS) supply chain is analogous but lacks the furnace and_

_the CCS unit._


We assume that forestry and agricultural residues have zero emissions embodied (only their
carbon content is considered). The emissions from the cultivation of the dedicated bioenergy
crops were obtained from the Farm Energy Analysis Tool (FEAT) [21] and regionalized with the yield

shown in _**Supplementary Table 30**_ . The results provided by the FEAT database are expressed as

CO 2 -eq emissions associated with the fertilizer, lime, seed, herbicide, insecticide, fuel, and


S30



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-29-0.png)
transportation requirements associated with the growth of the specific crops. These data are

shown in **Supplementary Table 23** .


_**Supplementary Table 23**_ _._ Emissions production for different countries and crops (EM j,bBP ) [kg
CO 2 /kg (wb)]

**Country** **Miscanthus Switchgrass** **Willow**

Austria 3.41x10 [-2] 1.07x10 [-1] 4.25x10 [-2]


Belgium 4.15x10 [-2] 8.85x10 [-2] 4.49x10 [-2]

Bulgaria 4.65x10 [-2] 1.07x10 [-1] 4.76x10 [-2]

Cyprus 3.29x10 [-2] 2.74x10 [-1] 6.55x10 [-2]

Czechia 3.50x10 [-2] 1.07x10 [-1] 3.07x10 [-2]


Germany 4.62x10 [-2] 1.07x10 [-1] 4.44x10 [-2]

Denmark 5.00x10 [-2] 1.21x10 [-1] 4.99x10 [-2]


Spain 2.77x10 [-2] 2.14x10 [-1] 4.99x10 [-2]

Estonia 4.65x10 [-2] 2.03x10 [-1] 7.99x10 [-2]


Finland 3.89x10 [-2] 2.74x10 [-1] 7.99x10 [-2]


France 4.43x10 [-2] 9.83x10 [-2] 4.54x10 [-2]


United Kingdom 5.19x10 [-2] 1.07x10 [-1] 4.54x10 [-2]


Greece 2.13x10 [-2] 2.74x10 [-1] 4.00x10 [-2]


Hungary 4.65x10 [-2] 8.85x10 [-2] 4.99x10 [-2]

Ireland 4.58x10 [-2] 1.63x10 [-1] 4.65x10 [-2]


Italy 2.59x10 [-2] 8.41x10 [-2] 1.33x10 [-1]

Lithuania 4.65x10 [-2] 1.07x10 [-1] 4.44x10 [-2]


Luxembourg 3.69x10 [-2] 8.85x10 [-2] 4.54x10 [-2]

Latvia 4.65x10 [-2] 1.63x10 [-1] 7.99x10 [-2]


Malta 3.29x10 [-2] 1.07x10 [-1] 6.55x10 [-2]


Netherlands 4.43x10 [-2] 1.05x10 [-1] 4.49x10 [-2]


Poland 4.43x10 [-2] 1.07x10 [-1] 4.99x10 [-2]


Portugal 3.32x10 [-2] 1.07x10 [-1] 4.00x10 [-1]

Romania 4.15x10 [-2] 1.07x10 [-1] 4.99x10 [-2]


Croatia 3.69x10 [-2] 1.07x10 [-1] 3.63x10 [-2]


Slovakia 4.15x10 [-2] 1.07x10 [-1] 5.71x10 [-2]


Slovenia 4.15x10 [-2] 1.07x10 [-1] 4.00x10 [-2]


Sweden 4.10x10 [-2] 4.97x10 [-1] 9.99x10 [-2]


After the energy crops are harvested, the biomass feedstock is converted into pellets to facilitate

its transportation to the power plants. The emissions associated with the drying and pelleting

are obtained from the Ecoinvent activity “Wood Pellet Production” for RER (Europe). Moreover,

for biomass transportation, we assume that the pellets are transported by lorry, i.e., activity

“transport, freight, lorry > 32 metric ton, EURO3, RER, market for transport”. These two

parameters are shown in **Supplementary Table 24** .


_**Supplementary Table 24**_ _._ Life cycle emissions of the pelletizing process (EM [BPe] ) and biomass
transportation emissions via truck (EM [BT] ) [5] .


**Parameter** **Value**

EM [BPe] 9.36×10 [-2] kgCO 2 /kg (db)
EM [BT] 8.90×10 [-5] tCO 2 /tkm


S31


*tkm is the abbreviation of ton-kilometer which is a unit representing the transport of one ton of goods over one

kilometer with a particular transportation media.


Similarly, as with the fossil-fueled power plants with CCS, we assume a conservative CO 2 capture

rate of 90% considering the post-combustion capture technology with monoethanolamine
(MEA) in the BECCS power plants [13] . Note that in the case of biomass power plants without CCS,

the biogenic emissions are set to zero, assuming carbon neutrality, as explained before.


In contrast, for BECCS, we account for the CO 2 embodied in the biomass feedstock (BREM : )
modeled as a negative CO 2 input in the system. These emissions can be obtained from the
carbon content of the different biomass types (CC : ) and the molecular weights of CO 2 and
carbon (MW [ (] [Ã] and MW, respectively) as shown in Eq. 56.



BREM : = CC : - [MW] [ (] [Ã] ∀y ∈�

MW



_Eq. 56_



The carbon and moisture contents and the Higher Heating Value of the biomass types are
obtained from the Phyllis2 database [22] ( **Supplementary Table 25** ).


_**Supplementary Table 25**_ _._ Biomass parameters: Carbon content (wb) (CC b, expressed in %),
humidity (HUM b, expressed in %) and higher heating value (HHV b, in MJ/kg(db)).

**Biomass** CC b ÎÏÐ Ñ ÎÎÒ Ñ
Miscanthus 28.75 40.00 18.57

Switchgrass 37.06 11.90 16.17

Willow 24.85 50.10 19.75

Straw residues 40.88 9.19 17.85

Agricultural prunings 47.05 4.80 19.57
Forestry residues 47.05 4.80 19.57


The amount of biogenic CO 2 released to the atmosphere (not captured) in the combustion

process of the pellets is shown in **Supplementary Table 26** . The amount of carbon released by

the biomass matches the amount captured during its growth; hence, for convenience, we

modify the basis from wet biomass to dry biomass as follows:


EM :;;5< = BREM : ∀y ∈� _Eq. 57_

›1 −HUM : œ


EM :;;$ ' = ›1 −HCAPœ BREM : ∀y ∈� _Eq. 58_

›1 −HUM : œ


For the CCS case, the direct emissions are calculated considering the capture efficiency

parameter (HCAP), equal to 90%, assuming a conservative estimate.



_**Supplementary Table 26**_ _._ Direct emissions from burning pellets for different biomass types b
(EM bBBio and EM bBBECCS )[kgCO 2 /kg (db)].
**Biomass** ÓÐ ÑÔÔÕÖ ÓÐ ÑÔÔÓ××Ø



ÔÔÓ××Ø



ÑÔÔÕÖ ÓÐ Ñ



Miscanthus 1.76 0.18

Switchgrass 1.58 0.16

Willow 1.83 0.18

Straw residues 1.65 0.17



S32


Agricultural prunings 1.81 0.18
Forestry residues 1.81 0.18


We also performed a sensitivity analysis of the emissions parameters retrieved from Ecoinvent
v3.5, [5] which are affected by various uncertainty sources [23] . Accordingly, we used the Simapro
v9.0 software [24] to generate 1,000 scenarios via Monte Carlo sampling, considering the default

uncertainty data therein (i.e., parameters of the underlying probability distributions of the

uncertain emissions). We then defined the optimistic and pessimistic scenarios considering ± 2

times the standard deviation of the samples. ( **Supplementary Table 27** ).


_**Supplementary Table 27**_ _._ Standard deviation of different emissions parameters.


**Parameter** **Value**

Life cycle emissions Wind onshore 1.72×10 [-3] kgCO 2 /kWh
Life cycle emissions Wind offshore 1.64×10 [-3] kgCO 2 /kWh
Life cycle emissions Hydro run-of-river 1.88×10 [-3] kgCO 2 /kWh
Life cycle emissions Hydro reservoir 1.88×10 [-3] kgCO 2 /kWh
Life cycle emissions Geothermal 31.1×10 [-3] kgCO 2 /kWh
Life cycle emissions Solar PV open ground 12.8×10 [-3] kgCO 2 /kWh
Life cycle emissions Solar PV roof 1.39×10 [-3] kgCO 2 /kWh
Life cycle emissions Solar Thermal 5.68×10 [-3] kgCO 2 /kWh
Life cycle emissions Coal 139×10 [-3] kgCO 2 /kWh
Life cycle emissions Natural gas 9.65×10 [-3] kgCO 2 /kWh
Life cycle emissions Nuclear 2.35×10 [-3] kgCO 2 /kWh
Life cycle emissions of pelletizing process 5.79×10 [-3] kgCO 2 /kg (db)
Life cycle emissions for pellets transportion 8.14×10 [-6] tCO 2 /tkm


**2.2.5. Biomass parameters**

The uptake of CO 2 by the plant via photosynthesis during its growth is calculated with Eq. 56 and

shown in **Supplementary Table 28** .


**Supplementary Table 28** . CO 2 removal via photosynthesis for different types of biomass
(BREM : ) [kg CO 2 /kg (wb)].


**Biomass** ÔÙÓÐ Ñ

Miscanthus 1.05

Switchgrass 1.40

Willow 0.91

Straw residues 1.50

Agricultural prunings 1.73

Forest residues 1.73


The electricity delivered with the bioenergy technologies is calculated from the efficiencies of
the boiler and turbine and the HHV of the biomass (Eq. 59). The assumed efficiencies at biomassbased power plants are 72.83 % for the boiler [25] (EFF [;<5?38] ) and 31.23 % for the turbine [13]
(EFF [,>8:5B3] ). The BECCS processes incur an energy (and efficiency) penalty due to the heat
required to desorb the CO 2 from the MEA (HEAT [%$�], 0.884 kWh/kg of captured CO 2 ), and the
extra electricity needed to operate the CCS system (ELEC [@?4B1], 0.145 kWh/kg of CO 2 ), mostly
needed to compress the captured CO 2 [13] . The electricity conversion efficiency parameters
(expressed as kWh per kg of pellets combusted) for both Biomass w/o CCS and BECCS plants


S33


(BVAL ;5<I4��: and BVAL ;$ ':, respectively) are displayed in **Supplementary Table 29**, while the
associated calculations are explained in detail next.


For the Biomass w/o CCS, the electricity conversion efficiency (BVAL ;5<I4��:, expressed in kWh
per kg of dry biomass combusted) is calculated from the energy content of the biomass (HHV : )
and the efficiencies of the boiler and the turbine (EFF [;<5?38] and EFF [,>8:5B3], respectively) using

Eq. 59.


BVAL ;5<I4��: = HHV :     - EFF ;<5?38     - EFF,>8:5B3 ∀y ∈� _Eq. 59_


For the BECCS plants, we consider that one portion of the biomass input will be combusted to

#3415BÛ
cover the heating needs of the desorption process (Biomass : ), while the rest is used to
generate the electricity required to operate the CCS system (electricity penalty).


The heating required to regenerate the MEA solution (kg for heating per kg of biomass input,

#3415BÛ
Biomass : ) is calculated in Eq. 60 considering the heating needs of the MEA per mass of
CO 2 captured (HEAT [%$�] ). The amount of CO 2 captured is determined from the capture rate
(HCAP) and the CO 2 embodied in the biomass entry (carbon uptake via photosynthesis, BREM : ),
which is released during biomass combustion. Finally, the inverse of the higher heating value of
the biomass (HHV : ) provides the amount of biomass required to cover the said heating needs.



Biomass #3415BÛ = HEAT %$� HCAP BREM :
:

›1 −HUM : œ



1

HHV :



_Eq. 60_
∀y ∈�



$?3C185C51M
The remaining fraction of the biomass is used to generate electricity (Biomass :,
expressed in kg of biomass providing electricity per kg of biomass input), as shown in Eq. 61.



$?3C185C51M
= 1 −Biomass

: :



Biomass
:



#3415BÛ ∀y ∈� _Eq. 61_



Finally, the value of BVAL : for BECCS is obtained considering the power produced from the

$?3C185C51M
fraction of biomass used for electricity generation (Biomass : ), its higher heating value
(HHV : ), the efficiencies of the boiler and turbine (EFF [;<5?38] - and EFF [,>8:5B3] ), and an electricity
penalty per kg of CO 2 captured (estimated considering the parameter ELEC [@?4B1] ).



;$ ': = Biomass :



BVAL :



$?3C185C51M HHV : - EFF ;<5?38 - EFF,>8:5B3



_Eq. 62_



BREM :
−ELEC [@?4B1] HCAP ∀y ∈�
›1 −HUM : œ



_**Supplementary Table 29**_ _._ Electricity conversion efficiency for different biomass types _b_ in bot
biomass w/o CCs (BVAL Biomassb ) and BECCS plants (BVAL BECCSb ) [kWh/kg (db)].
**Biomass** ÔÒÜÝ ÔÕÖÞßààÑ ÔÒÜÝ ÔÓ××ØÑ



ÔÓ××Ø



ÔÕÖÞßààÑ ÔÒÜÝ Ñ



Miscanthus 1.17 0.63

Switchgrass 1.02 0.53

Willow 1.25 0.68

Straw residues 1.13 0.61

Agricultural prunings 1.24 0.67

Forest residues 1.24 0.67



S34


The yields of the bioenergy crops were sourced from Fajardy et al. [26] .


_**Supplementary Table 30**_ . Biomass yield per energy crop and country (PY �,: ) [t/ha/yr (db)].


**Country** **Miscanthus** **Switchgrass** **Willow**

Austria 19.50 8.00 9.40

Belgium 16.00 12.00 8.90
Bulgaria 14.30 8.00 8.40
Cyprus 20.20 2.00 6.10

Czechia 19.00 8.00 13.00

Germany 14.40 8.00 9.00

Denmark 13.30 7.10 8.00

Spain 24.00 4.00 8.00

Estonia 14.30 4.00 5.00

Finland 17.10 2.00 5.00

France 15.00 9.50 8.80

United Kingdom 12.80 8.00 8.80

Greece 31.20 2.00 10.00

Hungary 14.30 12.00 8.00

Ireland 14.50 4.00 8.60

Italy 25.70 13.60 3.00

Lithuania 14.30 8.00 9.00

Luxembourg 18.00 12.00 8.80

Latvia 14.30 4.00 5.00

Malta 20.20 8.00 6.10

Netherlands 15.00 8.30 8.90

Poland 15.00 8.00 8.00

Portugal 20.00 8.00 1.00

Romania 16.00 8.00 8.00

Croatia 18.00 8.00 11.00

Slovakia 16.00 8.00 7.00

Slovenia 16.00 8.00 10.00

Sweden 16.20 1.00 4.00


**2.2.6. Storage parameters**

The amount of CO 2 captured post-combustion using monoethanolamine (MEA) solvent at the

coal and natural gas power plants, which is stored in geological reservoirs ( **Supplementary Table**
**31** ), was calculated assuming a CO 2 capture rate of 90%, and a surplus of fuel –to cover the

energy requirements of the CCS system– of 31.2% and 16.3% for coal and natural gas,
respectively [8] .


_**Supplementary Table 31**_ _._ CO 2 post-combustion captured in Coal and Natural Gas with CCS
power plants (STO Elecj,i ) [kg CO 2 /kWh].


**Country** **Coal + CCS Natural Gas + CCS**


Austria 0.95 0.38

Belgium 1.11 0.35

Bulgaria 1.92 0.34

Cyprus 1.13 0.34


S35


Czechia 1.18 0.38

Germany 1.05 0.36

Denmark 1.13 0.41

Spain 1.10 0.40

Estonia 1.13 0.41

Finland 1.02 0.65

France 1.08 0.48

United Kingdom 1.06 0.34

Greece 1.13 0.43

Hungary 1.13 0.43

Ireland 1.02 0.37

Italy 1.08 0.38

Lithuania 1.13 0.41

Luxembourg 1.13 0.34

Latvia 0.97 0.34

Malta 1.13 0.34

Netherlands 1.00 0.34

Poland 1.13 0.34

Portugal 1.08 0.39

Romania 1.13 0.34

Croatia 1.11 0.64

Slovakia 1.13 0.40

Slovenia 1.13 0.34

Sweden 1.13 0.34


In the case of BECCS, the amount of CO 2 captured at the power plant and sent to storage is

calculated considering that 90% of the direct CO 2 emissions from the combustion of the pellets

are captured ( **Supplementary Table 32** ).


B
_**Supplementary Table 32**_ _._ CO 2 post-combustion captured for BECCS (STO i ) [kgCO 2 /kg (db)]
**Biomass** Øáâ ÔÕ

Miscanthus + CCS 1.58

Switchgrass + CCS 1.43

Willow + CCS 1.64

Straw residues + CCS 1.49

Agricultural prunings + CCS 1.63

Forest residues + CCS 1.63


The capacity available for CO 2 storage in the EU countries was sourced from the EU GeoCapacity
project [27], which considers potentials for deep saline aquifers, hydrocarbon fields, and coals

fields (except for Sweden and Finland, which did not participate in the EU GeoCapacity
project [27] ). Finland has no suitable underground fields for CO 2 long-term storage, while for
Sweden, the geological capacity was sourced from Mortensen et al. [28] . Data on geological

capacity in each country are summarized in **Supplementary Table 33** .


Cap
_**Supplementary Table 33**_ _._ CO 2 geological storage capacity for different countries (STO )
j

[GtCO 2 ].

Cap

**Country** STO
j


S36


Austria 0.00

Belgium 0.20
Bulgaria 2.12
Cyprus 0.00

Czechia 0.85

Germany 17.08

Denmark 2.76

Spain 14.18

Estonia 0.00

Finland 0.00

France 8.69

United Kingdom 14.40

Greece 0.25

Hungary 0.62

Ireland 0.00

Italy 6.55

Lithuania 0.04

Luxembourg 0.00

Latvia 0.40

Malta 0.00

Netherlands 2.34

Poland 2.94

Portugal 0.00

Romania 9.00

Croatia 2.90

Slovakia 1.72

Slovenia 0.09

Sweden 3.40


**2.2.7. Demand parameters**

The electricity demand data in each country for 2020 was obtained from the EU statistical
pocketbook [29] ( **Supplementary Table 34** ).


_**Supplementary Table 34**_ _._ Electricity demand in European countries for 2020 (D Elecj,t ) [Mtoe/yr].

**Country** D Elecj,t


Austria 5.40

Belgium 7.04
Bulgaria 2.57
Cyprus 0.39

Czechia 4.93

Germany 44.62

Denmark 2.69

Spain 20.17

Estonia 0.62

Finland 6.97

France 37.56

United Kingdom 25.85

Greece 4.64

Hungary 3.31

Ireland 2.22

Italy 25.10


S37


Lithuania 0.86

Luxembourg 0.55

Latvia 0.56

Malta 0.20

Netherlands 9.08

Poland 11.68

Portugal 4.01

Romania 3.84

Croatia 1.37

Slovakia 2.22

Slovenia 1.16

Sweden 10.94


The future electricity demand was estimated based on historical data and projections [30] . In

particular, we consider an expected growth in electricity demand in 2000-2050 from 3000 TWh

to approximately 4250 TWh. Notably, RAPID considers a constant annual growth until 2100,

resulting in a yearly increment of 0.7 %.


**2.2.8. Resources potential: limit parameters**

The maximum amount of heat generated in each country is limited by the primary production
of natural gas energy in 2017, shown in **Supplementary Table 35** [29] . Unlimited availability of

natural gas is assumed for Russia.


_**Supplementary Table 35**_ _._ Upper bound on the heat from natural gas generated in each country
(LIM jHeat ) [Mtoe/yr].
**Country** LIM jHeat


Austria 1.04

Belgium 0
Bulgaria 0.07
Cyprus 0.19

Czechia 0

Germany 6.03

Denmark 4.35

Spain 0.02

Estonia 0

Finland 0

France 0.01

United Kingdom 36.02

Greece 0.01

Hungary 1.41

Ireland 2.85

Italy 4.54

Lithuania 0

Luxembourg 0

Latvia 0

Malta 0

Netherlands 33.17

Poland 3.51

Portugal 0


S38


Romania 8.52

Croatia 1.23

Slovakia 0.12

Slovenia 0.01

Sweden 0

Russia ∞


For the renewable technologies, the following data on potentials were considered
( **Supplementary Table 36** ).


Data for wind onshore and offshore, solar PV open ground and rooftop installations, and

concentrated solar power technologies were sourced from the ENSPRESO database aggregated
at the country level [31] .


For wind onshore, we considered wind conditions with capacity factors above 25% and a high
level of exclusion of surfaces for wind (EU-Wide high restrictions). Moreover, we considered

wind offshore potentials in water depth on 0-30 m, 30-60 m, with any wind conditions and EU
Wide high restrictions.


For Solar PV open ground, we considered a potential of 85 MW/km [2] (south orientation 45%) and

only non-artificial areas, assuming that 20% of the agriculture low irradiation areas and 100% of

natural non-agriculture low irradiation areas are available. For solar PV rooftop, we included

both residential and industrial areas regardless of the facade orientation (north, south, east,
west) and roof-top inclination. For Concentrated Solar Power, which competes with Solar PV
ground-mounted for the land available, we considered a potential of 85 MW/km [2] and 100% of

the available non-artificial areas with high irradiation. Note that solar PV considers low

irradiation areas and, therefore, its potential does not overlap with that of CSP power. Data for
hydropower technologies (run-of-river and reservoir) were sourced from e-highways [32] “Energy

production in Europe by country in 2050 – 100% RES”. The geothermal data were taken from
the literature [29,33–37] .


_**Supplementary Table 36**_ _._ Potential electricity production for the renewable technologies by
country (��ã �,�^U� ) [TWh].



**Wind**
**Country**
**onshore**



**Wind**

**offshore**



**Hydro**
**Hydro reservoir**
**run-of-river**



Austria 45.24 0.00 43.86 11.39

Belgium 0.30 0.00 1.77 0.00
Bulgaria 7.75 0.00 5.75 8.93
Cyprus 0.00 0.00 0.00

Czechia 13.43 0.00 2.10 1.31

Germany 57.14 4.00 24.67 0.00

Denmark 40.12 0.00 0.07 0.00

Spain 600.70 0.00 37.67 26.07

Estonia 43.05 1.00 0.33 0.00

Finland 42.32 54.00 8.88 7.81

France 423.29 12.00 56.66 33.45

UnitedKingdom 502.53 187.00 4.62 12.92

Greece 254.76 0.00 3.62 15.87

Hungary 22.33 0.00 4.61 0.00

Ireland 277.34 0.00 1.07 0.00

Italy 117.52 2.00 25.94 33.38


S39


Lithuania 129.19 1.00 1.20 0.00

Luxembourg 0.00 0.00 0.94 0.00

Latvia 99.00 19.00 3.99 0.00

Malta 0.00 0.00 0.00

Netherlands 9.91 0.00 0.75 0.00

Poland 224.01 1.00 12.02 0.00

Portugal 7.00 0.00 14.40 9.50

Romania 38.54 1.00 29.69 9.98

Croatia 9.14 1.00 3.22 8.57

Slovakia 11.26 0.00 6.55 0.00

Slovenia 0.28 0.00 8.82 0.00

Sweden 301.36 42.00 13.93 82.92



**Solar**

**Photovoltaic**

**roof**



**Country** **Geothermal**



**Solar**

**Photovoltaic open**
**ground**



**Solar Thermal**

**Parabolic**



Austria 0.00 422.94 9.95 0.00

Belgium 0.00 344.84 12.31 0.00
Bulgaria 0.00 1,291.77 10.60 0.00
Cyprus 0.00 275.30 1.67 205.98

Czechia 3.00 655.55 11.76 0.00

Germany 1.00 3,247.60 86.60 0.00

Denmark 0.00 462.56 6.18 0.00

Spain 1.00 3,389.62 63.00 10,539.06

Estonia 0.00 145.00 1.36 0.00

Finland 0.00 159.60 5.57 0.00

France 0.00 6,388.85 82.16 270.23

United

Kingdom 0.00 1,666.42 62.13 0.00

Greece 0.00 145.00 16.67 219.23

Hungary 17.00 159.60 13.26 0.00
Ireland 0.00 6,388.85 4.53 0.00
Italy 12.00 3,972.01 83.62 437.21

Lithuania 0.00 813.00 3.19 0.00

Luxembourg 0.00 14.45 0.60 0.00

Latvia 0.00 260.77 2.10 0.00

Malta 0.00 2.06 0.71 5.49

Netherlands 0.00 373.06 17.38 0.00

Poland 0.00 2,617.80 41.68 0.00
Portugal 0.20 372.91 11.87 1,264.89
Romania 0.00 2,659.00 26.75 0.00

Croatia 3.00 525.03 5.95 1.08

Slovakia 1.00 354.10 6.57 0.00

Slovenia 0.00 108.63 2.59 0.00

Sweden 0.00 328.67 9.78 0.00


The estimates for the marginal land area available for growing energy crops were sourced from
Pozo et al. (2020) [38] . The authors followed a conservative approach, using the original estimates
from Cai et al. [39] based on the most conservative scenario (i.e., scenario S1), which accounts for

soil productivity, slope, climate, and land cover conservative criteria. Then, following Fritz et
al. [40], these land estimates at the country level were further downgraded by 69%, leading to even


S40


more conservative data. Our estimates for marginal land available include at least part of the

abandoned, wasted, or idle agricultural land and some small crop fields, which alleviate issues

related to land competition with food production and other sustainability concerns.

Nevertheless, the marginal land available is highly uncertain. Sectorial competition for the

limited marginal land might arise in the future, reducing the land available for energy purposes.

Other authors argue that more land might eventually become available due to improvements in
agriculture or dietary changes [41] . Hence, to understand how uncertainties in the marginal land

available affect our results, we performed a sensitivity analysis considering different scenarios
with increased/reduced land available (details in the Methods section and results in

**Supplementary Fig. 2** ).


We note that the nuclear power capacity cannot increase with time (Supplementary Table 39)

because we do not contemplate installing additional facilities. We adopted this assumption

based on the recent emergence of phase-out plans for coal and nuclear power in Europe (e.g.,
nuclear in Germany, Belgium, or Switzerland) [42,43] . The capacity limit for coal and natural gas

power is twice the current installed capacity (Supplementary Table 39).


_**Supplementary Table 37**_ _._ Limit on the capacity of coal, natural gas (LIM jNG and LIM jCoal ), and
nuclear power plants (LIM jNuclear ) [MW] and area available (LIM jArea ) [ha ] in each country.



NG LIM

j j



Nuclear LIM

j j



Area



**Country** LIM j



Coal LIM

j j



Austria 8,030 492 0 39,796
Belgium 13,298 940 5,931 17,889
Bulgaria 2,464 8,950 2,000 166,994
Cyprus 1,478 1,478 0 952
Czechia 2,452 20,060 4,040 66,497

Germany 63,328 92,996 9,516 211,107
Denmark 3,628 7,312 0 25,962

Spain 60,532 19,122 7,117 2,127,822
Estonia 250 102 0 352,613
Finland 3,824 4,556 2,785 1,785

France 23,904 7,932 63,130 298,899
United Kingdom 75,396 17,588 9,229 480,510

Greece 9,804 7,824 0 150,344

Hungary 8,056 2,098 1,899 53,865
Ireland 8,530 1,710 0 473,295
Italy 92,644 13,326 0 156,449
Lithuania 3,420 0 0 493,481
Luxembourg 162 0 0 371
Latvia 2,220 0 0 436,784
Malta 1,076 0 0 0
Netherlands 31,140 9,262 486 28,692
Poland 4,212 61,092 0 737,129
Portugal 9,212 3,512 0 381,688
Romania 6,066 8,256 1,300 201,652

Croatia 1,486 664 0 52,259
Slovakia 2,222 1,132 1,940 48,858
Slovenia 982 1,848 696 10,510
Sweden 0 0 8,586 141,918



S41


The residues available by country and per year are shown in **Supplementary Table 38** . The

estimates for straw residues, agricultural prunings, and forestry residues implicitly consider

sustainable practices (e.g., soil conservation and biodiversity protection). Moreover, the

estimates discount other competitive uses of such residues (e.g., straw for animal bedding or
prunings for composting and firewood) [44–46] .


_**Supplementary Table 38**_ _._ Residues potential in each country (LIM j,bBR ) [t/yr (wb)]
**Country** **Straw residues** [44] **Agricultural prunings** [45] **Forest residues** [46]


Austria 1,941,413 152,247 16,921,420
Belgium 957,802 57,093 2,462,967
Bulgaria 4,003,269 767,580 3,854,125
Cyprus 0 98,326 0
Czechia 4,152,388 31,718 12,151,984

Germany 25,473,524 415,508 50,050,490
Denmark 3,727,973 19,031 1,655,068

Spain 6,174,096 13,207,451 11,746,591
Estonia 817,286 6,344 6,269,738
Finland 1,651,779 25,375 39,072,530

France 31,544,384 3,159,131 39,270,607
United Kingdom 6,062,257 47,577 7,278,024
Greece 1,258,908 2,540,626 2,200,212

Hungary 9,124,930 0 5,263,762
Ireland 157,722 0 1,869,891
Italy 9,190,886 6,556,148 11,961,415
Lithuania 1,651,779 44,405 4,631,995
Luxembourg 0 0 456,212
Latvia 788,610 22,203 7,768,885

Malta 0 0 0

Netherlands 559,196 41,234 680,275
Poland 17,613,237 1,024,497 27,554,623
Portugal 544,858 1,858,685 4,864,143
Romania 9,497,727 995,951 15,798,792

Croatia 1,142,288 318,939 3,644,498
Slovakia 2,371,564 28,546 5,300,721
Slovenia 364,194 63,436 4,119,189
Sweden 1,709,132 69,780 49,762,326


**2.2.9. Installed capacity today parameters**

The capacity installed in 2020 for each power technology in each country was sourced from

Entsoe for 2019, which provides the installed net generation capacity –effectively installed on
January 1 [st] of the following year– [47] . For coal, data correspond to the summation of fossil hard
coal and fossil brown coal/lignite. Due to data gaps in Entsoe, for Slovakia data correspond to

2018, while for hydropower technologies in the United Kingdom, the data are gathered from
Eurostast [48] . For Malta, data on the installed capacity for solar and biomass were sourced from
ref, [49] and for natural gas based on the values reported by the Enemalta corporation [50] . For

Concentrated Solar Power, missing in the previous reference, the installed capacity was sourced
from EurObserver [51] . Due to data gaps, we assume that the age of the facilities in 2020 matches

the midpoint of their useful life. For the solar PV technologies (open ground and roof), we divide


S42


the total capacity sourced from Entsoe evenly among the subcategories according to the specific
data on capacities provided by the International Energy Agency [52] . Notably, according to the

source, there is no power technology with CCS installed today. The data are shown in

**Supplementary Table 39** .


,<L4M
**Supplementary Table 39** . Current capacity installed for each technology _i_ in country _j_ (CAP )
�,5

[MW] [47,48] .



**Wind**
**Country**
**onshore**



**Wind** **Wind** **Hydro** **Hydro**
**Country**
**onshore** **offshore** **run-of-river** **reservoir**


Austria 3,133 0 5,724 2,436
Belgium 2,248 1,548 181 0
Bulgaria 700 0 537 1,810
Cyprus 158 0 0 0

Czechia 316 0 334 753

Germany 52,792 6,393 3,983 1,298
Denmark 4,426 1,700 7 0

Spain 22,961 0 1,156 19,146

Estonia 462 0 0 0

Finland 2,013 0 3,148 0

France 13,610 0 10,955 8,279
United Kingdom 13,633 0 732 732

Greece 2,355 0 299 2,403

Hungary 327 0 30 28
Ireland 1,919 0 216 0
Italy 9,617 0 10,650 3,857

Lithuania 525 0 128 0

Luxembourg 154 0 25 11
Latvia 59 0 1,539 0

Malta 0 0 0 0

Netherlands 3,669 957 38 0
Poland 5,808 0 435 157
Portugal 5,127 0 2,858 1,515
Romania 2,968 0 2,770 3,373

Croatia 616 0 421 1,436
Slovakia 3 0 1,208 418
Slovenia 3 0 1,053 0
Sweden 7,506 0 0 16,301


**Solar** **Solar**

**Solar**

**Country** **Geothermal** **photovoltaic open** **Thermal**

**photovoltaic roof**

**ground** **Parabolic**


Austria 0 667 667 0

Belgium 0 1,685 1,685 0
Bulgaria 0 530 530 0
Cyprus 0 75 75 0
Czechia 0 1,025 1,025 0

Germany 42 22,650 22,650 0

Denmark 0 507 507 0

Spain 0 3,376 3,376 2,304

Estonia 0 17 17 0

Finland 0 2 2 0



**Wind**

**offshore**



**Hydro**

**run-of-river**



**Country** **Geothermal**



**Solar**

**photovoltaic open**
**ground**



**Solar**

**photovoltaic roof**



S43


France 0 4,094 4,094 0
United Kingdom 0 6,719 6,719 0

Greece 0 1,221 1,221 0

Hungary 3 468 468 0

Ireland 17 0 0 0

Italy 869 2,359 2,359 0

Lithuania 0 41 41 0

Luxembourg 0 68 68 0

Latvia 0 0 0 0

Malta 0 77 77 0

Netherlands 0 1,969 1,969 0

Poland 0 215 215 0

Portugal 0 162 162 0

Romania 0 575 575 0

Croatia 10 27 27 0

Slovakia 0 266 266 0

Slovenia 0 138 138 0

Sweden 0 0 0 0

**Country** **Coal** **Natural Gas** **Nuclear** **Biomass**

Austria 246 4,015 0 497
Belgium 470 6,649 5,931 708
Bulgaria 4,475 1,232 2,000 74
Cyprus 739 739 0 12
Czechia 10,030 1,226 4,040 400

Germany 46,498 31,664 9,516 7,752
Denmark 3,656 1,814 0 1,772

Spain 9,561 30,266 7,117 507

Estonia 51 125 0 157

Finland 2,278 1,912 2,785 1,804

France 3,966 11,952 63,130 1,931
United Kingdom 8,794 37,698 9,229 0
Greece 3,912 4,902 0 51

Hungary 1,049 4,028 1,899 246
Ireland 855 4,265 0 0
Italy 6,663 46,322 0 1,538
Lithuania 0 1,710 0 98
Luxembourg 0 81 0 30
Latvia 0 1,110 0 126

Malta 0 537 0 5

Netherlands 4,631 15,570 486 485
Poland 30,546 2,106 0 849
Portugal 1,756 4,606 0 605
Romania 4,128 3,033 1,300 115

Croatia 332 743 0 71

Slovakia 566 1,111 1,940 224

Slovenia 924 491 696 17

Sweden 0 0 8,586 0


The binary parameter that activates today’s capacity in a given period is computed as follows:


PARCAP 5,1,<L4M = 1 ∀t ∈�, r ∈�: r ≤⌈UL 5 /2⌉ _Eq. 63_


S44


PARCAP 5,1,<L4M = 0 ∀t ∈�, r ∈�: r > ⌈UL 5 /2⌉ _Eq. 64_


**2.2.10.** **Other parameters**
The capacity factor for the electricity technologies is obtained from Carlsson et al. [7] and shown

in **Supplementary Table 40** . The capacity factors for the periods missing in the table are assumed

to be the same as those reported.


_**Supplementary Table 40**_ _._ Capacity factor of each electricity technology _i_ and period _t_ (CF i,t )

[dimensionless].

Technology **2020 2030 2040 2050**

Wind onshore 0.30 0.35 0.40 0.45

Wind offshore 0.40 0.46 0.48 0.48

Hydro run-of-river 0.37 0.37 0.37 0.37

Hydro reservoir 0.35 0.35 0.35 0.35

Geothermal 0.95 0.95 0.95 0.95

Solar photovoltaic open ground 0.19 0.19 0.19 0.19

Solar photovoltaic roof 0.19 0.19 0.19 0.19

Solar parabolic thermal 0.38 0.40 0.41 0.41

Coal 0.85 0.85 0.85 0.85

Natural Gas 0.85 0.85 0.85 0.85

Nuclear 0.81 0.81 0.81 0.81

Coal + CCS 0.85 0.85 0.85 0.85

Natural Gas + CCS 0.85 0.85 0.85 0.85

Biomass 0.85 0.85 0.85 0.85

BECCS 0.85 0.85 0.85 0.85


The useful life of each electricity technology is obtained from Child et al. [6] and shown in

**Supplementary Table 41** .


_**Supplementary Table 41**_ _._ Useful life for each technology (UL i ) [y]

**Technology** UL i
Wind 25

Hydro 60

Geothermal 40

Solar 30

Coal 40

Natural Gas 35

Nuclear 40

Biomass 30


The remaining parameters values are shown in **Supplementary Table 42** .


_**Supplementary Table 42**_ _._ Other parameters.


**Parameter** **Value**

DPER 5 y
BUC 0.50 [53]


ED 0.50

YH 8760 h/yr


S45


ELOSS 7 % /1000 km [54]
HHV [NG] 55.25 MJ/kg [55]

CAP [EF] 0.20

OPEN [Tech] 60 MW

INITIAL [BECCS] 7000 MW

LOSS [cul] 2 %

LOSS [pell] 2 % [16]


The time horizon spans until 2100, and is divided into 16 intervals of five years each.


The capacity diffusion rate is set to 20% per year, which corresponds to the maximum value
observed in energy-related technologies [2] . An example of how this diffusion rate affects the
maximum capacity is shown in the supplementary results ( **Supplementary Fig. 4** )


We assume 60 MW of installed capacity in Europe for all the power technologies that have not

been deployed yet. This assumption allows expansions in capacity in those technologies with

,<L4M
zero current capacity (CAP ), for example, Natural gas with CCS. Note that this is a very
�,5

conservative estimate, since the capacity of a single coal plant can be as high as 300 MW. The

initial capacity for BECCS is set to 250 MW in each of the 28 EU countries (i.e., 7,000 MW at the

European level), based on the state-of-the-art largest standalone biomass-fired combustion

power plants.

# **3. Supplementary results**

### **3.1. Results uncertainty on costs**


We include here the results of the uncertainty analysis on the economic performance, as shown

in **Supplementary Table 43-50** . In essence, we ran RAPID for the nominal cost parameters and

then re-calculated the objective function considering the lower and upper bounds on the CAPEX

expenditures of the technologies.


_**Supplementary Table 43**_ _._ Uncertainty results for the equipotential curve of 0 Gt [billion 2015€].


Starting year Minimum cost Maximum cost

2020 18,570 26,592

2025 18,542 26,654

2030 18,424 26,745

2035 18,396 26,892

2040 18,343 27,151

2045 18,279 27,515

2050 18,228 27,799

2055 18,240 27,958

2060 18,251 28,104

2065 18,229 28,406

2070 18,236 28,809

2075 18,153 29,540

2080 18,233 31,436


S46


_**Supplementary Table 44**_ _._ Uncertainty results for the equipotential curve of -10 Gt [billion
2015€].


Starting year Minimum cost Maximum cost

2020 19,027 27,703

2025 18,979 27,863

2030 18,910 27,982

2035 18,843 28,180

2040 18,871 28,452

2045 19,021 28,749

2050 18,988 29,115

2055 18,851 29,491

2060 18,876 29,724

2065 18,709 30,446


_**Supplementary Table 45**_ _._ Uncertainty results for the equipotential curve of -20 Gt [billion
2015€].


Starting year Minimum cost Maximum cost

2020 19,530 28,965

2025 19,556 29,108

2030 19,570 29,204

2035 19,625 29,437

2040 19,610 29,838

2045 19,678 30,325

2050 19,732 30,759

2055 19,781 31,378

2060 20,060 32,360


_**Supplementary Table 46**_ _._ Uncertainty results for the equipotential curve of -30 Gt [billion
2015€].


Starting year Minimum cost Maximum cost

2020 20,345 30,162

2025 20,306 30,413

2030 20,253 30,655

2035 20,352 31,052

2040 20,565 31,450

2045 20,692 32,030

2050 20,980 32,577

2055 21,630 34,216


_**Supplementary Table 47**_ _._ Uncertainty results for the equipotential curve of -40 Gt [billion
2015€].


Starting year Minimum cost Maximum cost

2020 21,028 31,649

2025 21,101 32,056

2030 21,259 32,233

2035 21,517 32,592

2040 21,825 33,006

2045 22,080 33,756

2050 22,817 35,194


S47


_**Supplementary Table 48**_ _._ Uncertainty results for the equipotential curve of -50 Gt [billion
2015€].


Starting year Minimum cost Maximum cost

2020 22,204 313,147

2025 22,374 33,526

2030 22,555 33,716

2035 22,850 34,110

2040 23,072 34,857

2045 23,398 36,234

2050 25,831 38,769


_**Supplementary Table 49**_ _._ Uncertainty results for the equipotential curve of -60 Gt [billion 2015€]


Starting year Minimum cost Maximum cost

2020 23,218 34,960

2025 23,411 35,449

2030 23,528 35,815

2035 23,899 36,583

2040 24,711 37,850

2045 25,927 39,745


_**Supplementary Table 50.**_ Uncertainty results for the equipotential curve of -70 Gt [billion 2015€]


Starting year Minimum cost Maximum cost

2020 24,565 39,260

2025 24,942 40,253

2030 25,540 41,722

2035 26,910 44,794

### **3.2. Results uncertainty on biomass potential**


Biomass residues and marginal land availability are highly uncertain, so we carried out a

sensitivity analysis to analyze the associated implications. Notably, the inherent versatility of

biomass in the transition toward a defossilized economy may lead to sectoral competition for

the limited biomass resources available. At the same time, sustainability concerns may result in

less marginal land available. Conversely, more land might eventually become available due to
improvements in agriculture or dietary changes [41] . **Supplementary Fig. 2** shows the results of
varying the biomass resources availability within a given range (i.e., ±10%, ±25%, ±50% of the

central estimates shown in Supplementary Tables 37 and 38 for marginal land available and

amount of residues, respectively).


S48


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-48-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-48-1.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-48-2.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-48-3.png)

_**Supplementary Fig. 2.**_ _**Sensitivity analysis on the biomass potentials (biomass residues and**_
_**marginal land).**_ _Dots correspond to the optimal solutions deploying bioenergy with carbon_
_capture and storage (BECCS) and direct air carbon capture and storage (DACCS) from a particular_
_point in time onwards (from 2020 to 2100). The shaded areas indicate the new results for a given_
_percentage change in biomass potentials. Subplot_ _**a**_ _shows the sensitivity analysis for the_
_minimum costs of the European power system associated with increasing carbon dioxide removal_
_(CDR) targets. Subplot_ _**b**_ _corresponds to the sensitivity analysis for the maximum CDR attainable._
_In subplot b, the green profile considers only BECCS, blue DACCS, and yellow both BECCS and_
_DACCS, while the pie charts illustrate the proportion of gross CDR provided with BECCS and_
_DACCS, respectively_ .


S49


### **3.3. Results in the context of the EU Green Deal by 2050**

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-49-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-49-1.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-49-2.png)

_**Supplementary Fig. 3**_ _**.**_ _**Implications on costs and emissions of delayed-actions on carbon**_
_**dioxide removal (CDR) for different starting points for bioenergy with carbon capture and**_

_**storage (BECCS) and direct air carbon capture and storage (DACCS) considering the EU climate**_

_**neutrality goals by 2050 (x-axis).**_ _Subplot_ _**a**_ _shows the minimum costs of the EU power system_

_associated with increasing CDR targets. Subplot_ _**b**_ _shows the maximum cumulative net CDR that_

_could be attained deploying BECCS and DACCS from a particular point in time onwards (green_
_profile only with BECCS, blue with DACCS, and yellow considering both BECCS and DACCS). Dots_

_correspond to the optimal solutions for the 5-year time steps starting in 2020 and ending in 2020._

_The shaded areas in subplot_ _**b**_ _indicate the ranges of the results considering the uncertainty in_
_the life cycle CO_ _2_ _emissions (i.e.,_ è é 2ê _, Methods for details on the uncertainty analysis). The_
_pie charts illustrate the proportion of gross CDR provided with BECCS and DACCS, respectively._


S50


### **3.4. Maximum technology deployment rate**

The maximum deployment rate of technologies, known as diffusion rate, establishes the

maximum speed at which technologies can be deployed considering the capacity already

installed. **Supplementary Fig. 4** shows an example of the time required to achieve 1 TW of
installed capacity for the power technologies and 200 MtCO 2 /yr with DACCS considering their

given initial power capacities (2020) and a 20% diffusion rate, which has been observed in other
energy-related technologies [2] . For the DACCS the initial capacity is set to 1 Mt of gross CO 2

captured per year —reflecting the current scale ambition.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-50-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-50-1.png)


_**Supplementary Fig. 4**_ _._ _**Maximum deployment capacity as a function of time and initial**_
_**capacity for each technology.**_ _Note the secondary y-axis for direct air carbon capture and_
_storage (DACCS) while bioenergy with carbon capture and storage (BECCS) refers to the_
_primary y-axis._


The diffusion rate leads to an exponential bound on the capacity, with a small slope in the first

years of deployment. For example, a technology with an initial capacity of 60 MW would need

50 years to reach 1 TW, while wind offshore would require 10 years because of its larger initial

capacity. Similarly, for DACCS (dashed blue line), it takes around 25 years to scale from a capacity
of 1 MtCO 2 /yr to a capacity of 200 MtCO 2 /yr.

### **3.5. Regional implications for the SLOW and LATE scenarios**


**Supplementary Fig. 5** shows the regional power system and the CO 2 emissions removal

breakdown for the SLOW scenario, introducing CDR technologies in 2055. **Supplementary Fig. 6**

shows the regional power system and the CO 2 emissions removal breakdown for the LATE

scenario, introducing CDR technologies in 2055. **Supplementary Fig. 7** shows the trade of

biomass, CO 2 and electricity for the SLOW scenario, deploying CDR technologies in 2055.

**Supplementary Fig. 8** shows the trade of biomass, CO 2 and electricity for the LATE scenario,

introducing CDR technologies in 2085.


S51


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-51-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-51-1.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-51-2.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-51-3.png)

_**Supplementary Fig. 5**_ _._ _**Regional implications for the European energy system starting the**_
_**deployment of bioenergy with carbon capture and storage (BECCS) and direct air carbon**_
_**capture and storage (DACCS)in 2055**_ **SLOW** _**scenario).**_ _Subplot_ _**a**_ _corresponds to the optimal_
_electricity generation by 2100 in each European country. The pie charts show the share of_
_generation per electricity technology depicted with different colors, while the size of the pie_
_charts is proportional to the generation by 2100 (TWh). Each country is colored according to_
_the CO_ _2_ _stored in geological sites; the darker the shade, the greater the CO_ _2_ _stored. Subplot_ _**b**_
_shows the breakdown by country of the gross CO_ _2_ _removed from the atmosphere considering_
_the different biomass resources for BECCS and DACCS technologies. Countries in subplot_ _**b**_ _are_
_labeled according to the ISO3 code abbreviation. The map in subplot a was created using_
_ArcGIS® 10.7.1 software by Esri_ [56] _; no copyrighted material was used._


S52


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-52-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-52-1.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-52-2.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-52-3.png)

_**Supplementary Fig. 6.**_ _**Regional implications for the European energy system starting the**_
_**deployment of bioenergy with carbon capture and storage (BECCS) and direct air carbon**_
_**capture and storage (DACCS)in 2085 LATE scenario).**_ _Subplot_ _**a**_ _corresponds to the optimal_
_electricity generation by 2100 in each European country. The pie charts show the share of_
_generation per electricity technology depicted with different colors, while the size of the pie_
_charts is proportional to the generation by 2100 (TWh). Each country is colored according to the_
_CO_ _2_ _stored in geological sites; the darker the shade, the greater the CO_ _2_ _stored. Subplot_ _**b**_ _shows_
_the breakdown by country of the gross CO_ _2_ _removed from the atmosphere considering the_
_different biomass resources for BECCS and DACCS technologies. Countries in subplot_ _**b**_ _are labeled_
_according to the ISO3 code abbreviation. The map in subplot a was created using ArcGIS® 10.7.1_
_software by Esri_ [56] _; no copyrighted material was used._


S53


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-53-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-53-1.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-53-2.png)

_**Supplementary Fig. 7. Biomass trade, CO**_ _**2**_ _**flows and electricity transmission in the SLOW**_
_**scenario by 2100.**_ _Subplot_ _**a**_ _shows the biomass traded in the form of pellets between European_


S54


_countries. Subplot_ _**b**_ _shows the CO_ _2_ _transported via pipeline between European countries. Subplot_
_**c**_ _shows the electricity traded between European countries. In the chord diagrams produced using_
_Circos_ [57] _, the European countries are depicted by arcs on the outer part of the circular layout,_
_where the arc length provides the total biomass (subplot_ _**a**_ _), CO_ _2_ _(subplot_ _**b**_ _) and electricity_
_(subplot_ _**c**_ _) imported, exported and consumed/stored domestically (the latter refers to chords_
_leaving and entering the same country). Each chord represents a flow, where its thickness is_
_proportional to the magnitude of the trade (some values are indicated for illustrative purposes)._
_Chords directly connected to the countries' arcs represent an export (i.e., exporter country) while_
_those non-connected (separated by a white layer) correspond to imports. Countries are labeled_
_according to the ISO3 code abbreviation._


S55


![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-55-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-55-1.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/galan-martinDelayingCarbonDioxide2021/galan-martinDelayingCarbonDioxide2021.pdf-55-2.png)

_**Supplementary Fig. 8. Biomass trade, CO**_ _**2**_ _**flows and electricity transmission in the LATE**_

_**scenario by 2100.**_ _Subplot_ _**a**_ _shows the biomass traded in the form of pellets between European_


S56


_countries. Subplot_ _**b**_ _shows the CO_ _2_ _transported via pipeline between European countries. Subplot_

_**c**_ _shows the electricity traded between European countries. In the chord diagrams produced using_
_Circos_ _[57]_ _, the European countries are depicted by arcs on the outer part of the circular layout,_

_where the arc length provides the total biomass (subplot_ _**a**_ _), CO_ _2_ _(subplot_ _**b**_ _) and electricity_
_(subplot_ _**c**_ _) imported, exported and consumed/stored domestically (the latter refers to chords_

_leaving and entering the same country). Each chord represents a flow, where its thickness is_

_proportional to the magnitude of the trade (some values are indicated for illustrative purposes)._

_Chords directly connected to the countries' arcs represent an export (i.e., exporter country) while_
_those non-connected (separated by a white layer) correspond to imports. Countries are labeled_

_according to the ISO3 code abbreviation._

# **4. Methodological assumptions and future work**


We next highlight the main methodological assumptions in the RAPID modeling framework:


- The RAPID model assumes perfect foresight over the entire horizon, a standard assumption
widespread in energy systems models such as TIMES, MARKAL, and MESSAGE [58,59] . In

essence, under the perfect foresight assumption, the parameter values during the entire

time horizon are assumed to be perfectly known in advance, and the model is solved with

full visibility of current and future events. Hence, following the perfect foresight approach,

decisions in RAPID are optimized for the entire 2020-2100 horizon, yielding the best possible

roadmap based on an ideal planning. The starting year to deploy CDR is defined beforehand,

so short-term decisions affecting the power system are optimized with full awareness of

longer-term technological and market changes.

The perfect foresight assumption is fully aligned with our work's goal, which studies the

implications of delaying CDR actions by optimizing roadmaps starting from a specific year

during the horizon. This perfect foresight approach provides, therefore, a lower bound on

the cost and emissions. However, in practice, decision-makers may take short-term
decisions with limited information [58,60] .

- RAPID adopts a country-level spatial representation. A simplified representation of the EU

power system was adopted where the centroids of the countries correspond to demand

load areas. Additionally, the capacities installed and resources available refer to these

centroids (e.g., biomass residues, marginal land, and geological sites). The biomass and CO 2
storage trades are modeled with arcs between pairs of nodes (centroids) in the resulting

network. We assume that all the biomass is converted into pellets and transported via truck.

Similarly, CO 2 is always transported via pipeline, as only onshore geological sites are

considered. Storage of electricity and biomass between periods is omitted. The costs of the

new transmission lines are neglected, yet transportation losses are accounted for. Regarding

the temporal representation, RAPID considers a five-year temporal resolution.

We consider that the temporal and spatial scales are consistent with the goal of this work.

A model with higher granularity would most likely lead to the same conclusions and insights,

yet it would result in a heavier computational burden.

- The RAPID modeling framework has been initially developed for the EU (27 member

countries) plus the United Kingdom, which plays a key role in the European Network of

Transmission System Operators for Electricity (ENTSO-E). We focus on assessing the

implications of delayed actions on CDR in the EU power system as a highly relevant

illustrative case where countries are committed to cooperating to meet the Paris climate

goal. We assume full cooperation among countries in terms of electricity transmission,


S57


biomass transportation, and CO 2 trade. We consider the domestic availability of biomass

resources (forestry and agricultural residues and marginal land), and onshore geological

sites within the EU borders. However, potentials could be increased by considering other

residues available (e.g., municipal solid waste) or by adding abandoned agricultural land or
land that would be eventually available due to efficiency gains or dietary changes [41] .

Similarly, other CO 2 storage alternatives such as offshore geological storage or mineral

carbonation could be included. Hence, further research is needed on the regional CO 2

storage capacities to ultimately define the suitability of each specific storage site based on

a full range of technical, economic, and environmental constraints.

- Uncertainties in the model arise mainly due to the long-term horizon considered (from 2020

to 2100, consistent with the Paris temperature target). Notably, various parameters in

RAPID are inherently uncertain, such as future technology performance, crop yields, and

some economic and environmental parameters, among others. To get insight into how these

uncertainties affect the results, we performed an _a posteriori_ sensitivity analysis of the

economic and emissions parameters, providing confidence intervals for the optimal

solutions. The uncertainty analysis results for the emissions are shown in Fig. 1b, while the

cost results are provided in Supplementary Tables 43-50.

- In RAPID, the emissions balance focuses only on CO 2 emissions. However, other greenhouse

gas (GHG) emissions could be incorporated into the model, making the CDR targets more

ambitious. Despite globally the methane and nitrous oxide emissions are important

contributors to global warming, those GHGs are mostly linked to the livestock and fertilizers

in the agricultural sector.

The life cycle CO 2 emissions for both the foreground and background systems are retrieved
from the Ecoinvent v3.5 database [5] accessed through the Simapro software [24] . These

emissions data could be adjusted based on prospects on how technologies will evolve in the

future under a prospective LCA framework. This approach would lead to more accurate

results, yet it would also result in more pronounced uncertainties. As a matter of fact,
prospective LCAs are still scarce and could be regarded (to some extent) as proof-of-concept
studies, more so when coupled with optimization [61] . As an alternative approach, we carried

out a sensitivity analysis to study the effects of uncertainties in the LCA emissions data,

which partly stem from technological changes (details in Methods, Uncertainty analysis).


Future research directions of the current work could include:


- The scope of RAPID could be enlarged to consider a broader portfolio of CDR options,

including negative emissions technologies and practices (e.g., biochar, soil carbon
sequestration, or afforestation/reforestation). Moreover, issues related to the permanence

of storage and saturation of sinks, the vulnerability of the CO 2 storage, and the length of

crediting horizon should be considered within the scope of the model.

- RAPID could also consider other countries beyond the EU borders and model other high
emitting sectors, e.g., transport, steel industry, heating and building sector, and agriculture.

Moreover, other GHG emissions beyond the energy sector could be incorporated in the
model, with a focus on hard-to-abate emissions that CDR could offset (e.g., methane

emissions from agriculture).

- Other environmental impacts beyond climate change could be incorporated in RAPID, such

as impacts on human health or biodiversity. Modeling social or political barriers could also

help to reproduce more realistic decision-making environments.


S58


- Uncertainties could be incorporated in RAPID following a stochastic programming or robust
optimization framework [62] . This, however, would lead to more complex formulations and

larger CPU times.

# **5. Supplementary references**


1. Brooke, A., Kendrick, D., Meeraus, A. & Raman, R. GAMS—A User’s Manual. (GAMS

Development Corporation, 1998).


2. Iyer, G. et al. Diffusion of low-carbon technologies and the feasibility of long-term climate

targets. _Technol. Forecast. Soc. Change_ **90**, 103–118 (2015).


3. Dataset Publishing Language, Countries. https://developers.google.com/publicdata/docs/canonical/countries_csv


4. Keith, D. W., Holmes, G., Angelo, D. S. & Heidel, K. A Process for Capturing CO 2 from the
Atmosphere. _Joule_ **2**, 1573–1594 (2018).


5. Wernet, G. et al. The ecoinvent database version 3 (part I): overview and methodology. _Int._
_J. Life Cycle Assess_ . **21**, 1218–1230 (2016).


6. Child, M., Kemfert, C., Bogdanov, D. & Breyer, C. Flexible electricity generation, grid exchange

and storage for the transition to a 100% renewable energy system in Europe. _Renew. Energy_

**139**, 80–101 (2019).


7. Carlsson, J. E. al. (Ed). ETRI 2014 - Energy Technology Reference Indicator projections for

2010-2050. EUR 26950. Luxembourg (Luxembourg): Publications Office of the European Union;

2014. JRC92496


8. Mathieu, P. _The IPCC special report on carbon dioxide capture and storage_ . ECOS 2006 
Proceedings of the 19th International Conference on Efficiency, Cost, Optimization, Simulation

and Environmental Impact of Energy Systems (2006).


9. Miranville, A. Annual report 2019. _AIMS Math_ . **5**, i–v (2020).


10. Eurostat. Data explorer, 2019. Gas prices for non-household consumers - bi-annual data

(from 2007 onwards) (2019).


11. European Commission. Euroatom Supply Agency. Nuclear Observatory Prices.
https://ec.europa.eu/euratom/observatory_price.html


12. de Wit, M. & Faaij, A. European biomass resource potential and costs. _Biomass and_

_Bioenergy_ **34**, 188–202 (2010).


13. IEA. Greenhouse Gas R&D Programme (IEA GHG), Biomass CCS Study, 2009/9, November

2009


14. Steffen, B. Estimating the cost of capital for renewable energy projects. _Energy Econ_ . **88**,

104783 (2020).


15. Zero Emissions Platform. The Costs of CO 2 Transport: Post-demonstration CCS in the EU.

European Technology Platform for Zero Emission Fossil Fuel Power Plants, Brussels, Belgium

(2011).


S59


16. Fajardy, M. & Mac Dowell, N. Can BECCS deliver sustainable and resource efficient negative

emissions? _Energy Environ. Sci._ **10**, 1389–1426 (2017).


17. European Commission. Energy. Data and analysis. Weekly oil bulletin.
https://ec.europa.eu/energy/data-analysis/weekly-oil-bulletin_en


18. Budinis, S., Krevor, S., Dowell, N. Mac, Brandon, N. & Hawkes, A. An assessment of CCS

costs, barriers and potential. _Energy Strateg. Rev_ . **22**, 61–81 (2018).


19. IMF. International Monetary Fund. World Economic Outlook Database April 2020.
https://www.imf.org/en/Publications/WEO/weo-database/2020/April


20. Wildbolz, C. Life Cycle Assessment of Selected Technologies for CO₂ Transport and

Sequestration. (2007).


21. Camargo, G. G. T., Ryan, M. R. & Richard, T. L. Energy Use and Greenhouse Gas Emissions

from Crop Production Using the Farm Energy Analysis Tool. _Bioscience_ **63**, 263–273 (2013).


22. ECN.TNO. Phyllis2 - Database for (treated) biomass, algae, feedstocks for biogas production
and biochar. https://phyllis.nl/


23. Huijbregts, M. A. J. et al. Framework for modelling data uncertainty in life cycle inventories.
_Int. J. Life Cycle Assess_ . **6**, 127 (2001).


24. Goedkoop, M., Oele, M., Leijting, J., Ponsioen, T. & Meijer, E. Introduction to LCA with

SimaPro. PRé. (2016).


25. Wickwire, S. (2007). Biomass combined heat and power catalog of technologies. US

Environmental Protection Agency Combined Heat and Power Partnership, September 2007.
https://www. epa. gov/sites/production/files/2015
07/documents/biomass_combined_heat_and_power_catalog_of_technologies_v, 1, 122.


26. Fajardy, M., Chiquier, S. & Mac Dowell, N. Investigating the BECCS resource nexus:
delivering sustainable negative emissions. _Energy Environ. Sci_ . **11**, 3408-3430 (2018).


27. Vangkilde-Pedersen, T. et al. Assessing European capacity for geological storage of carbon

dioxide–the EU GeoCapacity project. _Energy Procedia_ **1**, 2663–2670 (2009).


28. Mortensen, G. M., Bergmo, P. E. S. & Emmel, B. U. Characterization and estimation of CO 2

storage capacity for the most prospective aquifers in Sweden. _Energy Procedia_ **86**, 352–360

(2016).


29. European Commission. Directorate-General for Energy. (2019). EU Energy in Figures:

Statistical Pocketbook. Publications Office of the European Union.


30 . Capros, P. et al 2016 EU Reference Scenario 2016. Energy Transport and GHG Emissions–

Trends to 2050 (Luxembourg: Publication Office of the European Union) (2016)


31. Ruiz, P. et al. ENSPRESO-an open, EU-28 wide, transparent and coherent database of wind,

solar and biomass energy potentials. _Energy Strateg. Rev_ . **26**, 100379 (2019).


32. Adam, K., Müller-Mienack, M., Paun, M., Sanchis, G. & Strunz, K. e-HIGHWAY 2050—The

ENTSO-E facilitated study programme towards a Modular Development Plan on pan-European

Electricity Highways System 2050. in IEEE Power and Energy Society General Meeting (2012).


S60


33. Zurano-Cervelló, P., Pozo, C., Mateo-Sanz, J. M., Jiménez, L. & Guillén-Gosálbez, G.

Sustainability efficiency assessment of the electricity mix of the 28 EU member countries

combining data envelopment analysis and optimized projections. _Energy Policy_ **134**, 110921

(2019).


34. Van Wees, J. D., et al. "A prospective study on the geothermal potential in the EU." _Geoelect_

_Report_ 13 (2013).


35. Pirker, O. et al. Hydro in Europe: Powering renewables. Renew. Action Plan 66 (2011).


36. EREC: European Renewable Energy Council. Mapping Renewable Energy Pathways towards
2020. Eur. Renew. Energy Counc. 28 (2011).


37. Swart, R. J., et al. Europe's onshore and offshore wind energy potential: An assessment of
environmental and economic constraints. No. 6/2009. European Environment Agency, 2009.


38. Pozo, C., Galán-Martín, Á., Reiner, D. M., Mac Dowell, N. & Guillén-Gosálbez, G. Equity in

allocating carbon dioxide removal quotas. _Nat. Clim. Chang._ **10**, 640–646 (2020).


39. Cai, X., Zhang, X. & Wang, D. Land availability for biofuel production. _Environ. Sci. Technol_ .

**45**, 334–339 (2010).


40. Fritz, S. et al. Downgrading recent estimates of land available for biofuel production.

_Environ. Sci. Technol_ . **47**, 1688–1694 (2013).


41. Röös, E. et al. Greedy or needy? Land use and climate impacts of food in 2050 under
different livestock futures. _Glob. Environ. Chang_ . **47**, 1–12 (2017).


42. Plessmann, G. & Blechinger, P. How to meet EU GHG emission reduction targets? A model

based decarbonization pathway for Europe’s electricity supply system until 2050. _Energy_
_Strateg. Rev_ . **15**, 19–32 (2017).


43. Pietzcker, R. C., Osorio, S. & Rodrigues, R. Tightening EU ETS targets in line with the

European Green Deal: Impacts on the decarbonization of the EU power sector. _Appl. Energy_ **293**,

116914 (2021).


44. Wietschel, L., Thorenz, A. & Tuma, A. Spatially explicit forecast of feedstock potentials for

second generation bioconversion industry from the EU agricultural sector until the year 2030. _J._

_Clean. Prod_ . **209**, 1533–1544 (2019).


45. Dyjakon, A. & García-Galindo, D. Implementing Agricultural Pruning to Energy in Europe:
Technical, Economic and Implementation Potentials. _Energies_ **12**, 1513 (2019).


46. IINAS - International Institute for Sustainability Analysis and Strategy EFI - European Forest

Institute, and J.-J. R. Forest biomass for energy in the EU: current trends, carbon balance and
sustainable potential for BirdLife Europe, EEB, and Transport & Environment. 1–121 (2014).


47. ENTSOE. Central collection and publication of electricity generation, transportation and

consumption data and information for the pan-European market. Available at:
https://transparency.entsoe.eu/generation/r2/installedGenerationCapacityAggregation/show.


48. EUROSTAT. Electricity production capacities by main fuel groups and operator. Available at:
https://ec.europa.eu/eurostat/cache/metadata/en/nrg_inf_epc_esms.htm.


S61


49. IRENA. Renewable capacity statistics 2020 International Renewable Energy Agency (IRENA).

(2020).


50. Enemalta. Enemalta coorporation. Delimara Power Station. Available at:
https://www.enemalta.com.mt/about-us/delimara-power-station/.


51. EurObserver. Solar thermal and concentrated solar power barometer. (2019).


52. Murdock, Hannah E., et al. Renewables 2020-Global status report. (2020).
https://www.iea.org/reports/renewables-2020.


53. Galán-Martín, A. et al. Time for global action: an optimised cooperative approach towards

effective climate change mitigation. _Energy Environ. Sci._ **11**, 572-581 (2018).


54. Trieb, F. et al. Captive and Open Sea Energy Import Framework. REACCESS Deliverable D2.1

& D3.1. (2010).


55. IEA. Natural Gas Information: Database Documentation. (2019).


56. ESRI (2019). ArcGIS Desktop: Release 10.7.1. Redlands, CA
(https://desktop.arcgis.com/en/arcmap)


57. Krzywinski, M. et al. Circos: an information aesthetic for comparative genomics. _Genome_

_Res_ . **19**, 1639–1645 (2009).


58. Nerini, F. F., Keppo, I. & Strachan, N. Myopic decision making in energy system
decarbonisation pathways. A UK case study. _Energy Strateg. Rev_ . **17**, 19–26 (2017).


59. Keppo, I. & Strubegger, M. Short term decisions for long term problems–The effect of
foresight on model based energy systems analysis. _Energy_ **35**, 2033–2042 (2010).


60. Heuberger, C. F., Staffell, I., Shah, N. & Mac Dowell, N. Impact of myopic decision-making

and disruptive events in power systems planning. _Nat. Energy_ **3**, 634–640 (2018).


61. Mendoza Beltran, A. et al. When the background matters: using scenarios from integrated

assessment models in prospective life cycle assessment. _J. Ind. Ecol_ . **24**, 64–79 (2020).


62. Sahinidis, N. V.. Optimization under uncertainty: state-of-the-art and opportunities. _Comput._

_Chem. Eng._, **28** (6-7), 971-983 (2004).


S62


