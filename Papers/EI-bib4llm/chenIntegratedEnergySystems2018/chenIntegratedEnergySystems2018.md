# Citation Key: chenIntegratedEnergySystems2018

---

IEEE TRANSACTIONS ON POWER SYSTEMS, VOL. 33, NO. 2, MARCH 2018 1309

## Integrated Energy Systems for Higher Wind Penetration in China: Formulation, Implementation, and Impacts


<PERSON><PERSON><PERSON>, Member, IEEE_, <PERSON>, and <PERSON><PERSON><PERSON>, Fellow, IEEE_



_**Abstract**_ **—With the largest installed capacity in the world, wind**
**power in China is experiencing a** _**∼**_ **20% curtailment. The inflexible**
**combined heat and power (CHP) has been recognized as the major**
**barrier for integrating the wind source. The approach to reconcile**
**the conflict between inflexible CHP units and variable wind power**
**in Chinese energy system is yet unclear. This paper explores the**
**technical and economic feasibility of deploying the heat storage**
**tanks and electric boilers under typical power grids and practical**
**operational regulations. A mixed integer linear optimization model**
**is proposed to simulate an integrated power and heating energy sys-**
**tems, including a CHP model capable of accounting for the commit-**
**ment decisions and nonconvex energy generation constraints. The**
**model is applied to simulate a regional energy system (Jing<PERSON>Jin–**
**<PERSON>) covering 100-million population, with hourly resolution over**
**a year, incorporating actual data, and operational regulations. The**
**results project an accelerating increase in wind curtailment rate at**
**elevated wind penetration. Investment for wind breaks even at 14%**
**wind penetration. At such penetration, the electric boiler (with heat**
**storage) is effective in reducing wind curtailment. The investment**
**in electric boilers is justified on a social economic basis, but the**
**revenues for different stakeholders are not distributed evenly.**


_**Index Terms**_ **—Combined heat and power (CHP), wind power,**
**energy system integration, heat storage, electric boiler.**


I. I NTRODUCTION


HE installed capacity for wind power in China reached
# T over 100 GW in 2014, accounting for over 30% of total

capacity worldwide [1]. It is expected to double by 2020, and
to increase further by 2030 to contribute to the “20% renewable
penetration” target announced by the Chinese government [2].
Problems associated with the integration of wind power, however, pose an important barrier for its further development. The
onshore wind base (a cluster of wind farms with total capacity


Manuscript received August 3, 2016; revised February 14, 2017 and June
4, 2017; accepted July 26, 2017. Date of publication August 7, 2017; date of
current version February 16, 2018. This work was supported in part by a grant
from SHKP-Kwoks’ Foundation through the Harvard China Fund, in part by
the Ash Center of the Harvard Kennedy School of Government, and in part
by the Harvard Global Institute. The work of C. Kang was supported by the
National Science Fund for Distinguished Young Scholars (********). Paper
no. TPWRS-01178-2016. _(Corresponding author: Michael B. McElroy.)_

X. Chen and M. B. McElroy are with Harvard University, Cambridge, MA
02138 USA (e-mail: <EMAIL>; <EMAIL>).

C. Kang is with the State Key Laboratory of Power Systems, Department of
Electrical Engineering, Tsinghua University, Beijing 100084, China (e-mail:
<EMAIL>).

Color versions of one or more of the figures in this paper are available online
at http://ieeexplore.ieee.org.

Digital Object Identifier 10.1109/TPWRS.2017.2736943



above 10 GW) is mainly located in the North and Northeastern
provinces. The local generation mix is dominated by inflexible
coal-fired units, of which 50% _−_ 70% are combined heat and
power (CHP) plants operated in heat driven mode during the
winter season. Lacking operating flexibility from thermal units,
the curtailment rate for wind power reaches 15% 25% in the
Northern and Northeastern provinces annually [3].

Introducing additional flexibility in the heating sector and
relieving the constraints between heat and power production in
CHP units is critical for successful integration of variable wind
power in these regions. Installing electric boilers to produce
heat and thus relieve the binding constraints on CHP when wind
power is at a surplus offer one possible solution. Heat storage
tanks (heat accumulators) store or release heat by changing the
interior water temperature, providing a low cost heat storage
option. Operating in parallel with CHP units, heat storage tanks
could potentially substitute for part of the heat production of a
CHP unit increasing its operational flexibility on the power side,
facilitating thus enhanced integration of wind power.

These technologies have been investigated in several Nordic
regions [4]–[6], where energy systems have a similar generation
mix operating though under different regulatory schemes. A
comprehensive outlook for smart heating systems, with features
for energy conservation and to better accommodate renewables,
is proposed in the context of 100% renewable electricity
systems [7], [8]. An integrated strategy for supplying heating,
cooling and power in small scale distributed networks incorporating a contribution from renewables is proposed in [9] to
increase the overall energy efficiency. Reference [10] indicates
that introducing electrical heating would only slightly reduce
wind curtailment but would largely improve the revenue from
wind power integration, since the market price would increase
even in the absence of wind curtailment. References [11], [12]
suggest that heat storage (heat accumulators) could increase the
flexibility and thus increase the revenue for CHPs in the face of
fluctuating prices in wholesale market. But the benefit of heat
storage is sensitive to the structure of existing heat supply system
and to the level of wind penetration [13], [16]. The two options
are found to be theoretically feasible in a regulated power
system with centralized dispatch [14]. Reference [15] concludes
importantly that the thermal inertia of pipelines in district heating systems could also be explored to reduce wind curtailment.
These analyses are based however on illustrative “cases” that
incorporate a number of important simplifying assumptions.



0885-8950 © 2017 IEEE. Personal use is permitted, but republication/redistribution requires IEEE permission.

See http://www.ieee.org/publications standards/publications/rights/index.html for more information.


Authorized licensed use limited to: Tsinghua University. Downloaded on May 04,2025 at 12:22:11 UTC from IEEE Xplore. Restrictions apply.


1310 IEEE TRANSACTIONS ON POWER SYSTEMS, VOL. 33, NO. 2, MARCH 2018



Given the diverse regulatory requirements and operational
schemes applicable for a regulated power system exemplified
for example in the Chinese context, the potential impacts of
the two flexible heating technologies are unclear not only with
respect to carbon emissions and operational economics.

We should note that heat pumps offer another promising
technology to reduce both wind curtailment and CO 2 emissions

[13], [17]. Given their high investment cost and low operational
expense (high efficiency), heat pumps are more appropriate for
deployment on the demand side, supplying heat continuously

[13]. As this paper focuses on the flexible operation of CHP
units on the generation side, the heat pump solution is not
considered in the following analysis.

This paper reports a detailed simulation for a typical regional
energy system in Northern China to explore the major barriers
for integrating a higher penetration wind power, investigating
specifically the technical feasibility of electric boilers and heat
storage systems to reduce wind curtailment and CO 2 emissions.
The area studied covers Jing-Jin-Tang region, which represents
the largest industrial and load center in North China, equivalent
in annual energy demand to that for the total Nordic countries
combined. Annual steel production in this region exceeds that
for any other country. The JJT region is adjacent to 5 out of ten
10-GW scale wind power bases in China, which in total constitute about 50% of the entire installed wind power capacity
in China. CHP units account for 50% of the thermal generation
capacity of the area, supplying heat to 7 major cities. The paper
will analyze specifically the implications of wind integration
at different penetration levels, using measured data for CHP
operational limits, as well as real power and district heating system configurations. The impacts of introducing the two flexible
heating sources-electric boiler and heat storage-on wind power
integration, CO 2 emissions, fuel cost and revenue of different
stakeholders will be discussed.


The simulation is based on a newly developed integrated
power and heat optimization (IPHO) model. The IPHO model is
a high resolution mixed integer optimization scheme designed
to simulate the operation of power and district heating systems,
including their detailed interactions on an hourly basis. The
model can simulate the temporal operation of regional power
systems and their interaction with multiple district heating
areas. It optimizes the hourly power and heat output for each
energy generating unit, including thermal units, CHP, wind
power, pumped hydro storage, electric boilers and heat storage
(heat accumulator). The objective is to minimize the fuel cost
for both the power and heating systems. The power system load
balance, system reserve requirement, ramping constraints and
inter-regional network constraints are considered. The heat supply and demand in each heating district are balanced separately.
A standardized modeling of operational constraints and costs of
CHP units is formulated to allow for start-up/shut-down decisions and the non-convex restrictions on heat/power production.

The results indicate that wind curtailment increases significantly at higher wind penetration levels given the current energy
system structure and regulations. Investing in electric boilers
will markedly reduce wind curtailment by providing additional
flexibility and reserve capacity. Further benefits accrue in terms



of reductions in carbon emissions combined with increases in

revenue. Thermal storage has a relatively minor impact on accommodating wind power, largely since the cycling of storage
is on daily basis while wind power varies on a longer cycle.
On a social economic basis, the investment in electric boilers
could be compensated by fuel saving in 5.7 years; the combined
electric boiler and heat storage solution has a payback period
of 10 years. The allocation of revenue, however, is imbalanced
distributed over different power generating stockholders.

The main contributions of this paper are as follows.
1) This paper proposes a mixed integer linear optimization

model to simulate integrated power and heating energy
systems. Compared with multi-energy sector models such
as the widely used EnergyPlan [4], [8], the modeling for
the CHP unit proposed in this paper employs a novel convex combination formulation, capable of accounting for
commitment decisions, flexibility constraints, a linear approximation for variable fuel costs and non-convex energy
generation constraints.
2) A detailed simulation for a typical regional energy system

in Northern China is conducted in this study incorporating
actual operational properties of CHP units, hourly power
demand and assimilated meteorology data. In comparison
to most related studies, this analysis incorporate hourly
simulation throughout an entire year rather than focusing
on selected time periods.
3) Results indicate that electric boilers (with heat storage) are

effective in reducing wind curtailment. The investment in
electric boilers is justified on a social economic basis,
but revenues for different stakeholders are not distributed

evenly, which will be a key barrier for the implementation
of electrical boilers and heat storage in China.
The IPHO model is described in Section II. The energy system
and data are presented in Section III. Simulation results are
analyzed in Section IV, with conclusions presented in Section V.


II. I NTEGRATED P OWER AND H EAT O PTIMIZATION


M ODELING F ORMULATION


In this section, an integrated power and heat optimization
model is introduced considering both commitment and dispatch
decisions for reliable and economic power and heat supply. We
present also the general model framework, decision variables
and objectives, the modeling of CHP with respect to generalized
operational restrictions and cost, as well as other constraints.


_A. General Modeling Framework_


The IPHO model is a mixed integer linear optimization model
to describe the joint operation of power and district heating
systems on an hourly basis. Based on the conventional power
system unit commitment model with wind power integration,
the IPHO model is formulated to account for the hourly energy
balance in district heating systems considering both heat storage
tanks (heat accumulator) and electric boilers. For the power
system, the model balances hourly power supply considering
inter-regional network constraints and reserve requirements. For
heating, the model considers the balances of heat in multiple



Authorized licensed use limited to: Tsinghua University. Downloaded on May 04,2025 at 12:22:11 UTC from IEEE Xplore. Restrictions apply.


CHEN _et al._ : INTEGRATED ENERGY SYSTEMS FOR HIGHER WIND PENETRATION IN CHINA 1311



_it_ _[c]_ [and] _[ S]_ _it_ _[c]_



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenIntegratedEnergySystems2018/chenIntegratedEnergySystems2018.pdf-2-0.png)

Fig. 1. Modeling framework for an integrated system of space heating and
wind power.


independent districts on an hourly basis, accounting for the
possible application of heat storage tanks and electric boilers.
Constraints on water flow limitation and time-lags are presented
in [18], similar to the models in [15], but are not included in the
following analysis due to data limitation.

The model structure is summarized in Fig. 1. The input includes hourly heat demands for different heating districts, hourly
power demand, power network topology and information on
connections between heating sources and heating districts. The
output includes hourly energy generation for individual units,
corresponding fuel use and emissions. Wind curtailment is also
calculated.


_B. Decision Variables and Objective Function_


The decision variables include both continuous variables as
sociated with dispatch and binary variables associated with commitment decisions. The continuous decision variables include

energy generation for each unit for each time period, including power consumption in electric boilers, variables associated
with CHP, as well as variables related to heat storage: _p_ _[e]_ _it_ [and] _[ p]_ _[w]_ _it_

represent the power production from the _i_ _[th]_ conventional power
plant and the _i_ _[th]_ wind farm at time _t_, respectively; _p_ _[c]_ _it_ [and] _[ q]_ _it_ _[c]_ [the]

power and heat production in the _i_ _[th]_ CHP unit at time _t_, respectively; _q_ _[h]_ [,] _[ q]_ _[s]_ [and] _[ q]_ _[eb]_ [the heat output from] _[ i]_ _[th]_ [ heat boiler, the]



is the penalty term for wind curtailment, so that wind could
be given priority of dispatch; _C_ _it_ _[c]_ [and] _[ S]_ _it_ _[c]_ [are the fuel cost and]

start-up cost for CHPs, determined according to (14) and (15),
respectively. n indicates the number of time intervals considered
in the optimization model. As indicated later, the simulation
model is most suitable for day-ahead scheduling and the typical
optimization period is on daily basis. The fuel costs for power
plants _C_ _it_ _[p]_ [and for heating plants] _[ C]_ _it_ _[q]_ [depend linearly on fuel]

consumption. _S_ _it_ _[p]_ [is the start-up cost for conventional power]

plants [19]. _C_ _W_ is given by:



_it_ _[p]_ [and for heating plants] _[ C]_ _[q]_



_N_ _w_
�


_i_ =1




_[w]_ _it_ [)] (2)



_C_ _W_ = _θ ·_



_n_
�


_t_ =1



( _P_ [¯] _it_ _[w]_



_it_ _[w]_ _[−]_ _[p]_ _[w]_ _it_



where _θ_ denotes the penalty factor for wind curtailment, _P_ [¯] _it_ _[w]_



where _θ_ denotes the penalty factor for wind curtailment, _P_ _it_ _[w]_ [is]

the wind power that would be available from wind farm _i_ at time
_t_ and _p_ _[w]_ _it_ [is the actual power output;] _[ N]_ _[w]_ [ indicates the number]

of wind farms. The penalty term _C_ _W_ for wind curtailment considered here reflects the legislative requirement in China. The
Renewable Energy Law published in 2005 stated that renewable
power, including wind and solar, should have the highest priority in dispatch whenever the technological constraints allows.
A large penalty factor of _θ_ will ensure that the wind curtailment ( _P_ [¯] _it_ _[w]_ _[−]_ _[p]_ _[w]_ _it_ [)][ can be minimized first in the optimization]

process. As the actual regulation is ambiguous in practice, the
penalty factor we adopted in the following analysis is set as
1000 (sce/kWh), sufficiently large to prioritize dispatch of wind

power.



_it_ _[w]_ _[−]_ _[p]_ _[w]_ _it_




_[e]_ _it_ [and] _[ p]_ _[w]_ _it_



_C. CHP Modeling_


The heat and power production for a CHP unit are coupled
and restricted within certain boundaries. The restrictions reflect
internal operational limitations, such as steam pressure limits of
the individual turbines and fuel limits of the boiler. All viable

combinations of heat and power output for CHP units satisfying
operational constraints jointly constitute a feasible operational
area. Diversified designs of CHP units would result in different
shapes of feasible operational areas. Here we present a generalized model for the commitment and dispatch of CHP units. The
model formulates the fuel cost and constraints on power and
heat outputs for all types of CHP units.

_1) Convexity of Feasible Operational Area:_ Given any CHP
unit, the feasible operational area from the modeling point of
view is divided into convex and non-convex regions as shown in
Fig. 2. For convex areas, any convex combination of coordinates
defining the corner points is within the feasible operational area
as shown in Fig. 2(a). For non-convex areas, some convex combinations of corner points lie outside the feasible operational
area as illustrated in Fig. 2(b).

_2) Constraints on Power and Heat Production:_ For CHP
units with convex feasible operational areas, the power, heat
production and cost can be represented by a convex combination
of coordinates of the corner points [14], [23]:




_[c]_ _it_ [and] _[ q]_ _[c]_



tively; _q_ _i,t_ _[h]_ [,] _[ q]_ _i,t_ _[s]_ [and] _[ q]_ _i,t_ _[eb]_ [the heat output from] _[ i]_ _[th]_ [ heat boiler, the]

heat storage tank and the electrical heat boiler at time _t_, respectively. The binary decision variables include start-up variables
for thermal unit _i_ at time _t_, denoted as _I_ _[e]_ [, Start-up variables]



_i,t_ _[s]_ [and] _[ q]_ _i,t_ _[eb]_



_i,t_ [,] _[ q]_ _[s]_




_[e]_

_i,t_ [, Start-up variables]



for CHP unit _i_ at time _t_, are denoted by _I_ _[c]_ [(1)]



_i,t_ _[c]_ [(1)] _[, I]_ _i,t_ _[c]_ [(2)]



_i,t_ _[c]_ [(2)] _[, . . ., I]_ _i,t_ _[c]_ [(] _[k]_ [)]



_i,t_ _i,t_ _i,t_ [,]

with _k >_ 1 when the feasible operational area of the CHP unit
is non-convex, as described in the following section.

The proposed model is designed to minimize wind power curtailment, as well as fuel and start-up costs. The objective function includes the total fuel cost, start-up costs and the penalties
for curtailment of wind power. The fuel cost considered in the
objective includes the fuel cost for conventional power plants,
for heating plants and for combined heat and power plants, according to



_N_ _p_
�


_i_ =1



_N_ _q_
�


_i_ =1



_N_ _c_
�


_i_ =1



_n_
�


_t_ =1



_C_ _it_ _[q]_ [+]



_n_
�


_t_ =1



min _f_ =



_n_
�


_t_ =1



_C_ _it_ _[p]_ [+]



_C_ _it_ _[c]_



_N_ _c_
�


_i_ =1



_N_ _p_
�


_i_ =1



⎧⎪⎨⎪⎩



_p_ _[c]_



_n_ _−_ 1
�


_t_ =1



_S_ _it_ _[p]_ [+] _[ C]_ _[W]_ (1)



_S_ _[p]_



+



_n_ _−_ 1
�


_t_ =1



_S_ _it_ _[c]_ [+]



_it_ _[k]_ _[x]_ _[k]_ _i_

_it_ _[k]_ _[y]_ _i_ _[k]_

_it_ _[k]_ _[c]_ _[k]_ _i_



_i_




_[c]_ _it_ [=][ �] _[M]_ _k_

_it_ _[c]_ [=][ �] _[M]_ _k_

_it_ _[c]_ [=][ �] _[M]_ _k_



_k_ =1 _[α]_ _it_ _[k]_



_k_ =1 _[α]_ _it_ _[k]_



where _N_ _p_, _N_ _q_, and _N_ _c_ denote the number of conventional thermal power plants, heating boilers and CHPs, respectively. _C_ _W_



_it_ _k_ =1 _it_ _i_

_q_ _it_ _[c]_ [=][ �] _[M]_ _k_ =1 _[α]_ _it_ _[k]_ _[y]_ _i_ _[k]_

_C_ _[c]_ [=] _[M]_ _[α]_ _[k]_ _[c]_ _[k]_



_k_ =1 _[α]_ _it_ _[k]_



(3)



Authorized licensed use limited to: Tsinghua University. Downloaded on May 04,2025 at 12:22:11 UTC from IEEE Xplore. Restrictions apply.


1312 IEEE TRANSACTIONS ON POWER SYSTEMS, VOL. 33, NO. 2, MARCH 2018


_3) Flexibility Constraints:_ Ramping constraints for a CHP
unit, based on [14] and [19], employ the form:



_i,t_ _[c]_ _−_ 1 [)]



⎧⎪⎨⎪⎩




_[c]_ _i,t_ _[−]_ _[p]_ _[c]_



_i,t_ _[c]_ _[−]_ _[I]_ _[c]_



_i,t_ _[c]_ _−_ 1 [+] _[ S]_ _i_ _[u]_



_i_ _[u]_ _[·]_ [ (] _[I]_ _[c]_



_p_ _[c]_




_[c]_ _i,t_ _[−]_ _[p]_ _[c]_




_[c]_ _i,t−_ 1 _[≤]_ _[R]_ _i_ _[u]_



_i_ _[u]_ _[·][ I]_ _[c]_



+ _P_ [¯] _[c]_




_[c]_

_i,t_ [)]



(8)



_i_ _[c]_ _[·]_ [ (1] _[ −]_ _[I]_ _[c]_



_i,t_ _[c]_ [+] _[ S]_ _i_ _[d]_



_i,t_ _[c]_ _−_ 1 _[−]_ _[I]_ _[c]_




_[c]_

_i,t_ [))]



_i_ _[·]_ [ (] _[I]_ _[c]_



_p_ _[c]_




_[c]_ _i,t−_ 1 _[≥−]_ [(] _[R]_ _i_ _[d]_



_i_ _[·][ I]_ _[c]_



where _R_ _[d]_



where _R_ _i_ [and] _[ R]_ _i_ _[u]_ [are the ramp-up and ramp-down limits for] _[ i]_ [th]

CHP unit. _S_ _i_ _[d]_ [and] _[ S]_ _i_ _[u]_ [are the ramping limits for the unit to start]

up or shut down. _P_ [¯] _i_ _[c]_ [is the maximum power output from the] _[ i]_ [th]

CHP unit, a constant number defined by:



_i_ [and] _[ R]_ _i_ _[u]_



_i_ [and] _[ S]_ _i_ _[u]_



_i_ _[c]_ [= max(] _[x]_ _[k]_ _i_



_P_ ¯ _[c]_



_i_ [)] _[, k]_ [ = 1] _[, . . ., M]_ (9)



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenIntegratedEnergySystems2018/chenIntegratedEnergySystems2018.pdf-3-0.png)

Fig. 2. The convex and non-convex feasible operational area of CHP units.



The power outputs are constrained additionally by:



_i,t_ _[c]_ _[−]_ _[I]_ _[c]_



_i,t_ _[c]_ +1 [+] _[ S]_ _i_ _[d]_



_i,t_ _[c]_ +1 [)] (10)




_[ c]_ _i_ _[·][ I]_ _[c]_



_i_ _[·]_ [ (] _[I]_ _[c]_



where ( _x_ _[k]_ _i_




_[k]_ _i_ _[, y]_ _i_ _[k]_



_i_ _[k]_ _[, c]_ _[k]_ _i_



where ( _x_ _i_ _[, y]_ _i_ _[, c]_ _i_ [)][ is the power output, heat production and fuel]

cost for the _k_ _[th]_ corner point, and _α_ _it_ _[k]_ [are non-negative values]

constrained to satisfy [�] _[M]_ _k_ =1 _[α]_ _it_ _[k]_ [=] _[ I]_ _i,t_ _[c]_ [(1)] [.]

For non-convex areas, the feasible operational area can be
divided into sub-areas of convexity. As an example, in Fig. 2(b),
the region could be divided into two sub-regions: ABEF, and
BCDE. If ( _x_ _[k]_ _i,_ 1 _[, y]_ _i,_ _[k]_ 1 [)][ and][ (] _[x]_ _[k]_ _i,_ 2 _[, y]_ _i,_ _[k]_ 2 [)][ define the power and heat]

production of the _k_ _[th]_ corner points of the two regions, the power
and heat output of the _i_ _[th]_ combined heat and power plant at time
t, ( _p_ _[c]_ _it_ _[, q]_ _it_ _[c]_ [)][, would be given by:]



⎧⎪⎪⎪⎪⎨⎪⎪⎪⎪⎩



_T −_ _UT_ _i_ + 1

_T_
� _v_ = _t_ [(] _[I]_ _[c]_ _[−]_ [(] _[I]_ _[c]_ _[−]_ _[I]_ _[c]_ _−_ 1 [))] _[ ≥]_ [0]



_P_ _[c]_



_i,t_ _[c]_ _[≤]_ _[P]_ [¯] _[ c]_ _i_



_it_ _[k]_ [=] _[ I]_ _[c]_ [(1)]



The minimum on and off time constraints, similar to those for
conventional thermal units [19], are formulated as in (11) and
(12) respectively:



_k_ =1 _[α]_ _it_ _[k]_



_v_ = _t_ [(] _[I]_ _[c]_



_i,v_ _[c]_ _[−]_ [(] _[I]_ _[c]_



_i,t_ _[c]_ _[−]_ _[I]_ _[c]_




_[k]_ _i,_ 1 _[, y]_ _i,_ _[k]_



_i,_ _[k]_ 1 [)][ and][ (] _[x]_ _[k]_ _i,_




_[k]_ _i,_ 2 _[, y]_ _i,_ _[k]_



� _Gt_ _i_



� _t_ =1 _i_ [(1] _[ −]_ _[I]_ _i,t_ _[c]_ [) = 0]

� _tv_ += _U Tt_ _i_ _−_ 1 ( _I_ _[c]_ [)] _[ ≥]_



_i_

_t_ =1 [(1] _[ −]_ _[I]_ _[c]_

_tv_ += _U Tt_ _i_ _−_ 1 ( _I_ _[c]_




_[c]_

_i,v_ [)] _[ ≥]_ _[UT]_ _[i]_ _[ ·]_ [ (] _[I]_ _i,t_ _[c]_



_i,t_ _[c]_ _−_ 1 [)] _[t]_ [ =] _[ G]_ _[j]_ [ + 1] _[, . . . .,]_



_it_ _[c]_ [)][, would be given by:]




_[c]_ _it_ _[, q]_ _[c]_



_i,t_ _[c]_ _[−]_ _[I]_ _i,t_ _[c]_



_i,t_ _[c]_ _−_ 1 [))] _[ ≥]_ [0] _[ t]_ [ =] _[ T][ −]_ _[UT]_ _[t]_ [ + 2] _[, . . . ., T]_



�




_[c]_ _it_ [=][ �] _[M]_ _k_ [ 1]



_it_ _[k]_ _[x]_ _[k]_ _i,_

_it_ _[k]_ _[y]_ _[k]_




_[k]_ _i,_ 1 [+][ �] _[M]_ _k_ [ 2]

_i,_ _[k]_ 1 [+][ �] _[M]_ _k_ [ 2]




[ 2]

_k_ =1 _[β]_ _[k]_

_[M]_ _k_ =1 [ 2] _[β]_ _it_ _[k]_



_it_ _[k]_ _[x]_ _[k]_



_it_ _[k]_ _[y]_ _[k]_



_i,_ 2



(4)



_p_ _[c]_ _it_




[ 1]

_k_ =1 _[α]_ _it_ _[k]_

_[M]_ _k_ =1 [ 1] _[α]_ _it_ _[k]_



_it_ _k_ =1 _it_ _i,_ 1 _k_ =1 _it_ _i,_ 2

_q_ _it_ _[c]_ [=][ �] _[M]_ _k_ =1 [ 1] _[α]_ _it_ _[k]_ _[y]_ _[k]_ 1 [+][ �] _[M]_ _k_ =1 [ 2] _[β]_ _it_ _[k]_ _[y]_ _[k]_ 2



_it_ _[c]_ [=][ �] _[M]_ _k_ [ 1]



� _Gt_ _i_



_i_

_t_ =1 _[I]_ _[c]_




_[c]_

_i,v_ [)] _[ ≥]_ _[DT]_ _[i]_ _[ ·]_ [ (] _[I]_ _[c]_



where ( _α_ _[k]_



_it_ _[k]_ _[, β]_ _it_ _[k]_



_i_ _−_

_v_ = _t_ (1 _−_ _I_ _[c]_



where ( _α_ _it_ _[, β]_ _it_ [)][ are the combination coefficients for the two]

convex sub-regions. The combination coefficients all lie within
the range [0,1], satisfying the equations:



� _t_ =1 _i_ _[I]_ _i,t_ _[c]_ [= 0]


_t_ + _DT_ _i_ _−_ 1
� _v_ = _t_ (1



_t_ = _G_ _i_ + 1 _, . . . ., T −_ _DT_ _i_ + 1

_T_
� _v_ = _t_ [(1] _[ −]_ _[I]_ _[c]_ _[−]_ [(] _[I]_ _[c]_ _−_ 1 _[−]_ _[I]_ _[c]_ [))] _[ ≥]_ [0]



_i,t_ _[c]_ _−_ 1 _[−]_ _[I]_ _[c]_




_[c]_

_i,t_ [)]



(11)


(12)



_i,t_ _[c]_ _−_ 1 _[−]_ _[I]_ _[c]_




_[c]_

_i,t_ [))] _[ ≥]_ [0]



_M_ 1

_k_

��� _Mk_ 2



_it_ _[k]_ [=] _[ I]_ _[c]_ [(1)]



(5)



_v_ = _t_ [(1] _[ −]_ _[I]_ _[c]_



_i,v_ _[c]_ _[−]_ [(] _[I]_ _[c]_



1

_k_ =1 _[α]_ _it_ _[k]_


_M_ 2

_k_ =1 _[β]_ _it_ _[k]_



_i,t_ _∀i, t_



_k_ =1 1 _[α]_ _it_ [=] _[ I]_ _i, t_ _[c]_ _∀i, t_

� _Mk_ =1 2 _[β]_ _it_ _[k]_ [=] _[ I]_ _[c]_ [(2)] _∀i, t_



⎧⎪⎪⎪⎪⎪⎪⎪⎨⎪⎪⎪⎪⎪⎪⎪⎩



_t_ = _T −_ _DT_ _t_ + 2 _, . . . ., T_



_it_ _[k]_ [=] _[ I]_ _[c]_ [(2)]



where ( _I_ _[c]_ [(1)]




_[c]_ [(1)]

1 _, t_ _[, I]_ 2 _[c]_ _, t_ [(2)]



2 _[c]_ _, t_ [(2)] [)][ are binary variables indicating the status of]



CHP unit. _I_ _i,t_ _[c]_ [(1)] = 1 indicates that the CHP is operating in the



first sub-region, whereas _I_ _i,t_ _[c]_ [(2)] = 1 indicates CHP is operating

in the other sub-region. The two binary variables satisfy the
following equation:



_I_ _[c]_ [(1)]




_[c]_ [(1)] + _I_ _[c]_ [(2)]

_i,t_ _i,t_



_i,t_ _≤_ 1 (6)



where _UT_ _i_ and _DT_ _i_ represent the minimum on and off times of
the _i_ _[th]_ CHP unit respectively. _G_ _i_ is associated with the initial
status of the _i_ _[th]_ CHP unit. If unit _i_ is operating in the initial
time interval, _G_ _i_ represents the minimum time intervals required
before it can shut down; otherwise, _G_ _i_ represents the minimum
time intervals required before the unit can start-up.

_4) Fuel Costs:_ The fuel cost of a CHP unit is determined
jointly by its power and heat production, and the typical cost
function is quadratic, as shown in (13).



_it_ _[c]_ [)] [2]



This equation constraints the CHP unit to operate in only one
of the sub-regions for a particular time interval.

Binary variables,( _I_ _i,t_ _[c]_ [(1)] _[, I]_ _i,t_ _[c]_ [(2)] [)][, also indicate the on/off sta-]

tus of the CHP unit. The unit is shut down when both binary variables, ( _I_ _i,t_ _[c]_ [(1)] _[, I]_ _i,t_ _[c]_ [(2)] [)][, equal zero. For the convenience]

of following modeling presentation, we use one binary variable,
_I_ _[c]_

_i,t_ [, to indicate the on-off status of the] _[ i]_ _[th]_ [ CHP unit at time t,]
satisfying:



_C_ _[c]_



_i,t_ _[c]_ [=] _[ μ]_ [0] [+] _[ μ]_ [1] _[·][ p]_ _[c]_




_[c]_ _it_ [+] _[ μ]_ [2] _[·][ q]_ _[c]_



_it_ _[c]_ [+] _[ μ]_ [3] _[·]_ [ (] _[p]_ _[c]_




_[c]_ _it_ [)] [2] [ +] _[ μ]_ [4] _[·]_ [ (] _[q]_ _[c]_



_it_ _[c]_ (13)



Binary variables,( _I_ _[c]_ [(1)]



_i,t_ _[c]_ [(1)] _[, I]_ _i,t_ _[c]_ [(2)]



+ _μ_ 5 _· p_ _[c]_




_[c]_ _it_ _[·][ q]_ _[c]_



_i,t_ _[c]_ [(1)] _[, I]_ _i,t_ _[c]_ [(2)]



_i,t_ _[c]_ [=] _[ I]_ _i,t_ _[c]_ [(1)]



_i,t_ (7)



_I_ _[c]_




_[c]_ [(1)] + _I_ _[c]_ [(2)]

_i,t_ _i,t_



where _μ_ 0 _−_ _μ_ 5 represent the coefficients associated with the
cost function. The overall fuel cost varies quadratically with
both power and heat production. To incorporate such non-linear
fuel cost in the Mixed-integer linear programing formulation,
we use also the convex combination method to represent the
fuel costs for CHP units. The convex combination provides for
a linear simplification of the quadratic cost function above, as



Authorized licensed use limited to: Tsinghua University. Downloaded on May 04,2025 at 12:22:11 UTC from IEEE Xplore. Restrictions apply.


CHEN _et al._ : INTEGRATED ENERGY SYSTEMS FOR HIGHER WIND PENETRATION IN CHINA 1313



in:



_i,_ 1 [+]



_M_ _i_
�


_k_ =1



_i,_ 2



�



_C_ _it_ _[c]_ [= min]



_M_ _i_
�
� _k_ =1



_α_ _[k]_




_[k]_

_i t_ _[c]_ _[k]_



load at time _t_ . _p_ _[T]_ _it_ [is the power imported from] _[ i]_ _[th]_ [ neighboring]

regions at time t.

The system reserve constraint is defined as:



_β_ _[k]_



_i t_ _[k]_ _[c]_ _[k]_



(14)



_p_ _[T]_ _it_ _[≥]_ _[P]_ _[t]_ [+] _[RS]_ _[t]_ _[−][RW]_ _[t]_ _∀t_


(18)



_i_ [+]



_N_ _s_
�


_i_ =1



_p_ _[s]_ _it_ [+]



_p_ _[s]_



_N_ _T_
�


_i_ =1



where _C_ _it_ _[c]_ [is the operational cost for the] _[ i]_ _[th]_ [ CHP unit at time] _[ t]_ [. In]

such formulation, only fuel costs at corner points are required
to determine the cost function for combined heat and power
units, resulting in a significantly simplified specification of input
parameters in the simulation process.

Its start-up cost is represented by a non-negative variable _S_ _it_ _[c]_ [,]

satisfying:



_N_ _p_
�


_i_ =1



_I_ _[e]_



_i,t_ _[e]_ _[·]_ [ ¯] _[p]_ _[e]_ _i_



_N_ _c_
�


_i_ =1



_p_ ˆ _[c]_ _it_ [+]



where _I_ _[e]_




_[e]_ _i_ [is the nameplate capacity for] _[ i]_ _[th]_



_it_ _[c]_ _[≥]_ _[λ]_ _[i]_ _[·]_ [ (] _[I]_ _[c]_



_i,t_ _[c]_ _−_ 1 [)] (15)



where _I_ _i,t_ _[e]_ [is the binary variable indicating the on/off status at]

time t of thermal plant _I_, ¯ _p_ _[e]_ _i_ [is the nameplate capacity for] _[ i]_ _[th]_

thermal unit, _RS_ _t_ is the required system reserve margin at time
_t_, _RW_ _t_ is the reserve contribution from wind power at time _t_,
and ˆ _p_ _[c]_ _it_ [indicates the maximum possible power output for the]

_i_ _[th]_ CHP unit at time t.



thermal unit, _RS_ _t_ is the required system reserve margin at time
_t_, _RW_ _t_ is the reserve contribution from wind power at time _t_,
and ˆ _p_ _[c]_ _it_ [indicates the maximum possible power output for the]



_S_ _[c]_



_i,t_ _[c]_ _[−]_ _[I]_ _[c]_



where _λ_ _i_ is the start-up cost for _i_ _[th]_ unit.

In sum, the generalized formulation of CHP units includes
decision variables identified in (4) and (5), with energy production constraints of (4)–(7), flexibility constraints of (8)–(12),
fuel costs in (14) and (15). This modeling approach is applicable for all kinds of heat and power output constraints including
non-convex boundaries.


According to current regulations in China, CHP units are
required to be online during the entire heating period. During
this period, flexibility constraints (8) and (10), assuming _R_ _i_ _[d]_

equals to _R_ _i_ _[u]_ [, could be simplified as:]



_2) Zonal Heat Balance Constraints:_ The heating demand is
balanced separately within each heating district:



_N_ _h_
�


_i_ =1



_a_ _[h]_




_[h]_

_i j_ _[q]_ _[h]_



_i,t_ [+]



_i,t_ [+]



_it_ [+]



_i,t_ _[s]_ [=] _[ Q]_ _[jt]_



_N_ _c_
�


_i_ =1



_a_ _[c]_




_[c]_ _i j_ _[q]_ _[c]_



_N_ _e_
�


_i_ =1



_a_ _[eb]_




_[eb]_

_i j_ _[q]_ _i,t_ _[eb]_



_N_ _s_
�


_i_ =1



_a_ _[s]_




_[s]_ _i j_ _[q]_ _[s]_



(19)



where _Q_ _jt_ is the heating demand in heating district _j_ at time _t_, _q_ _[c]_



_it_ _[h]_ [,] _[ q]_ _it_ _[eb]_



_it_ [,] _[ q]_ _[s]_



_|P_ _[c]_




_[ c]_ _i,t−_ 1 _[| ≤]_ _[R]_ _i_ _[d]_



_i_ (16)



_i,t_ _[c]_ _[−]_ _[P]_ _[ c]_



_jt_, _it_ [,]

_q_ _it_ _[h]_ [,] _[ q]_ _it_ _[eb]_ [,] _[ q]_ _i,t_ _[s]_ [, as indicated earlier, are the heat output from heat]

boilers, the CHP unit, the electrical boiler and the heat storage
devices at time _t_ . _a_ _ij_ indicates the connectivity of the _i_ _[th]_ heating
source to the _j_ _[th]_ heating district. When _a_ _ij_ = 1, the _i_ _[th]_ heating
element is connected to the _j_ _[th]_ heating district. Otherwise, _a_ _ij_ =
0. _a_ _[h]_ _ij_ [,] _[ a]_ _[c]_ _ij_ [,] _[ a]_ _[eb]_ _ij_ [and] _[ a]_ _[s]_ _ij_ [represent the connectivity between the] _[ i]_ [th]

heat boiler, the CHP unit, the electrical boiler, the heat storage
device, and heating district j, respectively. Heat generated from
electrical boiler _i_ at time _t_ depends linearly on its consumption
of electricity with a constant conversion efficiency _η_ _i_ _[eb]_ [:]



_ij_ [and] _[ a]_ _[s]_



_D. Energy Storages Modeling_


A detailed representation of the heat storage simulation is embedded in the IPHO model. The mathematical modeling of the
energy balance within the storage tank accounts for stratification
of the interior water temperature, as detailed in [14].

A simplified electrical storage model, based on pumped hydro
storage, is also embedded in the model. The model differentiates between inlet and outlet efficiencies, and the maximum
charging/discharging rates. It incorporates constraints imposed
by limitations on available reservoir volumes, as detailed in [18].


_E. Other System Constraints_


The energy balance constraints include both the real time
balance for power supply and the heat balance for each heating
district. Reserve requirements and other restrictions for individual thermal units are also included in the model.


_1) Power Balance and Reserve Requirements:_ The system
load equals the sum of the power output from all power generating units, the CHP plants, the wind farms, and power imports,
subtracting the power demand from the electrical boilers according to:



_ij_ [,] _[ a]_ _[c]_ _ij_




_[c]_

_ij_ [,] _[ a]_ _[eb]_ _ij_



_i_ [:]



_q_ _[eb]_



_i,t_ _[eb]_ [=] _[ η]_ _i_ _[eb]_



_i_ _[eb]_ _[·][ p]_ _[eb]_ _it_



_it_ (20)



_N_ _p_
�


_i_ =1



_N_ _s_
�


_i_ =1



_p_ _[s]_ _it_ [+]



_p_ _[e]_ _it_ [+]



_N_ _c_
�


_i_ =1



_p_ _[c]_ _it_ [+]



_N_ _w_
�


_i_ =1



_p_ _[w]_ _it_ _[−]_



_N_ _e b_
�


_i_ =1



_p_ _[eb]_ _it_ [+]



_p_ _[s]_



_N_ _T_
�


_i_ =1



_p_ _[T]_ _it_



= _P_ _t_ _∀t_ (17)


where _N_ _eb_ and _N_ _s_ define the total number of electrical boilers
and pumped hydro storage units, respectively. _P_ _t_ is the system



_η_ _i_ _[eb]_ is taken as 90% in the following analysis.

_3) Other Constraints for Thermal Units:_ Other constraints
related to thermal units include the maximum and minimum

generation limits, as indicated in (a1), the ramping constraints,
as indicated in (a2), and minimum on/off time constraints as
indicated in (a3)–(a4). The constraints defined by (a1)–(a4) are
detailed in Appendix. The network constraint uses the power
distribution shift factor, based on DC power flow [14].


III. J ING -J IN -T ANG E NERGY S YSTEMS


A key regional energy system, Jing-Jin-Tang, is analyzed to
examine the challenges of integrating wind power in Northern China. The generation mix, heating districts, technological
characteristics of CHP units, and regulatory requirements are
introduced in this section. Data inputs and simulation setups are
presented.


_A. Jing-Jin-Tang Energy Systems_


The analysis is based on the Jing-Jin-Tang (JJT) regional
energy system. The Jing-Jin-Tang region is one of the three
largest economic centers in China. The regional power system
covers Beijing, Tianjin, Hebei, and parts of Inner Mongolia



Authorized licensed use limited to: Tsinghua University. Downloaded on May 04,2025 at 12:22:11 UTC from IEEE Xplore. Restrictions apply.


1314 IEEE TRANSACTIONS ON POWER SYSTEMS, VOL. 33, NO. 2, MARCH 2018


TABLE I

I NSTALLED C APACITIES OF P OWER P LANTS IN JJT A REA (2014)


Unit Total Installed Unit Average Fuel Average
Type Capacity (GW) Number [1] Use (g/kWh) [2,3] Price



Thermal (Coal) 26.5 53 303 0.37
Thermal (Gas) 1.47 6 351 0.65
CHP-coal 21.0 78 311 0.42
CHP-gas 1.80 7 351 0.65
Pumped Hydro 0.77 8 – –
Import 8.35 8 –
Export 3.65 3 –


Note:

1. The number for import/export accounts for the number of transmission corridors
2. The fuel efficiency is converted to Standard Coal Equivalent(SCE)/ kWh
3. For gas fired units, we use the national average efficiency for power generation of 35%.


TABLE II

H EATING D ISTRICT AND S UPPLY I NFORMATION


City City Aggregated heat Number of Annual heat
Number Name capacity Units production
(MW) (10,000 GJ/yr)


1 Beijing 5526 31 5518
2 Tianjin 3333 14 2921
3 Tangshan 3375 12 2483
4 Zhangjiakou 2149 7 1381
5 Datong 1920 10 1898
6 Qinhuangdao 1392 6 1073
7 Chengde 933 5 589


and Shanxi province. The total annual power demand in the
region amounted to about 360 TWh in 2014, approximately
equal to the annual electricity consumption of the combined
Nordic countries [21].

The generation mix in the area is constituted mainly by
conventional thermal units and CHP units, complemented by
pumped hydro and interregional transmission. The installed capacity, number of units and averaged fuel efficiency are summarized in Table I.


One ton of Standard Coal Equivalent represents the energy
generated by burning one metric ton of coal (with a heat content
of 7000 Cal), equivalent to 29.39 GJ or 8.14 MWh. An average
value of 303 gram of SCE per kWh is equivalent to an efficiency
of 40%. The average fuel efficiencies for coal fired generators are
from the State Grid Company, consistent with the data adopted
for hourly simulation described in Section E. The sample size
for the fuel efficiency of gas-fired units in this region is small and
the data reported from generation companies is not statistically
reliable. An average fuel efficiency of 35% for gas-fired units
is adopted here based on national data from [25]. Note that
the capacity of gas-fired unit accounts for only 5% of the total
capacity, the specification on its fuel efficiency has minimal
impact on final results. The average fuel efficiency for CHP
units in this region is based on statistical data from the State
Grid Company.

The CHP units in this regional power systems provide heat to
7 major cities. Each is considered as a separate heating district
in the following analysis. The city name, total CHP heating
capacity and unit number of CHPs are given in Table II.



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenIntegratedEnergySystems2018/chenIntegratedEnergySystems2018.pdf-5-0.png)

Fig. 3. Typical boundaries for power and heat output in CHP unit.


The generation structure in the JJT area generally represents
typical conditions for the northern part of China, where over
90% of the onshore wind farms are located. The average wind
capacity factor within this region is about 0.25, close to the
average capacity factor for Northern China.


_B. Operational Characteristics of CHP Units_


About 50% of the generation units in JJT area are CHPs
capable of producing simultaneously heat and power during the
heating season. The heating season lasts from October to March.
During the heating season, all of the CHP units are required to
be in operation, and the hourly heating demand is designated by
the heating district dispatch center.

Fig. 3 shows four actual boundaries that limit the power and
heat output of CHP units. The CHP boundary for power and heat
output is derived from the online monitoring system in the North
China Power Grid operational since 2013. Among all 83 CHP
units in this region, 43 have actual measured power-heat output
boundaries. The boundaries of other CHP units are derived from

the known boundaries according to similar capacity and type.


_C. The Regulations on Power Exchange and Reliability_


Interregional power exchange will affect the hourly power
balance and wind integration. Net import of power currently
amounts for approximately 10% of total power demand in this
area. As all targeted wind investments considered in this study
are deployed within the region, the necessity for interregional
transmission expansion is negligible and is ignored. The annual
average utilization rate for transmission assets within the JJT
region is as low as 20%, and electrical boilers function only
in winter when demand level is lower than that in summer.

Intra-regional transmission congestion attributed to power consumption from electrical boilers is minimal and is also ignored.

The exchange energy and daily profile of interregional transmission corridors are fixed on an annual basis in the form of
contracts between regional grid companies. The daily profile is
the same throughout the year, except for contingencies. During
off-peak hours, 11 p.m. to 6 a.m., transmitted power is equal to
half of the power transmitted during peaking periods.

The reserve requirement largely impacts both day-ahead unitcommitment decisions and wind curtailment. In the grid code
of the North China power system, the positive spin reserve is
equal to the largest capacity of a single online conventional unit,
currently 1000MW. The provision of spin reserve is mainly from
online thermal units and pumped hydro units; the contribution
of spin reserve from wind power is fixed and accounts only
for the firm capacity, whereas the inter-regional connection is



Authorized licensed use limited to: Tsinghua University. Downloaded on May 04,2025 at 12:22:11 UTC from IEEE Xplore. Restrictions apply.


CHEN _et al._ : INTEGRATED ENERGY SYSTEMS FOR HIGHER WIND PENETRATION IN CHINA 1315



not considered in the provision of spin reserve. Unlike some of
the other power systems around the world, the generation mix
in this region is dominated by coal fired units, for which the
start-up time is around 6–8 hours [26]. Hence the spin reserve
requirement is necessarily higher for this region.


_D. Hourly Power, Heat Demand and Wind Production_


The hourly load profile is based on the historical hourly load
profile for 2012 from the North China Dispatch Center, increased proportionally according to total energy demand.

Heating demands are balanced separately in different heating
districts. The hourly heat demands _Q_ _t_ in the seven heating zones
are determined according to ambient temperature _T_ _t_ _[e]_ [, regulated]

indoor temperature _T_ _t_ _[i]_ [s, and average building insulation (] _[IS]_ [):]



_Q_ _t_ = _IS ·_ ( _T_ _t_ _[i]_




_[ e]_ _t_ [)] _∀t_ (21)



is 0.26 in this region, generally consistent with the results from
major wind resource analyses [28].


_E. CO_ 2 _and Fuel Price Assumptions_


Fuel use for different types of units are based on measurements for typical units in the appropriate category converted to
standard coal equivalent. For the thermal units, the measured
costs at different power output level for generators with various
nameplate capacities (ranging from 100 MW to 600 MW) are
adopted from [24]. The cost curve is matched then to individual
power generation units in the JJT area according to the capacity.
For CHP units, the costs are from the internal data of the State
Grid Company, and the average fuel cost for power production
is summarized in Table I. Emission factors are calculated separately based on fuel use (coal/gas). The CO 2 emission factors
for coal and natural gas are taken as 3.67 and 2.75 ton of CO 2
per ton fuel respectively. The price for coal in this area is around
$80/ton.

Revenue depends both on costs for fuel and income from sales
of electricity. The price for electricity is fixed and regulated on
a plant by plant basis by Chinese Government. The revenue of a
typical CHP plant is based on a balance between revenue from
delivering of heat and power, offset by prices for fuel.


_F. Simulation Setup_


The conventional unit characteristics include capacity, minimal output and ramping rate. Intraregional transmission limits
are also considered in the optimization. To evaluate the economics of the investments in electrical boiler and heat storage,
the simulation is conducted on daily basis for an entire year.
In this case, we can capture the effectiveness of the seasonal
and weekly variation pattern of both power demand and wind
generation, which allows for more accurate assessment of the
economics. The model is solved by Mixed Integer Linear Optimization Programming provided by the CPLEX optimizer. The
annual simulation requires around 6.5 million variables in total,
in which 36,000 are binary.


IV. R ESULTS


The hourly simulation is applied to the JJT energy system for
an entire year with detailed data, using the IPHO model. Wind
power curtailment at different wind penetration levels under the
current power system structure and regulatory requirements is
analyzed first. The impacts of electric boilers and heat storage
on wind curtailment and CO 2 emissions are considered with
special attention to high levels of wind penetration. The section
follows with a discussion of the implied changes in revenue for
different energy producing stakeholders.


_A. Wind Power Integration Under Different Penetration Levels_


The simulation is conducted on an hourly basis over an entire
year. The power output and curtailments computed appropriate for an installed wind capacity of 23 GW are displayed in
Fig. 4. Curtailment is indicated by the shaded area. Curtailment
is highest during the spring festival, in late January, when power



_t_ _[−]_ _[T]_ _[ e]_ _t_



As the building insulation is fixed and the indoor temperature
is required to be above 18 _[◦]_ C [17], hourly heat demand depends
mainly on the variation in ambient temperature. The total energy
demand for space heating presented in Table II is allocated for
each hour of the heating season in proportion to the temperature
difference between ambient conditions and the regulated indoor
temperature of 18 _[◦]_ C. The hourly ambient temperature data is
derived from the NASA-assimilated meteorological database
(GEOS-5).

The wind data are derived also from wind fields compiled
in GEOS-5. This database defines hourly wind speeds with a
spatial resolution of 1/3 degree longitude by 1/4 degree latitude. Hourly wind speeds at 100 m height are extrapolated from
50 m, using the vertical profile of the power law described by
Archer and Jacobson [27]. Instead of assuming a value of 1/7
for the associated friction coefficient, we evaluated the friction
coefficient in this analysis using wind speeds represented at
10 m and 50 m for each grid cell [17]. Wind power is computed
hourly using the power curve for GE 2.5 MW wind turbines,
a typical system deployed for onshore applications [17]. Windgenerated electricity supplied to Beijing is assumed to originate
from wind farms deployed in suitable areas of Hebei province,
the major wind concentration within the JJT region. Areas that
are forested, covered with water, urban or otherwise settled, or
characterized by slopes of more than 20%, are excluded.

Several factors may influence the simulation of wind capacity
factors as compared with actual power output from operational
wind farms. The simulated capacity factor is averaged in regions
suitable for development of wind farms, while the actual wind
farms optimize the siting of individual turbines based on local
topography and wind conditions. The interference between wind
turbines in actual wind farms might be stronger than the results
simulated here, which might offset partly the underestimation
of wind outputs due to local turbine placements. Additional uncertainty may result from different geographical configurations
and surface roughness for the actual situation as compared to the
NASA reanalysis database. However, the wind power simulation adopted here is generally consistent in temporal variations
and average results compared with the actual measured data.
The annual average capacity factor from the above simulation



Authorized licensed use limited to: Tsinghua University. Downloaded on May 04,2025 at 12:22:11 UTC from IEEE Xplore. Restrictions apply.


1316 IEEE TRANSACTIONS ON POWER SYSTEMS, VOL. 33, NO. 2, MARCH 2018

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenIntegratedEnergySystems2018/chenIntegratedEnergySystems2018.pdf-7-0.png)


Fig. 4. Daily wind curtailment throughout the year.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenIntegratedEnergySystems2018/chenIntegratedEnergySystems2018.pdf-7-1.png)


Fig. 6. Variation of wind curtailment under different wind capacity.

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenIntegratedEnergySystems2018/chenIntegratedEnergySystems2018.pdf-7-2.png)


Fig. 5. The hourly power balance for JJT area in January.


demand is 30% lower than normal. Curtailment reaches 34% in

January, averages 20% over the first half of the heating season,
declining to less than 15% during the non-heating season.



The hourly power balance for January is shown in Fig. 5.
All CHP units are kept online during the month to secure the
required source of heat, and thus have to supply almost half of
the load during off-peak periods and one third during peaking
periods. The daily profile for net-import power is fixed independent of wind power fluctuations, as indicated by the yellow
shading in the figure. A significant share of thermal power must
be kept online even on days with strong wind and significant
wind curtailment to provide spin reserve to accommodate for
possible failure of a major thermal plant or for an unexpected
ramp down of wind power. The major barrier on the technical
side results from heat-driven CHP units, and inflexible thermal
units; regulatory requirements such as high spin reserve capacity and fixed inter-regional transmission schedules also diminish
the ability of the system to accommodate available wind power.

Considering the response to an increase in installed wind capacity from 0 to 23 GW, the analysis indicates that the annual
wind curtailment rate increases in an accelerating rate as shown
in Fig. 6. The curtailment rate is 18% at 23 GW wind penetration. The analysis indicates that even under the ideal condition,
integration of wind power is challenging at higher penetration
level given the existing generation mix and regulatory require
ments.


An increase wind curtailment will result in a reduction in

the actually realized average capacity factor, and thus a decline
in revenue for the wind power producer. As shown in Fig. 7,
the capacity factor for wind power decline from 0.26 at a low
penetration level for wind to 0.21 at a penetration level of about
14% of wind penetration. The capacity factor of 0.21 (equivalent
to 1850 full load hours) defines the revenue break-even point
under current feed-in tariff and investment cost conditions for

wind power. This indicates that wind power in this area could
account at most for 14% of total power supply under current
energy system specification.



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenIntegratedEnergySystems2018/chenIntegratedEnergySystems2018.pdf-7-3.png)

Fig. 7. Capacity factor changes under different wind penetration rate.


In comparison, renewable energy is expected to supply 20% of
total energy demand in China by 2030, roughly equal to 40% of
renewables in the power system. The current analysis indicates
that this target could be exceptionally challenging under current
feed-in tariff and grid structures. As most of the infrastructure
will be locked in for at least 30 years, the challenge for the
development of wind power if not addressed, is likely to persist
for the long term.


_B. Improvement of Wind Power Integration With Different_
_Flexible Heating Solutions_


For better integration of wind power at higher penetration
levels, we explore the advantages that could be realized using electric boiler and heat storage. Compared with the Business As Usual (BAU) scenario (same generation structure as in
Section A), three alternative scenarios were simulated for the
heating season assuming a wind capacity of 23 GW.

1) E-boiler: The E-boiler scenario assumes installation of

electric boilers in each of the heating districts capable of
supplying up to one third of the maximum heat demand;
2) H-storage: The H-storage scenario assumes heat stor
age systems incorporated in each heating district. The
maximum heat output is assumed equal to one third of
the maximum heat demand for the heating district capable of supplying heat for eight hours at maximum
charging/discharging rate;



Authorized licensed use limited to: Tsinghua University. Downloaded on May 04,2025 at 12:22:11 UTC from IEEE Xplore. Restrictions apply.


CHEN _et al._ : INTEGRATED ENERGY SYSTEMS FOR HIGHER WIND PENETRATION IN CHINA 1317

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenIntegratedEnergySystems2018/chenIntegratedEnergySystems2018.pdf-8-0.png)


Fig. 9. The hourly power balance in different scenarios from Jan. 10-Jan 20.



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenIntegratedEnergySystems2018/chenIntegratedEnergySystems2018.pdf-8-1.png)

Fig. 8. The daily wind integration in heating season under different heating
technologies.


TABLE III

C OAL U SE AND CO 2 E MISSIONS FOR E ACH T YPE OF E NERGY S OURCE FOR

D IFFERENT S CENARIOS (M ILLION T ONS )


BAU E-boiler H-storage Both


Thermal unit 14.35 15.25 14.37 15.20

CHP 25.61 24.06 25.43 23.99

Total Fossil Fuel 40.00 39.30 39.81 39.20

Total Emissions 146.5 144.1 145.9 143.7
Fuel Cost Saving (million USD) – 55 12 63


3) Both: This scenario incorporates both the electric boiler

and heat storage options with the same capacity as in the
E-boiler and H-storage scenarios.
Results for these scenarios are as follows:

_1) Wind Integration:_ The daily average wind capacity factors and the associated shares of wind curtailment in the heating
season under these scenarios are shown in Fig. 8. Note that results for all scenarios are the same for the non-heating season as
demonstrated in Fig. 4, we show only results for heating periods
in Fig. 8.

Introducing electric boilers would significantly reduce wind
curtailment. The wind curtailment rate would be lowered to

3.2% during the heating season with the E-boiler scenario as
compared to 15% in the BAU case. Curtailment occurs during
58 of the first 100 days in the heating season with the BAU
scenario; curtailment is reduced to 27 days with the E-boiler
scenario, to 25 days when the E-boiler and H-storage options
are combined. Investing in heat storage alone is not as effective
as either E-boiler or combined options; curtailment is indicated
to occur over 57 days with the H-storage scenario. The overall
reduction in annual costs and emissions due to reduced wind

curtailment will be summarized later in Table III.


_2) Power and Energy Production Changes:_ Hourly power
balances corresponding to the different scenarios over a
10-day period are illustrated in Fig. 9. With the E-boiler scenario, the net power output for CHP (power generated from CHP
subtracting the power consumed by electric boilers) is reduced,
and consequently curtailment is mostly eliminated during these
time periods. The lower net power production in CHP units pro


vides also for extra spin reserve during operation. Note that the
fluctuations of net load are also reduced, an extra benefit from
the flexible heating sources.

The effectiveness of heat storage depends on the daily variation of the wind source. Heat storage could decrease the power
production from CHP when curtailment occurs only during offpeak hours, while heat storage provides minimal benefit when
wind curtailment persists over the entire day. It reflects the fact
that the energy in the heat storage system must be balanced on
a daily basis. Wind power maintains a relatively high power
output typically for 2–4 days. Heat storage is more likely to be
helpful during starting and ending periods. The hourly balance
when combining electric boiler and heat storage option is similar to the results observed with the E-boiler scenario; in both
case, requirements for wind curtailment are markedly reduced.


_C. Investment Feasibilities, Revenue Distribution and Changes_


The demand for coal in the energy system during the heating
season is summarized in Table III. Total coal consumption from
CHP in the E-boiler and H-storage scenarios decrease by about
0.7 and 0.2 million ton respectively, compared with BAU, while
the total coal consumption from conventional thermal units remains relatively unchanged. As a result, the combination of
electric boiler and heat storage is effective in reducing CO 2
emissions by 3 million tons.

The E-boiler and H-storage scenarios result in a reduction in
annual fuel cost of 55 million and 12 million US dollars as com
pared with BAU. Given that the cost of the electric boiler and
heat storage options are respectively $0.056 million/MW and
$0.0037 million/MWh [22], the overall additional investments
in E-boiler scenario and H-storage scenario would amount to
314 million USD and 165 million USD respectively. The investment would be paid back in 5.7 years with the E-boiler
scenario. The payback time for the combined electric boiler and
heat storage scenario would be 10.2 years. It follows that integrating the power and heating system through the electric boiler
option (and further coupling with heat storage) would provide
an economically constructive approach to reducing wind curtailment and CO 2 emissions in Northern China, especially when
compared with the alternative deployment of electrical storage
technologies.



Authorized licensed use limited to: Tsinghua University. Downloaded on May 04,2025 at 12:22:11 UTC from IEEE Xplore. Restrictions apply.


1318 IEEE TRANSACTIONS ON POWER SYSTEMS, VOL. 33, NO. 2, MARCH 2018



The total revenue for all energy generating entities in the
E-boiler scenario is higher than for BAU over the entire heating
season. Generation is shifted from coal fired units selling electricity at a price of about 5 cents/kWh with a fuel cost of about
2 cents/kWh, to wind power marketing electricity at a price of
8 cents/kWh with a close to zero variable operational cost.

Given the current business model, however, the enhancement
in revenue relative to BAU is distributed unevenly among all
energy production stakeholders. The revenue for wind increases
proportionally with wind energy production under the present
feed-in tariff scheme. In contrast, the revenue for CHP units will
decline as the net power production from CHP units is reduced.
Additional incentives must be provided if CHP units are to invest
in these flexible heating sources. A potential strategy would be
to allow bilateral contracts between CHP and wind producers,
in which case CHP units could transfer their generation rights
to wind generators at a negotiated price (say, 0.25 RMB/kWh
or 3.6 cents/kWh).


V. C ONCLUSION


In this paper, a typical regional energy system in North China
(covering Jing-Jin-Tang area) was simulated on an hourly basis
throughout an entire year assuming different penetration levels
for wind. The simulation was based on an integrated power and
heat optimization model, with detailed description of district
level heat balances and operational constraints on CHP units.
The investment return for wind power decreases at higher penetration levels, reflecting an increase in wind curtailment. Wind
power could account for at most 14% of the total power consumption in this region under the current feed-in-tariff scheme.
At such wind levels, installing electric boilers could reduce
wind curtailment during the heating season, decreasing CO 2
emissions by about 3 million tons, while increasing the overall revenue for generation entities. The investment in electric
boilers could be paid back in less than 6 years as a consequence of the savings in overall fuel use. However, while wind
generators would benefit from this improvement, the revenue
for CHP plants- the potential investor in the electrical boiler
option- would decrease.

Similar to most existing studies on storage, this investigation
balances the energy in heat storage on a daily basis. As the cycling of storage is on daily basis while wind power tends to vary
on a longer cycle, the benefit of heat storage may be improved
on longer time scales by optimizing inter-day operation, a topic
scheduled to be explored in a future study. This study treats
the existing generation and transmission network as given. In
the long run, the expansion of transmission networks will also
influence the optimal deployment of energy generating assets.
Coordinated planning for energy systems considering network
expansion merits further investigation in future studies.


A PPENDIX

C ONSTRAINTS FOR T HERMAL U NITS


Power output for the _i_ _[th]_ thermal unit includes the ramping
constraints



where A ~~_i_~~ and A [¯] _i_ are the lower and upper limits of power output
for unit I respectively. Ramping constraints for the _i_ th thermal
unit are defined according to [19] as follows:



⎧⎪⎪⎪⎨⎪⎪⎪⎩




_[e]_ _i,t_ _[≤]_ [A][¯] _[i]_ _[ ·][ I]_ _[e]_



_i,t_ _[e]_ _[−]_ _[I]_ _[e]_



_i,t_ _[c]_ _−_ 1 [+] _[ S]_ _i_ _[u]_




[+] _[ S]_ _i_ _[u]_ _[·]_ [ (] _[I]_ _i,t_ _[e]_ _[−]_ _[I]_ _i,t_ _[e]_ _−_ 1 [)]

+ A [¯] _i_ _·_ (1 _−_ _I_ _[e]_ [)]



_i_ _[u]_ _[·]_ [ (] _[I]_ _[e]_



_p_ _[e]_




_[e]_ _i,t_ _[−]_ _[p]_ _[e]_




_[e]_ _i,t−_ 1 _[≤]_ _[R]_ _i_ _[u]_



_i_ _[u]_ _[·][ I]_ _[c]_




_[e]_

_i,t_ [)]



_i,t_ _[e]_ [+] _[ S]_ _i_ _[d]_



_i,t_ _[e]_ _−_ 1 _[−]_ _[I]_ _[e]_




_[e]_

_i,t_ [))]



(a2)



_i_ _[·]_ [ (] _[I]_ _[e]_



_p_ _[e]_


_p_ _[e]_




_[e]_ _i,t_ _[−]_ _[p]_ _[e]_




_[e]_ _i,t−_ 1 _[≥−]_ [(] _[R]_ _i_ _[d]_



_i,t_ _[e]_ +1 [+] _[ S]_ _i_ _[d]_



_i_ _[·][ I]_ _[e]_



_i_ _[·]_ [ (] _[I]_ _[e]_



_i,t_ _[e]_ _[−]_ _[I]_ _[e]_




_[e]_

_i,t_ +1 [)]



where _R_ _[d]_



where _R_ _i_ [and] _[ R]_ _i_ _[u]_ [are the ramp-up and ramp-down limits for] _[ i]_ [th]

unit. The minimal on/off time constraints for thermal units are

formulated according to [19], as follows:



_i_ [and] _[ R]_ _i_ _[u]_



� _Gt_ _i_



⎧⎪⎪⎪⎪⎨⎪⎪⎪⎪⎩



_v_ = _t_ [(] _[I]_ _[e]_



_i,v_ _[e]_ _[−]_ [(] _[I]_ _[e]_



� _t_ =1 _i_ [(1] _[ −]_ _[I]_ _i,t_ _[e]_ [) = 0]

� _tv_ += _U Tt_ _i_ _−_ 1 ( _I_ _[e]_ [)] _[ ≥]_



_i_

_t_ =1 [(1] _[ −]_ _[I]_ _i,t_ _[e]_

_tv_ += _U Tt_ _i_ _−_ 1 ( _I_ _[e]_



_i,t_ _[e]_ _[−]_ _[I]_ _[e]_




_[e]_

_i,v_ [)] _[ ≥]_ _[UT]_ _[i]_ _[ ·]_ [ (] _[I]_ _[e]_



_i,t_ _[e]_ _−_ 1 [)] _[ t]_ [ =] _[ G]_ _[j]_ [ + 1] _[, . . . .,]_



_T −_ _UT_ _i_ + 1

_T_
� _v_ = _t_ [(] _[I]_ _[e]_ _[−]_ [(] _[I]_ _[e]_ _[−]_ _[I]_ _[e]_



_i,t_ _[e]_ _[−]_ _[I]_ _[e]_



_i,t_ _[e]_ _−_ 1 [))] _[ ≥]_ [0] _[ t]_ [ =] _[ T][ −]_ _[UT]_ _[t]_ [ + 2] _[, . . . ., T]_



(a3)
⎧⎪⎪⎪⎪⎪⎪⎪�� _Gttv_ =1+= _i_ _DTt_ _[I]_ _i,t_ _[e]_ _i_ _−_ 1 [= 0] (1 _−_ _I_ _i,v_ _[c]_ [)] _[ ≥]_ _[DT]_ _[i]_ _[ ·]_ [ (] _[I]_ _i,t_ _[e]_ _−_ 1 _[−]_ _[I]_ _i,t_ _[e]_ [)] _[ t]_ [ =] _[ G]_ _[i]_ [ + 1] _[,]_


_. . . ., T −_ _DT_ _i_ + 1

⎨



� _Gt_ _i_



_i_

_t_ =1 _[I]_ _[e]_



� _t_ =1 _i_ _[I]_ _i,t_ _[e]_ [= 0]

_t_ + _DT_ _i_ _−_ 1
� _v_ = _t_ (1




_[c]_

_i,v_ [)] _[ ≥]_ _[DT]_ _[i]_ _[ ·]_ [ (] _[I]_ _[e]_



_i_ _−_

_v_ = _t_ (1 _−_ _I_ _[c]_



_i,t_ _[e]_ _−_ 1 _[−]_ _[I]_ _[e]_




_[e]_

_i,t_ [)] _[ t]_ [ =] _[ G]_ _[i]_ [ + 1] _[,]_



_. . . ., T −_ _DT_ _i_ + 1

_T_
� _v_ = _t_ [(1] _[ −]_ _[I]_ _[e]_ _[−]_ [(] _[I]_ _[e]_ _−_ 1 _[−]_ _[I]_ _[e]_ [))]



⎪⎪⎪⎪⎪⎪⎪⎩



_v_ = _t_ [(1] _[ −]_ _[I]_ _[e]_



_i,v_ _[e]_ _[−]_ [(] _[I]_ _[e]_




_[−]_ [(] _[I]_ _i,t_ _[e]_ _−_ 1 _[−]_ _[I]_ _i,t_ _[e]_ [))] _[ ≥]_ [0] _t_ = _T −_ _DT_ _t_


+ 2 _, . . . ., T_



_i,t_ _[e]_ _−_ 1 _[−]_ _[I]_ _i,t_ _[e]_



A ~~_i_~~ _· I_ _[e]_



_it_ _[e]_ _[≤]_ _[p]_ _[e]_




_[e]_ _i,t_ _[≤]_ [A][¯] _[i]_ _[·][ I]_ _[e]_



_it_ _[e]_ (a1)



(a4)


where _DT_ _i_ and _UT_ _i_ represent the minimal on/off time of the
_i_ _[th]_ unit respectively, _G_ _i_ is the time interval to allow for change
in on-off status with respect to the initial conditions, similar to
the description in (12).


R EFERENCES


[1] Global Wind Energy Council, 2015, Brussels, Belgium, Global Wind

Statistics 2014. [Online]. Available: http://www.gwec.net/wpcontent/
uploads/2015/02/GWEC_GlobalWindStats2014_FINAL_10.2.2015.pdf

[2] International Energy Agency, Paris, France, China Wind Energy De
velopment Roadmap 2050. 2011. [Online]. Available: https://www.iea.
org/publications/freepublications/publication/china_wind.pdf

[3] K. Chongqing _et al._, “Balance of power: Towards a more environmentally

friendly, efficient, and effective integration of energy systems in China,”
_IEEE Power Energy Mag._, vol. 11, no. 5, pp. 56–64, Sep./Oct. 2013.

[4] H. Lund, E. M¨unster, and L. H. Tambjerg. EnergyPlan, Computer model

for energy system analysis version 6.0. Department of Development and
Planning, Aalborg University, Aalborg, Denmark, 2004.

[5] R. Lahdelma and H. Hakonen, “An efficient linear programming algorithm

for combined heat and power production,” _Eur. J. Oper. Res._, vol. 148,
no. 1, pp. 141–151, 2003.

[6] H. Lund, “Large-scale integration of wind power into different energy

systems,” _Energy_, vol. 30, no. 13, pp. 2402–2412, Oct. 2005.

[7] H. Lund _et al._, “4th generation district heating (4GDH). Integrating smart

thermal grids into future sustainable energy systems,” _Energy_, vol. 68,
pp. 1–11, Apr. 2014.

[8] W. Xionga, Y. Wanga, B. V. Mathiesen, H. Lund, and X. Zhang, “Heat

roadmap China: New heat strategy to reduce energy consumption towards
2030,” _Energy_, vol. 81, pp. 274–285, Mar. 2015.

[9] Z. X. Jing, X. S. Jiang, Q. H. Wu, W. H. Tang, and B. Hua, “Modelling and

optimal operation of a small-scale integrated energy based district heating
and cooling system,” _Energy_, vol. 73, pp. 399–415, 2014.

[10] P. Meibom, J. Kiviluoma, R. Barth, H. Brand, C. Weber, and H. V. Larsen,

“Value of electric heat boilers and heat pumps for wind power integration,”
_Wind Energy_, vol. 10, no. 4, pp. 321–337, 2007.



Authorized licensed use limited to: Tsinghua University. Downloaded on May 04,2025 at 12:22:11 UTC from IEEE Xplore. Restrictions apply.


CHEN _et al._ : INTEGRATED ENERGY SYSTEMS FOR HIGHER WIND PENETRATION IN CHINA 1319




[11] V. Vittorio and C. Francesco, “Primary energy savings through thermal

storage in district heating networks,” _Energy_, vol. 36, no. 7, pp. 4278–
4286, Jul. 2011.

[12] G. Streckiene, V. Martianaitis, A. N. Andersen, “Feasibility of CHP-plants

with thermal stores in the German spot market,” _Appl. Energy_, vol. 86,
pp. 2308–16, 2009.

[13] K. Hedegaard, B. V. Mathiesen, H. Lund, and P. Heiselberg, “Wind power

integration using individual heat pumps—Analysis of different heat storage options,” _Energy_, vol. 47, no. 1, pp. 284–293, 2012.

[14] X. Chen, C. Kang, M. O’Malley _et al._, “Increasing the flexibility of com
bined heat and power for wind power integration in china: Modeling and
implications,” _IEEE Trans. Power Syst._, vol. 30, no. 4, pp. 1848–1857,
Jul. 2015.

[15] Z. Li, W. Wu, M. Shahidehpour, J. Wang, and B. Zhang, “Combined heat

and power dispatch considering pipeline energy storage of district heating
network,” _IEEE Trans. Sustain. Energy_, vol. 7, no. 1, pp. 12–22, Jan. 2016.

[16] S. Rinne and S. Syri, “The possibilities of combined heat and power

production balancing large amounts of wind power in Finland,” _Energy_,
vol. 82, pp. 1034–1046, 2015.

[17] X. Chen _et al._, “Synergies of wind power and electrified space heating:

Case study for beijing,” _Environ. Sci. Technol._, vol. 48, no. 3, pp. 2016–
2024, 2014.

[18] G. U. Zepeng, K. Chongqing, C. Xinyu _et al._, “Operation optimization of

integrated power and heat energy systems and the benefit on wind power
accommodation considering heating network constraints,” _Proc. CSEE_,
vol. 35, no. 14, pp. 3596–3604, 2015. (in Chinese)

[19] M. Carrion and J. Arroyo, “A computationally efficient mixed-integer

linear formulation for the thermal unit commitment problem,” _IEEE Trans._
_Power Syst._, vol. 21, no. 3, pp. 1371–1378, Aug. 2006.

[20] H. Lund, “Large-scale integration of wind power into different energy

systems,” _Energy_, vol. 30, no. 13, pp. 2402–2412, Oct. 2005,

[21] Nordic Energy Regulators, “Nordic market report,” 2014. [Online].

Available: http://www.nordicenergyregulators.org/wp-content/uploads/
2014/06/Nordic-Market-Report-2014.pdf

[22] Danish Ministry of Energy, Utilities and Climate: “Technology data

for energy plants: Generation of electricity and district heating, energy
storage and energy carrier generation and conversion.” 2012. [Online].
Available: http://www.energinet.dk/SiteCollectionDocuments/Danske%
20dokumenter/Forskning/Technology_data_for_energy_plants.pdf

[23] R. Lahdelma and H. Hakonen, “An efficient linear programming algorithm

for combined heat and power production,” _Eur. J. Oper. Res._, vol. 148,
no. 1, pp. 141–151, Jan. 2003.

[24] H. Zhong, Q. Xia, Y. Chen, and C. Kang, “Energy-saving generation dis
patch toward sustainable electric power industry,” _Energy Policy_, vol. 83,
pp. 14–25, 2015.

[25] ECO-FYS, Utrecht, The Netherlands, “Comparison of efficiency fossil

power generation.” 2006. [Online]. Available: http://www.ecofys.com/
files/files/ecofyscomparison_fossil_power_efficiencyaug2006_02.pdf

[26] Coal Fired Unit Start-Up Time. 2014. [Online] Available: https://www.

usea.org/sites/default/files/092014_Increasing%20the%20flexibility%20
of%20coal-fired%20power%20plants_ccc242.pdf

[27] C. L. Archer and M. Z. Jacobson, “Evaluation of global wind power,”

_J. Geophys. Res._, vol. 110, no. D12, 2005, Art. no. D12110.

[28] M. B. McElroy, X. Lu, C. P. Nielsen, and Y. Wang, “Potential for wind
generated electricity in China,” _Science_, vol. 325 no. 5946, pp. 1378–1380.
2009.



**Xinyu Chen** (M’14) received the B.S. and Ph.D. degrees in electrical engineering from Tsinghua University, Beijing, China, in 2009 and 2014, respectively.

He was an exchange Ph.D. student at Harvard
University, Cambridge, MA, USA, in 2012. From
2015 to 2016, he was a Post-Doctoral Researcher at
Harvard University. Since 2016, he has been a Lecturer with Harvard University. His research interests
include power system operation and planning, multienergy system optimization, renewable energy integration, and energy policy.


**Michael B. McElroy** received the B.S. and Ph.D.
degrees in applied mathematics from Queen’s University, Belfast, Ireland, in 1960 and 1962, respectively. He was the Director of the Center for Earth
and Planetary Sciences, the Founding Director of the
Center for the Environment, and the Founding Chair
of the Department of Earth and Planetary Sciences at
Harvard University, Cambridge, MA, USA, where he
has been Gilbert Butler Professor of Environmental
Studies since 1970, and heads up the China Project,
which has collaborated for more than 20 years with

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenIntegratedEnergySystems2018/chenIntegratedEnergySystems2018.pdf-10-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenIntegratedEnergySystems2018/chenIntegratedEnergySystems2018.pdf-10-1.png)
colleagues in China to advance this last objective. For ten years. He was a
member of the China Council for International Cooperation on Environment
and Development, a body constituted in 1992 to advise the Chinese Premier
on these important interconnected issues. He is the author of more than 250
journal articles, three books, three edited books, and more than 30 articles in
_Nature_ and _Science_ . His research interests include atomic physics, planetary science, atmospheric chemistry, climate science, global climate change mitigation,
power and energy science and policy, and the challenges posed for sustainable
development in China.

Prof. McElroy is a Fellow of the American Academy of Arts and Science, the
American Association for the Advanced of Science, the American Geophysical
Union, and Royal Irish Academy of Arts and Science.


**Chongqing Kang** (M’01–SM’07–F’ 17) received the
Ph.D. degree from the Department of Electrical Engineering in Tsinghua University, China, in 1997. He is
now a Professor at the same university. His research
interests include power system load forecasting, lowcarbon electricity, and power system planning.



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/chenIntegratedEnergySystems2018/chenIntegratedEnergySystems2018.pdf-10-2.png)

Authorized licensed use limited to: Tsinghua University. Downloaded on May 04,2025 at 12:22:11 UTC from IEEE Xplore. Restrictions apply.


