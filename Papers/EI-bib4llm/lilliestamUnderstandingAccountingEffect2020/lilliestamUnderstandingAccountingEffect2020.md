# Citation Key: lilliestamUnderstandingAccountingEffect2020

---

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lilliestamUnderstandingAccountingEffect2020/lilliestamUnderstandingAccountingEffect2020.pdf-0-0.png)
## **Understanding and accounting for the effect** **of exchange rate fluctuations on global** **learning rates**

**[<PERSON>](http://orcid.org/0000-0001-6913-5956)** **[1,2]** **[*, <PERSON>](http://orcid.org/0000-0003-0617-2578)** **[1]** **, <PERSON>** **[1]** **[, <PERSON>](http://orcid.org/0000-0002-7971-2187)** **[3,4]** **[and <PERSON><PERSON><PERSON>](http://orcid.org/0000-0003-2219-1402)** **[3,4]**


**Learning rates are a central concept in energy system models and integrated assessment models, as they allow researchers**
**to project the future costs of new technologies and to optimize energy system costs. Here we argue that exchange rate fluc-**
**tuations are an important, but thus far overlooked, determinant of the learning-rate variance observed in the literature. We**
**explore how empirically observed global learning rates depend on where technologies are installed and which currency is used**
**to calculate the learning rate. Using global data of large-scale photovoltaic (≥5 MW) plants, we show that the currency choice**
**can result in learning-rate differences of up to 16 percentage points. We then introduce an adjustment factor to correct for**
**the effect of exchange rate and market focus fluctuations and discuss the implications of our findings for innovation scholars,**
**energy modellers and decision makers.**



hen new technologies mature, their costs and performance typically improve due to learning and econo# W mies of scale in both the manufacturing and use of the

technology [1][,][2] . The typical metric that determines the extent of these
effects is the learning rate, which describes the relative cost reduction per doubling of the cumulative output [3] .
Learning rates are a central concept in energy system models
(ESMs) and integrated assessment models (IAMs), as they allow
researchers to project the future costs of new technologies, which
include renewables [4][,][5] . These models have become standard tools not
only in science, but also in supporting policymakers to formulate
effective and efficient strategies to address climate change—one
of the most pressing challenges of our time [6][–][8] . Over the past two
decades, a large amount of learning-rate literature has emerged
that examines and identifies learning rates for new technologies,
such as photovoltaics (PV) [9], concentrated solar power [10], wind
power [11][,][12], battery storage [13] and conventional energy conversion [14] .
Learning-rate studies have become increasingly sophisticated as
they correct for technology-exogenous parameters [15], use multivariate approaches, such as two-factor learning—which considers R&D
spending as a cost-reduction driver [15][–][18] —and differentiate between
global and local learning [19][,][20] .
Learning-curve analyses were also extended to investigate how
other parameters develop as a function of cumulative deployment—
for example, to analyse the development of renewable energy
financing costs [21] and energetic performance [22] . Although our understanding of technological learning curves and their underlying
processes has increased greatly, identified learning rates vary substantially between studies. For example, a meta-analysis by Rubin
et al. [1] found learning rates for PV modules of 10–47% (mean, 23%)
and for wind power of –11 to +34% (mean, 14%), and note that the
ranges are not explainable by systematic variation in key variables.
However, learning rates vary across time and geographies as they
are influenced by a range of factors: technology develops unevenly
at different times, and technology development is influenced by



(typically national) policy [23][,][24] . Learning rates may also change as the
economic context develops, for example, as wages increase or more
or less hidden (industry) subsidies are introduced or abandoned [25] .
These large ranges in observed learning rates are an especially grave
problem for the modellers who use learning rates in their analyses. As IAMs and ESMs typically generate cost-minimized energy
scenarios, the technology with the highest learning rate is likely to
become dominant—and a difference of a few percentage points can
lead to an entirely different power mix [26][,][27] . Hence, learning-rate figures must be robust and measure as precisely and exclusively as possible what they are supposed to measure (technological learning);
otherwise, models may produce ‘misleading results for policy’ [28][–][30] .
Here we argue that estimates of global learning rates, based on
expansion in several countries and currency areas, are biased because
they overlook the effect of currency exchange rate (FX) fluctuations.
This effect is important as renewables deployment happens in multiple currency areas simultaneously and as the geographical focus
shifts over time. We explore how empirically observed global learning rates depend on which currency is used to calculate the learning
rate. Using global data of large-scale PV (≥5 MW) plants, we show
that the currency choice can result in learning-rate differences of up
to 16 percentage points. We then introduce an adjustment factor to
correct for the effect of exchange rate and market focus fluctuations
and discuss the implications of our findings for innovation scholars,
energy modellers and decision makers.


**Dynamics in PV markets and exchange rates**
As renewables are deployed globally and manufactured in various
countries, learning effects cross boundaries and currency areas. To
estimate a global learning rate, all cost statements must be converted
into one currency—the base currency. The common practice for
doing this is to convert the average local costs into a base currency
with the relevant exchange rate at the time of installation, create an
average cost (typically unweighted, based on project counts [10][,][27][,][31][–][33] )
of all projects during a time interval (often yearly), and then deflate



1 Institute for Advanced Sustainability Studies (IASS), Potsdam, Germany. 2 University of Potsdam, Potsdam, Germany. 3 Energy Politics Group, Department
of Humanities, Social and Political Sciences, ETH Zürich, Zürich, Switzerland. [4] Institute of Science, Technology and Policy, ETH Zürich, Zürich, Switzerland.
[*e-mail: <EMAIL>](mailto:<EMAIL>)


**Nature EnergY** [| VOL 5 | January 2020 | 71–78 | www.nature.com/natureenergy](http://www.nature.com/natureenergy) **71**


#### Articles Nature Energy



**a**


**c**



60


40


20


0


1.4


1.2


1.0



**b**



China


Euro area


United Kingdom


India


Japan


United States


ROW


CNY


EUR


GBP



1.00


0.75


0.50


0.25


0.00



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lilliestamUnderstandingAccountingEffect2020/lilliestamUnderstandingAccountingEffect2020.pdf-1-0.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lilliestamUnderstandingAccountingEffect2020/lilliestamUnderstandingAccountingEffect2020.pdf-1-1.png)

![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lilliestamUnderstandingAccountingEffect2020/lilliestamUnderstandingAccountingEffect2020.pdf-1-2.png)

INR


JPY


0.8


**Fig. 1 |** **Global large PV deployment by country and exchange rates.** **a**, **b**, Deployment of ≥5 MW plants as total global capacity ( **a** ), shares of yearly
installations ( **b** ) and development of the nominal exchange rates of the main markets for large PV from January 2006 to December 2016, indexed in
(CNY, EUR, GBP, INR or JPY)/USD, with January 2006 = 1 ( **c** ); in **b**, an increase indicates appreciation and a decrease indicates depreciation against the USD.
Sources: IRENA [42] ( **a**, **b** ) and OFX [43] and OECD [44] ( **c** ).



the nominal into real average costs using a deflator for the base currency. Often, the US dollar (USD) is chosen as base currency, especially in global learning-rate analyses [17][,][34][,][35] . Although this reflects
the dominance of USD in global trade and economic analyses,
global studies based on the euro (EUR) exist as well [36][,][37] . For national
studies, authors often use the local currency of the investigated
region, such as the Chinese yuan [38][,][39] (CNY), the Korean won [40] or
the Danish krona [41] . However, the past decade has seen a shift in the
geographical focus of renewables and particularly large-scale PV
expansion from Europe (mainly Germany, Italy and Spain) to Asia
(primarily China and Japan) and a simultaneous 12-fold increase
in global capacity installation pace from 2006 to 2016 [42] . This has
three implications regarding the relationship between technological
learning and FX fluctuations.
First, approximately 90% of the global PV deployment in the
2006–2016 period for which we have detailed project data happened
in six currency areas (Fig. 1a,b) and especially in EUR, CNY and
Japanese yen (JPY). Only 11% of the large (≥5 MW) PV capacity was
installed in the United States [42] . Second, by far the most PV modules
were manufactured in China or Taiwan [9] . This implies that, although
modules are exported to other markets, often with a national currency or USD ‘price sticker’, changes of exchange rates to CNY and
the New Taiwan dollar can affect the cost trajectory of PV systems
in markets around the world (and, according to whether module
manufacturers depend on imports of the components or machinery,
a second-order FX effect may apply). Third, as the support schemes,



such as feed-in tariffs or auctions, in all the countries that saw largescale PV expansion were denominated in their domestic currencies,
almost all large PV projects have their revenues in a currency other
than USD, most commonly CNY, JPY and EUR. If the components
were imported, FX fluctuations affected the revenue of the PV projects, as the by far largest share of the global PV fleet was remunerated in domestic currencies. The three latter observations imply that
almost the entire global PV fleet was exposed to exchange rate fluctuations concerning at least one non-USD currency, even if—which
may be the case, especially with the rise of Asian module manufacturers post-2010—parts of the globally traded components are
invoiced in USD.
In the past decade, exchange rates among the six currencies relevant to PV fluctuated considerably (Fig. 1c). For example, the USD
appreciated strongly against the EUR, from 0.67 in 2008 to 0.95 in
2016, as did the CNY, by about 50% from late 2009 to early 2015 [43][–][45] .
Note that the CNY is not free-floating, unlike most industrialized
countries’ currencies, but is managed as a part of the Chinese government’s general economic policy, and many argue that it is kept
artificially low to increase the international competitiveness of
Chinese export-oriented firms [46] . As PV modules (and important
balance-of-system (BOS) components, such as inverters) are traded
internationally, every estimate of a PV learning rate in all markets,
national and global, includes such technology-unrelated FX developments. The USD, as a general trend, has appreciated against other
currencies (except CNY) since the financial crisis of 2008, and



**72** **Nature EnergY** [| VOL 5 | January 2020 | 71–78 | www.nature.com/natureenergy](http://www.nature.com/natureenergy)


#### Nature Energy Articles



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lilliestamUnderstandingAccountingEffect2020/lilliestamUnderstandingAccountingEffect2020.pdf-2-0.png)







therefore a global learning rate based on the USD will be higher
than the actual technological progress.
The mechanism behind this is simple: if, for instance, the CNY
depreciates compared to the USD, projects realized in the CNY area
become cheaper in USD terms. Global learning rates using USD as
the base currency would then overestimate technological learning,
as part of the observed ‘improvement’ is caused by the changing
exchange rate. Here we develop a method to correct the calculated
learning rate for the (unwanted) impact of exchange rate fluctuations
and make the aggregation across currency areas FX independent.


**A global PV learning rate and its exchange rate**
**dependence**
In this section, we estimate a global learning rate for large-scale PV
(≥5 MW) with each of the six large currencies mentioned in Fig. 1
as base currency. Our analysis is based on all projects for which the
investment costs are available in the Bloomberg New Energy Finance
(BNEF) database with commissioning date 2006–2016 (Table 1).
This is the most comprehensive global renewable power project
database available (Methods gives data description and methodological details). We calculated the learning rates for the investment
costs of projects, not just for modules, as this is the cost that needs
to be remunerated by policy support or consumers. Furthermore,
ESMs and IAMs use the learning rate for the full project cost, which
makes this the most relevant scope.
We estimated the learning rates with a global one-factor learning curve model based on average costs per year and cumulative
deployment per year [12][,][47] Note that the majority—for large PVs often
two-thirds to three-quarters of the hard costs [48] —of the components
for large PV is globally traded goods, namely, the modules and
important parts of the balance-of-system components (inverters
and switchgear). We formulate the global learning curve as:



market shares (Supplementary Note 1). The cost changes are
described by the learning rate:


LR ¼ 1 � 2 [�][a] [l] ð2Þ


Depending on the base currency, the observed learning rate varies between 27 and 33% for the whole period (Fig. 2; Supplementary
Note 2 gives the complete numerical results). As exchange rates often
move in different directions, the FX effect may be larger in shorter
time intervals. For example, the 2011–2016 learning rate based on
CNY is 37%, whereas it is 28% in the EUR-based estimate and only
21% in the JPY-based estimate, which corresponds to a maximum
difference of 16 percentage points compared to a maximum difference of 6 percentage points for the longer 2006–2016 timespan.
Hence, for longer time intervals, the differences between learning
rates for different base currencies are smaller if the exchange rate
fluctuations level out over time (for example, EUR–USD). However,
systematic depreciations (for example, INR–USD) or appreciations
(for example, CNY–USD) can persist over long periods (Fig. 1a),
which biases the estimated learning rates in a systemic way.
For each global learning-rate estimate, the analyst must choose
a base currency, and several different choices are defendable. For
example, the choice may be based on the largest installed capacity
(EUR area until 2012, China since 2013) or it may be based on USD
as the default currency for global markets. The lack of a strongly
compelling reason to choose one base currency over another is problematic in two respects. First, if the learning-rate result depends on
a choice for which several decisions are defendable, the estimate is
not robust; however, given the importance of learning rates in IAMs
and ESMs, robust estimates are critical—and a learning-rate difference of up to 16 percentage points (Fig. 2) is probably a dominant
uncertainty in model runs. Second, the concept of learning refers
to cost reductions through technological advances, but learning
rates calculated using the method of simply converting costs into a
base currency and aggregating them into a global average evidently
hold a large non-technological component: exchange rate development. We thus need an estimation method that is independent of
exchange rate fluctuations, that is, a method that corrects for the
confounding factor FX dynamics.



t
I



**Exchange rate and market focus fluctuation corrections**
To make the global learning rate independent of shifting the geographical focus of deployment and of the FX effect to better reflect
technological learning, we modified equation (1) and gave the market-share-weighted (based on nameplate capacity) average global
costs P [e] t [l] in a base currency (Methods gives details). The new P [e] t [l] in

equation ( I 4) differs from the approach used for P t [l] in equation (1 I ) as
I



t
I



in a base currency (Methods gives details). The new P [e] t [l]



t
I



equation ( I 4) differs from the approach used for P t in equation (1 I ) as

we apply a correction factor for the exchange rate changes between I
the years 0 and _t_ (details in Methods). Hence, the result is unaffected
by the FX changes relative to the base currency. The correction factor _α_ [l] is defined as:



is defined as:



0 [C] t [i]

[l] t _[=]_ [i] [C] t [i]



ð3Þ



i _[δ]_ [i] t




[i] t [w] 0 [l] _[=]_ [i]

[i] t [w] t [l] _[=]_ [i]



P



i t 0 t

~~P~~ i _[δ]_ [i] t [w] t [l] _[=]_ [i] [C] t [i]



_α_ [l] t [¼]



P t [l] [¼][ P] 0 [l]



�a l

_χ_ t

� _χ_ 0 �



ð1Þ



t



with P t [l] [and][ P] 0 [l]



with P t [and][ P] 0 as the market-share-weighted (based on yearly

capacity additions per currency area) average global cost (per MW) I
converted into base currency _l_ in years _t_ and 0, respectively, _χ_ _t_ and
_χ_ 0 as the global cumulative deployment in years _t_ and 0, respectively,
and _a_ as the learning-by-doing elasticity (which depends on the
choice of base currency _l_ ) [12] . We weight by the capacity-based market
share, and not by the project count as most other studies do, to better reflect the actual geographical distribution of capacity deployment—the independent variable of learning-rate assessments—and
use data from IRENA [42] for the global expansion and the national



with _δ_ [i] t
I



with _δ_ t as the share of deployment (capacity share of global total

per currency area) with the cost reported in currency I _i_ in year _t_,
w [l] 0 _[=]_ [i] [and][ w] [l] t _[=]_ [i] as the exchange rates between currency _i_ and the base

currency I _l_ in price notation in years _t_ and 0, respectively, and C t [i] as

_i_ _t_ . The FX-corrected I



t 0

I




[l] 0 _[=]_ [i] [and][ w] [l] t _[=]_ [i]

I _l_



currency t as

the observed average cost in currency _i_ in year _t_ . The FX-corrected I
one-factor learning curve model is then:



Pe tl [¼] _[ α]_ [l] t [P] t [l] [¼][ P] 0 [l]



�b

_χ_ t

 _χ_ o 



ð4Þ



**Nature EnergY** [| VOL 5 | January 2020 | 71–78 | www.nature.com/natureenergy](http://www.nature.com/natureenergy) **73**


#### Articles Nature Energy



**c**



**a**



**b**



1.6

1.4

1.2


1.0
0.9
0.8

0.7

0.6


0.5


0.4


0.3


0.2



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lilliestamUnderstandingAccountingEffect2020/lilliestamUnderstandingAccountingEffect2020.pdf-3-0.png)







![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lilliestamUnderstandingAccountingEffect2020/lilliestamUnderstandingAccountingEffect2020.pdf-3-1.png)











![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lilliestamUnderstandingAccountingEffect2020/lilliestamUnderstandingAccountingEffect2020.pdf-3-2.png)











6 8 10 20 40 60 80 100 200 6 8 10 20 40 60 80 100 200


Cumulative capacity (GW)


CNY EUR GBP INR JPY USD


**Fig. 2 |** **Global learning curves for large PV in different currencies.** **a** – **c**, PV installations ≥ 5 MW. Results for the whole interval 2006–2016 ( **a** ) and two
subintervals 2006–2011 ( **b** ) and 2011–2016 ( **c** ). The lines show the uncorrected, market-share-weighted learning curves in different base currencies.
The underlying yearly average costs depicted by the coloured points are an index of the 2006 market-share-weighted average costs for each currency
(real cost, 2017 basis). Numerical results, _R_ [2] values and confidence intervals are given in Supplementary Note 2.



with P t [l]
I



and P [l]



0
I



with P t and P 0 as the market-share-weighted average global cost

converted into the base currency I I _l_ in years _t_ and 0, respectively, _χ_ _t_
and _χ_ 0 as the global cumulative deployment in years _t_ and 0, respectively, and _b_ as the currency-corrected learning-by-doing elasticity.
The FX-corrected learning rate is then:



LR ¼ 1 � 2 [�][b] ð5Þ


Applying this correction factor has profound effects, especially by
reducing the difference between estimates in different base currencies due to the elimination of the FX bias factor. The FX-corrected
global learning rate for large-scale PV across the whole period
2006–2016 is 29% with USD as the base currency (Fig. 3). The corrected learning-rate estimates in all base currencies (except the
high-inflation INR (Discussion)) for the entire time interval are
28–31% (Supplementary Note 2 and especially Supplementary Fig.
5d–f), compared to 17–33% in the uncorrected estimates (Fig. 2).
The shorter time spans show a similar picture: for 2006–2011, all
the base currencies (except INR) are 20–24% (uncorrected 17–27%)
and for 2011–2016, they are 34–36% (uncorrected 21–37%).
The FX-corrected estimate is unaffected by FX fluctuations and
shifting deployment, and hence measures the technological learning
more closely. However, the result is still not entirely independent of
the base currency choice, as inflation is different in different currency
regions: the JPY-based estimates are lower than the rest due to the
lower (and sometimes negative) inflation in Japan. The much higher
inflation in India is the reason for the INR estimates being very different from the others, which suggests that high-inflation currencies
are unsuited base currencies for learning-rate estimates (Discussion).
This is the only impact of the base currency choice: in nominal values, the FX-corrected learning rates are identical (Discussion and
Supplementary Notes 2 and 3). Eliminating the effects of FX fluctuations also reduces, without removing, the impact of the time interval
selection: this still has an impact—as it should, as learning rates vary
over time [23][,][24] (see above)—but as the result is now FX independent,
the bias from shifting global markets in the calculation is lower.


**Discussion**
In this article, we quantified the effect of exchange rate fluctuations
on global learning rates using large-scale PV as an example and



developed a method to correct for it. This method can be adapted
for use in all types of learning-rate estimation methods, which
include two-factor learning rates. The commonly used approach of
converting project costs into a base currency, deflating the costs and
then calculating a learning rate results in strongly diverging learning rates in the range 17–37%, depending on the chosen base currency and time interval; within one time interval (2011–2016), we
observed differences of up to 16 percentage points. To address this
problem, we defined and used a correction factor to make the global
learning-rate estimate FX independent, which provided a corrected
learning rate for large-scale PV of 28–31% for the period 2006–2016
(except in the high-inflation outlier INR, see below).
As expected, currencies that consistently appreciated (against the
USD, or the whole set of currencies) see lower estimated learning
rates when we use them as the base currency (for example, CNY, all
periods; JPY, 2006–2011), whereas the opposite happens for generally depreciating currencies (for example, EUR and GBP, all periods). Although the identified learning rates are high across all the
base currencies and periods, currency fluctuations and shifts to new
markets have masked or exaggerated the observed cost reduction
due to learning for large-scale PV.
In this article, we discuss the impact of FX fluctuations for global
learning-rate estimates based on project data from different currency areas, and propose a method to correct for it. However, we
acknowledge that the issue we investigate—FX fluctuations—may
have effects beyond the one we assess.
First, FX changes may affect the import prices of components,
which can also bias global learning rates—and even local learning rates within a single currency area, if projects use components
imported from other currency areas. In this article, we do not
account for this ‘component FX effect’, but expect that it would—
for our specific case of PV ≥ 5 MW—be small, as the main country
of module production has been roughly the same as the focus of
deployment (first the Euro zone, and then China): if large parts of
the module manufacturing and deployment happen in the same currency area (albeit different regions over time), there are few component imports. Nevertheless, the FX effect for imported components
will probably gain importance in the future with Asian manufacturers becoming increasingly dominant and more countries deploying
PV, and it could already be more relevant for other technologies than



**74** **Nature EnergY** [| VOL 5 | January 2020 | 71–78 | www.nature.com/natureenergy](http://www.nature.com/natureenergy)


#### Nature Energy Articles



**c**



**a**



**b**



40


30


20


10



![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lilliestamUnderstandingAccountingEffect2020/lilliestamUnderstandingAccountingEffect2020.pdf-4-1.png)





![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lilliestamUnderstandingAccountingEffect2020/lilliestamUnderstandingAccountingEffect2020.pdf-4-2.png)







![](/Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/lilliestamUnderstandingAccountingEffect2020/lilliestamUnderstandingAccountingEffect2020.pdf-4-0.png)

















0


FX-corrected (USD) CNY EUR GBP INR JPY USD


**Fig. 3 |** **FX-corrected, market-share-weighted learning rate.** **a** – **c**, FX-corrected results with USD as base currency (black bar) and uncorrected learning
rates with different base currencies (all other bars) for large (≥5 MW) PV for different time intervals: 2006–2016 ( **a** ), 2006–2011 ( **b** ) and 2011–2016 ( **c** ).
Supplementary Note 2 gives _R_ [2] values, confidence intervals and all the learning-rate estimates in all the base currencies, in real and nominal values.



for PV: further research is needed to explore this issue. Our method
for addressing FX fluctuations in global aggregations—that is, using
the correction factor _α_ [l] t to derive adjusted cost data points P [e] t [l] to be

used in learning curve regressions—can be extended to correct for I I
imported components as well, with the factor _α_ [l] t being applied only

to the cost share that relates to imports in a two-component learning I
curve. It, however, requires detailed data on the import shares from
the different currency areas for each market and year, and of the cost
share of each imported/domestic component—and such data are not
consistently available for global analysis as presented here. This is
why we did not carry out this analysis here.
Second, the FX effect ‘artificially’ makes internationally traded
technology appear cheaper or more expensive without an underlying technological change, which biases the learning-rate estimate.
This effect may also _affect_ learning indirectly: if products are ‘artificially’ more expensive due to persisting appreciation (for example
CNY 2005–2015), companies cannot easily maintain the same price
margin on exported goods [49] – which increases pressure on domestic
companies to innovate and push their products down the respective learning curves. We have not assessed this potential source of
learning here, but—as the Chinese PV industry outcompeted foreign companies based on cost very rapidly—expect it to be small
for our case. In the long term, persistent appreciation or depreciation could also affect domestic costs independently from learning
(for example, monetary expansion relative to other currency areas
causing higher wages). To assess the impact of such economy- or
industry-wide effects on global costs for specific goods, such as PV
modules, is an area for further research.
Finally, we reiterate that FX effects are only one among several
non-technological potential biases in global learning-rate estimations, for example, state support for manufacturing or keeping wage
increases below productivity increase—issues often criticized in the
context of China and PV manufacturing [25] . Hence, correcting for FX
fluctuations will not necessarily lead to entirely unbiased learningrate estimates, but it is an important step to reduce the bias and
measure the effect of technological advances more closely.



to derive adjusted cost data points P [e] t [l]
I



t
I



t
I



**Implications for analysts and decision makers.** Our analysis of
solar PV has a number of implications that can be generalized to all
renewables—and other new high-tech components or technologies
that are deployed across currency areas.



First, our findings cast doubt on previous learning-rate estimates. Often, authors may not even be aware of the FX effect, as
many datasets state that costs already converted into USD. Although
the FX effect can be strong (in our case, up to 16 percentage points),
for other technologies, currencies or time periods, it could be small
and undramatic. The FX effect is often not critical for the learningrate studies themselves as their conclusions may be quite general
(for example, ‘the observed trend of decreasing costs is strong’ [10] )
and not dramatically affected by such quantitative variations. Yet,
it is a biasing factor that must be corrected—we provide a method
for doing so here—and acknowledged in future global learning estimates not only for PV and other renewables, but also for any technology for which such quantifications are needed and used. Again,
we note that our method is not independent of the base currency;
our estimates are still affected by inflation, which is different in different currencies, which suggests that it is important to base studies on hard currencies with low inflation, such as EUR or USD. If
analysts do this, the impact of inflation is minor, although not zero
(Supplementary Notes 2 and 3).
We believe that correcting for exchange rate fluctuations and
market shifts is particularly important for relatively new (globally
traded) technologies, such as automotive- or grid-scale batteries. As
they do not yet have a long market history [13], their short track record
may be particularly impacted by short-term FX and market-shift
dynamics, masking or overemphasizing technological progress.
This finding comes with a caveat: rapid currency appreciation could
be an important short-term deployment driver, by directly increasing the profitability of investments under a national support scheme
(and vice versa for depreciation). Further, governments can (and
do) use currency depreciation to influence the international competitiveness of their domestic industry. Although it is unlikely that
governments steer their exchange rate explicitly to push renewable
energy technology exports, given the small size of this industry, an
‘artificially low’ exchange rate will nevertheless have this effect. In
such cases, it is not only the technological progress measured by the
(corrected) learning rate that stimulates expansion, but also the FX
movement, which is effectively (from the perspective of the analyst)
an unpredictable ad hoc factor. For the learning-rate metric itself,
however, the FX effect remains an unwanted bias.
Second, and of great importance to energy-system and policy
research, the dependency of learning rates on technology-external



**Nature EnergY** [| VOL 5 | January 2020 | 71–78 | www.nature.com/natureenergy](http://www.nature.com/natureenergy) **75**


#### Articles Nature Energy



factors, such as FX variations, cast serious doubt on the robustness
of models and forecasts using empirically estimated but uncorrected
learning rates as central input parameters. As small variations in
IAM and ESM input data can lead to great differences in the results,
inaccuracies such as those we identify for large-scale PV can tip the
energy mix of a scenario in one direction or another and give potentially misleading advice. The assumption of a fixed learning rate
decades into the future is already highly debateable, but unaffected
by our work; however, our method makes the identified historical
learning-rate figures more robust and closer connected to the technological learning they are supposed to measure by eliminating the
impact of one technology-external factor, as indicated by the much
smaller range of corrected estimates compared to the uncorrected.
Finally, the FX effect is important for decision makers who rely on
scientific input or advice regarding past and expected cost trends—
for example, policymakers deciding whether to introduce a new or
continue an existing renewable power support scheme. However,
the cost trajectory in their domestic currency is important: this is
the cost they will need to justify to electricity consumers, taxpayers
and, ultimately, voters. Also, exchange rate fluctuations are a factor
that is typically unrelated to the technology in question and unrelated to the success or failure of policy action: a support scheme may
have led to considerably higher or lower cost reductions than those
revealed by analyses, with the true rate hidden behind exchange rate
fluctuations. Basing new policies and reforms on FX-independent
metrics of technology cost trajectories is a further step towards
more evidence-based policy action, and the method we developed
and applied here can be used to achieve this aim.


**Methods**
**Approach.** If experience with a new technology accumulates not only within a
region but also worldwide (for example, through deployment in several markets),
cost dynamics are best described through global learning curves. PV is an
example of such a global commodity [48] . Estimating global learning curves from
global cost data thereby requires the aggregation of information expressed in
different currencies. Here we describe the approach of converting all the data
into a base currency in two steps. The first step results in the FX-dependent
learning rates frequently used in the literature. We used market-share-weighted
(capacity-based) average yearly costs instead of the more common ‘unweighted’,
that is, based on the number of projects, aggregation used in the literature to
better reflect actual market developments for non-exhaustive data sources (such
as the BNEF project data that we and others use). The second step results in the
FX-independent market-share-weighted learning rates. We performed these
steps for both nominal and real costs. In the final step, the learning rate is FX
independent, but still not independent of the base currency choice, as we used
real costs and inflation differs between currency regions. The nominal corrected
learning rates are identical in all the base currencies (Supplementary Note 2). We
analysed the entire 2006–2016 period and two arbitrarily chosen subintervals
(2006–2011 and 2011–2016).
For the following calculations, let:
δ [i] t [ denotes the share of capacity deployment (%) with cost reported in currency ]
_i_ in year _t_ (that is, the amount of deployment per currency area relative to all global
deployment),

x [i] t [ denotes the cumulative capacity deployed (MW) with cost reported in ]

currency _i_ in year _t_,

_χ_ t denotes the global cumulative capacity deployed (MW) in year _t_,
C t [i] [ denotes the average cost (per MW) in currency ] _[i]_ [ in year ] _[t]_ [ (that is, only ]
projects that were financed in currency _i_ ),



of projects listed in the BNEF dataset—which strongly over-represents Chinese
projects—to better reflect the actual deployment (Supplementary Note 1 gives a
description of the project data focus). We used IRENA data [42] for the actual market
shares. First, we aggregated the single project costs to yearly average costs C t [i] [. ]



t [. ]
The nominal market-share-weighted average global cost P t [l] [ was then calculated as:]



t [ was then calculated as:]



t and
~~P~~ j [x] t [j]



X i



P [¼] t



X



i [δ] t [i] [w] t [l] _[=]_ [iC] t [i] _;_ with δ [i] t [¼] x t [i]



The global one-factor learning curve is then described as:



i [δ] t [i] [] [1] ð6Þ


ð7Þ



_χ_ t �a l

� _χ_ 0 �



P t [l] [¼][ P] [l] 0



_χ_ t



_χ_ 0



If P t [l] [ is deflated (using the deflator of the base currency), we obtain real ]
learning rates; otherwise, we obtain (somewhat unconventional) nominal learning
rates. Using equation (6), this can be rewritten as:



δ [i] 0 [w] 0 [l] _[=]_ [i] [C] 0 [i]



_χ_ t �a l

 _χ_ 0 



X

i



δ [i] t [w] t [l] _[=]_ [i] [C] t [i] [¼] X

i



ð8Þ



Following equation (2), we described the market-share-weighted, uncorrected
global learning rate LR [l] a [ (Fig. ][3][) as]



LR [l] a [¼][ 1][ �] [2] [�][a] [l] ð9Þ



**Correction for exchange rate fluctuations.** For applications such as long-term
ESMs or IAMs, the learning curve should describe technological improvements
only, with exchange rate fluctuations (that is, changes from w 0 [l] _[=]_ [i] [ to ] [w] t [l] _[=]_ [i] [) filtered ]
out. The corrected learning rate is defined as LR = 1 – 2 [–] _[b]_ (equation (5)), which
is estimated from a learning curve (equation (4)) in which P [e] [l] t [ are global average ]

prices converted into currency _l_ in year _t_ ; we corrected for exchange rate
changes between year 0 and year _t_ by using _w_ 0 instead of _w_ _t_ and hence adjust
equation (6) to:



Pe lt [¼] X

i



δ [i] t [w] 0 [l] _[=]_ [i] [C] t [i] ð10Þ



If P [e] [l] t [ is deflated (using the deflator of the base currency), we obtain real ]
learning rates; otherwise, we obtain nominal learning rates.
We defined the adjustment factor _α_ [l] t [ such that:]



_α_ [l]




[l] t [P] t [l]



t [l] [¼][ e][P] t [l]



t [l] ð11Þ



and used equations (6) and (10) to arrive at a formulation for the adjustment factor
that uses the average costs in each currency, the market shares of different currency
areas and exchange rates:



P i [δ] [i] t [w] 0 [C] t [i]

~~P~~ i [δ] [i] t [w] t [l] _[=]_ [i] [C] t [i]




[i] t [w] t [l] _[=]_ [i]



t [C] t [i]



t



_α_ [l] t [¼]



Pe lt
P [l] t



P i



i [δ] [i] t [w] 0 [l] _[=]_ [i]



¼



ð12Þ



For each base currency and year, we calculated the adjustment factor _α_ [l] t [, ]
transformed the P t [l] [ into ] [P][e] t [l] [ and estimated the learning rates provided in the section ]

Exchange rate and market focus fluctuation corrections (Fig. 3 and Supplementary
Note 2) using:

LR [l] b [¼][ 1][ �] [2] [�][b] [l] ð13Þ



P [l]



P t [ denotes the market share-weighted average global cost (per MW) converted ]

into base currency e _l_ in year _t_,
P l



Although Fig. 3 shows the corrected global learning rate estimated using the
base currency USD, the choice of the base currency still matters, although much
less than with the conventional method (Supplementary Note 2). The learning
rate is identical in nominal terms in all the base currencies, which underlines the
appropriateness of the adjustment factor _α_ [l] t [ based on the empirical data, whereas ]

the learning rates based on real costs are generally similar (for low-inflation
currencies) but not identical (Supplementary Notes 2 and 3).
In addition, a brief discussion further underlines the appropriateness of _α_ [l] t [.]



P t [ denotes the FX-corrected, market-share-weighted average global cost (per ]

MW) converted into base currency _l_ in year _t_ and



w t [l] _[=]_ [i] [ denotes the exchange rate between currency ] _[i]_ [ and the base currency ] _[l]_ [ in ]
year _t_ in price notation (that is, one needs to pay w [l] t _[=]_ [i] [ units of the base currency ] _[l]_



year _t_ in price notation (that is, one needs to pay w t [ units of the base currency ] _[l]_ [ to ]

receive one unit of currency _i_ ).



t [.]
**Proposition 1.** If w [l] t _[=]_ [i] ¼ w [l] 0 _[=]_ [i] [ for all ] _[i]_ [ (that is, the exchange rates do not change), ]

then _α_ [l] [¼][ 1] [ and ] [P][e] [l] [¼][ P] [l] [. The same holds true if some ] [w] [l] _[=]_ [i] [≠][w] [l] _[=]_ [i] _[t]_




[P] [l]

then _α_ [l] t [¼][ 1] [ and ] t [¼][ P] t [l] [. The same holds true if some ] [w] t [l] _[=]_ [i] [≠][w] [l] 0 _[=]_ [i] [, but only for ] _[t]_ [ and ] _[I]_

where δ [i] t [¼][ 0] [ (that is, the exchange rate changes only for years in which no capacity ]
is added in the respective currency areas).
**Proof.** In equation (12), by replacing w [l] t _[=]_ [i] [ by ] [w] [l] 0 _[=]_ [i] [ for all ] _[t]_ [, and ] _[i]_ [ where ] [w] t [l] _[=]_ [i] ¼ w [l] 0 _[=]_ [i]




[l] t [¼][ 1] [ and ] [P][e] [l]




[l] t [¼][ P] t [l] [. The same holds true if some ] [w] t [l] _[=]_ [i]



**Correction for data coverage.** Here we describe how we estimated the
uncorrected, FX-dependent learning rate (which results in Fig. 2) using the
common-practice method of converting cost statements from local currency to a
base currency, aggregating the costs into yearly averages (we used a market-shareweighted average instead of a project-number-weighted one) and deflating the
costs to real costs for a target year (here, 2017). We weighted the average global
costs by market shares (capacity based) of each currency area, not by number



t 0 t 0

holds, the proposition follows.
**Proposition 2.** For any currency _i_ with δ [i] t [≠][0] [, if ] [w] t [l] _[=]_ [i] _[<]_ [w] [l] 0 _[=]_ [i] [ (that is, the base currency ]




[l] t _[=]_ [i] _[>]_ [w] [l] 0 _[=]_ [i] [.]




[l] t _[=]_ [i] ¼ w [l] 0 _[=]_ [i]




[i] t [≠][0] [, if ] [w] t [l] _[=]_ [i]




[l] t _[=]_ [i] _[<]_ [w] [l] 0 _[=]_ [i]



0 [ (that is, the base currency ]



appreciates versus currency _i_ ) whereas w [l] t _[=]_ [j]



appreciates versus currency _i_ ) whereas w t ¼ w 0 [ for all other currencies ] _[j]_ [, then (1) ]

_α_ [l] _[>]_ [1] [ and (2) ] [P][e] [l] _[>]_ [P] [l] [ (that is, ] [P][e] [l] [ is corrected for the fact that some observed cost ]



_α_ [l] t _[>]_ [1] [ and (2) ] [P][e] t [l] _[>]_ [P] t [l] [ (that is, ] [P][e] t [l] [ is corrected for the fact that some observed cost ]

reductions down to P t [l] [ stem from improved foreign exchange rates only). ]
The inverse is the case for w [l] _[=]_ [i] _[>]_ [w] [l] _[=]_ [i] [.]




[l] t _[>]_ [1] [ and (2) ] [P][e] [l]




[l] t _[=]_ [j] ¼ w [l] 0 _[=]_ [j]



**76** **Nature EnergY** [| VOL 5 | January 2020 | 71–78 | www.nature.com/natureenergy](http://www.nature.com/natureenergy)


#### Nature Energy Articles




[j] t [w] [l] t _[=]_ [j]



**Proof.** Let D t ¼ [P] i [δ] t [j]




[l] t _[=]_ [j] [C] t [j]



**Proof.** Let D t ¼ [P] i [δ] t [w] t [C] t [ where ] _[j]_ [ denotes all currencies other than ] _[I]_ [, and let ]

E t ¼ δ [i] t [C] t [i] [. Then, equation (][12][) can be rewritten as:]



0

_α_ [l] t [¼] [D] [0] [ þ][ E] [t] [w] [l] _[=]_ [i]



_;_




[t] 0

D t þ E t w [l] t _[=]_ [i]



¼ [D] [0] [ þ][ E] [t] [w] 0 [l] _[=]_ [i]




[t] 0

D 0 þ E t w [l] t _[=]_ [i]



t



t



and statement (1) follows. Statement (2) follows from equation (11).


**Regression analysis.** We used the ordinary least squares method to fit a log–log
linear regression function to the (nominal and real) yearly average costs to estimate
all the learning rates. We rearranged the single-factor learning curve model in
equation (1) to infer the log–log regression model as defined in Lindman and
Söderholm [12], where:




_[χ]_ [t] j _β_ 0 ¼ ln� p [l] 0 �

� _χ_ 0 �



ln P� [l] t � ¼ _β_ 0 þ _β_ 1 ln _[χ]_ [t]



5. Grübler, A., Nakicenovic, N. & Victor, D. Dynamics of energy technologies
and global change. _Energy Policy_ **27**, 247–280 (1999).
6. Ellenbeck, S. & Lilliestam, J. How modelers construct energy costs: discursive
elements in energy system and integrated assessment models. _Energy Res. Soc._
_Sci._ **47**, 69–77 (2019).
7. _Special Report on Global Warming of 1.5_ _°C (SR15)_ (Intergovernmental Panel
on Climate Change, 2018).
8. _Special Report on Renewable Energy Sources and Climate Change Mitigation_
(Cambridge Univ. Press, 2011).
9. Fraunhofer ISE. _Photovoltaics Report, Update 10 August 2018_ (Fraunhofer
ISE, 2018).
10. Lilliestam, J., Labordena, M., Patt, A. & Pfenninger, S. Empirically observed
learning rates for concentrating solar power and their responses to regime
change. _Nat. Energy_ **2**, 17094 (2017).
11. Hayashi, D., Huenteler, J. & Lewis, J. Gone with the wind: a learning curve
analysis of China’s wind power industry. _Energy Policy_ **120**, 38–51 (2018).
12. Lindman, Å. & Söderholm, P. Wind power learning rates: a conceptual review
and meta-analysis. _Energy Econ._ **34**, 754–761 (2012).
13. Schmidt, O., Hawkes, A., Gambhir, A. & Staffell, I. The future cost of electrical
energy storage based on experience rates. _Nat. Energy_ **2**, 17110 (2017).
14. Weiss, M., Zerfass, A. & Helmers, E. Fully electric and plug-in hybrid
cars—an analysis of learning rates, user costs, and costs for mitigating CO 2
and air pollutant emissions. _J. Clean. Prod._ **212**, 1478–1489 (2019).
15. Söderholm, P. & Sundqvist, T. Empirical challenges in the use of learning
curves for assessing the economic prospects of renewable energy
technologies. _Renew. Energy_ **32**, 2559–2578 (2007).
16. Klaasen, G., Miketa, A., Larsen, K. & Sundqvist, T. The impact of R&D on
innovation for wind energy in Denmark, Germany and the United Kingdom.
_Ecol. Econ._ **54**, 227–240 (2005).
17. Jamasb, T. Technical change theory and learning curves: patterns of progress
in electricity generation technologies. _Energy J._ **28**, 51–71 (2007).
18. Kittner, N., Lill, F. & Kammen, D. Energy storage deployment and innovation
for the clean energy transition. _Nat. Energy_ **2**, 17125 (2017).
19. Huenteler, J., Niebuhr, C. & Schmidt, T. The effect of local and global
learning on the cost of renewable energy in developing countries. _J. Clean._
_Prod._ **128**, 6–21 (2016).
20. Steffen, B., Matsuo, T., Steinemann, D. & Schmidt, T. Opening new markets
for clean energy: the role of project developers in the global diffusion of
renewable energy technologies. _Bus. Polit._ **20**, 553–587 (2018).
21. Egli, F., Steffen, B. & Schmidt, T. A dynamic analysis of financing conditions
for renewable energy technologies. _Nat. Energy_ **3**, 1084–1092 (2018).
22. Steffen, B., Hirschier, D. & Schmidt, T. Historical and projected improvements
in net energy performance of power generation technologies. _Energy Environ._
_Sci._ **11**, 3254–3530 (2018).
23. van Buskirk, R., Kantner, C., Gerke, B. & Chu, S. A retrospective investigation
of energy efficiency standards: policies may have accelerated long term
declines in appliance costs. _Environ. Res. Lett._ **9**, 114010 (2014).
24. Wei, M., Smith, S. & Sohn, M. Non-constant learning rates in retrospective
experience curve analyses and their correlation to deployment programs.
_Energy Policy_ **107**, 356–369 (2017).
25. Gang, C. China’s solar PV manufacturing and subsidies from the perspective
of state capitalism. _Copen. J. Asian Stud._ **33**, 90–106 (2015).
26. Creutzig, F. et al. The underestimated potential of solar energy to mitigate
climate change. _Nat. Energy_ **2**, 17140 (2017).
27. van Sark, W., Alsema, E., Junginger, M., de Moor, H. & Schaeffer, G. J.
Accuracy of progress ratios determined from experience curves: the case of
crystalline silicon photovoltaic module technology development. _Prog._
_Photovoltaics_ **16**, 441–453 (2008).
28. Nordhaus, W. The perils of the learning model for modeling endogenous
technological change. _Energy J._ **35**, 1–13 (2014).
29. Pindyck, R. The use and misuse of models for climate policy. _Rev. Environ._
_Econ. Policy_ **11**, 100–114 (2017).
30. van Sark, W. Introducing errors in progress ratios determined from
experience curves. _Technol. Forecast. Soc. Change_ **75**, 405–415 (2008).
31. Gan, P. Y. & Li, Z. Quantitative study on long term global solar photovoltaic
market. _Renew. Sustain. Energy Rev._ **46**, 88–99 (2015).
32. Yu, C., van Sark, W. & Alsema, E. Unraveling the photovoltaic technology
learning curve by incorporation of input price changes and scale effects.
_Renew. Sustain. Energy Rev._ **15**, 324–337 (2011).
33. Nemet, G. Interrim monitoring of cost dynamics for publicly supported
energy technologies. _Energy Policy_ **37**, 825–835 (2009).
34. _Renewable Power Generation Costs in 2017_ (International Renewable Energy
Agency, 2018).
35. Mauleón, I. & Hamoudi, H. Photovoltaic and wind cost decrease estimation:
implications for investment analysis. _Energy_ **137**, 1054–1065 (2017).
36. Junginger, M., Faaij, A. & Turkenburg, W. Global experience curves for wind
farms. _Energy Policy_ **33**, 133–150 (2005).
37. Fraunhofer ISE. _Current and Future Cost of Photovoltaics_ (Agora
Energiewende, 2015).



_χ_ 0



; _β_ 1 ¼ �a [l] ð14Þ



with _β_ 0 and _β_ 1 as linear regression estimates. The same applies for P [e] [l] t [. We then ]

derived the learning rates from _β_ 1 using:


LR ¼ 1 � 2 _β_ 1 ð15Þ


**Data sources.** We relied on three different data types to estimate the learning rates:
(1) PV project data from the BNEF renewable energy database [50], which describes
project characteristics and individual project costs; (2) capacity data from the
IRENA renewable energy database, which describes the total global PV (≥5 MW)
expansion and market shares per country [42] and (3) historical exchange rates from
OFX [43] and OECD.Stat [44] to convert currencies to the various base currencies, as well
as the consumer price index data from OECD.Stat [51] to convert nominal into real
values. For each year, we used only one exchange rate and price index, namely the
yearly average.
We used the BNEF database for project cost input, as it gives investment costs
for individual installations and projects in the original currencies and is the most
comprehensive project database available. Our study is based on a subset of 1,990
PV projects that were commissioned between 2006 and 2016 and are reported in
one of the six currencies in which most PV expansion, by far, happened (Fig. 1).
We only considered projects with capacities ≥5 MW because these projects use a
high share of globally traded components [9][,][48] . Table 1 summarizes how we filtered
the database (that is, how many projects and what capacity remains for each filter
criterion). The outliers are projects that we deemed have implausible costs that
deviate very strongly (by a factor of ten or more) from the average trend. Very
probably, these outliers were entered incorrectly in the database.
For the global PV expansion and the national market shares, we used IRENA
data [42], as it is more complete and describes the actual market development more
accurately than BNEF, which overemphasizes the Chinese market (Supplementary
Note 1). As IRENA reports market development per country and our learning
rate calculation is based on currencies rather than countries, we assumed
that all the projects were paid and remunerated in the main currencies of the
corresponding countries. This is very likely to be true in most countries, and the
support schemes in the six currency areas we investigated here were denominated
in the domestic currency.


**Data availability**
The data that support the findings of this study are available from BNEF, but
restrictions apply to the availability of these data, which were used under license
for the current study and so are not publicly available. Data are, however, available
from the authors upon reasonable request and with permission of BNEF.


**Code availability**
[The source code (in R) and supporting documents are available at Zenodo (https://](https://doi.org/10.5281/zenodo.3553796)
[doi.org/10.5281/zenodo.3553796) and can be freely used and manipulated by all](https://doi.org/10.5281/zenodo.3553796)
users, without restriction, under the MIT licence.


Received: 29 May 2019; Accepted: 2 December 2019;
Published online: 13 January 2020


**References**
1. Rubin, E., Azevedo, I., Jaramillo, P. & Yeh, S. A review of learning rates for
electricity supply technologies. _Energy Policy_ **86**, 198–218 (2015).
2. Samadi, S. The experience curve theory and its application in the field of
electricity generation technologies—a literature review. _Renew. Sustain. Energy_
_Rev._ **82**, 2346–2364 (2018).
3. Wright, T. P. Factors affecting the cost of airplanes. _J. Aeronautical Sci._ **3**,
122–128 (1936).
4. Gallagher, K., Grübler, A., Kuhl, L., Nemet, G. & Wilson, C. The energy
technology innovation system. _Annu. Rev. Environ. Resour._ **37**,
137–162 (2012).



**Nature EnergY** [| VOL 5 | January 2020 | 71–78 | www.nature.com/natureenergy](http://www.nature.com/natureenergy) **77**


#### Articles Nature Energy



38. Zou, H. et al. China’s future energy mix and emissions reduction potential: a
scenario analysis incorporating technological learning curves. _J. Clean. Prod._
**112**, 1475–1485 (2016).
39. Lin, B. & He, J. Learning curves for harnessing biomass power—what could
explain the reduction of its cost during the expansion in China? _Renew._
_Energy_ **99**, 280–288 (2016).
40. Hong, S., Chung, Y. & Woo, C. Scenario analysis for estimating the learning
rate of photovoltaic power generation based on learning curve theory in
South Korea. _Energy_ **79** (2015).
41. Drud Hansen, J., Jensen, C. & Strøjer Madsen, E. The establishment of the
Danish windmill industry—was it worthwhile? _Rev. World Econ._ **139**,
324–347 (2003).
42. _Renewable Capacity Statistics 2018_ (International Renewable Energy
Agency, 2018).
43. _Monthly Average Rates_ [(OFX, 2019); https://www.ofx.com/en-au/forex-news/](https://www.ofx.com/en-au/forex-news/historical-exchange-rates/monthly-average-rates/)
[historical-exchange-rates/monthly-average-rates/](https://www.ofx.com/en-au/forex-news/historical-exchange-rates/monthly-average-rates/)
44. _Monthly Monetary and Financial Statistics: Exchange Rates (USD Monthly_
_Averages)_ [(OECD.Stat, 2018); https://stats.oecd.org/index.aspx?queryid=169#](https://stats.oecd.org/index.aspx?queryid=169#)
45. _Currencies_ [(Reuters, 2019); https://uk.reuters.com/business/currencies](https://uk.reuters.com/business/currencies)
46. Schnabl, G. China’s overinvestment and international trade conflicts.
_China World Econ._ **27**, 37–62 (2019).
47. Junginger, M., van Sark, W. & Faaij, A. _Technological learning in the energy_
_sector: lessons for policy, industry and science_ (Edward Elgar, 2010).
48. Fu, R., Feldman, D., Margolis, R., Woodhouse, M. & Ardani, K. _US Solar_
_Photovoltaic System Cost Benchmark: Q1 2017_ (National Renewable Energy
Laboratory, 2017).
49. Marston, R. Pricing to market in Japanese manufacturing. _J. Int. Econ._ **29**,
217–236 (1990).
50. _Bloomberg New Energy Finance Database: Renewable Energy Projects_
(Bloomberg New Energy Finance, accessed 1 September 2017).



51. _Monthly Monetary and Financial Statistics: Relative Consumer Price Indices_
[(OECD.Stat, 2019); https://stats.oecd.org/index.aspx?queryid=168](https://stats.oecd.org/index.aspx?queryid=168)


**Acknowledgements**
J.L., L.O. and M.M. received funding from the European Research Council (ERC)
under the European Union’s Horizon 2020 research and innovation programme (grant
agreement no. 715132). We thank F. Egli and M. Meyer for helpful comments on earlier
drafts of the paper.


**Author contributions**

J.L. designed the study with the support of all the authors and drafted the article,
M.M. carried out the quantitative analyses and produced the figures, B.S. designed the
correction factor and all the authors contributed to the analysis and the final article.


**Competing interests**
The authors declare no competing interests.


**Additional information**

**Supplementary information** [is available for this paper at https://doi.org/10.1038/](https://doi.org/10.1038/s41560-019-0531-y)
[s41560-019-0531-y.](https://doi.org/10.1038/s41560-019-0531-y)


**Correspondence and requests for materials** should be addressed to J.L.


**Reprints and permissions information** [is available at www.nature.com/reprints.](http://www.nature.com/reprints)


**Publisher’s note** Springer Nature remains neutral with regard to jurisdictional claims in
published maps and institutional affiliations.


© The Author(s), under exclusive licence to Springer Nature Limited 2020



**78** **Nature EnergY** [| VOL 5 | January 2020 | 71–78 | www.nature.com/natureenergy](http://www.nature.com/natureenergy)


