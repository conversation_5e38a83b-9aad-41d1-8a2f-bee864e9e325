# 基于文献的可再生能源潜力评估方法

## 1. 方法概述

基于提供的文献，本方法采用GIS数据排除不适宜区域，结合标准风机功率曲线和太阳能光伏物理模型，计算小时级容量因子和可开发潜力。

## 2. GIS数据和约束条件

### 2.1 太阳能适宜性筛选

#### 排除区域
- **土地利用**: 水体、湿地、永久积雪和冰、森林、农田
- **地形**: 坡度 > 5%
- **保护区**: 基于世界保护区数据库(WDPA)的保护区

#### 数据源
- **土地覆盖**: 欧空局气候变化倡议(ESA CCI)土地覆盖数据
- **地形**: 全球多分辨率地形高程数据(GMTED)
- **保护区**: 世界保护区数据库(WDPA)

### 2.2 风电适宜性筛选

#### 排除区域
- **土地利用**: 城市用地、水体、永久积雪和冰、森林
- **地形**: 坡度 > 20%、海拔 > 3000m
- **保护区**: 基于WDPA的保护区

#### 数据源
- 与太阳能相同的GIS数据集

### 2.3 海上风电适宜性筛选

#### 筛选标准 (基于Wang et al.)
- **水深约束**: ≥ 1m (避免过浅区域和陆地)
- **领海区域约束**: 仅在各国专属经济区(EEZ)内部署
- **环境保护约束**: 完全排除海洋生态保护区

#### 数据源
- **领海区域数据**: 海洋边界地理数据库 (Maritime Boundaries Geodatabase)
- **水深数据**: 雷达地形测绘任务全球增强坡度数据库 (Radar Topography Mission Global Enhanced Slope Database)
- **海洋生态保护区**: 国家海洋数据和信息服务 (National Marine Data and Information Service)
## 3. 风电容量因子计算

### 3.1 标准风机规格

#### 陆上风电
- **风机型号**: GE 2.5 MW
- **功率曲线**: 标准功率曲线数据

#### 海上风电  
- **风机型号**: Vestas 8.0 MW
- **功率曲线**: 标准功率曲线数据

### 3.2 计算公式

#### 风电容量因子
```
CFw = fw(Vhub)                                    (1)
```
其中 fw 表示标准风机的功率曲线

#### 轮毂高度风速
```
Vhub = V10 × (hub/10)^α                          (2)
```
其中:
- V10: 10米高度风速
- hub: 轮毂高度
- α: 表面摩擦系数，通常取1/7

#### 10米高度风速
```
V10 = √(uas² + vas²)                             (3)
```
其中:
- uas: 10米高度北向风速分量
- vas: 10米高度东向风速分量

### 3.3 数据需求
- **CMIP6风速数据**: 10米高度的uas和vas分量
- **时间分辨率**: 小时级
- **空间分辨率**: 1°×1°

## 4. 太阳能容量因子计算

### 4.1 基本参数设置

#### 光伏系统参数
- **光电转换效率**: EF = 16.19%
- **系统性能系数**: SYScoef = 80.56%
- **温度系数**: γ = 0.005 °C⁻¹ (单晶硅)
- **额定输出**: PWp = 161.9 W/m² (标准测试条件)
- **跟踪系统**: 双轴跟踪系统

#### 标准测试条件
- **电池温度**: 25°C
- **辐照度**: 1000 W/m²
- **大气质量**: AM 1.5

### 4.2 计算公式

#### 太阳能容量因子
```
CFs = PGHI / PWp                                 (4)
```

#### 实际电力输出
```
PGHI = IΣ × EF × TEMcoef × SYScoef              (5)
```

#### 面板总辐照度
```
IΣ = IBΣ + IDΣ + IRΣ                            (6)
```

#### 直射辐照度分量
```
IBΣ = IBH × cos θ0                              (7)
```

#### 散射辐照度分量
```
IDΣ = IDH × Rd                                  (8)
```

#### 反射辐照度分量
```
IRΣ = IH × ρf × (1-cos Σ)/2                    (9)
```

其中:
- Σ: 光伏组件倾斜角
- IBH, IDH, IH: 水平面直射、散射、总辐照度
- Rd: 散射辐照度转换系数
- ρf: 反射率常数 (草地和地面取0.2)

### 4.3 几何角度计算

#### 直射辐照入射角
```
θ0 = cos⁻¹[cos θz × cos Σ + sin θz × sin Σ × cos(γs - γ)]  (10)
```

#### 太阳天顶角
```
θz = cos⁻¹[sin δ × sin LAT + cos δ × cos LAT × cos ω]       (11)
```

#### 太阳赤纬角
```
δ = -23.45 cos[360(n+10)/365.25]                           (12)
```

其中:
- θz: 太阳天顶角
- δ: 太阳赤纬角
- γs, γ: 太阳方位角和面板方位角
- LAT: 纬度
- ω: 太阳时角
- n: 年内天数

### 4.4 温度修正

#### 温度系数
```
TEMcoef = 1 - γ × (Tcell - TSTC)                          (13)
```

#### 电池工作温度
```
Tcell = c1 + c2 + c3 × I - c4 × V                        (14)
```

其中:
- γ: 温度系数 (0.005 °C⁻¹)
- Tcell: 太阳能板工作温度
- TSTC: 标准测试条件下温度 (25°C)
- I: 地表太阳辐射
- V: 地表风速
- c1 = 4.3°C (经验常数)
- c2 = 0.943 (温度系数)
- c3 = 0.028°C·m²/W (辐射系数)
- c4 = 1.528°C·s/m (冷却系数)

## 5. 数据处理流程

### 5.1 空间聚合
- **原始分辨率**: 1°×1° 网格
- **聚合方法**: 面积加权的国家平均值
- **排除区域**: 使用GIS数据排除不适宜部署区域

### 5.2 偏差修正
- **方法**: 分位数映射(Quantile Mapping)
- **参考数据**: ERA5再分析数据
- **应用对象**: 风电和太阳能容量因子

## 6. 实施方案

### 6.1 数据需求清单

#### 气象数据
- **CMIP6数据**: 
  - 10米高度风速分量 (uas, vas)
  - 地表太阳辐射分量 (直射、散射、总辐射)
  - 地表温度
  - 地表风速
- **ERA5数据**: 用于偏差修正的参考数据

#### GIS数据
- **ESA CCI土地覆盖**: 最新版本土地利用分类
- **GMTED地形数据**: 坡度计算
- **WDPA保护区**: 保护区边界数据

### 6.2 计算流程

#### 第一步: 数据预处理
1. 下载和整理CMIP6气象数据
2. 获取GIS约束数据
3. 统一空间分辨率到1°×1°
4. 质量控制和缺失值处理

#### 第二步: 约束掩码生成
1. 基于土地利用生成排除掩码
2. 基于坡度生成地形掩码
3. 基于保护区生成环境掩码
4. 合并生成最终约束掩码

#### 第三步: 容量因子计算
1. 风电容量因子计算
   - 轮毂高度风速外推
   - 应用标准功率曲线
   - 计算小时级容量因子
2. 太阳能容量因子计算
   - 太阳几何角度计算
   - 面板辐照度分量计算
   - 温度修正
   - 计算小时级容量因子

#### 第四步: 偏差修正
1. 与ERA5数据对比
2. 应用分位数映射方法
3. 修正系统性偏差

#### 第五步: 空间聚合
1. 应用约束掩码
2. 面积加权聚合到国家级
3. 生成最终结果

### 6.3 输出格式

#### 容量因子时间序列
```python
capacity_factors = {
    'solar': {
        'country_code': [8760 hourly values],  # 小时级容量因子
        ...
    },
    'wind_onshore': {
        'country_code': [8760 hourly values],
        ...
    },
    'wind_offshore': {
        'country_code': [8760 hourly values],
        ...
    }
}
```

#### 可开发潜力
```python
technical_potential = {
    'solar': {
        'country_code': {
            'suitable_area_km2': float,      # 适宜面积
            'capacity_density_MW_km2': float, # 装机密度
            'total_potential_MW': float,      # 总潜力
            'average_capacity_factor': float  # 年平均容量因子
        },
        ...
    },
    'wind_onshore': {...},
    'wind_offshore': {...}
}
```

## 7. 技术参数设置

### 7.1 风电技术参数

#### 陆上风电
- **轮毂高度**: 根据GE 2.5MW规格
- **装机密度**: 5-8 MW/km² (考虑风机间距)
- **可用土地比例**: 1-3% (排除约束后)

#### 海上风电
- **轮毂高度**: 根据Vestas 8.0MW规格
- **装机密度**: 8-12 MW/km²
- **水深范围**: 0-200m

### 7.2 太阳能技术参数

#### 光伏系统
- **装机密度**: 40-60 MW/km² (地面电站)
- **可用土地比例**: 0.5-2% (排除约束后)
- **系统寿命**: 25年
- **年衰减率**: 0.5%

## 8. 验证方法

### 8.1 与现有研究对比
- 对比IRENA全球能源地图集
- 对比IEA可再生能源路线图
- 对比学术文献结果

### 8.2 实际项目验证
- 选择已建项目进行容量因子对比
- 验证计算精度和可靠性

### 8.3 敏感性分析
- 关键参数变化影响分析
- 不确定性量化

---

**方法特点**:
- 严格遵循文献方法
- 物理模型驱动
- 小时级时间分辨率
- 全面的GIS约束
- 标准化的技术参数

**下一步**: 基于此方法进行代码实现
