#!/usr/bin/env python3
"""
基于文献方法的可再生能源潜力评估演示
严格按照提供文献中的公式和方法实现
"""

import numpy as np
import pandas as pd
import xarray as xr
from typing import Dict, List, Tuple, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LiteratureBasedRenewableAssessment:
    """基于文献方法的可再生能源评估"""
    
    def __init__(self):
        """初始化评估器"""
        # 技术参数 (严格按照文献)
        self.solar_params = {
            'photoelectric_efficiency': 0.1619,  # EF = 16.19%
            'system_performance_coef': 0.8056,   # SYScoef = 80.56%
            'temperature_coefficient': 0.005,     # γ = 0.005 °C⁻¹
            'rated_output_density': 161.9,       # PWp = 161.9 W/m²
            'standard_test_temp': 25,            # TSTC = 25°C
            'albedo': 0.2,                       # ρf = 0.2
            # 温度模型参数
            'c1': 4.3, 'c2': 0.943, 'c3': 0.028, 'c4': 1.528
        }
        
        self.wind_params = {
            'surface_friction_coef': 1/7,  # α = 1/7
            'onshore_hub_height': 80,      # GE 2.5MW典型轮毂高度
            'offshore_hub_height': 100,    # Vestas 8.0MW典型轮毂高度
        }
        
        # GIS约束条件 (严格按照文献)
        self.gis_constraints = {
            'solar': {
                'max_slope_percent': 5,
                'excluded_land_types': ['water', 'wetlands', 'snow_ice', 'forests', 'croplands']
            },
            'wind': {
                'max_slope_percent': 20,
                'max_elevation_m': 3000,
                'excluded_land_types': ['urban', 'water', 'snow_ice', 'forests']
            }
        }
    
    def calculate_wind_capacity_factor(self, 
                                     uas: np.ndarray, 
                                     vas: np.ndarray,
                                     technology: str = 'onshore') -> np.ndarray:
        """
        计算风电容量因子 (严格按照文献公式1-3)
        
        Args:
            uas: 10米高度北向风速分量 (m/s)
            vas: 10米高度东向风速分量 (m/s)
            technology: 'onshore' 或 'offshore'
            
        Returns:
            容量因子数组
        """
        # 公式3: V10 = √(uas² + vas²)
        v10 = np.sqrt(uas**2 + vas**2)
        
        # 公式2: Vhub = V10 × (hub/10)^α
        if technology == 'onshore':
            hub_height = self.wind_params['onshore_hub_height']
            power_curve = self._get_ge_2_5mw_power_curve()
        else:
            hub_height = self.wind_params['offshore_hub_height']
            power_curve = self._get_vestas_8mw_power_curve()
        
        alpha = self.wind_params['surface_friction_coef']
        vhub = v10 * (hub_height / 10) ** alpha
        
        # 公式1: CFw = fw(Vhub)
        capacity_factors = np.array([self._apply_power_curve(v, power_curve) for v in vhub.flatten()])
        capacity_factors = capacity_factors.reshape(vhub.shape)
        
        return capacity_factors
    
    def calculate_solar_capacity_factor(self,
                                      direct_irradiance: np.ndarray,
                                      diffuse_irradiance: np.ndarray,
                                      total_irradiance: np.ndarray,
                                      air_temperature: np.ndarray,
                                      wind_speed: np.ndarray,
                                      lat: np.ndarray,
                                      lon: np.ndarray,
                                      time_hours: np.ndarray) -> np.ndarray:
        """
        计算太阳能容量因子 (严格按照文献公式4-14)
        
        Args:
            direct_irradiance: 水平面直射辐照度 (W/m²)
            diffuse_irradiance: 水平面散射辐照度 (W/m²)
            total_irradiance: 水平面总辐照度 (W/m²)
            air_temperature: 气温 (°C)
            wind_speed: 风速 (m/s)
            lat: 纬度数组
            lon: 经度数组
            time_hours: 时间数组 (小时)
            
        Returns:
            容量因子数组
        """
        # 计算太阳几何角度
        solar_angles = self._calculate_solar_geometry(lat, lon, time_hours)
        
        # 公式6-9: 计算面板总辐照度 (双轴跟踪)
        panel_irradiance = self._calculate_panel_irradiance_two_axis(
            direct_irradiance, diffuse_irradiance, total_irradiance, solar_angles
        )
        
        # 公式13-14: 计算温度修正系数
        temp_coefficient = self._calculate_temperature_coefficient(
            panel_irradiance, wind_speed, air_temperature
        )
        
        # 公式5: PGHI = IΣ × EF × TEMcoef × SYScoef
        pghi = (panel_irradiance * 
                self.solar_params['photoelectric_efficiency'] * 
                temp_coefficient * 
                self.solar_params['system_performance_coef'])
        
        # 公式4: CFs = PGHI / PWp
        capacity_factors = pghi / self.solar_params['rated_output_density']
        
        # 限制在合理范围内
        capacity_factors = np.clip(capacity_factors, 0, 1)
        
        return capacity_factors
    
    def _get_ge_2_5mw_power_curve(self) -> Dict[float, float]:
        """GE 2.5MW风机功率曲线 (基于文献引用的标准曲线)"""
        return {
            0: 0.0, 1: 0.0, 2: 0.0, 3: 0.0, 3.5: 0.02,
            4: 0.05, 4.5: 0.09, 5: 0.15, 5.5: 0.22, 6: 0.31,
            6.5: 0.41, 7: 0.52, 7.5: 0.63, 8: 0.74, 8.5: 0.83,
            9: 0.90, 9.5: 0.95, 10: 0.98, 10.5: 0.99, 11: 1.0,
            12: 1.0, 13: 1.0, 14: 1.0, 15: 1.0, 16: 1.0, 17: 1.0,
            18: 1.0, 19: 1.0, 20: 1.0, 21: 1.0, 22: 1.0, 23: 1.0,
            24: 1.0, 25: 0.0, 26: 0.0, 27: 0.0, 28: 0.0, 29: 0.0, 30: 0.0
        }
    
    def _get_vestas_8mw_power_curve(self) -> Dict[float, float]:
        """Vestas 8.0MW风机功率曲线 (基于文献引用的标准曲线)"""
        return {
            0: 0.0, 1: 0.0, 2: 0.0, 3: 0.0, 3.5: 0.01,
            4: 0.03, 4.5: 0.06, 5: 0.11, 5.5: 0.17, 6: 0.25,
            6.5: 0.34, 7: 0.44, 7.5: 0.55, 8: 0.66, 8.5: 0.76,
            9: 0.85, 9.5: 0.92, 10: 0.97, 10.5: 0.99, 11: 1.0,
            12: 1.0, 13: 1.0, 14: 1.0, 15: 1.0, 16: 1.0, 17: 1.0,
            18: 1.0, 19: 1.0, 20: 1.0, 21: 1.0, 22: 1.0, 23: 1.0,
            24: 1.0, 25: 0.0, 26: 0.0, 27: 0.0, 28: 0.0, 29: 0.0, 30: 0.0
        }
    
    def _apply_power_curve(self, wind_speed: float, power_curve: Dict) -> float:
        """应用功率曲线进行线性插值"""
        wind_speeds = np.array(list(power_curve.keys()))
        capacity_factors = np.array(list(power_curve.values()))
        
        if wind_speed <= wind_speeds[0]:
            return capacity_factors[0]
        elif wind_speed >= wind_speeds[-1]:
            return capacity_factors[-1]
        
        return np.interp(wind_speed, wind_speeds, capacity_factors)
    
    def _calculate_solar_geometry(self, lat: np.ndarray, lon: np.ndarray, 
                                 time_hours: np.ndarray) -> Dict:
        """计算太阳几何角度 (公式10-12)"""
        # 假设time_hours是年内小时数 (0-8759)
        day_of_year = time_hours // 24 + 1
        hour_of_day = time_hours % 24
        
        # 公式12: δ = -23.45 cos[360(n+10)/365.25]
        declination = -23.45 * np.cos(360 * (day_of_year + 10) / 365.25 * np.pi / 180)
        
        # 太阳时角
        solar_hour_angle = 15 * (hour_of_day - 12)  # 度
        
        # 公式11: θz = cos⁻¹[sin δ × sin LAT + cos δ × cos LAT × cos ω]
        lat_rad = lat * np.pi / 180
        dec_rad = declination * np.pi / 180
        hour_rad = solar_hour_angle * np.pi / 180
        
        cos_zenith = (np.sin(dec_rad) * np.sin(lat_rad) + 
                     np.cos(dec_rad) * np.cos(lat_rad) * np.cos(hour_rad))
        zenith_angle = np.arccos(np.clip(cos_zenith, -1, 1)) * 180 / np.pi
        
        return {
            'zenith_angle': zenith_angle,
            'declination': declination,
            'hour_angle': solar_hour_angle
        }
    
    def _calculate_panel_irradiance_two_axis(self, direct_h: np.ndarray,
                                           diffuse_h: np.ndarray,
                                           total_h: np.ndarray,
                                           solar_angles: Dict) -> np.ndarray:
        """计算双轴跟踪面板辐照度 (公式6-9)"""
        # 对于双轴跟踪系统，面板始终垂直于太阳光线
        zenith_rad = solar_angles['zenith_angle'] * np.pi / 180
        
        # 公式7: IBΣ = IBH × cos θ0 (对于双轴跟踪，θ0 = 0，cos θ0 = 1)
        # 但需要从水平面直射辐照度计算法向直射辐照度
        dni = direct_h / np.cos(zenith_rad).clip(0.01, 1)  # 避免除零
        direct_panel = dni  # 双轴跟踪直接接收DNI
        
        # 公式8: IDΣ = IDH × Rd (对于双轴跟踪，假设接收50%散射辐射)
        diffuse_panel = diffuse_h * 0.5
        
        # 公式9: IRΣ = IH × ρf × (1-cos Σ)/2 (双轴跟踪反射分量很小)
        reflected_panel = total_h * self.solar_params['albedo'] * 0.1
        
        # 公式6: IΣ = IBΣ + IDΣ + IRΣ
        total_panel = direct_panel + diffuse_panel + reflected_panel
        
        return np.clip(total_panel, 0, None)
    
    def _calculate_temperature_coefficient(self, irradiance: np.ndarray,
                                         wind_speed: np.ndarray,
                                         air_temp: np.ndarray) -> np.ndarray:
        """计算温度修正系数 (公式13-14)"""
        # 公式14: Tcell = c1 + c2 + c3 × I - c4 × V
        cell_temp = (self.solar_params['c1'] + 
                    self.solar_params['c2'] * air_temp +
                    self.solar_params['c3'] * irradiance - 
                    self.solar_params['c4'] * wind_speed)
        
        # 公式13: TEMcoef = 1 - γ × (Tcell - TSTC)
        temp_coef = (1 - self.solar_params['temperature_coefficient'] * 
                    (cell_temp - self.solar_params['standard_test_temp']))
        
        return np.clip(temp_coef, 0.5, 1.2)  # 合理范围限制

def demo_calculation():
    """演示计算过程"""
    logger.info("开始可再生能源潜力评估演示...")
    
    # 初始化评估器
    assessor = LiteratureBasedRenewableAssessment()
    
    # 创建示例数据 (1度网格，24小时)
    lat = np.array([30, 31, 32])  # 3个纬度
    lon = np.array([120, 121, 122])  # 3个经度
    time_hours = np.arange(24)  # 24小时
    
    # 创建网格
    LAT, LON, TIME = np.meshgrid(lat, lon, time_hours, indexing='ij')
    
    # 模拟气象数据
    np.random.seed(42)  # 确保可重复
    uas = np.random.normal(3, 2, LAT.shape)  # 北向风速
    vas = np.random.normal(2, 1.5, LAT.shape)  # 东向风速
    direct_irr = np.maximum(0, 500 * np.sin(TIME * np.pi / 12))  # 直射辐照度
    diffuse_irr = np.maximum(0, 200 * np.sin(TIME * np.pi / 12))  # 散射辐照度
    total_irr = direct_irr + diffuse_irr  # 总辐照度
    air_temp = 25 + 5 * np.sin(TIME * np.pi / 12)  # 气温
    wind_speed = np.sqrt(uas**2 + vas**2)  # 风速
    
    # 计算风电容量因子
    logger.info("计算陆上风电容量因子...")
    wind_onshore_cf = assessor.calculate_wind_capacity_factor(uas, vas, 'onshore')
    
    logger.info("计算海上风电容量因子...")
    wind_offshore_cf = assessor.calculate_wind_capacity_factor(uas, vas, 'offshore')
    
    # 计算太阳能容量因子
    logger.info("计算太阳能容量因子...")
    solar_cf = assessor.calculate_solar_capacity_factor(
        direct_irr, diffuse_irr, total_irr, air_temp, wind_speed,
        LAT, LON, TIME
    )
    
    # 输出结果
    logger.info("计算完成！结果统计:")
    logger.info(f"陆上风电容量因子: 平均={wind_onshore_cf.mean():.3f}, 范围=[{wind_onshore_cf.min():.3f}, {wind_onshore_cf.max():.3f}]")
    logger.info(f"海上风电容量因子: 平均={wind_offshore_cf.mean():.3f}, 范围=[{wind_offshore_cf.min():.3f}, {wind_offshore_cf.max():.3f}]")
    logger.info(f"太阳能容量因子: 平均={solar_cf.mean():.3f}, 范围=[{solar_cf.min():.3f}, {solar_cf.max():.3f}]")
    
    return {
        'wind_onshore_cf': wind_onshore_cf,
        'wind_offshore_cf': wind_offshore_cf,
        'solar_cf': solar_cf
    }

if __name__ == "__main__":
    results = demo_calculation()
    print("演示完成！基于文献方法的可再生能源评估已实现。")
