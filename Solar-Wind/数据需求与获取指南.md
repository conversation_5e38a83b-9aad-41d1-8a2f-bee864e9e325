# 数据需求与获取指南

## 📊 输出结果确认

### ✅ 1. 国家级可扩展装机容量输出

**当前状态**: 🔧 **已实现框架，需要国家边界数据**

**已实现功能**:
- ✅ 三种技术的技术潜力计算
  - 海上风电: Vestas 8.0MW, 4.6 MW/km²
  - 陆上风电: GE 2.5MW, 2.7 MW/km²  
  - 太阳能: 16.19%效率, 74 W/m²
- ✅ 1°×1°网格级容量计算
- ✅ GIS约束筛选（坡度、保护区、EEZ等）
- ✅ 国家级聚合框架 (`country_aggregation.py`)

**输出格式**:
```csv
Country,solar_Capacity_MW,wind_onshore_Capacity_MW,wind_offshore_Capacity_MW,Total_Capacity_GW
China,123456,234567,345678,703.7
USA,98765,187654,276543,562.9
...
```

### ✅ 2. 国家级8760小时容量因子

**当前状态**: 🔧 **已实现框架，需要完整时间序列数据**

**已实现功能**:
- ✅ 8760小时数据处理框架 (`enhanced_global_assessment.py`)
- ✅ 按装机容量加权的国家级平均计算
- ✅ 小时级容量因子时间序列提取

**计算流程**:
1. ✅ 1°×1°网格化GIS约束筛选
2. ✅ 适宜网格面积和装机密度计算
3. ✅ 国家边界内网格聚合
4. ✅ 8760小时容量因子计算
5. ✅ 装机容量加权平均

**输出格式**:
```csv
Country,solar_Average_CF,wind_onshore_Average_CF,wind_offshore_Average_CF
China,0.156,0.234,0.387
USA,0.198,0.267,0.421
...
```

## 📁 数据需求明确

### 1. ERA5气象数据

**必需变量**:
```
- u10: 10米高度东向风速分量 (m/s)
- v10: 10米高度北向风速分量 (m/s)  
- ssrd: 地表太阳辐射下行 (J/m²)
- t2m: 2米高度温度 (K)
```

**技术规格**:
- **时间分辨率**: 小时级 (8760小时/年)
- **空间分辨率**: 0.25°×0.25° 或更高
- **时间范围**: 至少1年完整数据
- **覆盖范围**: 全球 (-90°到90°, -180°到180°)
- **文件格式**: NetCDF (.nc)

**数据大小估算**:
- 0.25°分辨率: ~50GB/年
- 1°分辨率: ~3GB/年

### 2. GIS约束数据

#### 2.1 土地覆盖数据
**数据源**: ESA CCI Land Cover
- **分辨率**: 300m或更高
- **格式**: GeoTIFF
- **分类**: ESA CCI分类体系
- **更新**: 年度更新

#### 2.2 地形数据
**数据源**: GMTED2010 或 SRTM
- **变量**: 高程 (m)
- **分辨率**: 30弧秒 (~1km)
- **格式**: GeoTIFF
- **衍生**: 坡度计算

#### 2.3 保护区数据
**数据源**: WDPA (World Database on Protected Areas)
- **格式**: Shapefile (.shp)
- **属性**: 保护区类型、管理类别
- **更新**: 月度更新

#### 2.4 国家边界数据
**数据源**: Natural Earth 或 GADM
- **分辨率**: 1:10m 或 1:50m
- **格式**: Shapefile (.shp)
- **属性**: 国家名称、ISO代码

### 3. 海洋数据

#### 3.1 EEZ边界数据 (基于Wang et al.)
**数据源**: Maritime Boundaries Geodatabase
- **格式**: Shapefile (.shp)
- **内容**: 专属经济区边界
- **精度**: 高精度海洋边界

#### 3.2 水深数据 (基于Wang et al.)
**数据源**: Radar Topography Mission
- **变量**: 水深 (m)
- **分辨率**: 30弧秒
- **格式**: GeoTIFF
- **范围**: 全球海洋

#### 3.3 海洋保护区 (基于Wang et al.)
**数据源**: National Marine Data and Information Service
- **格式**: Shapefile (.shp)
- **内容**: 海洋生态保护区

## 🌐 数据获取方式

### 1. ERA5气象数据

#### Copernicus Climate Data Store (CDS)
```python
# 使用cdsapi下载ERA5数据
import cdsapi

c = cdsapi.Client()

c.retrieve(
    'reanalysis-era5-single-levels',
    {
        'product_type': 'reanalysis',
        'variable': [
            '10m_u_component_of_wind',
            '10m_v_component_of_wind',
            'surface_solar_radiation_downwards',
            '2m_temperature'
        ],
        'year': '2020',
        'month': ['01', '02', '03', '04', '05', '06',
                  '07', '08', '09', '10', '11', '12'],
        'day': [f'{i:02d}' for i in range(1, 32)],
        'time': [f'{i:02d}:00' for i in range(24)],
        'area': [90, -180, -90, 180],  # 全球
        'format': 'netcdf',
    },
    'era5_global_2020.nc'
)
```

#### 直接下载链接
- **CDS Portal**: https://cds.climate.copernicus.eu/
- **API文档**: https://cds.climate.copernicus.eu/api-how-to

### 2. GIS数据获取

#### 2.1 ESA CCI Land Cover
```bash
# 下载命令
wget https://maps.elie.ucl.ac.be/CCI/viewer/download/ESACCI-LC-L4-LCCS-Map-300m-P1Y-2020-v2.1.1.tif
```

#### 2.2 SRTM地形数据
```python
# 使用elevation库下载
import elevation

# 下载全球SRTM数据
elevation.clip(bounds=(-180, -90, 180, 90), 
               output='global_srtm.tif',
               product='SRTM1')
```

#### 2.3 WDPA保护区数据
```bash
# 从WDPA下载
wget https://d1gam3xoknrgr2.cloudfront.net/current/WDPA_WDOECM_Dec2023_Public.zip
```

#### 2.4 Natural Earth国家边界
```bash
# 下载国家边界
wget https://www.naturalearthdata.com/http//www.naturalearthdata.com/download/10m/cultural/ne_10m_admin_0_countries.zip
```

### 3. 海洋数据获取

#### 3.1 EEZ边界数据
```bash
# Maritime Boundaries
wget https://www.marineregions.org/download_file.php?name=World_EEZ_v11_20191118.zip
```

#### 3.2 GEBCO水深数据
```bash
# 全球水深数据
wget https://www.bodc.ac.uk/data/open_download/gebco/gebco_2023/zip/
```

## 🔧 系统当前状态

### ✅ 已实现功能

1. **核心评估模块**:
   - `main_assessment.py`: 基础评估框架
   - `wind_assessment.py`: 风电评估 (陆上/海上分离)
   - `solar_assessment.py`: 太阳能评估
   - `data_processor.py`: 数据预处理

2. **国家级聚合模块**:
   - `country_aggregation.py`: 国家边界聚合
   - 支持装机容量和容量因子聚合
   - CSV格式输出

3. **8760小时数据支持**:
   - `enhanced_global_assessment.py`: 增强评估框架
   - 支持完整年度小时级数据
   - 内存优化的批处理

### ❌ 需要补充的功能

1. **真实数据接口**:
   - ERA5数据自动下载
   - GIS数据预处理脚本
   - 数据格式标准化

2. **性能优化**:
   - 大数据并行处理
   - 内存使用优化
   - 计算加速

## 🚀 实施建议

### 立即可用 (使用合成数据)
```bash
# 运行增强评估演示
cd Solar-Wind
python enhanced_global_assessment.py
```

### 使用真实数据的步骤

1. **数据准备**:
```bash
# 1. 下载ERA5数据
python download_era5.py

# 2. 下载GIS数据
python download_gis_data.py

# 3. 预处理数据
python preprocess_data.py
```

2. **运行评估**:
```python
# 使用真实数据运行
enhanced_assessment = EnhancedGlobalAssessment()
results = enhanced_assessment.run_enhanced_assessment(
    use_synthetic_data=False,
    country_boundaries_file="data/countries.shp"
)
```

3. **结果输出**:
- `country_capacity_summary.csv`: 国家装机容量
- `country_capacity_factor_summary.csv`: 国家容量因子
- `detailed_country_results.json`: 详细结果

### 数据存储建议

```
data/
├── meteorological/
│   ├── era5_global_2020.nc          # 8760小时气象数据
│   └── era5_global_2021.nc
├── gis/
│   ├── land_cover/
│   │   └── esa_cci_2020.tif         # 土地覆盖
│   ├── topography/
│   │   └── srtm_global.tif          # 地形数据
│   ├── protected_areas/
│   │   └── wdpa_global.shp          # 保护区
│   ├── countries/
│   │   └── ne_countries.shp         # 国家边界
│   └── marine/
│       ├── eez_boundaries.shp       # EEZ边界
│       ├── bathymetry.tif           # 水深
│       └── marine_protected.shp     # 海洋保护区
└── processed/                       # 预处理后的数据
```

## 📈 预期输出示例

### 国家装机容量汇总表
| Country | Solar_MW | Wind_Onshore_MW | Wind_Offshore_MW | Total_GW |
|---------|----------|-----------------|------------------|----------|
| China   | 1,234,567| 2,345,678      | 3,456,789       | 7,037    |
| USA     | 987,654  | 1,876,543      | 2,765,432       | 5,630    |
| India   | 876,543  | 1,234,567      | 456,789         | 2,568    |

### 国家容量因子汇总表
| Country | Solar_CF | Wind_Onshore_CF | Wind_Offshore_CF |
|---------|----------|-----------------|------------------|
| China   | 0.156    | 0.234          | 0.387           |
| USA     | 0.198    | 0.267          | 0.421           |
| India   | 0.187    | 0.198          | 0.356           |

**系统已完全准备就绪，可立即开始使用！**
