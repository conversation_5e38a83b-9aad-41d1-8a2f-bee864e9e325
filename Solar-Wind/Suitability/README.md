# 可再生能源适宜性评估系统
## Renewable Energy Suitability Assessment System

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Documentation](https://img.shields.io/badge/Documentation-Complete-brightgreen.svg)](scripts/docs/)

基于Nature Communications方法论的全球可再生能源适宜性评估系统，支持太阳能光伏、陆上风电和海上风电的适宜性分析。

## 🎯 项目概述

本项目实现了严格遵循科学文献的可再生能源适宜性评估方法，主要基于以下研究：
- **Zheng et al., Nature Communications, 2025** - 太阳能和陆上风电方法论
- **Wang et al., Nature Communications, 2025** - 海上风电约束条件

### 主要功能

- ✅ **太阳能光伏适宜性评估**: 基于坡度、土地覆盖和保护区约束
- ✅ **陆上风电适宜性评估**: 基于坡度、土地覆盖和保护区约束  
- ✅ **海上风电适宜性评估**: 基于水深、EEZ和环境保护约束
- ✅ **多尺度分析**: 支持全球、区域和国家级分析
- ✅ **可视化系统**: 完整的地图可视化和统计分析
- ✅ **质量控制**: 严格的数据验证和结果检查机制

## 📁 项目结构

```
Solar-Wind/Suitability/
├── scripts/                          # 核心脚本文件
│   ├── docs/                         # 详细技术文档
│   │   ├── 00_overall_methodology_documentation.md
│   │   ├── 01_terrain_data_processor_documentation.md
│   │   ├── 02_protected_areas_processor_documentation.md
│   │   ├── 03_landcover_raw_analysis_documentation.md
│   │   ├── 04_landcover_reclassification_documentation.md
│   │   ├── 05_integrate_suitability_documentation.md
│   │   ├── 06_country_capacity_analysis_documentation.md
│   │   ├── 07_offshore_wind_technical_documentation.md
│   │   └── terminology_glossary.md
│   ├── terrain_data_processor.py     # 地形数据处理
│   ├── protected_areas_processor.py  # 保护区数据处理
│   ├── landcover_raw_analysis.py     # 土地覆盖原始分析
│   ├── landcover_reclassification_visualization.py  # 土地覆盖重分类
│   ├── integrate_suitability.py      # 适宜性综合分析
│   ├── offshore_wind_suitability.py  # 海上风电适宜性
│   ├── country_capacity_analysis.py  # 国家级容量分析
│   ├── README.md                     # 脚本使用说明
│   └── EXECUTION_CHECKLIST.md        # 执行检查清单
├── outputs/                          # 输出结果目录
│   ├── processed_data/               # 处理后的数据
│   ├── final_suitability/            # 最终适宜性结果
│   ├── visualizations/               # 可视化图表
│   ├── reports/                      # 分析报告
│   └── country_analysis/             # 国家级分析结果
├── examples/                         # 示例文件和小型测试数据
├── .gitignore                        # Git忽略文件配置
└── README.md                         # 本文档
```

## 🚀 快速开始

### 环境要求

- **Python**: 3.8 或更高版本
- **内存**: 建议 16GB 或更多
- **存储**: 建议 100GB 可用空间（用于大型数据集）

### 依赖包安装

```bash
# 创建虚拟环境
conda create -n renewable-energy python=3.9
conda activate renewable-energy

# 安装必要的包
pip install rasterio geopandas xarray netcdf4 matplotlib seaborn pandas numpy scipy
pip install cartopy folium plotly dash
```

### 数据准备

由于GitHub文件大小限制，大型源数据文件需要单独下载：

#### 1. 地形数据 (GMTED2010)
```bash
# 下载GMTED2010全球高程数据
# 来源: https://www.usgs.gov/coastal-changes-and-impacts/gmted2010
# 文件: GMTED2010_*.tif (约15GB)
# 放置位置: data/terrain/
```

#### 2. 土地覆盖数据 (ESA CCI)
```bash
# 下载ESA CCI Land Cover数据
# 来源: https://www.esa-landcover-cci.org/
# 文件: ESACCI-LC-L4-LCCS-Map-300m-P1Y-*.nc (约1GB/年)
# 放置位置: data/landcover/
```

#### 3. 保护区数据 (WDPA)
```bash
# 下载世界保护区数据库
# 来源: https://www.protectedplanet.net/
# 文件: WDPA_*.gdb (约2GB)
# 放置位置: data/protected_areas/
```

#### 4. 海洋数据
```bash
# EEZ专属经济区数据
# 来源: https://www.marineregions.org/
# 文件: World_EEZ_v10.shp (约500MB)

# GEBCO水深数据  
# 来源: https://www.gebco.net/
# 文件: GEBCO_2024.nc (约13GB)
# 放置位置: data/bathymetry/
```

### 执行流程

1. **地形数据处理**:
```bash
cd scripts
python terrain_data_processor.py
```

2. **保护区数据处理**:
```bash
python protected_areas_processor.py
```

3. **土地覆盖分析**:
```bash
python landcover_raw_analysis.py
python landcover_reclassification_visualization.py
```

4. **适宜性综合分析**:
```bash
python integrate_suitability.py
```

5. **海上风电专项分析**:
```bash
python offshore_wind_suitability.py
```

6. **国家级容量分析**:
```bash
python country_capacity_analysis.py
```

## 📊 输出结果

### 主要输出文件

1. **适宜性栅格数据**: 
   - `solar_suitability.tif` - 太阳能适宜性
   - `onshore_wind_suitability.tif` - 陆上风电适宜性
   - `offshore_wind_suitability.tif` - 海上风电适宜性

2. **统计分析结果**:
   - `suitability_detailed_results.csv` - 详细统计数据
   - `country_capacity_analysis.xlsx` - 国家级容量分析

3. **可视化图表**:
   - 全球适宜性分布图
   - 国家级对比分析图
   - 技术潜力评估图

4. **技术报告**:
   - 方法论说明
   - 质量控制报告
   - 不确定性分析

## 🔬 技术方法

### 太阳能光伏适宜性

**约束条件**:
- 坡度 ≤ 5°
- 排除水体、城市、裸地
- 排除保护区

**数据源**:
- 地形: GMTED2010
- 土地覆盖: ESA CCI Land Cover
- 保护区: WDPA

### 陆上风电适宜性

**约束条件**:
- 坡度 ≤ 20°
- 排除水体、城市、裸地
- 排除保护区

**技术参数**:
- 风机型号: GE 2.5MW
- 轮毂高度: 100m
- 装机密度: 2.7 MW/km²

### 海上风电适宜性

**约束条件**:
- 水深: 1-60m
- 限制在EEZ内
- 排除海洋保护区

**技术参数**:
- 风机型号: Vestas 8.0MW
- 轮毂高度: 100m
- 装机密度: 4.6 MW/km²

## 📈 质量控制

### 数据验证
- 输入数据完整性检查
- 坐标系统一致性验证
- 数值范围合理性检查

### 结果验证
- 适宜性比例合理性检查
- 空间分布连续性验证
- 与文献结果对比验证

### 不确定性分析
- 参数敏感性分析
- 蒙特卡洛不确定性传播
- 置信区间估计

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 创建 [Issue](../../issues)
- 发送邮件至项目维护者

## 🙏 致谢

感谢以下数据提供方和研究团队：
- ESA Climate Change Initiative Land Cover项目
- USGS GMTED2010项目团队
- UNEP-WCMC世界保护区数据库
- GEBCO海洋测深数据项目
- Zheng et al. 和 Wang et al. 的开创性研究

## 📚 引用

如果您在研究中使用了本项目，请引用：

```bibtex
@software{renewable_energy_suitability,
  title={Renewable Energy Suitability Assessment System},
  author={[Your Name]},
  year={2025},
  url={https://github.com/[username]/renewable-energy-suitability}
}
```

---

**注意**: 本项目仅用于学术研究目的。实际项目开发请结合当地具体条件和最新政策法规。
