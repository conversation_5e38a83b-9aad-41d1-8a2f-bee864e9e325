#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
按国家聚合的可开发容量分析脚本
使用国家边界数据，计算每个国家的可开发风光面积和容量
"""

import os
import numpy as np
import pandas as pd
import geopandas as gpd
import rasterio
from rasterio.features import rasterize
from rasterio.transform import from_bounds
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

# 容量密度参数（根据参考文献）
SOLAR_DENSITY = 74  # W/m², 74 W/m²
WIND_DENSITY = 2.7  # MW/km², 2.7 MW/km² for onshore wind

def setup_directories():
    """设置输出目录"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    base_dir = os.path.dirname(script_dir)
    
    outputs_dir = os.path.join(base_dir, 'outputs')
    country_analysis_dir = os.path.join(outputs_dir, 'country_analysis')
    
    os.makedirs(country_analysis_dir, exist_ok=True)
    
    return country_analysis_dir

def load_country_boundaries():
    """加载国家边界数据"""
    print("\n加载国家边界数据...")
    
    script_dir = os.path.dirname(os.path.abspath(__file__))
    countries_file = os.path.join(
        script_dir, '..', '..', 'data', 'gis', 'countries', 
        'ne_50m_admin_0_countries', 'ne_50m_admin_0_countries.shp'
    )
    
    if not os.path.exists(countries_file):
        raise FileNotFoundError(f"未找到国家边界文件: {countries_file}")
    
    # 读取国家边界数据
    countries_gdf = gpd.read_file(countries_file)
    
    # 转换到WGS84坐标系
    if countries_gdf.crs != 'EPSG:4326':
        countries_gdf = countries_gdf.to_crs('EPSG:4326')
    
    print(f"加载了 {len(countries_gdf)} 个国家/地区")
    print(f"字段: {list(countries_gdf.columns)}")
    
    return countries_gdf

def load_suitability_data():
    """加载适宜性数据"""
    print("\n加载适宜性数据...")
    
    script_dir = os.path.dirname(os.path.abspath(__file__))
    base_dir = os.path.dirname(script_dir)
    final_dir = os.path.join(base_dir, 'outputs', 'final_suitability')
    
    # 加载不考虑保护区约束的数据（用于对比）
    solar_file_no_protected = os.path.join(final_dir, 'solar_final_suitable_areas_no_protected.tif')
    wind_file_no_protected = os.path.join(final_dir, 'onshore_wind_final_suitable_areas_no_protected.tif')
    
    # 加载考虑保护区约束的数据
    solar_file_protected = os.path.join(final_dir, 'solar_final_suitable_areas.tif')
    wind_file_protected = os.path.join(final_dir, 'onshore_wind_final_suitable_areas.tif')
    
    data = {}
    
    # 加载数据
    for name, filepath in [
        ('solar_no_protected', solar_file_no_protected),
        ('wind_no_protected', wind_file_no_protected),
        ('solar_protected', solar_file_protected),
        ('wind_protected', wind_file_protected)
    ]:
        if os.path.exists(filepath):
            with rasterio.open(filepath) as src:
                data[name] = {
                    'array': src.read(1),
                    'transform': src.transform,
                    'crs': src.crs,
                    'bounds': src.bounds,
                    'shape': src.shape
                }
                print(f"加载 {name}: {src.shape}, 范围: {src.bounds}")
        else:
            print(f"警告: 未找到文件 {filepath}")
    
    return data

def calculate_pixel_area(transform):
    """计算每个像元的面积（平方米）"""
    # 0.5度分辨率
    pixel_width = abs(transform[0])  # 经度方向
    pixel_height = abs(transform[4])  # 纬度方向
    
    # 地球半径（米）
    earth_radius = 6371000
    
    # 计算每个像元的面积（平方米）
    # 纬度1度 ≈ 111,320米
    # 经度1度 ≈ 111,320 * cos(lat)米，这里用平均值
    lat_meters_per_degree = 111320
    lon_meters_per_degree = 111320 * np.cos(np.radians(45))  # 使用45度作为平均纬度
    
    pixel_area_m2 = (pixel_width * lon_meters_per_degree) * (pixel_height * lat_meters_per_degree)
    pixel_area_km2 = pixel_area_m2 / 1000000  # 转换为平方公里
    
    print(f"像元尺寸: {pixel_width:.3f}° × {pixel_height:.3f}°")
    print(f"像元面积: {pixel_area_m2:.0f} m² ({pixel_area_km2:.2f} km²)")
    
    return pixel_area_m2, pixel_area_km2

def create_country_masks(countries_gdf, template_data):
    """为每个国家创建栅格掩码"""
    print("\n为每个国家创建栅格掩码...")
    
    template_array = template_data['array']
    transform = template_data['transform']
    height, width = template_array.shape
    
    country_masks = {}
    
    for idx, country in countries_gdf.iterrows():
        country_name = country.get('NAME', f'Country_{idx}')
        country_code = country.get('ISO_A3', f'UNK_{idx}')
        
        # 创建该国家的掩码
        try:
            if country.geometry is not None and not country.geometry.is_empty:
                shapes = [(country.geometry, 1)]
                
                mask = rasterize(
                    shapes,
                    out_shape=(height, width),
                    transform=transform,
                    fill=0,
                    dtype=np.uint8,
                    all_touched=True
                )
                
                country_masks[country_code] = {
                    'name': country_name,
                    'mask': mask,
                    'total_pixels': np.sum(mask == 1)
                }
                
                if idx % 50 == 0:
                    print(f"  处理第 {idx+1} 个国家: {country_name}")
                    
        except Exception as e:
            print(f"  错误处理国家 {country_name}: {str(e)}")
            continue
    
    print(f"成功创建 {len(country_masks)} 个国家掩码")
    return country_masks

def calculate_country_capacity(country_masks, suitability_data, pixel_area_m2, pixel_area_km2):
    """计算每个国家的可开发容量"""
    print("\n计算每个国家的可开发容量...")
    
    results = []
    
    for country_code, country_info in country_masks.items():
        country_name = country_info['name']
        country_mask = country_info['mask']
        total_pixels = country_info['total_pixels']
        
        if total_pixels == 0:
            continue
        
        result = {
            'Country_Code': country_code,
            'Country_Name': country_name,
            'Total_Land_Area_km2': total_pixels * pixel_area_km2
        }
        
        # 计算每种场景的可开发面积和容量
        for scenario in ['no_protected', 'protected']:
            for tech in ['solar', 'wind']:
                data_key = f"{tech}_{scenario}"
                
                if data_key in suitability_data:
                    suitable_array = suitability_data[data_key]['array']
                    
                    # 计算该国家内的适宜像元
                    country_suitable = (suitable_array > 0) & (country_mask == 1)
                    suitable_pixels = np.sum(country_suitable)
                    
                    # 计算面积
                    suitable_area_km2 = suitable_pixels * pixel_area_km2
                    suitable_area_m2 = suitable_pixels * pixel_area_m2
                    
                    # 计算容量
                    if tech == 'solar':
                        # 太阳能: 74 W/m²
                        capacity_w = suitable_area_m2 * SOLAR_DENSITY
                        capacity_mw = capacity_w / 1000000
                        capacity_gw = capacity_mw / 1000
                    else:
                        # 风电: 2.7 MW/km²
                        capacity_mw = suitable_area_km2 * WIND_DENSITY
                        capacity_gw = capacity_mw / 1000
                    
                    # 添加到结果
                    prefix = f"{tech.title()}_{scenario.replace('_', ' ').title().replace(' ', '_')}"
                    result[f'{prefix}_Area_km2'] = suitable_area_km2
                    result[f'{prefix}_Capacity_GW'] = capacity_gw
                    result[f'{prefix}_Area_Ratio'] = (suitable_area_km2 / result['Total_Land_Area_km2'] * 100) if result['Total_Land_Area_km2'] > 0 else 0
        
        results.append(result)
    
    return pd.DataFrame(results)

def save_results(results_df, output_dir):
    """保存结果"""
    print("\n保存结果...")
    
    # 保存完整结果
    full_results_file = os.path.join(output_dir, 'country_renewable_capacity.csv')
    results_df.to_csv(full_results_file, index=False)
    print(f"完整结果已保存: {full_results_file}")
    
    # 筛选有意义的数据（有可开发容量的国家）
    meaningful_df = results_df[
        (results_df['Solar_Protected_Capacity_GW'] > 0.1) |
        (results_df['Wind_Protected_Capacity_GW'] > 0.1)
    ].copy()
    
    # 按总容量排序
    meaningful_df['Total_Protected_Capacity_GW'] = (
        meaningful_df['Solar_Protected_Capacity_GW'] + 
        meaningful_df['Wind_Protected_Capacity_GW']
    )
    meaningful_df = meaningful_df.sort_values('Total_Protected_Capacity_GW', ascending=False)
    
    # 保存主要结果
    main_results_file = os.path.join(output_dir, 'country_renewable_capacity_main.csv')
    meaningful_df.to_csv(main_results_file, index=False)
    print(f"主要结果已保存: {main_results_file}")
    
    # 打印前20名
    print("\n前20名可开发容量最大的国家（考虑保护区约束）:")
    print("="*80)
    for idx, row in meaningful_df.head(20).iterrows():
        print(f"{row['Country_Name']:25} | "
              f"太阳能: {row['Solar_Protected_Capacity_GW']:8.1f} GW | "
              f"风电: {row['Wind_Protected_Capacity_GW']:8.1f} GW | "
              f"合计: {row['Total_Protected_Capacity_GW']:8.1f} GW")
    
    return full_results_file, main_results_file

def create_visualizations(results_df, output_dir):
    """创建可视化"""
    print("\n创建可视化...")
    
    # 添加总容量列（如果不存在）
    if 'Total_Protected_Capacity_GW' not in results_df.columns:
        results_df['Total_Protected_Capacity_GW'] = (
            results_df['Solar_Protected_Capacity_GW'] + 
            results_df['Wind_Protected_Capacity_GW']
        )
    
    # 筛选前20名国家
    top_countries = results_df.nlargest(20, 'Total_Protected_Capacity_GW')
    
    # 1. 容量对比图
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12))
    
    # 保护区约束对比
    countries = top_countries['Country_Name']
    solar_protected = top_countries['Solar_Protected_Capacity_GW']
    solar_no_protected = top_countries['Solar_No_Protected_Capacity_GW']
    wind_protected = top_countries['Wind_Protected_Capacity_GW']
    wind_no_protected = top_countries['Wind_No_Protected_Capacity_GW']
    
    x = np.arange(len(countries))
    width = 0.35
    
    # 太阳能对比
    ax1.bar(x - width/2, solar_no_protected, width, label='Without Protected Areas', alpha=0.8, color='orange')
    ax1.bar(x + width/2, solar_protected, width, label='With Protected Areas', alpha=0.8, color='red')
    ax1.set_title('Solar PV Potential by Country (Top 20)')
    ax1.set_ylabel('Capacity (GW)')
    ax1.set_xticks(x)
    ax1.set_xticklabels(countries, rotation=45, ha='right')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 风电对比
    ax2.bar(x - width/2, wind_no_protected, width, label='Without Protected Areas', alpha=0.8, color='lightblue')
    ax2.bar(x + width/2, wind_protected, width, label='With Protected Areas', alpha=0.8, color='blue')
    ax2.set_title('Onshore Wind Potential by Country (Top 20)')
    ax2.set_ylabel('Capacity (GW)')
    ax2.set_xticks(x)
    ax2.set_xticklabels(countries, rotation=45, ha='right')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    capacity_chart_file = os.path.join(output_dir, 'country_capacity_comparison.png')
    plt.savefig(capacity_chart_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 保护区影响分析
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    
    # 计算保护区影响
    solar_impact = ((solar_no_protected - solar_protected) / solar_no_protected * 100).fillna(0)
    wind_impact = ((wind_no_protected - wind_protected) / wind_no_protected * 100).fillna(0)
    
    # 太阳能影响
    ax1.barh(range(len(countries)), solar_impact, alpha=0.8, color='orange')
    ax1.set_title('Solar PV Capacity Loss Due to Protected Areas (%)')
    ax1.set_xlabel('Capacity Loss (%)')
    ax1.set_yticks(range(len(countries)))
    ax1.set_yticklabels(countries)
    ax1.grid(True, alpha=0.3)
    
    # 风电影响
    ax2.barh(range(len(countries)), wind_impact, alpha=0.8, color='blue')
    ax2.set_title('Onshore Wind Capacity Loss Due to Protected Areas (%)')
    ax2.set_xlabel('Capacity Loss (%)')
    ax2.set_yticks(range(len(countries)))
    ax2.set_yticklabels(countries)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    impact_chart_file = os.path.join(output_dir, 'protected_areas_impact.png')
    plt.savefig(impact_chart_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"容量对比图已保存: {capacity_chart_file}")
    print(f"保护区影响图已保存: {impact_chart_file}")
    
    return capacity_chart_file, impact_chart_file

def main():
    """主函数"""
    print("按国家聚合的可开发容量分析")
    print("="*50)
    
    try:
        # 设置目录
        output_dir = setup_directories()
        
        # 加载国家边界数据
        countries_gdf = load_country_boundaries()
        
        # 加载适宜性数据
        suitability_data = load_suitability_data()
        
        if not suitability_data:
            print("错误: 未找到适宜性数据")
            return
        
        # 使用第一个可用数据作为模板
        template_key = list(suitability_data.keys())[0]
        template_data = suitability_data[template_key]
        
        # 计算像元面积
        pixel_area_m2, pixel_area_km2 = calculate_pixel_area(template_data['transform'])
        
        # 创建国家掩码
        country_masks = create_country_masks(countries_gdf, template_data)
        
        # 计算每个国家的可开发容量
        results_df = calculate_country_capacity(
            country_masks, suitability_data, pixel_area_m2, pixel_area_km2
        )
        
        # 保存结果
        full_file, main_file = save_results(results_df, output_dir)
        
        # 创建可视化
        chart1, chart2 = create_visualizations(results_df, output_dir)
        
        print("\n分析完成!")
        print(f"输出文件:")
        print(f"- 完整结果: {full_file}")
        print(f"- 主要结果: {main_file}")
        print(f"- 容量对比图: {chart1}")
        print(f"- 保护区影响图: {chart2}")
        
        # 打印全球汇总统计
        print("\n全球汇总统计（考虑保护区约束）:")
        total_solar = results_df['Solar_Protected_Capacity_GW'].sum()
        total_wind = results_df['Wind_Protected_Capacity_GW'].sum()
        total_combined = total_solar + total_wind
        
        print(f"全球太阳能可开发容量: {total_solar:.1f} GW")
        print(f"全球陆上风电可开发容量: {total_wind:.1f} GW")
        print(f"全球风光总可开发容量: {total_combined:.1f} GW")
        
        # 保护区影响统计
        total_solar_no_protected = results_df['Solar_No_Protected_Capacity_GW'].sum()
        total_wind_no_protected = results_df['Wind_No_Protected_Capacity_GW'].sum()
        
        solar_loss_pct = (total_solar_no_protected - total_solar) / total_solar_no_protected * 100
        wind_loss_pct = (total_wind_no_protected - total_wind) / total_wind_no_protected * 100
        
        print(f"\n保护区约束影响:")
        print(f"太阳能容量损失: {solar_loss_pct:.1f}%")
        print(f"风电容量损失: {wind_loss_pct:.1f}%")
        
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 