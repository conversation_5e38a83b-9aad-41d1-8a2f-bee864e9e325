# 全球风光资源适宜性分析工具包

## 概述

本工具包提供了一套完整的全球风光资源适宜性分析流程，包括土地覆盖分析、地形约束、保护区排除和按国家聚合的容量评估。工具包基于0.5度分辨率的全球数据，采用GIS空间分析方法，为可再生能源规划提供科学数据支持。

## 核心功能

- **土地覆盖适宜性分析**: 基于ESA CCI土地覆盖数据，识别适合风光开发的土地类型
- **地形约束评估**: 基于GMTED2010数据，计算坡度和海拔约束
- **保护区排除**: 基于WDPA数据，排除受保护的区域
- **空间整合分析**: 综合考虑所有约束条件，生成最终适宜性地图
- **国家级容量评估**: 按国家聚合，计算可开发面积和理论装机容量
- **对比分析**: 提供考虑和不考虑保护区约束的对比结果

## 技术参数

### 容量密度参数（基于参考文献）
- **太阳能光伏**: 74 W/m² (考虑组件间距和维护通道)
- **陆上风电**: 2.7 MW/km² (考虑7倍叶轮直径间距)

*参考文献: Jiang et al. (2025)*

### 约束条件
- **太阳能坡度约束**: ≤ 5°
- **风电坡度约束**: ≤ 20°
- **风电海拔约束**: ≤ 3000m
- **保护区排除**: WDPA状态为Designated/Inscribed/Established的区域

## 脚本说明

### 1. landcover_raw_analysis.py
**功能**: ESA CCI土地覆盖数据的初步分析和重采样
- 读取原始土地覆盖数据
- 分析土地类型分布和统计
- 重采样至0.5度分辨率（使用众数方法）
- 生成初步可视化

**输入**: ESA CCI土地覆盖NetCDF文件
**输出**: 重采样后的土地覆盖栅格、统计报告

### 2. landcover_reclassification_visualization.py
**功能**: 土地覆盖重分类和适宜性评估
- 将土地覆盖类型重分类为10的倍数
- 定义风光开发的适宜土地类型
- 排除不适宜的土地类型（如水体、城市、森林等）
- 生成适宜性地图和统计分析

**输入**: 重采样的土地覆盖数据
**输出**: 风光适宜区域栅格、可视化地图

### 3. terrain_data_processor.py
**功能**: 地形数据处理和约束计算
- 处理GMTED2010海拔数据
- 计算坡度（基于0.05度高分辨率）
- 使用99%分位数聚合至0.5度
- 应用地形约束条件

**输入**: GMTED2010 DEM数据
**输出**: 海拔栅格、坡度栅格

### 4. protected_areas_processor.py
**功能**: 保护区数据处理和栅格化
- 处理WDPA多分片shapefile数据
- 筛选有效保护区状态
- 批量栅格化至0.5度分辨率
- 生成保护区掩码

**输入**: WDPA shapefile数据
**输出**: 保护区栅格掩码、统计报告

### 5. integrate_suitability.py
**功能**: 综合适宜性分析和结果生成
- 整合土地覆盖、地形和保护区约束
- 生成最终适宜性地图
- 提供考虑和不考虑保护区的对比结果
- 输出详细的网格级CSV数据

**输入**: 所有约束图层
**输出**: 最终适宜性栅格、对比CSV、可视化地图

### 6. country_capacity_analysis.py
**功能**: 按国家聚合的容量评估
- 使用Natural Earth国家边界数据
- 空间聚合计算每个国家的适宜面积
- 基于容量密度参数计算理论装机容量
- 生成国家排名和对比分析

**输入**: 适宜性栅格、国家边界数据
**输出**: 国家容量CSV、排名图表、影响分析

## 工作流程

```
1. 土地覆盖分析
   ├── landcover_raw_analysis.py          # 原始数据分析
   └── landcover_reclassification_visualization.py  # 重分类和适宜性评估

2. 地形约束分析
   └── terrain_data_processor.py          # 坡度和海拔处理

3. 保护区约束
   └── protected_areas_processor.py       # 保护区栅格化

4. 综合分析
   ├── integrate_suitability.py           # 空间整合和对比
   └── country_capacity_analysis.py       # 国家级容量评估
```

## 运行环境

### 依赖包
```bash
conda create -n solar-wind python=3.9
conda activate solar-wind
conda install -c conda-forge geopandas rasterio xarray netcdf4 matplotlib pandas numpy
```

### 数据要求
```
Solar-Wind/
├── data/
│   ├── landcover/
│   │   └── ESACCI-LC-L4-LCCS-Map-300m-P1Y-2020-v2.1.1.nc
│   ├── terrain/
│   │   └── GMTED2010_*.nc
│   ├── gis/
│   │   ├── protected_areas/WDPA_Jul2025_Public_shp/
│   │   └── countries/ne_50m_admin_0_countries/
│   └── ...
└── Suitability/
    ├── scripts/          # 本脚本目录
    └── outputs/          # 输出结果目录
```

## 运行说明

### 完整流程运行
```bash
# 激活环境
conda activate solar-wind

# 进入脚本目录
cd Solar-Wind/Suitability/scripts

# 1. 土地覆盖分析
python landcover_raw_analysis.py
python landcover_reclassification_visualization.py

# 2. 地形数据处理
python terrain_data_processor.py

# 3. 保护区处理
python protected_areas_processor.py

# 4. 综合分析
python integrate_suitability.py

# 5. 国家容量评估
python country_capacity_analysis.py
```

### 单独运行某个步骤
每个脚本都可以独立运行，但需要确保前置步骤的输出文件存在。

## 输出结果

### 主要输出文件

#### 栅格数据
- `solar_final_suitable_areas.tif` - 太阳能最终适宜区（含保护区约束）
- `onshore_wind_final_suitable_areas.tif` - 风电最终适宜区（含保护区约束）
- `*_no_protected.tif` - 不考虑保护区约束的对比结果

#### CSV数据
- `suitability_comparison_results.csv` - 网格级详细对比数据（259,200行）
- `country_renewable_capacity.csv` - 国家级容量评估结果
- `suitable_areas_summary.csv` - 适宜区域汇总

#### 可视化
- `final_suitability_map.png` - 全球适宜性地图
- `suitability_comparison_map.png` - 保护区影响对比地图
- `country_capacity_comparison.png` - 国家容量排名图
- `protected_areas_impact.png` - 保护区影响分析图

### 关键统计结果

#### 全球汇总（考虑保护区约束）
- **太阳能可开发容量**: 2,349,910.4 GW
- **陆上风电可开发容量**: 109,812.9 GW
- **保护区对太阳能影响**: 39.8%容量损失
- **保护区对风电影响**: 47.5%容量损失

#### 前五名国家（总容量）
1. 俄罗斯: 264,604.4 GW
2. 加拿大: 255,127.2 GW  
3. 中国: 250,229.6 GW
4. 澳大利亚: 154,740.2 GW
5. 哈萨克斯坦: 129,654.9 GW

## 技术特点

### 数据处理优化
- **内存优化**: 大数据集采用分块处理
- **并行处理**: 多文件批量处理支持
- **错误处理**: 完善的异常捕获和日志记录

### 空间分析精度
- **高精度地形**: 0.05度坡度计算后聚合
- **保守估计**: 使用高百分位数聚合避免低估约束
- **空间一致性**: 所有图层统一投影和分辨率

### 结果验证
- **对比分析**: 提供有无保护区约束的对比
- **多尺度验证**: 从网格到国家的多层次统计
- **可视化验证**: 丰富的地图和图表输出

## 注意事项

1. **内存需求**: 全球0.5度数据需要约8GB内存
2. **计算时间**: 完整流程约需30-60分钟
3. **数据完整性**: 确保所有输入数据文件完整
4. **坐标系统**: 所有数据统一使用WGS84 (EPSG:4326)
5. **NoData处理**: 统一使用-9999.0作为无效值

## 引用参考

如果使用本工具包，请引用相关数据源：
- ESA CCI Land Cover: ESA Climate Change Initiative
- GMTED2010: USGS Global Multi-resolution Terrain Elevation Data
- WDPA: World Database on Protected Areas
- Natural Earth: Natural Earth Country Boundaries

## 更新日志

- 2025-07-20: 初始版本，完整工作流程
- 支持全球0.5度分辨率分析
- 集成多源约束条件
- 提供国家级容量评估

## 联系信息

如有问题或建议，请查看代码注释或联系开发团队。 