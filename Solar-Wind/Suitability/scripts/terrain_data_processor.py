#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
地形数据处理脚本
专门用于处理GMTED2010地形数据，生成全球坡度数据
处理流程：
1. 将GMTED2010数据重采样到0.1度
2. 在0.1度分辨率下计算坡度
3. 将坡度聚合到0.5度（使用90百分位数）
"""

import os
import glob
import numpy as np
from scipy import ndimage
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import rasterio
from rasterio.warp import calculate_default_transform, reproject, Resampling
import warnings
warnings.filterwarnings('ignore')

# 可视化设置（使用英文避免中文显示问题）
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

def setup_directories():
    """设置输出目录"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    base_dir = os.path.dirname(script_dir)
    
    outputs_dir = os.path.join(base_dir, 'outputs')
    visualizations_dir = os.path.join(outputs_dir, 'visualizations')
    processed_dir = os.path.join(outputs_dir, 'processed_data')
    temp_dir = os.path.join(processed_dir, 'temp')  # 临时文件目录
    
    for directory in [visualizations_dir, processed_dir, temp_dir]:
        os.makedirs(directory, exist_ok=True)
    
    return outputs_dir, visualizations_dir, processed_dir, temp_dir

def create_grid(resolution):
    """创建指定分辨率的全球网格"""
    global_bounds = (-180, -90, 180, 90)
    
    # 计算网格维度
    width = int(360 / resolution)
    height = int(180 / resolution)
    
    print(f"目标网格: {width} x {height} 像元，分辨率 {resolution}°")
    
    # 创建坐标数组
    lon = np.linspace(-180 + resolution/2, 180 - resolution/2, width)
    lat = np.linspace(90 - resolution/2, -90 + resolution/2, height)
    
    # 创建变换矩阵
    transform = rasterio.transform.from_bounds(
        global_bounds[0], global_bounds[1], 
        global_bounds[2], global_bounds[3],
        width, height
    )
    
    return lon, lat, transform, width, height

def process_elevation_data(mea_files, temp_dir, resolution=0.05):
    """处理高程数据到指定分辨率"""
    print(f"\n处理高程数据到 {resolution}° 分辨率...")
    
    # 创建目标网格
    lon, lat, transform, width, height = create_grid(resolution)
    
    # 初始化输出数组
    elevation_data = np.full((height, width), -9999.0, dtype=np.float32)
    
    # 处理每个瓦片
    processed_tiles = 0
    for i, mea_file in enumerate(mea_files):
        if i % 10 == 0:
            print(f"正在处理瓦片 {i+1}/{len(mea_files)}")
        
        try:
            with rasterio.open(mea_file) as src:
                # 创建临时数组
                temp_data = np.full((height, width), -9999.0, dtype=np.float32)
                
                # 重投影到临时数组
                reproject(
                    source=rasterio.band(src, 1),
                    destination=temp_data,
                    src_transform=src.transform,
                    src_crs=src.crs,
                    dst_transform=transform,
                    dst_crs='EPSG:4326',
                    resampling=Resampling.bilinear,  # 使用双线性插值以保持地形细节
                    dst_nodata=-9999.0
                )
                
                # 合并到主数组
                valid_mask = temp_data != -9999.0
                elevation_data[valid_mask] = temp_data[valid_mask]
                
                processed_tiles += 1
                
                if i % 10 == 0:
                    valid_count = np.sum(valid_mask)
                    print(f"  瓦片 {os.path.basename(mea_file)} 贡献了 {valid_count:,} 个有效像元")
        
        except Exception as e:
            print(f"错误: 处理文件 {mea_file} 失败: {e}")
    
    print(f"成功处理 {processed_tiles}/{len(mea_files)} 个瓦片")
    
    # 计算全局统计
    valid_pixels = np.sum(elevation_data != -9999.0)
    total_pixels = height * width
    coverage_ratio = (valid_pixels / total_pixels) * 100
    
    print(f"\n全球覆盖统计 ({resolution}°):")
    print(f"  总像元数: {total_pixels:,}")
    print(f"  有效像元数: {valid_pixels:,}")
    print(f"  覆盖率: {coverage_ratio:.2f}%")
    
    # 保存高分辨率高程数据
    elevation_file = os.path.join(temp_dir, f'global_elevation_{resolution:.3f}deg.tif')
    
    profile = {
        'driver': 'GTiff',
        'height': height,
        'width': width,
        'count': 1,
        'dtype': elevation_data.dtype,
        'crs': 'EPSG:4326',
        'transform': transform,
        'nodata': -9999.0,
        'compress': 'lzw'
    }
    
    with rasterio.open(elevation_file, 'w', **profile) as dst:
        dst.write(elevation_data, 1)
    
    print(f"高分辨率高程数据已保存: {elevation_file}")
    
    return elevation_data, elevation_file, transform

def calculate_slope_highres(elevation_data, temp_dir, transform, resolution=0.05):
    """在高分辨率下计算坡度"""
    print(f"\n在 {resolution}° 分辨率下计算坡度...")
    
    height, width = elevation_data.shape
    
    # 创建纬度数组
    latitudes = np.linspace(90 - resolution/2, -90 + resolution/2, height)
    
    # 使用有效值掩码
    valid_mask = elevation_data != -9999.0
    
    # 直接计算梯度，不进行平滑处理
    dy, dx = np.gradient(np.where(valid_mask, elevation_data, np.nan))
    
    # 初始化坡度数组
    slope_degrees = np.full_like(elevation_data, -9999.0)
    
    # 计算每个纬度的实际距离（考虑地球曲率）
    for i in range(height):
        # 计算该纬度下的实际距离（米）
        dx_meters = resolution * 111320 * np.cos(np.radians(latitudes[i]))
        dy_meters = resolution * 111320
        
        # 计算该纬度的坡度
        dz_dx = dx[i] / dx_meters
        dz_dy = dy[i] / dy_meters
        
        slope_radians = np.arctan(np.sqrt(dz_dx**2 + dz_dy**2))
        slope_degrees[i] = np.degrees(slope_radians)
    
    # 处理无效值
    slope_degrees[~valid_mask] = -9999.0
    slope_degrees[np.isnan(slope_degrees)] = -9999.0
    
    # 统计坡度信息
    valid_slope = slope_degrees[slope_degrees != -9999.0]
    print("\n坡度统计:")
    print(f"  有效像元数: {len(valid_slope):,}")
    print(f"  范围: {np.min(valid_slope):.2f}° 到 {np.max(valid_slope):.2f}°")
    print(f"  平均值: {np.mean(valid_slope):.2f}°")
    print(f"  中位数: {np.median(valid_slope):.2f}°")
    print(f"  90百分位数: {np.percentile(valid_slope, 90):.2f}°")
    print(f"  95百分位数: {np.percentile(valid_slope, 95):.2f}°")
    print(f"  99百分位数: {np.percentile(valid_slope, 99):.2f}°")
    print(f"  坡度 ≤ 5°的像元数: {np.sum(valid_slope <= 5.0):,} ({np.sum(valid_slope <= 5.0)/len(valid_slope)*100:.2f}%)")
    print(f"  坡度 ≤ 20°的像元数: {np.sum(valid_slope <= 20.0):,} ({np.sum(valid_slope <= 20.0)/len(valid_slope)*100:.2f}%)")
    
    # 保存高分辨率坡度数据
    slope_file = os.path.join(temp_dir, f'global_slope_{resolution:.3f}deg.tif')
    
    profile = {
        'driver': 'GTiff',
        'height': height,
        'width': width,
        'count': 1,
        'dtype': slope_degrees.dtype,
        'crs': 'EPSG:4326',
        'transform': transform,
        'nodata': -9999.0,
        'compress': 'lzw'
    }
    
    with rasterio.open(slope_file, 'w', **profile) as dst:
        dst.write(slope_degrees, 1)
    
    print(f"高分辨率坡度数据已保存: {slope_file}")
    
    return slope_degrees, slope_file

def aggregate_to_lowres(highres_data, processed_dir, highres_resolution=0.05, lowres_resolution=0.5):
    """将高分辨率数据聚合到低分辨率（使用99百分位数）"""
    print(f"\n将 {highres_resolution}° 数据聚合到 {lowres_resolution}°...")
    
    # 计算聚合因子
    factor = int(lowres_resolution / highres_resolution)
    height, width = highres_data.shape
    new_height = height // factor
    new_width = width // factor
    
    # 创建低分辨率网格
    _, _, transform, _, _ = create_grid(lowres_resolution)
    
    # 初始化输出数组
    lowres_data = np.full((new_height, new_width), -9999.0, dtype=np.float32)
    
    # 对每个低分辨率像元进行聚合
    for i in range(new_height):
        for j in range(new_width):
            # 提取高分辨率窗口
            window = highres_data[i*factor:(i+1)*factor, j*factor:(j+1)*factor]
            valid_values = window[window != -9999.0]
            
            if len(valid_values) > 0:
                # 使用99百分位数进行聚合
                lowres_data[i,j] = np.percentile(valid_values, 99)
    
    # 统计信息
    valid_mask = lowres_data != -9999.0
    valid_data = lowres_data[valid_mask]
    
    print(f"\n{lowres_resolution}° 分辨率统计:")
    print(f"  有效像元数: {len(valid_data):,}")
    print(f"  范围: {np.min(valid_data):.2f}° 到 {np.max(valid_data):.2f}°")
    print(f"  平均值: {np.mean(valid_data):.2f}°")
    print(f"  中位数: {np.median(valid_data):.2f}°")
    print(f"  90百分位数: {np.percentile(valid_data, 90):.2f}°")
    print(f"  95百分位数: {np.percentile(valid_data, 95):.2f}°")
    print(f"  99百分位数: {np.percentile(valid_data, 99):.2f}°")
    print(f"  坡度 ≤ 5°的像元数: {np.sum(valid_data <= 5.0):,} ({np.sum(valid_data <= 5.0)/len(valid_data)*100:.2f}%)")
    print(f"  坡度 ≤ 20°的像元数: {np.sum(valid_data <= 20.0):,} ({np.sum(valid_data <= 20.0)/len(valid_data)*100:.2f}%)")
    
    # 保存低分辨率数据
    lowres_file = os.path.join(processed_dir, f'global_slope_{lowres_resolution:.1f}deg_99percentile.tif')
    
    profile = {
        'driver': 'GTiff',
        'height': new_height,
        'width': new_width,
        'count': 1,
        'dtype': lowres_data.dtype,
        'crs': 'EPSG:4326',
        'transform': transform,
        'nodata': -9999.0,
        'compress': 'lzw'
    }
    
    with rasterio.open(lowres_file, 'w', **profile) as dst:
        dst.write(lowres_data, 1)
    
    print(f"聚合后的坡度数据已保存: {lowres_file}")
    
    return lowres_data, lowres_file

def aggregate_elevation_to_lowres(highres_data, processed_dir, highres_resolution=0.05, lowres_resolution=0.5):
    """将高分辨率高程数据聚合到低分辨率（使用99百分位数）"""
    print(f"\n将高程数据从 {highres_resolution}° 聚合到 {lowres_resolution}°...")
    
    # 计算聚合因子
    factor = int(lowres_resolution / highres_resolution)
    height, width = highres_data.shape
    new_height = height // factor
    new_width = width // factor
    
    # 创建低分辨率网格
    _, _, transform, _, _ = create_grid(lowres_resolution)
    
    # 初始化输出数组
    lowres_data = np.full((new_height, new_width), -9999.0, dtype=np.float32)
    
    # 对每个低分辨率像元进行聚合
    for i in range(new_height):
        for j in range(new_width):
            # 提取高分辨率窗口
            window = highres_data[i*factor:(i+1)*factor, j*factor:(j+1)*factor]
            valid_values = window[window != -9999.0]
            
            if len(valid_values) > 0:
                # 使用99百分位数进行聚合
                lowres_data[i,j] = np.percentile(valid_values, 99)
    
    # 统计信息
    valid_mask = lowres_data != -9999.0
    valid_data = lowres_data[valid_mask]
    
    print(f"\n{lowres_resolution}° 分辨率统计:")
    print(f"  有效像元数: {len(valid_data):,}")
    print(f"  范围: {np.min(valid_data):.1f}m 到 {np.max(valid_data):.1f}m")
    print(f"  平均值: {np.mean(valid_data):.1f}m")
    print(f"  中位数: {np.median(valid_data):.1f}m")
    print(f"  90百分位数: {np.percentile(valid_data, 90):.1f}m")
    print(f"  95百分位数: {np.percentile(valid_data, 95):.1f}m")
    print(f"  99百分位数: {np.percentile(valid_data, 99):.1f}m")
    print(f"  海拔 ≤ 3000m的像元数: {np.sum(valid_data <= 3000.0):,}")
    
    # 保存低分辨率数据
    lowres_file = os.path.join(processed_dir, f'global_elevation_{lowres_resolution:.1f}deg_99percentile.tif')
    
    profile = {
        'driver': 'GTiff',
        'height': new_height,
        'width': new_width,
        'count': 1,
        'dtype': lowres_data.dtype,
        'crs': 'EPSG:4326',
        'transform': transform,
        'nodata': -9999.0,
        'compress': 'lzw'
    }
    
    with rasterio.open(lowres_file, 'w', **profile) as dst:
        dst.write(lowres_data, 1)
    
    print(f"聚合后的高程数据已保存: {lowres_file}")
    
    return lowres_data, lowres_file

def create_visualization(data, title, filename, visualizations_dir, 
                       vmin=0, vmax=None, cmap='YlOrRd'):
    """创建数据可视化"""
    plt.figure(figsize=(20, 10))
    
    # 处理无效值
    masked_data = np.ma.masked_where(data == -9999.0, data)
    
    # 如果没有指定vmax，使用95百分位数
    if vmax is None:
        valid_data = data[data != -9999.0]
        vmax = np.percentile(valid_data, 95)
    
    plt.imshow(masked_data, cmap=cmap, vmin=vmin, vmax=vmax,
              extent=[-180, 180, -90, 90])
    
    plt.colorbar(label='degrees')
    plt.title(title)
    plt.xlabel('Longitude')
    plt.ylabel('Latitude')
    
    plt.tight_layout()
    plt.savefig(os.path.join(visualizations_dir, filename),
               dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"已保存{title}可视化")

def main():
    """主函数"""
    print("地形数据处理与分析")
    print("="*50)
    
    try:
        # 设置路径
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        topo_dir = os.path.join(base_dir, 'data', 'gis', 'topography')
        _, visualizations_dir, processed_dir, temp_dir = setup_directories()
        
        # 检查GMTED数据
        mea_files = []
        for gmted_dir in glob.glob(os.path.join(topo_dir, 'GMTED2010*_300')):
            if os.path.isdir(gmted_dir):
                mea_file = glob.glob(os.path.join(gmted_dir, '*_mea300.tif'))
                if mea_file:
                    mea_files.extend(mea_file)
        
        if not mea_files:
            print("错误: 未找到高程数据文件!")
            return
        
        print(f"找到 {len(mea_files)} 个高程瓦片")
        
        # 1. 处理高程数据到0.05度分辨率
        elevation_data, elevation_file, transform = process_elevation_data(
            mea_files, temp_dir, resolution=0.05
        )
        
        # 2. 在0.05度分辨率下计算坡度
        slope_highres, slope_highres_file = calculate_slope_highres(
            elevation_data, temp_dir, transform, resolution=0.05
        )
        
        # 3. 聚合高程数据到0.5度分辨率
        elevation_lowres, elevation_lowres_file = aggregate_elevation_to_lowres(
            elevation_data, processed_dir,
            highres_resolution=0.05,
            lowres_resolution=0.5
        )
        
        # 4. 聚合坡度数据到0.5度分辨率
        slope_lowres, slope_lowres_file = aggregate_to_lowres(
            slope_highres, processed_dir,
            highres_resolution=0.05,
            lowres_resolution=0.5
        )
        
        # 5. 创建可视化
        create_visualization(
            slope_highres,
            'Global Slope at 0.05° Resolution (degrees)',
            'global_slope_005deg.png',
            visualizations_dir
        )
        
        create_visualization(
            slope_lowres,
            'Global Slope at 0.5° Resolution (99th percentile, degrees)',
            'global_slope_05deg_99percentile.png',
            visualizations_dir
        )
        
        print("\n处理完成!")
        print(f"输出文件:")
        print(f"- 高分辨率高程数据: {elevation_file}")
        print(f"- 最终高程数据: {elevation_lowres_file}")
        print(f"- 高分辨率坡度数据: {slope_highres_file}")
        print(f"- 最终坡度数据: {slope_lowres_file}")
        print(f"- 可视化文件保存在: {visualizations_dir}")
        
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 