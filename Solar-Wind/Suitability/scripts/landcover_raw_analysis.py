#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
C3S-LC-L4-LCCS-Map-300m 土地覆盖数据重采样分析脚本
使用与data_processor相同的重采样方法（众数重采样）
直接生成0.5度分辨率tif文件并统计土地类型
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import xarray as xr
import rasterio
from rasterio.warp import calculate_default_transform, reproject, Resampling
from rasterio.enums import Resampling
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Noto Sans CJK SC']
plt.rcParams['axes.unicode_minus'] = False

def setup_directories():
    """设置输出目录"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    base_dir = os.path.dirname(script_dir)
    
    # 创建输出目录
    outputs_dir = os.path.join(base_dir, 'outputs')
    visualizations_dir = os.path.join(outputs_dir, 'visualizations')
    reports_dir = os.path.join(outputs_dir, 'reports')
    processed_dir = os.path.join(outputs_dir, 'processed_data')
    
    for directory in [outputs_dir, visualizations_dir, reports_dir, processed_dir]:
        os.makedirs(directory, exist_ok=True)
    
    return outputs_dir, visualizations_dir, reports_dir, processed_dir

def load_landcover_data():
    """加载土地覆盖数据"""
    # 数据文件路径
    data_file = os.path.join(
        os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))),
        'Solar-Wind', 'data', 'gis', 'land_cover', 
        'C3S-LC-L4-LCCS-Map-300m-P1Y-2022-v2.1.1.nc'
    )
    
    print(f"正在加载数据文件: {data_file}")
    
    if not os.path.exists(data_file):
        raise FileNotFoundError(f"数据文件不存在: {data_file}")
    
    # 使用xarray加载NetCDF文件
    print("正在读取NetCDF文件...")
    ds = xr.open_dataset(data_file)
    
    # 打印数据集信息
    print("\n=== 数据集基本信息 ===")
    print(f"数据集维度: {dict(ds.dims)}")
    print(f"数据变量: {list(ds.data_vars)}")
    print(f"坐标变量: {list(ds.coords)}")
    
    # 获取土地覆盖数据变量（通常是第一个数据变量）
    data_var_name = list(ds.data_vars)[0]
    print(f"土地覆盖数据变量名: {data_var_name}")
    
    # 提取土地覆盖数据
    landcover_data = ds[data_var_name]
    print(f"数据形状: {landcover_data.shape}")
    print(f"数据类型: {landcover_data.dtype}")
    
    return landcover_data, ds

def create_global_grid():
    """创建全球0.5度网格模板（与data_processor相同）"""
    target_resolution = 0.5
    
    # 全球范围
    global_bounds = (-180, -90, 180, 90)  # (west, south, east, north)
    
    # 计算网格维度
    width = int(360 / target_resolution)  # 720 for 0.5°
    height = int(180 / target_resolution)  # 360 for 0.5°
    
    print(f"目标网格: {width} x {height} 像素，分辨率 {target_resolution}°")
    
    # 创建坐标数组
    lon = np.linspace(-179.75, 179.75, width)  # 像素中心
    lat = np.linspace(89.75, -89.75, height)   # 像素中心 (北到南)
    
    # 创建仿射变换矩阵
    transform = rasterio.transform.from_bounds(
        global_bounds[0], global_bounds[1], 
        global_bounds[2], global_bounds[3],
        width, height
    )
    
    return lon, lat, transform, width, height

def resample_to_half_degree_rasterio(landcover_data, processed_dir):
    """使用rasterio重采样到0.5度分辨率（众数重采样）"""
    print("\n=== 开始重采样到0.5度分辨率（使用众数重采样） ===")
    
    # 如果是3D数组，取第一个时间步
    if landcover_data.ndim == 3:
        lc_data = landcover_data.values[0]
    else:
        lc_data = landcover_data.values
    
    # 获取原始坐标
    lons = landcover_data.lon.values
    lats = landcover_data.lat.values
    
    print(f"原始数据形状: {lc_data.shape}")
    print(f"原始纬度范围: {lats.min():.3f} 到 {lats.max():.3f}")
    print(f"原始经度范围: {lons.min():.3f} 到 {lons.max():.3f}")
    
    # 创建源数据的仿射变换矩阵
    src_transform = rasterio.transform.from_bounds(
        lons.min(), lats.min(), lons.max(), lats.max(),
        len(lons), len(lats)
    )
    
    # 创建目标网格
    lon, lat, dst_transform, width, height = create_global_grid()
    
    # 初始化输出数组
    lc_resampled = np.full((height, width), 0, dtype=np.uint8)
    
    print("正在进行众数重采样...")
    
    # 使用rasterio进行重投影（众数重采样）
    reproject(
        source=lc_data,
        destination=lc_resampled,
        src_transform=src_transform,
        src_crs='EPSG:4326',
        dst_transform=dst_transform,
        dst_crs='EPSG:4326',
        resampling=Resampling.mode,  # 使用众数重采样（与data_processor相同）
        dst_nodata=0
    )
    
    print(f"重采样后数据形状: {lc_resampled.shape}")
    
    # 保存重采样后的数据为tif文件
    output_file = os.path.join(processed_dir, 'global_landcover_05deg_analysis.tif')
    
    profile = {
        'driver': 'GTiff',
        'height': height,
        'width': width,
        'count': 1,
        'dtype': lc_resampled.dtype,
        'crs': 'EPSG:4326',
        'transform': dst_transform,
        'nodata': 0,
        'compress': 'lzw'
    }
    
    with rasterio.open(output_file, 'w', **profile) as dst:
        dst.write(lc_resampled, 1)
    
    print(f"重采样数据已保存为tif文件: {output_file}")
    
    return lc_resampled, output_file

def analyze_landcover_types(landcover_data, data_name="重采样数据"):
    """分析土地覆盖类型"""
    print(f"\n=== 开始分析土地覆盖类型 ({data_name}) ===")

    # 将数据转换为numpy数组进行分析
    print("正在将数据转换为numpy数组...")
    if hasattr(landcover_data, 'values'):
        data_array = landcover_data.values
    else:
        data_array = landcover_data

    # 如果是3D数组，取第一个时间步
    if data_array.ndim == 3:
        data_array = data_array[0]

    # 处理可能的无效值
    print("正在处理无效值...")
    valid_mask = (data_array != 0) & (~np.isnan(data_array))
    valid_data = data_array[valid_mask]

    print(f"总像素数: {data_array.size:,}")
    print(f"有效像素数: {valid_data.size:,}")
    print(f"无效像素数: {data_array.size - valid_data.size:,}")

    # 获取所有唯一的土地覆盖类型
    print("正在统计唯一土地覆盖类型...")
    unique_types = np.unique(valid_data)
    unique_types = unique_types[unique_types != 0]  # 移除0值
    unique_types = unique_types[~np.isnan(unique_types)]  # 移除NaN值
    unique_types = unique_types.astype(int)  # 转换为整数

    print(f"发现的土地覆盖类型数量: {len(unique_types)}")
    print(f"类型范围: {unique_types.min()} - {unique_types.max()}")

    # 统计每种类型的像素数量
    print("正在统计每种类型的像素数量...")
    type_counts = {}
    total_valid_pixels = len(valid_data)

    for land_type in unique_types:
        count = np.sum(valid_data == land_type)
        percentage = (count / total_valid_pixels) * 100
        type_counts[land_type] = {
            'count': count,
            'percentage': percentage
        }

    return unique_types, type_counts, total_valid_pixels

def create_summary_table(unique_types, type_counts, total_pixels, data_name=""):
    """创建统计摘要表"""
    print(f"\n=== 创建统计摘要表 ({data_name}) ===")

    # 创建DataFrame
    summary_data = []
    for land_type in sorted(unique_types):
        summary_data.append({
            '类型编号': land_type,
            '像素数量': type_counts[land_type]['count'],
            '百分比(%)': round(type_counts[land_type]['percentage'], 4)
        })

    df = pd.DataFrame(summary_data)

    # 按像素数量排序
    df_sorted = df.sort_values('像素数量', ascending=False).reset_index(drop=True)

    return df, df_sorted

def print_summary_statistics(df, unique_types, total_pixels, data_name=""):
    """打印统计摘要"""
    print("\n" + "="*60)
    print(f"        C3S 土地覆盖数据统计摘要 ({data_name})")
    print("="*60)
    print(f"总土地覆盖类型数量: {len(unique_types)}")
    print(f"总有效像素数量: {total_pixels:,}")
    print(f"数据完整性: 100.00%")
    print("\n前10种最常见的土地覆盖类型:")
    print("-" * 50)

    df_top10 = df.head(10)
    for idx, row in df_top10.iterrows():
        print(f"{row['类型编号']:>3}: {row['像素数量']:>12,} 像素 ({row['百分比(%)']:>6.2f}%)")

    print("\n所有土地覆盖类型详细列表:")
    print("-" * 50)
    for idx, row in df.iterrows():
        print(f"类型 {row['类型编号']:>3}: {row['像素数量']:>12,} 像素 ({row['百分比(%)']:>6.2f}%)")

def create_visualizations(df, visualizations_dir, data_name=""):
    """创建可视化图表"""
    print(f"\n=== 创建可视化图表 ({data_name}) ===")

    # 1. 土地类型分布直方图
    plt.figure(figsize=(15, 8))

    # 只显示前20种类型以保持图表清晰
    df_top20 = df.head(20)

    bars = plt.bar(range(len(df_top20)), df_top20['像素数量'],
                   color='steelblue', alpha=0.7, edgecolor='black', linewidth=0.5)

    plt.xlabel('土地覆盖类型编号', fontsize=12, fontweight='bold')
    plt.ylabel('像素数量', fontsize=12, fontweight='bold')
    plt.title(f'C3S 土地覆盖类型分布 (前20种) - {data_name}', fontsize=14, fontweight='bold')

    # 设置x轴标签
    plt.xticks(range(len(df_top20)), df_top20['类型编号'], rotation=45)

    # 添加数值标签
    for i, bar in enumerate(bars):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height,
                f'{int(height):,}',
                ha='center', va='bottom', fontsize=8, rotation=90)

    plt.grid(axis='y', alpha=0.3)
    plt.tight_layout()

    hist_file = os.path.join(visualizations_dir, 'landcover_distribution_histogram_resampled.png')
    plt.savefig(hist_file, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"直方图已保存: {hist_file}")

    # 2. 饼图（显示前10种类型）
    plt.figure(figsize=(12, 10))

    df_top10 = df.head(10)
    others_count = df.iloc[10:]['像素数量'].sum() if len(df) > 10 else 0

    # 准备饼图数据
    pie_data = df_top10['像素数量'].tolist()
    pie_labels = [f'类型 {int(x)}' for x in df_top10['类型编号']]

    if others_count > 0:
        pie_data.append(others_count)
        pie_labels.append('其他类型')

    # 创建饼图
    colors = plt.cm.Set3(np.linspace(0, 1, len(pie_data)))
    wedges, texts, autotexts = plt.pie(pie_data, labels=pie_labels, autopct='%1.1f%%',
                                       colors=colors, startangle=90)

    plt.title(f'C3S 土地覆盖类型分布 (前10种) - {data_name}', fontsize=14, fontweight='bold')

    # 调整文本大小
    for text in texts:
        text.set_fontsize(10)
    for autotext in autotexts:
        autotext.set_fontsize(8)
        autotext.set_color('white')
        autotext.set_fontweight('bold')

    plt.axis('equal')

    pie_file = os.path.join(visualizations_dir, 'landcover_distribution_pie_resampled.png')
    plt.savefig(pie_file, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"饼图已保存: {pie_file}")

def save_results(df, df_sorted, reports_dir, tif_file_path, data_name=""):
    """保存分析结果"""
    print(f"\n=== 保存分析结果 ({data_name}) ===")

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 保存详细统计结果
    csv_file = os.path.join(reports_dir, f'landcover_analysis_resampled_{timestamp}.csv')
    df_sorted.to_csv(csv_file, index=False, encoding='utf-8-sig')
    print(f"详细统计结果已保存: {csv_file}")

    # 保存摘要报告
    report_file = os.path.join(reports_dir, f'landcover_summary_resampled_{timestamp}.txt')
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(f"C3S-LC-L4-LCCS-Map-300m 土地覆盖数据分析报告 ({data_name})\n")
        f.write("="*60 + "\n")
        f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"重采样方法: 众数重采样 (Resampling.mode)\n")
        f.write(f"目标分辨率: 0.5度 x 0.5度\n")
        f.write(f"生成tif文件: {tif_file_path}\n")
        f.write(f"总土地覆盖类型数量: {len(df)}\n")
        f.write(f"总有效像素数量: {df['像素数量'].sum():,}\n\n")

        f.write("所有土地覆盖类型统计:\n")
        f.write("-" * 40 + "\n")
        for idx, row in df_sorted.iterrows():
            f.write(f"类型 {row['类型编号']:>3}: {row['像素数量']:>12,} 像素 ({row['百分比(%)']:>6.2f}%)\n")

    print(f"摘要报告已保存: {report_file}")

def main():
    """主函数"""
    print("开始C3S土地覆盖数据重采样分析...")
    print("="*60)

    try:
        # 设置目录
        outputs_dir, visualizations_dir, reports_dir, processed_dir = setup_directories()

        # 加载数据
        landcover_data, dataset = load_landcover_data()

        # ===== 注释掉原始300m数据分析部分 =====
        # print("\n" + "="*60)
        # print("第一部分：分析原始300m分辨率数据 - 已跳过")
        # print("="*60)
        # print("根据要求，跳过300m分辨率数据统计...")

        # ===== 重采样并分析 =====
        print("\n" + "="*60)
        print("使用众数重采样方法重采样到0.5度分辨率并分析")
        print("="*60)

        # 使用rasterio重采样到0.5度分辨率（众数重采样）
        resampled_data, tif_file_path = resample_to_half_degree_rasterio(landcover_data, processed_dir)

        # 分析重采样后的土地覆盖类型
        unique_types_resamp, type_counts_resamp, total_pixels_resamp = analyze_landcover_types(
            resampled_data, "重采样数据(0.5度)"
        )

        # 创建重采样数据统计表
        df_resamp, df_sorted_resamp = create_summary_table(
            unique_types_resamp, type_counts_resamp, total_pixels_resamp, "重采样数据"
        )

        # 打印重采样数据统计摘要
        print_summary_statistics(df_sorted_resamp, unique_types_resamp, total_pixels_resamp, "重采样数据(0.5度)")

        # 创建重采样数据可视化
        create_visualizations(df_sorted_resamp, visualizations_dir, "重采样数据(0.5度)")

        # 保存重采样数据结果
        save_results(df_resamp, df_sorted_resamp, reports_dir, tif_file_path, "重采样数据(0.5度)")

        # 关闭数据集
        dataset.close()

        print("\n" + "="*60)
        print("土地覆盖数据重采样分析完成！")
        print("结果包括：")
        print(f"- 0.5度分辨率tif文件: {tif_file_path}")
        print("- 土地类型统计分析报告和可视化图表")
        print("- 使用与data_processor相同的众数重采样方法")
        print("="*60)

    except Exception as e:
        print(f"分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
