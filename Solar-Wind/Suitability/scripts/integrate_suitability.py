#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
适宜性整合分析脚本
整合土地覆盖筛选结果和地形筛选结果，排除保护区，得到最终的风电和光伏适宜区域
"""

import os
import glob
import numpy as np
import pandas as pd
import geopandas as gpd
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import rasterio
from rasterio.features import rasterize
from rasterio.transform import from_bounds
import warnings
warnings.filterwarnings('ignore')

# 可视化设置（使用英文避免中文显示问题）
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

def setup_directories():
    """设置输出目录"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    base_dir = os.path.dirname(script_dir)
    
    outputs_dir = os.path.join(base_dir, 'outputs')
    processed_dir = os.path.join(outputs_dir, 'processed_data')
    visualizations_dir = os.path.join(outputs_dir, 'visualizations')
    final_dir = os.path.join(outputs_dir, 'final_suitability')
    
    for directory in [processed_dir, visualizations_dir, final_dir]:
        os.makedirs(directory, exist_ok=True)
    
    return processed_dir, visualizations_dir, final_dir

def load_wdpa_data():
    """加载WDPA保护区数据"""
    print("\n加载WDPA保护区数据...")
    
    script_dir = os.path.dirname(os.path.abspath(__file__))
    base_dir = os.path.dirname(os.path.dirname(script_dir))
    wdpa_dir = os.path.join(base_dir, 'data', 'gis', 'protected_areas', 'WDPA_Jul2025_Public_shp')
    
    # 查找polygon文件
    polygon_files = glob.glob(os.path.join(wdpa_dir, '*', '*polygon*.shp'))
    
    if not polygon_files:
        print("警告: 未找到WDPA polygon文件，将不排除保护区")
        return None
    
    print(f"找到 {len(polygon_files)} 个polygon文件")
    
    # 合并所有分片的保护区数据
    valid_status = ['Designated', 'Inscribed', 'Established']
    all_valid_features = []
    
    for file in polygon_files:
        print(f"  处理文件: {os.path.basename(os.path.dirname(file))}/{os.path.basename(file)}")
        try:
            gdf = gpd.read_file(file)
            
            # 筛选有效的保护区
            if 'STATUS' in gdf.columns:
                valid_gdf = gdf[gdf['STATUS'].isin(valid_status)]
                if not valid_gdf.empty:
                    all_valid_features.append(valid_gdf)
                    
        except Exception as e:
            print(f"    错误: {str(e)}")
            continue
    
    if not all_valid_features:
        print("警告: 未找到有效的保护区数据")
        return None
    
    # 合并所有分片数据
    protected_gdf = gpd.GeoDataFrame(pd.concat(all_valid_features, ignore_index=True))
    print(f"合并后的保护区数量: {len(protected_gdf):,}")
    
    return protected_gdf

def create_protected_mask(protected_gdf, template_file):
    """创建保护区掩码"""
    print("\n创建保护区掩码...")
    
    if protected_gdf is None:
        print("无保护区数据，返回空掩码")
        with rasterio.open(template_file) as src:
            return np.zeros(src.shape, dtype=np.uint8)
    
    # 读取模板栅格的参数
    with rasterio.open(template_file) as src:
        height, width = src.shape
        transform = src.transform
        bounds = src.bounds
    
    print(f"栅格参数: {width}x{height}, 范围: {bounds}")
    
    # 创建保护区的几何图形列表
    shapes = [(geom, 1) for geom in protected_gdf.geometry if geom is not None and geom.is_valid]
    print(f"有效几何图形数量: {len(shapes)}")
    
    # 栅格化保护区
    protected_mask = rasterize(
        shapes,
        out_shape=(height, width),
        transform=transform,
        fill=0,
        dtype=np.uint8,
        all_touched=True
    )
    
    protected_pixels = np.sum(protected_mask == 1)
    total_pixels = height * width
    print(f"保护区像元数: {protected_pixels:,} ({protected_pixels/total_pixels*100:.2f}%)")
    
    return protected_mask

def load_and_check_data(processed_dir):
    """加载并检查输入数据"""
    print("\n加载输入数据...")
    
    # 定义输入文件
    files = {
        'solar_landcover': 'solar_suitable_areas_05deg_updated.tif',
        'wind_landcover': 'wind_suitable_areas_05deg_updated.tif',
        'slope': 'global_slope_0.5deg_99percentile.tif',
        'elevation': 'global_elevation_0.5deg_99percentile.tif',
        'protected': 'protected_areas_05deg.tif'  # 新生成的保护区栅格文件
    }
    
    data = {}
    transform = None
    
    # 加载数据
    for key, filename in files.items():
        filepath = os.path.join(processed_dir, filename)
        if not os.path.exists(filepath):
            print(f"错误: 未找到文件 {filepath}")
            return None, None
        
        with rasterio.open(filepath) as src:
            data[key] = src.read(1)
            if transform is None:
                transform = src.transform
            
            print(f"\n{key} 数据统计:")
            if key == 'protected':
                # 保护区数据使用255作为nodata值
                valid_mask = data[key] != 255
                protected_pixels = np.sum(data[key][valid_mask] == 1)
                print(f"  形状: {data[key].shape}")
                print(f"  有效像元数: {np.sum(valid_mask):,}")
                print(f"  保护区像元数: {protected_pixels:,}")
                print(f"  保护区占比: {protected_pixels/np.sum(valid_mask)*100:.2f}%")
                # 将nodata值转换为0（非保护区）
                data[key] = np.where(data[key] == 255, 0, data[key])
            else:
                valid_mask = data[key] != -9999.0
                valid_data = data[key][valid_mask]
                print(f"  形状: {data[key].shape}")
                print(f"  有效像元数: {len(valid_data):,}")
                if key == 'slope':
                    print(f"  范围: {np.min(valid_data):.2f}° 到 {np.max(valid_data):.2f}°")
                    print(f"  坡度 ≤ 5°的像元数: {np.sum(valid_data <= 5.0):,}")
                    print(f"  坡度 ≤ 20°的像元数: {np.sum(valid_data <= 20.0):,}")
                elif key == 'elevation':
                    print(f"  范围: {np.min(valid_data):.1f}m 到 {np.max(valid_data):.1f}m")
                    print(f"  海拔 ≤ 3000m的像元数: {np.sum(valid_data <= 3000.0):,}")
    
    return data, transform

def apply_terrain_constraints(data):
    """应用地形和保护区约束"""
    print("\n应用地形和保护区约束...")
    
    # 创建地形掩码
    solar_slope_mask = data['slope'] <= 5.0  # 坡度 ≤ 5° 适合太阳能
    wind_slope_mask = data['slope'] <= 20.0  # 坡度 ≤ 20° 适合风电
    wind_elevation_mask = data['elevation'] <= 3000.0  # 海拔 ≤ 3000m 适合风电
    
    # 创建保护区掩码（1表示保护区，需要取反）
    protected_mask = data['protected'] != 1
    
    # 应用约束
    solar_suitable = np.where(
        (data['solar_landcover'] > 0) & 
        (data['slope'] != -9999.0),
        (solar_slope_mask & protected_mask).astype(np.float32),
        -9999.0
    )
    
    wind_suitable = np.where(
        (data['wind_landcover'] > 0) & 
        (data['slope'] != -9999.0) & 
        (data['elevation'] != -9999.0),
        (wind_slope_mask & wind_elevation_mask & protected_mask).astype(np.float32),
        -9999.0
    )
    
    # 打印调试信息
    print("\n调试信息:")
    print(f"  原始太阳能适宜像元数: {np.sum(data['solar_landcover'] > 0):,}")
    print(f"  原始风电适宜像元数: {np.sum(data['wind_landcover'] > 0):,}")
    print(f"  坡度≤5°的像元数: {np.sum(solar_slope_mask):,}")
    print(f"  坡度≤20°的像元数: {np.sum(wind_slope_mask):,}")
    print(f"  海拔≤3000m的像元数: {np.sum(wind_elevation_mask):,}")
    print(f"  非保护区像元数: {np.sum(protected_mask):,}")
    print(f"  最终太阳能适宜像元数: {np.sum(solar_suitable > 0):,}")
    print(f"  最终风电适宜像元数: {np.sum(wind_suitable > 0):,}")
    print(f"\n排除原因统计:")
    print(f"  太阳能:")
    print(f"    - 坡度>5°: {np.sum((data['solar_landcover'] > 0) & ~solar_slope_mask):,}像元")
    print(f"    - 位于保护区: {np.sum((data['solar_landcover'] > 0) & ~protected_mask):,}像元")
    print(f"  风电:")
    print(f"    - 坡度>20°: {np.sum((data['wind_landcover'] > 0) & ~wind_slope_mask):,}像元")
    print(f"    - 海拔>3000m: {np.sum((data['wind_landcover'] > 0) & ~wind_elevation_mask):,}像元")
    print(f"    - 位于保护区: {np.sum((data['wind_landcover'] > 0) & ~protected_mask):,}像元")
    
    return solar_suitable, wind_suitable

def apply_terrain_constraints_no_protected(data):
    """应用地形约束，但不排除保护区"""
    print("\n应用地形约束（不考虑保护区）...")
    
    # 创建地形掩码
    solar_slope_mask = data['slope'] <= 5.0  # 坡度 ≤ 5° 适合太阳能
    wind_slope_mask = data['slope'] <= 20.0  # 坡度 ≤ 20° 适合风电
    wind_elevation_mask = data['elevation'] <= 3000.0  # 海拔 ≤ 3000m 适合风电
    
    # 应用约束（不考虑保护区）
    solar_suitable_no_protected = np.where(
        (data['solar_landcover'] > 0) & 
        (data['slope'] != -9999.0),
        solar_slope_mask.astype(np.float32),
        -9999.0
    )
    
    wind_suitable_no_protected = np.where(
        (data['wind_landcover'] > 0) & 
        (data['slope'] != -9999.0) & 
        (data['elevation'] != -9999.0),
        (wind_slope_mask & wind_elevation_mask).astype(np.float32),
        -9999.0
    )
    
    # 打印调试信息
    print("\n调试信息（不考虑保护区）:")
    print(f"  原始太阳能适宜像元数: {np.sum(data['solar_landcover'] > 0):,}")
    print(f"  原始风电适宜像元数: {np.sum(data['wind_landcover'] > 0):,}")
    print(f"  坡度≤5°的像元数: {np.sum(solar_slope_mask):,}")
    print(f"  坡度≤20°的像元数: {np.sum(wind_slope_mask):,}")
    print(f"  海拔≤3000m的像元数: {np.sum(wind_elevation_mask):,}")
    print(f"  最终太阳能适宜像元数: {np.sum(solar_suitable_no_protected > 0):,}")
    print(f"  最终风电适宜像元数: {np.sum(wind_suitable_no_protected > 0):,}")
    
    return solar_suitable_no_protected, wind_suitable_no_protected

def analyze_results(solar_suitable, wind_suitable):
    """分析最终结果"""
    print("\n分析最终适宜性结果...")
    
    # 计算有效陆地面积（任一数据集中的有效像元）
    total_land = np.sum(
        (solar_suitable != -9999.0) | (wind_suitable != -9999.0)
    )
    
    # 太阳能统计
    solar_valid = solar_suitable > 0
    solar_pixels = np.sum(solar_valid)
    solar_ratio = solar_pixels / total_land * 100 if total_land > 0 else 0
    
    print("\n太阳能适宜区统计:")
    print(f"  有效像元数: {solar_pixels:,}")
    print(f"  占总陆地面积比例: {solar_ratio:.2f}%")
    
    # 风电统计
    wind_valid = wind_suitable > 0
    wind_pixels = np.sum(wind_valid)
    wind_ratio = wind_pixels / total_land * 100 if total_land > 0 else 0
    
    print("\n风电适宜区统计:")
    print(f"  有效像元数: {wind_pixels:,}")
    print(f"  占总陆地面积比例: {wind_ratio:.2f}%")
    
    # 重叠区域统计
    overlap = np.sum((solar_valid) & (wind_valid))
    overlap_ratio = overlap / total_land * 100 if total_land > 0 else 0
    
    print("\n重叠区域统计:")
    print(f"  重叠像元数: {overlap:,}")
    print(f"  占总陆地面积比例: {overlap_ratio:.2f}%")
    
    return {
        'solar_pixels': solar_pixels,
        'wind_pixels': wind_pixels,
        'overlap': overlap,
        'total_pixels': total_land
    }

def save_results(solar_suitable, wind_suitable, transform, final_dir):
    """保存最终结果"""
    print("\n保存最终结果...")
    
    height, width = solar_suitable.shape
    profile = {
        'driver': 'GTiff',
        'height': height,
        'width': width,
        'count': 1,
        'dtype': solar_suitable.dtype,
        'crs': 'EPSG:4326',
        'transform': transform,
        'nodata': -9999.0,
        'compress': 'lzw'
    }
    
    # 保存太阳能适宜区
    solar_file = os.path.join(final_dir, 'solar_final_suitable_areas.tif')
    with rasterio.open(solar_file, 'w', **profile) as dst:
        dst.write(solar_suitable, 1)
    print(f"太阳能适宜区已保存: {solar_file}")
    
    # 保存陆上风电适宜区
    wind_file = os.path.join(final_dir, 'onshore_wind_final_suitable_areas.tif')
    with rasterio.open(wind_file, 'w', **profile) as dst:
        dst.write(wind_suitable, 1)
    print(f"陆上风电适宜区已保存: {wind_file}")
    
    return solar_file, wind_file

def save_csv_results(solar_suitable, wind_suitable, data, transform, final_dir):
    """保存CSV格式的详细结果"""
    print("\n保存CSV格式的详细结果...")
    
    # 获取栅格参数
    height, width = solar_suitable.shape
    
    # 创建结果列表
    results = []
    
    print("正在处理每个网格...")
    for i in range(height):
        for j in range(width):
            # 将像素坐标转换为地理坐标
            lon, lat = rasterio.transform.xy(transform, i, j)
            
            # 检查是否为有效陆地像元
            if (data['solar_landcover'][i, j] != -9999.0 or 
                data['wind_landcover'][i, j] != -9999.0):
                
                # 获取各项数据
                solar_landcover = data['solar_landcover'][i, j] if data['solar_landcover'][i, j] != -9999.0 else 0
                wind_landcover = data['wind_landcover'][i, j] if data['wind_landcover'][i, j] != -9999.0 else 0
                slope = data['slope'][i, j] if data['slope'][i, j] != -9999.0 else None
                elevation = data['elevation'][i, j] if data['elevation'][i, j] != -9999.0 else None
                protected = data['protected'][i, j]
                
                # 判断适宜性
                solar_suitable_flag = solar_suitable[i, j] > 0
                wind_suitable_flag = wind_suitable[i, j] > 0
                
                # 确定适宜类型
                if solar_suitable_flag and wind_suitable_flag:
                    suitability_type = "Both"
                elif solar_suitable_flag:
                    suitability_type = "Solar Only"
                elif wind_suitable_flag:
                    suitability_type = "Wind Only"
                else:
                    suitability_type = "Unsuitable"
                
                # 添加到结果列表
                results.append({
                    'Grid_ID': f"{i}_{j}",
                    'Longitude': lon,
                    'Latitude': lat,
                    'Solar_Landcover_Suitable': solar_landcover > 0,
                    'Wind_Landcover_Suitable': wind_landcover > 0,
                    'Slope_degrees': slope,
                    'Elevation_m': elevation,
                    'Protected_Area': protected == 1,
                    'Solar_Suitable': solar_suitable_flag,
                    'Wind_Suitable': wind_suitable_flag,
                    'Suitability_Type': suitability_type,
                    'Slope_Constraint_Solar': slope <= 5.0 if slope is not None else None,
                    'Slope_Constraint_Wind': slope <= 20.0 if slope is not None else None,
                    'Elevation_Constraint_Wind': elevation <= 3000.0 if elevation is not None else None
                })
    
    # 创建DataFrame并保存为CSV
    import pandas as pd
    df = pd.DataFrame(results)
    
    # 保存所有网格的详细信息
    csv_file = os.path.join(final_dir, 'suitability_detailed_results.csv')
    df.to_csv(csv_file, index=False)
    print(f"详细结果已保存: {csv_file}")
    print(f"总网格数: {len(df):,}")
    
    # 保存仅适宜区域的汇总信息
    suitable_df = df[df['Suitability_Type'] != 'Unsuitable'].copy()
    if not suitable_df.empty:
        suitable_csv_file = os.path.join(final_dir, 'suitable_areas_summary.csv')
        suitable_df.to_csv(suitable_csv_file, index=False)
        print(f"适宜区域汇总已保存: {suitable_csv_file}")
        print(f"适宜网格数: {len(suitable_df):,}")
        
        # 按适宜类型统计
        type_counts = suitable_df['Suitability_Type'].value_counts()
        print("\n适宜区域统计:")
        for type_name, count in type_counts.items():
            print(f"  {type_name}: {count:,} 个网格")
    
    return csv_file

def save_csv_results_comparison(solar_suitable, wind_suitable, solar_no_protected, wind_no_protected, data, transform, final_dir):
    """保存CSV格式的对比结果"""
    print("\n保存CSV格式的对比结果...")
    
    # 获取栅格参数
    height, width = solar_suitable.shape
    
    # 创建结果列表
    results = []
    
    print("正在处理每个网格...")
    for i in range(height):
        for j in range(width):
            # 将像素坐标转换为地理坐标
            lon, lat = rasterio.transform.xy(transform, i, j)
            
            # 检查是否为有效陆地像元
            if (data['solar_landcover'][i, j] != -9999.0 or 
                data['wind_landcover'][i, j] != -9999.0):
                
                # 获取各项数据
                solar_landcover = data['solar_landcover'][i, j] if data['solar_landcover'][i, j] != -9999.0 else 0
                wind_landcover = data['wind_landcover'][i, j] if data['wind_landcover'][i, j] != -9999.0 else 0
                slope = data['slope'][i, j] if data['slope'][i, j] != -9999.0 else None
                elevation = data['elevation'][i, j] if data['elevation'][i, j] != -9999.0 else None
                protected = data['protected'][i, j]
                
                # 判断适宜性（考虑保护区）
                solar_suitable_flag = solar_suitable[i, j] > 0
                wind_suitable_flag = wind_suitable[i, j] > 0
                
                # 判断适宜性（不考虑保护区）
                solar_no_protected_flag = solar_no_protected[i, j] > 0
                wind_no_protected_flag = wind_no_protected[i, j] > 0
                
                # 确定适宜类型（考虑保护区）
                if solar_suitable_flag and wind_suitable_flag:
                    suitability_type_protected = "Both"
                elif solar_suitable_flag:
                    suitability_type_protected = "Solar Only"
                elif wind_suitable_flag:
                    suitability_type_protected = "Wind Only"
                else:
                    suitability_type_protected = "Unsuitable"
                
                # 确定适宜类型（不考虑保护区）
                if solar_no_protected_flag and wind_no_protected_flag:
                    suitability_type_no_protected = "Both"
                elif solar_no_protected_flag:
                    suitability_type_no_protected = "Solar Only"
                elif wind_no_protected_flag:
                    suitability_type_no_protected = "Wind Only"
                else:
                    suitability_type_no_protected = "Unsuitable"
                
                # 添加到结果列表
                results.append({
                    'Grid_ID': f"{i}_{j}",
                    'Longitude': lon,
                    'Latitude': lat,
                    'Solar_Landcover_Suitable': solar_landcover > 0,
                    'Wind_Landcover_Suitable': wind_landcover > 0,
                    'Slope_degrees': slope,
                    'Elevation_m': elevation,
                    'Protected_Area': protected == 1,
                    # 考虑保护区的结果
                    'Solar_Suitable_With_Protected': solar_suitable_flag,
                    'Wind_Suitable_With_Protected': wind_suitable_flag,
                    'Suitability_Type_With_Protected': suitability_type_protected,
                    # 不考虑保护区的结果
                    'Solar_Suitable_No_Protected': solar_no_protected_flag,
                    'Wind_Suitable_No_Protected': wind_no_protected_flag,
                    'Suitability_Type_No_Protected': suitability_type_no_protected,
                    # 约束条件
                    'Slope_Constraint_Solar': slope <= 5.0 if slope is not None else None,
                    'Slope_Constraint_Wind': slope <= 20.0 if slope is not None else None,
                    'Elevation_Constraint_Wind': elevation <= 3000.0 if elevation is not None else None,
                    # 保护区影响
                    'Protected_Area_Impact_Solar': solar_no_protected_flag and not solar_suitable_flag,
                    'Protected_Area_Impact_Wind': wind_no_protected_flag and not wind_suitable_flag
                })
    
    # 创建DataFrame并保存为CSV
    import pandas as pd
    df = pd.DataFrame(results)
    
    # 保存完整对比结果
    comparison_csv_file = os.path.join(final_dir, 'suitability_comparison_results.csv')
    df.to_csv(comparison_csv_file, index=False)
    print(f"对比结果已保存: {comparison_csv_file}")
    print(f"总网格数: {len(df):,}")
    
    # 统计保护区影响
    protected_impact_solar = df['Protected_Area_Impact_Solar'].sum()
    protected_impact_wind = df['Protected_Area_Impact_Wind'].sum()
    
    print(f"\n保护区影响统计:")
    print(f"  太阳能：因保护区约束失去 {protected_impact_solar:,} 个适宜网格")
    print(f"  风电：因保护区约束失去 {protected_impact_wind:,} 个适宜网格")
    
    # 保存不考虑保护区的适宜区域汇总
    no_protected_suitable_df = df[df['Suitability_Type_No_Protected'] != 'Unsuitable'].copy()
    if not no_protected_suitable_df.empty:
        no_protected_csv_file = os.path.join(final_dir, 'suitable_areas_no_protected.csv')
        no_protected_suitable_df.to_csv(no_protected_csv_file, index=False)
        print(f"不考虑保护区的适宜区域已保存: {no_protected_csv_file}")
        
        # 按适宜类型统计
        type_counts_no_protected = no_protected_suitable_df['Suitability_Type_No_Protected'].value_counts()
        print("\n不考虑保护区的适宜区域统计:")
        for type_name, count in type_counts_no_protected.items():
            print(f"  {type_name}: {count:,} 个网格")
    
    return comparison_csv_file

def create_visualization(solar_suitable, wind_suitable, stats, visualizations_dir):
    """创建最终适宜性可视化"""
    print("\n创建可视化...")
    
    # 创建组合掩码（0=不适宜, 1=仅太阳能, 2=仅风电, 3=两者都适宜）
    combined_mask = np.full_like(solar_suitable, 0)
    combined_mask[solar_suitable > 0] = 1
    combined_mask[wind_suitable > 0] += 2
    
    # 创建自定义颜色映射
    colors = ['white', 'yellow', 'blue', 'green']
    labels = ['Unsuitable', 'Solar Only', 'Onshore Wind Only', 'Both Suitable']
    
    plt.figure(figsize=(20, 10))
    
    # 绘制地图
    plt.imshow(combined_mask, cmap=matplotlib.colors.ListedColormap(colors),
              extent=[-180, 180, -90, 90])
    
    # 添加图例
    patches = [plt.Rectangle((0,0),1,1, facecolor=color) for color in colors]
    plt.legend(patches, labels, loc='upper right', title='Suitability')
    
    # 添加标题和统计信息
    title = 'Renewable Energy Suitability Map (With Protected Areas Excluded)\n'
    title += f'Solar: {stats["solar_pixels"]:,} pixels ({stats["solar_pixels"]/stats["total_pixels"]*100:.1f}% of land)\n'
    title += f'Onshore Wind: {stats["wind_pixels"]:,} pixels ({stats["wind_pixels"]/stats["total_pixels"]*100:.1f}% of land)\n'
    title += f'Overlap: {stats["overlap"]:,} pixels ({stats["overlap"]/stats["total_pixels"]*100:.1f}% of land)'
    
    plt.title(title)
    plt.xlabel('Longitude')
    plt.ylabel('Latitude')
    
    # 添加网格
    plt.grid(True, linestyle='--', alpha=0.5)
    
    # 保存图片
    vis_file = os.path.join(visualizations_dir, 'final_suitability_map.png')
    plt.savefig(vis_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"可视化已保存: {vis_file}")
    return vis_file

def create_comparison_visualization(solar_suitable, wind_suitable, solar_no_protected, wind_no_protected, visualizations_dir):
    """创建对比可视化"""
    print("\n创建对比可视化...")
    
    # 创建组合掩码（考虑保护区）
    combined_mask_protected = np.full_like(solar_suitable, 0)
    combined_mask_protected[solar_suitable > 0] = 1
    combined_mask_protected[wind_suitable > 0] += 2
    
    # 创建组合掩码（不考虑保护区）
    combined_mask_no_protected = np.full_like(solar_no_protected, 0)
    combined_mask_no_protected[solar_no_protected > 0] = 1
    combined_mask_no_protected[wind_no_protected > 0] += 2
    
    # 创建自定义颜色映射
    colors = ['white', 'yellow', 'blue', 'green']
    labels = ['Unsuitable', 'Solar Only', 'Wind Only', 'Both Suitable']
    
    # 创建对比图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(24, 10))
    
    # 左图：考虑保护区约束
    im1 = ax1.imshow(combined_mask_protected, cmap=matplotlib.colors.ListedColormap(colors),
                     extent=[-180, 180, -90, 90])
    ax1.set_title('Renewable Energy Suitability\n(With Protected Areas Excluded)')
    ax1.set_xlabel('Longitude')
    ax1.set_ylabel('Latitude')
    ax1.grid(True, linestyle='--', alpha=0.5)
    
    # 右图：不考虑保护区约束
    im2 = ax2.imshow(combined_mask_no_protected, cmap=matplotlib.colors.ListedColormap(colors),
                     extent=[-180, 180, -90, 90])
    ax2.set_title('Renewable Energy Suitability\n(Without Protected Areas Constraint)')
    ax2.set_xlabel('Longitude')
    ax2.set_ylabel('Latitude')
    ax2.grid(True, linestyle='--', alpha=0.5)
    
    # 添加共同图例
    patches = [plt.Rectangle((0,0),1,1, facecolor=color) for color in colors]
    fig.legend(patches, labels, loc='upper center', bbox_to_anchor=(0.5, 0.95), 
               ncol=4, title='Suitability')
    
    # 调整布局
    plt.tight_layout()
    plt.subplots_adjust(top=0.85)
    
    # 保存图片
    comparison_vis_file = os.path.join(visualizations_dir, 'suitability_comparison_map.png')
    plt.savefig(comparison_vis_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"对比可视化已保存: {comparison_vis_file}")
    return comparison_vis_file

def main():
    """主函数"""
    print("风光资源适宜性整合分析")
    print("="*50)
    
    try:
        # 设置目录
        processed_dir, visualizations_dir, final_dir = setup_directories()
        
        # 加载数据
        data, transform = load_and_check_data(processed_dir)
        if data is None:
            return
        
        # 应用地形约束（考虑保护区）
        solar_suitable, wind_suitable = apply_terrain_constraints(data)
        
        # 应用地形约束（不考虑保护区）
        solar_no_protected, wind_no_protected = apply_terrain_constraints_no_protected(data)
        
        # 分析结果（考虑保护区）
        stats = analyze_results(solar_suitable, wind_suitable)
        
        # 分析结果（不考虑保护区）
        print("\n分析不考虑保护区的适宜性结果...")
        total_land = np.sum(
            (solar_no_protected != -9999.0) | (wind_no_protected != -9999.0)
        )
        solar_no_protected_pixels = np.sum(solar_no_protected > 0)
        wind_no_protected_pixels = np.sum(wind_no_protected > 0)
        overlap_no_protected = np.sum((solar_no_protected > 0) & (wind_no_protected > 0))
        
        print(f"\n不考虑保护区的适宜区统计:")
        print(f"  太阳能适宜像元数: {solar_no_protected_pixels:,} ({solar_no_protected_pixels/total_land*100:.2f}%)")
        print(f"  风电适宜像元数: {wind_no_protected_pixels:,} ({wind_no_protected_pixels/total_land*100:.2f}%)")
        print(f"  重叠像元数: {overlap_no_protected:,} ({overlap_no_protected/total_land*100:.2f}%)")
        
        # 保存结果
        solar_file, wind_file = save_results(
            solar_suitable, wind_suitable, transform, final_dir
        )
        
        # 保存不考虑保护区的结果
        solar_no_protected_file = os.path.join(final_dir, 'solar_final_suitable_areas_no_protected.tif')
        wind_no_protected_file = os.path.join(final_dir, 'onshore_wind_final_suitable_areas_no_protected.tif')
        
        height, width = solar_no_protected.shape
        profile = {
            'driver': 'GTiff',
            'height': height,
            'width': width,
            'count': 1,
            'dtype': solar_no_protected.dtype,
            'crs': 'EPSG:4326',
            'transform': transform,
            'nodata': -9999.0,
            'compress': 'lzw'
        }
        
        with rasterio.open(solar_no_protected_file, 'w', **profile) as dst:
            dst.write(solar_no_protected, 1)
        
        with rasterio.open(wind_no_protected_file, 'w', **profile) as dst:
            dst.write(wind_no_protected, 1)
        
        print(f"不考虑保护区的太阳能适宜区已保存: {solar_no_protected_file}")
        print(f"不考虑保护区的风电适宜区已保存: {wind_no_protected_file}")
        
        # 保存CSV格式的对比结果
        comparison_csv_file = save_csv_results_comparison(
            solar_suitable, wind_suitable, solar_no_protected, wind_no_protected, 
            data, transform, final_dir
        )
        
        # 创建可视化
        vis_file = create_visualization(
            solar_suitable, wind_suitable, stats, visualizations_dir
        )
        
        # 创建对比可视化
        comparison_vis_file = create_comparison_visualization(
            solar_suitable, wind_suitable, solar_no_protected, wind_no_protected, 
            visualizations_dir
        )
        
        print("\n处理完成!")
        print(f"输出文件:")
        print(f"- 太阳能适宜区（含保护区约束）: {solar_file}")
        print(f"- 风电适宜区（含保护区约束）: {wind_file}")
        print(f"- 太阳能适宜区（不含保护区约束）: {solar_no_protected_file}")
        print(f"- 风电适宜区（不含保护区约束）: {wind_no_protected_file}")
        print(f"- 对比结果CSV: {comparison_csv_file}")
        print(f"- 可视化文件: {vis_file}")
        print(f"- 对比可视化文件: {comparison_vis_file}")
        
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 