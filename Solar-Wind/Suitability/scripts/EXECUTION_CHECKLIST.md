# 脚本执行清单

## 快速开始

### 环境准备
```bash
# 1. 激活conda环境
conda activate solar-wind

# 2. 验证环境
python -c "import geopandas, rasterio, xarray; print('环境正常')"

# 3. 进入工作目录
cd Solar-Wind/Suitability/scripts
```

### 数据准备检查
- [ ] ESA CCI土地覆盖数据: `../data/landcover/ESACCI-LC-L4-LCCS-Map-300m-P1Y-2020-v2.1.1.nc`
- [ ] GMTED2010地形数据: `../data/terrain/GMTED2010_*.nc`
- [ ] WDPA保护区数据: `../data/gis/protected_areas/WDPA_Jul2025_Public_shp/`
- [ ] 国家边界数据: `../data/gis/countries/ne_50m_admin_0_countries/`

## 完整执行流程

### 步骤1: 土地覆盖分析 (预计10-15分钟)
```bash
# 原始数据分析和重采样
python landcover_raw_analysis.py
```
**输出检查**:
- [ ] `../outputs/processed_data/landcover_05deg_resampled.tif`
- [ ] 控制台显示土地类型统计

```bash
# 重分类和适宜性评估
python landcover_reclassification_visualization.py
```
**输出检查**:
- [ ] `../outputs/processed_data/solar_suitable_areas_05deg_updated.tif`
- [ ] `../outputs/processed_data/wind_suitable_areas_05deg_updated.tif`
- [ ] `../outputs/visualizations/` 中的可视化文件

### 步骤2: 地形数据处理 (预计15-20分钟)
```bash
python terrain_data_processor.py
```
**输出检查**:
- [ ] `../outputs/processed_data/global_elevation_0.5deg_99percentile.tif`
- [ ] `../outputs/processed_data/global_slope_0.5deg_99percentile.tif`
- [ ] 控制台显示地形统计信息

### 步骤3: 保护区处理 (预计10-15分钟)
```bash
python protected_areas_processor.py
```
**输出检查**:
- [ ] `../outputs/processed_data/protected_areas_05deg.tif`
- [ ] `../outputs/visualizations/protected_areas_global_map.png`
- [ ] 控制台显示保护区统计

### 步骤4: 综合适宜性分析 (预计5-10分钟)
```bash
python integrate_suitability.py
```
**输出检查**:
- [ ] `../outputs/final_suitability/solar_final_suitable_areas.tif`
- [ ] `../outputs/final_suitability/onshore_wind_final_suitable_areas.tif`
- [ ] `../outputs/final_suitability/solar_final_suitable_areas_no_protected.tif`
- [ ] `../outputs/final_suitability/onshore_wind_final_suitable_areas_no_protected.tif`
- [ ] `../outputs/final_suitability/suitability_comparison_results.csv`
- [ ] `../outputs/visualizations/final_suitability_map.png`
- [ ] `../outputs/visualizations/suitability_comparison_map.png`

### 步骤5: 国家容量评估 (预计10-15分钟)
```bash
python country_capacity_analysis.py
```
**输出检查**:
- [ ] `../outputs/country_analysis/country_renewable_capacity.csv`
- [ ] `../outputs/country_analysis/country_renewable_capacity_main.csv`
- [ ] `../outputs/country_analysis/country_capacity_comparison.png`
- [ ] `../outputs/country_analysis/protected_areas_impact.png`

## 关键统计验证

### 全球汇总数据验证
运行完成后，检查最终输出的全球统计：

**预期结果范围**:
- 太阳能总容量: 2,300,000 - 2,400,000 GW
- 风电总容量: 100,000 - 120,000 GW  
- 保护区影响: 35-45%的容量损失

**前五名国家应包含**:
1. 俄罗斯
2. 加拿大
3. 中国
4. 澳大利亚
5. 哈萨克斯坦

### 数据完整性检查
```bash
# 检查输出文件大小
ls -lh ../outputs/final_suitability/*.tif
ls -lh ../outputs/country_analysis/*.csv

# 验证CSV文件行数
wc -l ../outputs/final_suitability/suitability_comparison_results.csv
# 应显示约259,200行（含标题行）

wc -l ../outputs/country_analysis/country_renewable_capacity.csv  
# 应显示约240行（含标题行）
```

## 常见错误排查

### 错误1: 内存不足
**症状**: `MemoryError`或进程被杀死
**解决**: 
- 关闭其他占用内存的应用
- 使用更小的处理块大小
- 考虑分步骤运行，不连续执行

### 错误2: 文件路径错误
**症状**: `FileNotFoundError`
**解决**:
```bash
# 检查数据文件是否存在
find ../data -name "*.nc" -o -name "*.shp" | head -10
```

### 错误3: 环境包缺失
**症状**: `ModuleNotFoundError`
**解决**:
```bash
# 重新安装缺失的包
conda install -c conda-forge geopandas rasterio xarray
```

### 错误4: 磁盘空间不足
**症状**: 写入错误或进程中断
**解决**:
```bash
# 检查磁盘空间
df -h .
# 清理临时文件
rm -rf ../outputs/temp_*
```

## 性能监控

### 实时监控脚本性能
在另一个终端中运行：
```bash
# 监控内存使用
watch -n 5 'ps aux | grep python | grep -v grep'

# 监控磁盘空间
watch -n 10 'df -h .'
```

### 预期执行时间
- **总计**: 50-75分钟
- **最耗时步骤**: terrain_data_processor.py (15-20分钟)
- **最快步骤**: integrate_suitability.py (5-10分钟)

## 结果快速验证

### 运行完成后执行快速检查
```bash
# 检查所有输出文件是否生成
python -c "
import os
files_to_check = [
    '../outputs/final_suitability/solar_final_suitable_areas.tif',
    '../outputs/final_suitability/onshore_wind_final_suitable_areas.tif',
    '../outputs/final_suitability/suitability_comparison_results.csv',
    '../outputs/country_analysis/country_renewable_capacity.csv'
]
missing = [f for f in files_to_check if not os.path.exists(f)]
if missing:
    print('缺失文件:', missing)
else:
    print('所有关键文件已生成✓')
"
```

### 数据合理性检查
```bash
# 检查CSV文件格式
head -n 3 ../outputs/country_analysis/country_renewable_capacity_main.csv

# 查看前10名国家
python -c "
import pandas as pd
df = pd.read_csv('../outputs/country_analysis/country_renewable_capacity_main.csv')
print('前10名国家:')
print(df[['Country_Name', 'Solar_Protected_Capacity_GW', 'Wind_Protected_Capacity_GW']].head(10))
"
```

## 结果文件说明

### 主要输出文件用途

#### 栅格文件 (.tif)
- **适宜性地图**: 用于GIS软件进一步分析
- **保护区掩码**: 可用于其他环境评估
- **地形数据**: 可重复使用于其他项目

#### CSV数据文件
- **网格级数据**: 详细的空间分析和统计
- **国家级数据**: 政策制定和投资决策支持
- **对比数据**: 保护区政策影响评估

#### 可视化文件 (.png)
- **全球地图**: 报告和演示使用
- **对比图表**: 政策影响可视化
- **国家排名**: 投资机会识别

## 后续分析建议

### 进一步分析方向
1. **经济性评估**: 结合LCOE数据进行经济可行性分析
2. **电网接入**: 考虑输电线路距离和容量约束
3. **资源质量**: 集成太阳辐射和风速数据进行精细化评估
4. **时间序列**: 分析多年数据变化趋势
5. **情景分析**: 不同约束条件下的敏感性分析

### 数据更新建议
- **年度更新**: 土地覆盖和保护区数据
- **技术更新**: 容量密度参数和约束条件
- **分辨率提升**: 考虑使用更高分辨率数据（0.25度或更高）

---

**完成标志**: 所有步骤执行完成且通过验证后，您将获得一套完整的全球风光资源适宜性评估结果，可用于进一步的能源规划和决策分析。 