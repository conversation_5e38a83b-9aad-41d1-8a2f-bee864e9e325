# 土地覆盖重分类与可视化技术文档
## Land Cover Reclassification and Visualization Technical Documentation

### 脚本概述 | Script Overview

**脚本名称**: `landcover_reclassification_visualization.py`  
**主要功能**: 对土地覆盖数据进行重分类处理，筛选适合可再生能源开发的区域，并生成可视化产品  
**在工作流程中的作用**: 基于Wang et al.方法论筛选太阳能和风能适宜的土地类型

### 方法论依据 | Methodological Foundation

本脚本基于以下研究方法论：
- **重分类策略**: 将非10倍数的土地类型四舍五入到最近的10倍数
- **适宜性标准**: 基于Wang et al. (Nature Communications 2025)的土地类型排除标准
- **可视化方法**: 使用CCI LC标准颜色配置进行全球土地覆盖可视化
- **筛选原则**: 保守的适宜性评估，优先保护生态敏感区域

### 技术实现细节 | Technical Implementation Details

#### 输入数据源和格式 | Input Data Sources and Formats

1. **输入文件**
   - 文件名: `global_landcover_05deg_analysis.tif`
   - 来源: 土地覆盖原始数据分析模块
   - 分辨率: 0.5° × 0.5°
   - 数据类型: uint8
   - 坐标系: WGS84 (EPSG:4326)

#### 核心算法和处理逻辑 | Core Algorithms and Processing Logic

1. **重分类算法**
   ```python
   def reclassify_landcover(data):
       for value in original_unique:
           if value != 0:
               new_value = round(value / 10) * 10
               reclassified_data[data == value] = new_value
   ```

2. **适宜性筛选算法**
   ```python
   # 太阳能适宜性筛选
   solar_excluded = [210, 180, 220, 50, 60, 70, 80, 90, 100, 10, 20, 30, 160, 170]
   suitable_mask = ~np.isin(data, solar_excluded) & (data != 0)
   
   # 风电适宜性筛选
   wind_excluded = [210, 190, 220, 50, 60, 70, 80, 90, 100, 160, 170]
   suitable_mask = ~np.isin(data, wind_excluded) & (data != 0)
   ```

3. **适宜性比例计算**
   ```python
   suitability_ratio = (suitable_pixels / total_valid_pixels) * 100
   ```

#### 关键参数设置 | Key Parameter Settings

| 参数 | 值 | 说明 |
|------|----|----|
| 重分类规则 | 四舍五入到10倍数 | 简化土地类型分类 |
| 太阳能排除类型 | 12个类型 | 基于Wang et al.标准 |
| 风电排除类型 | 11个类型 | 基于Wang et al.标准 |
| 可视化分辨率 | 300 DPI | 学术出版质量 |
| 颜色映射 | CCI LC标准 | 国际标准颜色配置 |

### 土地类型适宜性标准 | Land Type Suitability Criteria

#### 太阳能发电排除类型 | Solar PV Excluded Types

| 类型编码 | 类型名称 | 排除原因 |
|----------|----------|----------|
| 210 | 水体 | 技术不可行 |
| 180 | 洪泛植被 | 环境敏感 |
| 220 | 永久冰雪 | 技术不可行 |
| 50-100 | 各类森林 | 生态保护 |
| 10-30 | 农田类型 | 粮食安全 |
| 160-170 | 洪泛森林 | 环境敏感 |

#### 陆上风电排除类型 | Onshore Wind Excluded Types

| 类型编码 | 类型名称 | 排除原因 |
|----------|----------|----------|
| 210 | 水体 | 技术不可行 |
| 190 | 城市区域 | 社会约束 |
| 220 | 永久冰雪 | 技术不可行 |
| 50-100 | 各类森林 | 生态保护 |
| 160-170 | 洪泛森林 | 环境敏感 |

#### 适宜类型分析 | Suitable Types Analysis

**太阳能适宜类型**: 40, 110, 120, 130, 140, 150, 190, 200  
**风电适宜类型**: 10, 20, 30, 40, 110, 120, 130, 140, 150, 200

### 数据处理流程 | Data Processing Workflow

#### 第一阶段：数据重分类
1. **原始数据加载**: 读取0.5°分辨率土地覆盖数据
2. **唯一值统计**: 识别所有存在的土地覆盖类型
3. **重分类执行**: 将非10倍数类型归并到最近的10倍数
4. **重分类验证**: 检查重分类前后的类型变化

#### 第二阶段：适宜性筛选
1. **排除标准应用**: 根据Wang et al.标准排除不适宜类型
2. **适宜区域识别**: 生成太阳能和风电适宜性掩码
3. **统计计算**: 计算各技术的适宜性比例
4. **结果验证**: 检查适宜性结果的合理性

#### 第三阶段：可视化生成
1. **全球土地覆盖地图**: 使用CCI LC标准颜色
2. **适宜性地图**: 分别生成太阳能和风电适宜性地图
3. **图例和标注**: 添加完整的图例和统计信息
4. **质量检查**: 验证可视化产品的准确性

### 输出结果格式 | Output Result Formats

#### 主要输出文件
1. **global_landcover_05deg_reclassified_updated.tif**
   - 内容: 重分类后的全球土地覆盖数据
   - 分辨率: 0.5° × 0.5°
   - 数据类型: uint8

2. **solar_suitable_areas_05deg_updated.tif**
   - 内容: 太阳能适宜区域掩码
   - 适宜性比例: 通常15-25%

3. **wind_suitable_areas_05deg_updated.tif**
   - 内容: 风电适宜区域掩码
   - 适宜性比例: 通常25-35%

#### 可视化产品
1. **Global_Land_Cover_Distribution_Reclassified_updated.png**
   - 全球土地覆盖分布图
   - 使用CCI LC标准颜色配置

2. **Solar_suitability_map_updated.png**
   - 太阳能适宜性分布图
   - 绿色系列表示适宜程度

3. **Onshore_Wind_suitability_map_updated.png**
   - 风电适宜性分布图
   - 绿色系列表示适宜程度

### 颜色配置标准 | Color Configuration Standards

#### CCI LC标准颜色映射 | CCI LC Standard Color Mapping

```python
LAND_COVER_COLORS = {
    0: (0, 0, 0),           # No Data - Black
    10: (255, 255, 100),    # Cropland, rainfed - Yellow
    20: (170, 240, 240),    # Cropland, irrigated - Light Blue
    30: (220, 240, 100),    # Mosaic cropland - Light Yellow-Green
    40: (200, 200, 100),    # Mosaic natural vegetation - Olive
    50: (0, 100, 0),        # Tree cover, broadleaved, evergreen - Dark Green
    # ... 更多颜色配置
    210: (0, 70, 200),      # Water bodies - Blue
    220: (255, 255, 255),   # Permanent snow and ice - White
}
```

### 质量控制措施 | Quality Control Measures

#### 重分类验证
1. **类型数量检查**: 验证重分类后类型数量的合理性
2. **面积保守性**: 确保重分类不显著改变总面积
3. **空间连续性**: 检查重分类后的空间连续性

#### 适宜性验证
1. **比例合理性**: 验证适宜性比例是否在预期范围内
2. **地理分布**: 检查适宜区域的地理分布合理性
3. **技术差异**: 验证太阳能和风电适宜性的差异

#### 可视化质量控制
1. **颜色准确性**: 确保颜色映射与标准一致
2. **图例完整性**: 验证图例信息的完整性和准确性
3. **分辨率质量**: 确保输出图像质量满足要求

### 技术创新点 | Technical Innovations

1. **智能重分类**: 自动化的土地类型归并策略
2. **标准化颜色**: 采用国际标准的CCI LC颜色配置
3. **多技术适宜性**: 同时评估太阳能和风电适宜性
4. **集成可视化**: 一体化的数据处理和可视化流程

### 计算性能 | Computational Performance

#### 性能指标
- **处理时间**: 约10-20分钟
- **内存需求**: 峰值约2-4GB RAM
- **存储需求**: 输出文件约100-200MB
- **CPU利用率**: 中等强度

#### 性能优化策略
1. **向量化操作**: 使用NumPy向量化提高效率
2. **内存管理**: 及时释放不需要的数组
3. **批量处理**: 批量生成多个可视化产品
4. **压缩存储**: 使用适当的压缩方法

### 误差分析 | Error Analysis

#### 主要误差来源
1. **重分类误差**: 四舍五入可能改变原始分类意义
2. **适宜性标准**: 排除标准的主观性
3. **可视化误差**: 颜色映射的视觉偏差
4. **分辨率限制**: 0.5°分辨率的空间精度限制

#### 误差控制措施
1. **保守重分类**: 采用保守的重分类策略
2. **标准化方法**: 使用已发表的适宜性标准
3. **质量检查**: 多层次的结果验证
4. **文档记录**: 详细记录处理假设和参数

### 应用限制 | Application Limitations

1. **分类简化**: 重分类可能丢失原始分类的细节信息
2. **静态评估**: 基于单一时间点的静态适宜性评估
3. **技术假设**: 基于当前技术水平的适宜性标准
4. **区域差异**: 未考虑不同区域的具体条件差异

### 结果解释指南 | Result Interpretation Guidelines

#### 适宜性比例解释
- **太阳能适宜性**: 15-25%为合理范围
- **风电适宜性**: 25-35%为合理范围
- **重叠区域**: 约10-15%的区域同时适合两种技术

#### 地理分布特征
- **太阳能**: 主要分布在干旱半干旱地区
- **风电**: 分布相对广泛，包括草地和部分农田
- **排除区域**: 森林、水体、城市和农田被合理排除

### 与其他模块的接口 | Interface with Other Modules

#### 输入依赖
- `global_landcover_05deg_analysis.tif`: 来自土地覆盖原始分析模块

#### 输出提供
- 重分类土地覆盖数据: 供适宜性整合模块使用
- 适宜性掩码数据: 供后续地形约束处理使用
- 可视化产品: 供报告和展示使用

#### 数据格式兼容性
- 保持与其他模块相同的坐标系和分辨率标准
- 确保数据类型和无效值处理的一致性
