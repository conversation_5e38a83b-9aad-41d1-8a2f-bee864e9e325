# 地形数据处理技术文档
## Terrain Data Processing Technical Documentation

### 脚本概述 | Script Overview

**脚本名称**: `terrain_data_processor.py`  
**主要功能**: 处理GMTED2010全球地形数据，生成高精度坡度数据用于可再生能源适宜性评估  
**在工作流程中的作用**: 为太阳能和风能适宜性筛选提供地形约束条件

### 方法论依据 | Methodological Foundation

本脚本基于以下研究方法论：
- **数据源**: GMTED2010 (Global Multi-resolution Terrain Elevation Data 2010)，分辨率300m
- **坡度计算方法**: 基于数字高程模型(DEM)的梯度计算方法
- **聚合策略**: 采用99百分位数聚合方法，保留地形复杂性信息
- **参考文献**: <PERSON> et al. (Nature Communications 2025) 中的地形约束方法

### 技术实现细节 | Technical Implementation Details

#### 输入数据源和格式 | Input Data Sources and Formats

1. **GMTED2010数据**
   - 格式: GeoTIFF (.tif)
   - 分辨率: 300m (7.5弧秒)
   - 坐标系: WGS84 (EPSG:4326)
   - 数据类型: 16位有符号整数
   - 无效值: -9999

2. **数据组织结构**
   ```
   data/gis/topography/
   ├── GMTED2010_*_300/
   │   └── *_mea300.tif
   ```

#### 核心算法和计算公式 | Core Algorithms and Formulas

1. **坡度计算公式**
   ```
   slope = arctan(√((∂z/∂x)² + (∂z/∂y)²))
   ```
   其中：
   - z: 高程值 (米)
   - ∂z/∂x, ∂z/∂y: x和y方向的高程梯度
   - slope: 坡度 (度)

2. **地理距离校正**
   ```
   dx_meters = resolution × 111320 × cos(latitude)
   dy_meters = resolution × 111320
   ```
   其中：
   - 111320: 纬度1度对应的米数
   - cos(latitude): 纬度校正因子

3. **99百分位数聚合**
   ```
   lowres_value = percentile(highres_window, 99)
   ```

#### 关键参数设置 | Key Parameter Settings

| 参数 | 值 | 说明 |
|------|----|----|
| 高分辨率处理分辨率 | 0.05° | 中间处理分辨率，约5.5km |
| 最终输出分辨率 | 0.5° | 目标分辨率，约55km |
| 聚合方法 | 99百分位数 | 保留地形复杂性 |
| 重采样方法 | 双线性插值 | 保持地形细节 |
| 太阳能坡度阈值 | ≤5° | 基于Wang et al.方法 |
| 风电坡度阈值 | ≤20° | 基于Wang et al.方法 |

### 数据处理流程 | Data Processing Workflow

#### 第一阶段：高程数据预处理
1. **瓦片数据读取**: 逐个读取GMTED2010瓦片文件
2. **坐标系统一**: 确保所有数据使用WGS84坐标系
3. **重采样到0.05°**: 使用双线性插值方法
4. **数据合并**: 将所有瓦片合并为全球连续数据集

#### 第二阶段：坡度计算
1. **梯度计算**: 使用numpy.gradient计算x、y方向梯度
2. **地理校正**: 根据纬度调整实际地理距离
3. **坡度转换**: 将梯度值转换为度数单位
4. **质量控制**: 处理无效值和异常值

#### 第三阶段：数据聚合
1. **窗口提取**: 从0.05°数据中提取10×10像素窗口
2. **统计聚合**: 计算每个窗口的99百分位数
3. **输出生成**: 生成0.5°分辨率的最终产品

### 输出结果格式 | Output Result Formats

#### 主要输出文件
1. **global_elevation_0.5deg_99percentile.tif**
   - 内容: 全球高程数据（99百分位数）
   - 分辨率: 0.5° × 0.5°
   - 单位: 米 (m)

2. **global_slope_0.5deg_99percentile.tif**
   - 内容: 全球坡度数据（99百分位数）
   - 分辨率: 0.5° × 0.5°
   - 单位: 度 (°)

#### 数据质量指标
- **覆盖率**: >95% 全球陆地覆盖
- **精度**: 基于GMTED2010的6m RMSE精度
- **完整性**: 保留原始数据的地形特征

### 质量控制措施 | Quality Control Measures

#### 数据验证方法
1. **统计检查**: 验证高程和坡度值的合理性范围
2. **空间连续性**: 检查相邻像素间的连续性
3. **极值检测**: 识别和处理异常高值或低值
4. **覆盖率验证**: 确保全球陆地区域的完整覆盖

#### 结果检查机制
1. **坡度分布验证**: 
   - 坡度≤5°像素比例应合理（通常15-25%）
   - 坡度≤20°像素比例应合理（通常60-80%）
2. **地理合理性**: 验证山区、平原等地形特征的正确表达
3. **数据一致性**: 确保高程和坡度数据的逻辑一致性

### 可视化说明 | Visualization Description

#### 生成的可视化产品
1. **global_slope_005deg.png**: 0.05°分辨率坡度分布图
2. **global_slope_05deg_99percentile.png**: 0.5°分辨率坡度分布图

#### 可视化特征
- **颜色映射**: YlOrRd (黄-橙-红)色彩方案
- **数值范围**: 0-95百分位数动态范围
- **地理投影**: 等经纬度投影
- **分辨率**: 300 DPI高质量输出

### 技术创新点 | Technical Innovations

1. **多尺度处理策略**: 采用0.05°中间分辨率平衡计算效率和精度
2. **99百分位数聚合**: 相比平均值更好地保留地形复杂性
3. **纬度校正算法**: 精确考虑地球曲率对距离计算的影响
4. **内存优化处理**: 分批处理大数据集，避免内存溢出

### 计算性能 | Computational Performance

- **处理时间**: 约2-4小时（取决于硬件配置）
- **内存需求**: 峰值约8-16GB RAM
- **存储需求**: 临时文件约20-30GB，最终输出约500MB
- **并行化**: 支持瓦片级并行处理

### 误差分析 | Error Analysis

#### 主要误差来源
1. **原始数据误差**: GMTED2010固有的6m RMSE误差
2. **重采样误差**: 双线性插值引入的平滑效应
3. **聚合误差**: 99百分位数可能高估局部坡度
4. **投影误差**: 等经纬度投影在极地区域的变形

#### 误差控制措施
1. **高质量数据源**: 使用国际标准的GMTED2010数据
2. **适当的重采样方法**: 双线性插值保持地形细节
3. **统计验证**: 与已知地形特征进行对比验证
4. **文档记录**: 详细记录处理参数和假设条件

### 应用限制 | Application Limitations

1. **分辨率限制**: 0.5°分辨率无法捕捉小尺度地形变化
2. **时间快照**: 基于2010年数据，未考虑地形变化
3. **极地精度**: 高纬度地区投影变形较大
4. **海洋区域**: 仅处理陆地区域，海洋区域为无效值

### 后续处理建议 | Recommendations for Further Processing

1. **数据更新**: 定期使用最新DEM数据更新
2. **分辨率优化**: 根据应用需求调整输出分辨率
3. **区域细化**: 对重点区域进行高分辨率处理
4. **多源融合**: 结合其他地形数据源提高精度
