# 按国家聚合容量分析技术文档
## Country-Level Capacity Aggregation Analysis Technical Documentation

### 脚本概述 | Script Overview

**脚本名称**: `country_capacity_analysis.py`  
**主要功能**: 基于最终适宜性结果，按国家边界聚合计算可再生能源可开发容量  
**在工作流程中的作用**: 将网格级适宜性结果转换为国家级可开发容量统计

### 方法论依据 | Methodological Foundation

本脚本基于以下研究方法论：
- **容量密度方法**: 基于Wang et al.和相关文献的容量密度参数
- **国家边界聚合**: 使用Natural Earth数据的标准国家边界
- **栅格化统计**: 基于栅格掩码的精确面积计算方法
- **对比分析**: 评估保护区约束对国家级容量的影响

### 技术实现细节 | Technical Implementation Details

#### 输入数据源和格式 | Input Data Sources and Formats

1. **适宜性数据**
   - `solar_final_suitable_areas.tif`: 考虑保护区的太阳能适宜区域
   - `onshore_wind_final_suitable_areas.tif`: 考虑保护区的风电适宜区域
   - `solar_final_suitable_areas_no_protected.tif`: 不考虑保护区的太阳能适宜区域
   - `onshore_wind_final_suitable_areas_no_protected.tif`: 不考虑保护区的风电适宜区域

2. **国家边界数据**
   - 数据源: Natural Earth 50m分辨率国家边界
   - 文件: `ne_50m_admin_0_countries.shp`
   - 坐标系: WGS84 (EPSG:4326)

#### 核心算法和计算公式 | Core Algorithms and Formulas

1. **像元面积计算**
   ```python
   # 0.5度像元面积计算
   lat_meters_per_degree = 111320  # 纬度1度对应米数
   lon_meters_per_degree = 111320 * np.cos(np.radians(45))  # 45度平均纬度
   pixel_area_m2 = (0.5 * lon_meters_per_degree) * (0.5 * lat_meters_per_degree)
   pixel_area_km2 = pixel_area_m2 / 1000000
   ```

2. **容量计算公式**
   ```python
   # 太阳能容量计算 (74 W/m²)
   solar_capacity_mw = suitable_area_m2 * 74 / 1000000
   
   # 风电容量计算 (2.7 MW/km²)
   wind_capacity_mw = suitable_area_km2 * 2.7
   ```

3. **国家掩码生成**
   ```python
   country_mask = rasterize(
       shapes=[(country.geometry, 1)],
       out_shape=(height, width),
       transform=transform,
       fill=0,
       dtype=np.uint8,
       all_touched=True
   )
   ```

#### 关键参数设置 | Key Parameter Settings

| 参数类型 | 参数值 | 单位 | 依据来源 |
|----------|--------|------|----------|
| 太阳能容量密度 | 74 | W/m² | 国际可再生能源署(IRENA)标准 |
| 风电容量密度 | 2.7 | MW/km² | 陆上风电行业平均水平 |
| 像元分辨率 | 0.5° × 0.5° | 度 | 与适宜性数据保持一致 |
| 栅格化方法 | all_touched=True | - | 保守的国家边界识别 |

### 数据处理流程 | Data Processing Workflow

#### 第一阶段：数据准备和验证
1. **适宜性数据加载**: 读取四种适宜性场景的栅格数据
2. **国家边界加载**: 读取Natural Earth国家边界矢量数据
3. **坐标系统一**: 确保所有数据使用WGS84坐标系
4. **数据完整性检查**: 验证数据的空间范围和属性完整性

#### 第二阶段：国家掩码生成
1. **逐国处理**: 为每个国家生成独立的栅格掩码
2. **几何验证**: 检查国家几何图形的有效性
3. **栅格化处理**: 将矢量边界转换为栅格掩码
4. **质量控制**: 验证掩码生成的准确性

#### 第三阶段：容量计算和聚合
1. **像元面积计算**: 基于0.5°分辨率计算标准像元面积
2. **适宜面积统计**: 计算每个国家的适宜像元数量
3. **容量转换**: 基于容量密度参数计算可开发容量
4. **多场景对比**: 生成考虑和不考虑保护区的对比结果

### 输出结果格式 | Output Result Formats

#### 主要CSV输出文件
1. **country_renewable_capacity.csv** (完整结果)
   - 包含所有国家的详细容量数据
   - 字段包括: 国家代码、国家名称、总陆地面积、各场景容量等

2. **country_renewable_capacity_main.csv** (主要结果)
   - 筛选有意义容量的国家 (>0.1 GW)
   - 按总容量排序，便于分析

#### 关键输出字段 | Key Output Fields

| 字段名 | 数据类型 | 单位 | 说明 |
|--------|----------|------|------|
| Country_Code | String | - | ISO 3166-1 alpha-3国家代码 |
| Country_Name | String | - | 国家名称 |
| Total_Land_Area_km2 | Float | km² | 总陆地面积 |
| Solar_Protected_Capacity_GW | Float | GW | 考虑保护区的太阳能容量 |
| Wind_Protected_Capacity_GW | Float | GW | 考虑保护区的风电容量 |
| Solar_No_Protected_Capacity_GW | Float | GW | 不考虑保护区的太阳能容量 |
| Wind_No_Protected_Capacity_GW | Float | GW | 不考虑保护区的风电容量 |
| Solar_Protected_Area_km2 | Float | km² | 太阳能适宜面积 |
| Wind_Protected_Area_km2 | Float | km² | 风电适宜面积 |
| Solar_Protected_Area_Ratio | Float | % | 太阳能适宜面积占比 |
| Wind_Protected_Area_Ratio | Float | % | 风电适宜面积占比 |

#### 可视化产品
1. **country_capacity_comparison.png**: 前20名国家容量对比图
2. **protected_areas_impact.png**: 保护区约束影响分析图

### 容量密度参数说明 | Capacity Density Parameters

#### 太阳能容量密度 (74 W/m²)
1. **参数来源**: 基于IRENA和IEA的全球太阳能统计数据
2. **技术假设**: 
   - 考虑现代硅基太阳能板效率 (18-22%)
   - 包含阵列间距和系统损失
   - 适用于大规模地面光伏电站

3. **地理适用性**: 全球平均水平，未考虑太阳辐射差异

#### 风电容量密度 (2.7 MW/km²)
1. **参数来源**: 基于全球陆上风电项目统计数据
2. **技术假设**:
   - 现代大型风机 (2-3 MW单机容量)
   - 考虑风机间距要求 (5-10倍叶轮直径)
   - 适用于商业化风电场开发

3. **地理适用性**: 全球平均水平，未考虑风资源差异

### 质量控制措施 | Quality Control Measures

#### 数据验证方法
1. **面积一致性检查**
   ```python
   # 验证国家总面积的合理性
   area_ratio = calculated_area / reference_area
   if not (0.8 <= area_ratio <= 1.2):
       print(f"Warning: Area mismatch for {country_name}")
   ```

2. **容量合理性检查**
   - 验证容量密度计算的正确性
   - 检查异常高值或低值国家
   - 对比已知的国家可再生能源潜力数据

3. **地理分布验证**
   - 检查适宜性分布的地理合理性
   - 验证沙漠、草原等地区的高适宜性
   - 确认森林、水体等地区的低适宜性

#### 统计质量指标
1. **覆盖率检查**: 确保所有主要国家都被正确处理
2. **数据完整性**: 验证关键字段的非空性
3. **数值范围**: 检查容量和面积数值的合理范围

### 可视化说明 | Visualization Description

#### 容量对比图特征
1. **图表类型**: 分组柱状图，显示前20名国家
2. **对比维度**: 
   - 太阳能: 考虑vs不考虑保护区
   - 风电: 考虑vs不考虑保护区
3. **颜色方案**: 
   - 橙色系: 太阳能容量
   - 蓝色系: 风电容量
   - 透明度区分: 保护区约束影响

#### 保护区影响图特征
1. **图表类型**: 水平柱状图
2. **显示内容**: 保护区约束导致的容量损失百分比
3. **排序方式**: 按总容量排序的前20名国家
4. **颜色编码**: 橙色(太阳能)和蓝色(风电)

### 技术创新点 | Technical Innovations

1. **高效栅格化**: 优化的国家边界栅格化算法
2. **多场景对比**: 系统性的保护区影响评估
3. **自动化统计**: 集成的容量计算和统计分析
4. **可视化集成**: 一体化的数据分析和可视化流程

### 计算性能 | Computational Performance

#### 性能指标
- **处理时间**: 约30-60分钟 (取决于国家数量)
- **内存需求**: 峰值约4-8GB RAM
- **存储需求**: 输出文件约10-50MB
- **CPU利用率**: 中等强度，主要为几何运算

#### 性能优化策略
1. **分批处理**: 每50个国家输出一次进度信息
2. **内存管理**: 及时释放大型掩码数组
3. **几何优化**: 预先验证和简化复杂几何图形
4. **并行化潜力**: 可优化为国家级并行处理

### 误差分析 | Error Analysis

#### 主要误差来源
1. **栅格化误差**: 矢量到栅格转换的精度损失
2. **容量密度误差**: 全球平均参数的地区差异
3. **边界精度**: 国家边界数据的精度限制
4. **分辨率限制**: 0.5°分辨率的空间精度约束

#### 误差控制措施
1. **高质量边界数据**: 使用Natural Earth标准数据
2. **保守栅格化**: 使用all_touched确保完整覆盖
3. **参数验证**: 使用国际标准的容量密度参数
4. **结果验证**: 与已知统计数据进行对比验证

### 结果解释和应用 | Result Interpretation and Applications

#### 全球容量分布特征
1. **太阳能潜力**: 主要集中在澳大利亚、中国、美国等大国
2. **风电潜力**: 分布相对均匀，包括欧洲、北美、亚洲等地区
3. **保护区影响**: 平均减少20-40%的可开发容量

#### 政策应用价值
1. **国家规划**: 为各国可再生能源规划提供基础数据
2. **国际合作**: 识别可再生能源合作潜力大的国家
3. **投资指导**: 为可再生能源投资提供参考依据

### 应用限制 | Application Limitations

1. **技术假设**: 基于当前技术水平，未考虑技术进步
2. **经济因素**: 未考虑开发成本、电网接入等经济约束
3. **社会因素**: 未考虑土地权属、社会接受度等因素
4. **动态变化**: 基于静态数据，未考虑土地利用变化

### 验证和校准建议 | Validation and Calibration Recommendations

1. **国家级验证**: 与各国官方可再生能源潜力评估对比
2. **区域校准**: 针对重点区域进行高分辨率验证
3. **参数更新**: 定期更新容量密度参数
4. **方法改进**: 结合更多约束条件提高评估精度

### 与其他模块的接口 | Interface with Other Modules

#### 输入依赖
- 适宜性整合模块: 最终适宜性栅格数据
- 外部数据: Natural Earth国家边界数据

#### 输出提供
- 国家级容量统计: 供政策分析和规划使用
- 可视化产品: 供报告和展示使用
- 详细数据表: 供进一步分析和建模使用

#### 数据标准
- 国家编码: ISO 3166-1 alpha-3标准
- 容量单位: 吉瓦 (GW)
- 面积单位: 平方公里 (km²)
- 数据格式: CSV和PNG
