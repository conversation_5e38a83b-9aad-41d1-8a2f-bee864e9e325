# 土地覆盖原始数据分析技术文档
## Land Cover Raw Data Analysis Technical Documentation

### 脚本概述 | Script Overview

**脚本名称**: `landcover_raw_analysis.py`  
**主要功能**: 处理C3S-LC-L4-LCCS-Map-300m土地覆盖数据，重采样至0.5°分辨率并进行统计分析  
**在工作流程中的作用**: 为可再生能源适宜性评估提供标准化的土地覆盖基础数据

### 方法论依据 | Methodological Foundation

本脚本基于以下研究方法论：
- **数据源**: Copernicus Climate Change Service (C3S) Land Cover数据
- **重采样方法**: 众数重采样 (Mode resampling)，保持土地类型的离散特性
- **分类标准**: Land Cover Classification System (LCCS) 标准
- **参考文献**: 基于Zheng Nature paper和Wang et al.的土地覆盖处理方法

### 技术实现细节 | Technical Implementation Details

#### 输入数据源和格式 | Input Data Sources and Formats

1. **C3S土地覆盖数据**
   - 文件名: `C3S-LC-L4-LCCS-Map-300m-P1Y-2022-v2.1.1.nc`
   - 格式: NetCDF (.nc)
   - 原始分辨率: 300m
   - 坐标系: WGS84 (EPSG:4326)
   - 时间范围: 2022年年度数据
   - 数据类型: 8位无符号整数

2. **数据变量结构**
   ```
   Dimensions: (time, lat, lon)
   Variables:
   - lccs_class: 土地覆盖分类 (主要数据变量)
   - lat: 纬度坐标
   - lon: 经度坐标
   - time: 时间维度
   ```

#### 核心算法和计算公式 | Core Algorithms and Formulas

1. **全球网格创建**
   ```python
   # 0.5度分辨率全球网格
   width = int(360 / 0.5)  # 720像素
   height = int(180 / 0.5)  # 360像素
   
   # 像素中心坐标
   lon = np.linspace(-179.75, 179.75, width)
   lat = np.linspace(89.75, -89.75, height)
   ```

2. **众数重采样算法**
   ```python
   reproject(
       source=source_data,
       destination=target_data,
       src_transform=src_transform,
       dst_transform=dst_transform,
       resampling=Resampling.mode  # 众数重采样
   )
   ```

3. **统计分析公式**
   ```python
   percentage = (pixel_count / total_valid_pixels) * 100
   ```

#### 关键参数设置 | Key Parameter Settings

| 参数 | 值 | 说明 |
|------|----|----|
| 目标分辨率 | 0.5° × 0.5° | 与其他数据层保持一致 |
| 重采样方法 | Mode (众数) | 保持土地类型的离散特性 |
| 输出网格尺寸 | 720 × 360 像素 | 全球覆盖 |
| 无效值处理 | 0 | 表示无数据区域 |
| 数据类型 | uint8 | 内存效率优化 |
| 压缩方式 | LZW | 减少文件大小 |

### 数据处理流程 | Data Processing Workflow

#### 第一阶段：数据加载和检查
1. **NetCDF文件读取**: 使用xarray加载多维数据
2. **数据结构分析**: 检查维度、变量和属性信息
3. **坐标系验证**: 确认地理坐标的正确性
4. **时间维度处理**: 提取2022年数据（如为3D数组取第一个时间步）

#### 第二阶段：重采样处理
1. **源数据变换矩阵创建**: 基于原始坐标计算仿射变换
2. **目标网格定义**: 创建0.5°分辨率的全球网格
3. **众数重采样执行**: 使用rasterio.warp.reproject进行重采样
4. **数据质量检查**: 验证重采样结果的完整性

#### 第三阶段：统计分析
1. **唯一值识别**: 统计所有出现的土地覆盖类型
2. **像素计数**: 计算每种类型的像素数量
3. **百分比计算**: 计算各类型占总有效像素的比例
4. **排序和汇总**: 按像素数量排序生成统计表

### 输出结果格式 | Output Result Formats

#### 主要输出文件
1. **global_landcover_05deg_analysis.tif**
   - 内容: 0.5°分辨率全球土地覆盖数据
   - 分辨率: 720 × 360 像素
   - 数据类型: uint8
   - 无效值: 0

2. **统计报告文件**
   - CSV格式: `landcover_analysis_resampled_YYYYMMDD_HHMMSS.csv`
   - TXT格式: `landcover_summary_resampled_YYYYMMDD_HHMMSS.txt`

3. **可视化产品**
   - 直方图: `landcover_distribution_histogram_resampled.png`
   - 饼图: `landcover_distribution_pie_resampled.png`

#### 土地覆盖类型编码 | Land Cover Type Codes

根据LCCS标准，主要土地覆盖类型包括：

| 编码 | 类型名称 | 英文名称 |
|------|----------|----------|
| 10 | 雨养农田 | Cropland, rainfed |
| 20 | 灌溉农田 | Cropland, irrigated |
| 30 | 农田马赛克 | Mosaic cropland |
| 40 | 自然植被马赛克 | Mosaic natural vegetation |
| 50-90 | 各类森林 | Tree cover (various types) |
| 100 | 树木灌木马赛克 | Mosaic tree and shrub |
| 110 | 草本马赛克 | Mosaic herbaceous |
| 120 | 灌木地 | Shrubland |
| 130 | 草地 | Grassland |
| 140 | 地衣苔藓 | Lichens and mosses |
| 150 | 稀疏植被 | Sparse vegetation |
| 160-180 | 洪泛植被 | Flooded vegetation |
| 190 | 城市区域 | Urban areas |
| 200 | 裸地 | Bare areas |
| 210 | 水体 | Water bodies |
| 220 | 永久冰雪 | Permanent snow and ice |

### 质量控制措施 | Quality Control Measures

#### 数据验证方法
1. **完整性检查**
   ```python
   total_pixels = data_array.size
   valid_pixels = np.sum(data_array != 0)
   completeness = valid_pixels / total_pixels * 100
   ```

2. **类型范围验证**
   - 检查土地覆盖类型编码是否在合理范围内
   - 识别异常或未定义的类型编码

3. **空间连续性检查**
   - 验证相邻像素的逻辑一致性
   - 检查是否存在孤立的异常像素

#### 统计质量指标
1. **数据完整性**: 有效像素占总像素的比例
2. **类型多样性**: 识别的土地覆盖类型数量
3. **分布合理性**: 各类型的面积分布是否符合地理常识

### 可视化说明 | Visualization Description

#### 直方图特征
- **显示内容**: 前20种最常见的土地覆盖类型
- **颜色方案**: 钢蓝色(steelblue)柱状图
- **数值标签**: 每个柱子顶部显示精确像素数量
- **坐标轴**: X轴为类型编号，Y轴为像素数量

#### 饼图特征
- **显示内容**: 前10种土地覆盖类型 + 其他类型汇总
- **颜色方案**: Set3色彩映射，确保颜色区分度
- **百分比标注**: 自动计算并显示各类型占比
- **图例**: 清晰标注各类型名称和编号

### 技术创新点 | Technical Innovations

1. **众数重采样策略**: 相比双线性插值更适合离散分类数据
2. **内存优化处理**: 高效处理大规模NetCDF数据
3. **自动化统计分析**: 集成数据处理和统计分析流程
4. **多格式输出**: 同时生成栅格、表格和可视化产品

### 计算性能 | Computational Performance

#### 性能指标
- **处理时间**: 约15-30分钟（取决于数据大小）
- **内存需求**: 峰值约4-8GB RAM
- **存储需求**: 输出文件约200-500MB
- **CPU利用率**: 中等强度，主要为I/O密集型

#### 性能优化策略
1. **数据类型优化**: 使用uint8减少内存占用
2. **分块处理**: 对大数据集进行分块读取
3. **压缩存储**: 使用LZW压缩减少文件大小
4. **缓存机制**: 避免重复计算和数据读取

### 误差分析 | Error Analysis

#### 主要误差来源
1. **重采样误差**: 众数重采样可能丢失少数类型信息
2. **分辨率降级**: 从300m到0.5°的精度损失
3. **边界效应**: 网格边界处的分类不确定性
4. **时间代表性**: 单年数据的时间局限性

#### 误差控制措施
1. **适当重采样方法**: 众数重采样保持分类数据特性
2. **质量检查**: 多层次数据验证机制
3. **统计验证**: 与已知土地利用统计数据对比
4. **文档记录**: 详细记录处理参数和假设

### 应用限制 | Application Limitations

1. **分辨率限制**: 0.5°分辨率无法表达局地土地利用细节
2. **时间静态性**: 基于单一年份数据，未考虑动态变化
3. **分类精度**: 受原始数据分类精度限制
4. **区域差异**: 不同区域的分类精度可能存在差异

### 后续处理建议 | Recommendations for Further Processing

1. **多年数据融合**: 使用多年数据提高代表性
2. **区域验证**: 对重点区域进行高分辨率验证
3. **动态更新**: 建立定期数据更新机制
4. **精度评估**: 与地面真实数据进行精度验证

### 与其他模块的接口 | Interface with Other Modules

#### 输出提供
- `global_landcover_05deg_analysis.tif`: 供土地覆盖重分类模块使用
- 统计分析结果: 供报告生成和质量评估使用

#### 数据格式标准
- 坐标系: WGS84 (EPSG:4326)
- 分辨率: 0.5° × 0.5°
- 数据类型: uint8
- 无效值: 0
