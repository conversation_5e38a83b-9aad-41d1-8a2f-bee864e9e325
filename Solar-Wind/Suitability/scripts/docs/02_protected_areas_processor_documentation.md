# 保护区数据处理技术文档
## Protected Areas Data Processing Technical Documentation

### 脚本概述 | Script Overview

**脚本名称**: `protected_areas_processor.py`  
**主要功能**: 处理WDPA (World Database on Protected Areas) 数据，生成全球保护区栅格掩码  
**在工作流程中的作用**: 为可再生能源适宜性评估提供环境保护约束条件

### 方法论依据 | Methodological Foundation

本脚本基于以下研究方法论：
- **数据源**: WDPA (World Database on Protected Areas) 2025年7月版本
- **保护区定义**: 遵循IUCN保护区分类标准
- **栅格化方法**: 基于矢量到栅格转换的标准GIS方法
- **参考文献**: <PERSON> et al. (Nature Communications 2025) 中的保护区约束方法

### 技术实现细节 | Technical Implementation Details

#### 输入数据源和格式 | Input Data Sources and Formats

1. **WDPA数据结构**
   ```
   WDPA_Jul2025_Public_shp/
   ├── 0/
   │   ├── WDPA_Jul2025_Public_shp_0-polygon.shp
   │   └── WDPA_Jul2025_Public_shp_0-point.shp
   ├── 1/
   │   ├── WDPA_Jul2025_Public_shp_1-polygon.shp
   │   └── WDPA_Jul2025_Public_shp_1-point.shp
   └── ...
   ```

2. **数据格式规范**
   - 格式: Shapefile (.shp)
   - 坐标系: WGS84 (EPSG:4326)
   - 几何类型: Polygon (面状) 和 Point (点状)
   - 编码: UTF-8

#### 关键属性字段 | Key Attribute Fields

| 字段名 | 数据类型 | 说明 | 示例值 |
|--------|----------|------|--------|
| STATUS | String | 保护区状态 | Designated, Inscribed, Established |
| DESIG_TYPE | String | 保护区类型 | National Park, Nature Reserve |
| NAME | String | 保护区名称 | Yellowstone National Park |
| IUCN_CAT | String | IUCN分类 | Ia, Ib, II, III, IV, V, VI |
| REP_AREA | Float | 报告面积 | 8983.18 (km²) |

#### 核心算法和处理逻辑 | Core Algorithms and Processing Logic

1. **保护区筛选标准**
   ```python
   valid_status = ['Designated', 'Inscribed', 'Established']
   valid_features = gdf[gdf['STATUS'].isin(valid_status)]
   ```

2. **栅格化算法**
   ```python
   protected_mask = rasterize(
       shapes=[(geometry, 1) for geometry in valid_geometries],
       out_shape=(height, width),
       transform=transform,
       fill=0,
       dtype=np.uint8,
       all_touched=True
   )
   ```

3. **分批处理策略**
   - 批次大小: 1000个polygon/批
   - 内存管理: 逐批处理避免内存溢出
   - 结果合并: 使用numpy.maximum合并各批次结果

#### 关键参数设置 | Key Parameter Settings

| 参数 | 值 | 说明 |
|------|----|----|
| 输出分辨率 | 0.5° × 0.5° | 与其他数据层保持一致 |
| 栅格尺寸 | 720 × 360 像素 | 全球覆盖 |
| 批处理大小 | 1000 polygons | 内存优化 |
| 栅格化方法 | all_touched=True | 保守的保护区识别 |
| 数据类型 | uint8 | 内存效率优化 |
| 无效值 | 255 | 区别于保护区(1)和非保护区(0) |

### 数据处理流程 | Data Processing Workflow

#### 第一阶段：数据发现和验证
1. **文件扫描**: 自动发现所有WDPA分片文件
2. **数据结构分析**: 检查属性字段和数据完整性
3. **坐标系验证**: 确认所有数据使用WGS84坐标系
4. **统计摘要**: 生成数据概览和质量报告

#### 第二阶段：数据筛选和预处理
1. **状态筛选**: 仅保留有效状态的保护区
   - Designated: 正式指定的保护区
   - Inscribed: 已登记的保护区  
   - Established: 已建立的保护区
2. **几何验证**: 检查几何图形的有效性
3. **数据合并**: 将所有分片数据合并为统一数据集

#### 第三阶段：栅格化处理
1. **全球网格创建**: 建立0.5°分辨率的全球栅格框架
2. **Polygon数据处理**: 
   - 分批处理大型polygon数据
   - 使用all_touched策略确保完整覆盖
3. **Point数据处理**: 将点状保护区栅格化
4. **结果合并**: 合并polygon和point栅格化结果

### 输出结果格式 | Output Result Formats

#### 主要输出文件
1. **protected_areas_05deg.tif**
   - 内容: 全球保护区二值掩码
   - 分辨率: 0.5° × 0.5°
   - 数值含义: 0=非保护区, 1=保护区, 255=无数据
   - 数据类型: uint8

2. **protected_areas_distribution_05deg.png**
   - 内容: 保护区分布可视化地图
   - 颜色方案: 浅灰色(非保护区) + 森林绿(保护区)
   - 分辨率: 300 DPI

#### 统计输出信息
- 总保护区像素数量
- 保护区覆盖率百分比
- 处理的polygon和point数量
- 数据完整性报告

### 质量控制措施 | Quality Control Measures

#### 数据验证方法
1. **几何有效性检查**
   ```python
   valid_geometries = [geom for geom in geometries 
                      if geom is not None and geom.is_valid]
   ```

2. **状态字段验证**
   - 检查STATUS字段的完整性
   - 统计各状态类别的数量分布
   - 识别缺失或异常状态值

3. **空间范围检查**
   - 验证坐标范围在合理的地理边界内
   - 检查极端坐标值和异常几何图形

#### 处理异常情况
1. **缺失文件处理**: 自动跳过缺失的分片文件
2. **损坏数据处理**: 捕获并记录读取错误
3. **无效几何处理**: 过滤无效的几何图形
4. **内存管理**: 分批处理防止内存溢出

### 可视化说明 | Visualization Description

#### 可视化设计原则
1. **颜色选择**: 
   - 浅灰色(#f0f0f0): 非保护区域
   - 森林绿(#228B22): 保护区域
2. **地图投影**: 等经纬度投影，便于全球展示
3. **图例设计**: 清晰标注保护区和非保护区
4. **标题信息**: 包含统计数据和覆盖率信息

#### 可视化输出特征
- **分辨率**: 300 DPI专业质量
- **尺寸**: 20×10英寸，适合学术出版
- **格式**: PNG格式，支持透明背景
- **网格线**: 添加经纬度网格便于定位

### 技术创新点 | Technical Innovations

1. **分批处理算法**: 有效处理大规模矢量数据
2. **内存优化策略**: 避免大数据集的内存溢出问题
3. **自动化数据发现**: 智能识别和处理分片数据
4. **质量控制集成**: 内置多层次数据验证机制

### 计算性能 | Computational Performance

#### 性能指标
- **处理时间**: 约30-60分钟（取决于数据规模）
- **内存需求**: 峰值约4-8GB RAM
- **存储需求**: 输出文件约50-100MB
- **CPU利用率**: 单线程处理，CPU密集型

#### 性能优化策略
1. **分批处理**: 控制内存使用量
2. **数据类型优化**: 使用uint8减少内存占用
3. **几何简化**: 预处理阶段移除无效几何
4. **压缩存储**: 使用LZW压缩减少文件大小

### 误差分析 | Error Analysis

#### 主要误差来源
1. **栅格化误差**: 矢量到栅格转换的精度损失
2. **分辨率限制**: 0.5°分辨率无法表达小型保护区
3. **边界效应**: all_touched策略可能高估保护区面积
4. **数据时效性**: 基于特定时间点的数据快照

#### 误差控制措施
1. **保守策略**: 使用all_touched确保保护区不被遗漏
2. **高质量数据源**: 使用官方WDPA数据库
3. **定期更新**: 建议定期更新保护区数据
4. **验证机制**: 与已知保护区进行对比验证

### 应用限制 | Application Limitations

1. **分辨率约束**: 无法识别小于0.5°的保护区
2. **时间静态性**: 不反映保护区边界的动态变化
3. **分类简化**: 未区分不同类型和级别的保护区
4. **海洋保护区**: 主要关注陆地保护区

### 数据更新建议 | Data Update Recommendations

1. **定期更新**: 建议每年更新一次WDPA数据
2. **版本控制**: 记录数据版本和处理时间
3. **增量更新**: 开发增量更新机制提高效率
4. **质量监控**: 建立数据质量监控和报警机制

### 与其他模块的接口 | Interface with Other Modules

#### 输入依赖
- 无直接依赖，使用外部WDPA数据

#### 输出提供
- `protected_areas_05deg.tif`: 供适宜性整合模块使用
- 保护区统计信息: 供报告生成使用

#### 数据格式兼容性
- 与地形数据处理模块保持相同的坐标系和分辨率
- 与土地覆盖数据处理模块保持数据格式一致性
