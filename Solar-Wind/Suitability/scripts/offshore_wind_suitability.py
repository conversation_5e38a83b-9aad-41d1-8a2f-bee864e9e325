#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
海上风电适宜性评估脚本 (Offshore Wind Suitability Assessment)

基于文献方法进行海上风电适宜性筛选：
1. 土地覆盖筛选：仅水体区域(100%适宜)，其他土地类型完全排除(0%)
2. EEZ约束：仅在各国专属经济区内部署
3. 水深约束：≥1m (避免过浅区域和陆地)
4. 海洋保护区约束：排除海洋生态保护区

参考文献: Wang et al., Nature Communications, 2025
数据分辨率: 0.5度
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
from matplotlib.patches import Patch
import rasterio
from rasterio.plot import show
from rasterio.features import rasterize
from rasterio.mask import mask
from rasterio.warp import calculate_default_transform, reproject, Resampling
from rasterio.crs import CRS
import xarray as xr
import geopandas as gpd
from shapely.geometry import mapping
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置英文字体
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

def setup_directories():
    """设置输出目录"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    base_dir = os.path.dirname(script_dir)
    
    outputs_dir = os.path.join(base_dir, 'outputs')
    visualizations_dir = os.path.join(outputs_dir, 'visualizations')
    processed_dir = os.path.join(outputs_dir, 'processed_data')
    reports_dir = os.path.join(outputs_dir, 'reports')
    
    for directory in [visualizations_dir, processed_dir, reports_dir]:
        os.makedirs(directory, exist_ok=True)
    
    return outputs_dir, visualizations_dir, processed_dir, reports_dir

def load_landcover_data():
    """加载重分类后的土地覆盖数据"""
    print("\n=== Loading reclassified land cover data ===")
    
    script_dir = os.path.dirname(os.path.abspath(__file__))
    base_dir = os.path.dirname(script_dir)
    
    landcover_file = os.path.join(base_dir, 'outputs', 'processed_data', 
                                  'global_landcover_05deg_reclassified.tif')
    
    if not os.path.exists(landcover_file):
        raise FileNotFoundError(f"Land cover file not found: {landcover_file}")
    
    print(f"Loading: {landcover_file}")
    
    with rasterio.open(landcover_file) as src:
        landcover_data = src.read(1)
        landcover_meta = src.meta.copy()
        landcover_transform = src.transform
        landcover_crs = src.crs
    
    print(f"Land cover data shape: {landcover_data.shape}")
    print(f"Land cover CRS: {landcover_crs}")
    print(f"Unique land cover values: {np.unique(landcover_data[landcover_data != landcover_meta['nodata']])}")
    
    return landcover_data, landcover_meta, landcover_transform, landcover_crs

def load_eez_data():
    """加载EEZ专属经济区数据"""
    print("\n=== Loading EEZ (Exclusive Economic Zone) data ===")
    
    # 获取数据路径
    project_root = '/Users/<USER>/PycharmProjects/Global_Power_System_Planning'
    eez_dir = os.path.join(project_root, 'Solar-Wind', 'data', 'gis', 'marine', 'World_EEZ_v10_20180221')
    eez_file = os.path.join(eez_dir, 'eez_v10.shp')
    
    if not os.path.exists(eez_file):
        raise FileNotFoundError(f"EEZ shapefile not found: {eez_file}")
    
    print(f"Loading: {eez_file}")
    
    # 读取EEZ数据
    eez_gdf = gpd.read_file(eez_file)
    
    print(f"EEZ data loaded: {len(eez_gdf)} features")
    print(f"EEZ CRS: {eez_gdf.crs}")
    print(f"EEZ columns: {list(eez_gdf.columns)}")
    
    # 确保使用WGS84坐标系
    if eez_gdf.crs != 'EPSG:4326':
        print("Reprojecting EEZ to WGS84...")
        eez_gdf = eez_gdf.to_crs('EPSG:4326')
    
    return eez_gdf

def load_bathymetry_data(landcover_meta, landcover_transform):
    """加载GEBCO水深数据，优先用GEBCO_2024.nc"""
    print("\n=== Loading GEBCO bathymetry data ===")
    project_root = '/Users/<USER>/PycharmProjects/Global_Power_System_Planning'
    nc_path = os.path.join(project_root, 'Solar-Wind', 'data', 'gis', 'marine', 'gebco_2024', 'GEBCO_2024.nc')
    if os.path.exists(nc_path):
        print(f"Using NetCDF: {nc_path}")
        ds = xr.open_dataset(nc_path)
        elev = ds['elevation'].values  # shape: (lat, lon)
        lats = ds['lat'].values
        lons = ds['lon'].values
        # 目标网格参数
        dst_height = landcover_meta['height']
        dst_width = landcover_meta['width']
        dst_transform = landcover_transform
        # 生成目标网格的经纬度
        rows, cols = np.meshgrid(np.arange(dst_height), np.arange(dst_width), indexing='ij')
        xs, ys = rasterio.transform.xy(dst_transform, rows, cols)
        xs = np.array(xs).reshape(dst_height, dst_width)
        ys = np.array(ys).reshape(dst_height, dst_width)
        
        # 将目标网格经纬度映射到GEBCO索引
        lon_resolution = lons[1] - lons[0]
        lat_resolution = lats[1] - lats[0]
        lon_idx = np.clip(np.round((xs - lons[0]) / lon_resolution).astype(int), 0, len(lons)-1)
        lat_idx = np.clip(np.round((ys - lats[0]) / lat_resolution).astype(int), 0, len(lats)-1)
        
        # 使用高级索引获取水深数据
        bathymetry = elev[lat_idx, lon_idx]

        print(f"Bathymetry shape (resampled): {bathymetry.shape}")
        print(f"Bathymetry range: {np.nanmin(bathymetry):.1f} to {np.nanmax(bathymetry):.1f} m")
        ds.close()
        return bathymetry

def create_water_body_mask(landcover_data, landcover_meta):
    """
    创建水体掩码
    根据Wang et al.方法，海上风电仅在水体区域部署(100%适宜)
    """
    print("\n=== Creating water body mask ===")
    
    # 水体类型值（根据重分类后的土地覆盖数据）
    # 通常水体在ESA CCI数据中为210类型，重分类后为210
    water_types = [210]  # 水体
    
    # 创建水体掩码
    water_mask = np.isin(landcover_data, water_types)
    
    # 排除nodata区域
    nodata_value = landcover_meta.get('nodata', -9999)
    valid_mask = landcover_data != nodata_value
    water_mask = water_mask & valid_mask
    
    water_area = np.sum(water_mask)
    total_valid_area = np.sum(valid_mask)
    
    print(f"Water body pixels: {water_area:,}")
    print(f"Total valid pixels: {total_valid_area:,}")
    print(f"Water body percentage: {water_area/total_valid_area*100:.2f}%")
    
    return water_mask

def create_eez_mask(eez_gdf, landcover_meta, landcover_transform):
    """创建EEZ专属经济区掩码"""
    print("\n=== Creating EEZ mask ===")
    
    # 获取栅格参数
    height = landcover_meta['height']
    width = landcover_meta['width']
    
    # 创建EEZ掩码
    print("Rasterizing EEZ polygons...")
    
    # 将EEZ多边形栅格化
    eez_shapes = [(geom, 1) for geom in eez_gdf.geometry]
    
    eez_mask = rasterize(
        eez_shapes,
        out_shape=(height, width),
        transform=landcover_transform,
        fill=0,
        default_value=1,
        dtype=np.uint8
    )
    
    eez_area = np.sum(eez_mask)
    total_area = height * width
    
    print(f"EEZ pixels: {eez_area:,}")
    print(f"Total pixels: {total_area:,}")
    print(f"EEZ coverage: {eez_area/total_area*100:.2f}%")
    
    return eez_mask.astype(bool)

def create_depth_mask(bathymetry_data, min_depth=1.0, max_depth=60.0):
    """
    创建水深掩码
    筛选-60<=depth<=-1m（即1-60m水深，负值为海洋）
    """
    print(f"\n=== Creating depth mask ({min_depth}-{max_depth}m) ===")
    # GEBCO: 负值为海洋深度
    depth_mask = (bathymetry_data <= -min_depth) & (bathymetry_data >= -max_depth)
    valid_mask = ~np.isnan(bathymetry_data)
    depth_mask = depth_mask & valid_mask
    suitable_depth_area = np.sum(depth_mask)
    total_valid_area = np.sum(valid_mask)
    print(f"Suitable depth pixels: {suitable_depth_area:,}")
    print(f"Total valid bathymetry pixels: {total_valid_area:,}")
    if total_valid_area > 0:
        print(f"Suitable depth percentage: {suitable_depth_area/total_valid_area*100:.2f}%")
    return depth_mask

def calculate_offshore_suitability(water_mask, eez_mask, depth_mask):
    """计算海上风电综合适宜性"""
    print("\n=== Calculating offshore wind suitability ===")
    
    # 综合适宜性：水体 AND EEZ内 AND 合适水深
    offshore_suitable = water_mask & eez_mask & depth_mask
    
    # 统计结果
    water_area = np.sum(water_mask)
    eez_area = np.sum(eez_mask)
    depth_area = np.sum(depth_mask)
    suitable_area = np.sum(offshore_suitable)
    
    print(f"Water body area: {water_area:,} pixels")
    print(f"EEZ area: {eez_area:,} pixels")
    print(f"Suitable depth area: {depth_area:,} pixels")
    print(f"Final suitable area: {suitable_area:,} pixels")
    
    # 逐步筛选分析
    water_and_eez = water_mask & eez_mask
    water_eez_area = np.sum(water_and_eez)
    
    print(f"\nStep-by-step filtering:")
    print(f"1. Water bodies: {water_area:,} pixels")
    print(f"2. Water + EEZ: {water_eez_area:,} pixels")
    print(f"3. Water + EEZ + Depth: {suitable_area:,} pixels")
    
    if water_eez_area > 0:
        print(f"Depth constraint reduces area by: {(water_eez_area-suitable_area)/water_eez_area*100:.1f}%")
    
    return offshore_suitable, {
        'water_area': water_area,
        'eez_area': eez_area,
        'depth_area': depth_area,
        'water_eez_area': water_eez_area,
        'suitable_area': suitable_area
    }

def visualize_offshore_suitability(landcover_data, water_mask, eez_mask, depth_mask, 
                                 offshore_suitable, landcover_meta, landcover_transform, 
                                 stats, visualizations_dir):
    """可视化海上风电适宜性分析结果"""
    print("\n=== Creating visualizations ===")
    
    # 创建图形
    fig, axes = plt.subplots(2, 3, figsize=(24, 16))
    fig.suptitle('Offshore Wind Suitability Analysis\n(Based on Wang et al., Nature Communications, 2025)', 
                 fontsize=16, fontweight='bold')
    
    # 准备地理范围
    height, width = landcover_data.shape
    west, north = rasterio.transform.xy(landcover_transform, 0, 0)
    east, south = rasterio.transform.xy(landcover_transform, height, width)
    extent = [west, east, south, north]
    
    # 1. 原始土地覆盖
    ax1 = axes[0, 0]
    landcover_plot = landcover_data.copy().astype(np.float32)
    landcover_plot[landcover_data == landcover_meta.get('nodata', -9999)] = np.nan
    im1 = ax1.imshow(landcover_plot, extent=extent, cmap='tab20', interpolation='nearest')
    ax1.set_title('Original Land Cover\n(ESA CCI Reclassified)', fontweight='bold')
    ax1.set_xlabel('Longitude')
    ax1.set_ylabel('Latitude')
    
    # 2. 水体掩码
    ax2 = axes[0, 1]
    water_plot = np.where(water_mask, 1, 0)
    water_plot = np.where(landcover_data == landcover_meta.get('nodata', -9999), np.nan, water_plot)
    im2 = ax2.imshow(water_plot, extent=extent, cmap='Blues', vmin=0, vmax=1)
    ax2.set_title(f'Water Bodies\n({stats["water_area"]:,} pixels)', fontweight='bold')
    ax2.set_xlabel('Longitude')
    ax2.set_ylabel('Latitude')
    
    # 3. EEZ掩码
    ax3 = axes[0, 2]
    eez_plot = np.where(eez_mask, 1, 0)
    eez_plot = np.where(landcover_data == landcover_meta.get('nodata', -9999), np.nan, eez_plot)
    im3 = ax3.imshow(eez_plot, extent=extent, cmap='Greens', vmin=0, vmax=1)
    ax3.set_title(f'Exclusive Economic Zones (EEZ)\n({stats["eez_area"]:,} pixels)', fontweight='bold')
    ax3.set_xlabel('Longitude')
    ax3.set_ylabel('Latitude')
    
    # 4. 水深掩码
    ax4 = axes[1, 0]
    depth_plot = np.where(depth_mask, 1, 0)
    depth_plot = np.where(landcover_data == landcover_meta.get('nodata', -9999), np.nan, depth_plot)
    im4 = ax4.imshow(depth_plot, extent=extent, cmap='Purples', vmin=0, vmax=1)
    ax4.set_title(f'Suitable Depth (≥1m)\n({stats["depth_area"]:,} pixels)', fontweight='bold')
    ax4.set_xlabel('Longitude')
    ax4.set_ylabel('Latitude')
    
    # 5. 水体+EEZ组合
    ax5 = axes[1, 1]
    water_eez_plot = np.where(water_mask & eez_mask, 1, 0)
    water_eez_plot = np.where(landcover_data == landcover_meta.get('nodata', -9999), np.nan, water_eez_plot)
    im5 = ax5.imshow(water_eez_plot, extent=extent, cmap='Oranges', vmin=0, vmax=1)
    ax5.set_title(f'Water Bodies in EEZ\n({stats["water_eez_area"]:,} pixels)', fontweight='bold')
    ax5.set_xlabel('Longitude')
    ax5.set_ylabel('Latitude')
    
    # 6. 最终适宜性
    ax6 = axes[1, 2]
    suitable_plot = np.where(offshore_suitable, 1, 0)
    suitable_plot = np.where(landcover_data == landcover_meta.get('nodata', -9999), np.nan, suitable_plot)
    im6 = ax6.imshow(suitable_plot, extent=extent, cmap='Reds', vmin=0, vmax=1)
    ax6.set_title(f'Final Offshore Wind Suitable Areas\n({stats["suitable_area"]:,} pixels)', 
                  fontweight='bold')
    ax6.set_xlabel('Longitude')
    ax6.set_ylabel('Latitude')
    
    # 添加颜色条
    for i, (ax, im) in enumerate([(ax2, im2), (ax3, im3), (ax4, im4), (ax5, im5), (ax6, im6)]):
        cbar = plt.colorbar(im, ax=ax, shrink=0.6)
        cbar.set_label('Suitability')
        cbar.set_ticks([0, 1])
        cbar.set_ticklabels(['Not Suitable', 'Suitable'])
    
    plt.tight_layout()
    
    # 保存图片
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f'offshore_wind_suitability_analysis_{timestamp}.png'
    filepath = os.path.join(visualizations_dir, filename)
    plt.savefig(filepath, dpi=300, bbox_inches='tight')
    print(f"Visualization saved: {filepath}")
    plt.close()
    
    return filepath

def save_results(offshore_suitable, water_mask, eez_mask, depth_mask, 
                landcover_data, landcover_meta, landcover_transform, stats, 
                processed_dir, reports_dir):
    """保存分析结果"""
    print("\n=== Saving results ===")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 1. 保存适宜性栅格数据
    output_meta = landcover_meta.copy()
    output_meta.update({
        'dtype': 'uint8',
        'nodata': 255,
        'count': 1
    })
    
    # 保存最终适宜性结果
    suitable_raster = np.where(offshore_suitable, 1, 0).astype(np.uint8)
    suitable_raster[landcover_data == landcover_meta.get('nodata', -9999)] = 255
    
    suitable_file = os.path.join(processed_dir, f'offshore_wind_suitability_{timestamp}.tif')
    with rasterio.open(suitable_file, 'w', **output_meta) as dst:
        dst.write(suitable_raster, 1)
    print(f"Suitability raster saved: {suitable_file}")
    
    # 2. 保存分层结果
    layers = {
        'water_mask': water_mask,
        'eez_mask': eez_mask,
        'depth_mask': depth_mask
    }
    
    for layer_name, layer_data in layers.items():
        layer_raster = np.where(layer_data, 1, 0).astype(np.uint8)
        layer_raster[landcover_data == landcover_meta.get('nodata', -9999)] = 255
        
        layer_file = os.path.join(processed_dir, f'{layer_name}_{timestamp}.tif')
        with rasterio.open(layer_file, 'w', **output_meta) as dst:
            dst.write(layer_raster, 1)
        print(f"{layer_name} saved: {layer_file}")
    
    # 3. 生成统计报告
    report_content = f"""# 海上风电适宜性评估报告 (Offshore Wind Suitability Assessment Report)

## 分析概述
- **方法依据**: Wang et al., Nature Communications, 2025
- **分析时间**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- **数据分辨率**: 0.5度
- **坐标系统**: {landcover_meta.get('crs', 'Unknown')}

## 筛选标准
1. **土地覆盖适宜性**: 仅水体区域(100%适宜)，其他土地类型完全排除(0%)
2. **专属经济区约束**: 仅在各国EEZ内部署
3. **水深约束**: ≥1m (避免过浅区域和陆地)
4. **环境保护约束**: 排除海洋生态保护区(如有)

## 分析结果

### 面积统计 (像素数)
- **水体总面积**: {stats['water_area']:,} pixels
- **EEZ总面积**: {stats['eez_area']:,} pixels  
- **合适水深面积**: {stats['depth_area']:,} pixels
- **水体+EEZ面积**: {stats['water_eez_area']:,} pixels
- **最终适宜面积**: {stats['suitable_area']:,} pixels

### 筛选效果分析
- **水体占比**: {stats['water_area']/(landcover_meta['height']*landcover_meta['width'])*100:.2f}%
- **EEZ占比**: {stats['eez_area']/(landcover_meta['height']*landcover_meta['width'])*100:.2f}%
- **最终适宜占比**: {stats['suitable_area']/(landcover_meta['height']*landcover_meta['width'])*100:.2f}%

### 约束影响分析
"""
    
    if stats['water_eez_area'] > 0:
        depth_reduction = (stats['water_eez_area'] - stats['suitable_area']) / stats['water_eez_area'] * 100
        report_content += f"- **水深约束影响**: 减少面积 {depth_reduction:.1f}%\n"
    
    report_content += f"""
## 技术参数
- **海上风机型号**: Vestas 8.0 MW  
- **轮毂高度**: 100m
- **装机密度**: 4.6 MW/km²
- **风机间距**: 7倍转子直径

## 输出文件
- **适宜性栅格**: offshore_wind_suitability_{timestamp}.tif
- **水体掩码**: water_mask_{timestamp}.tif  
- **EEZ掩码**: eez_mask_{timestamp}.tif
- **水深掩码**: depth_mask_{timestamp}.tif
- **可视化图片**: offshore_wind_suitability_analysis_{timestamp}.png

## 备注
本分析基于Wang et al. (Nature Communications, 2025)的海上风电适宜性评估方法，
结合ESA CCI土地覆盖数据、EEZ专属经济区数据和GEBCO水深数据进行综合评估。
"""
    
    # 保存报告
    report_file = os.path.join(reports_dir, f'offshore_wind_suitability_report_{timestamp}.txt')
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    print(f"Report saved: {report_file}")
    
    return {
        'suitable_file': suitable_file,
        'report_file': report_file,
        'timestamp': timestamp
    }

def main():
    """主函数"""
    print("=== 海上风电适宜性评估 (Offshore Wind Suitability Assessment) ===")
    print("Based on: Wang et al., Nature Communications, 2025")
    print(f"Analysis started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 设置目录
        outputs_dir, visualizations_dir, processed_dir, reports_dir = setup_directories()
        
        # 2. 加载土地覆盖数据
        landcover_data, landcover_meta, landcover_transform, landcover_crs = load_landcover_data()
        
        # 3. 加载EEZ数据
        eez_gdf = load_eez_data()
        
        # 4. 加载水深数据
        bathymetry_data = load_bathymetry_data(landcover_meta, landcover_transform)
        
        # 5. 创建各种掩码
        water_mask = create_water_body_mask(landcover_data, landcover_meta)
        eez_mask = create_eez_mask(eez_gdf, landcover_meta, landcover_transform)
        depth_mask = create_depth_mask(bathymetry_data, min_depth=1.0, max_depth=60.0)
        
        # 6. 计算综合适宜性
        offshore_suitable, stats = calculate_offshore_suitability(water_mask, eez_mask, depth_mask)
        
        # 7. 可视化结果
        viz_file = visualize_offshore_suitability(
            landcover_data, water_mask, eez_mask, depth_mask, offshore_suitable,
            landcover_meta, landcover_transform, stats, visualizations_dir
        )
        
        # 8. 保存结果
        output_files = save_results(
            offshore_suitable, water_mask, eez_mask, depth_mask,
            landcover_data, landcover_meta, landcover_transform, stats,
            processed_dir, reports_dir
        )
        
        # 9. 输出总结
        print(f"\n=== Analysis completed successfully ===")
        print(f"Final suitable area: {stats['suitable_area']:,} pixels")
        print(f"Visualization: {viz_file}")
        print(f"Suitability raster: {output_files['suitable_file']}")
        print(f"Report: {output_files['report_file']}")
        print(f"Analysis completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"\nError occurred: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 