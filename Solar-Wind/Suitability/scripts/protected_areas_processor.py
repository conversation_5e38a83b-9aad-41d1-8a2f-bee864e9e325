#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
保护区数据处理脚本
处理WDPA保护区数据，将polygon和point数据栅格化为0.5度分辨率
"""

import os
import glob
import numpy as np
import pandas as pd
import geopandas as gpd
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import rasterio
from rasterio.features import rasterize
from rasterio.transform import from_bounds
import warnings
warnings.filterwarnings('ignore')

def setup_directories():
    """设置输出目录"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(script_dir)))
    
    data_dir = os.path.join(base_dir, 'Solar-Wind', 'data', 'gis', 'protected_areas', 'WDPA_Jul2025_Public_shp')
    outputs_dir = os.path.join(base_dir, 'Solar-Wind', 'Suitability', 'outputs')
    processed_dir = os.path.join(outputs_dir, 'processed_data')
    
    for directory in [processed_dir]:
        os.makedirs(directory, exist_ok=True)
    
    return data_dir, processed_dir

def find_wdpa_files(data_dir):
    """查找WDPA数据文件"""
    print("\n查找WDPA数据文件...")
    
    # 存储找到的文件
    files = {'polygon': set(), 'point': set()}
    
    # 直接在data_dir中查找文件
    polygon_files = glob.glob(os.path.join(data_dir, '*', '*polygon*.shp'))
    point_files = glob.glob(os.path.join(data_dir, '*', '*point*.shp'))
    
    # 添加到集合中去重
    files['polygon'].update(polygon_files)
    files['point'].update(point_files)
    
    # 转换回列表
    files = {k: sorted(list(v)) for k, v in files.items()}
    
    print(f"\n找到的文件:")
    print(f"  Polygon文件: {len(files['polygon'])} 个")
    for f in files['polygon']:
        print(f"    - {os.path.basename(os.path.dirname(f))}/{os.path.basename(f)}")
    print(f"\n  Point文件: {len(files['point'])} 个")
    for f in files['point']:
        print(f"    - {os.path.basename(os.path.dirname(f))}/{os.path.basename(f)}")
    
    return files

def analyze_wdpa_data(files):
    """分析WDPA数据结构"""
    print("\n分析WDPA数据结构...")
    
    data_info = {'polygon': None, 'point': None}
    
    for data_type in ['polygon', 'point']:
        if not files[data_type]:
            continue
            
        print(f"\n分析{data_type.upper()}数据:")
        # 读取第一个文件来分析结构
        try:
            gdf = gpd.read_file(files[data_type][0])
            print(f"  要素数量: {len(gdf):,}")
            print(f"  坐标参考系统: {gdf.crs}")
            print("\n  属性字段:")
            for col in gdf.columns:
                non_null = gdf[col].count()
                total = len(gdf)
                print(f"    - {col}: {gdf[col].dtype} (非空值: {non_null:,}/{total:,})")
            
            if 'STATUS' in gdf.columns:
                status_counts = gdf['STATUS'].value_counts()
                print("\n  STATUS字段统计:")
                for status, count in status_counts.items():
                    print(f"    - {status}: {count:,}")
            
            if 'DESIG_TYPE' in gdf.columns:
                desig_counts = gdf['DESIG_TYPE'].value_counts().head()
                print("\n  DESIG_TYPE字段前5项统计:")
                for desig, count in desig_counts.items():
                    print(f"    - {desig}: {count:,}")
            
            data_info[data_type] = {
                'sample_file': files[data_type][0],
                'crs': gdf.crs,
                'columns': list(gdf.columns)
            }
            
        except Exception as e:
            print(f"  错误: 无法读取文件: {str(e)}")
    
    return data_info

def process_wdpa_data(files, data_info):
    """处理WDPA数据"""
    print("\n处理WDPA数据...")
    
    valid_status = ['Designated', 'Inscribed', 'Established']
    gdfs = {'polygon': None, 'point': None}
    
    for data_type in ['polygon', 'point']:
        if not files[data_type] or not data_info[data_type]:
            continue
            
        print(f"\n处理{data_type.upper()}数据:")
        total_features = 0
        valid_features = 0
        
        # 用于存储所有有效要素
        all_valid_features = []
        
        for file in files[data_type]:
            print(f"  处理文件: {os.path.basename(os.path.dirname(file))}/{os.path.basename(file)}")
            try:
                gdf = gpd.read_file(file)
                total_features += len(gdf)
                
                # 筛选有效的保护区
                if 'STATUS' in gdf.columns:
                    valid_gdf = gdf[gdf['STATUS'].isin(valid_status)]
                    valid_features += len(valid_gdf)
                    
                    if not valid_gdf.empty:
                        all_valid_features.append(valid_gdf)
                
            except Exception as e:
                print(f"    错误: {str(e)}")
                continue
        
        print(f"  总要素数: {total_features:,}")
        print(f"  有效要素数: {valid_features:,}")
        
        # 合并所有分片的有效要素
        if all_valid_features:
            print(f"  合并分片数据...")
            gdfs[data_type] = gpd.GeoDataFrame(pd.concat(all_valid_features, ignore_index=True))
            print(f"  最终要素数: {len(gdfs[data_type]):,}")
    
    return gdfs

def create_raster_mask(gdfs, processed_dir):
    """创建0.5度分辨率的栅格掩码"""
    print("\n创建0.5度分辨率的栅格掩码...")
    
    # 设置0.5度分辨率的全球栅格参数
    resolution = 0.5
    width = int(360 / resolution)
    height = int(180 / resolution)
    transform = from_bounds(-180, -90, 180, 90, width, height)
    
    print(f"栅格参数: {width}x{height}, 分辨率: {resolution}度")
    
    # 初始化掩码
    protected_mask = np.zeros((height, width), dtype=np.uint8)
    
    # 处理polygon数据
    if gdfs['polygon'] is not None:
        print("\n处理polygon数据...")
        
        # 分批处理，每批1000个polygon
        batch_size = 1000
        total_polygons = len(gdfs['polygon'])
        num_batches = (total_polygons + batch_size - 1) // batch_size
        
        print(f"总polygon数量: {total_polygons:,}")
        print(f"分{num_batches}批处理，每批{batch_size}个")
        
        for batch_idx in range(num_batches):
            start_idx = batch_idx * batch_size
            end_idx = min((batch_idx + 1) * batch_size, total_polygons)
            
            print(f"\n处理第{batch_idx + 1}/{num_batches}批 ({start_idx:,} - {end_idx:,})...")
            
            # 获取当前批次的几何图形
            batch_gdf = gdfs['polygon'].iloc[start_idx:end_idx]
            shapes = [(geom, 1) for geom in batch_gdf.geometry if geom is not None and geom.is_valid]
            
            if shapes:
                # 栅格化当前批次
                batch_mask = rasterize(
                    shapes,
                    out_shape=(height, width),
                    transform=transform,
                    fill=0,
                    dtype=np.uint8,
                    all_touched=True
                )
                protected_mask = np.maximum(protected_mask, batch_mask)
                print(f"当前批次新增保护区像元: {np.sum(batch_mask == 1):,}")
    
    # 处理point数据
    if gdfs['point'] is not None:
        print("\n处理point数据...")
        shapes = [(geom, 1) for geom in gdfs['point'].geometry if geom is not None]
        print(f"有效point数量: {len(shapes):,}")
        
        point_mask = rasterize(
            shapes,
            out_shape=(height, width),
            transform=transform,
            fill=0,
            dtype=np.uint8,
            all_touched=True
        )
        protected_mask = np.maximum(protected_mask, point_mask)
        print(f"point保护区像元数: {np.sum(point_mask == 1):,}")
    
    # 计算总的保护区统计
    total_protected = np.sum(protected_mask == 1)
    total_pixels = height * width
    print(f"\n总保护区像元数: {total_protected:,} ({total_protected/total_pixels*100:.2f}%)")
    
    # 保存栅格文件
    output_file = os.path.join(processed_dir, 'protected_areas_05deg.tif')
    
    with rasterio.open(output_file, 'w',
                      driver='GTiff',
                      height=height,
                      width=width,
                      count=1,
                      dtype=np.uint8,
                      crs='EPSG:4326',
                      transform=transform,
                      nodata=255,
                      compress='lzw') as dst:
        dst.write(protected_mask, 1)
    
    print(f"栅格文件已保存: {output_file}")
    return protected_mask, output_file

def create_visualization(protected_mask, processed_dir):
    """创建保护区分布可视化"""
    print("\n创建保护区分布可视化...")
    
    # 创建全球底图
    fig, ax = plt.subplots(1, 1, figsize=(20, 10))
    
    # 设置地图范围
    ax.set_xlim([-180, 180])
    ax.set_ylim([-90, 90])
    
    # 创建掩码（0 -> 浅灰色，1 -> 绿色）
    colors = ['#f0f0f0', '#228B22']  # 浅灰色和森林绿
    
    # 绘制地图
    plt.imshow(protected_mask, cmap=matplotlib.colors.ListedColormap(colors),
              extent=[-180, 180, -90, 90])
    
    # 添加图例
    patches = [plt.Rectangle((0,0),1,1, facecolor=color) for color in colors]
    plt.legend(patches, ['Non-protected Areas', 'Protected Areas'],
              loc='upper right', title='Protected Areas Distribution')
    
    # 添加标题和轴标签
    protected_pixels = np.sum(protected_mask == 1)
    total_pixels = protected_mask.size
    plt.title(f'Global Protected Areas Distribution (0.5° Resolution)\nProtected Areas: {protected_pixels:,} pixels ({protected_pixels/total_pixels*100:.2f}% of total area)')
    plt.xlabel('Longitude')
    plt.ylabel('Latitude')
    
    # 添加网格
    ax.grid(True, linestyle='--', alpha=0.5)
    
    # 保存图片
    output_file = os.path.join(processed_dir, 'protected_areas_distribution_05deg.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"可视化已保存: {output_file}")

def main():
    """主函数"""
    print("WDPA保护区数据分析")
    print("="*50)
    
    try:
        # 设置目录
        data_dir, processed_dir = setup_directories()
        
        # 查找WDPA文件
        files = find_wdpa_files(data_dir)
        if not files:
            return
        
        # 分析数据结构
        data_info = analyze_wdpa_data(files)
        
        # 处理数据
        gdfs = process_wdpa_data(files, data_info)
        
        # 创建栅格掩码
        protected_mask, _ = create_raster_mask(gdfs, processed_dir)
        
        # 创建可视化
        create_visualization(protected_mask, processed_dir)
        
        print("\n处理完成!")
        
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 