#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Land cover data reclassification and suitability visualization script
1. Round non-multiples of 10 land cover types to nearest 10
2. Use updated CCI LC color configuration for global visualization
3. Filter and visualize suitable land types for solar and wind development
"""

import os
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
from matplotlib.patches import Patch
import rasterio
from rasterio.plot import show
import xarray as xr
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置英文字体
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

def setup_directories():
    """设置输出目录"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    base_dir = os.path.dirname(script_dir)
    
    outputs_dir = os.path.join(base_dir, 'outputs')
    visualizations_dir = os.path.join(outputs_dir, 'visualizations')
    processed_dir = os.path.join(outputs_dir, 'processed_data')
    reports_dir = os.path.join(outputs_dir, 'reports')
    
    for directory in [visualizations_dir, processed_dir, reports_dir]:
        os.makedirs(directory, exist_ok=True)
    
    return outputs_dir, visualizations_dir, processed_dir, reports_dir

def reclassify_landcover(data):
    """将非10倍数的土地类型四舍五入到最近的10倍数"""
    print("\n=== Starting land cover reclassification ===")
    
    # 复制数据以避免修改原始数据
    reclassified_data = data.copy()
    
    # 统计重新归类前的唯一值
    original_unique = np.unique(data[data != 0])
    print(f"Original unique land cover types: {len(original_unique)}")
    print(f"Original types: {sorted(original_unique)}")
    
    # 重新归类：四舍五入到最近的10倍数
    for value in original_unique:
        if value != 0:  # 保持0值不变
            new_value = round(value / 10) * 10
            if new_value != value:
                reclassified_data[data == value] = new_value
                print(f"Type {value} → {new_value}")
    
    # 统计重新归类后的唯一值
    new_unique = np.unique(reclassified_data[reclassified_data != 0])
    print(f"\nReclassified unique land cover types: {len(new_unique)}")
    print(f"Reclassified types: {sorted(new_unique)}")
    
    return reclassified_data

def get_updated_lc_colors():
    """获取更新的土地覆盖颜色配置"""
    # 用户提供的颜色配置 (RGB 0-255)
    LAND_COVER_COLORS = {
        0: (0, 0, 0),           # No Data - Black
        10: (255, 255, 100),    # Cropland, rainfed - Yellow
        20: (170, 240, 240),    # Cropland, irrigated - Light Blue
        30: (220, 240, 100),    # Mosaic cropland - Light Yellow-Green
        40: (200, 200, 100),    # Mosaic natural vegetation - Olive
        50: (0, 100, 0),        # Tree cover, broadleaved, evergreen - Dark Green
        60: (0, 160, 0),        # Tree cover, broadleaved, deciduous - Green
        70: (0, 60, 0),         # Tree cover, needleleaved, evergreen - Very Dark Green
        80: (40, 80, 0),        # Tree cover, needleleaved, deciduous - Dark Olive
        90: (120, 130, 0),      # Tree cover, mixed - Brown-Green
        100: (140, 160, 0),     # Mosaic tree and shrub - Light Brown-Green
        110: (190, 150, 0),     # Mosaic herbaceous - Brown
        120: (150, 100, 0),     # Shrubland - Dark Brown
        130: (255, 180, 50),    # Grassland - Orange
        140: (255, 220, 210),   # Lichens and mosses - Light Pink
        150: (255, 235, 175),   # Sparse vegetation - Light Beige
        160: (0, 120, 90),      # Tree cover, flooded, fresh - Teal
        170: (0, 150, 120),     # Tree cover, flooded, saline - Light Teal
        180: (0, 220, 130),     # Shrub/herbaceous, flooded - Light Green-Blue
        190: (195, 20, 0),      # Urban areas - Red
        200: (255, 245, 215),   # Bare areas - Beige
        210: (0, 70, 200),      # Water bodies - Blue
        220: (255, 255, 255),   # Permanent snow and ice - White
    }
    
    # 转换RGB (0-255) 到 hex
    color_map = {}
    for lc_type, (r, g, b) in LAND_COVER_COLORS.items():
        color_map[lc_type] = f'#{r:02x}{g:02x}{b:02x}'
    
    return color_map

def create_landcover_colormap(unique_values):
    """创建土地覆盖颜色映射"""
    color_dict = get_updated_lc_colors()
    
    # 创建颜色列表和标签
    colors = []
    labels = []
    values = []
    
    # 土地类型标签 (英文)
    lc_labels = {
        0: 'No Data',
        10: 'Cropland, rainfed',
        20: 'Cropland, irrigated',
        30: 'Mosaic cropland',
        40: 'Mosaic natural vegetation',
        50: 'Tree cover, broadleaved, evergreen',
        60: 'Tree cover, broadleaved, deciduous',
        70: 'Tree cover, needleleaved, evergreen',
        80: 'Tree cover, needleleaved, deciduous',
        90: 'Tree cover, mixed leaf type',
        100: 'Mosaic tree and shrub',
        110: 'Mosaic herbaceous cover',
        120: 'Shrubland',
        130: 'Grassland',
        140: 'Lichens and mosses',
        150: 'Sparse vegetation',
        160: 'Tree cover, flooded, fresh water',
        170: 'Tree cover, flooded, saline water',
        180: 'Shrub/herbaceous cover, flooded',
        190: 'Urban areas',
        200: 'Bare areas',
        210: 'Water bodies',
        220: 'Permanent snow and ice'
    }
    
    for value in sorted(unique_values):
        if value in color_dict:
            colors.append(color_dict[value])
            labels.append(f"{int(value)}: {lc_labels.get(value, 'Unknown')}")
            values.append(value)
    
    return colors, labels, values

def visualize_global_landcover(data, transform, output_dir, title="Global Land Cover Distribution"):
    """可视化全球土地覆盖类型"""
    print(f"\n=== Creating {title} visualization ===")
    
    unique_values = np.unique(data[data != 0])
    colors, labels, values = create_landcover_colormap(unique_values)
    
    # 创建离散颜色映射
    n_colors = len(colors)
    cmap = mcolors.ListedColormap(colors)
    norm = mcolors.BoundaryNorm(boundaries=np.arange(len(values)+1) - 0.5, ncolors=n_colors)
    
    # 创建用于显示的数据（将值映射到索引）
    display_data = np.zeros_like(data, dtype=float)
    for i, value in enumerate(values):
        display_data[data == value] = i
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(20, 12))
    
    # 计算经纬度范围
    height, width = data.shape
    west, south, east, north = rasterio.transform.array_bounds(height, width, transform)
    
    im = ax.imshow(display_data, cmap=cmap, norm=norm, 
                   extent=[west, east, south, north], 
                   interpolation='nearest', aspect='auto')
    
    # 设置标题和坐标轴
    ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('Longitude', fontsize=12)
    ax.set_ylabel('Latitude', fontsize=12)
    
    # 创建图例
    legend_elements = [Patch(facecolor=colors[i], label=labels[i]) for i in range(len(colors))]
    ax.legend(handles=legend_elements, loc='center left', bbox_to_anchor=(1.02, 0.5), 
              fontsize=8, ncol=1)
    
    plt.tight_layout()
    
    # 保存图片
    filename = title.replace(' ', '_').replace('：', '_') + '_updated.png'
    filepath = os.path.join(output_dir, filename)
    plt.savefig(filepath, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"Visualization saved: {filepath}")
    return filepath

def filter_suitable_areas(data, excluded_types, energy_type):
    """筛选适合能源建设的区域"""
    print(f"\n=== Filtering suitable areas for {energy_type} development ===")
    
    # 创建适宜性掩码
    suitable_mask = np.ones_like(data, dtype=bool)
    
    # 排除不适合的土地类型
    for excluded_type in excluded_types:
        suitable_mask[data == excluded_type] = False
    
    # 排除无数据区域
    suitable_mask[data == 0] = False
    
    # 创建适宜性数据
    suitable_data = np.where(suitable_mask, data, 0)
    
    # 统计适宜区域
    total_pixels = np.sum(data != 0)
    suitable_pixels = np.sum(suitable_data != 0)
    suitability_ratio = (suitable_pixels / total_pixels) * 100
    
    print(f"Total valid pixels: {total_pixels:,}")
    print(f"Suitable {energy_type} pixels: {suitable_pixels:,}")
    print(f"Suitability ratio: {suitability_ratio:.2f}%")
    
    # 统计适宜的土地类型
    suitable_types = np.unique(suitable_data[suitable_data != 0])
    print(f"Suitable land types: {sorted(suitable_types)}")
    print(f"Excluded land types: {sorted(excluded_types)}")
    
    return suitable_data, suitability_ratio

def create_suitability_map(suitable_data, transform, output_dir, energy_type, suitability_ratio):
    """创建适宜性地图"""
    title = f"Suitable Land Types for {energy_type} Development (Suitability: {suitability_ratio:.1f}%)"
    
    # 为适宜性地图创建简化的颜色配置
    unique_values = np.unique(suitable_data[suitable_data != 0])
    
    if len(unique_values) == 0:
        print(f"Warning: No suitable land types found for {energy_type}")
        return None
    
    # 使用绿色系列表示适宜区域
    green_colors = plt.cm.Greens(np.linspace(0.3, 1, len(unique_values)))
    colors = ['#ffffff'] + [mcolors.rgb2hex(c) for c in green_colors]  # 白色表示不适宜
    
    # 创建标签
    lc_labels = {
        0: 'No Data',
        10: 'Cropland, rainfed',
        20: 'Cropland, irrigated',
        30: 'Mosaic cropland',
        40: 'Mosaic natural vegetation',
        50: 'Tree cover, broadleaved, evergreen',
        60: 'Tree cover, broadleaved, deciduous',
        70: 'Tree cover, needleleaved, evergreen',
        80: 'Tree cover, needleleaved, deciduous',
        90: 'Tree cover, mixed leaf type',
        100: 'Mosaic tree and shrub',
        110: 'Mosaic herbaceous cover',
        120: 'Shrubland',
        130: 'Grassland',
        140: 'Lichens and mosses',
        150: 'Sparse vegetation',
        160: 'Tree cover, flooded, fresh water',
        170: 'Tree cover, flooded, saline water',
        180: 'Shrub/herbaceous cover, flooded',
        190: 'Urban areas',
        200: 'Bare areas',
        210: 'Water bodies',
        220: 'Permanent snow and ice'
    }
    
    labels = ['Unsuitable'] + [f"{int(v)}: {lc_labels.get(v, 'Unknown')}" for v in unique_values]
    values = [0] + list(unique_values)
    
    # 创建离散颜色映射
    cmap = mcolors.ListedColormap(colors)
    norm = mcolors.BoundaryNorm(boundaries=np.arange(len(values)+1) - 0.5, ncolors=len(colors))
    
    # 创建用于显示的数据
    display_data = np.zeros_like(suitable_data, dtype=float)
    for i, value in enumerate(values):
        display_data[suitable_data == value] = i
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(20, 12))
    
    height, width = suitable_data.shape
    west, south, east, north = rasterio.transform.array_bounds(height, width, transform)
    
    im = ax.imshow(display_data, cmap=cmap, norm=norm,
                   extent=[west, east, south, north],
                   interpolation='nearest', aspect='auto')
    
    ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('Longitude', fontsize=12)
    ax.set_ylabel('Latitude', fontsize=12)
    
    # 创建图例
    legend_elements = [Patch(facecolor=colors[i], label=labels[i]) for i in range(len(colors))]
    ax.legend(handles=legend_elements, loc='center left', bbox_to_anchor=(1.02, 0.5),
              fontsize=10, ncol=1)
    
    plt.tight_layout()
    
    # 保存图片
    filename = f"{energy_type}_suitability_map_updated.png"
    filepath = os.path.join(output_dir, filename)
    plt.savefig(filepath, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"Suitability map saved: {filepath}")
    return filepath

def save_reclassified_data(reclassified_data, transform, output_file):
    """保存重新归类后的数据"""
    print(f"\n=== Saving reclassified data ===")
    
    height, width = reclassified_data.shape
    
    profile = {
        'driver': 'GTiff',
        'height': height,
        'width': width,
        'count': 1,
        'dtype': reclassified_data.dtype,
        'crs': 'EPSG:4326',
        'transform': transform,
        'nodata': 0,
        'compress': 'lzw'
    }
    
    with rasterio.open(output_file, 'w', **profile) as dst:
        dst.write(reclassified_data, 1)
    
    print(f"Reclassified data saved: {output_file}")

def create_statistics_report(original_data, reclassified_data, solar_data, wind_data, 
                           solar_ratio, wind_ratio, reports_dir):
    """创建统计报告"""
    print("\n=== Creating statistics report ===")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = os.path.join(reports_dir, f'landcover_reclassification_report_updated_{timestamp}.txt')
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("Land Cover Data Reclassification and Suitability Analysis Report\n")
        f.write("="*60 + "\n")
        f.write(f"Analysis time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # 原始数据统计
        original_unique = np.unique(original_data[original_data != 0])
        f.write(f"Original data statistics:\n")
        f.write(f"- Unique land cover types: {len(original_unique)}\n")
        f.write(f"- Land cover types: {sorted(original_unique)}\n\n")
        
        # 重新归类后统计
        reclassified_unique = np.unique(reclassified_data[reclassified_data != 0])
        f.write(f"Reclassified data statistics:\n")
        f.write(f"- Unique land cover types: {len(reclassified_unique)}\n")
        f.write(f"- Land cover types: {sorted(reclassified_unique)}\n\n")
        
        # 适宜性统计
        f.write(f"Suitability analysis results:\n")
        f.write(f"- Solar development suitability ratio: {solar_ratio:.2f}%\n")
        f.write(f"- Wind development suitability ratio: {wind_ratio:.2f}%\n\n")
        
        # 适宜的土地类型
        solar_types = np.unique(solar_data[solar_data != 0])
        wind_types = np.unique(wind_data[wind_data != 0])
        f.write(f"Suitable land types for solar development: {sorted(solar_types)}\n")
        f.write(f"Suitable land types for wind development: {sorted(wind_types)}\n")
    
    print(f"Statistics report saved: {report_file}")

def main():
    """主函数"""
    print("Starting land cover data reclassification and suitability analysis...")
    print("="*60)
    
    try:
        # 设置目录
        outputs_dir, visualizations_dir, processed_dir, reports_dir = setup_directories()
        
        # 读取已生成的0.5度分辨率土地覆盖数据
        tif_file = os.path.join(processed_dir, 'global_landcover_05deg_analysis.tif')
        
        if not os.path.exists(tif_file):
            print(f"Error: Input file not found {tif_file}")
            print("Please run landcover_raw_analysis.py first to generate 0.5-degree resolution data")
            return
        
        print(f"Reading data file: {tif_file}")
        
        with rasterio.open(tif_file) as src:
            original_data = src.read(1)
            transform = src.transform
            
        print(f"Data shape: {original_data.shape}")
        print(f"Original unique values count: {len(np.unique(original_data[original_data != 0]))}")
        
        # 1. 重新归类土地类型
        reclassified_data = reclassify_landcover(original_data)
        
        # 保存重新归类后的数据
        reclassified_file = os.path.join(processed_dir, 'global_landcover_05deg_reclassified_updated.tif')
        save_reclassified_data(reclassified_data, transform, reclassified_file)
        
        # 2. 创建全球土地覆盖类型可视化
        global_vis_file = visualize_global_landcover(
            reclassified_data, transform, visualizations_dir, 
            "Global Land Cover Distribution (Reclassified)"
        )
        
        # 3. 定义适宜性筛选标准
        # 光伏：排除210, 180, 220, 50-100, 10-30, 160, 170
        solar_excluded = [210, 180, 220, 50, 60, 70, 80, 90, 100, 10, 20, 30, 160, 170]
        
        # 陆上风电：排除210, 190, 220, 50-100, 160, 170
        wind_excluded = [210, 190, 220, 50, 60, 70, 80, 90, 100, 160, 170]
        
        # 4. 筛选适合光伏建设的区域
        solar_suitable_data, solar_ratio = filter_suitable_areas(
            reclassified_data, solar_excluded, "Solar"
        )
        
        # 5. 筛选适合风电建设的区域
        wind_suitable_data, wind_ratio = filter_suitable_areas(
            reclassified_data, wind_excluded, "Onshore Wind"
        )
        
        # 6. 创建适宜性可视化
        solar_vis_file = create_suitability_map(
            solar_suitable_data, transform, visualizations_dir, "Solar", solar_ratio
        )
        
        wind_vis_file = create_suitability_map(
            wind_suitable_data, transform, visualizations_dir, "Onshore Wind", wind_ratio
        )
        
        # 7. 保存适宜性数据
        solar_file = os.path.join(processed_dir, 'solar_suitable_areas_05deg_updated.tif')
        wind_file = os.path.join(processed_dir, 'wind_suitable_areas_05deg_updated.tif')
        
        save_reclassified_data(solar_suitable_data, transform, solar_file)
        save_reclassified_data(wind_suitable_data, transform, wind_file)
        
        # 8. 创建统计报告
        create_statistics_report(
            original_data, reclassified_data, solar_suitable_data, wind_suitable_data,
            solar_ratio, wind_ratio, reports_dir
        )
        
        print("\n" + "="*60)
        print("Land cover data reclassification and suitability analysis completed!")
        print("="*60)
        print("Generated files include:")
        print(f"- Reclassified data: {reclassified_file}")
        print(f"- Global land cover visualization: {global_vis_file}")
        print(f"- Solar suitability data: {solar_file}")
        print(f"- Wind suitability data: {wind_file}")
        if solar_vis_file:
            print(f"- Solar suitability visualization: {solar_vis_file}")
        if wind_vis_file:
            print(f"- Wind suitability visualization: {wind_vis_file}")
        print("="*60)
        
    except Exception as e:
        print(f"Error during analysis: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 