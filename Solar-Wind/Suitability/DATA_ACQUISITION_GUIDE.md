# 数据获取指南
## Data Acquisition Guide

本文档详细说明如何获取和配置可再生能源适宜性评估所需的大型数据文件。

## 📋 数据清单

### 必需数据文件

| 数据类型 | 文件名 | 大小 | 来源 | 用途 |
|----------|--------|------|------|------|
| 地形数据 | GMTED2010_*.tif | ~15GB | USGS | 坡度计算 |
| 土地覆盖 | ESACCI-LC-*.nc | ~1GB/年 | ESA CCI | 土地类型筛选 |
| 保护区 | WDPA_*.gdb | ~2GB | UNEP-WCMC | 保护区排除 |
| 海洋边界 | World_EEZ_v10.shp | ~500MB | Marine Regions | EEZ约束 |
| 水深数据 | GEBCO_2024.nc | ~13GB | GEBCO | 海上风电水深约束 |

## 🌍 地形数据 (GMTED2010)

### 数据描述
- **全称**: Global Multi-resolution Terrain Elevation Data 2010
- **分辨率**: 7.5弧秒 (~250米)
- **覆盖**: 全球陆地区域
- **格式**: GeoTIFF
- **坐标系**: WGS84

### 下载步骤

1. **访问USGS官网**:
   ```
   https://www.usgs.gov/coastal-changes-and-impacts/gmted2010
   ```

2. **选择数据产品**:
   - 推荐: `GMTED2010 Mean Elevation (7.5 arc-seconds)`
   - 文件格式: GeoTIFF
   - 投影: Geographic (WGS84)

3. **下载方式**:
   ```bash
   # 方法1: 直接下载
   wget https://cloud.sdsc.edu/v1/AUTH_opentopography/Raster/GMTED2010/GMTED2010_mn75_grd.zip
   
   # 方法2: 使用OpenTopography API
   # 注册账户: https://portal.opentopography.org/
   ```

4. **文件放置**:
   ```
   Solar-Wind/Suitability/data/terrain/
   ├── GMTED2010_mn75_grd/
   │   ├── mn75_grd/
   │   └── *.tif
   ```

## 🌱 土地覆盖数据 (ESA CCI)

### 数据描述
- **全称**: ESA Climate Change Initiative Land Cover
- **分辨率**: 300米
- **时间范围**: 1992-2020年
- **格式**: NetCDF4
- **分类系统**: UN-LCCS

### 下载步骤

1. **访问ESA CCI官网**:
   ```
   https://www.esa-landcover-cci.org/
   ```

2. **数据下载页面**:
   ```
   https://cds.climate.copernicus.eu/cdsapp#!/dataset/satellite-land-cover
   ```

3. **推荐下载**:
   ```bash
   # 最新年份数据 (2020)
   ESACCI-LC-L4-LCCS-Map-300m-P1Y-2020-v2.1.1.nc
   
   # 历史数据 (可选)
   ESACCI-LC-L4-LCCS-Map-300m-P1Y-2015-v2.1.1.nc
   ESACCI-LC-L4-LCCS-Map-300m-P1Y-2010-v2.1.1.nc
   ```

4. **文件放置**:
   ```
   Solar-Wind/Suitability/data/landcover/
   ├── ESACCI-LC-L4-LCCS-Map-300m-P1Y-2020-v2.1.1.nc
   └── [其他年份文件]
   ```

## 🛡️ 保护区数据 (WDPA)

### 数据描述
- **全称**: World Database on Protected Areas
- **更新频率**: 月度更新
- **格式**: File Geodatabase (.gdb)
- **内容**: 全球陆地和海洋保护区

### 下载步骤

1. **访问Protected Planet**:
   ```
   https://www.protectedplanet.net/
   ```

2. **下载页面**:
   ```
   https://www.protectedplanet.net/en/thematic-areas/wdpa
   ```

3. **下载选项**:
   ```bash
   # 全球数据包 (推荐)
   WDPA_[MMYYYY]_Public.gdb.zip
   
   # 按国家下载 (可选)
   WDPA_[MMYYYY]_[COUNTRY]_shp.zip
   ```

4. **文件放置**:
   ```
   Solar-Wind/Suitability/data/protected_areas/
   ├── WDPA_Jan2024_Public.gdb/
   └── [解压后的文件]
   ```

## 🌊 海洋数据

### EEZ专属经济区数据

1. **数据来源**: Flanders Marine Institute
   ```
   https://www.marineregions.org/downloads.php
   ```

2. **下载文件**:
   ```bash
   # 世界EEZ边界 v10
   World_EEZ_v10_20180221.zip
   ```

3. **文件放置**:
   ```
   Solar-Wind/Suitability/data/eez/
   ├── World_EEZ_v10.shp
   ├── World_EEZ_v10.dbf
   ├── World_EEZ_v10.shx
   └── World_EEZ_v10.prj
   ```

### GEBCO水深数据

1. **数据来源**: General Bathymetric Chart of the Oceans
   ```
   https://www.gebco.net/data_and_products/gridded_bathymetry_data/
   ```

2. **下载最新版本**:
   ```bash
   # GEBCO 2024 Grid
   https://www.bodc.ac.uk/data/open_download/gebco/gebco_2024/zip/
   ```

3. **文件放置**:
   ```
   Solar-Wind/Suitability/data/bathymetry/
   └── GEBCO_2024.nc
   ```

## 🔧 数据预处理

### 自动化下载脚本

创建 `download_data.py`:

```python
#!/usr/bin/env python
"""
自动化数据下载脚本
"""

import os
import requests
import zipfile
from pathlib import Path

def download_file(url, local_path):
    """下载文件"""
    print(f"下载: {url}")
    response = requests.get(url, stream=True)
    
    with open(local_path, 'wb') as f:
        for chunk in response.iter_content(chunk_size=8192):
            f.write(chunk)
    
    print(f"完成: {local_path}")

def setup_data_directories():
    """创建数据目录结构"""
    directories = [
        'data/terrain',
        'data/landcover', 
        'data/protected_areas',
        'data/eez',
        'data/bathymetry'
    ]
    
    for dir_path in directories:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        print(f"创建目录: {dir_path}")

if __name__ == "__main__":
    setup_data_directories()
    print("数据目录结构创建完成")
    print("请手动下载大型数据文件，参考 DATA_ACQUISITION_GUIDE.md")
```

### 数据验证脚本

创建 `validate_data.py`:

```python
#!/usr/bin/env python
"""
数据完整性验证脚本
"""

import os
from pathlib import Path

def validate_data_files():
    """验证必需数据文件是否存在"""
    
    required_files = {
        'terrain': ['GMTED2010_*.tif'],
        'landcover': ['ESACCI-LC-*.nc'],
        'protected_areas': ['WDPA_*.gdb'],
        'eez': ['World_EEZ_v10.shp'],
        'bathymetry': ['GEBCO_2024.nc']
    }
    
    missing_files = []
    
    for category, patterns in required_files.items():
        data_dir = Path(f'data/{category}')
        
        if not data_dir.exists():
            missing_files.append(f"目录不存在: data/{category}")
            continue
        
        for pattern in patterns:
            files = list(data_dir.glob(pattern))
            if not files:
                missing_files.append(f"文件缺失: data/{category}/{pattern}")
    
    if missing_files:
        print("❌ 数据验证失败:")
        for file in missing_files:
            print(f"  - {file}")
        return False
    else:
        print("✅ 所有必需数据文件已就位")
        return True

if __name__ == "__main__":
    validate_data_files()
```

## 📊 存储空间规划

### 磁盘空间需求

```
总计: ~32GB

详细分解:
├── 地形数据 (GMTED2010): ~15GB
├── 水深数据 (GEBCO): ~13GB
├── 土地覆盖 (ESA CCI): ~1GB
├── 保护区 (WDPA): ~2GB
├── 海洋边界 (EEZ): ~500MB
└── 处理结果和缓存: ~500MB
```

### 优化建议

1. **分区域下载**: 如果只分析特定区域，可以下载区域数据
2. **数据压缩**: 使用压缩格式存储不常用数据
3. **云存储**: 考虑使用云存储服务存储大型数据文件
4. **定期清理**: 删除不需要的中间处理文件

## 🚨 常见问题

### Q1: 下载速度慢怎么办？
**A**: 
- 使用下载管理器支持断点续传
- 选择离您较近的镜像站点
- 避开网络高峰时段下载

### Q2: 文件损坏怎么办？
**A**:
- 验证文件MD5/SHA256校验和
- 重新下载损坏的文件
- 检查磁盘空间是否充足

### Q3: 数据版本如何选择？
**A**:
- 优先使用最新版本数据
- 确保所有数据的时间基准一致
- 查看数据更新日志了解变化

### Q4: 如何处理网络限制？
**A**:
- 使用机构网络或VPN
- 联系数据提供方申请离线拷贝
- 寻找数据镜像站点

## 📞 技术支持

如果在数据获取过程中遇到问题：

1. **查看官方文档**: 各数据源都有详细的用户指南
2. **联系数据提供方**: 大多数机构提供技术支持
3. **社区论坛**: 在相关学术论坛寻求帮助
4. **创建Issue**: 在本项目仓库创建问题报告

---

**重要提示**: 请遵守各数据源的使用条款和引用要求。部分数据可能需要注册账户或申请许可。
