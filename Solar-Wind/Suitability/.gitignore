# Solar-Wind Suitability Assessment - Git Ignore File

# Large source data files (>100MB)
# These should be downloaded separately as described in README.md
data/raw/
data/source/
data/external/

# Large processed data files (>10MB)
outputs/processed_data/temp/global_elevation_*.tif
outputs/processed_data/temp/global_slope_*.tif
outputs/processed_data/processed_WDPA_data/protected_areas_polygons.*
outputs/processed_data/processed_WDPA_data/*.shp
outputs/processed_data/processed_WDPA_data/*.dbf
outputs/processed_data/processed_WDPA_data/*.shx
outputs/processed_data/processed_WDPA_data/*.prj

# Large output result files (>10MB)
outputs/final_suitability/*.csv
outputs/final_suitability/*.xlsx
outputs/final_suitability/*.tif
outputs/processed_data/*.tif
outputs/processed_data/*.nc

# Temporary and cache files
*.tmp
*.temp
*~
.DS_Store
Thumbs.db

# Python cache files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Jupyter Notebook
.ipynb_checkpoints

# Log files
*.log
logs/

# Large visualization files (keep small examples)
outputs/visualizations/*_large.png
outputs/visualizations/*_full_resolution.png

# Backup files
*.bak
*.backup

# System files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Large raster files
*.tif
*.tiff
*.nc
*.hdf
*.h5

# Large vector files
*.shp
*.dbf
*.shx
*.prj
*.cpg

# Compressed archives of large data
*.zip
*.tar.gz
*.rar
*.7z

# Keep small example files and documentation
!examples/
!docs/
!README.md
!*.py
!*.md
!*.txt
!*.rst
!*.json
!*.yml
!*.yaml

# Keep small output examples (override the above exclusions for specific small files)
!outputs/visualizations/example_*.png
!outputs/reports/*.txt
!outputs/reports/*.md
