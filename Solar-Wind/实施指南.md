# Solar-Wind系统实施指南

## 环境状态确认 ✅

conda环境 `solar-wind` 已成功创建并配置：
- Python 3.9
- 核心科学计算包已安装
- 基础测试 8/8 通过

## 第一步：验证完整依赖包安装

首先检查是否还有缺失的依赖包：

```bash
# 激活环境
source /opt/anaconda3/etc/profile.d/conda.sh
conda activate solar-wind

# 进入项目目录
cd Solar-Wind

# 尝试导入所有模块
python -c "
try:
    import numpy, scipy, pandas, xarray, netcdf4
    import matplotlib, seaborn, cartopy
    import rasterio, geopandas
    print('✅ 核心包导入成功')
except ImportError as e:
    print('❌ 缺少依赖包:', e)
"
```

## 第二步：运行模块导入测试

测试我们的自定义模块是否可以正常导入：

```bash
python -c "
import sys
sys.path.append('.')

try:
    from config import *
    from data_processor import DataProcessor
    from wind_assessment import WindAssessment
    from solar_assessment import SolarAssessment
    print('✅ 所有自定义模块导入成功')
except ImportError as e:
    print('❌ 模块导入失败:', e)
"
```

## 第三步：创建示例数据进行测试

运行示例数据生成和基础功能测试：

```bash
python example_usage.py
```

## 第四步：运行完整测试套件

如果依赖包都安装好了，运行完整测试：

```bash
python test_system.py
```

## 第五步：准备真实数据（可选）

如果您有真实数据，请按以下结构组织：

```
data/
├── era5_data.nc          # ERA5气象数据
├── land_cover.tif        # 土地覆盖数据
├── elevation.tif         # 高程数据
├── protected_areas.shp   # 保护区数据
├── eez_boundaries.shp    # EEZ边界数据
├── bathymetry.tif        # 水深数据
└── marine_protected.shp  # 海洋保护区数据
```

## 第六步：运行完整评估（如有数据）

```bash
python main_assessment.py
```

## 常见问题排查

### 1. 依赖包问题
如果遇到导入错误，安装缺失的包：
```bash
conda install -c conda-forge [package_name]
# 或
pip install [package_name]
```

### 2. 内存问题
如果数据太大导致内存不足：
- 减小数据规模进行测试
- 调整config.py中的chunk_size参数

### 3. 数据格式问题
确保数据格式符合要求：
- NetCDF格式的气象数据
- GeoTIFF格式的栅格数据
- Shapefile格式的矢量数据

## 下一步计划

1. **立即执行**：运行上述测试步骤
2. **数据准备**：获取或准备测试数据
3. **功能验证**：逐步测试各个模块
4. **完整评估**：运行端到端评估流程
