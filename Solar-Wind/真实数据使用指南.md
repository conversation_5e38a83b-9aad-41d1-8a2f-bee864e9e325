# 🌍 真实数据使用指南

## 📋 回答您的问题

### ❓ 使用真实数据应该用哪个程序？

**答案**: 使用 **`real_data_assessment.py`** 

### 📊 程序功能对比

| 程序 | 数据类型 | 国家级聚合 | 8760小时 | 推荐用途 |
|------|----------|------------|----------|----------|
| `global_assessment.py` | ❌ 合成数据 | ❌ | ❌ | 仅演示 |
| `main_assessment.py` | ✅ 真实数据 | ❌ | ✅ | 基础评估 |
| **`real_data_assessment.py`** | ✅ **真实数据** | ✅ | ✅ | **生产环境** |

## 🚀 使用真实数据的完整流程

### 第一步：数据准备

```bash
# 1. 运行数据下载工具
cd Solar-Wind
python download_real_data.py
```

这将：
- ✅ 创建完整的数据目录结构
- ✅ 生成ERA5下载脚本 (`download_era5.py`)
- ✅ 生成数据路径配置文件 (`data_paths_config.py`)
- ✅ 提供详细的数据下载说明

### 第二步：下载ERA5数据

```bash
# 1. 安装cdsapi
pip install cdsapi

# 2. 注册CDS账户并配置API密钥
# https://cds.climate.copernicus.eu/user/register

# 3. 运行ERA5下载脚本
python download_era5.py
```

### 第三步：下载GIS数据

根据 `download_real_data.py` 输出的说明，从以下数据源下载：

1. **国家边界**: Natural Earth (自动下载)
2. **土地覆盖**: ESA CCI Land Cover
3. **地形数据**: SRTM/GMTED
4. **保护区**: WDPA
5. **EEZ边界**: Maritime Boundaries
6. **水深数据**: GEBCO

### 第四步：运行真实数据评估

```python
# 方法1: 使用配置文件
python data_paths_config.py

# 方法2: 自定义路径
from real_data_assessment import RealDataAssessment

# 配置真实数据路径
data_paths = {
    'era5': 'data/meteorological/era5_global_2020.nc'
}

gis_paths = {
    'land_cover': 'data/gis/land_cover/esa_cci_2020.tif',
    'elevation': 'data/gis/topography/srtm_global.tif',
    'protected_areas': 'data/gis/protected_areas/wdpa_global.shp',
    'eez': 'data/gis/marine/eez_boundaries.shp',
    'bathymetry': 'data/gis/marine/bathymetry.tif',
    'marine_protected_areas': 'data/gis/marine/marine_protected.shp'
}

# 创建评估实例
assessment = RealDataAssessment(
    output_dir="real_assessment_results",
    country_boundaries_file="data/gis/countries/ne_50m_admin_0_countries.shp"
)

# 运行评估
results = assessment.run_real_data_assessment(
    data_paths=data_paths,
    gis_paths=gis_paths
)
```

## 📊 输出结果确认

### ✅ 国家级装机容量输出

**文件**: `real_assessment_results/country_results/country_capacity_summary.csv`

```csv
Country,solar_Capacity_MW,wind_onshore_Capacity_MW,wind_offshore_Capacity_MW,Total_Capacity_GW
China,1234567,2345678,3456789,7037
USA,987654,1876543,2765432,5630
India,876543,1234567,456789,2568
...
```

### ✅ 国家级容量因子输出

**文件**: `real_assessment_results/country_results/country_capacity_factor_summary.csv`

```csv
Country,solar_Average_CF,wind_onshore_Average_CF,wind_offshore_Average_CF
China,0.156,0.234,0.387
USA,0.198,0.267,0.421
India,0.187,0.198,0.356
...
```

### ✅ 详细结果

**文件**: `real_assessment_results/country_results/detailed_country_results.json`

包含每个国家的：
- 装机容量 (MW, GW, TW)
- 年发电量 (GWh, TWh, PWh)
- 适宜面积 (km², 千km²)
- 加权平均容量因子
- 8760小时容量因子时间序列

## 🔍 数据质量验证

`real_data_assessment.py` 包含完整的数据质量检查：

### ERA5数据验证
- ✅ 文件存在性检查
- ✅ 必需变量检查 (u10, v10, ssrd, t2m)
- ✅ 时间步数验证 (8760小时)
- ✅ 空间覆盖验证 (全球范围)
- ✅ 数据完整性检查

### GIS数据验证
- ✅ 文件格式验证
- ✅ 空间参考系统检查
- ✅ 数据范围验证

## 📁 完整输出结构

```
real_assessment_results/
├── country_results/                    # 国家级结果
│   ├── country_capacity_summary.csv    # 装机容量汇总
│   ├── country_capacity_factor_summary.csv  # 容量因子汇总
│   └── detailed_country_results.json   # 详细结果
├── results/                            # 全球网格结果
│   ├── solar_capacity_factors.nc       # 太阳能容量因子
│   ├── wind_onshore_capacity_factors.nc # 陆上风电容量因子
│   ├── wind_offshore_capacity_factors.nc # 海上风电容量因子
│   ├── solar_technical_potential.nc    # 太阳能技术潜力
│   ├── wind_onshore_technical_potential.nc # 陆上风电技术潜力
│   └── wind_offshore_technical_potential.nc # 海上风电技术潜力
├── validation/                         # 验证报告
│   ├── solar_validation.json
│   ├── wind_onshore_validation.json
│   └── wind_offshore_validation.json
└── real_data_assessment_report.txt     # 评估报告
```

## ⚡ 快速开始（如果已有数据）

如果您已经有真实的ERA5和GIS数据：

```bash
# 1. 修改数据路径
vim data_paths_config.py

# 2. 直接运行评估
python data_paths_config.py
```

## 🔧 技术特点确认

### ✅ 完全符合要求的功能

1. **三种技术装机容量**:
   - 海上风电: Vestas 8.0MW (基于Wang et al.)
   - 陆上风电: GE 2.5MW (基于Zheng et al.)
   - 太阳能: 16.19%效率 (基于Zheng et al.)

2. **8760小时容量因子**:
   - 基于真实ERA5小时级数据
   - 按装机容量加权的国家级平均
   - 完整时间序列输出

3. **GIS约束筛选**:
   - 1°×1°网格化处理
   - 坡度、保护区、EEZ等完整约束
   - 国家边界内网格聚合

4. **标准化输出**:
   - CSV格式国家级汇总表
   - JSON格式详细结果
   - NetCDF格式网格数据

## 📈 数据需求总结

### 必需数据
1. **ERA5气象数据** (~50GB/年)
   - u10, v10, ssrd, t2m
   - 8760小时完整时间序列
   - 全球0.25°分辨率

2. **国家边界数据** (~50MB)
   - Natural Earth shapefile
   - 包含国家名称属性

### 可选数据（提高精度）
3. **GIS约束数据** (~10GB)
   - 土地覆盖、地形、保护区
   - EEZ边界、水深数据

## 🎯 总结

**使用真实数据进行全球可再生能源评估的正确程序是 `real_data_assessment.py`**

这个程序：
- ✅ 专门设计用于处理真实ERA5和GIS数据
- ✅ 包含完整的数据质量验证
- ✅ 支持国家级聚合和8760小时容量因子
- ✅ 输出标准化的CSV和JSON格式结果
- ✅ 严格遵循Nature文献方法学

**立即可用**: 系统已完全准备就绪，只需要下载真实数据即可开始评估！
