#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
容量因子评估系统配置文件
Capacity Factor Assessment System Configuration
"""

import numpy as np
from datetime import datetime

# =============================================================================
# 基本系统配置 | Basic System Configuration
# =============================================================================

# 空间分辨率配置
SPATIAL_CONFIG = {
    'target_resolution': 0.5,  # 目标分辨率（度）
    'crs': 'EPSG:4326',       # 坐标参考系统
    'global_bounds': (-180, -90, 180, 90),  # 全球范围
    'grid_width': 720,        # 网格宽度（像素）
    'grid_height': 360,       # 网格高度（像素）
}

# 时间配置
TEMPORAL_CONFIG = {
    'reference_year': 2022,   # 参考年份
    'hours_per_year': 8760,   # 年小时数
    'time_zone': 'UTC',       # 时区
}

# =============================================================================
# ERA5数据配置 | ERA5 Data Configuration
# =============================================================================

# ERA5数据变量
ERA5_VARIABLES = {
    # 太阳能相关变量
    'solar': [
        'surface_solar_radiation_downwards',  # 地表太阳辐射 (J/m²)
        '2m_temperature',                     # 2米温度 (K)
        'surface_pressure',                   # 地表气压 (Pa)
        'total_cloud_cover',                  # 总云量 (0-1)
    ],
    # 风能相关变量
    'wind': [
        '10m_u_component_of_wind',           # 10米u风分量 (m/s)
        '10m_v_component_of_wind',           # 10米v风分量 (m/s)
        '100m_u_component_of_wind',          # 100米u风分量 (m/s)
        '100m_v_component_of_wind',          # 100米v风分量 (m/s)
        '2m_temperature',                    # 2米温度 (K)
        'surface_pressure',                  # 地表气压 (Pa)
    ]
}

# ERA5数据下载配置
ERA5_DOWNLOAD_CONFIG = {
    'product_type': 'reanalysis',
    'format': 'netcdf',
    'area': [90, -180, -90, 180],  # North, West, South, East
    'grid': [0.25, 0.25],          # ERA5原始分辨率
    'time': [f'{i:02d}:00' for i in range(24)],  # 24小时
    'year': '2022',
    'month': [f'{i:02d}' for i in range(1, 13)],  # 12个月
    'day': [f'{i:02d}' for i in range(1, 32)],    # 31天
}

# =============================================================================
# 空间采样策略配置 | Spatial Sampling Strategy Configuration
# =============================================================================

# 采样策略参数
SAMPLING_CONFIG = {
    'strategy': 'adaptive_grid',  # 采样策略类型
    'base_samples_per_grid': 9,   # 每个0.5°网格的基础采样点数（3x3）
    'max_samples_per_grid': 25,   # 最大采样点数（5x5）
    'min_samples_per_grid': 4,    # 最小采样点数（2x2）
    
    # 地形复杂度阈值
    'terrain_complexity_threshold': {
        'low': 50,     # 低复杂度：标准差 < 50m
        'medium': 200, # 中等复杂度：50m <= 标准差 < 200m
        'high': 200,   # 高复杂度：标准差 >= 200m
    },
    
    # 海陆分布考虑
    'land_sea_consideration': True,
    'coastal_buffer_km': 10,  # 海岸线缓冲区（公里）
    
    # 采样点分布策略
    'sampling_pattern': 'regular_grid',  # 规则网格采样
    'edge_buffer_ratio': 0.1,           # 边缘缓冲比例
}

# =============================================================================
# 太阳能光伏配置 | Solar PV Configuration
# =============================================================================

SOLAR_PV_CONFIG = {
    # 技术参数
    'technology': {
        'panel_type': 'crystalline_silicon',  # 晶硅电池板
        'nominal_efficiency': 0.20,           # 标称效率 (20%)
        'temperature_coefficient': -0.004,    # 温度系数 (%/°C)
        'reference_temperature': 25.0,        # 参考温度 (°C)
        'reference_irradiance': 1000.0,       # 参考辐照度 (W/m²)
        'noct': 45.0,                         # 标称工作温度 (°C)
    },
    
    # 系统参数
    'system': {
        'dc_ac_ratio': 1.2,                   # 直流交流比
        'inverter_efficiency': 0.96,          # 逆变器效率
        'system_losses': 0.14,                # 系统损失 (14%)
        'soiling_losses': 0.02,               # 污染损失 (2%)
        'shading_losses': 0.03,               # 阴影损失 (3%)
    },
    
    # 安装参数
    'installation': {
        'tilt_angle': 'optimal',              # 倾斜角（最优）
        'azimuth_angle': 180,                 # 方位角（南向）
        'tracking': False,                    # 是否跟踪
    },
    
    # 计算参数
    'calculation': {
        'min_irradiance_threshold': 10,      # 最小辐照度阈值 (W/m²)
        'max_temperature': 85,               # 最大工作温度 (°C)
        'min_temperature': -40,              # 最小工作温度 (°C)
    }
}

# =============================================================================
# 陆上风电配置 | Onshore Wind Configuration
# =============================================================================

WIND_CONFIG = {
    # 风机技术参数
    'turbine': {
        'model': 'Generic_2.5MW',            # 风机型号
        'rated_power': 2.5,                  # 额定功率 (MW)
        'hub_height': 100,                   # 轮毂高度 (m)
        'rotor_diameter': 112,               # 叶轮直径 (m)
        'cut_in_speed': 3.0,                 # 切入风速 (m/s)
        'rated_speed': 12.0,                 # 额定风速 (m/s)
        'cut_out_speed': 25.0,               # 切出风速 (m/s)
    },
    
    # 功率曲线参数（简化模型）
    'power_curve': {
        'type': 'analytical',               # 解析模型
        'shape_parameter': 2.0,             # 形状参数
        'scale_factor': 1.0,                # 尺度因子
    },
    
    # 环境修正参数
    'environmental': {
        'reference_air_density': 1.225,     # 参考空气密度 (kg/m³)
        'reference_temperature': 15.0,      # 参考温度 (°C)
        'reference_pressure': 101325,       # 参考气压 (Pa)
    },
    
    # 系统损失
    'losses': {
        'wake_losses': 0.08,                # 尾流损失 (8%)
        'electrical_losses': 0.03,          # 电气损失 (3%)
        'availability': 0.97,               # 可用率 (97%)
        'maintenance_downtime': 0.02,       # 维护停机 (2%)
    },
    
    # 计算参数
    'calculation': {
        'wind_speed_bins': np.arange(0, 30.1, 0.5),  # 风速分档
        'extrapolation_method': 'power_law',          # 外推方法
        'power_law_exponent': 0.143,                  # 幂律指数
    }
}

# =============================================================================
# 质量控制配置 | Quality Control Configuration
# =============================================================================

QC_CONFIG = {
    # 数据范围检查
    'data_ranges': {
        'solar_irradiance': (0, 1500),      # 太阳辐射范围 (W/m²)
        'temperature': (200, 330),          # 温度范围 (K)
        'wind_speed': (0, 50),              # 风速范围 (m/s)
        'pressure': (50000, 110000),        # 气压范围 (Pa)
        'cloud_cover': (0, 1),              # 云量范围
    },
    
    # 容量因子合理性检查
    'cf_ranges': {
        'solar_min': 0.05,                  # 太阳能最小CF
        'solar_max': 0.35,                  # 太阳能最大CF
        'wind_min': 0.10,                   # 风电最小CF
        'wind_max': 0.60,                   # 风电最大CF
    },
    
    # 异常值检测
    'outlier_detection': {
        'method': 'iqr',                    # 四分位距方法
        'iqr_multiplier': 1.5,              # IQR倍数
        'max_outlier_ratio': 0.05,          # 最大异常值比例
    },
    
    # 时间序列检查
    'temporal_checks': {
        'max_consecutive_missing': 24,      # 最大连续缺失小时数
        'max_total_missing_ratio': 0.02,    # 最大总缺失比例
        'seasonal_consistency_check': True,  # 季节一致性检查
    }
}

# =============================================================================
# 输出配置 | Output Configuration
# =============================================================================

OUTPUT_CONFIG = {
    # 文件格式
    'formats': {
        'raster': 'GeoTIFF',               # 栅格格式
        'vector': 'Shapefile',             # 矢量格式
        'table': 'CSV',                    # 表格格式
        'metadata': 'JSON',                # 元数据格式
    },
    
    # 压缩设置
    'compression': {
        'raster_compress': 'LZW',          # 栅格压缩
        'raster_tiled': True,              # 栅格分块
        'csv_compression': None,           # CSV压缩
    },
    
    # 精度设置
    'precision': {
        'capacity_factor': 4,              # 容量因子小数位数
        'coordinates': 6,                  # 坐标小数位数
        'meteorological': 2,               # 气象数据小数位数
    },
    
    # 输出产品
    'products': {
        'hourly_cf': True,                 # 逐小时容量因子
        'monthly_cf': True,                # 月平均容量因子
        'annual_cf': True,                 # 年平均容量因子
        'seasonal_cf': True,               # 季节容量因子
        'statistics': True,                # 统计信息
        'quality_flags': True,             # 质量标志
    }
}

# =============================================================================
# 验证配置 | Validation Configuration
# =============================================================================

VALIDATION_CONFIG = {
    # 参考数据源
    'reference_data': {
        'solar_cf_global_range': (0.10, 0.30),    # 全球太阳能CF范围
        'wind_cf_global_range': (0.15, 0.45),     # 全球风电CF范围
        'known_high_solar_regions': [              # 已知高太阳能区域
            'sahara', 'atacama', 'australian_outback'
        ],
        'known_high_wind_regions': [               # 已知高风能区域
            'north_sea', 'great_plains', 'patagonia'
        ],
    },
    
    # 验证方法
    'validation_methods': {
        'spatial_correlation': True,       # 空间相关性检查
        'temporal_correlation': True,      # 时间相关性检查
        'physical_consistency': True,      # 物理一致性检查
        'literature_comparison': True,     # 文献对比
    },
    
    # 不确定性分析
    'uncertainty_analysis': {
        'monte_carlo_runs': 1000,          # 蒙特卡洛运行次数
        'parameter_uncertainty': {         # 参数不确定性
            'solar_efficiency': 0.02,      # 太阳能效率不确定性
            'wind_power_curve': 0.05,      # 风电功率曲线不确定性
            'meteorological_data': 0.10,   # 气象数据不确定性
        }
    }
}

# =============================================================================
# 计算资源配置 | Computational Resource Configuration
# =============================================================================

COMPUTE_CONFIG = {
    # 并行处理
    'parallel_processing': {
        'enable': True,                    # 启用并行处理
        'max_workers': 8,                  # 最大工作进程数
        'chunk_size': 100,                 # 数据块大小
        'memory_limit_gb': 16,             # 内存限制 (GB)
    },
    
    # 缓存设置
    'caching': {
        'enable_cache': True,              # 启用缓存
        'cache_size_gb': 4,                # 缓存大小 (GB)
        'cache_location': 'temp',          # 缓存位置
    },
    
    # 进度监控
    'monitoring': {
        'progress_bar': True,              # 显示进度条
        'log_level': 'INFO',               # 日志级别
        'save_intermediate': True,         # 保存中间结果
    }
}

# =============================================================================
# 辅助函数 | Utility Functions
# =============================================================================

def get_sampling_points(grid_center_lat, grid_center_lon, complexity_level='medium'):
    """
    根据网格中心和复杂度级别生成采样点
    
    Parameters:
    -----------
    grid_center_lat : float
        网格中心纬度
    grid_center_lon : float
        网格中心经度
    complexity_level : str
        复杂度级别 ('low', 'medium', 'high')
    
    Returns:
    --------
    list of tuples
        采样点坐标列表 [(lat, lon), ...]
    """
    
    # 根据复杂度确定采样点数
    if complexity_level == 'low':
        n_points = SAMPLING_CONFIG['min_samples_per_grid']
        grid_size = 2
    elif complexity_level == 'medium':
        n_points = SAMPLING_CONFIG['base_samples_per_grid']
        grid_size = 3
    else:  # high
        n_points = SAMPLING_CONFIG['max_samples_per_grid']
        grid_size = 5
    
    # 计算采样点间距
    grid_resolution = SPATIAL_CONFIG['target_resolution']
    point_spacing = grid_resolution / grid_size
    
    # 生成规则网格采样点
    sampling_points = []
    start_offset = -(grid_size - 1) / 2 * point_spacing
    
    for i in range(grid_size):
        for j in range(grid_size):
            lat_offset = start_offset + i * point_spacing
            lon_offset = start_offset + j * point_spacing
            
            sample_lat = grid_center_lat + lat_offset
            sample_lon = grid_center_lon + lon_offset
            
            sampling_points.append((sample_lat, sample_lon))
    
    return sampling_points

def validate_config():
    """验证配置参数的合理性"""
    
    errors = []
    
    # 检查基本参数
    if SPATIAL_CONFIG['target_resolution'] <= 0:
        errors.append("目标分辨率必须大于0")
    
    if TEMPORAL_CONFIG['hours_per_year'] != 8760:
        errors.append("年小时数应为8760")
    
    # 检查太阳能参数
    if not (0 < SOLAR_PV_CONFIG['technology']['nominal_efficiency'] < 1):
        errors.append("太阳能电池板效率应在0-1之间")
    
    # 检查风电参数
    wind_speeds = [
        WIND_CONFIG['turbine']['cut_in_speed'],
        WIND_CONFIG['turbine']['rated_speed'],
        WIND_CONFIG['turbine']['cut_out_speed']
    ]
    if not (wind_speeds[0] < wind_speeds[1] < wind_speeds[2]):
        errors.append("风机切入、额定、切出风速应递增")
    
    if errors:
        raise ValueError("配置验证失败:\n" + "\n".join(errors))
    
    return True

# 配置验证
if __name__ == "__main__":
    try:
        validate_config()
        print("配置验证通过")
    except ValueError as e:
        print(f"配置验证失败: {e}")
