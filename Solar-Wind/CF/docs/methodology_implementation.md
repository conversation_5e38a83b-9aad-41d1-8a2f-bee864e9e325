# 容量因子计算方法论实现文档
## Capacity Factor Calculation Methodology Implementation

### 文档概述

本文档详细说明了基于Zheng et al. & <PERSON> et al. (Nature Communications, 2025)方法论的容量因子计算系统实现，确保严格遵循文献中的科学计算公式和技术参数。

### 方法论基础

#### 核心文献依据
- **主要方法**: <PERSON> et al., Nature Communications, 2025
- **海上风电约束**: <PERSON> et al., Nature Communications, 2025
- **技术参数**: 基于文献标准设置，确保科学准确性

#### 计算原理
本系统采用物理模型驱动的小时级时间分辨率计算，结合GIS约束和适宜性评估，生成高精度的容量因子时间序列。

### 陆上风电容量因子计算

#### 技术参数设置 (严格按照Zheng et al.)

```python
onshore_wind_params = {
    'turbine_model': 'General Electric 2.5 MW',  # 文献指定型号
    'rated_power': 2.5,                          # MW
    'hub_height': 100,                           # m
    'capacity_density': 2.7,                     # MW/km²
    'cut_in_speed': 3.0,                         # m/s
    'rated_speed': 12.0,                         # m/s
    'cut_out_speed': 25.0,                       # m/s
    'power_law_exponent': 1/7,                   # 表面摩擦系数
}
```

#### 计算流程

**步骤1: 10米高度风速计算 (公式3)**
```python
def calculate_10m_wind_speed(u10, v10):
    """
    V10 = √(uas² + vas²)
    
    其中:
    - uas: 10米高度北向风速分量
    - vas: 10米高度东向风速分量
    """
    return np.sqrt(u10**2 + v10**2)
```

**步骤2: 轮毂高度风速外推 (公式2)**
```python
def extrapolate_to_hub_height(wind_speed_10m, hub_height=100):
    """
    Vhub = V10 × (hub/10)^α
    
    其中:
    - V10: 10米高度风速
    - hub: 轮毂高度 (100米)
    - α: 表面摩擦系数 (1/7)
    """
    alpha = 1/7  # 基于文献标准
    return wind_speed_10m * (hub_height / 10)**alpha
```

**步骤3: 功率曲线应用 (公式1)**
```python
def apply_power_curve(wind_speed_hub):
    """
    CFw = fw(Vhub)
    
    其中 fw 表示GE 2.5MW风机的功率曲线
    """
    # 标准GE 2.5MW功率曲线数据点
    wind_speeds = [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 30]
    power_output = [0, 0, 0.1, 0.2, 0.4, 0.6, 0.8, 0.9, 0.95, 0.98, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0, 0]
    
    # 插值计算
    power_curve = interp1d(wind_speeds, power_output, bounds_error=False, fill_value=0)
    return power_curve(wind_speed_hub)
```

### 太阳能光伏容量因子计算

#### 技术参数设置 (严格按照Zheng et al.)

```python
solar_pv_params = {
    'efficiency': 0.1619,              # 光电转换效率 16.19%
    'system_coef': 0.8056,             # 系统性能系数 80.56%
    'temp_coefficient': 0.005,         # 温度系数 0.005 °C⁻¹
    'rated_power': 161.9,              # W/m² (标准测试条件)
    'capacity_density': 74,            # W/m² (考虑间距)
    'stc_temperature': 25,             # 标准测试条件温度 °C
    'stc_irradiance': 1000,            # 标准测试条件辐照度 W/m²
    'ground_reflectance': 0.2,         # 地面反射率
    # 温度计算系数 (基于文献公式14)
    'c1': 4.3,     # °C
    'c2': 0.943,   # 温度系数
    'c3': 0.028,   # °C·m²/W
    'c4': 1.528,   # °C·s/m
}
```

#### 计算流程

**步骤1: 太阳几何角度计算**

```python
def calculate_solar_geometry(time, latitude, longitude):
    """
    基于公式(11)和(12)计算太阳位置
    
    公式(12): δ = -23.45 cos[360(n+10)/365.25]  # 太阳赤纬角
    公式(11): θz = cos⁻¹[sin δ × sin LAT + cos δ × cos LAT × cos ω]  # 太阳天顶角
    """
    # 年内天数
    day_of_year = time.dayofyear
    
    # 太阳赤纬角
    declination = -23.45 * np.cos(360 * (day_of_year + 10) / 365.25 * np.pi / 180)
    
    # 太阳时角
    hour_of_day = time.hour + time.minute / 60.0
    solar_time = hour_of_day + longitude / 15.0
    hour_angle = 15 * (solar_time - 12)
    
    # 太阳天顶角
    lat_rad = np.radians(latitude)
    dec_rad = np.radians(declination)
    hour_rad = np.radians(hour_angle)
    
    cos_zenith = (np.sin(lat_rad) * np.sin(dec_rad) + 
                 np.cos(lat_rad) * np.cos(dec_rad) * np.cos(hour_rad))
    zenith_angle = np.degrees(np.arccos(np.clip(cos_zenith, -1, 1)))
    
    return {
        'zenith_angle': zenith_angle,
        'elevation_angle': 90 - zenith_angle,
        'declination': declination,
        'hour_angle': hour_angle
    }
```

**步骤2: 组件辐照度计算 (公式6-9)**

```python
def calculate_panel_irradiance(ghi, solar_geometry, panel_tilt):
    """
    基于公式(6)-(9)计算组件总辐照度
    
    公式(6): IΣ = IBΣ + IDΣ + IRΣ
    公式(7): IBΣ = IBH × cos θ0
    公式(8): IDΣ = IDH × Rd
    公式(9): IRΣ = IH × ρf × (1-cos Σ)/2
    """
    zenith = solar_geometry['zenith_angle']
    
    # 入射角计算 (公式10简化版)
    cos_incidence = np.cos(np.radians(zenith)) * np.cos(np.radians(panel_tilt))
    cos_incidence = np.maximum(cos_incidence, 0)
    
    # 辐射分解 (简化模型)
    clearness_index = np.clip(ghi / 1000, 0, 1)
    direct_fraction = np.where(clearness_index > 0.2, 0.9 * clearness_index, 0.1)
    diffuse_fraction = 1 - direct_fraction
    
    # 直射分量 (公式7)
    direct_horizontal = ghi * direct_fraction
    direct_tilted = direct_horizontal * cos_incidence / np.maximum(np.cos(np.radians(zenith)), 0.01)
    
    # 散射分量 (公式8简化)
    diffuse_horizontal = ghi * diffuse_fraction
    diffuse_tilted = diffuse_horizontal * (1 + np.cos(np.radians(panel_tilt))) / 2
    
    # 反射分量 (公式9)
    ground_reflectance = 0.2
    reflected_tilted = ghi * ground_reflectance * (1 - np.cos(np.radians(panel_tilt))) / 2
    
    # 总辐照度 (公式6)
    total_irradiance = direct_tilted + diffuse_tilted + reflected_tilted
    
    return np.maximum(total_irradiance, 0)
```

**步骤3: 电池温度计算 (公式14)**

```python
def calculate_cell_temperature(ambient_temp, irradiance, wind_speed):
    """
    公式(14): Tcell = c1 + c2×T + c3×I - c4×V
    
    其中:
    - c1 = 4.3°C (经验常数)
    - c2 = 0.943 (温度系数)
    - c3 = 0.028°C·m²/W (辐射系数)
    - c4 = 1.528°C·s/m (冷却系数)
    """
    ambient_temp_c = ambient_temp - 273.15  # K转°C
    
    c1, c2, c3, c4 = 4.3, 0.943, 0.028, 1.528
    cell_temp = c1 + c2 * ambient_temp_c + c3 * irradiance - c4 * wind_speed
    
    return cell_temp
```

**步骤4: 容量因子计算 (公式4-5,13)**

```python
def calculate_solar_capacity_factor(irradiance, cell_temperature):
    """
    公式(4): CFs = PGHI / PWp
    公式(5): PGHI = IΣ × EF × TEMcoef × SYScoef
    公式(13): TEMcoef = 1 - γ × (Tcell - TSTC)
    """
    # 温度修正系数 (公式13)
    gamma = 0.005  # 温度系数
    t_stc = 25     # 标准测试条件温度
    temp_coef = 1 - gamma * (cell_temperature - t_stc)
    
    # 实际电力输出 (公式5)
    efficiency = 0.1619      # 光电转换效率
    system_coef = 0.8056     # 系统性能系数
    power_output = irradiance * efficiency * temp_coef * system_coef
    
    # 容量因子 (公式4)
    rated_power = 161.9  # W/m²
    capacity_factor = power_output / rated_power
    
    return np.maximum(capacity_factor, 0)
```

### 海上风电容量因子计算

#### 技术参数设置 (基于Zheng et al.)

```python
offshore_wind_params = {
    'turbine_model': 'Vestas 8.0 MW',    # 文献指定型号
    'rated_power': 8.0,                  # MW
    'hub_height': 100,                   # m
    'capacity_density': 4.6,             # MW/km²
    'cut_in_speed': 3.0,                 # m/s
    'rated_speed': 13.0,                 # m/s
    'cut_out_speed': 25.0,               # m/s
    'power_law_exponent': 1/7,           # 海上摩擦系数
}
```

#### 特殊约束条件 (基于Wang et al.)

```python
offshore_constraints = {
    'water_depth_min': 1,        # 最小水深 ≥ 1m
    'eez_constraint': True,      # 严格限制在EEZ内
    'marine_protected_areas': True,  # 排除海洋保护区
}
```

### 空间采样策略

#### 网格内采样方法

```python
def generate_adaptive_sampling_points(grid_i, grid_j, complexity='medium'):
    """
    为0.5°网格生成自适应采样点
    
    Parameters:
    -----------
    complexity : str
        地形复杂度 ('low', 'medium', 'high')
    
    Returns:
    --------
    list
        采样点坐标列表
    """
    # 根据复杂度确定采样密度
    if complexity == 'low':
        grid_size = 2    # 2x2 = 4个点
    elif complexity == 'medium':
        grid_size = 3    # 3x3 = 9个点
    else:  # high
        grid_size = 5    # 5x5 = 25个点
    
    # 生成规则网格采样点
    center_lat, center_lon = grid_to_coordinates(grid_i, grid_j)
    point_spacing = 0.5 / grid_size
    
    sampling_points = []
    start_offset = -(grid_size - 1) / 2 * point_spacing
    
    for i in range(grid_size):
        for j in range(grid_size):
            lat_offset = start_offset + i * point_spacing
            lon_offset = start_offset + j * point_spacing
            
            sample_lat = center_lat + lat_offset
            sample_lon = center_lon + lon_offset
            
            sampling_points.append((sample_lat, sample_lon))
    
    return sampling_points
```

### 多年数据处理

#### 不确定性降低策略

```python
def process_multi_year_data(years_list=[2020, 2021, 2022]):
    """
    处理多年数据以降低不确定性
    
    Parameters:
    -----------
    years_list : list
        年份列表
    
    Returns:
    --------
    dict
        多年统计结果
    """
    all_years_cf = []
    
    for year in years_list:
        year_cf = calculate_annual_cf(year)
        all_years_cf.extend(year_cf)
    
    return {
        'multi_year_average': np.mean(all_years_cf),
        'standard_deviation': np.std(all_years_cf),
        'coefficient_of_variation': np.std(all_years_cf) / np.mean(all_years_cf),
        'years_count': len(years_list),
        'confidence_interval_95': np.percentile(all_years_cf, [2.5, 97.5])
    }
```

### 质量控制机制

#### 数据验证标准

```python
def validate_capacity_factors(cf_results):
    """
    容量因子结果验证
    
    基于文献和实际项目数据的合理性检查
    """
    validation_criteria = {
        'solar_cf_range': (0.05, 0.35),    # 太阳能CF合理范围
        'wind_cf_range': (0.10, 0.60),     # 风电CF合理范围
        'seasonal_variation': True,         # 季节变化检查
        'geographic_consistency': True,     # 地理一致性检查
    }
    
    # 执行验证逻辑
    validation_results = {}
    
    # 范围检查
    solar_cf = cf_results.get('solar_cf', [])
    wind_cf = cf_results.get('wind_cf', [])
    
    validation_results['solar_range_valid'] = all(
        validation_criteria['solar_cf_range'][0] <= cf <= validation_criteria['solar_cf_range'][1]
        for cf in solar_cf if not np.isnan(cf)
    )
    
    validation_results['wind_range_valid'] = all(
        validation_criteria['wind_cf_range'][0] <= cf <= validation_criteria['wind_cf_range'][1]
        for cf in wind_cf if not np.isnan(cf)
    )
    
    return validation_results
```

### 输出格式标准

#### 与现有模块兼容性

```python
def format_output_for_integration():
    """
    格式化输出以确保与现有模块兼容
    
    输出格式:
    - 坐标系: WGS84 (EPSG:4326)
    - 分辨率: 0.5° × 0.5°
    - 数据格式: CSV (汇总) + JSON (详细)
    - 时间分辨率: 8760小时 + 月度 + 年度
    """
    output_structure = {
        'spatial_reference': 'EPSG:4326',
        'resolution': '0.5_degree',
        'temporal_resolution': {
            'hourly': '8760_hours',
            'monthly': '12_months',
            'annual': 'multi_year_average'
        },
        'data_format': {
            'summary': 'CSV',
            'detailed': 'JSON',
            'visualization': 'PNG'
        }
    }
    
    return output_structure
```

### 性能优化策略

#### 计算效率优化

```python
def optimize_computation():
    """
    计算性能优化策略
    
    1. 向量化运算: 使用NumPy向量化操作
    2. 内存管理: 分批处理大数据集
    3. 并行处理: 网格级并行计算
    4. 缓存机制: 避免重复计算
    """
    optimization_techniques = {
        'vectorization': 'numpy_operations',
        'memory_management': 'batch_processing',
        'parallel_processing': 'grid_level_parallelism',
        'caching': 'intermediate_results_cache'
    }
    
    return optimization_techniques
```

### 验证和校准

#### 与已有研究对比

```python
def validate_against_literature():
    """
    与已有研究结果对比验证
    
    参考数据源:
    - IRENA全球能源地图集
    - IEA可再生能源路线图
    - 学术文献结果
    - 实际项目运行数据
    """
    reference_benchmarks = {
        'global_solar_cf_range': (0.15, 0.25),     # 全球平均
        'global_wind_cf_range': (0.20, 0.35),      # 全球平均
        'high_resource_regions': {
            'sahara_solar': (0.25, 0.35),
            'north_sea_wind': (0.35, 0.50),
            'great_plains_wind': (0.30, 0.45)
        }
    }
    
    return reference_benchmarks
```

### 技术创新点

1. **严格文献遵循**: 完全基于Nature Communications发表的方法论
2. **多年数据处理**: 系统性降低年际变异性带来的不确定性
3. **空间采样策略**: 科学处理网格内气象条件异质性
4. **质量控制集成**: 多层次数据验证和结果检查
5. **模块化设计**: 与现有Solar-Wind项目架构无缝集成

### 未来改进方向

1. **高分辨率数据**: 集成更高分辨率的气象数据
2. **动态参数**: 考虑技术进步对参数的影响
3. **不确定性量化**: 更详细的不确定性分析
4. **实时验证**: 与实际项目数据的持续对比验证
