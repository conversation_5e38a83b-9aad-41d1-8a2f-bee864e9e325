This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2025) (preloaded format=pdflatex 2025.6.7)  10 JUL 2025 17:43
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**全球电力系统扩展规划MILP模型_修订版.tex
(./全球电力系统扩展规划MILP模型_修订版.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count196
\c@section=\count197
\c@subsection=\count198
\c@subsubsection=\count199
\c@paragraph=\count266
\c@subparagraph=\count267
\c@figure=\count268
\c@table=\count269
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks17
\ex@=\dimen142
))
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen143
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count270
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count271
\leftroot@=\count272
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count273
\DOTSCASE@=\count274
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen144
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count275
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count276
\dotsspace@=\muskip17
\c@parentequation=\count277
\dspbrk@lvl=\count278
\tag@help=\toks18
\row@=\count279
\column@=\count280
\maxfields@=\count281
\andhelp@=\toks19
\eqnshift@=\dimen145
\alignsep@=\dimen146
\tagshift@=\dimen147
\tagwidth@=\dimen148
\totwidth@=\dimen149
\lineht@=\dimen150
\@envbody=\toks20
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks21
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(/usr/local/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks22
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count282
\Gm@cntv=\count283
\c@Gm@tempcnt=\count284
\Gm@bindingoffset=\dimen151
\Gm@wd@mp=\dimen152
\Gm@odd@mp=\dimen153
\Gm@even@mp=\dimen154
\Gm@layoutwidth=\dimen155
\Gm@layoutheight=\dimen156
\Gm@layouthoffset=\dimen157
\Gm@layoutvoffset=\dimen158
\Gm@dimlist=\toks23
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/cjk/texinput/CJKutf8.sty
Package: CJKutf8 2021/10/16 4.8.5

(/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/ifpdf.sty
Package: ifpdf 2019/10/25 v3.4 ifpdf legacy package. Use iftex instead.
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks24
\inpenc@posthook=\toks25
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/cjk/texinput/CJK.sty
Package: CJK 2021/10/16 4.8.5

(/usr/local/texlive/2025/texmf-dist/tex/latex/cjk/texinput/mule/MULEenc.sty
Package: MULEenc 2021/10/16 4.8.5
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/cjk/texinput/CJK.enc
File: CJK.enc 2021/10/16 4.8.5
Now handling font encoding C00 ...
... no UTF-8 mapping file for font encoding C00
Now handling font encoding C05 ...
... no UTF-8 mapping file for font encoding C05
Now handling font encoding C09 ...
... no UTF-8 mapping file for font encoding C09
Now handling font encoding C10 ...
... no UTF-8 mapping file for font encoding C10
Now handling font encoding C20 ...
... no UTF-8 mapping file for font encoding C20
Now handling font encoding C19 ...
... no UTF-8 mapping file for font encoding C19
Now handling font encoding C40 ...
... no UTF-8 mapping file for font encoding C40
Now handling font encoding C42 ...
... no UTF-8 mapping file for font encoding C42
Now handling font encoding C43 ...
... no UTF-8 mapping file for font encoding C43
Now handling font encoding C50 ...
... no UTF-8 mapping file for font encoding C50
Now handling font encoding C52 ...
... no UTF-8 mapping file for font encoding C52
Now handling font encoding C49 ...
... no UTF-8 mapping file for font encoding C49
Now handling font encoding C60 ...
... no UTF-8 mapping file for font encoding C60
Now handling font encoding C61 ...
... no UTF-8 mapping file for font encoding C61
Now handling font encoding C63 ...
... no UTF-8 mapping file for font encoding C63
Now handling font encoding C64 ...
... no UTF-8 mapping file for font encoding C64
Now handling font encoding C65 ...
... no UTF-8 mapping file for font encoding C65
Now handling font encoding C70 ...
... no UTF-8 mapping file for font encoding C70
Now handling font encoding C31 ...
... no UTF-8 mapping file for font encoding C31
Now handling font encoding C32 ...
... no UTF-8 mapping file for font encoding C32
Now handling font encoding C33 ...
... no UTF-8 mapping file for font encoding C33
Now handling font encoding C34 ...
... no UTF-8 mapping file for font encoding C34
Now handling font encoding C35 ...
... no UTF-8 mapping file for font encoding C35
Now handling font encoding C36 ...
... no UTF-8 mapping file for font encoding C36
Now handling font encoding C37 ...
... no UTF-8 mapping file for font encoding C37
Now handling font encoding C80 ...
... no UTF-8 mapping file for font encoding C80
Now handling font encoding C81 ...
... no UTF-8 mapping file for font encoding C81
Now handling font encoding C01 ...
... no UTF-8 mapping file for font encoding C01
Now handling font encoding C11 ...
... no UTF-8 mapping file for font encoding C11
Now handling font encoding C21 ...
... no UTF-8 mapping file for font encoding C21
Now handling font encoding C41 ...
... no UTF-8 mapping file for font encoding C41
Now handling font encoding C62 ...
... no UTF-8 mapping file for font encoding C62
)
\CJK@indent=\box54
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
))
(/usr/local/texlive/2025/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2025/02/06 v3.11 Customized lists
\labelindent=\skip54
\enit@outerparindent=\dimen159
\enit@toks=\toks26
\enit@inbox=\box55
\enit@count@id=\count285
\enitdp@description=\count286
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count287
\l__pdf_internal_box=\box56
)
(./全球电力系统扩展规划MILP模型_修订版.aux)
\openout1 = `全球电力系统扩展规划MILP模型_修订版.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C00/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C05/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C09/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C10/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C20/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C19/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C40/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C42/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C43/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C50/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C52/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C49/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C60/mj/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C61/mj/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C63/mj/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C64/mj/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C65/mj/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C70/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C31/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C32/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C33/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C34/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C35/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C36/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C37/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C80/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C81/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C01/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C11/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C21/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C41/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for C62/song/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(71.13188pt, 455.24411pt, 71.13188pt)
* v-part:(T,H,B)=(71.13188pt, 702.78308pt, 71.13188pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=455.24411pt
* \textheight=702.78308pt
* \oddsidemargin=-1.1381pt
* \evensidemargin=-1.1381pt
* \topmargin=-38.1381pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=10.0pt
* \footskip=30.0pt
* \marginparwidth=65.0pt
* \marginparsep=11.0pt
* \columnsep=10.0pt
* \skip\footins=9.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

(/usr/local/texlive/2025/texmf-dist/tex/latex/cjk/texinput/UTF8/UTF8.bdg
File: UTF8.bdg 2021/10/16 4.8.5
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/cjk/texinput/UTF8/UTF8.enc
File: UTF8.enc 2021/10/16 4.8.5
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/cjk/texinput/UTF8/UTF8.chr
File: UTF8.chr 2021/10/16 4.8.5
)
LaTeX Font Info:    Trying to load font information for C70+gbsn on input line 
16.

(/usr/local/texlive/2025/texmf-dist/tex/latex/cjk/texinput/UTF8/c70gbsn.fd
File: c70gbsn.fd 2021/10/16 4.8.5
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/cjk/texinput/UTF8/c70gbsn.fdx
File: c70gbsn.fdx 2021/10/16 4.8.5
)
LaTeX Font Info:    Trying to load font information for U+msa on input line 16.


(/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 16.


(/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

[1

{/usr/local/texlive/2025/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{/usr/loc
al/texlive/2025/texmf-dist/fonts/enc/dvips/cm-super/cm-super-ts1.enc}]

[2]

[3]

[4]

[5]

[6]

[7]

[8]

[9]

[10]

[11]

[12]

[13]

[14]

[15]

[16]

[17]

[18] (./全球电力系统扩展规划MILP模型_修订版.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
 ***********
 ) 
Here is how much of TeX's memory you used:
 6060 strings out of 473190
 87714 string characters out of 5715801
 509477 words of memory out of 5000000
 28882 multiletter control sequences out of 15000+600000
 644373 words of font info for 341 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 59i,15n,65p,731b,1241s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx10.pfb
></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx12.pfb>
</usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb><
/usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb></
usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi5.pfb></us
r/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi7.pfb></usr/
local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb></usr/lo
cal/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr17.pfb></usr/loca
l/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr7.pfb></usr/local/t
exlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb></usr/local/te
xlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy7.pfb></usr/local/texl
ive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu30.pfb></usr/local/texlive/20
25/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu4e.pfb></usr/local/texlive/2025/tex
mf-dist/fonts/type1/arphic/gbsnu/gbsnu4f.pfb></usr/local/texlive/2025/texmf-dis
t/fonts/type1/arphic/gbsnu/gbsnu50.pfb></usr/local/texlive/2025/texmf-dist/font
s/type1/arphic/gbsnu/gbsnu51.pfb></usr/local/texlive/2025/texmf-dist/fonts/type
1/arphic/gbsnu/gbsnu52.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/arph
ic/gbsnu/gbsnu53.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/arphic/gbs
nu/gbsnu54.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbs
nu55.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu56.p
fb></usr/local/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu57.pfb></u
sr/local/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu58.pfb></usr/loc
al/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu59.pfb></usr/local/tex
live/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu5b.pfb></usr/local/texlive/2
025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu5c.pfb></usr/local/texlive/2025/te
xmf-dist/fonts/type1/arphic/gbsnu/gbsnu5d.pfb></usr/local/texlive/2025/texmf-di
st/fonts/type1/arphic/gbsnu/gbsnu5e.pfb></usr/local/texlive/2025/texmf-dist/fon
ts/type1/arphic/gbsnu/gbsnu5f.pfb></usr/local/texlive/2025/texmf-dist/fonts/typ
e1/arphic/gbsnu/gbsnu60.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/arp
hic/gbsnu/gbsnu61.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/arphic/gb
snu/gbsnu62.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gb
snu63.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu65.
pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu66.pfb></
usr/local/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu67.pfb></usr/lo
cal/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu68.pfb></usr/local/te
xlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu69.pfb></usr/local/texlive/
2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu6a.pfb></usr/local/texlive/2025/t
exmf-dist/fonts/type1/arphic/gbsnu/gbsnu6b.pfb></usr/local/texlive/2025/texmf-d
ist/fonts/type1/arphic/gbsnu/gbsnu6c.pfb></usr/local/texlive/2025/texmf-dist/fo
nts/type1/arphic/gbsnu/gbsnu6d.pfb></usr/local/texlive/2025/texmf-dist/fonts/ty
pe1/arphic/gbsnu/gbsnu6e.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/ar
phic/gbsnu/gbsnu6f.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/arphic/g
bsnu/gbsnu70.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/g
bsnu71.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu72
.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu73.pfb><
/usr/local/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu74.pfb></usr/l
ocal/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu75.pfb></usr/local/t
exlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu76.pfb></usr/local/texlive
/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu77.pfb></usr/local/texlive/2025/
texmf-dist/fonts/type1/arphic/gbsnu/gbsnu78.pfb></usr/local/texlive/2025/texmf-
dist/fonts/type1/arphic/gbsnu/gbsnu79.pfb></usr/local/texlive/2025/texmf-dist/f
onts/type1/arphic/gbsnu/gbsnu7a.pfb></usr/local/texlive/2025/texmf-dist/fonts/t
ype1/arphic/gbsnu/gbsnu7b.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/a
rphic/gbsnu/gbsnu7c.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/arphic/
gbsnu/gbsnu7e.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/
gbsnu7f.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu8
0.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu81.pfb>
</usr/local/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu82.pfb></usr/
local/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu83.pfb></usr/local/
texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu84.pfb></usr/local/texliv
e/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu86.pfb></usr/local/texlive/2025
/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu88.pfb></usr/local/texlive/2025/texmf
-dist/fonts/type1/arphic/gbsnu/gbsnu89.pfb></usr/local/texlive/2025/texmf-dist/
fonts/type1/arphic/gbsnu/gbsnu8b.pfb></usr/local/texlive/2025/texmf-dist/fonts/
type1/arphic/gbsnu/gbsnu8c.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/
arphic/gbsnu/gbsnu8d.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/arphic
/gbsnu/gbsnu8f.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu
/gbsnu90.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu
91.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu94.pfb
></usr/local/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu95.pfb></usr
/local/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu96.pfb></usr/local
/texlive/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu97.pfb></usr/local/texli
ve/2025/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu98.pfb></usr/local/texlive/202
5/texmf-dist/fonts/type1/arphic/gbsnu/gbsnu99.pfb></usr/local/texlive/2025/texm
f-dist/fonts/type1/arphic/gbsnu/gbsnu9a.pfb></usr/local/texlive/2025/texmf-dist
/fonts/type1/arphic/gbsnu/gbsnuff.pfb></usr/local/texlive/2025/texmf-dist/fonts
/type1/public/cm-super/sfrm1000.pfb>
Output written on 全球电力系统扩展规划MILP模型_修订版.pdf (18 pa
ges, 638869 bytes).
PDF statistics:
 545 PDF objects out of 1000 (max. 8388607)
 288 compressed objects within 3 object streams
 0 named destinations out of 1000 (max. 500000)
 281 words of extra memory for PDF output out of 10000 (max. 10000000)

