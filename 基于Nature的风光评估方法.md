# 基于文献的可再生能源潜力评估方法

## 1. 方法概述

基于提供的文献，本方法采用GIS数据排除不适宜区域，结合标准风机功率曲线和太阳能光伏物理模型，计算小时级容量因子和可开发潜力。

## 2. GIS数据和约束条件

### 2.1 太阳能适宜性筛选

#### 排除区域
- **土地利用**: 水体、湿地、永久积雪和冰、森林、农田
- **地形**: 坡度 > 5%
- **保护区**: 基于世界保护区数据库(WDPA)的保护区

#### 数据源
- **土地覆盖**: 欧空局气候变化倡议(ESA CCI)土地覆盖数据
- **地形**: 全球多分辨率地形高程数据(GMTED)
- **保护区**: 世界保护区数据库(WDPA)

### 2.2 陆上风电适宜性筛选

#### 筛选标准 (基于Zheng et al.)

**土地覆盖适宜性因子**:
- **森林**: 0% (完全排除)
- **灌木地**: 100% (完全适宜)
- **草原**: 100% (完全适宜)
- **湿地**: 0% (完全排除)
- **农田**: 100% (完全适宜)
- **城市**: 0% (完全排除)
- **植被镶嵌**: 100% (完全适宜)
- **雪冰**: 0% (完全排除)
- **沙漠**: 100% (完全适宜)
- **水体**: 0% (完全排除)

**地形约束**:
- **坡度限制**: < 20%
- **海拔限制**: < 3000m

**其他约束**:
- **保护区**: 排除陆地生态保护区

#### 数据源
- **土地覆盖**: 欧空局气候变化倡议(ESA CCI)土地覆盖数据
- **地形**: 全球多分辨率地形高程数据(GMTED)
- **保护区**: 世界保护区数据库(WDPA)

### 2.3 海上风电适宜性筛选

#### 筛选标准 (基于Wang et al., Nature Communications, 2025)

**土地覆盖适宜性因子**:
- **水体**: 100% (海上风电仅在水体区域部署)
- **其他土地类型**: 0% (森林、灌木地、草原、湿地、农田、城市、植被镶嵌、雪冰、沙漠均为0%)

**水深约束**:
- **水深**: 1m-60m “Secondly, locations with an offshore depth of more than 60 m are filtered.” ([Chen 等, 2021, p. 2773](zotero://select/library/items/QAGIXDVM)) (避免过浅区域和陆地)

**领海区域约束**:
- **专属经济区限制**: 仅在各国专属经济区(EEZ)内部署

**环境保护约束**:
- **海洋生态保护区**: 完全排除海洋生态保护区

#### 数据源 (基于Wang et al.)
- **领海区域数据**: 海洋边界地理数据库 (Maritime Boundaries Geodatabase)
- **水深数据**: 雷达地形测绘任务全球增强坡度数据库 (Radar Topography Mission Global Enhanced Slope Database)
- **海洋生态保护区**: 国家海洋数据和信息服务 (National Marine Data and Information Service)

## 3. 陆上风电容量因子计算

### 3.1 陆上风机规格 (基于Zheng et al.)

#### 技术参数
- **风机型号**: General Electric 2.5 MW
- **轮毂高度**: 100米
- **功率曲线**: 标准功率曲线数据
- **装机密度**: ~2.7 MW/km² (风机间距为7倍转子直径)

### 3.2 计算公式 (基于Zheng et al.)

#### 陆上风电容量因子
```
CFw = fw(Vhub)                                    (1)
```
其中 fw 表示GE 2.5MW风机的功率曲线

#### 轮毂高度风速外推
```
Vhub = V10 × (hub/10)^α                          (2)
```
其中:
- V10: 10米高度风速
- hub: 轮毂高度 (100米)
- α: 表面摩擦系数，通常取1/7

#### 10米高度风速
```
V10 = √(uas² + vas²)                             (3)
```
其中:
- uas: 10米高度北向风速分量
- vas: 10米高度东向风速分量

### 3.3 数据需求
- **ERA5风速数据**: 10米高度的uas和vas分量
- **时间分辨率**: 小时级
- **空间分辨率**: 0.25°×0.25°

## 4. 海上风电容量因子计算

### 4.1 海上风机规格 (基于Zheng et al.)

#### 技术参数
- **风机型号**: Vestas 8.0 MW
- **轮毂高度**: 100米
- **功率曲线**: 标准功率曲线数据
- **装机密度**: ~4.6 MW/km² (风机间距为7倍转子直径)

### 4.2 计算公式

#### 海上风电容量因子
```
CFw_offshore = fw_offshore(Vhub)                  (1')
```
其中 fw_offshore 表示Vestas 8.0MW风机的功率曲线

#### 轮毂高度风速外推 (海上)
海上风速外推使用与陆上相同的公式(2)，但考虑海面粗糙度较低的特点。

### 4.3 海上特殊约束条件 (基于Wang et al.)

#### 水深技术约束
- **技术可行范围**: ≥ 1m水深
- **经济可行范围**: 通常1-200m (涵盖固定式和浮式技术)

#### EEZ约束
海上风电开发严格限制在各国专属经济区(EEZ)范围内。

## 5. 太阳能容量因子计算

### 5.1 基本参数设置

#### 光伏系统参数
- **光电转换效率**: EF = 16.19%
- **系统性能系数**: SYScoef = 80.56%
- **温度系数**: γ = 0.005 °C⁻¹ (单晶硅)
- **额定输出**: PWp = 161.9 W/m² (标准测试条件)
- **跟踪系统**: 双轴跟踪系统

#### 标准测试条件
- **电池温度**: 25°C
- **辐照度**: 1000 W/m²
- **大气质量**: AM 1.5

### 5.2 计算公式

#### 太阳能容量因子
```
CFs = PGHI / PWp                                 (4)
```

#### 实际电力输出
```
PGHI = IΣ × EF × TEMcoef × SYScoef              (5)
```

#### 面板总辐照度
```
IΣ = IBΣ + IDΣ + IRΣ                            (6)
```

#### 直射辐照度分量
```
IBΣ = IBH × cos θ0                              (7)
```

#### 散射辐照度分量
```
IDΣ = IDH × Rd                                  (8)
```

#### 反射辐照度分量
```
IRΣ = IH × ρf × (1-cos Σ)/2                    (9)
```

其中:
- Σ: 光伏组件倾斜角
- IBH, IDH, IH: 水平面直射、散射、总辐照度
- Rd: 散射辐照度转换系数
- ρf: 反射率常数 (草地和地面取0.2)

### 5.3 几何角度计算

#### 直射辐照入射角
```
θ0 = cos⁻¹[cos θz × cos Σ + sin θz × sin Σ × cos(γs - γ)]  (10)
```

#### 太阳天顶角
```
θz = cos⁻¹[sin δ × sin LAT + cos δ × cos LAT × cos ω]       (11)
```

#### 太阳赤纬角
```
δ = -23.45 cos[360(n+10)/365.25]                           (12)
```

其中:
- θz: 太阳天顶角
- δ: 太阳赤纬角
- γs, γ: 太阳方位角和面板方位角
- LAT: 纬度
- ω: 太阳时角
- n: 年内天数

### 5.4 温度修正

#### 温度系数
```
TEMcoef = 1 - γ × (Tcell - TSTC)                          (13)
```

#### 电池工作温度
```
Tcell = c1 + c2 + c3 × I - c4 × V                        (14)
```

其中:
- γ: 温度系数 (0.005 °C⁻¹)
- Tcell: 太阳能板工作温度
- TSTC: 标准测试条件下温度 (25°C)
- I: 地表太阳辐射
- V: 地表风速
- c1 = 4.3°C (经验常数)
- c2 = 0.943 (温度系数)
- c3 = 0.028°C·m²/W (辐射系数)
- c4 = 1.528°C·s/m (冷却系数)

## 6. 数据处理流程
### 6.1 空间聚合
- **原始分辨率**: 1°×1° 网格
- **聚合方法**: 面积加权的国家平均值 排除GIS约束，得到可开发面积之后，计算风光的可开发容量时，参考：

“Taking into account the necessary spacing between PV panels and maintenance access walkways, we adopt an average installation density of 74 W/m2, as determined from our prior survey52.” (Jiang 等, 2025, p. 10)

“For each 0.25° × 0.25° grid-box, turbines are arranged such that there is a spacing of seven rotor diameters between neighboring turbines60. Consequently, the average installation density is ~2.7 MW/km2 for onshore and 4.6 MW/km2 for offshore wind farms.” (Jiang 等, 2025, p. 11)
- **排除区域**: 使用GIS数据排除不适宜部署区域

### 6.2 偏差修正
- **方法**: 分位数映射(Quantile Mapping)
- **参考数据**: ERA5再分析数据
- **应用对象**: 风电和太阳能容量因子

## 7. 实施方案

### 7.1 数据需求清单

#### 气象数据
- **CMIP6数据**: 
  - 10米高度风速分量 (uas, vas)
  - 地表太阳辐射分量 (直射、散射、总辐射)
  - 地表温度
  - 地表风速
- **ERA5数据**: 用于偏差修正的参考数据

#### GIS数据 (基于Wang et al.和Zheng et al.)
- **ESA CCI土地覆盖**: 土地利用分类
- **SRTM地形数据**: 坡度计算
- **WDPA保护区**: 陆地和海洋保护区边界
- **海洋边界数据库**: 专属经济区边界 (基于Wang et al.)
- **水深数据**: 雷达地形测绘任务数据 (基于Wang et al.)

### 7.2 计算流程

#### 第一步: 数据预处理
1. 下载和整理数据
2. 获取GIS约束数据 (陆地和海洋)
3. 统一空间分辨率到1°×1°
4. 质量控制和缺失值处理

#### 第二步: 分技术约束掩码生成
1. **太阳能光伏掩码**:
   - 基于土地利用生成排除掩码
   - 基于坡度生成地形掩码
   - 基于保护区生成环境掩码
2. **陆上风电掩码**:
   - 基于土地利用生成排除掩码 (基于Zheng et al.)
   - 基于坡度和海拔生成地形掩码
   - 基于保护区生成环境掩码
3. **海上风电掩码**:
   - 基于水体识别海洋区域 (基于Wang et al.)
   - 基于水深生成技术可行掩码
   - 基于EEZ生成领海掩码
   - 基于海洋保护区生成环境掩码

#### 第三步: 分技术容量因子计算
1. **陆上风电容量因子计算**:
   - 轮毂高度风速外推 (100m)
   - 应用GE 2.5MW功率曲线
   - 计算小时级容量因子
2. **海上风电容量因子计算**:
   - 轮毂高度风速外推 (100m)
   - 应用Vestas 8.0MW功率曲线
   - 计算小时级容量因子
3. **太阳能容量因子计算**:
   - 太阳几何角度计算
   - 面板辐照度分量计算
   - 温度修正
   - 计算小时级容量因子

#### 第四步: 偏差修正
1. 与ERA5数据对比
2. 应用分位数映射方法
3. 修正系统性偏差

#### 第五步: 空间聚合
1. 应用约束掩码
2. 面积加权聚合到国家级
3. 生成最终结果

### 7.3 输出格式

#### 容量因子时间序列
```python
capacity_factors = {
    'solar': {
        'country_code': [8760 hourly values],  # 小时级容量因子
        ...
    },
    'wind_onshore': {
        'country_code': [8760 hourly values],
        ...
    },
    'wind_offshore': {
        'country_code': [8760 hourly values],
        ...
    }
}
```

#### 可开发潜力
```python
technical_potential = {
    'solar': {
        'country_code': {
            'suitable_area_km2': float,      # 适宜面积
            'capacity_density_MW_km2': float, # 装机密度
            'total_potential_MW': float,      # 总潜力
            'average_capacity_factor': float  # 年平均容量因子
        },
        ...
    },
    'wind_onshore': {...},
    'wind_offshore': {...}
}
```

## 8. 技术参数设置

### 8.1 陆上风电技术参数 (基于Zheng et al.)
- **风机型号**: General Electric 2.5 MW
- **轮毂高度**: 100米
- **装机密度**: 2.7 MW/km² (风机间距7倍转子直径)
- **风机间距**: 7D × 7D (D为转子直径)

### 8.2 海上风电技术参数 (基于Zheng et al.)
- **风机型号**: Vestas 8.0 MW
- **轮毂高度**: 100米
- **装机密度**: 4.6 MW/km² (风机间距7倍转子直径)
- **风机间距**: 7D × 7D (D为转子直径)
- **EEZ约束**: 严格限制在专属经济区内 (基于Wang et al.)
- **水深范围**: ≥ 1m (基于Wang et al.)

### 8.3 太阳能技术参数 (基于Zheng et al.)
- **光伏板类型**: 晶硅光伏板
- **标准功率**: 395 W (标准测试条件)
- **装机密度**: 74 W/m² (考虑间距和维护通道)
- **面板配置**: 朝向赤道，纬度相关最优倾角

## 9. 验证方法

### 9.1 与现有研究对比^[2]^
- 对比IRENA全球能源地图集
- 对比IEA可再生能源路线图
- 对比学术文献结果

### 9.2 实际项目验证^[3]^
- 选择已建项目进行容量因子对比
- 验证计算精度和可靠性

### 9.3 敏感性分析^[4]^
- 关键参数变化影响分析
- 不确定性量化

^[2]^ 非Zheng/Wang文献来源：IRENA, IEA等机构报告
^[3]^ 非Zheng/Wang文献来源：实际项目运行数据
^[4]^ 非Zheng/Wang文献来源：敏感性分析方法学文献

---

**方法特点**:
- 严格遵循Zheng et al. (Nature Communications, 2025)和Wang et al. (Nature Communications, 2025)文献方法
- 陆上和海上风电分开建模和计算
- 海上风电特别考虑EEZ约束 (基于Wang et al.)
- 物理模型驱动的小时级时间分辨率
- 全面的GIS约束和适宜性评估
- 标准化的技术参数设置

**文献来源说明**:
- 主要方法基于: Zheng et al., Nature Communications, 2025
- 海上风电约束基于: Wang et al., Nature Communications, 2025
- 补充技术标准和验证方法来源已在脚注中标注

**下一步**: 基于此方法进行代码实现
