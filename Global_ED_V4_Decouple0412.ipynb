{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["V4相比V3添加了生物质能和地热能，并且更新了各国负荷数据和电力进口数据。\n", "之后尝试调整电力联络线数据和控制load shedding，便于为六国二区域添加松弛版火电机组比例约束。\n", "以及可能还需要约束抽蓄储能。\n", "\n", "Monthly分解串行运行"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Index(['ISO_Code', 'Country', 'Coal Capacity (MW)', 'Gas Capacity (MW)',\n", "       'Oil Capacity (MW)', 'Thermal Capacity (MW)', 'Thermal Ratio Coal',\n", "       'Thermal Ratio Gas', 'Thermal Ratio Oil', 'Biomass Capacity (MW)',\n", "       'Electricity Imports (MWh)', 'Electricity Exports (MWh)',\n", "       'Net Electricity Imports (MWh)', 'Power Carbon Emissions (Mt)',\n", "       'Storage Max Power (MW)', 'Storage Capacity (MWh)',\n", "       'Storage Efficiency', 'Storage SOC Lower Limit',\n", "       'Storage SOC Upper Limit'],\n", "      dtype='object')\n"]}], "source": ["# 导入所需库\n", "import pandas as pd\n", "import numpy as np\n", "import gurobipy as gp\n", "from gurobipy import GRB\n", "import pickle\n", "import os\n", "import time\n", "\n", "# 文件路径\n", "static_data_file = '/Users/<USER>/PycharmProjects/Global_Energy_Internet/Static_Data_Summary_2023_V4.xlsx'\n", "time_series_data_file = '/Users/<USER>/PycharmProjects/Global_Energy_Internet/Time_Series_Data_2023_V4.xlsx'\n", "transmission_lines_file = '/Users/<USER>/PycharmProjects/Global_Energy_Internet/Transmission_Lines_2023_V5.xlsx'\n", "\n", "# ---------------------\n", "# 加载静态数据\n", "# ---------------------\n", "static_data = pd.read_excel(static_data_file)\n", "\n", "# 打印静态数据的列头以确认内容\n", "print(static_data.columns)\n", "\n", "# 提取静态参数\n", "iso_codes = static_data['ISO_Code'].tolist()\n", "num_countries = len(iso_codes)\n", "coal_capacity = static_data['Coal Capacity (MW)'].values\n", "gas_capacity = static_data['Gas Capacity (MW)'].values\n", "oil_capacity = static_data['Oil Capacity (MW)'].values\n", "storage_max_power = static_data['Storage Max Power (MW)'].values\n", "storage_capacity = static_data['Storage Capacity (MWh)'].values\n", "storage_efficiency = static_data['Storage Efficiency'].values\n", "storage_soc_lower = static_data['Storage SOC Lower Limit'].values\n", "storage_soc_upper = static_data['Storage SOC Upper Limit'].values\n", "electricity_imports = static_data['Electricity Imports (MWh)'].values\n", "electricity_exports = static_data['Electricity Exports (MWh)'].values\n", "carbon_emissions = static_data['Power Carbon Emissions (Mt)'].values * 1e6  # 转为 ton\n", "biomass_capacity = static_data['Biomass Capacity (MW)'].values\n", "\n", "# 生物质能年发电量限制\n", "biomass_generation_limits = {\n", "    'BRA': 58313069.30,\n", "    'CHN': 172918314.50,\n", "    'IND': 33727037.10,\n", "    'JPN': 39000000.00,\n", "    'RUS': 3779000.00,\n", "    'USA': 57156946.51,\n", "    'EU': 195878968.50,\n", "    'ROW': 126529322.22\n", "}\n", "\n", "# ---------------------\n", "# 加载时序数据文件\n", "# ---------------------\n", "time_series = pd.ExcelFile(time_series_data_file)\n", "\n", "# 提取各时序数据\n", "wind_data = pd.read_excel(time_series, sheet_name='Wind').iloc[:8760, 1:].values\n", "solar_data = pd.read_excel(time_series, sheet_name='Solar').iloc[:8760, 1:].values\n", "hydro_data = pd.read_excel(time_series, sheet_name='Hydro').iloc[:8760, 1:].values\n", "nuclear_data = pd.read_excel(time_series, sheet_name='Nuclear').iloc[:8760, 1:].values\n", "geothermal_data = pd.read_excel(time_series, sheet_name='Geothermal').iloc[:8760, 1:].values\n", "load_data = pd.read_excel(time_series, sheet_name='Load').iloc[:8760, 1:].values\n", "\n", "# ---------------------\n", "# 加载传输线路数据\n", "# ---------------------\n", "transmission_data = pd.read_excel(transmission_lines_file, usecols=['from_country', 'to_country', 'max_flow (MW)', 'max_counter_flow (MW)'])\n", "trans_from = transmission_data['from_country'].tolist()\n", "trans_to = transmission_data['to_country'].tolist()\n", "max_flows = transmission_data['max_flow (MW)'].values\n", "max_counter_flows = transmission_data['max_counter_flow (MW)'].values\n", "num_translines = len(transmission_data)\n", "\n", "# ---------------------\n", "# 参数设定\n", "# ---------------------\n", "heat_rate_coal = 2.93  # MMBtu/MWh\n", "heat_rate_gas_oil = 2.5   # MMBtu/MWh (gas和oil的加权平均)\n", "\n", "price_coal = 5.27  # $/MMBtu\n", "price_gas_oil = 11.5     # $/MMBtu (gas和oil的加权平均)\n", "\n", "emi_factor_coal = 820/1e3 # ton/MWh\n", "emi_factor_gas_oil = 550/1e3 # ton/MWh (gas和oil的加权平均)\n", "\n", "price_ls = 1e3  # Load shedding $/MWh\n", "price_cur = 1e2   # Curtailment $/MWh\n", "\n", "storage_soc_init = 0.6 # 初始储能状态 p.u.\n", "\n", "# 初始化成本详情字典\n", "cost_details = {\n", "    'gas_oil_cost': np.zeros(num_countries),  # 将gas和oil合并为gas_oil\n", "    'coal_cost': np.zeros(num_countries),\n", "    'shedding_cost': np.zeros(num_countries),\n", "    'curtail_cost': np.zeros(num_countries)\n", "}\n", "        \n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# 定义全局经济调度函数，支持不同时间粒度的分解\n", "def run_global_economic_dispatch(decomposition_type='monthly',  warm_start=False):\n", "    \"\"\"\n", "    全球经济调度模型，支持不同时间粒度的分解运行，并支持热启动\n", "    \n", "    参数:\n", "    decomposition_type: 分解类型，可选值为 'monthly'(月度), 'quarterly'(季度), 'semiannual'(半年度), 'annual'(年度)\n", "    warm_start: 是否使用热启动, 如果为True, 将尝试加载月度优化结果作为初始值\n", "    \n", "    返回:\n", "    results: 包含所有时段结果的列表\n", "    \"\"\"\n", "    global pickle\n", "    typical_countries = ['BRA', 'CHN', 'IND', 'JPN', 'RUS', 'USA']        \n", "    eu_countries = ['AUT', 'BEL', 'BGR', 'HRV', 'CYP', 'CZE', 'DNK', 'EST', 'FIN', 'FRA', 'DEU', 'GRC', 'HUN', 'IRL', 'ITA', 'LVA', 'LTU', 'LUX', 'MLT', 'NLD', 'POL', 'PRT', 'ROU', 'SVK', 'SVN', 'ESP', 'SWE', 'GBR']\n", "    row_countries = [iso for iso in iso_codes if iso not in typical_countries and iso not in eu_countries]\n", "    # 根据分解类型定义时间段\n", "    if decomposition_type == 'monthly':\n", "        time_periods = [\n", "            (0, 744),      # 1月: 0-744h\n", "            (744, 1416),   # 2月: 745-1416h\n", "            (1416, 2160),  # 3月: 1417-2160h\n", "            (2160, 2880),  # 4月: 2161-2880h\n", "            (2880, 3624),  # 5月: 2881-3624h\n", "            (3624, 4344),  # 6月: 3625-4344h\n", "            (4344, 5088),  # 7月: 4345-5088h\n", "            (5088, 5832),  # 8月: 5089-5832h\n", "            (5832, 6552),  # 9月: 5833-6552h\n", "            (6552, 7296),  # 10月: 6553-7296h\n", "            (7296, 8016),  # 11月: 7297-8016h\n", "            (8016, 8760)   # 12月: 8017-8760h\n", "        ]\n", "        period_names = [f\"月度_{i+1}\" for i in range(12)]\n", "    elif decomposition_type == 'quarterly':\n", "        time_periods = [\n", "            (0, 2160),     # 第一季度: 1-3月\n", "            (2160, 4344),  # 第二季度: 4-6月\n", "            (4344, 6552),  # 第三季度: 7-9月\n", "            (6552, 8760)   # 第四季度: 10-12月\n", "        ]\n", "        period_names = [f\"季度_{i+1}\" for i in range(4)]\n", "    elif decomposition_type == 'semiannual':\n", "        time_periods = [\n", "            (0, 4344),     # 上半年: 1-6月\n", "            (4344, 8760)   # 下半年: 7-12月\n", "        ]\n", "        period_names = [\"上半年\", \"下半年\"]\n", "    elif decomposition_type == 'annual':\n", "        time_periods = [(0, 8760)]  # 全年\n", "        period_names = [\"全年\"]\n", "    else:\n", "        raise ValueError(\"不支持的分解类型，请选择 'monthly', 'quarterly', 'semiannual' 或 'annual'\")\n", "    \n", "    # 定义年度生物质能发电量限制\n", "    annual_biomass_limits = {\n", "        'BRA': 58313069.30,\n", "        'CHN': 172918314.50,\n", "        'IND': 33727037.10,\n", "        'JPN': 39000000.00,\n", "        'RUS': 3779000.00,\n", "        'USA': 57156946.51,\n", "        'EU': 195878968.50,\n", "        'ROW': 126529322.22\n", "    }\n", "    \n", "    # 定义需要处理的所有区域（典型国家+EU+ROW）\n", "    regions = ['BRA', 'CHN', 'IND', 'JPN', 'RUS', 'USA', 'EU', 'ROW']\n", "    \n", "    # 为每个区域初始化存储净负荷比例的字典\n", "    region_load_ratios = {}\n", "    \n", "    # 为每个区域和每个时段初始化生物质能限制字典\n", "    biomass_generation_limits = {region: {} for region in regions}\n", "    \n", "    # 为每个区域计算净负荷分配\n", "    for region in regions:\n", "        # 获取该区域的索引\n", "        if region == 'EU':\n", "            region_indices = [iso_codes.index(c) for c in eu_countries if c in iso_codes]\n", "        elif region == 'ROW':\n", "            region_indices = [iso_codes.index(c) for c in row_countries if c in iso_codes]\n", "        else:\n", "            region_indices = [iso_codes.index(region)]\n", "        \n", "        # 计算各时段的净负荷\n", "        net_loads_raw = []\n", "        for start_hour, end_hour in time_periods:\n", "            period_net_load = 0\n", "            for idx in region_indices:\n", "                # 计算该区域在当前时段的净负荷\n", "                region_net_load = np.sum(\n", "                    load_data[start_hour:end_hour, idx] - \n", "                    wind_data[start_hour:end_hour, idx] - \n", "                    solar_data[start_hour:end_hour, idx] - \n", "                    hydro_data[start_hour:end_hour, idx] - \n", "                    nuclear_data[start_hour:end_hour, idx] - \n", "                    geothermal_data[start_hour:end_hour, idx]\n", "                )\n", "                period_net_load += region_net_load\n", "            net_loads_raw.append(period_net_load)\n", "        \n", "        # 归一化处理\n", "        min_load = min(net_loads_raw)\n", "        max_load = max(net_loads_raw)\n", "        \n", "        # 避免除零错误\n", "        if max_load > min_load:\n", "            net_loads_normalized = [(nl - min_load) / (max_load - min_load) for nl in net_loads_raw]\n", "        else:\n", "            net_loads_normalized = [1.0 / len(time_periods) for _ in time_periods]\n", "        \n", "        # 确保归一化值的总和不为零\n", "        total_normalized = sum(net_loads_normalized)\n", "        if total_normalized > 0:\n", "            net_load_ratios = [nl / total_normalized for nl in net_loads_normalized]\n", "        else:\n", "            net_load_ratios = [1.0 / len(time_periods) for _ in time_periods]\n", "        \n", "        # 获取该区域的年度生物质能限制和容量约束\n", "        annual_biomass_limit = annual_biomass_limits[region]\n", "        \n", "        # 计算各区域容量约束下的最大可发电量\n", "        max_biomass_generation = {}\n", "        for i, (start_hour, end_hour) in enumerate(time_periods):\n", "            hours_in_period = end_hour - start_hour\n", "            \n", "            # 获取该区域的生物质能容量上限\n", "            if region == 'EU':\n", "                total_capacity = sum(biomass_capacity[idx] for idx in region_indices)\n", "            elif region == 'ROW':\n", "                total_capacity = sum(biomass_capacity[idx] for idx in region_indices)\n", "            else:\n", "                total_capacity = biomass_capacity[region_indices[0]]\n", "                \n", "            # 该时段最大可发电量 = 容量 × 时段小时数\n", "            max_biomass_generation[i] = total_capacity * hours_in_period\n", "        # 打印区域信息\n", "        print(f\"\\n{region}区域资源分配：\")\n", "        print(f\"原始净负荷值(MWh): {[nl/1e6 for nl in net_loads_raw]}\")\n", "        \n", "        # 首先根据净负荷占比分配生物质能限制\n", "        preliminary_allocation = {}\n", "        for i, period in enumerate(period_names):\n", "            preliminary_allocation[i] = annual_biomass_limit * net_load_ratios[i]\n", "            \n", "        # 检查是否有任何时段的分配超过了最大可发电量\n", "        excess_allocation = {}\n", "        for i in range(len(time_periods)):\n", "            if preliminary_allocation[i] > max_biomass_generation[i]:\n", "                excess_allocation[i] = preliminary_allocation[i] - max_biomass_generation[i]\n", "                preliminary_allocation[i] = max_biomass_generation[i]\n", "            else:\n", "                excess_allocation[i] = 0\n", "                \n", "        # 重新分配多余的生物质能\n", "        total_excess = sum(excess_allocation.values())\n", "        if total_excess > 0:\n", "            # 找出哪些时段有余量可以接收额外分配\n", "            available_periods = [i for i in range(len(time_periods)) \n", "                               if preliminary_allocation[i] < max_biomass_generation[i]]\n", "            \n", "            if available_periods:\n", "                # 计算可用时段的总空余容量\n", "                total_available_capacity = sum(\n", "                    [max_biomass_generation[i] - preliminary_allocation[i] for i in available_periods])\n", "                \n", "                # 按比例分配多余的电量\n", "                if total_available_capacity > 0:\n", "                    for i in available_periods:\n", "                        available_capacity = max_biomass_generation[i] - preliminary_allocation[i]\n", "                        additional_allocation = (available_capacity / total_available_capacity) * total_excess\n", "                        preliminary_allocation[i] += min(additional_allocation, available_capacity)\n", "        \n", "        # 最终分配结果\n", "        for i, period in enumerate(period_names):\n", "            biomass_generation_limits[region][i] = preliminary_allocation[i]\n", "            \n", "            print(f\"{period} - 净负荷: {net_loads_raw[i]/1e6:.2f} TWh, \"\n", "                  f\"归一化比例: {net_load_ratios[i]*100:.2f}%, \"\n", "                  f\"初始分配: {annual_biomass_limit * net_load_ratios[i]/1e6:.2f} TWh, \"\n", "                  f\"最大可发电量: {max_biomass_generation[i]/1e6:.2f} TWh, \"\n", "                  f\"最终分配: {biomass_generation_limits[region][i]/1e6:.2f} TWh\")\n", "    \n", "    # 尝试加载月度优化结果作为热启动初始值\n", "    monthly_results = None\n", "    if warm_start and decomposition_type != 'monthly':\n", "        try:\n", "            with open('V4_Monthly_full_optimization_results.pkl', 'rb') as f:\n", "                monthly_results = pickle.load(f)\n", "            print(f\"已加载月度优化结果作为{decomposition_type}优化的热启动初始值\")\n", "        except FileNotFoundError:\n", "            print(\"未找到月度优化结果文件，将使用冷启动\")\n", "\n", "    # 存储每个时段的结果\n", "    all_period_results = []\n", "    # 初始化储能SOC\n", "    current_soc = {i: storage_soc_init for i in range(num_countries)}\n", "    \n", "    # 按时段求解\n", "    for p, ((start_hour, end_hour), period_name) in enumerate(zip(time_periods, period_names)):\n", "        print(f\"\\n求解{period_name}...\")\n", "        \n", "        # 获取当前时段的时序数据\n", "        num_periods = end_hour - start_hour\n", "        \n", "        # 创建模型\n", "        model = gp.Model(f'Global_Economic_Dispatch_{period_name}')\n", "        \n", "        # 添加变量\n", "        P_gas = model.addMVar((num_periods, num_countries), vtype=GRB.CONTINUOUS, name=\"P_gas\")\n", "        P_oil = model.addMVar((num_periods, num_countries), vtype=GRB.CONTINUOUS, name=\"P_oil\")\n", "        P_coal = model.addMVar((num_periods, num_countries), vtype=GRB.CONTINUOUS, name=\"P_coal\")\n", "        P_wind = model.addMVar((num_periods, num_countries), vtype=GRB.CONTINUOUS, name=\"P_wind\")\n", "        P_solar = model.addMVar((num_periods, num_countries), vtype=GRB.CONTINUOUS, name=\"P_solar\")\n", "        P_hydro = model.addMVar((num_periods, num_countries), vtype=GRB.CONTINUOUS, name=\"P_hydro\")\n", "        P_nuclear = model.addMVar((num_periods, num_countries), vtype=GRB.CONTINUOUS, name=\"P_nuclear\")\n", "        P_geothermal = model.addMVar((num_periods, num_countries), vtype=GRB.CONTINUOUS, name=\"P_geothermal\")\n", "        P_biomass = model.addMVar((num_periods, num_countries), vtype=GRB.CONTINUOUS, name=\"P_biomass\")\n", "\n", "        P_cha = model.addMVar((num_periods, num_countries), vtype=GRB.CONTINUOUS, name=\"P_cha\")\n", "        P_dis = model.addMVar((num_periods, num_countries), vtype=GRB.CONTINUOUS, name=\"P_dis\")\n", "        E_storage = model.addMVar((num_periods, num_countries), vtype=GRB.CONTINUOUS, name=\"E_storage\")\n", "\n", "        P_shedding = model.addMVar((num_periods, num_countries), vtype=GRB.CONTINUOUS, name=\"P_shedding\")\n", "        P_curtail = model.addMVar((num_periods, num_countries), vtype=GRB.CONTINUOUS, name=\"P_curtail\")\n", "\n", "        P_trade_hour = model.addMVar((num_periods, num_countries), vtype=GRB.CONTINUOUS, lb=-GRB.INFINITY, name=\"P_trade_hour\")\n", "        P_trans = model.addMVar((num_periods, num_translines), vtype=GRB.CONTINUOUS, lb=-GRB.INFINITY, name=\"P_trans\")\n", "\n", "        # 如果有热启动数据，设置变量初始值\n", "        if warm_start and monthly_results is not None:\n", "            # 确定当前时段对应的月度结果\n", "            if decomposition_type == 'quarterly':\n", "                # 第一季度对应1-3月\n", "                if p == 0:\n", "                    monthly_indices = [0, 1, 2]\n", "                # 第二季度对应4-6月\n", "                elif p == 1:\n", "                    monthly_indices = [3, 4, 5]\n", "                # 第三季度对应7-9月\n", "                elif p == 2:\n", "                    monthly_indices = [6, 7, 8]\n", "                # 第四季度对应10-12月\n", "                else:\n", "                    monthly_indices = [9, 10, 11]\n", "            elif decomposition_type == 'semiannual':\n", "                # 上半年对应1-6月\n", "                if p == 0:\n", "                    monthly_indices = [0, 1, 2, 3, 4, 5]\n", "                # 下半年对应7-12月\n", "                else:\n", "                    monthly_indices = [6, 7, 8, 9, 10, 11]\n", "            elif decomposition_type == 'annual':\n", "                # 全年对应1-12月\n", "                monthly_indices = list(range(12))\n", "            \n", "            # 设置变量初始值\n", "            try:\n", "                # 合并相关月份的数据\n", "                combined_data = {}\n", "                for var_name in ['P_coal', 'P_gas', 'P_oil', 'P_wind', 'P_solar', 'P_biomass', \n", "                                'P_cha', 'P_dis', 'P_shedding', 'P_curtail', 'P_trade_hour', 'P_trans']:\n", "                    combined_data[var_name] = np.vstack([monthly_results[i][var_name] for i in monthly_indices])\n", "                \n", "                # 设置变量初始值\n", "                P_coal.start = combined_data['P_coal']\n", "                P_gas.start = combined_data['P_gas']\n", "                P_oil.start = combined_data['P_oil']\n", "                # P_wind.start = combined_data['P_wind']\n", "                # P_solar.start = combined_data['P_solar']\n", "                # P_biomass.start = combined_data['P_biomass']\n", "                # P_cha.start = combined_data['P_cha']\n", "                # P_dis.start = combined_data['P_dis']\n", "                # P_shedding.start = combined_data['P_shedding']\n", "                # P_curtail.start = combined_data['P_curtail']\n", "                # P_trade_hour.start = combined_data['P_trade_hour']\n", "                # P_trans.start = combined_data['P_trans']\n", "                \n", "                # 设置储能初始SOC\n", "                if p == 0:\n", "                    # 第一个时段使用月度结果的初始SOC\n", "                    for i in range(num_countries):\n", "                        if storage_capacity[i] > 0:\n", "                            current_soc[i] = monthly_results[0]['final_soc'][i]\n", "                \n", "                print(f\"已为{period_name}设置热启动初始值\")\n", "            except Exception as e:\n", "                print(f\"设置热启动初始值时出错: {e}\")\n", "                print(\"将使用冷启动\")\n", "\n", "        # 过滤有效国家\n", "        active_countries = [i for i in range(num_countries) if np.any(load_data[start_hour:end_hour, i] > 0)]\n", "\n", "        # 添加火电机组总出力变量\n", "        P_thermal = model.addMVar((num_periods, num_countries), vtype=GRB.CONTINUOUS, name=\"P_thermal\")\n", "        \n", "        # 火电机组总出力等于三种机组之和\n", "        model.addConstr(\n", "            P_thermal == P_coal + P_gas + P_oil,\n", "            name=\"thermal_sum\"\n", "        )\n", "        \n", "        # 各类型火电的上限约束\n", "        model.addConstr(P_coal[:, active_countries] <= coal_capacity[active_countries], name=\"coal_upper\")\n", "        model.addConstr(P_gas[:, active_countries] <= gas_capacity[active_countries], name=\"gas_upper\")\n", "        model.addConstr(P_oil[:, active_countries] <= oil_capacity[active_countries], name=\"oil_upper\")\n", "        \n", "        # 设置系统层技术性最小出力比例\n", "        x_coal = 0.01    # 煤电系统最小出力比例1%\n", "        x_gas = 0.005    # 气电系统最小出力比例0.5%\n", "        x_oil = 0.003    # 油电系统最小出力比例0.3%\n", "        \n", "        # 火电机组出力比例\n", "        generation_ratios = {\n", "            'BRA': {'coal': 0.2366, 'gas': 0.6179, 'oil': 0.1455},\n", "            'CHN': {'coal': 0.9522, 'gas': 0.0458, 'oil': 0.0020},\n", "            'IND': {'coal': 0.9457, 'gas': 0.0496, 'oil': 0.0047},\n", "            'JPN': {'coal': 0.4524, 'gas': 0.5028, 'oil': 0.0448},\n", "            'RUS': {'coal': 0.2494, 'gas': 0.7385, 'oil': 0.0121},\n", "            'USA': {'coal': 0.2738, 'gas': 0.7198, 'oil': 0.0064},\n", "            'EU': {'coal': 0.3887, 'gas': 0.5605, 'oil': 0.0508},\n", "            'ROW': {'coal': 0.3156, 'gas': 0.5637, 'oil': 0.1207}\n", "        }\n", "        \n", "        # 1. 处理六个典型国家\n", "        for country in typical_countries:\n", "            if country in generation_ratios:\n", "                country_idx = iso_codes.index(country)\n", "                \n", "                # 获取该国三类火电装机容量\n", "                c_coal = coal_capacity[country_idx]\n", "                c_gas = gas_capacity[country_idx]\n", "                c_oil = oil_capacity[country_idx]\n", "                \n", "                # 获取该国火电各类机组占年发电量比例\n", "                a_coal = generation_ratios[country]['coal']\n", "                a_gas = generation_ratios[country]['gas']\n", "                a_oil = generation_ratios[country]['oil']\n", "                \n", "                # 计算该国综合最小出力\n", "                country_min_output = x_coal * c_coal + x_gas * c_gas + x_oil * c_oil\n", "                \n", "                # 计算修正后的最小出力比例\n", "                # 煤电修正最小出力\n", "                if c_coal > 0:\n", "                    coal_ratio_based_min = a_coal * country_min_output / c_coal\n", "                    corrected_coal_min = max(x_coal, coal_ratio_based_min)\n", "                else:\n", "                    corrected_coal_min = x_coal\n", "                \n", "                # 气电修正最小出力\n", "                if c_gas > 0:\n", "                    gas_ratio_based_min = a_gas * country_min_output / c_gas\n", "                    corrected_gas_min = max(x_gas, gas_ratio_based_min)\n", "                else:\n", "                    corrected_gas_min = x_gas\n", "                \n", "                # 油电修正最小出力\n", "                if c_oil > 0:\n", "                    oil_ratio_based_min = a_oil * country_min_output / c_oil\n", "                    corrected_oil_min = max(x_oil, oil_ratio_based_min)\n", "                else:\n", "                    corrected_oil_min = x_oil\n", "                \n", "                # 添加约束\n", "                # 煤电最小出力约束\n", "                if c_coal > 0:\n", "                    model.addConstr(\n", "                        P_coal[:, country_idx] >= corrected_coal_min * c_coal,\n", "                        name=f\"coal_min_{country}\"\n", "                    )\n", "                \n", "                # 气电最小出力约束\n", "                if c_gas > 0:\n", "                    model.addConstr(\n", "                        P_gas[:, country_idx] >= corrected_gas_min * c_gas,\n", "                        name=f\"gas_min_{country}\"\n", "                    )\n", "                \n", "                # 油电最小出力约束\n", "                if c_oil > 0:\n", "                    model.addConstr(\n", "                        P_oil[:, country_idx] >= corrected_oil_min * c_oil,\n", "                        name=f\"oil_min_{country}\"\n", "                    )\n", "        \n", "        # 2. 处理欧盟区域\n", "        # 计算欧盟区域内各类机组总装机容量\n", "        eu_coal_capacity = sum(coal_capacity[iso_codes.index(country)] for country in eu_countries if country in iso_codes)\n", "        eu_gas_capacity = sum(gas_capacity[iso_codes.index(country)] for country in eu_countries if country in iso_codes)\n", "        eu_oil_capacity = sum(oil_capacity[iso_codes.index(country)] for country in eu_countries if country in iso_codes)\n", "        \n", "        # 欧盟区域火电各类机组占年发电量比例\n", "        eu_a_coal = generation_ratios['EU']['coal']\n", "        eu_a_gas = generation_ratios['EU']['gas']\n", "        eu_a_oil = generation_ratios['EU']['oil']\n", "        \n", "        # 计算欧盟区域综合最小出力\n", "        eu_min_output = x_coal * eu_coal_capacity + x_gas * eu_gas_capacity + x_oil * eu_oil_capacity\n", "        \n", "        # 计算修正后的欧盟区域各类机组最小出力比例\n", "        # 煤电\n", "        if eu_coal_capacity > 0:\n", "            eu_coal_ratio_based_min = eu_a_coal * eu_min_output / eu_coal_capacity\n", "            eu_corrected_coal_min = max(x_coal, eu_coal_ratio_based_min)\n", "        else:\n", "            eu_corrected_coal_min = x_coal\n", "        \n", "        # 气电\n", "        if eu_gas_capacity > 0:\n", "            eu_gas_ratio_based_min = eu_a_gas * eu_min_output / eu_gas_capacity\n", "            eu_corrected_gas_min = max(x_gas, eu_gas_ratio_based_min)\n", "        else:\n", "            eu_corrected_gas_min = x_gas\n", "        \n", "        # 油电\n", "        if eu_oil_capacity > 0:\n", "            eu_oil_ratio_based_min = eu_a_oil * eu_min_output / eu_oil_capacity\n", "            eu_corrected_oil_min = max(x_oil, eu_oil_ratio_based_min)\n", "        else:\n", "            eu_corrected_oil_min = x_oil\n", "        \n", "        # 为欧盟区域各国添加约束\n", "        for country in eu_countries:\n", "            if country in iso_codes:\n", "                country_idx = iso_codes.index(country)\n", "                \n", "                # 煤电最小出力约束\n", "                if coal_capacity[country_idx] > 0:\n", "                    model.addConstr(\n", "                        P_coal[:, country_idx] >= eu_corrected_coal_min * coal_capacity[country_idx],\n", "                        name=f\"coal_min_eu_{country}\"\n", "                    )\n", "                \n", "                # 气电最小出力约束\n", "                if gas_capacity[country_idx] > 0:\n", "                    model.addConstr(\n", "                        P_gas[:, country_idx] >= eu_corrected_gas_min * gas_capacity[country_idx],\n", "                        name=f\"gas_min_eu_{country}\"\n", "                    )\n", "                \n", "                # 油电最小出力约束\n", "                if oil_capacity[country_idx] > 0:\n", "                    model.addConstr(\n", "                        P_oil[:, country_idx] >= eu_corrected_oil_min * oil_capacity[country_idx],\n", "                        name=f\"oil_min_eu_{country}\"\n", "                    )\n", "        \n", "        # 3. 处理世界其他国家(ROW)区域\n", "        # 计算ROW区域内各类机组总装机容量\n", "        row_coal_capacity = sum(coal_capacity[iso_codes.index(country)] for country in row_countries if country in iso_codes)\n", "        row_gas_capacity = sum(gas_capacity[iso_codes.index(country)] for country in row_countries if country in iso_codes)\n", "        row_oil_capacity = sum(oil_capacity[iso_codes.index(country)] for country in row_countries if country in iso_codes)\n", "        \n", "        # ROW区域火电各类机组占年发电量比例\n", "        row_a_coal = generation_ratios['ROW']['coal']\n", "        row_a_gas = generation_ratios['ROW']['gas']\n", "        row_a_oil = generation_ratios['ROW']['oil']\n", "        \n", "        # 计算ROW区域综合最小出力\n", "        row_min_output = x_coal * row_coal_capacity + x_gas * row_gas_capacity + x_oil * row_oil_capacity\n", "        \n", "        # 计算修正后的ROW区域各类机组最小出力比例\n", "        # 煤电\n", "        if row_coal_capacity > 0:\n", "            row_coal_ratio_based_min = row_a_coal * row_min_output / row_coal_capacity\n", "            row_corrected_coal_min = max(x_coal, row_coal_ratio_based_min)\n", "        else:\n", "            row_corrected_coal_min = x_coal\n", "        \n", "        # 气电\n", "        if row_gas_capacity > 0:\n", "            row_gas_ratio_based_min = row_a_gas * row_min_output / row_gas_capacity\n", "            row_corrected_gas_min = max(x_gas, row_gas_ratio_based_min)\n", "        else:\n", "            row_corrected_gas_min = x_gas\n", "        \n", "        # 油电\n", "        if row_oil_capacity > 0:\n", "            row_oil_ratio_based_min = row_a_oil * row_min_output / row_oil_capacity\n", "            row_corrected_oil_min = max(x_oil, row_oil_ratio_based_min)\n", "        else:\n", "            row_corrected_oil_min = x_oil\n", "        \n", "        # 为ROW区域各国添加约束\n", "        for country in row_countries:\n", "            if country in iso_codes:\n", "                country_idx = iso_codes.index(country)\n", "                \n", "                # 煤电最小出力约束\n", "                if coal_capacity[country_idx] > 0:\n", "                    model.addConstr(\n", "                        P_coal[:, country_idx] >= row_corrected_coal_min * coal_capacity[country_idx],\n", "                        name=f\"coal_min_row_{country}\"\n", "                    )\n", "                \n", "                # 气电最小出力约束\n", "                if gas_capacity[country_idx] > 0:\n", "                    model.addConstr(\n", "                        P_gas[:, country_idx] >= row_corrected_gas_min * gas_capacity[country_idx],\n", "                        name=f\"gas_min_row_{country}\"\n", "                    )\n", "                \n", "                # 油电最小出力约束\n", "                if oil_capacity[country_idx] > 0:\n", "                    model.addConstr(\n", "                        P_oil[:, country_idx] >= row_corrected_oil_min * oil_capacity[country_idx],\n", "                        name=f\"oil_min_row_{country}\"\n", "                    )\n", "        \n", "        # 添加爬坡约束 (Ramping Constraints)\n", "        # 定义各类型电厂的爬坡率限制 (每小时最大变化率，相对于装机容量的百分比)\n", "        coal_ramp_rate = 0.3  # 煤电爬坡率\n", "        gas_ramp_rate = 0.4   # 气电爬坡率\n", "        oil_ramp_rate = 0.4   # 油电爬坡率\n", "        \n", "        # 对每个时段(除第一个时段外)添加爬坡约束\n", "        for t in range(1, end_hour - start_hour):\n", "            # 煤电爬坡约束\n", "            model.addConstr(\n", "                P_coal[t, active_countries] - P_coal[t-1, active_countries] <= coal_ramp_rate * coal_capacity[active_countries],\n", "                name=f\"coal_ramp_up_{t}\"\n", "            )\n", "            model.addConstr(\n", "                P_coal[t-1, active_countries] - P_coal[t, active_countries] <= coal_ramp_rate * coal_capacity[active_countries],\n", "                name=f\"coal_ramp_down_{t}\"\n", "            )\n", "            \n", "            # 气电爬坡约束\n", "            model.addConstr(\n", "                P_gas[t, active_countries] - P_gas[t-1, active_countries] <= gas_ramp_rate * gas_capacity[active_countries],\n", "                name=f\"gas_ramp_up_{t}\"\n", "            )\n", "            model.addConstr(\n", "                P_gas[t-1, active_countries] - P_gas[t, active_countries] <= gas_ramp_rate * gas_capacity[active_countries],\n", "                name=f\"gas_ramp_down_{t}\"\n", "            )\n", "            \n", "            # 油电爬坡约束\n", "            model.addConstr(\n", "                P_oil[t, active_countries] - P_oil[t-1, active_countries] <= oil_ramp_rate * oil_capacity[active_countries],\n", "                name=f\"oil_ramp_up_{t}\"\n", "            )\n", "            model.addConstr(\n", "                P_oil[t-1, active_countries] - P_oil[t, active_countries] <= oil_ramp_rate * oil_capacity[active_countries],\n", "                name=f\"oil_ramp_down_{t}\"\n", "            )\n", "        \n", "        # 参数设定部分添加\n", "        ratio_tolerance = 0.0001  # 0.1%的容差\n", "\n", "        # 修改后的约束结构\n", "        for country in typical_countries:\n", "            if country in generation_ratios and country not in eu_countries and country not in row_countries:\n", "                country_idx = iso_codes.index(country)\n", "                ratios = generation_ratios[country]\n", "                total_thermal = P_thermal[:, country_idx].sum()\n", "                \n", "                # 改为范围约束\n", "                model.addConstr(P_coal[:, country_idx].sum() >= (ratios['coal'] - ratio_tolerance) * total_thermal,\n", "                               name=f\"coal_ratio_{country}_lower_{p}\")\n", "                model.addConstr(P_coal[:, country_idx].sum() <= (ratios['coal'] + ratio_tolerance) * total_thermal,\n", "                               name=f\"coal_ratio_{country}_upper_{p}\")\n", "                \n", "                model.addConstr(P_gas[:, country_idx].sum() >= (ratios['gas'] - ratio_tolerance) * total_thermal,\n", "                               name=f\"gas_ratio_{country}_lower_{p}\")\n", "                model.addConstr(P_gas[:, country_idx].sum() <= (ratios['gas'] + ratio_tolerance) * total_thermal,\n", "                               name=f\"gas_ratio_{country}_upper_{p}\")\n", "                \n", "                model.addConstr(P_oil[:, country_idx].sum() >= (ratios['oil'] - ratio_tolerance) * total_thermal,\n", "                               name=f\"oil_ratio_{country}_lower_{p}\")\n", "                model.addConstr(P_oil[:, country_idx].sum() <= (ratios['oil'] + ratio_tolerance) * total_thermal,\n", "                               name=f\"oil_ratio_{country}_upper_{p}\")\n", "\n", "        # 欧盟整体约束\n", "        eu_total_thermal = sum(P_thermal[:, iso_codes.index(country)].sum()\n", "                             for country in eu_countries if country in iso_codes)\n", "        eu_total_coal = sum(P_coal[:, iso_codes.index(country)].sum()\n", "                           for country in eu_countries if country in iso_codes)\n", "        eu_total_gas = sum(P_gas[:, iso_codes.index(country)].sum()\n", "                          for country in eu_countries if country in iso_codes)\n", "        eu_total_oil = sum(P_oil[:, iso_codes.index(country)].sum()\n", "                          for country in eu_countries if country in iso_codes)\n", "\n", "        # 改为范围约束\n", "        model.addConstr(eu_total_coal >= (generation_ratios['EU']['coal'] - ratio_tolerance) * eu_total_thermal,\n", "                       name=f\"eu_coal_ratio_lower_{p}\")\n", "        model.addConstr(eu_total_coal <= (generation_ratios['EU']['coal'] + ratio_tolerance) * eu_total_thermal,\n", "                       name=f\"eu_coal_ratio_upper_{p}\")\n", "\n", "        model.addConstr(eu_total_gas >= (generation_ratios['EU']['gas'] - ratio_tolerance) * eu_total_thermal,\n", "                       name=f\"eu_gas_ratio_lower_{p}\")\n", "        model.addConstr(eu_total_gas <= (generation_ratios['EU']['gas'] + ratio_tolerance) * eu_total_thermal,\n", "                       name=f\"eu_gas_ratio_upper_{p}\")\n", "\n", "        model.addConstr(eu_total_oil >= (generation_ratios['EU']['oil'] - ratio_tolerance) * eu_total_thermal,\n", "                       name=f\"eu_oil_ratio_lower_{p}\")\n", "        model.addConstr(eu_total_oil <= (generation_ratios['EU']['oil'] + ratio_tolerance) * eu_total_thermal,\n", "                       name=f\"eu_oil_ratio_upper_{p}\")\n", "\n", "        # ROW整体约束\n", "        row_total_thermal = sum(P_thermal[:, iso_codes.index(country)].sum()\n", "                              for country in row_countries if country in iso_codes)\n", "        row_total_coal = sum(P_coal[:, iso_codes.index(country)].sum()\n", "                            for country in row_countries if country in iso_codes)\n", "        row_total_gas = sum(P_gas[:, iso_codes.index(country)].sum()\n", "                           for country in row_countries if country in iso_codes)\n", "        row_total_oil = sum(P_oil[:, iso_codes.index(country)].sum()\n", "                           for country in row_countries if country in iso_codes)\n", "\n", "        model.addConstr(row_total_coal >= (generation_ratios['ROW']['coal'] - ratio_tolerance) * row_total_thermal,\n", "                       name=f\"row_coal_ratio_lower_{p}\")\n", "        model.addConstr(row_total_coal <= (generation_ratios['ROW']['coal'] + ratio_tolerance) * row_total_thermal,\n", "                       name=f\"row_coal_ratio_upper_{p}\")\n", "\n", "        model.addConstr(row_total_gas >= (generation_ratios['ROW']['gas'] - ratio_tolerance) * row_total_thermal,\n", "                       name=f\"row_gas_ratio_lower_{p}\")\n", "        model.addConstr(row_total_gas <= (generation_ratios['ROW']['gas'] + ratio_tolerance) * row_total_thermal,\n", "                       name=f\"row_gas_ratio_upper_{p}\")\n", "\n", "        model.addConstr(row_total_oil >= (generation_ratios['ROW']['oil'] - ratio_tolerance) * row_total_thermal,\n", "                       name=f\"row_oil_ratio_lower_{p}\")\n", "        model.addConstr(row_total_oil <= (generation_ratios['ROW']['oil'] + ratio_tolerance) * row_total_thermal,\n", "                       name=f\"row_oil_ratio_upper_{p}\")\n", "\n", "        # 约束2: 可再生能源出力约束\n", "        model.addConstr(P_wind[:, active_countries] <= wind_data[start_hour:end_hour, active_countries], name=\"wind_limit\")\n", "        model.addConstr(P_solar[:, active_countries] <= solar_data[start_hour:end_hour, active_countries], name=\"solar_limit\")\n", "        model.addConstr(P_hydro[:, active_countries] == hydro_data[start_hour:end_hour, active_countries], name=\"hydro_output\")\n", "        model.addConstr(P_nuclear[:, active_countries] == nuclear_data[start_hour:end_hour, active_countries], name=\"nuclear_output\")\n", "        model.addConstr(P_geothermal[:, active_countries] == geothermal_data[start_hour:end_hour, active_countries], name=\"geothermal_output\")\n", "        model.addConstr(P_biomass[:, active_countries] <= biomass_capacity[active_countries], name=\"biomass_upper\")\n", "        biomass_min = 0.0\n", "        model.addConstr(P_biomass[:, active_countries] >= biomass_min * biomass_capacity[active_countries], name=\"biomass_lower\")\n", "        # 定义弃电量为未利用的光伏和风电之和\n", "        renewable_available = wind_data[start_hour:end_hour, active_countries] + solar_data[start_hour:end_hour, active_countries]\n", "        renewable_used = P_wind[:, active_countries] + P_solar[:, active_countries]\n", "        model.addConstr(P_curtail[:, active_countries] >= renewable_available - renewable_used, name=\"curtailment_definition\")\n", "        \n", "        # 生物质能时段发电量约束（添加容差）\n", "        tolerance_biomass = 0.0001  # 0.01%的容差\n", "        \n", "        for region in regions:\n", "            if region == 'EU':\n", "                # 欧盟作为一个整体处理\n", "                region_indices = [iso_codes.index(c) for c in eu_countries if c in iso_codes]\n", "            elif region == 'ROW':\n", "                # ROW作为一个整体处理\n", "                region_indices = [iso_codes.index(c) for c in row_countries if c in iso_codes]\n", "            else:\n", "                # 单个国家\n", "                region_indices = [iso_codes.index(region)]\n", "            \n", "            # 获取当前时段的生物质能限制目标\n", "            target_value = biomass_generation_limits[region][p]\n", "            lower_bound = target_value * (1 - tolerance_biomass)\n", "            upper_bound = target_value * (1 + tolerance_biomass)\n", "            \n", "            # 添加生物质能约束\n", "            if len(region_indices) == 1:\n", "                # 单个国家的约束\n", "                idx = region_indices[0]\n", "                model.addConstr(P_biomass[:, idx].sum() >= lower_bound, \n", "                              name=f\"biomass_generation_{region}_lower\")\n", "                model.addConstr(P_biomass[:, idx].sum() <= upper_bound, \n", "                              name=f\"biomass_generation_{region}_upper\")\n", "            else:\n", "                # 区域的约束（多个国家的总和）\n", "                region_biomass_sum = gp.quicksum(P_biomass[:, idx].sum() for idx in region_indices)\n", "                model.addConstr(region_biomass_sum >= lower_bound, \n", "                              name=f\"biomass_generation_{region}_lower\")\n", "                model.addConstr(region_biomass_sum <= upper_bound, \n", "                              name=f\"biomass_generation_{region}_upper\")\n", "        \n", "        # 约束3: 储能系统约束\n", "        charging_status = {}\n", "        for i in active_countries:\n", "            if storage_capacity[i] > 0:\n", "                if storage_efficiency[i] == 0 or np.isnan(storage_efficiency[i]):\n", "                    print(f\"国家 {iso_codes[i]} 的储能效率为零或缺失，跳过储能约束。\")\n", "                    continue\n", "\n", "                charging_status[i] = model.addMVar(num_periods, vtype=GRB.BINARY, name=f\"charging_status_{i}\")\n", "                \n", "                # 初始时刻储能约束\n", "                model.addConstr(E_storage[0, i] == storage_capacity[i] * current_soc[i] + \n", "                              P_cha[0, i] * storage_efficiency[i] - P_dis[0, i] / storage_efficiency[i], \n", "                              name=f\"storage_init_{i}\")\n", "                \n", "                # 储能动态方程\n", "                model.addConstr(E_storage[1:, i] == E_storage[:-1, i] + \n", "                              P_cha[1:, i] * storage_efficiency[i] - P_dis[1:, i] / storage_efficiency[i],\n", "                              name=f\"storage_dynamic_{i}\")\n", "                \n", "                # 如果是最后一个时段,终端约束为初始SOC\n", "                if p == len(time_periods) - 1:\n", "                    model.addConstr(E_storage[-1, i] == storage_capacity[i] * storage_soc_init, \n", "                                  name=f\"storage_final_{i}\")\n", "                \n", "                # 储能容量约束\n", "                model.addConstr(E_storage[:, i] >= storage_capacity[i] * storage_soc_lower[i], \n", "                              name=f\"storage_lower_{i}\")\n", "                model.addConstr(E_storage[:, i] <= storage_capacity[i] * storage_soc_upper[i], \n", "                              name=f\"storage_upper_{i}\")\n", "                \n", "                # 充放电功率约束\n", "                model.addConstr(P_cha[:, i] <= storage_max_power[i] * charging_status[i], \n", "                              name=f\"charging_power_{i}\")\n", "                model.addConstr(P_dis[:, i] <= storage_max_power[i] * (1 - charging_status[i]), \n", "                              name=f\"discharging_power_{i}\")\n", "            else:\n", "                model.addConstr(P_cha[:, i] == 0, name=f\"no_charging_{i}\")\n", "                model.addConstr(P_dis[:, i] == 0, name=f\"no_discharging_{i}\")\n", "\n", "        # 约束4: 传输容量约束\n", "        model.addConstr(P_trans <= max_flows, name=\"trans_upper\")\n", "        model.addConstr(P_trans >= -max_counter_flows, name=\"trans_lower\")\n", "\n", "        # 约束5: 电力贸易平衡\n", "        for i in active_countries:\n", "            index_translines_in = [idx for idx, to in enumerate(trans_to) if to == iso_codes[i]]\n", "            index_translines_out = [idx for idx, frm in enumerate(trans_from) if frm == iso_codes[i]]\n", "            model.addConstr(\n", "                P_trade_hour[:, i] == gp.quicksum(P_trans[:, idx] for idx in index_translines_in) -\n", "                                     gp.quicksum(P_trans[:, idx] for idx in index_translines_out),\n", "                name=f\"trade_balance_{i}\"\n", "            )\n", "\n", "        # 约束6: 电力平衡约束\n", "        for i in active_countries:\n", "            P_load = load_data[start_hour:end_hour, i]\n", "            model.addConstr(\n", "                P_gas[:, i] + P_oil[:, i] + P_coal[:, i] + P_biomass[:, i] +\n", "                P_wind[:, i] + P_solar[:, i] + P_hydro[:, i] + P_nuclear[:, i] + P_geothermal[:, i] +\n", "                P_dis[:, i] + P_shedding[:, i] + P_trade_hour[:, i] ==\n", "                P_load + P_cha[:, i] + P_curtail[:, i],\n", "                name=f\"power_balance_{i}\"\n", "            )\n", "        \n", "        # 约束7: 无效国家变量限制\n", "        inactive_countries = [i for i in range(num_countries) if i not in active_countries]\n", "        for i in inactive_countries:\n", "            model.addConstr(P_gas[:, i] == 0, name=f\"inactive_gas_{i}\")\n", "            model.addConstr(P_oil[:, i] == 0, name=f\"inactive_oil_{i}\")\n", "            model.addConstr(P_coal[:, i] == 0, name=f\"inactive_coal_{i}\")\n", "            model.addConstr(P_wind[:, i] == 0, name=f\"inactive_wind_{i}\")\n", "            model.addConstr(P_solar[:, i] == 0, name=f\"inactive_solar_{i}\")\n", "            model.addConstr(P_hydro[:, i] == 0, name=f\"inactive_hydro_{i}\")\n", "            model.addConstr(P_nuclear[:, i] == 0, name=f\"inactive_nuclear_{i}\")\n", "            model.addConstr(P_geothermal[:, i] == 0, name=f\"inactive_geothermal_{i}\")\n", "            model.addConstr(P_biomass[:, i] == 0, name=f\"inactive_biomass_{i}\")\n", "            model.addConstr(P_cha[:, i] == 0, name=f\"inactive_charging_{i}\")\n", "            model.addConstr(P_dis[:, i] == 0, name=f\"inactive_discharging_{i}\")\n", "            model.addConstr(E_storage[:, i] == 0, name=f\"inactive_storage_{i}\")\n", "            model.addConstr(P_shedding[:, i] == 0, name=f\"inactive_shedding_{i}\")\n", "            model.addConstr(P_curtail[:, i] == 0, name=f\"inactive_curtail_{i}\")\n", "            model.addConstr(P_trade_hour[:, i] == 0, name=f\"inactive_trade_{i}\")\n", "\n", "        # 约束8: 时段净进口量约束\n", "        tolerance_trade = 1e-6\n", "        \n", "        # 根据时段的小时数占比来调整净进口量\n", "        hours_in_period = end_hour - start_hour\n", "        total_hours_in_year = 8760  # 一年的总小时数\n", "        import_factor = hours_in_period / total_hours_in_year\n", "\n", "        for i in range(num_countries):\n", "            net_import = gp.quicksum(P_trade_hour[:, i])\n", "            actual_net_import = (electricity_imports[i] - electricity_exports[i]) * import_factor\n", "\n", "            if actual_net_import >= 0:\n", "                model.addConstr(net_import >= actual_net_import * (1 - tolerance_trade), name=f\"net_import_lower_{i}\")\n", "                model.addConstr(net_import <= actual_net_import * (1 + tolerance_trade), name=f\"net_import_upper_{i}\")\n", "            else:\n", "                model.addConstr(net_import >= actual_net_import * (1 + tolerance_trade), name=f\"net_import_lower_{i}\")\n", "                model.addConstr(net_import <= actual_net_import * (1 - tolerance_trade), name=f\"net_import_upper_{i}\")\n", "\n", "        # 设置目标函数\n", "\n", "        total_cost = gp.quicksum(\n", "            price_gas_oil * P_gas_oil[:, i].sum() * heat_rate_gas_oil +  # 合并gas和oil\n", "            price_coal * P_coal[:, i].sum() * heat_rate_coal +\n", "            price_ls * P_shedding[:, i].sum() +\n", "            price_cur * P_curtail[:, i].sum()\n", "            for i in range(num_countries)\n", "        )\n", "        \n", "        # P_trans_abs = model.addMVar((num_periods, num_translines), lb=0, vtype=GRB.CONTINUOUS, name=\"P_trans_abs\")\n", "        # for t in range(num_periods):\n", "        #     for i in range(num_translines):\n", "        #         model.addGenConstrAbs(P_trans_abs[t, i], P_trans[t, i], name=f\"trans_abs_{t}_{i}\")\n", "        # # 显式遍历所有元素进行求和\n", "        # trans_cost = 0.1\n", "        # transmission_cost = trans_cost * gp.quicksum(P_trans_abs[t, i] for t in range(num_periods) for i in range(num_translines))\n", "        # total_cost = total_cost + transmission_cost\n", "        \n", "        model.setObjective(total_cost, GRB.MINIMIZE)\n", "        time_limit = 10800  # 限制为1小时\n", "        model.setParam('TimeLimit', time_limit)\n", "        gap_tolerance = 0.001\n", "        model.setParam('MIPGap', gap_tolerance)\n", "\n", "        # 求解模型\n", "        model.optimize()\n", "\n", "        # 检查解的状态并保存结果\n", "        if model.status == GRB.OPTIMAL:\n", "            print(f\"{period_name}找到最优解\")\n", "            print(f\"目标值 (总成本): {model.ObjVal:,.2f}\")\n", "\n", "            # 更新储能SOC作为下一时段的初始值\n", "            for i in active_countries:\n", "                if storage_capacity[i] > 0 and storage_efficiency[i] > 0 and not np.isnan(storage_efficiency[i]):\n", "                    current_soc[i] = E_storage.X[-1, i] / storage_capacity[i]\n", "\n", "            # 保存时段结果\n", "            period_results = {\n", "                'P_coal': P_coal.X,\n", "                'P_gas': P_gas.X,\n", "                'P_oil': P_oil.X,\n", "                'P_wind': P_wind.X,\n", "                'P_solar': P_solar.X,\n", "                'P_hydro': P_hydro.X,\n", "                'P_nuclear': P_nuclear.X,\n", "                'P_geothermal': P_geothermal.X,\n", "                'P_biomass': P_biomass.X,\n", "                'P_cha': P_cha.X,\n", "                'P_dis': P_dis.X,\n", "                'E_storage': E_storage.X,\n", "                'P_shedding': P_shedding.X,\n", "                'P_curtail': P_curtail.X,\n", "                'P_trade_hour': P_trade_hour.X,\n", "                'P_trans': P_trans.X,\n", "                'objective_value': model.ObjVal,\n", "                'final_soc': current_soc,\n", "                'start_hour': start_hour,\n", "                'end_hour': end_hour\n", "            }\n", "            all_period_results.append(period_results)\n", "            \n", "            # 创建Excel写入器\n", "            with pd.ExcelWriter(f'V4_{decomposition_type.capitalize()}_Optimization_results_{period_name}.xlsx', engine='openpyxl') as writer:\n", "                # 为每个变量创建DataFrame并写入不同的sheet\n", "                variables = {\n", "                    '煤炭发电量': P_coal.X,\n", "                    '燃气发电量': P_gas.X,\n", "                    '石油发电量': P_oil.X,\n", "                    '风电发电量': P_wind.X,\n", "                    '太阳能发电量': P_solar.X,\n", "                    '水电发电量': P_hydro.X,\n", "                    '核电发电量': P_nuclear.X,\n", "                    '地热发电量': P_geothermal.X,\n", "                    '生物质发电量': P_biomass.X,\n", "                    '储能充电量': P_cha.X,\n", "                    '储能放电量': P_dis.X,\n", "                    '储能能量': E_storage.X,\n", "                    '负荷削减量': P_shedding.X,\n", "                    '可再生能源弃电量': P_curtail.X,\n", "                    '净电力交易量': P_trade_hour.X\n", "                }\n", "                \n", "                for name, data in variables.items():\n", "                    df = pd.DataFrame(data, columns=iso_codes)\n", "                    df.to_excel(writer, sheet_name=name, index=True)\n", "\n", "            print(f\"{period_name}结果已保存\")\n", "\n", "        elif model.status == GRB.INFEASIBLE:\n", "            print(f\"{period_name}模型不可行\")\n", "            model.computeIIS()\n", "            with open('Global_ED_V4_Decouple_带火电下限_IIS_Report.txt', 'w', encoding='utf-8') as f:\n", "                model.write(\"Global_ED_V4_Decouple_带火电下限_IIS_Report.ilp\")\n", "                f.write(f\"{period_name}模型不可行，IIS报告如下：\\n\")\n", "                for c in model.getConstrs():\n", "                    if c.IISConstr:\n", "                        f.write(f\"约束 {c<PERSON>}: {c.<PERSON>} {c.<PERSON>}\\n\")\n", "                for v in model.getVars():\n", "                    if v.IISLB:\n", "                        f.write(f\"变量 {v.VarName} 下界: {v.LB}\\n\")\n", "                    if v.IISUB:\n", "                        f.write(f\"变量 {v.VarName} 上界: {v.UB}\\n\")\n", "            break\n", "    \n", "    # 合并所有时段的结果\n", "    if len(all_period_results) == len(time_periods):\n", "        # 保存完整的优化结果用于热启动\n", "        with open(f'V4_{decomposition_type.capitalize()}_full_optimization_results.pkl', 'wb') as f:\n", "            pickle.dump(all_period_results, f)\n", "        \n", "        # 创建Excel写入器保存年度结果\n", "        with pd.ExcelWriter(f'V4_{decomposition_type.capitalize()}_annual_optimization_results.xlsx', engine='openpyxl') as writer:\n", "            # 定义年度变量映射\n", "            variables = {\n", "                '煤炭发电量': 'P_coal',\n", "                '燃气发电量': 'P_gas',\n", "                '石油发电量': 'P_oil',\n", "                '风电发电量': 'P_wind',\n", "                '太阳能发电量': 'P_solar',\n", "                '水电发电量': 'P_hydro',\n", "                '核电发电量': 'P_nuclear',\n", "                '地热发电量': 'P_geothermal',\n", "                '生物质发电量': 'P_biomass',\n", "                '储能充电量': 'P_cha',\n", "                '储能放电量': 'P_dis',\n", "                '储能能量': 'E_storage',\n", "                '负荷削减量': 'P_shedding',\n", "                '可再生能源弃电量': 'P_curtail',\n", "                '净电力交易量': 'P_trade_hour'\n", "            }\n", "            \n", "            # 对每个变量创建年度数据并保存到对应sheet\n", "            for sheet_name, var_name in variables.items():\n", "                # 拼接所有时段的数据\n", "                annual_data = np.vstack([pr[var_name] for pr in all_period_results])\n", "                # 创建DataFrame\n", "                df = pd.DataFrame(annual_data, columns=iso_codes)\n", "                # 保存到Excel\n", "                df.to_excel(writer, sheet_name=sheet_name, index=True)\n", "        \n", "        # 计算并打印年度总成本\n", "        total_cost = sum(pr['objective_value'] for pr in all_period_results)\n", "        print(\"\\n全年优化完成\")\n", "        print(f\"总成本: {total_cost:,.2f}\")\n", "        print(f\"年度结果已保存到V4_{decomposition_type.capitalize()}_annual_optimization_results.xlsx\")\n", "        print(f\"完整优化结果已保存到V4_{decomposition_type.capitalize()}_full_optimization_results.pkl\")\n", "\n", "        # 计算六国二区的各类发电量并保存到表格中\n", "        typical_countries = ['BRA', 'CHN', 'IND', 'JPN', 'RUS', 'USA']\n", "        eu_countries = ['AUT', 'BEL', 'BGR', 'HRV', 'CYP', 'CZE', 'DNK', 'EST', 'FIN', 'FRA', 'DEU', 'GRC', 'HUN', 'IRL', 'ITA', 'LVA', 'LTU', 'LUX', 'MLT', 'NLD', 'POL', 'PRT', 'ROU', 'SVK', 'SVN', 'ESP', 'SWE', 'GBR']\n", "        row_countries = [iso for iso in iso_codes if iso not in typical_countries and iso not in eu_countries]\n", "\n", "        # 定义所有需要统计的变量\n", "        all_vars = ['P_coal', 'P_gas', 'P_oil', 'P_wind', 'P_solar', 'P_hydro', \n", "                    'P_nuclear', 'P_geothermal', 'P_biomass', 'P_cha', 'P_dis', \n", "                    'P_shedding', 'P_curtail', 'P_trade_hour']\n", "        \n", "        # 创建DataFrame存储结果\n", "        results_df = pd.DataFrame(columns=['区域', '煤炭发电量', '燃气发电量', '石油发电量', '风电发电量', \n", "                                         '太阳能发电量', '水电发电量', '核电发电量', '地热发电量', \n", "                                         '生物质发电量', '储能充电量', '储能放电量', '负荷削减量', \n", "                                         '可再生能源弃电量', '净电力交易量'])\n", "        \n", "        print(f\"\\n各区域发电量及运行指标(TWh):\")\n", "        \n", "        # 先输出六个典型国家的单独结果\n", "        print(\"\\n典型国家单独结果:\")\n", "        for country in typical_countries:\n", "            country_data = np.zeros(len(all_vars))\n", "            for i, var in enumerate(all_vars):\n", "                annual_data = np.vstack([pr[var] for pr in all_period_results])\n", "                country_data[i] = annual_data[:, iso_codes.index(country)].sum() / 1e6  # 转换为TWh\n", "                \n", "            print(f\"\\n{country}:\")\n", "            print(f\"煤炭发电量: {country_data[0]:.2f}\")\n", "            print(f\"燃气发电量: {country_data[1]:.2f}\")\n", "            print(f\"石油发电量: {country_data[2]:.2f}\")\n", "            print(f\"风电发电量: {country_data[3]:.2f}\")\n", "            print(f\"太阳能发电量: {country_data[4]:.2f}\")\n", "            print(f\"水电发电量: {country_data[5]:.2f}\")\n", "            print(f\"核电发电量: {country_data[6]:.2f}\")\n", "            print(f\"地热发电量: {country_data[7]:.2f}\")\n", "            print(f\"生物质发电量: {country_data[8]:.2f}\")\n", "            print(f\"储能充电量: {country_data[9]:.2f}\")\n", "            print(f\"储能放电量: {country_data[10]:.2f}\")\n", "            print(f\"负荷削减量: {country_data[11]:.2f}\")\n", "            print(f\"可再生能源弃电量: {country_data[12]:.2f}\")\n", "            print(f\"净电力交易量: {country_data[13]:.2f}\")\n", "            \n", "            # 将结果添加到DataFrame\n", "            new_row = pd.DataFrame({\n", "                '区域': [country],\n", "                '煤炭发电量': [country_data[0]],\n", "                '燃气发电量': [country_data[1]],\n", "                '石油发电量': [country_data[2]],\n", "                '风电发电量': [country_data[3]],\n", "                '太阳能发电量': [country_data[4]],\n", "                '水电发电量': [country_data[5]],\n", "                '核电发电量': [country_data[6]],\n", "                '地热发电量': [country_data[7]],\n", "                '生物质发电量': [country_data[8]],\n", "                '储能充电量': [country_data[9]],\n", "                '储能放电量': [country_data[10]],\n", "                '负荷削减量': [country_data[11]],\n", "                '可再生能源弃电量': [country_data[12]],\n", "                '净电力交易量': [country_data[13]]\n", "            })\n", "            results_df = pd.concat([results_df, new_row], ignore_index=True)\n", "        \n", "        # 对欧盟和其他国家区域计算各类发电量\n", "        for region, countries in [('欧盟', eu_countries), ('其他国家', row_countries)]:\n", "            region_data = np.zeros(len(all_vars))\n", "            for i, var in enumerate(all_vars):\n", "                annual_data = np.vstack([pr[var] for pr in all_period_results])\n", "                region_data[i] = annual_data[:, [iso_codes.index(c) for c in countries if c in iso_codes]].sum() / 1e6  # 转换为TWh\n", "                \n", "            print(f\"\\n{region}:\")\n", "            print(f\"煤炭发电量: {region_data[0]:.2f}\")\n", "            print(f\"燃气发电量: {region_data[1]:.2f}\")\n", "            print(f\"石油发电量: {region_data[2]:.2f}\")\n", "            print(f\"风电发电量: {region_data[3]:.2f}\")\n", "            print(f\"太阳能发电量: {region_data[4]:.2f}\")\n", "            print(f\"水电发电量: {region_data[5]:.2f}\")\n", "            print(f\"核电发电量: {region_data[6]:.2f}\")\n", "            print(f\"地热发电量: {region_data[7]:.2f}\")\n", "            print(f\"生物质发电量: {region_data[8]:.2f}\")\n", "            print(f\"储能充电量: {region_data[9]:.2f}\")\n", "            print(f\"储能放电量: {region_data[10]:.2f}\")\n", "            print(f\"负荷削减量: {region_data[11]:.2f}\")\n", "            print(f\"可再生能源弃电量: {region_data[12]:.2f}\")\n", "            print(f\"净电力交易量: {region_data[13]:.2f}\")\n", "            \n", "            # 将结果添加到DataFrame\n", "            new_row = pd.DataFrame({\n", "                '区域': [region],\n", "                '煤炭发电量': [region_data[0]],\n", "                '燃气发电量': [region_data[1]],\n", "                '石油发电量': [region_data[2]],\n", "                '风电发电量': [region_data[3]],\n", "                '太阳能发电量': [region_data[4]],\n", "                '水电发电量': [region_data[5]],\n", "                '核电发电量': [region_data[6]],\n", "                '地热发电量': [region_data[7]],\n", "                '生物质发电量': [region_data[8]],\n", "                '储能充电量': [region_data[9]],\n", "                '储能放电量': [region_data[10]],\n", "                '负荷削减量': [region_data[11]],\n", "                '可再生能源弃电量': [region_data[12]],\n", "                '净电力交易量': [region_data[13]]\n", "            })\n", "            results_df = pd.concat([results_df, new_row], ignore_index=True)\n", "        \n", "        # 保存结果到Excel文件\n", "        results_df.to_excel(f'V4_{decomposition_type.capitalize()}_Optimization_results_Summary.xlsx', index=False)\n", "        print(f\"\\n结果摘要已保存到V4_{decomposition_type.capitalize()}_Optimization_results_Summary.xlsx\")\n", "    \n", "    return all_period_results"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "开始按monthly分解冷启动求解...\n", "\n", "BRA区域资源分配：\n", "原始净负荷值(MWh): [np.float64(13.34490289355761), np.float64(14.42653833472652), np.float64(18.157866448051863), np.float64(18.109774887923383), np.float64(18.32593968090296), np.float64(13.81170558977262), np.float64(1.5143803442047832), np.float64(4.720028461224754), np.float64(3.655507748310163), np.float64(6.572364839579313), np.float64(11.062634119382126), np.float64(12.348361562567625)]\n", "月度_1 - 净负荷: 13.34 TWh, 归一化比例: 10.04%, 初始分配: 5.85 TWh, 最大可发电量: 13.09 TWh, 最终分配: 5.85 TWh\n", "月度_2 - 净负荷: 14.43 TWh, 归一化比例: 10.95%, 初始分配: 6.39 TWh, 最大可发电量: 11.83 TWh, 最终分配: 6.39 TWh\n", "月度_3 - 净负荷: 18.16 TWh, 归一化比例: 14.12%, 初始分配: 8.23 TWh, 最大可发电量: 13.09 TWh, 最终分配: 8.23 TWh\n", "月度_4 - 净负荷: 18.11 TWh, 归一化比例: 14.08%, 初始分配: 8.21 TWh, 最大可发电量: 12.67 TWh, 最终分配: 8.21 TWh\n", "月度_5 - 净负荷: 18.33 TWh, 归一化比例: 14.26%, 初始分配: 8.32 TWh, 最大可发电量: 13.09 TWh, 最终分配: 8.32 TWh\n", "月度_6 - 净负荷: 13.81 TWh, 归一化比例: 10.43%, 初始分配: 6.08 TWh, 最大可发电量: 12.67 TWh, 最终分配: 6.08 TWh\n", "月度_7 - 净负荷: 1.51 TWh, 归一化比例: 0.00%, 初始分配: 0.00 TWh, 最大可发电量: 13.09 TWh, 最终分配: 0.00 TWh\n", "月度_8 - 净负荷: 4.72 TWh, 归一化比例: 2.72%, 初始分配: 1.59 TWh, 最大可发电量: 13.09 TWh, 最终分配: 1.59 TWh\n", "月度_9 - 净负荷: 3.66 TWh, 归一化比例: 1.82%, 初始分配: 1.06 TWh, 最大可发电量: 12.67 TWh, 最终分配: 1.06 TWh\n", "月度_10 - 净负荷: 6.57 TWh, 归一化比例: 4.29%, 初始分配: 2.50 TWh, 最大可发电量: 13.09 TWh, 最终分配: 2.50 TWh\n", "月度_11 - 净负荷: 11.06 TWh, 归一化比例: 8.10%, 初始分配: 4.72 TWh, 最大可发电量: 12.67 TWh, 最终分配: 4.72 TWh\n", "月度_12 - 净负荷: 12.35 TWh, 归一化比例: 9.19%, 初始分配: 5.36 TWh, 最大可发电量: 13.09 TWh, 最终分配: 5.36 TWh\n", "\n", "CHN区域资源分配：\n", "原始净负荷值(MWh): [np.float64(551.0067635167148), np.float64(412.7676266748233), np.float64(477.02261060524535), np.float64(388.55867846043947), np.float64(424.09008574495897), np.float64(488.9732263864372), np.float64(543.018860280296), np.float64(605.2320380355883), np.float64(500.70304547841215), np.float64(536.5375016965165), np.float64(536.1083242212079), np.float64(609.611891338588)]\n", "月度_1 - 净负荷: 551.01 TWh, 归一化比例: 11.51%, 初始分配: 19.91 TWh, 最大可发电量: 23.25 TWh, 最终分配: 20.13 TWh\n", "月度_2 - 净负荷: 412.77 TWh, 归一化比例: 1.72%, 初始分配: 2.97 TWh, 最大可发电量: 21.00 TWh, 最终分配: 4.16 TWh\n", "月度_3 - 净负荷: 477.02 TWh, 归一化比例: 6.27%, 初始分配: 10.84 TWh, 最大可发电量: 23.25 TWh, 最终分配: 11.66 TWh\n", "月度_4 - 净负荷: 388.56 TWh, 归一化比例: 0.00%, 初始分配: 0.00 TWh, 最大可发电量: 22.50 TWh, 最终分配: 1.49 TWh\n", "月度_5 - 净负荷: 424.09 TWh, 归一化比例: 2.52%, 初始分配: 4.35 TWh, 最大可发电量: 23.25 TWh, 最终分配: 5.60 TWh\n", "月度_6 - 净负荷: 488.97 TWh, 归一化比例: 7.12%, 初始分配: 12.31 TWh, 最大可发电量: 22.50 TWh, 最终分配: 12.98 TWh\n", "月度_7 - 净负荷: 543.02 TWh, 归一化比例: 10.95%, 初始分配: 18.93 TWh, 最大可发电量: 23.25 TWh, 最终分配: 19.22 TWh\n", "月度_8 - 净负荷: 605.23 TWh, 归一化比例: 15.36%, 初始分配: 26.55 TWh, 最大可发电量: 23.25 TWh, 最终分配: 23.25 TWh\n", "月度_9 - 净负荷: 500.70 TWh, 归一化比例: 7.95%, 初始分配: 13.74 TWh, 最大可发电量: 22.50 TWh, 最终分配: 14.32 TWh\n", "月度_10 - 净负荷: 536.54 TWh, 归一化比例: 10.49%, 初始分配: 18.14 TWh, 最大可发电量: 23.25 TWh, 最终分配: 18.47 TWh\n", "月度_11 - 净负荷: 536.11 TWh, 归一化比例: 10.46%, 初始分配: 18.08 TWh, 最大可发电量: 22.50 TWh, 最终分配: 18.38 TWh\n", "月度_12 - 净负荷: 609.61 TWh, 归一化比例: 15.67%, 初始分配: 27.09 TWh, 最大可发电量: 23.25 TWh, 最终分配: 23.25 TWh\n", "\n", "IND区域资源分配：\n", "原始净负荷值(MWh): [np.float64(88.75469556843645), np.float64(85.9006186288245), np.float64(110.79240769715433), np.float64(119.96899244784188), np.float64(137.64889662944586), np.float64(137.59533128088628), np.float64(142.06251220428305), np.float64(135.41980387675272), np.float64(123.86564810127634), np.float64(120.23673103823761), np.float64(101.24213985235629), np.float64(91.33484869673724)]\n", "月度_1 - 净负荷: 88.75 TWh, 归一化比例: 0.78%, 初始分配: 0.26 TWh, 最大可发电量: 8.00 TWh, 最终分配: 0.26 TWh\n", "月度_2 - 净负荷: 85.90 TWh, 归一化比例: 0.00%, 初始分配: 0.00 TWh, 最大可发电量: 7.23 TWh, 最终分配: 0.00 TWh\n", "月度_3 - 净负荷: 110.79 TWh, 归一化比例: 6.84%, 初始分配: 2.31 TWh, 最大可发电量: 8.00 TWh, 最终分配: 2.31 TWh\n", "月度_4 - 净负荷: 119.97 TWh, 归一化比例: 9.36%, 初始分配: 3.16 TWh, 最大可发电量: 7.74 TWh, 最终分配: 3.16 TWh\n", "月度_5 - 净负荷: 137.65 TWh, 归一化比例: 14.22%, 初始分配: 4.79 TWh, 最大可发电量: 8.00 TWh, 最终分配: 4.79 TWh\n", "月度_6 - 净负荷: 137.60 TWh, 归一化比例: 14.20%, 初始分配: 4.79 TWh, 最大可发电量: 7.74 TWh, 最终分配: 4.79 TWh\n", "月度_7 - 净负荷: 142.06 TWh, 归一化比例: 15.43%, 初始分配: 5.20 TWh, 最大可发电量: 8.00 TWh, 最终分配: 5.20 TWh\n", "月度_8 - 净负荷: 135.42 TWh, 归一化比例: 13.60%, 初始分配: 4.59 TWh, 最大可发电量: 8.00 TWh, 最终分配: 4.59 TWh\n", "月度_9 - 净负荷: 123.87 TWh, 归一化比例: 10.43%, 初始分配: 3.52 TWh, 最大可发电量: 7.74 TWh, 最终分配: 3.52 TWh\n", "月度_10 - 净负荷: 120.24 TWh, 归一化比例: 9.43%, 初始分配: 3.18 TWh, 最大可发电量: 8.00 TWh, 最终分配: 3.18 TWh\n", "月度_11 - 净负荷: 101.24 TWh, 归一化比例: 4.21%, 初始分配: 1.42 TWh, 最大可发电量: 7.74 TWh, 最终分配: 1.42 TWh\n", "月度_12 - 净负荷: 91.33 TWh, 归一化比例: 1.49%, 初始分配: 0.50 TWh, 最大可发电量: 8.00 TWh, 最终分配: 0.50 TWh\n", "\n", "JPN区域资源分配：\n", "原始净负荷值(MWh): [np.float64(61.745793743666034), np.float64(52.79897746384095), np.float64(54.190737014635836), np.float64(46.71963995169544), np.float64(45.964161780078335), np.float64(48.553125434936106), np.float64(56.20041828359361), np.float64(60.26200175791264), np.float64(54.00730434589928), np.float64(56.41860186478372), np.float64(57.1452134995248), np.float64(61.24106265586853)]\n", "月度_1 - 净负荷: 61.75 TWh, 归一化比例: 15.22%, 初始分配: 5.94 TWh, 最大可发电量: 4.32 TWh, 最终分配: 4.32 TWh\n", "月度_2 - 净负荷: 52.80 TWh, 归一化比例: 6.59%, 初始分配: 2.57 TWh, 最大可发电量: 3.90 TWh, 最终分配: 2.91 TWh\n", "月度_3 - 净负荷: 54.19 TWh, 归一化比例: 7.93%, 初始分配: 3.09 TWh, 最大可发电量: 4.32 TWh, 最终分配: 3.41 TWh\n", "月度_4 - 净负荷: 46.72 TWh, 归一化比例: 0.73%, 初始分配: 0.28 TWh, 最大可发电量: 4.18 TWh, 最终分配: 1.29 TWh\n", "月度_5 - 净负荷: 45.96 TWh, 归一化比例: 0.00%, 初始分配: 0.00 TWh, 最大可发电量: 4.32 TWh, 最终分配: 1.12 TWh\n", "月度_6 - 净负荷: 48.55 TWh, 归一化比例: 2.50%, 初始分配: 0.97 TWh, 最大可发电量: 4.18 TWh, 最终分配: 1.80 TWh\n", "月度_7 - 净负荷: 56.20 TWh, 归一化比例: 9.87%, 初始分配: 3.85 TWh, 最大可发电量: 4.32 TWh, 最终分配: 3.97 TWh\n", "月度_8 - 净负荷: 60.26 TWh, 归一化比例: 13.79%, 初始分配: 5.38 TWh, 最大可发电量: 4.32 TWh, 最终分配: 4.32 TWh\n", "月度_9 - 净负荷: 54.01 TWh, 归一化比例: 7.76%, 初始分配: 3.03 TWh, 最大可发电量: 4.18 TWh, 最终分配: 3.32 TWh\n", "月度_10 - 净负荷: 56.42 TWh, 归一化比例: 10.08%, 初始分配: 3.93 TWh, 最大可发电量: 4.32 TWh, 最终分配: 4.03 TWh\n", "月度_11 - 净负荷: 57.15 TWh, 归一化比例: 10.78%, 初始分配: 4.21 TWh, 最大可发电量: 4.18 TWh, 最终分配: 4.18 TWh\n", "月度_12 - 净负荷: 61.24 TWh, 归一化比例: 14.74%, 初始分配: 5.75 TWh, 最大可发电量: 4.32 TWh, 最终分配: 4.32 TWh\n", "\n", "RUS区域资源分配：\n", "原始净负荷值(MWh): [np.float64(67.16063320566631), np.float64(58.526984377446624), np.float64(60.32722758797129), np.float64(54.70066023595775), np.float64(51.53413758369566), np.float64(46.510918214081045), np.float64(50.45996917810351), np.float64(52.177297807081445), np.float64(54.97132566900648), np.float64(58.167912169039894), np.float64(60.982429428269924), np.float64(66.7196541743601)]\n", "月度_1 - 净负荷: 67.16 TWh, 归一化比例: 16.64%, 初始分配: 0.63 TWh, 最大可发电量: 1.02 TWh, 最终分配: 0.63 TWh\n", "月度_2 - 净负荷: 58.53 TWh, 归一化比例: 9.68%, 初始分配: 0.37 TWh, 最大可发电量: 0.92 TWh, 最终分配: 0.37 TWh\n", "月度_3 - 净负荷: 60.33 TWh, 归一化比例: 11.13%, 初始分配: 0.42 TWh, 最大可发电量: 1.02 TWh, 最终分配: 0.42 TWh\n", "月度_4 - 净负荷: 54.70 TWh, 归一化比例: 6.60%, 初始分配: 0.25 TWh, 最大可发电量: 0.99 TWh, 最终分配: 0.25 TWh\n", "月度_5 - 净负荷: 51.53 TWh, 归一化比例: 4.05%, 初始分配: 0.15 TWh, 最大可发电量: 1.02 TWh, 最终分配: 0.15 TWh\n", "月度_6 - 净负荷: 46.51 TWh, 归一化比例: 0.00%, 初始分配: 0.00 TWh, 最大可发电量: 0.99 TWh, 最终分配: 0.00 TWh\n", "月度_7 - 净负荷: 50.46 TWh, 归一化比例: 3.18%, 初始分配: 0.12 TWh, 最大可发电量: 1.02 TWh, 最终分配: 0.12 TWh\n", "月度_8 - 净负荷: 52.18 TWh, 归一化比例: 4.57%, 初始分配: 0.17 TWh, 最大可发电量: 1.02 TWh, 最终分配: 0.17 TWh\n", "月度_9 - 净负荷: 54.97 TWh, 归一化比例: 6.82%, 初始分配: 0.26 TWh, 最大可发电量: 0.99 TWh, 最终分配: 0.26 TWh\n", "月度_10 - 净负荷: 58.17 TWh, 归一化比例: 9.39%, 初始分配: 0.35 TWh, 最大可发电量: 1.02 TWh, 最终分配: 0.35 TWh\n", "月度_11 - 净负荷: 60.98 TWh, 归一化比例: 11.66%, 初始分配: 0.44 TWh, 最大可发电量: 0.99 TWh, 最终分配: 0.44 TWh\n", "月度_12 - 净负荷: 66.72 TWh, 归一化比例: 16.28%, 初始分配: 0.62 TWh, 最大可发电量: 1.02 TWh, 最终分配: 0.62 TWh\n", "\n", "USA区域资源分配：\n", "原始净负荷值(MWh): [np.float64(224.22469515761733), np.float64(188.06453248575593), np.float64(194.76095943233994), np.float64(157.91595120735502), np.float64(192.59177553479867), np.float64(237.56134892798596), np.float64(277.5315272952018), np.float64(274.0119419422321), np.float64(234.96079142912234), np.float64(192.90317106377395), np.float64(197.1315627242543), np.float64(230.4188245339505)]\n", "月度_1 - 净负荷: 224.22 TWh, 归一化比例: 9.38%, 初始分配: 5.36 TWh, 最大可发电量: 21.78 TWh, 最终分配: 5.36 TWh\n", "月度_2 - 净负荷: 188.06 TWh, 归一化比例: 4.26%, 初始分配: 2.44 TWh, 最大可发电量: 19.67 TWh, 最终分配: 2.44 TWh\n", "月度_3 - 净负荷: 194.76 TWh, 归一化比例: 5.21%, 初始分配: 2.98 TWh, 最大可发电量: 21.78 TWh, 最终分配: 2.98 TWh\n", "月度_4 - 净负荷: 157.92 TWh, 归一化比例: 0.00%, 初始分配: 0.00 TWh, 最大可发电量: 21.08 TWh, 最终分配: 0.00 TWh\n", "月度_5 - 净负荷: 192.59 TWh, 归一化比例: 4.90%, 初始分配: 2.80 TWh, 最大可发电量: 21.78 TWh, 最终分配: 2.80 TWh\n", "月度_6 - 净负荷: 237.56 TWh, 归一化比例: 11.26%, 初始分配: 6.44 TWh, 最大可发电量: 21.08 TWh, 最终分配: 6.44 TWh\n", "月度_7 - 净负荷: 277.53 TWh, 归一化比例: 16.92%, 初始分配: 9.67 TWh, 最大可发电量: 21.78 TWh, 最终分配: 9.67 TWh\n", "月度_8 - 净负荷: 274.01 TWh, 归一化比例: 16.42%, 初始分配: 9.38 TWh, 最大可发电量: 21.78 TWh, 最终分配: 9.38 TWh\n", "月度_9 - 净负荷: 234.96 TWh, 归一化比例: 10.90%, 初始分配: 6.23 TWh, 最大可发电量: 21.08 TWh, 最终分配: 6.23 TWh\n", "月度_10 - 净负荷: 192.90 TWh, 归一化比例: 4.95%, 初始分配: 2.83 TWh, 最大可发电量: 21.78 TWh, 最终分配: 2.83 TWh\n", "月度_11 - 净负荷: 197.13 TWh, 归一化比例: 5.55%, 初始分配: 3.17 TWh, 最大可发电量: 21.08 TWh, 最终分配: 3.17 TWh\n", "月度_12 - 净负荷: 230.42 TWh, 归一化比例: 10.25%, 初始分配: 5.86 TWh, 最大可发电量: 21.78 TWh, 最终分配: 5.86 TWh\n", "\n", "EU区域资源分配：\n", "原始净负荷值(MWh): [np.float64(124.76907355698964), np.float64(115.75329365234768), np.float64(98.9944508535203), np.float64(88.99453699442473), np.float64(90.17751275191814), np.float64(92.18829986591761), np.float64(95.41704759616526), np.float64(96.86764797844864), np.float64(99.41082650976975), np.float64(92.71361987315694), np.float64(103.55059769092838), np.float64(100.61910063656487)]\n", "月度_1 - 净负荷: 124.77 TWh, 归一化比例: 27.20%, 初始分配: 53.28 TWh, 最大可发电量: 31.24 TWh, 最终分配: 31.24 TWh\n", "月度_2 - 净负荷: 115.75 TWh, 归一化比例: 20.35%, 初始分配: 39.85 TWh, 最大可发电量: 28.21 TWh, 最终分配: 28.21 TWh\n", "月度_3 - 净负荷: 98.99 TWh, 归一化比例: 7.60%, 初始分配: 14.89 TWh, 最大可发电量: 31.24 TWh, 最终分配: 17.57 TWh\n", "月度_4 - 净负荷: 88.99 TWh, 归一化比例: 0.00%, 初始分配: 0.00 TWh, 最大可发电量: 30.23 TWh, 最终分配: 4.95 TWh\n", "月度_5 - 净负荷: 90.18 TWh, 归一化比例: 0.90%, 初始分配: 1.76 TWh, 最大可发电量: 31.24 TWh, 最终分配: 6.59 TWh\n", "月度_6 - 净负荷: 92.19 TWh, 归一化比例: 2.43%, 初始分配: 4.76 TWh, 最大可发电量: 30.23 TWh, 最终分配: 8.93 TWh\n", "月度_7 - 净负荷: 95.42 TWh, 归一化比例: 4.88%, 初始分配: 9.57 TWh, 最大可发电量: 31.24 TWh, 最终分配: 13.12 TWh\n", "月度_8 - 净负荷: 96.87 TWh, 归一化比例: 5.99%, 初始分配: 11.73 TWh, 最大可发电量: 31.24 TWh, 最终分配: 14.92 TWh\n", "月度_9 - 净负荷: 99.41 TWh, 归一化比例: 7.92%, 初始分配: 15.51 TWh, 最大可发电量: 30.23 TWh, 最终分配: 17.92 TWh\n", "月度_10 - 净负荷: 92.71 TWh, 归一化比例: 2.83%, 初始分配: 5.54 TWh, 最大可发电量: 31.24 TWh, 最终分配: 9.75 TWh\n", "月度_11 - 净负荷: 103.55 TWh, 归一化比例: 11.07%, 初始分配: 21.68 TWh, 最大可发电量: 30.23 TWh, 最终分配: 23.08 TWh\n", "月度_12 - 净负荷: 100.62 TWh, 归一化比例: 8.84%, 初始分配: 17.31 TWh, 最大可发电量: 31.24 TWh, 最终分配: 19.59 TWh\n", "\n", "ROW区域资源分配：\n", "原始净负荷值(MWh): [np.float64(nan), np.float64(nan), np.float64(nan), np.float64(nan), np.float64(nan), np.float64(nan), np.float64(nan), np.float64(nan), np.float64(nan), np.float64(nan), np.float64(nan), np.float64(nan)]\n", "月度_1 - 净负荷: nan T<PERSON><PERSON>, 归一化比例: 8.33%, 初始分配: 10.54 TWh, 最大可发电量: 21.94 TWh, 最终分配: 10.54 TWh\n", "月度_2 - 净负荷: nan <PERSON><PERSON><PERSON>, 归一化比例: 8.33%, 初始分配: 10.54 TWh, 最大可发电量: 19.82 TWh, 最终分配: 10.54 TWh\n", "月度_3 - 净负荷: nan <PERSON><PERSON><PERSON>, 归一化比例: 8.33%, 初始分配: 10.54 TWh, 最大可发电量: 21.94 TWh, 最终分配: 10.54 TWh\n", "月度_4 - 净负荷: nan <PERSON><PERSON><PERSON>, 归一化比例: 8.33%, 初始分配: 10.54 TWh, 最大可发电量: 21.23 TWh, 最终分配: 10.54 TWh\n", "月度_5 - 净负荷: nan <PERSON><PERSON><PERSON>, 归一化比例: 8.33%, 初始分配: 10.54 TWh, 最大可发电量: 21.94 TWh, 最终分配: 10.54 TWh\n", "月度_6 - 净负荷: nan <PERSON><PERSON><PERSON>, 归一化比例: 8.33%, 初始分配: 10.54 TWh, 最大可发电量: 21.23 TWh, 最终分配: 10.54 TWh\n", "月度_7 - 净负荷: nan <PERSON><PERSON><PERSON>, 归一化比例: 8.33%, 初始分配: 10.54 TWh, 最大可发电量: 21.94 TWh, 最终分配: 10.54 TWh\n", "月度_8 - 净负荷: nan <PERSON><PERSON><PERSON>, 归一化比例: 8.33%, 初始分配: 10.54 TWh, 最大可发电量: 21.94 TWh, 最终分配: 10.54 TWh\n", "月度_9 - 净负荷: nan <PERSON><PERSON><PERSON>, 归一化比例: 8.33%, 初始分配: 10.54 TWh, 最大可发电量: 21.23 TWh, 最终分配: 10.54 TWh\n", "月度_10 - 净负荷: nan <PERSON><PERSON><PERSON>, 归一化比例: 8.33%, 初始分配: 10.54 TWh, 最大可发电量: 21.94 TWh, 最终分配: 10.54 TWh\n", "月度_11 - 净负荷: nan <PERSON><PERSON><PERSON>, 归一化比例: 8.33%, 初始分配: 10.54 TWh, 最大可发电量: 21.23 TWh, 最终分配: 10.54 TWh\n", "月度_12 - 净负荷: nan <PERSON><PERSON><PERSON>, 归一化比例: 8.33%, 初始分配: 10.54 TWh, 最大可发电量: 21.94 TWh, 最终分配: 10.54 TWh\n", "\n", "求解月度_1...\n", "Set parameter LicenseID to value 2600968\n", "Set parameter TimeLimit to value 10800\n", "Set parameter MIPGap to value 0.001\n", "Gurobi Optimizer version 12.0.0 build v12.0.0rc1 (mac64[arm] - Darwin 24.1.0 24B83)\n", "\n", "CPU model: Apple M3\n", "Thread count: 8 physical cores, 8 logical processors, using up to 8 threads\n", "\n", "Non-default parameters:\n", "TimeLimit  10800\n", "MIPGap  0.001\n", "\n", "Optimize a model with 4214082 rows, 2999064 columns and 10798305 nonzeros\n", "Model fingerprint: 0x0a267de8\n", "Model has 278256 simple general constraints\n", "  278256 ABS\n", "Variable types: 2961120 continuous, 37944 integer (37944 binary)\n", "Coefficient statistics:\n", "  Matrix range     [2e-03, 5e+04]\n", "  Objective range  [1e-01, 1e+03]\n", "  Bounds range     [1e+00, 1e+00]\n", "  RHS range        [5e-03, 3e+07]\n", "Presolve removed 3253779 rows and 1758229 columns (presolve time = 5s)...\n", "Presolve removed 3253810 rows and 1758304 columns (presolve time = 10s)...\n", "Presolve removed 2948101 rows and 1766441 columns\n", "Presolve time: 13.52s\n", "Presolved: 1265981 rows, 1232744 columns, 4931260 nonzeros\n", "Variable types: 1195544 continuous, 37200 integer (37200 binary)\n", "Deterministic concurrent LP optimizer: primal simplex, dual simplex, and barrier\n", "Showing barrier log only...\n", "\n", "Root barrier log...\n", "\n", "Elapsed ordering time = 5s\n", "Ordering time: 14.58s\n", "\n", "Barrier statistics:\n", " AA' NZ     : 4.968e+06\n", " Factor NZ  : 3.617e+07 (roughly 1.2 GB of memory)\n", " Factor Ops : 1.517e+10 (less than 1 second per iteration)\n", " Threads    : 5\n", "\n", "                  Objective                Residual\n", "Iter       Primal          Dual         Primal    Dual     Compl     Time\n", "   0  -7.94949107e+14 -5.49938480e+13  2.62e+10 1.36e+04  1.11e+10    40s\n", "   1  -3.02075294e+14 -4.89416368e+13  1.10e+10 5.09e-11  4.62e+09    41s\n", "   2  -1.72648897e+13 -4.24859910e+13  6.99e+08 5.53e-11  3.05e+08    43s\n", "   3   1.33462594e+11 -3.06094593e+13  3.75e+07 4.80e-11  2.47e+07    45s\n", "   4   5.64394710e+11 -1.05861056e+13  4.73e+06 4.80e-11  4.91e+06    48s\n", "   5   3.25790916e+11 -3.09739678e+12  4.76e+05 7.28e-11  1.13e+06    50s\n", "   6   1.59531202e+11 -1.30992479e+12  1.75e+05 4.80e-11  4.64e+05    52s\n", "   7   9.36045433e+10 -1.00418246e+12  7.21e+04 3.64e-11  3.36e+05    53s\n", "   8   6.85818453e+10 -3.13552256e+11  3.48e+04 5.09e-11  1.16e+05    54s\n", "   9   5.56297029e+10 -1.59457345e+11  2.02e+04 4.37e-11  6.50e+04    56s\n", "  10   4.16489694e+10 -6.72934510e+10  6.92e+03 3.64e-11  3.27e+04    57s\n", "  11   3.99770333e+10 -3.96067159e+10  5.55e+03 2.91e-11  2.39e+04    59s\n", "  12   3.41419416e+10 -7.33966551e+09  8.82e+02 2.91e-11  1.24e+04    60s\n", "  13   3.37878887e+10  1.28711478e+10  7.20e+02 4.37e-11  6.24e+03    62s\n", "  14   3.25302762e+10  2.12957563e+10  2.13e+02 4.37e-11  3.35e+03    63s\n", "  15   3.21764746e+10  2.58352026e+10  1.06e+02 5.82e-11  1.89e+03    65s\n", "  16   3.19925024e+10  2.78015911e+10  5.79e+01 5.82e-11  1.25e+03    67s\n", "  17   3.19141655e+10  2.91168454e+10  3.90e+01 5.82e-11  8.33e+02    68s\n", "  18   3.18581424e+10  3.01096889e+10  2.63e+01 4.37e-11  5.21e+02    70s\n", "  19   3.17991252e+10  3.07149313e+10  1.40e+01 4.37e-11  3.23e+02    71s\n", "  20   3.17658576e+10  3.10149629e+10  7.69e+00 2.91e-11  2.24e+02    73s\n", "  21   3.17561252e+10  3.12588003e+10  5.68e+00 2.91e-11  1.48e+02    74s\n", "  22   3.17384322e+10  3.14251332e+10  2.56e+00 2.18e-11  9.32e+01    76s\n", "  23   3.17310101e+10  3.15716188e+10  1.35e+00 1.82e-11  4.74e+01    78s\n", "  24   3.17280651e+10  3.16349864e+10  9.15e-01 1.46e-11  2.77e+01    79s\n", "  25   3.17261997e+10  3.16584358e+10  7.00e-01 1.46e-11  2.02e+01    81s\n", "  26   3.17234367e+10  3.16850267e+10  4.14e-01 1.09e-11  1.14e+01    82s\n", "  27   3.17221956e+10  3.17022946e+10  3.05e-01 1.53e-11  5.92e+00    84s\n", "  28   3.17210911e+10  3.17069126e+10  2.20e-01 2.36e-11  4.22e+00    86s\n", "  29   3.17199696e+10  3.17105923e+10  1.37e-01 2.74e-11  2.79e+00    87s\n", "  30   3.17191669e+10  3.17121591e+10  7.92e-02 2.67e-11  2.09e+00    88s\n", "  31   3.17187001e+10  3.17153220e+10  4.54e-02 1.99e-11  1.01e+00    90s\n", "  32   3.17185142e+10  3.17165151e+10  3.22e-02 2.17e-11  5.95e-01    92s\n", "  33   3.17183949e+10  3.17165749e+10  2.43e-02 2.31e-11  5.42e-01    93s\n", "  34   3.17182814e+10  3.17169716e+10  1.63e-02 2.83e-11  3.90e-01    95s\n", "  35   3.17181866e+10  3.17173965e+10  9.81e-03 2.97e-11  2.35e-01    97s\n", "  36   3.17180946e+10  3.17177243e+10  3.57e-03 2.61e-11  1.10e-01    99s\n", "  37   3.17180712e+10  3.17178665e+10  2.07e-03 1.83e-11  6.09e-02   102s\n", "  38   3.17180527e+10  3.17179967e+10  7.82e-04 1.09e-11  1.67e-02   104s\n", "  39   3.17180437e+10  3.17180282e+10  1.81e-04 1.09e-11  4.62e-03   105s\n", "  40   3.17180410e+10  3.17180369e+10  4.29e-05 7.28e-12  1.24e-03   107s\n", "  41   3.17180403e+10  3.17180397e+10  8.56e-06 1.46e-11  1.86e-04   109s\n", "  42   3.17180401e+10  3.17180401e+10  9.73e-07 1.09e-11  1.31e-05   111s\n", "  43   3.17180401e+10  3.17180401e+10  4.29e-07 1.09e-11  1.14e-07   114s\n", "  44   3.17180401e+10  3.17180401e+10  1.21e-07 7.28e-12  1.38e-10   116s\n", "\n", "<PERSON>ier solved model in 44 iterations and 116.20 seconds (62.89 work units)\n", "Optimal objective 3.17180401e+10\n", "\n", "\n", "Root crossover log...\n", "\n", "  109826 DPushes remaining with DInf 0.0000000e+00               119s\n", "     138 DPushes remaining with DInf 0.0000000e+00               120s\n", "       0 DPushes remaining with DInf 0.0000000e+00               121s\n", "Warning: Markowitz tolerance tightened to 0.5\n", "\n", "  371888 PPushes remaining with PInf 0.0000000e+00               121s\n", "  321957 PPushes remaining with PInf 0.0000000e+00               125s\n", "  280930 PPushes remaining with PInf 0.0000000e+00               130s\n", "  249220 PPushes remaining with PInf 0.0000000e+00               135s\n", "  206430 PPushes remaining with PInf 0.0000000e+00               140s\n", "  189702 PPushes remaining with PInf 0.0000000e+00               145s\n", "  175958 PPushes remaining with PInf 0.0000000e+00               150s\n", "  161079 PPushes remaining with PInf 0.0000000e+00               155s\n", "  146613 PPushes remaining with PInf 0.0000000e+00               160s\n", "  133412 PPushes remaining with PInf 0.0000000e+00               165s\n", "  117720 PPushes remaining with PInf 0.0000000e+00               170s\n", "   95092 PPushes remaining with PInf 0.0000000e+00               175s\n", "   81634 PPushes remaining with PInf 0.0000000e+00               180s\n", "   68040 PPushes remaining with PInf 0.0000000e+00               185s\n", "   49655 PPushes remaining with PInf 0.0000000e+00               190s\n", "   30122 PPushes remaining with PInf 0.0000000e+00               195s\n", "   13744 PPushes remaining with PInf 0.0000000e+00               200s\n", "     354 PPushes remaining with PInf 0.0000000e+00               205s\n", "       0 PPushes remaining with PInf 0.0000000e+00               205s\n", "\n", "  Push phase complete: Pinf 0.0000000e+00, Dinf 3.2808742e-08    206s\n", "\n", "\n", "Root simplex log...\n", "\n", "Iteration    Objective       Primal Inf.    Dual Inf.      Time\n", "  421003    3.1718040e+10   0.000000e+00   0.000000e+00    206s\n", "Concurrent spin time: 27.97s (can be avoided by choosing Method=3)\n", "\n", "Solved with barrier\n", "  421003    3.1718040e+10   0.000000e+00   7.170443e+04    235s\n", "  421061    3.1718040e+10   0.000000e+00   0.000000e+00    235s\n", "Extra simplex iterations after uncrush: 58\n", "\n", "Root relaxation: objective 3.171804e+10, 421061 iterations, 220.21 seconds (88.30 work units)\n", "Total elapsed time = 402.24s (DegenMoves)\n", "Total elapsed time = 413.49s (<PERSON>genMoves)\n", "Total elapsed time = 421.91s (<PERSON>genMoves)\n", "Total elapsed time = 436.18s (DegenMoves)\n", "\n", "    Nodes    |    Current Node    |     Objective Bounds      |     Work\n", " Expl Unexpl |  Obj  Depth IntInf | Incumbent    BestBd   Gap | It/Node Time\n", "\n", "     0     0 3.1718e+10    0  397          - 3.1718e+10      -     -  445s\n", "H    0     0                    3.172482e+10 3.1718e+10  0.02%     -  564s\n", "\n", "Explored 1 nodes (514511 simplex iterations) in 565.42 seconds (327.22 work units)\n", "Thread count was 8 (of 8 available processors)\n", "\n", "Solution count 1: 3.17248e+10 \n", "\n", "Optimal solution found (tolerance 1.00e-03)\n", "Best objective 3.172482054032e+10, best bound 3.171804012026e+10, gap 0.0214%\n", "月度_1找到最优解\n", "目标值 (总成本): 31,724,820,540.32\n", "月度_1结果已保存\n", "\n", "求解月度_2...\n", "Set parameter TimeLimit to value 10800\n", "Set parameter MIPGap to value 0.001\n", "Gurobi Optimizer version 12.0.0 build v12.0.0rc1 (mac64[arm] - Darwin 24.1.0 24B83)\n", "\n", "CPU model: Apple M3\n", "Thread count: 8 physical cores, 8 logical processors, using up to 8 threads\n", "\n", "Non-default parameters:\n", "TimeLimit  10800\n", "MIPGap  0.001\n", "\n", "Optimize a model with 3806202 rows, 2708832 columns and 9753081 nonzeros\n", "Model fingerprint: 0xd5960252\n", "Model has 251328 simple general constraints\n", "  251328 ABS\n", "Variable types: 2674560 continuous, 34272 integer (34272 binary)\n", "Coefficient statistics:\n", "  Matrix range     [2e-03, 5e+04]\n", "  Objective range  [1e-01, 1e+03]\n", "  Bounds range     [1e+00, 1e+00]\n", "  RHS range        [5e-03, 3e+07]\n", "Presolve removed 2938810 rows and 1588055 columns (presolve time = 5s)...\n", "Presolve removed 2938839 rows and 1588129 columns (presolve time = 10s)...\n", "Presolve removed 2662770 rows and 1595522 columns\n", "Presolve time: 13.15s\n", "Presolved: 1143432 rows, 1113430 columns, 4453341 nonzeros\n", "Variable types: 1079830 continuous, 33600 integer (33600 binary)\n", "Deterministic concurrent LP optimizer: primal simplex, dual simplex, and barrier\n", "Showing barrier log only...\n", "\n", "Root barrier log...\n", "\n", "Elapsed ordering time = 5s\n", "Ordering time: 12.59s\n", "\n", "Barrier statistics:\n", " AA' NZ     : 4.487e+06\n", " Factor NZ  : 3.254e+07 (roughly 1.1 GB of memory)\n", " Factor Ops : 1.340e+10 (less than 1 second per iteration)\n", " Threads    : 5\n", "\n", "                  Objective                Residual\n", "Iter       Primal          Dual         Primal    Dual     Compl     Time\n", "   0  -6.80161982e+14 -4.71210113e+13  2.25e+10 1.23e+04  1.00e+10    36s\n", "   1  -2.32987639e+14 -4.20252178e+13  8.66e+09 4.37e-11  3.90e+09    37s\n", "   2  -1.09010838e+13 -3.55815029e+13  4.65e+08 6.26e-11  2.21e+08    38s\n", "   3   1.00029667e+11 -2.69893842e+13  3.47e+07 4.80e-11  2.44e+07    40s\n", "   4   4.83420933e+11 -9.03581651e+12  7.60e+06 5.53e-11  5.90e+06    42s\n", "   5   3.25090195e+11 -1.97933682e+12  6.48e+05 5.53e-11  9.22e+05    43s\n", "   6   1.47126540e+11 -7.91767677e+11  2.52e+05 5.82e-11  3.50e+05    45s\n", "   7   8.73499079e+10 -7.20879907e+11  1.19e+05 5.09e-11  2.85e+05    46s\n", "   8   6.63381951e+10 -3.51280602e+11  6.87e+04 5.82e-11  1.45e+05    48s\n", "   9   4.94772212e+10 -1.33409228e+11  3.16e+04 5.82e-11  6.24e+04    49s\n", "  10   3.84509237e+10 -5.05587835e+10  1.32e+04 4.37e-11  3.00e+04    51s\n", "  11   3.22028867e+10 -2.36220849e+10  4.62e+03 4.37e-11  1.86e+04    52s\n", "  12   3.03864493e+10 -1.00271515e+10  2.67e+03 3.64e-11  1.34e+04    54s\n", "  13   2.93417089e+10 -1.86994531e+09  1.70e+03 2.91e-11  1.03e+04    55s\n", "  14   2.84918486e+10  3.62218682e+09  9.87e+02 2.91e-11  8.22e+03    57s\n", "  15   2.78208881e+10  1.83454364e+10  4.87e+02 2.91e-11  3.13e+03    58s\n", "  16   2.74395173e+10  2.41873296e+10  2.47e+02 3.64e-11  1.08e+03    60s\n", "  17   2.72135206e+10  2.55000471e+10  1.24e+02 3.64e-11  5.67e+02    62s\n", "  18   2.70898599e+10  2.61638165e+10  6.15e+01 3.64e-11  3.06e+02    63s\n", "  19   2.70468997e+10  2.64076523e+10  4.09e+01 3.64e-11  2.11e+02    64s\n", "  20   2.70152596e+10  2.64695990e+10  2.61e+01 2.91e-11  1.80e+02    65s\n", "  21   2.70077774e+10  2.65710330e+10  2.27e+01 2.18e-11  1.44e+02    66s\n", "  22   2.69972456e+10  2.66523729e+10  1.78e+01 2.18e-11  1.14e+02    68s\n", "  23   2.69788580e+10  2.68104455e+10  9.53e+00 1.82e-11  5.56e+01    69s\n", "  24   2.69688885e+10  2.69007708e+10  5.58e+00 1.27e-11  2.25e+01    71s\n", "  25   2.69644893e+10  2.69359418e+10  4.04e+00 2.91e-11  9.47e+00    73s\n", "  26   2.69583227e+10  2.69435647e+10  2.07e+00 1.19e-09  4.89e+00    74s\n", "  27   2.69564491e+10  2.69464797e+10  1.50e+00 1.34e-09  3.31e+00    76s\n", "  28   2.69545953e+10  2.69474326e+10  9.66e-01 1.33e-09  2.37e+00    77s\n", "  29   2.69536677e+10  2.69493102e+10  7.07e-01 1.19e-09  1.45e+00    81s\n", "  30   2.69529036e+10  2.69498260e+10  4.79e-01 1.03e-09  1.02e+00    83s\n", "  31   2.69526366e+10  2.69502442e+10  3.90e-01 8.46e-10  7.94e-01    85s\n", "  32   2.69523549e+10  2.69504943e+10  3.01e-01 7.25e-10  6.18e-01    86s\n", "  33   2.69521052e+10  2.69508692e+10  2.24e-01 5.37e-10  4.11e-01    88s\n", "  34   2.69520035e+10  2.69509919e+10  1.92e-01 4.62e-10  3.36e-01    90s\n", "  35   2.69518731e+10  2.69512046e+10  1.52e-01 3.19e-10  2.23e-01    92s\n", "  36   2.69516402e+10  2.69513092e+10  6.82e-02 2.19e-10  1.10e-01    94s\n", "  37   2.69515134e+10  2.69513895e+10  2.34e-02 1.10e-10  4.12e-02    96s\n", "  38   2.69514533e+10  2.69514321e+10  2.39e-03 3.34e-11  7.04e-03    98s\n", "  39   2.69514464e+10  2.69514411e+10  4.53e-04 1.11e-11  1.75e-03    99s\n", "  40   2.69514455e+10  2.69514444e+10  1.51e-04 5.46e-12  3.54e-04   100s\n", "  41   2.69514453e+10  2.69514453e+10  1.48e-06 1.09e-11  1.73e-05   102s\n", "  42   2.69514453e+10  2.69514453e+10  1.51e-07 1.09e-11  2.65e-06   104s\n", "  43   2.69514453e+10  2.69514453e+10  5.34e-05 1.09e-11  2.21e-08   106s\n", "  44   2.69514453e+10  2.69514453e+10  1.52e-06 1.46e-11  5.65e-11   107s\n", "\n", "<PERSON><PERSON> solved model in 44 iterations and 106.92 seconds (56.36 work units)\n", "Optimal objective 2.69514453e+10\n", "\n", "\n", "Root crossover log...\n", "\n", "  113031 DPushes remaining with DInf 0.0000000e+00               109s\n", "    6550 DPushes remaining with DInf 0.0000000e+00               110s\n", "       0 DPushes remaining with DInf 0.0000000e+00               112s\n", "Warning: Markowitz tolerance tightened to 0.5\n", "\n", "  306719 PPushes remaining with PInf 0.0000000e+00               112s\n", "  271029 PPushes remaining with PInf 0.0000000e+00               115s\n", "  228082 PPushes remaining with PInf 0.0000000e+00               120s\n", "  189643 PPushes remaining with PInf 0.0000000e+00               125s\n", "  171644 PPushes remaining with PInf 0.0000000e+00               130s\n", "  153738 PPushes remaining with PInf 0.0000000e+00               135s\n", "  137649 PPushes remaining with PInf 0.0000000e+00               140s\n", "  123221 PPushes remaining with PInf 0.0000000e+00               145s\n", "  110038 PPushes remaining with PInf 0.0000000e+00               150s\n", "   89006 PPushes remaining with PInf 0.0000000e+00               155s\n", "   73194 PPushes remaining with PInf 0.0000000e+00               160s\n", "   58286 PPushes remaining with PInf 0.0000000e+00               165s\n", "   44784 PPushes remaining with PInf 0.0000000e+00               170s\n", "   29167 PPushes remaining with PInf 0.0000000e+00               175s\n", "   16216 PPushes remaining with PInf 0.0000000e+00               180s\n", "    4564 PPushes remaining with PInf 0.0000000e+00               185s\n", "       0 PPushes remaining with PInf 0.0000000e+00               187s\n", "\n", "  Push phase complete: Pinf 0.0000000e+00, Dinf 2.9103072e-08    187s\n", "\n", "\n", "Root simplex log...\n", "\n", "Iteration    Objective       Primal Inf.    Dual Inf.      Time\n", "  372217    2.6951445e+10   0.000000e+00   0.000000e+00    188s\n", "Concurrent spin time: 25.37s (can be avoided by choosing Method=3)\n", "\n", "Solved with barrier\n", "  372276    2.6951445e+10   0.000000e+00   0.000000e+00    214s\n", "Extra simplex iterations after uncrush: 59\n", "\n", "Root relaxation: objective 2.695145e+10, 372276 iterations, 199.69 seconds (79.19 work units)\n", "Total elapsed time = 299.15s (DegenMoves)\n", "Total elapsed time = 317.27s (<PERSON>gen<PERSON>oves)\n", "Total elapsed time = 330.46s (DegenMoves)\n", "Total elapsed time = 339.53s (<PERSON><PERSON><PERSON><PERSON><PERSON>)\n", "\n", "    Nodes    |    Current Node    |     Objective Bounds      |     Work\n", " Expl Unexpl |  Obj  Depth IntInf | Incumbent    BestBd   Gap | It/Node Time\n", "\n", "     0     0 2.6951e+10    0  371          - 2.6951e+10      -     -  351s\n", "H    0     0                    2.695383e+10 2.6951e+10  0.01%     -  444s\n", "\n", "Explored 1 nodes (481873 simplex iterations) in 445.05 seconds (277.00 work units)\n", "Thread count was 8 (of 8 available processors)\n", "\n", "Solution count 1: 2.69538e+10 \n", "\n", "Optimal solution found (tolerance 1.00e-03)\n", "Warning: max constraint violation (4.2671e-06) exceeds tolerance\n", "Best objective 2.695383426441e+10, best bound 2.695144532017e+10, gap 0.0089%\n", "月度_2找到最优解\n", "目标值 (总成本): 26,953,834,264.41\n", "月度_2结果已保存\n", "\n", "求解月度_3...\n", "Set parameter TimeLimit to value 10800\n", "Set parameter MIPGap to value 0.001\n", "Gurobi Optimizer version 12.0.0 build v12.0.0rc1 (mac64[arm] - Darwin 24.1.0 24B83)\n", "\n", "CPU model: Apple M3\n", "Thread count: 8 physical cores, 8 logical processors, using up to 8 threads\n", "\n", "Non-default parameters:\n", "TimeLimit  10800\n", "MIPGap  0.001\n", "\n", "Optimize a model with 4214082 rows, 2999064 columns and 10798305 nonzeros\n", "Model fingerprint: 0x4759045d\n", "Model has 278256 simple general constraints\n", "  278256 ABS\n", "Variable types: 2961120 continuous, 37944 integer (37944 binary)\n", "Coefficient statistics:\n", "  Matrix range     [2e-03, 5e+04]\n", "  Objective range  [1e-01, 1e+03]\n", "  Bounds range     [1e+00, 1e+00]\n", "  RHS range        [5e-03, 2e+07]\n", "Presolve removed 3253662 rows and 1757538 columns (presolve time = 5s)...\n", "Presolve removed 3253816 rows and 1758003 columns (presolve time = 10s)...\n", "Presolve removed 2948159 rows and 1766190 columns\n", "Presolve time: 13.08s\n", "Presolved: 1265923 rows, 1232996 columns, 4930841 nonzeros\n", "Variable types: 1195796 continuous, 37200 integer (37200 binary)\n", "Deterministic concurrent LP optimizer: primal simplex, dual simplex, and barrier\n", "Showing barrier log only...\n", "\n", "Root barrier log...\n", "\n", "Elapsed ordering time = 5s\n", "Ordering time: 11.29s\n", "\n", "Barrier statistics:\n", " AA' NZ     : 4.967e+06\n", " Factor NZ  : 3.654e+07 (roughly 1.2 GB of memory)\n", " Factor Ops : 1.566e+10 (less than 1 second per iteration)\n", " Threads    : 5\n", "\n", "                  Objective                Residual\n", "Iter       Primal          Dual         Primal    Dual     Compl     Time\n", "   0  -6.12830055e+14 -5.50586112e+13  2.07e+10 1.36e+04  8.76e+09    35s\n", "   1  -2.29300788e+14 -4.84763141e+13  8.63e+09 4.37e-11  3.70e+09    36s\n", "   2  -8.34192007e+12 -4.10112818e+13  4.40e+08 6.26e-11  2.01e+08    38s\n", "   3   6.60869180e+11 -3.21816052e+13  2.29e+07 5.53e-11  1.95e+07    40s\n", "   4   7.28838616e+11 -1.31299076e+13  6.15e+06 5.53e-11  6.25e+06    41s\n", "   5   4.43817453e+11 -2.78324712e+12  4.38e+05 6.26e-11  1.07e+06    43s\n", "   6   1.54999376e+11 -1.47979425e+12  9.14e+04 5.82e-11  5.02e+05    44s\n", "   7   8.87393936e+10 -4.29926784e+11  3.55e+04 8.73e-11  1.57e+05    46s\n", "   8   6.65009246e+10 -1.63224805e+11  2.05e+04 7.28e-11  6.94e+04    47s\n", "   9   4.40723643e+10 -8.36900073e+10  5.14e+03 4.37e-11  3.82e+04    48s\n", "  10   3.66810998e+10 -3.34543073e+10  2.03e+03 4.37e-11  2.09e+04    50s\n", "  11   3.33447843e+10 -1.70560363e+10  9.38e+02 2.91e-11  1.50e+04    52s\n", "  12   3.18984464e+10  2.84711204e+09  5.05e+02 4.37e-11  8.65e+03    53s\n", "  13   3.08124425e+10  1.59702870e+10  2.25e+02 5.82e-11  4.42e+03    54s\n", "  14   3.01462783e+10  2.59465773e+10  8.72e+01 8.73e-11  1.25e+03    56s\n", "  15   2.99783664e+10  2.75162294e+10  5.81e+01 8.73e-11  7.33e+02    57s\n", "  16   2.98601060e+10  2.82276366e+10  3.88e+01 5.82e-11  4.86e+02    59s\n", "  17   2.97546701e+10  2.88744404e+10  2.19e+01 4.37e-11  2.62e+02    60s\n", "  18   2.96718285e+10  2.92013974e+10  9.10e+00 4.37e-11  1.40e+02    62s\n", "  19   2.96508174e+10  2.93333266e+10  5.97e+00 2.91e-11  9.45e+01    63s\n", "  20   2.96322040e+10  2.94753225e+10  3.37e+00 2.18e-11  4.67e+01    64s\n", "  21   2.96155824e+10  2.95459513e+10  1.25e+00 1.46e-11  2.07e+01    66s\n", "  22   2.96090113e+10  2.95812270e+10  5.64e-01 1.75e-10  8.27e+00    68s\n", "  23   2.96069786e+10  2.95894664e+10  3.82e-01 1.16e-10  5.21e+00    70s\n", "  24   2.96056764e+10  2.95956601e+10  2.74e-01 2.25e-10  2.98e+00    71s\n", "  25   2.96046194e+10  2.95981959e+10  1.91e-01 4.03e-10  1.91e+00    73s\n", "  26   2.96036954e+10  2.95997750e+10  1.19e-01 4.62e-10  1.17e+00    74s\n", "  27   2.96029768e+10  2.96010865e+10  6.31e-02 4.10e-10  5.63e-01    75s\n", "  28   2.96025402e+10  2.96016647e+10  2.94e-02 3.00e-10  2.61e-01    77s\n", "  29   2.96023769e+10  2.96018120e+10  1.75e-02 2.38e-10  1.68e-01    78s\n", "  30   2.96023500e+10  2.96018707e+10  1.54e-02 2.05e-10  1.43e-01    79s\n", "  31   2.96022794e+10  2.96020052e+10  9.98e-03 1.30e-10  8.17e-02    81s\n", "  32   2.96022043e+10  2.96020532e+10  3.95e-03 9.73e-11  4.50e-02    82s\n", "  33   2.96021721e+10  2.96021192e+10  1.38e-03 4.09e-11  1.58e-02    84s\n", "  34   2.96021687e+10  2.96021300e+10  1.12e-03 2.92e-11  1.15e-02    85s\n", "  35   2.96021562e+10  2.96021495e+10  1.68e-04 7.73e-12  2.01e-03    86s\n", "  36   2.96021539e+10  2.96021537e+10  1.44e-05 7.28e-12  6.97e-05    89s\n", "  37   2.96021538e+10  2.96021538e+10  2.04e-03 7.28e-12  1.15e-06    94s\n", "  38   2.96021538e+10  2.96021538e+10  2.65e-03 7.28e-12  5.56e-09    96s\n", "\n", "<PERSON><PERSON> solved model in 38 iterations and 96.25 seconds (60.44 work units)\n", "Optimal objective 2.96021538e+10\n", "\n", "\n", "Root crossover log...\n", "\n", "  136638 DPushes remaining with DInf 0.0000000e+00                98s\n", "     438 DPushes remaining with DInf 0.0000000e+00               100s\n", "       0 DPushes remaining with DInf 0.0000000e+00               101s\n", "Warning: Markowitz tolerance tightened to 0.5\n", "\n", "  354392 PPushes remaining with PInf 5.6203660e-05               101s\n", "  327994 PPushes remaining with PInf 0.0000000e+00               105s\n", "  308539 PPushes remaining with PInf 0.0000000e+00               110s\n", "  280303 PPushes remaining with PInf 0.0000000e+00               115s\n", "  252496 PPushes remaining with PInf 0.0000000e+00               120s\n", "  226861 PPushes remaining with PInf 0.0000000e+00               125s\n", "  218707 PPushes remaining with PInf 0.0000000e+00               130s\n", "  209019 PPushes remaining with PInf 0.0000000e+00               135s\n", "  199171 PPushes remaining with PInf 0.0000000e+00               140s\n", "  190991 PPushes remaining with PInf 0.0000000e+00               145s\n", "  180843 PPushes remaining with PInf 0.0000000e+00               150s\n", "  172997 PPushes remaining with PInf 0.0000000e+00               155s\n", "  165137 PPushes remaining with PInf 0.0000000e+00               160s\n", "  157221 PPushes remaining with PInf 0.0000000e+00               165s\n", "  150467 PPushes remaining with PInf 0.0000000e+00               170s\n", "  142627 PPushes remaining with PInf 0.0000000e+00               175s\n", "  135240 PPushes remaining with PInf 0.0000000e+00               180s\n", "  126879 PPushes remaining with PInf 0.0000000e+00               185s\n", "  117461 PPushes remaining with PInf 0.0000000e+00               191s\n", "  110060 PPushes remaining with PInf 0.0000000e+00               195s\n", "  102337 PPushes remaining with PInf 0.0000000e+00               200s\n", "   94281 PPushes remaining with PInf 0.0000000e+00               205s\n", "   87177 PPushes remaining with PInf 0.0000000e+00               210s\n", "   79987 PPushes remaining with PInf 0.0000000e+00               215s\n", "   72805 PPushes remaining with PInf 0.0000000e+00               220s\n", "   65723 PPushes remaining with PInf 0.0000000e+00               225s\n", "   58539 PPushes remaining with PInf 0.0000000e+00               230s\n", "   47036 PPushes remaining with PInf 0.0000000e+00               235s\n", "   33109 PPushes remaining with PInf 0.0000000e+00               240s\n", "   20778 PPushes remaining with PInf 0.0000000e+00               245s\n", "   12557 PPushes remaining with PInf 0.0000000e+00               250s\n", "    5486 PPushes remaining with PInf 0.0000000e+00               255s\n", "       0 PPushes remaining with PInf 0.0000000e+00               259s\n", "\n", "  Push phase complete: Pinf 0.0000000e+00, Dinf 3.0996191e-08    259s\n", "\n", "\n", "Root simplex log...\n", "\n", "Iteration    Objective       Primal Inf.    Dual Inf.      Time\n", "  443695    2.9602154e+10   0.000000e+00   0.000000e+00    260s\n", "Concurrent spin time: 14.25s\n", "\n", "Solved with barrier\n", "  443764    2.9602154e+10   0.000000e+00   0.000000e+00    275s\n", "Extra simplex iterations after uncrush: 69\n", "\n", "Root relaxation: objective 2.960215e+10, 443764 iterations, 260.53 seconds (121.81 work units)\n", "Total elapsed time = 463.98s (<PERSON>genMoves)\n", "Total elapsed time = 483.61s (<PERSON><PERSON><PERSON>oves)\n", "Total elapsed time = 490.02s (DegenMoves)\n", "\n", "    Nodes    |    Current Node    |     Objective Bounds      |     Work\n", " Expl Unexpl |  Obj  Depth IntInf | Incumbent    BestBd   Gap | It/Node Time\n", "\n", "     0     0 2.9602e+10    0  494          - 2.9602e+10      -     -  491s\n", "H    0     0                    2.961332e+10 2.9602e+10  0.04%     -  611s\n", "\n", "Explored 1 nodes (540407 simplex iterations) in 611.55 seconds (404.55 work units)\n", "Thread count was 8 (of 8 available processors)\n", "\n", "Solution count 1: 2.96133e+10 \n", "\n", "Optimal solution found (tolerance 1.00e-03)\n", "Best objective 2.961331964053e+10, best bound 2.960215377420e+10, gap 0.0377%\n", "月度_3找到最优解\n", "目标值 (总成本): 29,613,319,640.53\n", "月度_3结果已保存\n", "\n", "求解月度_4...\n", "Set parameter TimeLimit to value 10800\n", "Set parameter MIPGap to value 0.001\n", "Gurobi Optimizer version 12.0.0 build v12.0.0rc1 (mac64[arm] - Darwin 24.1.0 24B83)\n", "\n", "CPU model: Apple M3\n", "Thread count: 8 physical cores, 8 logical processors, using up to 8 threads\n", "\n", "Non-default parameters:\n", "TimeLimit  10800\n", "MIPGap  0.001\n", "\n", "Optimize a model with 4078122 rows, 2902320 columns and 10449897 nonzeros\n", "Model fingerprint: 0xce4512d2\n", "Model has 269280 simple general constraints\n", "  269280 ABS\n", "Variable types: 2865600 continuous, 36720 integer (36720 binary)\n", "Coefficient statistics:\n", "  Matrix range     [2e-03, 5e+04]\n", "  Objective range  [1e-01, 1e+03]\n", "  Bounds range     [1e+00, 1e+00]\n", "  RHS range        [5e-03, 1e+07]\n", "Presolve removed 3150141 rows and 1702668 columns (presolve time = 5s)...\n", "Presolve removed 3150329 rows and 1703297 columns (presolve time = 10s)...\n", "Presolve removed 2854527 rows and 1711167 columns\n", "Presolve time: 12.34s\n", "Presolved: 1223595 rows, 1191273 columns, 4765660 nonzeros\n", "Variable types: 1155273 continuous, 36000 integer (36000 binary)\n", "Deterministic concurrent LP optimizer: primal simplex, dual simplex, and barrier\n", "Showing barrier log only...\n", "\n", "Root barrier log...\n", "\n", "Elapsed ordering time = 5s\n", "Ordering time: 10.61s\n", "\n", "Barrier statistics:\n", " AA' NZ     : 4.792e+06\n", " Factor NZ  : 3.499e+07 (roughly 1.2 GB of memory)\n", " Factor Ops : 1.459e+10 (less than 1 second per iteration)\n", " Threads    : 5\n", "\n", "                  Objective                Residual\n", "Iter       Primal          Dual         Primal    Dual     Compl     Time\n", "   0  -3.46110068e+14 -2.85026403e+13  1.17e+10 8.00e+03  2.81e+09    33s\n", "   1  -1.20128959e+14 -2.39610703e+13  4.76e+09 2.18e-11  1.10e+09    34s\n", "   2  -4.82098714e+12 -1.93009424e+13  3.30e+08 2.62e-11  8.31e+07    35s\n", "   3   1.94673035e+11 -1.46426407e+13  4.18e+07 1.09e-10  1.42e+07    37s\n", "   4   4.44360450e+11 -4.70815457e+12  5.75e+06 1.09e-10  2.69e+06    39s\n", "   5   1.65785174e+11 -1.51054863e+12  8.28e+05 3.71e-11  6.27e+05    40s\n", "   6   8.83645378e+10 -6.88929512e+11  2.94e+05 3.64e-11  2.67e+05    42s\n", "   7   5.87502263e+10 -1.78104576e+11  1.32e+05 2.91e-11  8.02e+04    43s\n", "   8   4.14988491e+10 -8.43904241e+10  5.04e+04 1.89e-11  4.09e+04    44s\n", "   9   3.66699834e+10 -4.97452042e+10  3.14e+04 1.46e-11  2.77e+04    45s\n", "  10   3.41073050e+10 -2.55247007e+10  2.13e+04 1.46e-11  1.90e+04    46s\n", "  11   3.05707489e+10  2.96001504e+09  8.57e+03 1.46e-11  8.71e+03    48s\n", "  12   2.94859599e+10  6.34582009e+09  5.31e+03 1.46e-11  7.25e+03    49s\n", "  13   2.89983504e+10  8.71455907e+09  3.89e+03 1.46e-11  6.33e+03    51s\n", "  14   2.82147028e+10  1.96874050e+10  1.78e+03 1.46e-11  2.66e+03    52s\n", "  15   2.77505809e+10  2.42626961e+10  6.80e+02 1.82e-11  1.08e+03    54s\n", "  16   2.75420257e+10  2.53232007e+10  2.54e+02 1.46e-11  6.87e+02    55s\n", "  17   2.74911197e+10  2.64125470e+10  1.61e+02 1.46e-11  3.34e+02    56s\n", "  18   2.74400803e+10  2.70600804e+10  7.52e+01 1.46e-11  1.18e+02    57s\n", "  19   2.74131391e+10  2.72258558e+10  3.55e+01 1.46e-11  5.82e+01    59s\n", "  20   2.73944005e+10  2.73115591e+10  1.23e+01 1.09e-11  2.57e+01    60s\n", "  21   2.73885700e+10  2.73390394e+10  6.40e+00 6.37e-12  1.53e+01    61s\n", "  22   2.73845542e+10  2.73630770e+10  3.00e+00 3.54e-11  6.65e+00    62s\n", "  23   2.73824730e+10  2.73741255e+10  1.36e+00 3.08e-10  2.59e+00    64s\n", "  24   2.73812743e+10  2.73769481e+10  5.07e-01 3.36e-10  1.34e+00    65s\n", "  25   2.73810828e+10  2.73781177e+10  3.72e-01 2.75e-10  9.18e-01    66s\n", "  26   2.73808959e+10  2.73792205e+10  2.44e-01 2.02e-10  5.19e-01    68s\n", "  27   2.73807247e+10  2.73797039e+10  1.32e-01 1.53e-10  3.16e-01    69s\n", "  28   2.73806352e+10  2.73799669e+10  7.32e-02 1.17e-10  2.07e-01    70s\n", "  29   2.73806196e+10  2.73800860e+10  6.23e-02 9.60e-11  1.65e-01    72s\n", "  30   2.73805775e+10  2.73803090e+10  3.46e-02 5.68e-11  8.31e-02    73s\n", "  31   2.73805413e+10  2.73804130e+10  9.65e-03 3.45e-11  3.96e-02    74s\n", "  32   2.73805315e+10  2.73804469e+10  3.46e-03 2.48e-11  2.61e-02    77s\n", "  33   2.73805299e+10  2.73804633e+10  2.46e-03 1.96e-11  2.05e-02    78s\n", "  34   2.73805290e+10  2.73804789e+10  1.89e-03 1.48e-11  1.54e-02    80s\n", "  35   2.73805262e+10  2.73805245e+10  2.22e-04 5.46e-12  5.40e-04    82s\n", "  36   2.73805260e+10  2.73805259e+10  7.31e-06 5.46e-12  1.19e-05    84s\n", "  37   2.73805259e+10  2.73805259e+10  1.91e-06 5.46e-12  2.49e-07    85s\n", "  38   2.73805259e+10  2.73805259e+10  8.57e-08 5.46e-12  2.66e-10    87s\n", "\n", "<PERSON><PERSON> solved model in 38 iterations and 87.01 seconds (55.67 work units)\n", "Optimal objective 2.73805259e+10\n", "\n", "\n", "Root crossover log...\n", "\n", "  130434 DPushes remaining with DInf 0.0000000e+00                89s\n", "    3367 DPushes remaining with DInf 0.0000000e+00                90s\n", "       0 DPushes remaining with DInf 0.0000000e+00                91s\n", "Warning: Markowitz tolerance tightened to 0.5\n", "\n", "  364353 PPushes remaining with PInf 0.0000000e+00                91s\n", "  323056 PPushes remaining with PInf 0.0000000e+00                95s\n", "  283228 PPushes remaining with PInf 0.0000000e+00               100s\n", "  239656 PPushes remaining with PInf 0.0000000e+00               105s\n", "  212168 PPushes remaining with PInf 0.0000000e+00               110s\n", "  195476 PPushes remaining with PInf 0.0000000e+00               115s\n", "  179946 PPushes remaining with PInf 0.0000000e+00               120s\n", "  162729 PPushes remaining with PInf 0.0000000e+00               125s\n", "  146868 PPushes remaining with PInf 0.0000000e+00               130s\n", "  132561 PPushes remaining with PInf 0.0000000e+00               135s\n", "  116186 PPushes remaining with PInf 0.0000000e+00               141s\n", "  101536 PPushes remaining with PInf 0.0000000e+00               145s\n", "   78093 PPushes remaining with PInf 0.0000000e+00               150s\n", "   61360 PPushes remaining with PInf 0.0000000e+00               155s\n", "   41780 PPushes remaining with PInf 0.0000000e+00               160s\n", "   28142 PPushes remaining with PInf 0.0000000e+00               165s\n", "   15025 PPushes remaining with PInf 0.0000000e+00               170s\n", "    1779 PPushes remaining with PInf 0.0000000e+00               175s\n", "       0 PPushes remaining with PInf 0.0000000e+00               176s\n", "\n", "  Push phase complete: Pinf 0.0000000e+00, Dinf 2.9340378e-08    176s\n", "\n", "\n", "Root simplex log...\n", "\n", "Iteration    Objective       Primal Inf.    Dual Inf.      Time\n", "  434255    2.7380526e+10   0.000000e+00   0.000000e+00    177s\n", "Concurrent spin time: 16.69s\n", "\n", "Solved with barrier\n", "  434260    2.7380526e+10   0.000000e+00   0.000000e+00    195s\n", "Extra simplex iterations after uncrush: 5\n", "\n", "Root relaxation: objective 2.738053e+10, 434260 iterations, 180.73 seconds (83.64 work units)\n", "Total elapsed time = 508.02s (DegenMoves)\n", "Total elapsed time = 531.63s (<PERSON>gen<PERSON>oves)\n", "Total elapsed time = 559.90s (DegenMoves)\n", "Total elapsed time = 574.47s (<PERSON>gen<PERSON>oves)\n", "\n", "    Nodes    |    Current Node    |     Objective Bounds      |     Work\n", " Expl Unexpl |  Obj  Depth IntInf | Incumbent    BestBd   Gap | It/Node Time\n", "\n", "     0     0 2.7381e+10    0  877          - 2.7381e+10      -     -  576s\n", "H    0     0                    3.910460e+12 2.7381e+10  99.3%     -  722s\n", "     0     0 2.7382e+10    0  981 3.9105e+12 2.7382e+10  99.3%     -  796s\n", "H    0     0                    2.739106e+10 2.7382e+10  0.03%     -  910s\n", "\n", "Cutting planes:\n", "  Gomory: 37\n", "  Implied bound: 2\n", "  MIR: 702\n", "  Flow cover: 597\n", "\n", "Explored 1 nodes (629315 simplex iterations) in 911.00 seconds (634.67 work units)\n", "Thread count was 8 (of 8 available processors)\n", "\n", "Solution count 2: 2.73911e+10 3.91046e+12 \n", "\n", "Optimal solution found (tolerance 1.00e-03)\n", "Best objective 2.739106165286e+10, best bound 2.738173004798e+10, gap 0.0341%\n", "月度_4找到最优解\n", "目标值 (总成本): 27,391,061,652.86\n", "月度_4结果已保存\n", "\n", "求解月度_5...\n", "Set parameter TimeLimit to value 10800\n", "Set parameter MIPGap to value 0.001\n", "Gurobi Optimizer version 12.0.0 build v12.0.0rc1 (mac64[arm] - Darwin 24.1.0 24B83)\n", "\n", "CPU model: Apple M3\n", "Thread count: 8 physical cores, 8 logical processors, using up to 8 threads\n", "\n", "Non-default parameters:\n", "TimeLimit  10800\n", "MIPGap  0.001\n", "\n", "Optimize a model with 4214082 rows, 2999064 columns and 10798305 nonzeros\n", "Model fingerprint: 0xe9050426\n", "Model has 278256 simple general constraints\n", "  278256 ABS\n", "Variable types: 2961120 continuous, 37944 integer (37944 binary)\n", "Coefficient statistics:\n", "  Matrix range     [2e-03, 5e+04]\n", "  Objective range  [1e-01, 1e+03]\n", "  Bounds range     [1e+00, 1e+00]\n", "  RHS range        [5e-03, 1e+07]\n", "Presolve removed 3255232 rows and 1757914 columns (presolve time = 5s)...\n", "Presolve removed 3272467 rows and 1771039 columns (presolve time = 10s)...\n", "Presolve removed 3285230 rows and 1783792 columns (presolve time = 15s)...\n", "Presolve removed 2978772 rows and 1791181 columns\n", "Presolve time: 19.77s\n", "Presolved: 1235310 rows, 1208002 columns, 4818509 nonzeros\n", "Variable types: 1170852 continuous, 37150 integer (37150 binary)\n", "Deterministic concurrent LP optimizer: primal simplex, dual simplex, and barrier\n", "Showing barrier log only...\n", "\n", "Root barrier log...\n", "\n", "Elapsed ordering time = 6s\n", "Ordering time: 14.20s\n", "\n", "Barrier statistics:\n", " AA' NZ     : 4.852e+06\n", " Factor NZ  : 3.608e+07 (roughly 1.2 GB of memory)\n", " Factor Ops : 1.517e+10 (roughly 1 second per iteration)\n", " Threads    : 5\n", "\n", "                  Objective                Residual\n", "Iter       Primal          Dual         Primal    Dual     Compl     Time\n", "   0  -4.08216044e+14 -9.58886322e+15  3.98e+09 5.75e+03  1.06e+12    46s\n", "   1  -5.47420986e+13 -8.28567500e+15  8.27e+08 5.84e-09  2.18e+11    47s\n", "   2  -1.32349471e+12 -6.08965288e+15  5.63e+07 3.84e-07  1.63e+10    49s\n", "   3   1.70975915e+12 -3.17052097e+15  4.03e+06 1.19e-07  1.90e+09    51s\n", "   4   1.90670247e+12 -8.14198824e+14  7.76e+05 3.31e-08  3.75e+08    53s\n", "   5   1.67625942e+12 -3.88519773e+14  5.13e+04 2.61e-08  1.24e+08    55s\n", "   6   1.39269746e+12 -1.10651581e+14  5.70e+03 1.68e-08  3.40e+07    57s\n", "   7   1.08258949e+12 -2.16657702e+13  8.91e+02 2.24e-08  6.86e+06    59s\n", "   8   6.14283509e+11 -4.99408842e+12  2.33e+02 7.45e-09  1.69e+06    60s\n", "   9   2.78917080e+11 -3.10533501e+12  7.91e+01 5.59e-09  1.02e+06    62s\n", "  10   1.20977245e+11 -8.34084603e+11  2.44e+01 1.12e-08  2.88e+05    63s\n", "  11   8.95452299e+10 -7.23838143e+11  1.42e+01 1.12e-08  2.45e+05    65s\n", "  12   4.88832674e+10 -4.00492568e+11  2.29e+00 7.45e-09  1.35e+05    67s\n", "  13   4.09637311e+10 -1.04120807e+11  1.01e+00 5.59e-09  4.37e+04    69s\n", "  14   3.53951594e+10 -5.68166593e+10  3.47e-01 5.59e-09  2.78e+04    70s\n", "  15   3.31918897e+10 -2.53828760e+10  1.83e-01 3.73e-09  1.76e+04    73s\n", "  16   3.28232753e+10 -2.44153566e+10  1.62e-01 3.73e-09  1.72e+04    75s\n", "  17   3.25549808e+10 -2.27905600e+10  1.45e-01 4.66e-09  1.67e+04    76s\n", "  18   3.22150715e+10 -2.25286860e+10  1.26e-01 5.59e-09  1.65e+04    78s\n", "  19   3.20480648e+10 -3.24240524e+09  1.17e-01 2.79e-09  1.06e+04    80s\n", "  20   3.05261728e+10  1.19189686e+10  3.67e-02 1.86e-09  5.60e+03    82s\n", "  21   2.99088236e+10  2.45879541e+10  1.20e-02 1.63e-09  1.60e+03    85s\n", "  22   2.96488736e+10  2.68570678e+10  4.54e-03 9.31e-10  8.41e+02    87s\n", "  23   2.95727107e+10  2.81999064e+10  2.72e-03 4.66e-10  4.13e+02    89s\n", "  24   2.95251818e+10  2.87423331e+10  1.67e-03 9.31e-10  2.36e+02    90s\n", "  25   2.94906042e+10  2.90633884e+10  9.55e-04 4.66e-10  1.29e+02    92s\n", "  26   2.94687771e+10  2.92465599e+10  5.41e-04 1.16e-10  6.69e+01    93s\n", "  27   2.94530551e+10  2.93238243e+10  2.66e-04 9.31e-10  3.89e+01    95s\n", "  28   2.94438740e+10  2.93812533e+10  1.28e-04 1.25e-10  1.89e+01    96s\n", "  29   2.94378468e+10  2.93991484e+10  5.46e-05 2.25e-10  1.17e+01    98s\n", "  30   2.94362041e+10  2.94172531e+10  3.77e-05 9.31e-10  5.71e+00   100s\n", "  31   2.94328160e+10  2.94246712e+10  6.29e-06 1.96e-10  2.45e+00   103s\n", "  32   2.94324189e+10  2.94283542e+10  3.85e-06 9.31e-10  1.22e+00   105s\n", "  33   2.94321902e+10  2.94294694e+10  5.76e-06 9.31e-10  8.19e-01   106s\n", "  34   2.94320763e+10  2.94307215e+10  1.99e-05 9.31e-10  4.08e-01   108s\n", "  35   2.94319840e+10  2.94313014e+10  1.25e-05 2.39e-11  2.06e-01   110s\n", "  36   2.94319491e+10  2.94316338e+10  1.47e-05 2.33e-10  9.49e-02   112s\n", "  37   2.94319167e+10  2.94317805e+10  4.71e-05 1.46e-11  4.10e-02   114s\n", "  38   2.94319061e+10  2.94318344e+10  1.02e-04 9.31e-10  2.16e-02   116s\n", "  39   2.94319020e+10  2.94318697e+10  2.30e-04 2.33e-10  9.72e-03   118s\n", "  40   2.94318997e+10  2.94318963e+10  2.94e-05 9.31e-10  1.02e-03   120s\n", "  41   2.94318989e+10  2.94318989e+10  9.17e-07 2.33e-10  1.30e-05   123s\n", "  42   2.94318989e+10  2.94318989e+10  2.34e-07 1.46e-11  1.16e-07   126s\n", "  43   2.94318989e+10  2.94318989e+10  3.90e-08 1.46e-11  1.35e-09   128s\n", "\n", "<PERSON><PERSON> solved model in 43 iterations and 127.93 seconds (68.50 work units)\n", "Optimal objective 2.94318989e+10\n", "\n", "\n", "Root crossover log...\n", "\n", "  130873 DPushes remaining with DInf 0.0000000e+00               130s\n", "       0 DPushes remaining with DInf 0.0000000e+00               133s\n", "Warning: Markowitz tolerance tightened to 0.5\n", "\n", "  364880 PPushes remaining with PInf 0.0000000e+00               133s\n", "  341207 PPushes remaining with PInf 0.0000000e+00               135s\n", "  310599 PPushes remaining with PInf 0.0000000e+00               140s\n", "  270295 PPushes remaining with PInf 0.0000000e+00               145s\n", "  229455 PPushes remaining with PInf 0.0000000e+00               150s\n", "  217342 PPushes remaining with PInf 0.0000000e+00               155s\n", "  203871 PPushes remaining with PInf 0.0000000e+00               160s\n", "  189213 PPushes remaining with PInf 0.0000000e+00               165s\n", "  174922 PPushes remaining with PInf 0.0000000e+00               170s\n", "  162921 PPushes remaining with PInf 0.0000000e+00               175s\n", "  149621 PPushes remaining with PInf 0.0000000e+00               180s\n", "  137504 PPushes remaining with PInf 0.0000000e+00               185s\n", "  125345 PPushes remaining with PInf 0.0000000e+00               190s\n", "  110192 PPushes remaining with PInf 0.0000000e+00               195s\n", "   97691 PPushes remaining with PInf 0.0000000e+00               200s\n", "   85230 PPushes remaining with PInf 0.0000000e+00               205s\n", "   74016 PPushes remaining with PInf 0.0000000e+00               210s\n", "   63875 PPushes remaining with PInf 0.0000000e+00               215s\n", "   29118 PPushes remaining with PInf 0.0000000e+00               220s\n", "   15666 PPushes remaining with PInf 0.0000000e+00               225s\n", "    3814 PPushes remaining with PInf 0.0000000e+00               230s\n", "       0 PPushes remaining with PInf 0.0000000e+00               232s\n", "\n", "  Push phase complete: Pinf 0.0000000e+00, Dinf 3.1975126e-08    232s\n", "\n", "\n", "Root simplex log...\n", "\n", "Iteration    Objective       Primal Inf.    Dual Inf.      Time\n", "  443929    2.9431899e+10   0.000000e+00   0.000000e+00    233s\n", "Concurrent spin time: 36.54s (can be avoided by choosing Method=3)\n", "\n", "Solved with barrier\n", "  443929    2.9431899e+10   0.000000e+00   4.863337e+04    270s\n", "  443941    2.9431899e+10   0.000000e+00   0.000000e+00    271s\n", "Extra simplex iterations after uncrush: 12\n", "\n", "Root relaxation: objective 2.943190e+10, 443941 iterations, 249.14 seconds (87.30 work units)\n", "Total elapsed time = 438.58s (<PERSON>gen<PERSON>oves)\n", "Total elapsed time = 463.15s (DegenMoves)\n", "Total elapsed time = 485.79s (<PERSON>gen<PERSON>oves)\n", "Total elapsed time = 505.12s (DegenMoves)\n", "\n", "    Nodes    |    Current Node    |     Objective Bounds      |     Work\n", " Expl Unexpl |  Obj  Depth IntInf | Incumbent    BestBd   Gap | It/Node Time\n", "\n", "     0     0 2.9432e+10    0  774          - 2.9432e+10      -     -  525s\n", "H    0     0                    3.514830e+12 2.9432e+10  99.2%     -  710s\n", "     0     0 2.9433e+10    0  876 3.5148e+12 2.9433e+10  99.2%     -  790s\n", "H    0     0                    2.944205e+10 2.9433e+10  0.03%     -  942s\n", "\n", "Cutting planes:\n", "  Gomory: 44\n", "  Implied bound: 1\n", "  MIR: 622\n", "  Flow cover: 556\n", "\n", "Explored 1 nodes (595302 simplex iterations) in 943.01 seconds (526.21 work units)\n", "Thread count was 8 (of 8 available processors)\n", "\n", "Solution count 2: 2.94421e+10 3.51483e+12 \n", "\n", "Optimal solution found (tolerance 1.00e-03)\n", "Best objective 2.944205167600e+10, best bound 2.943331828541e+10, gap 0.0297%\n", "月度_5找到最优解\n", "目标值 (总成本): 29,442,051,676.00\n", "月度_5结果已保存\n", "\n", "求解月度_6...\n", "Set parameter TimeLimit to value 10800\n", "Set parameter MIPGap to value 0.001\n", "Gurobi Optimizer version 12.0.0 build v12.0.0rc1 (mac64[arm] - Darwin 24.1.0 24B83)\n", "\n", "CPU model: Apple M3\n", "Thread count: 8 physical cores, 8 logical processors, using up to 8 threads\n", "\n", "Non-default parameters:\n", "TimeLimit  10800\n", "MIPGap  0.001\n", "\n", "Optimize a model with 4078122 rows, 2902320 columns and 10449897 nonzeros\n", "Model fingerprint: 0x842061ff\n", "Model has 269280 simple general constraints\n", "  269280 ABS\n", "Variable types: 2865600 continuous, 36720 integer (36720 binary)\n", "Coefficient statistics:\n", "  Matrix range     [2e-03, 5e+04]\n", "  Objective range  [1e-01, 1e+03]\n", "  Bounds range     [1e+00, 1e+00]\n", "  RHS range        [5e-03, 1e+07]\n", "Presolve removed 3150289 rows and 1701333 columns (presolve time = 5s)...\n", "Presolve removed 3150427 rows and 1701776 columns (presolve time = 10s)...\n", "Presolve removed 3179313 rows and 1726344 columns (presolve time = 15s)...\n", "Presolve removed 2882742 rows and 1733493 columns\n", "Presolve time: 19.68s\n", "Presolved: 1195380 rows, 1168945 columns, 4662328 nonzeros\n", "Variable types: 1132995 continuous, 35950 integer (35950 binary)\n", "Deterministic concurrent LP optimizer: primal simplex, dual simplex, and barrier\n", "Showing barrier log only...\n", "\n", "Root barrier log...\n", "\n", "Elapsed ordering time = 6s\n", "Ordering time: 14.74s\n", "\n", "Barrier statistics:\n", " AA' NZ     : 4.694e+06\n", " Factor NZ  : 3.462e+07 (roughly 1.2 GB of memory)\n", " Factor Ops : 1.450e+10 (less than 1 second per iteration)\n", " Threads    : 5\n", "\n", "                  Objective                Residual\n", "Iter       Primal          Dual         Primal    Dual     Compl     Time\n", "   0  -4.79641789e+14 -9.00739993e+15  4.64e+09 5.69e+03  1.24e+12    45s\n", "   1  -6.43802706e+13 -7.89933998e+15  9.62e+08 6.52e-09  2.53e+11    47s\n", "   2  -1.02307614e+12 -5.94743528e+15  5.49e+07 1.30e-06  1.60e+10    49s\n", "   3   1.78344700e+12 -3.11653729e+15  4.02e+06 1.60e-08  1.92e+09    51s\n", "   4   1.97104099e+12 -8.20974568e+14  8.51e+05 9.87e-08  3.99e+08    53s\n", "   5   1.80355447e+12 -3.58230736e+14  6.00e+04 2.98e-08  1.19e+08    55s\n", "   6   1.37382294e+12 -4.69236007e+13  5.58e+03 1.68e-08  1.52e+07    57s\n", "   7   8.27192432e+11 -1.12923573e+13  1.29e+03 2.79e-08  3.79e+06    58s\n", "   8   3.72521001e+11 -3.33264238e+12  4.19e+02 6.52e-09  1.16e+06    60s\n", "   9   2.06654350e+11 -8.53085944e+11  1.84e+02 5.59e-09  3.30e+05    61s\n", "  10   8.90873319e+10 -2.32464200e+11  4.57e+01 7.45e-09  1.00e+05    63s\n", "  11   4.88654371e+10 -7.54710983e+10  1.25e+01 5.59e-09  3.87e+04    65s\n", "  12   4.47790998e+10 -6.71798022e+10  9.61e+00 5.59e-09  3.48e+04    66s\n", "  13   4.16546288e+10 -4.25124307e+10  7.12e+00 5.59e-09  2.62e+04    68s\n", "  14   3.96826803e+10 -5.54413107e+09  5.65e+00 3.73e-09  1.41e+04    69s\n", "  15   3.50925718e+10  1.42052781e+10  2.20e+00 2.79e-09  6.50e+03    71s\n", "  16   3.24507805e+10  2.52959743e+10  5.17e-01 1.86e-09  2.23e+03    74s\n", "  17   3.17086331e+10  2.82916023e+10  2.34e-01 7.45e-09  1.06e+03    76s\n", "  18   3.15465782e+10  2.84819518e+10  1.78e-01 7.45e-09  9.54e+02    79s\n", "  19   3.15020176e+10  2.90376231e+10  1.61e-01 7.45e-09  7.67e+02    80s\n", "  20   3.13274448e+10  2.98535556e+10  9.70e-02 7.45e-09  4.59e+02    82s\n", "  21   3.12577063e+10  3.02945513e+10  7.23e-02 7.45e-09  3.00e+02    83s\n", "  22   3.11315713e+10  3.05699002e+10  3.06e-02 3.73e-09  1.75e+02    85s\n", "  23   3.10801617e+10  3.08226302e+10  1.45e-02 3.73e-09  8.01e+01    87s\n", "  24   3.10512026e+10  3.09590298e+10  6.02e-03 1.86e-09  2.87e+01    89s\n", "  25   3.10380911e+10  3.09961980e+10  2.77e-03 9.31e-10  1.30e+01    90s\n", "  26   3.10314920e+10  3.10111214e+10  1.34e-03 9.31e-10  6.34e+00    91s\n", "  27   3.10269833e+10  3.10184136e+10  4.62e-04 9.31e-10  2.67e+00    93s\n", "  28   3.10252829e+10  3.10226623e+10  1.58e-04 1.17e-10  8.16e-01    95s\n", "  29   3.10248911e+10  3.10236843e+10  9.15e-05 9.31e-10  3.76e-01    97s\n", "  30   3.10246682e+10  3.10239877e+10  5.48e-05 9.31e-10  2.12e-01    98s\n", "  31   3.10244797e+10  3.10241891e+10  2.41e-05 2.33e-10  9.04e-02   101s\n", "  32   3.10243983e+10  3.10242636e+10  1.07e-05 9.31e-10  4.19e-02   105s\n", "  33   3.10243636e+10  3.10242883e+10  4.98e-06 9.31e-10  2.34e-02   108s\n", "  34   3.10243594e+10  3.10242952e+10  4.31e-06 9.31e-10  2.00e-02   110s\n", "  35   3.10243516e+10  3.10242991e+10  3.01e-06 9.31e-10  1.63e-02   112s\n", "  36   3.10243477e+10  3.10243202e+10  2.37e-06 9.31e-10  8.54e-03   115s\n", "  37   3.10243345e+10  3.10243316e+10  2.44e-07 8.93e-12  9.14e-04   119s\n", "  38   3.10243332e+10  3.10243322e+10  9.13e-08 9.31e-10  3.10e-04   123s\n", "  39   3.10243328e+10  3.10243324e+10  3.36e-08 9.31e-10  1.11e-04   127s\n", "  40   3.10243327e+10  3.10243326e+10  1.42e-07 7.82e-12  5.30e-05   130s\n", "  41   3.10243327e+10  3.10243326e+10  5.14e-08 4.66e-10  3.53e-05   133s\n", "\n", "<PERSON>ier solved model in 41 iterations and 132.96 seconds (68.07 work units)\n", "Optimal objective 3.10243327e+10\n", "\n", "\n", "Root crossover log...\n", "\n", "  147901 DPushes remaining with DInf 1.9368253e-03               134s\n", "    7430 DPushes remaining with DInf 0.0000000e+00               135s\n", "       0 DPushes remaining with DInf 0.0000000e+00               137s\n", "Warning: Markowitz tolerance tightened to 0.5\n", "\n", "  475191 PPushes remaining with PInf 7.6542438e-03               137s\n", "  344801 PPushes remaining with PInf 5.5026484e-02               140s\n", "  325248 PPushes remaining with PInf 3.9956070e-01               145s\n", "  263291 PPushes remaining with PInf 2.1603658e-02               150s\n", "  230763 PPushes remaining with PInf 2.0388788e-02               155s\n", "  194663 PPushes remaining with PInf 1.9848985e-02               160s\n", "  167458 PPushes remaining with PInf 1.9346277e-02               165s\n", "  154777 PPushes remaining with PInf 1.9346279e-02               170s\n", "  134769 PPushes remaining with PInf 1.9371551e-02               175s\n", "  117742 PPushes remaining with PInf 1.9043420e-02               180s\n", "  105929 PPushes remaining with PInf 1.9024726e-02               185s\n", "   94101 PPushes remaining with PInf 1.8998762e-02               190s\n", "   84252 PPushes remaining with PInf 4.3064853e-05               195s\n", "   82072 PPushes remaining with PInf 4.1342482e-05               200s\n", "   73211 PPushes remaining with PInf 0.0000000e+00               205s\n", "   60834 PPushes remaining with PInf 0.0000000e+00               210s\n", "   48360 PPushes remaining with PInf 0.0000000e+00               215s\n", "   35041 PPushes remaining with PInf 0.0000000e+00               220s\n", "   21387 PPushes remaining with PInf 0.0000000e+00               225s\n", "    7876 PPushes remaining with PInf 0.0000000e+00               230s\n", "       0 PPushes remaining with PInf 0.0000000e+00               233s\n", "\n", "  Push phase complete: Pinf 0.0000000e+00, Dinf 4.0619032e+01    233s\n", "\n", "\n", "Root simplex log...\n", "\n", "Iteration    Objective       Primal Inf.    Dual Inf.      Time\n", "  590855    3.1024333e+10   0.000000e+00   4.061903e+01    234s\n", "  590859    3.1024333e+10   0.000000e+00   0.000000e+00    234s\n", "Concurrent spin time: 20.95s\n", "\n", "Solved with barrier\n", "  590881    3.1024333e+10   0.000000e+00   0.000000e+00    257s\n", "Extra simplex iterations after uncrush: 22\n", "\n", "Root relaxation: objective 3.102433e+10, 590881 iterations, 235.22 seconds (86.68 work units)\n", "Total elapsed time = 473.94s (<PERSON>gen<PERSON>oves)\n", "Total elapsed time = 494.44s (<PERSON>genMoves)\n", "Total elapsed time = 507.47s (<PERSON>genMoves)\n", "Total elapsed time = 516.31s (<PERSON>gen<PERSON>oves)\n", "\n", "    Nodes    |    Current Node    |     Objective Bounds      |     Work\n", " Expl Unexpl |  Obj  Depth IntInf | Incumbent    BestBd   Gap | It/Node Time\n", "\n", "     0     0 3.1024e+10    0 1092          - 3.1024e+10      -     -  518s\n", "H    0     0                    3.524474e+12 3.1024e+10  99.1%     -  673s\n", "     0     0 3.1026e+10    0 1264 3.5245e+12 3.1026e+10  99.1%     -  745s\n", "H    0     0                    3.103371e+10 3.1026e+10  0.03%     -  827s\n", "\n", "Cutting planes:\n", "  Gomory: 31\n", "  MIR: 905\n", "  Flow cover: 834\n", "  Relax-and-lift: 5\n", "\n", "Explored 1 nodes (766696 simplex iterations) in 827.53 seconds (512.66 work units)\n", "Thread count was 8 (of 8 available processors)\n", "\n", "Solution count 2: 3.10337e+10 3.52447e+12 \n", "\n", "Optimal solution found (tolerance 1.00e-03)\n", "Best objective 3.103370910039e+10, best bound 3.102556381367e+10, gap 0.0262%\n", "月度_6找到最优解\n", "目标值 (总成本): 31,033,709,100.39\n", "月度_6结果已保存\n", "\n", "求解月度_7...\n", "Set parameter TimeLimit to value 10800\n", "Set parameter MIPGap to value 0.001\n", "Gurobi Optimizer version 12.0.0 build v12.0.0rc1 (mac64[arm] - Darwin 24.1.0 24B83)\n", "\n", "CPU model: Apple M3\n", "Thread count: 8 physical cores, 8 logical processors, using up to 8 threads\n", "\n", "Non-default parameters:\n", "TimeLimit  10800\n", "MIPGap  0.001\n", "\n", "Optimize a model with 4214082 rows, 2999064 columns and 10798305 nonzeros\n", "Model fingerprint: 0x4acea7d9\n", "Model has 278256 simple general constraints\n", "  278256 ABS\n", "Variable types: 2961120 continuous, 37944 integer (37944 binary)\n", "Coefficient statistics:\n", "  Matrix range     [2e-03, 5e+04]\n", "  Objective range  [1e-01, 1e+03]\n", "  Bounds range     [1e+00, 1e+00]\n", "  RHS range        [5e-03, 2e+07]\n", "Presolve removed 3255284 rows and 1757983 columns (presolve time = 5s)...\n", "Presolve removed 3272532 rows and 1771128 columns (presolve time = 10s)...\n", "Presolve removed 3285294 rows and 1783878 columns (presolve time = 15s)...\n", "Presolve removed 2978835 rows and 1791267 columns\n", "Presolve time: 17.96s\n", "Presolved: 1235247 rows, 1207915 columns, 4817401 nonzeros\n", "Variable types: 1170763 continuous, 37152 integer (37152 binary)\n", "Deterministic concurrent LP optimizer: primal simplex, dual simplex, and barrier\n", "Showing barrier log only...\n", "\n", "Root barrier log...\n", "\n", "Elapsed ordering time = 5s\n", "Ordering time: 12.30s\n", "\n", "Barrier statistics:\n", " AA' NZ     : 4.850e+06\n", " Factor NZ  : 3.656e+07 (roughly 1.2 GB of memory)\n", " Factor Ops : 1.703e+10 (less than 1 second per iteration)\n", " Threads    : 5\n", "\n", "                  Objective                Residual\n", "Iter       Primal          Dual         Primal    Dual     Compl     Time\n", "   0  -5.90477024e+14 -9.63000470e+15  5.78e+09 5.73e+03  1.54e+12    40s\n", "   1  -7.91297324e+13 -8.51160643e+15  1.20e+09 6.71e-09  3.17e+11    41s\n", "   2  -7.62342361e+11 -6.62191328e+15  6.69e+07 9.44e-07  1.95e+10    43s\n", "   3   1.97095152e+12 -3.89561879e+15  5.40e+06 4.97e-07  2.49e+09    44s\n", "   4   2.16157262e+12 -1.39031985e+15  1.57e+06 1.23e-07  7.14e+08    47s\n", "   5   2.18568853e+12 -5.99005456e+14  1.93e+05 5.77e-08  2.08e+08    49s\n", "   6   2.12258221e+12 -2.18225358e+14  5.94e+04 2.61e-08  7.13e+07    50s\n", "   7   1.62477116e+12 -5.11902270e+13  5.68e+03 1.30e-08  1.61e+07    51s\n", "   8   1.13742806e+12 -1.26482132e+13  1.70e+03 7.45e-09  4.17e+06    53s\n", "   9   5.36912101e+11 -5.63549738e+12  4.81e+02 9.31e-09  1.86e+06    54s\n", "  10   3.54119274e+11 -2.12732834e+12  2.19e+02 7.45e-09  7.48e+05    56s\n", "  11   1.13432747e+11 -3.70050633e+11  8.07e+00 1.12e-08  1.46e+05    57s\n", "  12   6.78625489e+10 -2.29582658e+11  2.99e+00 7.45e-09  8.96e+04    59s\n", "  13   4.54511598e+10 -8.14177074e+10  9.81e-01 7.45e-09  3.82e+04    61s\n", "  14   3.94317207e+10 -3.81292470e+10  5.03e-01 5.59e-09  2.34e+04    63s\n", "  15   3.81825520e+10 -2.72160464e+10  3.98e-01 5.59e-09  1.97e+04    64s\n", "  16   3.60270512e+10  2.71286615e+09  2.21e-01 1.49e-08  1.00e+04    66s\n", "  17   3.44777026e+10  1.52007407e+10  8.95e-02 1.49e-08  5.81e+03    68s\n", "  18   3.39235492e+10  2.60640053e+10  5.65e-02 1.40e-09  2.37e+03    69s\n", "  19   3.35917893e+10  2.86374385e+10  4.25e-02 9.31e-10  1.49e+03    70s\n", "  20   3.30874282e+10  3.04553740e+10  1.78e-02 4.66e-10  7.93e+02    72s\n", "  21   3.29084467e+10  3.14958772e+10  1.01e-02 3.49e-10  4.25e+02    74s\n", "  22   3.28146257e+10  3.21831234e+10  6.35e-03 9.31e-10  1.90e+02    75s\n", "  23   3.27616617e+10  3.23812281e+10  4.34e-03 9.31e-10  1.15e+02    76s\n", "  24   3.27025467e+10  3.25061177e+10  2.16e-03 9.31e-10  5.92e+01    78s\n", "  25   3.26745721e+10  3.25818590e+10  1.16e-03 4.75e-10  2.79e+01    80s\n", "  26   3.26547760e+10  3.26088198e+10  5.09e-04 9.31e-10  1.38e+01    81s\n", "  27   3.26443103e+10  3.26262207e+10  1.96e-04 5.52e-10  5.45e+00    84s\n", "  28   3.26409067e+10  3.26307188e+10  1.00e-04 4.32e-10  3.07e+00    85s\n", "  29   3.26394285e+10  3.26335104e+10  6.03e-05 3.04e-10  1.78e+00    86s\n", "  30   3.26382088e+10  3.26353664e+10  6.18e-05 1.92e-10  8.56e-01    88s\n", "  31   3.26376444e+10  3.26363121e+10  5.88e-05 1.10e-10  4.01e-01    89s\n", "  32   3.26374626e+10  3.26366575e+10  4.97e-05 4.66e-10  2.42e-01    91s\n", "  33   3.26373674e+10  3.26367834e+10  7.22e-05 5.72e-11  1.76e-01    92s\n", "  34   3.26372781e+10  3.26369160e+10  4.62e-05 9.31e-10  1.09e-01    93s\n", "  35   3.26372299e+10  3.26370060e+10  4.58e-05 2.52e-11  6.74e-02    95s\n", "  36   3.26371910e+10  3.26370861e+10  6.32e-05 9.31e-10  3.16e-02    97s\n", "  37   3.26371588e+10  3.26371210e+10  8.41e-05 1.12e-11  1.14e-02    98s\n", "  38   3.26371452e+10  3.26371397e+10  7.49e-06 1.46e-11  1.65e-03   100s\n", "  39   3.26371436e+10  3.26371435e+10  5.65e-08 9.31e-10  3.87e-05   103s\n", "  40   3.26371435e+10  3.26371435e+10  1.71e-07 9.31e-10  2.45e-07   105s\n", "  41   3.26371435e+10  3.26371435e+10  2.70e-07 2.91e-11  5.58e-09   107s\n", "\n", "<PERSON><PERSON> solved model in 41 iterations and 107.18 seconds (68.87 work units)\n", "Optimal objective 3.26371435e+10\n", "\n", "\n", "Root crossover log...\n", "\n", "  112777 DPushes remaining with DInf 0.0000000e+00               109s\n", "    7412 DPushes remaining with DInf 0.0000000e+00               110s\n", "       0 DPushes remaining with DInf 0.0000000e+00               112s\n", "Warning: Markowitz tolerance tightened to 0.5\n", "\n", "  297739 PPushes remaining with PInf 0.0000000e+00               112s\n", "  280517 PPushes remaining with PInf 0.0000000e+00               115s\n", "  235411 PPushes remaining with PInf 0.0000000e+00               120s\n", "  175284 PPushes remaining with PInf 0.0000000e+00               125s\n", "  144531 PPushes remaining with PInf 0.0000000e+00               130s\n", "  114620 PPushes remaining with PInf 0.0000000e+00               135s\n", "   89085 PPushes remaining with PInf 0.0000000e+00               140s\n", "   67135 PPushes remaining with PInf 0.0000000e+00               145s\n", "   48779 PPushes remaining with PInf 0.0000000e+00               150s\n", "   30428 PPushes remaining with PInf 0.0000000e+00               155s\n", "   11309 PPushes remaining with PInf 0.0000000e+00               160s\n", "       0 PPushes remaining with PInf 0.0000000e+00               163s\n", "\n", "  Push phase complete: Pinf 0.0000000e+00, Dinf 3.5902882e-08    164s\n", "\n", "\n", "Root simplex log...\n", "\n", "Iteration    Objective       Primal Inf.    Dual Inf.      Time\n", "  371549    3.2637144e+10   0.000000e+00   0.000000e+00    164s\n", "Concurrent spin time: 15.74s (can be avoided by choosing Method=3)\n", "\n", "Solved with barrier\n", "  371549    3.2637144e+10   0.000000e+00   8.099443e+04    180s\n", "  371570    3.2637144e+10   0.000000e+00   0.000000e+00    181s\n", "Extra simplex iterations after uncrush: 21\n", "\n", "Root relaxation: objective 3.263714e+10, 371570 iterations, 161.18 seconds (82.39 work units)\n", "Total elapsed time = 283.94s (<PERSON>gen<PERSON>oves)\n", "Total elapsed time = 295.30s (DegenMoves)\n", "Total elapsed time = 305.02s (DegenMoves)\n", "Total elapsed time = 312.34s (DegenMoves)\n", "\n", "    Nodes    |    Current Node    |     Objective Bounds      |     Work\n", " Expl Unexpl |  Obj  Depth IntInf | Incumbent    BestBd   Gap | It/Node Time\n", "\n", "     0     0 3.2637e+10    0  883          - 3.2637e+10      -     -  319s\n", "H    0     0                    3.649748e+12 3.2637e+10  99.1%     -  439s\n", "     0     0 3.2639e+10    0  883 3.6497e+12 3.2639e+10  99.1%     -  496s\n", "     0     0 3.2639e+10    0  842 3.6497e+12 3.2639e+10  99.1%     -  621s\n", "     0     0 3.2639e+10    0  833 3.6497e+12 3.2639e+10  99.1%     -  650s\n", "     0     0 3.2639e+10    0  765 3.6497e+12 3.2639e+10  99.1%     -  703s\n", "     0     0 3.2639e+10    0  741 3.6497e+12 3.2639e+10  99.1%     -  739s\n", "     0     0 3.2639e+10    0  728 3.6497e+12 3.2639e+10  99.1%     -  763s\n", "     0     0 3.2639e+10    0  738 3.6497e+12 3.2639e+10  99.1%     -  779s\n", "     0     0 3.2639e+10    0  809 3.6497e+12 3.2639e+10  99.1%     -  900s\n", "     0     0 3.2639e+10    0  791 3.6497e+12 3.2639e+10  99.1%     - 1144s\n", "     0     0 3.2639e+10    0  785 3.6497e+12 3.2639e+10  99.1%     - 1202s\n", "     0     0 3.2639e+10    0  779 3.6497e+12 3.2639e+10  99.1%     - 1216s\n", "     0     0 3.2640e+10    0  773 3.6497e+12 3.2640e+10  99.1%     - 1259s\n", "     0     0 3.2640e+10    0  762 3.6497e+12 3.2640e+10  99.1%     - 1296s\n", "     0     0 3.2640e+10    0  766 3.6497e+12 3.2640e+10  99.1%     - 1333s\n", "     0     0 3.2640e+10    0  763 3.6497e+12 3.2640e+10  99.1%     - 1360s\n", "     0     0 3.2640e+10    0  747 3.6497e+12 3.2640e+10  99.1%     - 1381s\n", "     0     0 3.2640e+10    0  758 3.6497e+12 3.2640e+10  99.1%     - 1400s\n", "     0     0 3.2640e+10    0  751 3.6497e+12 3.2640e+10  99.1%     - 1444s\n", "     0     0 3.2640e+10    0  553 3.6497e+12 3.2640e+10  99.1%     - 1514s\n", "     0     2 3.2640e+10    0  544 3.6497e+12 3.2640e+10  99.1%     - 2089s\n", "     1     4 3.2640e+10    1  544 3.6497e+12 3.2640e+10  99.1%   660 2091s\n", "     3     7 3.2640e+10    2  556 3.6497e+12 3.2640e+10  99.1%  2178 2125s\n", "     6    14 3.2640e+10    3  554 3.6497e+12 3.2640e+10  99.1%  3295 2169s\n", "    13    22 3.2640e+10    4  552 3.6497e+12 3.2640e+10  99.1%  2741 2180s\n", "    21    76 3.2640e+10    5  552 3.6497e+12 3.2640e+10  99.1%  1851 2239s\n", "    75   120 3.2640e+10   11  552 3.6497e+12 3.2640e+10  99.1%   540 2325s\n", "   119   128 3.2640e+10   12  552 3.6497e+12 3.2640e+10  99.1%   341 2508s\n", "   127   172 3.2640e+10   13  552 3.6497e+12 3.2640e+10  99.1%   319 2639s\n", "   171   192 3.2640e+10   14  552 3.6497e+12 3.2640e+10  99.1%   238 2864s\n", "   191   259 3.2640e+10   14  551 3.6497e+12 3.2640e+10  99.1%   213 3019s\n", "H  258   327                    1.635148e+12 3.2640e+10  98.0%   158 3166s\n", "H  272   327                    1.616929e+12 3.2640e+10  98.0%   152 3167s\n", "H  312   327                    1.542867e+12 3.2640e+10  97.9%   134 3168s\n", "   326   373 3.2640e+10   34  549 1.5429e+12 3.2640e+10  97.9%   128 3441s\n", "   372   462 3.2640e+10   34  548 1.5429e+12 3.2640e+10  97.9%   113 3556s\n", "   461   554 3.2640e+10   46  548 1.5429e+12 3.2640e+10  97.9%  98.4 3643s\n", "   553   606 3.2641e+10   56  544 1.5429e+12 3.2640e+10  97.9%  87.9 3775s\n", "H  554   606                    1.077412e+12 3.2640e+10  97.0%  87.7 3776s\n", "H  567   606                    1.011865e+12 3.2640e+10  96.8%  85.7 3777s\n", "   605   710 3.2641e+10   57  544 1.0119e+12 3.2640e+10  96.8%  80.5 3799s\n", "   709   814 3.2641e+10   68  540 1.0119e+12 3.2640e+10  96.8%  71.0 3820s\n", "   813   918 3.2641e+10   78  540 1.0119e+12 3.2640e+10  96.8%  63.9 3845s\n", "   917  1022 3.2641e+10   90  536 1.0119e+12 3.2640e+10  96.8%  59.7 3866s\n", "  1021  1116 3.2641e+10  102  533 1.0119e+12 3.2640e+10  96.8%  55.9 3908s\n", "  1115  1186 3.2641e+10  113  527 1.0119e+12 3.2640e+10  96.8%  58.0 3972s\n", "  1185  1265 3.2641e+10  114  525 1.0119e+12 3.2640e+10  96.8%  58.5 4030s\n", "  1264  1273 3.2641e+10  118  527 1.0119e+12 3.2640e+10  96.8%  62.2 4147s\n", "  1272  1281 3.2641e+10  119  527 1.0119e+12 3.2640e+10  96.8%  62.0 4240s\n", "  1280  1289 3.2641e+10  119  525 1.0119e+12 3.2640e+10  96.8%  65.8 4362s\n", "  1288  1297 3.2641e+10  120  526 1.0119e+12 3.2640e+10  96.8%  66.2 4476s\n", "  1296  1305 3.2641e+10  121  526 1.0119e+12 3.2640e+10  96.8%  66.8 4585s\n", "  1304  1312 3.2641e+10  122  525 1.0119e+12 3.2640e+10  96.8%  67.8 4691s\n", "  1311  1320 3.2641e+10  122  524 1.0119e+12 3.2640e+10  96.8%  70.6 4830s\n", "  1319  1328 3.2641e+10  123  521 1.0119e+12 3.2640e+10  96.8%  78.3 4940s\n", "  1327  1336 3.2641e+10  123  523 1.0119e+12 3.2640e+10  96.8%  82.6 5052s\n", "  1335  1344 3.2641e+10  124  521 1.0119e+12 3.2640e+10  96.8%  84.0 5170s\n", "  1343  1352 3.2641e+10  125  521 1.0119e+12 3.2640e+10  96.8%  85.3 5316s\n", "  1351  1360 3.2641e+10  126  520 1.0119e+12 3.2640e+10  96.8%  86.8 5445s\n", "  1359  1368 3.2641e+10  127  520 1.0119e+12 3.2640e+10  96.8%  87.8 5588s\n", "  1367  1376 3.2641e+10  128  519 1.0119e+12 3.2640e+10  96.8%  88.3 5732s\n", "  1375  1384 3.2641e+10  128  517 1.0119e+12 3.2640e+10  96.8%  88.5 5971s\n", "H 1377  1384                    8.298132e+11 3.2640e+10  96.1%  88.4 5973s\n", "  1383  1460 3.2641e+10  129  519 8.2981e+11 3.2640e+10  96.1%  89.2 6109s\n", "  1459  1467 3.2641e+10  134  519 8.2981e+11 3.2640e+10  96.1%   105 6322s\n", "  1466  1483 3.2641e+10  134  517 8.2981e+11 3.2640e+10  96.1%   107 6469s\n", "  1482  1496 3.2641e+10  135  517 8.2981e+11 3.2640e+10  96.1%   111 6599s\n", "  1495  1512 3.2641e+10  135  518 8.2981e+11 3.2640e+10  96.1%   120 6717s\n", "  1511  1543 3.2641e+10  141  509 8.2981e+11 3.2640e+10  96.1%   125 6824s\n", "  1542  1551 3.2641e+10  147  502 8.2981e+11 3.2640e+10  96.1%   132 6985s\n", "  1550  1559 3.2641e+10  148  502 8.2981e+11 3.2640e+10  96.1%   132 7118s\n", "  1558  1567 3.2641e+10  148  502 8.2981e+11 3.2640e+10  96.1%   137 7283s\n", "  1566  1575 3.2641e+10  149  502 8.2981e+11 3.2640e+10  96.1%   144 7434s\n", "  1574  1584 3.2641e+10  150  502 8.2981e+11 3.2640e+10  96.1%   149 7559s\n", "  1583  1591 3.2641e+10  152  503 8.2981e+11 3.2640e+10  96.1%   155 7676s\n", "  1590  1598 3.2641e+10  164  498 8.2981e+11 3.2640e+10  96.1%   160 7793s\n", "  1597  1606 3.2641e+10  153  501 8.2981e+11 3.2640e+10  96.1%   163 7894s\n", "  1605  1614 3.2641e+10  154  498 8.2981e+11 3.2640e+10  96.1%   169 7997s\n", "  1613  1622 3.2641e+10  154  497 8.2981e+11 3.2640e+10  96.1%   171 8100s\n", "  1621  1630 3.2641e+10  155  497 8.2981e+11 3.2640e+10  96.1%   177 8212s\n", "  1629  1638 3.2641e+10  155  496 8.2981e+11 3.2640e+10  96.1%   182 8382s\n", "H 1634  1638                    8.132867e+11 3.2640e+10  96.0%   185 8382s\n", "  1637  1651 3.2641e+10  156  495 8.1329e+11 3.2640e+10  96.0%   186 8516s\n", "  1650  1696 3.2641e+10  158  496 8.1329e+11 3.2640e+10  96.0%   200 8620s\n", "  1695  1704 3.2641e+10  159  496 8.1329e+11 3.2640e+10  96.0%   207 8750s\n", "  1703  1714 3.2641e+10  159  493 8.1329e+11 3.2640e+10  96.0%   209 8860s\n", "  1713  1747 3.2641e+10  160  496 8.1329e+11 3.2640e+10  96.0%   216 8969s\n", "  1746  1761 3.2641e+10  161  494 8.1329e+11 3.2640e+10  96.0%   224 9096s\n", "  1760  1793 3.2641e+10  161  495 8.1329e+11 3.2640e+10  96.0%   229 9198s\n", "  1792  1801 3.2641e+10  165  485 8.1329e+11 3.2640e+10  96.0%   237 9332s\n", "  1800  1838 3.2641e+10  165  486 8.1329e+11 3.2640e+10  96.0%   241 9456s\n", "  1837  1858 3.2641e+10  172  476 8.1329e+11 3.2640e+10  96.0%   248 9580s\n", "H 1857  1866                    6.755491e+11 3.2640e+10  95.2%   248 9823s\n", "  1865  1942 3.2641e+10  181  472 6.7555e+11 3.2640e+10  95.2%   249 9920s\n", "  1941  1950 3.2641e+10  183  468 6.7555e+11 3.2640e+10  95.2%   261 10152s\n", "  1949  1970 3.2641e+10  184  467 6.7555e+11 3.2640e+10  95.2%   265 10304s\n", "  1969  1984 3.2641e+10  192  461 6.7555e+11 3.2640e+10  95.2%   269 10472s\n", "  1983  2021 3.2641e+10  196  461 6.7555e+11 3.2640e+10  95.2%   273 10632s\n", "  2020  2029 3.2641e+10  199  464 6.7555e+11 3.2640e+10  95.2%   276 10800s\n", "\n", "Cutting planes:\n", "  Gomory: 130\n", "  Implied bound: 1\n", "  MIR: 2479\n", "  Flow cover: 943\n", "  RLT: 1\n", "  Relax-and-lift: 18\n", "\n", "Explored 2028 nodes (1498535 simplex iterations) in 10801.75 seconds (5585.77 work units)\n", "Thread count was 8 (of 8 available processors)\n", "\n", "Solution count 9: 6.75549e+11 8.13287e+11 8.29813e+11 ... 3.64975e+12\n", "\n", "Time limit reached\n", "Best objective 6.755491048642e+11, best bound 3.263950776551e+10, gap 95.1684%\n", "\n", "求解月度_8...\n", "Set parameter TimeLimit to value 10800\n", "Set parameter MIPGap to value 0.001\n", "Gurobi Optimizer version 12.0.0 build v12.0.0rc1 (mac64[arm] - Darwin 24.1.0 24B83)\n", "\n", "CPU model: Apple M3\n", "Thread count: 8 physical cores, 8 logical processors, using up to 8 threads\n", "\n", "Non-default parameters:\n", "TimeLimit  10800\n", "MIPGap  0.001\n", "\n", "Optimize a model with 4214082 rows, 2999064 columns and 10798305 nonzeros\n", "Model fingerprint: 0xb47d98bb\n", "Model has 278256 simple general constraints\n", "  278256 ABS\n", "Variable types: 2961120 continuous, 37944 integer (37944 binary)\n", "Coefficient statistics:\n", "  Matrix range     [2e-03, 5e+04]\n", "  Objective range  [1e-01, 1e+03]\n", "  Bounds range     [1e+00, 1e+00]\n", "  RHS range        [5e-03, 2e+07]\n", "Presolve removed 3255249 rows and 1758275 columns (presolve time = 5s)...\n", "Presolve removed 3281273 rows and 1780000 columns (presolve time = 10s)...\n", "Presolve removed 2978797 rows and 1791373 columns\n", "Presolve time: 13.88s\n", "Presolved: 1235285 rows, 1207809 columns, 4818059 nonzeros\n", "Variable types: 1170657 continuous, 37152 integer (37152 binary)\n", "Deterministic concurrent LP optimizer: primal simplex, dual simplex, and barrier\n", "Showing barrier log only...\n", "\n", "Root barrier log...\n", "\n", "Ordering time: 7.71s\n", "\n", "Barrier statistics:\n", " AA' NZ     : 4.851e+06\n", " Factor NZ  : 3.570e+07 (roughly 1.2 GB of memory)\n", " Factor Ops : 1.444e+10 (less than 1 second per iteration)\n", " Threads    : 5\n", "\n", "                  Objective                Residual\n", "Iter       Primal          Dual         Primal    Dual     Compl     Time\n", "   0  -6.55572047e+14 -9.54132278e+15  6.31e+09 5.74e+03  1.68e+12    28s\n", "   1  -8.96005077e+13 -8.51126024e+15  1.31e+09 8.96e+05  3.53e+11    29s\n", "   2  -1.72616852e+12 -6.65520364e+15  8.04e+07 3.95e-07  2.34e+10    30s\n", "   3   1.69098364e+12 -5.15568730e+15  9.53e+06 1.92e-07  4.02e+09    31s\n", "   4   2.03417382e+12 -2.40193384e+15  2.98e+06 1.29e-07  1.38e+09    32s\n", "   5   2.15886482e+12 -6.32512602e+14  2.59e+05 4.28e-08  2.30e+08    33s\n", "   6   1.93941475e+12 -2.90903207e+14  2.67e+04 1.73e-08  9.09e+07    34s\n", "   7   1.61774029e+12 -1.12948130e+14  5.36e+03 7.64e-09  3.48e+07    35s\n", "   8   1.20723812e+12 -2.26183178e+13  8.18e+02 7.45e-09  7.19e+06    36s\n", "   9   6.81278876e+11 -5.49920239e+12  1.81e+02 5.59e-09  1.86e+06    36s\n", "  10   2.31915141e+11 -1.20744331e+12  2.57e+01 1.12e-08  4.34e+05    37s\n", "  11   7.93939918e+10 -2.03282788e+11  4.28e+00 1.49e-08  8.51e+04    38s\n", "  12   5.03746996e+10 -6.31292789e+10  1.51e+00 7.45e-09  3.42e+04    39s\n", "  13   4.57733046e+10 -6.03408927e+10  1.08e+00 2.56e-02  3.20e+04    40s\n", "  14   4.35388552e+10 -1.77587258e+10  8.66e-01 5.59e-09  1.85e+04    41s\n", "  15   3.83626372e+10 -7.64140922e+09  3.64e-01 4.66e-09  1.39e+04    42s\n", "  16   3.61913334e+10 -2.12004494e+09  1.66e-01 3.73e-09  1.15e+04    42s\n", "  17   3.51466555e+10  2.32446581e+10  8.58e-02 2.33e-09  3.58e+03    43s\n", "  18   3.38345148e+10  2.99305677e+10  2.24e-02 1.49e-08  1.18e+03    45s\n", "  19   3.37125076e+10  3.02731767e+10  1.70e-02 2.24e-08  1.04e+03    46s\n", "  20   3.36569967e+10  3.15628683e+10  1.50e-02 7.45e-09  6.31e+02    46s\n", "  21   3.35572849e+10  3.21823794e+10  1.12e-02 9.31e-10  4.14e+02    47s\n", "  22   3.34061393e+10  3.25893898e+10  5.78e-03 9.31e-10  2.46e+02    48s\n", "  23   3.33314721e+10  3.29587535e+10  3.26e-03 9.31e-10  1.12e+02    49s\n", "  24   3.32830469e+10  3.30092807e+10  1.69e-03 1.16e-10  8.25e+01    49s\n", "  25   3.32573122e+10  3.31466698e+10  8.81e-04 9.31e-10  3.33e+01    50s\n", "  26   3.32441320e+10  3.31850893e+10  5.17e-04 9.31e-10  1.78e+01    51s\n", "  27   3.32301521e+10  3.32060706e+10  1.58e-04 9.31e-10  7.25e+00    52s\n", "  28   3.32268495e+10  3.32132436e+10  8.19e-05 9.31e-10  4.10e+00    53s\n", "  29   3.32246737e+10  3.32172755e+10  3.50e-05 9.31e-10  2.23e+00    53s\n", "  30   3.32243167e+10  3.32197518e+10  2.76e-05 2.33e-10  1.37e+00    54s\n", "  31   3.32238182e+10  3.32209633e+10  3.24e-05 9.31e-10  8.60e-01    55s\n", "  32   3.32231987e+10  3.32217888e+10  3.02e-05 4.51e-11  4.25e-01    56s\n", "  33   3.32231258e+10  3.32220273e+10  4.50e-05 3.72e-11  3.31e-01    57s\n", "  34   3.32230720e+10  3.32223556e+10  4.58e-05 2.91e-11  2.16e-01    58s\n", "  35   3.32230030e+10  3.32227553e+10  6.19e-05 9.31e-10  7.46e-02    58s\n", "  36   3.32229754e+10  3.32229440e+10  6.07e-05 6.37e-12  9.45e-03    59s\n", "  37   3.32229713e+10  3.32229669e+10  9.39e-06 9.31e-10  1.33e-03    61s\n", "  38   3.32229706e+10  3.32229699e+10  2.09e-06 9.31e-10  2.15e-04    62s\n", "  39   3.32229704e+10  3.32229704e+10  8.94e-08 4.66e-10  3.71e-06    63s\n", "  40   3.32229704e+10  3.32229704e+10  2.62e-07 9.31e-10  3.72e-09    64s\n", "\n", "<PERSON><PERSON> solved model in 40 iterations and 64.20 seconds (64.14 work units)\n", "Optimal objective 3.32229704e+10\n", "\n", "\n", "Root crossover log...\n", "\n", "  705788 variables added to crossover basis                       65s\n", "\n", "  114218 DPushes remaining with DInf 0.0000000e+00                66s\n", "       0 DPushes remaining with DInf 0.0000000e+00                67s\n", "Warning: Markowitz tolerance tightened to 0.5\n", "\n", "  299389 PPushes remaining with PInf 0.0000000e+00                67s\n", "  265420 PPushes remaining with PInf 0.0000000e+00                70s\n", "  190889 PPushes remaining with PInf 0.0000000e+00                75s\n", "  166860 PPushes remaining with PInf 0.0000000e+00                80s\n", "  146232 PPushes remaining with PInf 0.0000000e+00                85s\n", "  125094 PPushes remaining with PInf 0.0000000e+00                90s\n", "  109644 PPushes remaining with PInf 0.0000000e+00                95s\n", "   95049 PPushes remaining with PInf 0.0000000e+00               100s\n", "   78138 PPushes remaining with PInf 0.0000000e+00               105s\n", "   61993 PPushes remaining with PInf 0.0000000e+00               110s\n", "   46452 PPushes remaining with PInf 0.0000000e+00               115s\n", "   30735 PPushes remaining with PInf 0.0000000e+00               120s\n", "   15911 PPushes remaining with PInf 0.0000000e+00               125s\n", "       0 PPushes remaining with PInf 0.0000000e+00               130s\n", "\n", "  Push phase complete: Pinf 0.0000000e+00, Dinf 2.2163461e-08    130s\n", "\n", "\n", "Root simplex log...\n", "\n", "Iteration    Objective       Primal Inf.    Dual Inf.      Time\n", "  371691    3.3222970e+10   0.000000e+00   0.000000e+00    131s\n", "Concurrent spin time: 16.54s (can be avoided by choosing Method=3)\n", "\n", "Solved with barrier\n", "  371709    3.3222970e+10   0.000000e+00   0.000000e+00    148s\n", "Extra simplex iterations after uncrush: 18\n", "\n", "Root relaxation: objective 3.322297e+10, 371709 iterations, 133.45 seconds (80.36 work units)\n", "Total elapsed time = 475.11s (<PERSON>genMoves)\n", "Total elapsed time = 505.48s (<PERSON>genMoves)\n", "Total elapsed time = 523.26s (<PERSON>gen<PERSON>oves)\n", "Total elapsed time = 537.90s (DegenMoves)\n", "\n", "    Nodes    |    Current Node    |     Objective Bounds      |     Work\n", " Expl Unexpl |  Obj  Depth IntInf | Incumbent    BestBd   Gap | It/Node Time\n", "\n", "     0     0 3.3223e+10    0 1585          - 3.3223e+10      -     -  551s\n", "H    0     0                    3.633565e+12 3.3223e+10  99.1%     -  692s\n", "     0     0 3.3224e+10    0 1959 3.6336e+12 3.3224e+10  99.1%     -  800s\n", "     0     0 3.3224e+10    0 1904 3.6336e+12 3.3224e+10  99.1%     - 1188s\n", "     0     0 3.3224e+10    0 1902 3.6336e+12 3.3224e+10  99.1%     - 1204s\n", "     0     0 3.3225e+10    0 1772 3.6336e+12 3.3225e+10  99.1%     - 1278s\n", "H    0     0                    3.338010e+10 3.3225e+10  0.47%     - 1509s\n", "     0     0 3.3225e+10    0 1711 3.3380e+10 3.3225e+10  0.47%     - 1558s\n", "     0     0 3.3225e+10    0 1705 3.3380e+10 3.3225e+10  0.46%     - 1583s\n", "     0     0 3.3225e+10    0 1713 3.3380e+10 3.3225e+10  0.46%     - 1600s\n", "     0     0 3.3225e+10    0 1718 3.3380e+10 3.3225e+10  0.46%     - 1617s\n", "     0     0 3.3225e+10    0 1555 3.3380e+10 3.3225e+10  0.46%     - 1731s\n", "H    0     0                    3.323824e+10 3.3225e+10  0.04%     - 1898s\n", "\n", "Cutting planes:\n", "  Gomory: 121\n", "  MIR: 4049\n", "  Flow cover: 1526\n", "  RLT: 1\n", "  Relax-and-lift: 17\n", "\n", "Explored 1 nodes (823731 simplex iterations) in 1899.31 seconds (1551.46 work units)\n", "Thread count was 8 (of 8 available processors)\n", "\n", "Solution count 2: 3.32382e+10 3.33801e+10 \n", "\n", "Optimal solution found (tolerance 1.00e-03)\n", "Best objective 3.323824312252e+10, best bound 3.322510285051e+10, gap 0.0395%\n", "月度_8找到最优解\n", "目标值 (总成本): 33,238,243,122.52\n", "月度_8结果已保存\n", "\n", "求解月度_9...\n", "Set parameter TimeLimit to value 10800\n", "Set parameter MIPGap to value 0.001\n", "Gurobi Optimizer version 12.0.0 build v12.0.0rc1 (mac64[arm] - Darwin 24.1.0 24B83)\n", "\n", "CPU model: Apple M3\n", "Thread count: 8 physical cores, 8 logical processors, using up to 8 threads\n", "\n", "Non-default parameters:\n", "TimeLimit  10800\n", "MIPGap  0.001\n", "\n", "Optimize a model with 4078122 rows, 2902320 columns and 10449897 nonzeros\n", "Model fingerprint: 0x2cf6c6f5\n", "Model has 269280 simple general constraints\n", "  269280 ABS\n", "Variable types: 2865600 continuous, 36720 integer (36720 binary)\n", "Coefficient statistics:\n", "  Matrix range     [2e-03, 5e+04]\n", "  Objective range  [1e-01, 1e+03]\n", "  Bounds range     [1e+00, 1e+00]\n", "  RHS range        [5e-03, 2e+07]\n", "Presolve removed 3150196 rows and 1701845 columns (presolve time = 5s)...\n", "Presolve removed 3179156 rows and 1726560 columns (presolve time = 10s)...\n", "Presolve removed 2882586 rows and 1733709 columns\n", "Presolve time: 13.70s\n", "Presolved: 1195536 rows, 1168730 columns, 4662942 nonzeros\n", "Variable types: 1132779 continuous, 35951 integer (35951 binary)\n", "Deterministic concurrent LP optimizer: primal simplex, dual simplex, and barrier\n", "Showing barrier log only...\n", "\n", "Root barrier log...\n", "\n", "Ordering time: 7.81s\n", "\n", "Barrier statistics:\n", " AA' NZ     : 4.696e+06\n", " Factor NZ  : 3.469e+07 (roughly 1.2 GB of memory)\n", " Factor Ops : 1.448e+10 (less than 1 second per iteration)\n", " Threads    : 5\n", "\n", "                  Objective                Residual\n", "Iter       Primal          Dual         Primal    Dual     Compl     Time\n", "   0  -5.61562478e+14 -8.91868131e+15  5.34e+09 5.69e+03  1.42e+12    28s\n", "   1  -7.69938273e+13 -7.88428272e+15  1.11e+09 5.84e-09  2.91e+11    29s\n", "   2   6.90360344e+11 -6.11826051e+15  3.97e+07 4.94e-07  1.22e+10    30s\n", "   3   1.91384238e+12 -3.69344891e+15  4.29e+06 2.45e+02  2.18e+09    31s\n", "   4   2.06961370e+12 -1.03997400e+15  9.42e+05 1.31e-07  4.89e+08    32s\n", "   5   2.08333975e+12 -5.55612848e+14  1.45e+05 6.33e-08  1.93e+08    33s\n", "   6   2.01388165e+12 -2.22624876e+14  2.61e+04 3.17e-08  7.21e+07    34s\n", "   7   1.75904637e+12 -5.58224041e+13  4.08e+03 1.12e-08  1.81e+07    35s\n", "   8   1.22225943e+12 -1.50408817e+13  9.28e+02 5.59e-09  5.07e+06    36s\n", "   9   5.83342987e+11 -4.21900836e+12  2.33e+02 9.31e-09  1.50e+06    36s\n", "  10   2.39410893e+11 -1.08548770e+12  6.92e+01 7.45e-09  4.13e+05    37s\n", "  11   7.47033962e+10 -2.06171880e+11  1.05e+01 7.45e-09  8.74e+04    38s\n", "  12   4.51896145e+10 -6.54700147e+10  2.71e+00 7.45e-09  3.44e+04    39s\n", "  13   4.11027588e+10 -5.98757435e+10  1.81e+00 7.45e-09  3.14e+04    40s\n", "  14   3.92992577e+10 -2.57070625e+10  1.46e+00 5.59e-09  2.02e+04    41s\n", "  15   3.49509274e+10 -2.51333769e+09  6.53e-01 3.73e-09  1.17e+04    42s\n", "  16   3.30241982e+10 -1.40711523e+09  3.74e-01 4.66e-09  1.07e+04    43s\n", "  17   3.29524665e+10 -1.10197838e+09  3.63e-01 3.73e-09  1.06e+04    43s\n", "  18   3.27868944e+10 -7.60321835e+08  3.42e-01 4.66e-09  1.04e+04    44s\n", "  19   3.16644941e+10  1.65615924e+10  1.88e-01 2.79e-09  4.70e+03    45s\n", "  20   3.09253114e+10  2.37410041e+10  1.03e-01 1.40e-09  2.24e+03    46s\n", "  21   3.04080633e+10  2.68266908e+10  4.93e-02 1.16e-09  1.11e+03    48s\n", "  22   3.01605478e+10  2.83382953e+10  2.49e-02 5.82e-10  5.67e+02    49s\n", "  23   3.00697285e+10  2.92000298e+10  1.62e-02 9.31e-10  2.71e+02    49s\n", "  24   3.00029581e+10  2.95041442e+10  1.01e-02 9.31e-10  1.55e+02    50s\n", "  25   2.99733741e+10  2.96844515e+10  7.53e-03 1.86e-09  8.99e+01    51s\n", "  26   2.99347849e+10  2.97696697e+10  4.27e-03 9.31e-10  5.14e+01    52s\n", "  27   2.99048240e+10  2.98249884e+10  1.87e-03 9.31e-10  2.48e+01    52s\n", "  28   2.98950562e+10  2.98445241e+10  1.14e-03 2.09e-11  1.57e+01    53s\n", "  29   2.98873591e+10  2.98602679e+10  5.97e-04 9.31e-10  8.43e+00    54s\n", "  30   2.98840087e+10  2.98671921e+10  3.74e-04 2.59e-10  5.23e+00    54s\n", "  31   2.98807643e+10  2.98730695e+10  1.64e-04 4.66e-10  2.39e+00    55s\n", "  32   2.98794311e+10  2.98759368e+10  8.28e-05 4.66e-10  1.09e+00    56s\n", "  33   2.98787146e+10  2.98768398e+10  3.94e-05 1.34e-10  5.83e-01    57s\n", "  34   2.98784579e+10  2.98774543e+10  2.42e-05 9.31e-10  3.12e-01    58s\n", "  35   2.98781805e+10  2.98777485e+10  1.37e-05 9.31e-10  1.34e-01    59s\n", "  36   2.98780753e+10  2.98779531e+10  3.23e-06 9.31e-10  3.80e-02    61s\n", "  37   2.98780544e+10  2.98779792e+10  1.29e-06 1.46e-11  2.34e-02    62s\n", "  38   2.98780525e+10  2.98780056e+10  1.10e-06 1.46e-11  1.46e-02    63s\n", "  39   2.98780454e+10  2.98780312e+10  4.01e-07 9.31e-10  4.42e-03    65s\n", "  40   2.98780427e+10  2.98780392e+10  1.05e-07 5.46e-12  1.11e-03    66s\n", "  41   2.98780419e+10  2.98780417e+10  4.47e-08 4.66e-10  5.22e-05    68s\n", "  42   2.98780417e+10  2.98780417e+10  4.82e-08 2.33e-10  1.12e-06    69s\n", "  43   2.98780417e+10  2.98780417e+10  7.98e-05 4.66e-10  9.00e-09    71s\n", "\n", "<PERSON><PERSON> solved model in 43 iterations and 70.96 seconds (68.15 work units)\n", "Optimal objective 2.98780417e+10\n", "\n", "\n", "Root crossover log...\n", "\n", "  128335 DPushes remaining with DInf 0.0000000e+00                72s\n", "       0 DPushes remaining with DInf 0.0000000e+00                74s\n", "Warning: Markowitz tolerance tightened to 0.5\n", "\n", "  335308 PPushes remaining with PInf 6.1554299e-05                74s\n", "  316648 PPushes remaining with PInf 6.1554299e-05                75s\n", "  264127 PPushes remaining with PInf 6.7418189e-05                80s\n", "  206748 PPushes remaining with PInf 0.0000000e+00                85s\n", "  187542 PPushes remaining with PInf 0.0000000e+00                90s\n", "  165524 PPushes remaining with PInf 0.0000000e+00                95s\n", "  145817 PPushes remaining with PInf 0.0000000e+00               100s\n", "  127148 PPushes remaining with PInf 0.0000000e+00               105s\n", "  108487 PPushes remaining with PInf 0.0000000e+00               110s\n", "   85149 PPushes remaining with PInf 0.0000000e+00               115s\n", "   64044 PPushes remaining with PInf 0.0000000e+00               120s\n", "   42075 PPushes remaining with PInf 0.0000000e+00               125s\n", "   18471 PPushes remaining with PInf 0.0000000e+00               130s\n", "    4044 PPushes remaining with PInf 0.0000000e+00               135s\n", "       0 PPushes remaining with PInf 0.0000000e+00               136s\n", "\n", "  Push phase complete: Pinf 0.0000000e+00, Dinf 2.8873357e-08    137s\n", "\n", "\n", "Root simplex log...\n", "\n", "Iteration    Objective       Primal Inf.    Dual Inf.      Time\n", "  415479    2.9878042e+10   0.000000e+00   0.000000e+00    137s\n", "Concurrent spin time: 17.08s (can be avoided by choosing Method=3)\n", "\n", "Solved with barrier\n", "  415519    2.9878042e+10   0.000000e+00   0.000000e+00    155s\n", "Extra simplex iterations after uncrush: 40\n", "\n", "Root relaxation: objective 2.987804e+10, 415519 iterations, 140.04 seconds (86.38 work units)\n", "Total elapsed time = 155.29s (DegenMoves)\n", "Total elapsed time = 277.29s (<PERSON>gen<PERSON>oves)\n", "Total elapsed time = 288.86s (DegenMoves)\n", "Total elapsed time = 299.75s (DegenMoves)\n", "Total elapsed time = 310.84s (DegenMoves)\n", "\n", "    Nodes    |    Current Node    |     Objective Bounds      |     Work\n", " Expl Unexpl |  Obj  Depth IntInf | Incumbent    BestBd   Gap | It/Node Time\n", "\n", "     0     0 2.9878e+10    0  698          - 2.9878e+10      -     -  316s\n", "H    0     0                    3.493144e+12 2.9878e+10  99.1%     -  424s\n", "     0     0 2.9878e+10    0  754 3.4931e+12 2.9878e+10  99.1%     -  524s\n", "H    0     0                    2.988631e+10 2.9878e+10  0.03%     -  649s\n", "\n", "Cutting planes:\n", "  Gomory: 63\n", "  MIR: 551\n", "  Flow cover: 538\n", "\n", "Explored 1 nodes (571040 simplex iterations) in 649.73 seconds (509.37 work units)\n", "Thread count was 8 (of 8 available processors)\n", "\n", "Solution count 2: 2.98863e+10 3.49314e+12 \n", "\n", "Optimal solution found (tolerance 1.00e-03)\n", "Best objective 2.988630537511e+10, best bound 2.987845016272e+10, gap 0.0263%\n", "月度_9找到最优解\n", "目标值 (总成本): 29,886,305,375.11\n", "月度_9结果已保存\n", "\n", "求解月度_10...\n", "Set parameter TimeLimit to value 10800\n", "Set parameter MIPGap to value 0.001\n", "Gurobi Optimizer version 12.0.0 build v12.0.0rc1 (mac64[arm] - Darwin 24.1.0 24B83)\n", "\n", "CPU model: Apple M3\n", "Thread count: 8 physical cores, 8 logical processors, using up to 8 threads\n", "\n", "Non-default parameters:\n", "TimeLimit  10800\n", "MIPGap  0.001\n", "\n", "Optimize a model with 4214082 rows, 2999064 columns and 10798305 nonzeros\n", "Model fingerprint: 0x8cfb5e9f\n", "Model has 278256 simple general constraints\n", "  278256 ABS\n", "Variable types: 2961120 continuous, 37944 integer (37944 binary)\n", "Coefficient statistics:\n", "  Matrix range     [2e-03, 5e+04]\n", "  Objective range  [1e-01, 1e+03]\n", "  Bounds range     [1e+00, 1e+00]\n", "  RHS range        [5e-03, 2e+07]\n", "Presolve removed 3253862 rows and 1758530 columns (presolve time = 5s)...\n", "Presolve removed 3253935 rows and 1758765 columns (presolve time = 10s)...\n", "Presolve removed 2948275 rows and 1766950 columns\n", "Presolve time: 13.33s\n", "Presolved: 1265807 rows, 1232235 columns, 4929537 nonzeros\n", "Variable types: 1195035 continuous, 37200 integer (37200 binary)\n", "Deterministic concurrent LP optimizer: primal simplex, dual simplex, and barrier\n", "Showing barrier log only...\n", "\n", "Root barrier log...\n", "\n", "Elapsed ordering time = 5s\n", "Ordering time: 12.29s\n", "\n", "Barrier statistics:\n", " AA' NZ     : 4.965e+06\n", " Factor NZ  : 3.618e+07 (roughly 1.2 GB of memory)\n", " Factor Ops : 1.512e+10 (less than 1 second per iteration)\n", " Threads    : 5\n", "\n", "                  Objective                Residual\n", "Iter       Primal          Dual         Primal    Dual     Compl     Time\n", "   0  -6.22297036e+14 -5.43823732e+13  2.09e+10 1.36e+04  8.85e+09    36s\n", "   1  -2.33119521e+14 -4.79069947e+13  8.70e+09 5.82e-11  3.69e+09    37s\n", "   2  -8.71417229e+12 -4.07224920e+13  4.48e+08 5.53e-11  2.03e+08    39s\n", "   3   7.00755475e+11 -3.18060223e+13  2.00e+07 5.53e-11  1.81e+07    41s\n", "   4   7.30717103e+11 -1.35288268e+13  5.98e+06 8.63e-11  6.30e+06    43s\n", "   5   4.62784663e+11 -2.56457036e+12  5.92e+05 6.98e-11  1.04e+06    44s\n", "   6   1.67147584e+11 -1.22378461e+12  1.62e+05 1.09e-09  4.40e+05    46s\n", "   7   9.32878659e+10 -1.09619434e+12  6.49e+04 8.59e-10  3.64e+05    47s\n", "   8   6.03364971e+10 -3.86663195e+11  2.30e+04 2.55e-10  1.35e+05    48s\n", "   9   4.76904375e+10 -1.26392917e+11  9.51e+03 8.73e-11  5.23e+04    50s\n", "  10   3.82416407e+10 -5.69862362e+10  2.63e+03 5.09e-11  2.84e+04    51s\n", "  11   3.52670806e+10 -2.26183956e+10  1.30e+03 4.37e-11  1.73e+04    53s\n", "  12   3.43443651e+10 -8.07926395e+07  9.85e+02 4.37e-11  1.03e+04    54s\n", "  13   3.19742411e+10  1.30135254e+10  2.33e+02 4.37e-11  5.65e+03    56s\n", "  14   3.13431769e+10  2.54901964e+10  8.85e+01 8.73e-11  1.74e+03    58s\n", "  15   3.10677437e+10  2.76558673e+10  4.07e+01 5.82e-11  1.02e+03    59s\n", "  16   3.09722942e+10  2.88608238e+10  2.61e+01 5.82e-11  6.29e+02    60s\n", "  17   3.08740753e+10  2.97399067e+10  1.18e+01 5.82e-11  3.38e+02    62s\n", "  18   3.08207815e+10  3.04092651e+10  4.80e+00 4.37e-11  1.23e+02    64s\n", "  19   3.08036780e+10  3.06505253e+10  2.93e+00 3.27e-11  4.56e+01    65s\n", "  20   3.07882994e+10  3.06958028e+10  1.43e+00 1.82e-11  2.75e+01    66s\n", "  21   3.07791221e+10  3.07489693e+10  7.54e-01 1.16e-10  8.98e+00    68s\n", "  22   3.07737515e+10  3.07612092e+10  2.99e-01 2.40e-10  3.74e+00    70s\n", "  23   3.07716910e+10  3.07646918e+10  1.46e-01 2.73e-10  2.08e+00    71s\n", "  24   3.07708955e+10  3.07667872e+10  8.89e-02 3.56e-10  1.22e+00    72s\n", "  25   3.07703747e+10  3.07685535e+10  5.23e-02 3.44e-10  5.43e-01    74s\n", "  26   3.07700034e+10  3.07690084e+10  2.70e-02 2.98e-10  2.96e-01    76s\n", "  27   3.07699090e+10  3.07691495e+10  2.03e-02 2.55e-10  2.26e-01    77s\n", "  28   3.07698234e+10  3.07692801e+10  1.42e-02 2.09e-10  1.62e-01    78s\n", "  29   3.07697009e+10  3.07694966e+10  5.36e-03 1.11e-10  6.09e-02    80s\n", "  30   3.07696401e+10  3.07695995e+10  1.04e-03 3.85e-11  1.21e-02    82s\n", "  31   3.07696311e+10  3.07696193e+10  4.58e-04 7.28e-12  3.51e-03    84s\n", "  32   3.07696290e+10  3.07696197e+10  3.22e-04 7.28e-12  2.77e-03    86s\n", "\n", "<PERSON><PERSON> solved model in 32 iterations and 85.52 seconds (52.37 work units)\n", "Optimal objective 3.07696290e+10\n", "\n", "\n", "Root crossover log...\n", "\n", "  174025 DPushes remaining with DInf 0.0000000e+00                87s\n", "       0 DPushes remaining with DInf 0.0000000e+00                89s\n", "Warning: Markowitz tolerance tightened to 0.5\n", "\n", "  849058 PPushes remaining with PInf 1.5144731e-03                90s\n", "  555040 PPushes remaining with PInf 2.0587222e-02                92s\n", "  522035 PPushes remaining with PInf 2.0570751e-02                95s\n", "  494900 PPushes remaining with PInf 2.0570751e-02               100s\n", "  449980 PPushes remaining with PInf 7.2733439e-04               105s\n", "  409720 PPushes remaining with PInf 7.2733439e-04               110s\n", "  390403 PPushes remaining with PInf 7.2733439e-04               116s\n", "  365356 PPushes remaining with PInf 7.2733439e-04               120s\n", "  339519 PPushes remaining with PInf 7.2733439e-04               125s\n", "  258066 PPushes remaining with PInf 7.5254907e-04               132s\n", "  249448 PPushes remaining with PInf 7.5254907e-04               135s\n", "  234491 PPushes remaining with PInf 6.5533283e-04               140s\n", "  200876 PPushes remaining with PInf 6.5533283e-04               145s\n", "  184779 PPushes remaining with PInf 6.5533283e-04               150s\n", "  174121 PPushes remaining with PInf 6.5533283e-04               155s\n", "  161059 PPushes remaining with PInf 6.5533283e-04               160s\n", "  132972 PPushes remaining with PInf 6.5533283e-04               165s\n", "  118919 PPushes remaining with PInf 6.5533283e-04               170s\n", "  105807 PPushes remaining with PInf 6.5533283e-04               175s\n", "   85296 PPushes remaining with PInf 6.5533283e-04               180s\n", "   63395 PPushes remaining with PInf 6.5533283e-04               185s\n", "   43341 PPushes remaining with PInf 6.5533283e-04               190s\n", "   29461 PPushes remaining with PInf 6.5533283e-04               195s\n", "   11635 PPushes remaining with PInf 6.5533283e-04               200s\n", "       0 PPushes remaining with PInf 0.0000000e+00               205s\n", "\n", "  Push phase complete: Pinf 0.0000000e+00, Dinf 1.4076053e+03    205s\n", "\n", "\n", "Root simplex log...\n", "\n", "Iteration    Objective       Primal Inf.    Dual Inf.      Time\n", "  981087    3.0769624e+10   0.000000e+00   1.407605e+03    206s\n", "  981210    3.0769624e+10   0.000000e+00   0.000000e+00    206s\n", "Concurrent spin time: 11.16s\n", "\n", "Solved with barrier\n", "  981300    3.0769624e+10   0.000000e+00   0.000000e+00    219s\n", "Extra simplex iterations after uncrush: 90\n", "\n", "Root relaxation: objective 3.076962e+10, 981300 iterations, 203.92 seconds (90.71 work units)\n", "Total elapsed time = 490.89s (DegenMoves)\n", "Total elapsed time = 519.38s (<PERSON>gen<PERSON>oves)\n", "Total elapsed time = 538.77s (<PERSON><PERSON><PERSON><PERSON>s)\n", "Total elapsed time = 559.82s (<PERSON>gen<PERSON><PERSON>s)\n", "\n", "    Nodes    |    Current Node    |     Objective Bounds      |     Work\n", " Expl Unexpl |  Obj  Depth IntInf | Incumbent    BestBd   Gap | It/Node Time\n", "\n", "     0     0 3.0770e+10    0  851          - 3.0770e+10      -     -  564s\n", "H    0     0                    3.658368e+12 3.0770e+10  99.2%     -  702s\n", "     0     0 3.0770e+10    0 1089 3.6584e+12 3.0770e+10  99.2%     -  793s\n", "H    0     0                    3.077752e+10 3.0770e+10  0.02%     - 1005s\n", "\n", "Cutting planes:\n", "  Gomory: 58\n", "  MIR: 647\n", "  Flow cover: 650\n", "\n", "Explored 1 nodes (1192832 simplex iterations) in 1006.34 seconds (729.72 work units)\n", "Thread count was 8 (of 8 available processors)\n", "\n", "Solution count 2: 3.07775e+10 3.65837e+12 \n", "\n", "Optimal solution found (tolerance 1.00e-03)\n", "Warning: max constraint violation (1.4182e-06) exceeds tolerance\n", "Best objective 3.077752112903e+10, best bound 3.077044953698e+10, gap 0.0230%\n", "月度_10找到最优解\n", "目标值 (总成本): 30,777,521,129.03\n", "月度_10结果已保存\n", "\n", "求解月度_11...\n", "Set parameter TimeLimit to value 10800\n", "Set parameter MIPGap to value 0.001\n", "Gurobi Optimizer version 12.0.0 build v12.0.0rc1 (mac64[arm] - Darwin 24.1.0 24B83)\n", "\n", "CPU model: Apple M3\n", "Thread count: 8 physical cores, 8 logical processors, using up to 8 threads\n", "\n", "Non-default parameters:\n", "TimeLimit  10800\n", "MIPGap  0.001\n", "\n", "Optimize a model with 4078122 rows, 2902320 columns and 10449897 nonzeros\n", "Model fingerprint: 0x3d4c03ec\n", "Model has 269280 simple general constraints\n", "  269280 ABS\n", "Variable types: 2865600 continuous, 36720 integer (36720 binary)\n", "Coefficient statistics:\n", "  Matrix range     [2e-03, 5e+04]\n", "  Objective range  [1e-01, 1e+03]\n", "  Bounds range     [1e+00, 1e+00]\n", "  RHS range        [5e-03, 2e+07]\n", "Presolve removed 3148854 rows and 1701646 columns (presolve time = 5s)...\n", "Presolve removed 3148899 rows and 1701836 columns (presolve time = 10s)...\n", "Presolve removed 2853105 rows and 1709708 columns\n", "Presolve time: 12.76s\n", "Presolved: 1225017 rows, 1192734 columns, 4770797 nonzeros\n", "Variable types: 1156734 continuous, 36000 integer (36000 binary)\n", "Deterministic concurrent LP optimizer: primal simplex, dual simplex, and barrier\n", "Showing barrier log only...\n", "\n", "Root barrier log...\n", "\n", "Elapsed ordering time = 5s\n", "Ordering time: 11.07s\n", "\n", "Barrier statistics:\n", " AA' NZ     : 4.805e+06\n", " Factor NZ  : 3.469e+07 (roughly 1.2 GB of memory)\n", " Factor Ops : 1.400e+10 (less than 1 second per iteration)\n", " Threads    : 5\n", "\n", "                  Objective                Residual\n", "Iter       Primal          Dual         Primal    Dual     Compl     Time\n", "   0  -6.73494759e+14 -5.21656732e+13  2.23e+10 1.32e+04  9.59e+09    33s\n", "   1  -2.46205646e+14 -4.61795538e+13  9.06e+09 4.73e-11  3.91e+09    34s\n", "   2  -9.63570869e+12 -3.89760222e+13  4.19e+08 6.26e-11  1.93e+08    36s\n", "   3   4.31893654e+11 -2.99007009e+13  2.44e+07 4.37e-11  1.98e+07    37s\n", "   4   5.84961781e+11 -9.75818156e+12  6.13e+06 5.53e-11  5.27e+06    39s\n", "   5   3.13667010e+11 -2.50297403e+12  9.05e+05 5.82e-11  1.09e+06    40s\n", "   6   1.34410664e+11 -9.95668816e+11  2.51e+05 5.53e-11  3.87e+05    42s\n", "   7   7.61843920e+10 -9.06880666e+11  9.29e+04 5.09e-11  3.16e+05    43s\n", "   8   5.46289743e+10 -1.99974713e+11  3.29e+04 5.82e-11  8.05e+04    44s\n", "   9   4.48422479e+10 -8.23906700e+10  1.75e+04 2.91e-11  3.99e+04    45s\n", "  10   3.65881639e+10 -4.75848407e+10  6.54e+03 2.91e-11  2.61e+04    47s\n", "  11   3.41921448e+10 -3.72526587e+10  3.77e+03 1.03e-02  2.21e+04    49s\n", "  12   3.24898899e+10 -2.05824688e+10  1.99e+03 2.18e-11  1.64e+04    50s\n", "  13   3.17360027e+10  5.66117412e+09  1.30e+03 4.37e-11  8.05e+03    51s\n", "  14   3.04863808e+10  2.09828819e+10  3.26e+02 4.37e-11  2.93e+03    53s\n", "  15   3.02053198e+10  2.53985250e+10  1.83e+02 5.82e-11  1.48e+03    54s\n", "  16   3.00632518e+10  2.71500280e+10  1.20e+02 4.37e-11  8.98e+02    55s\n", "  17   2.98499305e+10  2.82155064e+10  3.10e+01 4.37e-11  5.03e+02    56s\n", "  18   2.97873155e+10  2.93166537e+10  8.85e+00 5.82e-11  1.45e+02    58s\n", "  19   2.97743904e+10  2.95218270e+10  5.68e+00 3.64e-11  7.77e+01    59s\n", "  20   2.97656594e+10  2.96238418e+10  3.77e+00 2.55e-11  4.37e+01    60s\n", "  21   2.97583748e+10  2.96990394e+10  2.35e+00 1.27e-11  1.83e+01    62s\n", "  22   2.97473046e+10  2.97287751e+10  4.18e-01 2.00e-10  5.70e+00    63s\n", "  23   2.97451985e+10  2.97358169e+10  1.99e-01 1.62e-10  2.89e+00    65s\n", "  24   2.97443865e+10  2.97392459e+10  1.22e-01 1.17e-10  1.58e+00    66s\n", "  25   2.97434547e+10  2.97410403e+10  3.60e-02 8.32e-11  7.43e-01    67s\n", "  26   2.97432120e+10  2.97422371e+10  1.48e-02 4.65e-11  3.00e-01    68s\n", "  27   2.97431395e+10  2.97426463e+10  8.70e-03 4.31e-11  1.52e-01    70s\n", "  28   2.97431090e+10  2.97427541e+10  6.29e-03 3.84e-11  1.09e-01    71s\n", "  29   2.97430709e+10  2.97429061e+10  3.10e-03 2.78e-11  5.07e-02    73s\n", "  30   2.97430584e+10  2.97429970e+10  2.06e-03 1.50e-11  1.89e-02    74s\n", "  31   2.97430357e+10  2.97430172e+10  2.82e-04 1.04e-11  5.69e-03    76s\n", "  32   2.97430330e+10  2.97430291e+10  8.80e-05 7.28e-12  1.21e-03    78s\n", "  33   2.97430319e+10  2.97430317e+10  7.84e-06 1.46e-11  4.63e-05    80s\n", "  34   2.97430318e+10  2.97430318e+10  3.26e-07 2.18e-11  2.67e-06    82s\n", "  35   2.97430318e+10  2.97430318e+10  1.66e-06 7.28e-12  2.77e-08    84s\n", "  36   2.97430318e+10  2.97430318e+10  1.37e-07 1.09e-11  7.49e-11    86s\n", "\n", "<PERSON><PERSON> solved model in 36 iterations and 85.97 seconds (54.60 work units)\n", "Optimal objective 2.97430318e+10\n", "\n", "\n", "Root crossover log...\n", "\n", "  130917 DPushes remaining with DInf 0.0000000e+00                88s\n", "       0 DPushes remaining with DInf 0.0000000e+00                90s\n", "Warning: Markowitz tolerance tightened to 0.5\n", "\n", "  364424 PPushes remaining with PInf 0.0000000e+00                90s\n", "  298005 PPushes remaining with PInf 0.0000000e+00                95s\n", "  245160 PPushes remaining with PInf 0.0000000e+00               100s\n", "  212437 PPushes remaining with PInf 0.0000000e+00               105s\n", "  194212 PPushes remaining with PInf 0.0000000e+00               110s\n", "  179188 PPushes remaining with PInf 0.0000000e+00               116s\n", "  168930 PPushes remaining with PInf 0.0000000e+00               120s\n", "  154043 PPushes remaining with PInf 0.0000000e+00               125s\n", "  138354 PPushes remaining with PInf 0.0000000e+00               130s\n", "  125215 PPushes remaining with PInf 0.0000000e+00               135s\n", "  104115 PPushes remaining with PInf 0.0000000e+00               140s\n", "   85384 PPushes remaining with PInf 0.0000000e+00               145s\n", "   71219 PPushes remaining with PInf 0.0000000e+00               150s\n", "   58134 PPushes remaining with PInf 0.0000000e+00               155s\n", "   39708 PPushes remaining with PInf 0.0000000e+00               160s\n", "   26651 PPushes remaining with PInf 0.0000000e+00               165s\n", "   12584 PPushes remaining with PInf 0.0000000e+00               170s\n", "       0 PPushes remaining with PInf 0.0000000e+00               175s\n", "\n", "  Push phase complete: Pinf 0.0000000e+00, Dinf 2.8830314e-08    175s\n", "\n", "\n", "Root simplex log...\n", "\n", "Iteration    Objective       Primal Inf.    Dual Inf.      Time\n", "  435541    2.9743032e+10   0.000000e+00   0.000000e+00    175s\n", "Concurrent spin time: 16.81s\n", "\n", "Solved with barrier\n", "  435628    2.9743032e+10   0.000000e+00   0.000000e+00    193s\n", "Extra simplex iterations after uncrush: 87\n", "\n", "Root relaxation: objective 2.974303e+10, 435628 iterations, 179.06 seconds (81.57 work units)\n", "Total elapsed time = 202.66s (DegenMoves)\n", "Total elapsed time = 205.66s (DegenMoves)\n", "\n", "    Nodes    |    Current Node    |     Objective Bounds      |     Work\n", " Expl Unexpl |  Obj  Depth IntInf | Incumbent    BestBd   Gap | It/Node Time\n", "\n", "     0     0 2.9743e+10    0  141          - 2.9743e+10      -     -  209s\n", "H    0     0                    2.975196e+10 2.9743e+10  0.03%     -  329s\n", "\n", "Explored 1 nodes (437909 simplex iterations) in 330.05 seconds (196.91 work units)\n", "Thread count was 8 (of 8 available processors)\n", "\n", "Solution count 1: 2.9752e+10 \n", "\n", "Optimal solution found (tolerance 1.00e-03)\n", "Warning: max constraint violation (1.0127e-06) exceeds tolerance\n", "Best objective 2.975195720050e+10, best bound 2.974303175675e+10, gap 0.0300%\n", "月度_11找到最优解\n", "目标值 (总成本): 29,751,957,200.50\n", "月度_11结果已保存\n", "\n", "求解月度_12...\n", "Set parameter TimeLimit to value 10800\n", "Set parameter MIPGap to value 0.001\n", "Gurobi Optimizer version 12.0.0 build v12.0.0rc1 (mac64[arm] - Darwin 24.1.0 24B83)\n", "\n", "CPU model: Apple M3\n", "Thread count: 8 physical cores, 8 logical processors, using up to 8 threads\n", "\n", "Non-default parameters:\n", "TimeLimit  10800\n", "MIPGap  0.001\n", "\n", "Optimize a model with 4214133 rows, 2999064 columns and 10798356 nonzeros\n", "Model fingerprint: 0x32f63aff\n", "Model has 278256 simple general constraints\n", "  278256 ABS\n", "Variable types: 2961120 continuous, 37944 integer (37944 binary)\n", "Coefficient statistics:\n", "  Matrix range     [2e-03, 5e+04]\n", "  Objective range  [1e-01, 1e+03]\n", "  Bounds range     [1e+00, 1e+00]\n", "  RHS range        [5e-03, 2e+07]\n", "Presolve removed 3253964 rows and 1758783 columns (presolve time = 5s)...\n", "Presolve removed 3253975 rows and 1758857 columns (presolve time = 10s)...\n", "Presolve removed 2948315 rows and 1766991 columns\n", "Presolve time: 12.19s\n", "Presolved: 1265818 rows, 1232194 columns, 4929693 nonzeros\n", "Variable types: 1194994 continuous, 37200 integer (37200 binary)\n", "Deterministic concurrent LP optimizer: primal simplex, dual simplex, and barrier\n", "Showing barrier log only...\n", "\n", "Root barrier log...\n", "\n", "Elapsed ordering time = 5s\n", "Ordering time: 11.40s\n", "\n", "Barrier statistics:\n", " AA' NZ     : 4.965e+06\n", " Factor NZ  : 3.642e+07 (roughly 1.2 GB of memory)\n", " Factor Ops : 1.557e+10 (less than 1 second per iteration)\n", " Threads    : 5\n", "\n", "                  Objective                Residual\n", "Iter       Primal          Dual         Primal    Dual     Compl     Time\n", "   0  -6.85543938e+14 -5.21306650e+13  2.25e+10 1.36e+04  9.08e+09    33s\n", "   1  -2.60681200e+14 -4.62484375e+13  9.45e+09 4.29e+03  3.87e+09    35s\n", "   2  -2.22888819e+13 -3.94161540e+13  1.01e+09 5.82e-11  4.29e+08    36s\n", "   3  -3.14348730e+10 -2.88474692e+13  4.56e+07 4.82e-09  2.72e+07    38s\n", "   4   5.10776809e+11 -1.08980325e+13  7.48e+06 5.27e-09  5.96e+06    40s\n", "   5   4.16899447e+11 -3.88822581e+12  2.94e+06 2.32e-09  2.05e+06    41s\n", "   6   1.75717802e+11 -1.25153054e+12  5.02e+05 6.90e-10  5.09e+05    43s\n", "   7   1.02304291e+11 -8.50425411e+11  2.27e+05 5.28e-10  3.15e+05    44s\n", "   8   6.79817772e+10 -7.53320610e+11  9.27e+04 4.86e-10  2.56e+05    45s\n", "   9   5.72788483e+10 -2.39829551e+11  4.86e+04 1.57e-10  9.18e+04    46s\n", "  10   4.46463069e+10 -8.55450016e+10  1.53e+04 6.28e-11  3.94e+04    48s\n", "  11   3.92243622e+10 -6.38960927e+10  7.59e+03 5.46e-11  3.10e+04    50s\n", "  12   3.83179547e+10 -2.42031368e+10  6.50e+03 3.55e-11  1.88e+04    51s\n", "  13   3.66528518e+10 -4.04108335e+09  4.59e+03 2.18e-11  1.22e+04    52s\n", "  14   3.47717086e+10  1.24218781e+10  2.70e+03 2.18e-11  6.71e+03    53s\n", "  15   3.32441841e+10  2.23974688e+10  1.13e+03 2.18e-11  3.25e+03    55s\n", "  16   3.28057550e+10  2.78514499e+10  7.37e+02 2.91e-11  1.49e+03    56s\n", "  17   3.25784195e+10  2.91741855e+10  5.44e+02 3.64e-11  1.02e+03    57s\n", "  18   3.23153129e+10  2.99837922e+10  3.25e+02 3.64e-11  6.99e+02    59s\n", "  19   3.20842887e+10  3.07947841e+10  1.39e+02 3.64e-11  3.86e+02    61s\n", "  20   3.20203568e+10  3.12649216e+10  8.85e+01 2.91e-11  2.26e+02    62s\n", "  21   3.19940360e+10  3.14975448e+10  6.88e+01 2.91e-11  1.49e+02    63s\n", "  22   3.19468416e+10  3.17053040e+10  3.37e+01 2.91e-11  7.24e+01    65s\n", "  23   3.19153158e+10  3.18222982e+10  1.21e+01 2.18e-11  2.79e+01    66s\n", "  24   3.19045473e+10  3.18675419e+10  5.65e+00 2.91e-11  1.11e+01    68s\n", "  25   3.18992828e+10  3.18774054e+10  2.81e+00 6.15e-11  6.55e+00    69s\n", "  26   3.18965354e+10  3.18861998e+10  1.41e+00 8.76e-11  3.10e+00    71s\n", "  27   3.18951956e+10  3.18901216e+10  7.62e-01 7.71e-11  1.52e+00    72s\n", "  28   3.18946808e+10  3.18914367e+10  5.15e-01 6.17e-11  9.73e-01    74s\n", "  29   3.18943038e+10  3.18919685e+10  3.31e-01 6.94e-11  7.00e-01    75s\n", "  30   3.18941701e+10  3.18925057e+10  2.66e-01 8.34e-11  4.99e-01    76s\n", "  31   3.18940004e+10  3.18928257e+10  1.86e-01 8.76e-11  3.52e-01    78s\n", "  32   3.18938625e+10  3.18930069e+10  1.20e-01 8.35e-11  2.56e-01    80s\n", "  33   3.18937886e+10  3.18931728e+10  8.54e-02 7.38e-11  1.85e-01    82s\n", "  34   3.18937204e+10  3.18932964e+10  5.24e-02 6.14e-11  1.27e-01    85s\n", "  35   3.18936781e+10  3.18934013e+10  3.20e-02 4.76e-11  8.28e-02    87s\n", "  36   3.18936527e+10  3.18935483e+10  2.04e-02 2.41e-11  3.14e-02    89s\n", "  37   3.18936213e+10  3.18935817e+10  6.53e-03 1.61e-11  1.19e-02    93s\n", "  38   3.18936136e+10  3.18936005e+10  1.91e-03 1.46e-11  3.92e-03    95s\n", "  39   3.18936135e+10  3.18936053e+10  1.85e-03 1.09e-11  2.45e-03    96s\n", "\n", "<PERSON><PERSON> solved model in 39 iterations and 96.29 seconds (59.91 work units)\n", "Optimal objective 3.18936135e+10\n", "\n", "\n", "Root crossover log...\n", "\n", "  165145 DPushes remaining with DInf 0.0000000e+00                98s\n", "       0 DPushes remaining with DInf 0.0000000e+00               101s\n", "Warning: Markowitz tolerance tightened to 0.5\n", "\n", "  843477 PPushes remaining with PInf 9.2670723e-04               101s\n", "  542446 PPushes remaining with PInf 3.2972643e-03               105s\n", "  501879 PPushes remaining with PInf 3.2886800e-03               110s\n", "  478144 PPushes remaining with PInf 3.2075720e-03               115s\n", "  413742 PPushes remaining with PInf 1.4679193e-03               120s\n", "  382731 PPushes remaining with PInf 1.1985315e-03               125s\n", "  313427 PPushes remaining with PInf 3.0490065e-03               131s\n", "  290730 PPushes remaining with PInf 6.1919991e-04               135s\n", "  250330 PPushes remaining with PInf 4.3719475e-04               140s\n", "  234142 PPushes remaining with PInf 1.3941817e-05               145s\n", "  217657 PPushes remaining with PInf 1.3941817e-05               150s\n", "  200508 PPushes remaining with PInf 0.0000000e+00               156s\n", "  173498 PPushes remaining with PInf 0.0000000e+00               160s\n", "  157922 PPushes remaining with PInf 0.0000000e+00               165s\n", "  144289 PPushes remaining with PInf 0.0000000e+00               170s\n", "  131501 PPushes remaining with PInf 0.0000000e+00               175s\n", "  114715 PPushes remaining with PInf 0.0000000e+00               180s\n", "   96453 PPushes remaining with PInf 0.0000000e+00               185s\n", "   76994 PPushes remaining with PInf 0.0000000e+00               190s\n", "   62749 PPushes remaining with PInf 0.0000000e+00               195s\n", "   47336 PPushes remaining with PInf 0.0000000e+00               200s\n", "   33285 PPushes remaining with PInf 0.0000000e+00               205s\n", "   16358 PPushes remaining with PInf 0.0000000e+00               210s\n", "    2800 PPushes remaining with PInf 0.0000000e+00               215s\n", "       0 PPushes remaining with PInf 0.0000000e+00               216s\n", "\n", "  Push phase complete: Pinf 0.0000000e+00, Dinf 3.7922175e+03    217s\n", "\n", "\n", "Root simplex log...\n", "\n", "Iteration    Objective       Primal Inf.    Dual Inf.      Time\n", "  964547    3.1893610e+10   0.000000e+00   3.792218e+03    217s\n", "  964748    3.1893610e+10   0.000000e+00   0.000000e+00    218s\n", "Concurrent spin time: 22.05s\n", "\n", "Solved with barrier\n", "  964815    3.1893610e+10   0.000000e+00   0.000000e+00    241s\n", "Extra simplex iterations after uncrush: 67\n", "\n", "Root relaxation: objective 3.189361e+10, 964815 iterations, 227.43 seconds (105.48 work units)\n", "Total elapsed time = 247.10s (DegenMoves)\n", "\n", "    Nodes    |    Current Node    |     Objective Bounds      |     Work\n", " Expl Unexpl |  Obj  Depth IntInf | Incumbent    BestBd   Gap | It/Node Time\n", "\n", "     0     0 3.1894e+10    0   72          - 3.1894e+10      -     -  249s\n", "H    0     0                    3.842878e+12 3.1894e+10  99.2%     -  389s\n", "     0     0 3.1894e+10    0   63 3.8429e+12 3.1894e+10  99.2%     -  400s\n", "H    0     0                    3.189518e+10 3.1894e+10  0.00%     -  522s\n", "\n", "Cutting planes:\n", "  Gomory: 9\n", "  MIR: 61\n", "  Flow cover: 69\n", "\n", "Explored 1 nodes (968870 simplex iterations) in 522.50 seconds (318.34 work units)\n", "Thread count was 8 (of 8 available processors)\n", "\n", "Solution count 2: 3.18952e+10 3.84288e+12 \n", "\n", "Optimal solution found (tolerance 1.00e-03)\n", "Best objective 3.189518036836e+10, best bound 3.189394738857e+10, gap 0.0039%\n", "月度_12找到最优解\n", "目标值 (总成本): 31,895,180,368.36\n", "月度_12结果已保存\n", "\n", "求解完成，总运行时间: 20629.83 秒 (343.83 分钟)\n"]}], "source": ["# ----------------- main.py ------------------\n", "\n", "# 映射选择到分解类型和热启动选项\n", "decomposition_map = {\n", "    '2': ('monthly', False),\n", "    '3': ('quarterly', False),\n", "    '4': ('quarterly', True),\n", "    '5': ('semiannual', False),\n", "    '6': ('semiannual', True),\n", "    '7': ('annual', False),\n", "    '8': ('annual', True)\n", "}\n", "\n", "choice = '2'  # 按月度分解求解\n", "\n", "if choice in decomposition_map:\n", "    decomposition_type, warm_start = decomposition_map[choice]\n", "    warm_start_text = \"热启动\" if warm_start else \"冷启动\"\n", "    print(f\"\\n开始按{decomposition_type}分解{warm_start_text}求解...\")\n", "    \n", "    start_time = time.time()\n", "    results = run_global_economic_dispatch(decomposition_type, warm_start)\n", "    end_time = time.time()\n", "    run_time = end_time - start_time\n", "    \n", "    print(f\"\\n求解完成，总运行时间: {run_time:.2f} 秒 ({run_time/60:.2f} 分钟)\")\n", "else:\n", "    print(\"无效选择，请选择1-8之间的数字\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Global_Energy_Internet", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 4}