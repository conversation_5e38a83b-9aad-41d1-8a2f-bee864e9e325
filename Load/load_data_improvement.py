#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全球电力负荷时序数据改进脚本
作者: AI Assistant
日期: 2025-07-28

功能：
1. 为16个缺失详细时序数据的国家生成更真实的小时级负荷曲线
2. 基于参考国家的平均负荷模式生成具有合理日夜变化的负荷曲线
3. 确保生成的时序数据总和严格等于原有的年度负荷总量
4. 添加适当的随机变化使负荷曲线更加真实
"""

import pandas as pd
import numpy as np
import warnings
from datetime import datetime
import os

# 忽略警告信息
warnings.filterwarnings('ignore')

class LoadDataImprover:
    """负荷数据改进器"""
    
    def __init__(self, input_file='Load_TimeSeries_2023.xlsx', output_file='Load_TimeSeries_2023_Improved.xlsx'):
        """
        初始化改进器
        
        参数:
        input_file: 输入Excel文件路径
        output_file: 输出Excel文件路径
        """
        self.input_file = input_file
        self.output_file = output_file
        
        # 需要改进的16个国家列表
        self.target_countries = [
            'BFA', 'COG', 'GRL', 'HKG', 'KNA', 'KOS', 'LCA', 'MAC', 
            'NRU', 'PSE', 'SSD', 'SUR', 'TLS', 'TON', 'TWN', 'WSM'
        ]
        
        # 参考国家列表（用于提取负荷模式）
        self.reference_countries = [
            'USA', 'CHN', 'DEU', 'JPN', 'GBR', 'FRA', 'ITA', 'ESP', 
            'CAN', 'AUS', 'BRA', 'IND', 'RUS', 'KOR', 'MEX', 'TUR'
        ]
        
        self.data = None
        self.improved_data = None
        self.reference_pattern = None
        
    def load_data(self):
        """加载原始数据"""
        print("正在加载原始数据...")
        try:
            self.data = pd.read_excel(self.input_file, engine='openpyxl', sheet_name=0)
            print(f"✓ 成功加载数据，形状: {self.data.shape}")
            print(f"✓ 包含 {len(self.data.columns)-1} 个国家的数据")
            return True
        except Exception as e:
            print(f"✗ 加载数据失败: {e}")
            return False
    
    def extract_reference_pattern(self):
        """从参考国家提取平均负荷模式"""
        print("\n正在提取参考国家的负荷模式...")
        
        # 收集所有参考国家的标准化负荷数据
        normalized_patterns = []
        valid_references = []
        
        for country in self.reference_countries:
            if country in self.data.columns:
                country_data = self.data[country].dropna()
                if len(country_data) == 8760 and country_data.std() > 0:  # 确保有完整数据且有变化
                    # 标准化到0-1范围
                    normalized = (country_data - country_data.min()) / (country_data.max() - country_data.min())
                    normalized_patterns.append(normalized.values)
                    valid_references.append(country)
        
        if len(normalized_patterns) == 0:
            raise ValueError("没有找到有效的参考国家数据")
        
        # 计算平均模式
        patterns_array = np.array(normalized_patterns)
        self.reference_pattern = np.mean(patterns_array, axis=0)
        
        print(f"✓ 成功提取 {len(valid_references)} 个参考国家的负荷模式")
        print(f"✓ 参考国家: {', '.join(valid_references)}")
        
        # 计算模式统计信息
        pattern_std = np.std(self.reference_pattern)
        pattern_cv = pattern_std / np.mean(self.reference_pattern)
        print(f"✓ 参考模式变异系数: {pattern_cv:.4f}")
        
        return True
    
    def generate_improved_curve(self, annual_total, country_code):
        """
        为单个国家生成改进的负荷曲线
        
        参数:
        annual_total: 年度负荷总量
        country_code: 国家代码
        
        返回:
        改进的8760小时负荷数据
        """
        # 基础模式：使用参考模式
        base_pattern = self.reference_pattern.copy()
        
        # 添加随机变化（5-15%的变化幅度）
        np.random.seed(hash(country_code) % 2**32)  # 为每个国家设置固定种子，确保可重复性
        
        # 生成平滑的随机变化
        random_variation = np.random.normal(0, 0.1, 8760)
        # 应用移动平均平滑随机变化
        window_size = 24  # 24小时窗口
        smoothed_variation = np.convolve(random_variation, np.ones(window_size)/window_size, mode='same')
        
        # 将随机变化添加到基础模式
        modified_pattern = base_pattern + smoothed_variation * 0.2  # 20%的随机变化强度
        
        # 确保所有值为正
        modified_pattern = np.maximum(modified_pattern, 0.1)
        
        # 缩放到目标年度总量
        current_sum = np.sum(modified_pattern)
        scaled_pattern = modified_pattern * (annual_total / current_sum)
        
        return scaled_pattern
    
    def improve_data(self):
        """改进所有目标国家的数据"""
        print("\n正在改进目标国家的负荷数据...")
        
        # 复制原始数据
        self.improved_data = self.data.copy()
        
        improvement_stats = []
        
        for i, country in enumerate(self.target_countries):
            if country in self.data.columns:
                print(f"正在处理 {country} ({i+1}/{len(self.target_countries)})...")
                
                # 获取原始年度总量
                original_data = self.data[country].dropna()
                annual_total = original_data.sum()
                original_cv = original_data.std() / original_data.mean() if original_data.mean() > 0 else 0
                
                # 生成改进的负荷曲线
                improved_curve = self.generate_improved_curve(annual_total, country)
                
                # 更新数据
                self.improved_data[country] = improved_curve
                
                # 验证总量保持不变
                new_total = improved_curve.sum()
                total_error = abs(new_total - annual_total) / annual_total * 100
                
                # 计算新的变异系数
                new_cv = np.std(improved_curve) / np.mean(improved_curve)
                
                improvement_stats.append({
                    'country': country,
                    'original_cv': original_cv,
                    'new_cv': new_cv,
                    'total_error_pct': total_error,
                    'annual_total': annual_total
                })
                
                print(f"  ✓ 原始变异系数: {original_cv:.6f}")
                print(f"  ✓ 新变异系数: {new_cv:.6f}")
                print(f"  ✓ 总量误差: {total_error:.6f}%")
            else:
                print(f"  ✗ 未找到国家 {country} 的数据")
        
        return improvement_stats
    
    def save_improved_data(self):
        """保存改进后的数据"""
        print(f"\n正在保存改进后的数据到 {self.output_file}...")
        try:
            self.improved_data.to_excel(self.output_file, index=False, engine='openpyxl')
            print(f"✓ 成功保存改进后的数据")
            return True
        except Exception as e:
            print(f"✗ 保存数据失败: {e}")
            return False
    
    def print_summary_statistics(self, improvement_stats):
        """打印改进效果统计摘要"""
        print("\n" + "="*60)
        print("改进效果统计摘要")
        print("="*60)
        
        if not improvement_stats:
            print("没有改进任何国家的数据")
            return
        
        # 计算统计信息
        original_cvs = [stat['original_cv'] for stat in improvement_stats]
        new_cvs = [stat['new_cv'] for stat in improvement_stats]
        total_errors = [stat['total_error_pct'] for stat in improvement_stats]
        
        print(f"改进国家数量: {len(improvement_stats)}")
        print(f"平均原始变异系数: {np.mean(original_cvs):.6f}")
        print(f"平均新变异系数: {np.mean(new_cvs):.6f}")
        print(f"平均总量误差: {np.mean(total_errors):.6f}%")
        print(f"最大总量误差: {np.max(total_errors):.6f}%")
        
        print("\n各国详细统计:")
        print("-" * 60)
        print(f"{'国家':<6} {'原始CV':<10} {'新CV':<10} {'总量误差%':<12} {'年度总量':<15}")
        print("-" * 60)
        
        for stat in improvement_stats:
            print(f"{stat['country']:<6} {stat['original_cv']:<10.6f} {stat['new_cv']:<10.6f} "
                  f"{stat['total_error_pct']:<12.6f} {stat['annual_total']:<15.2f}")
        
        print("="*60)
    
    def run(self):
        """运行完整的改进流程"""
        print("开始全球电力负荷时序数据改进流程")
        print("="*60)
        
        # 1. 加载数据
        if not self.load_data():
            return False
        
        # 2. 提取参考模式
        if not self.extract_reference_pattern():
            return False
        
        # 3. 改进数据
        improvement_stats = self.improve_data()
        
        # 4. 保存数据
        if not self.save_improved_data():
            return False
        
        # 5. 打印统计摘要
        self.print_summary_statistics(improvement_stats)
        
        print(f"\n✓ 改进流程完成！输出文件: {self.output_file}")
        return True


def main():
    """主函数"""
    print(f"脚本开始执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查输入文件是否存在
    input_file = 'Load_TimeSeries_2023.xlsx'
    if not os.path.exists(input_file):
        print(f"✗ 输入文件 {input_file} 不存在")
        return
    
    # 创建改进器并运行
    improver = LoadDataImprover(input_file, 'Load_TimeSeries_2023_Improved.xlsx')
    
    try:
        success = improver.run()
        if success:
            print("\n🎉 负荷数据改进成功完成！")
        else:
            print("\n❌ 负荷数据改进过程中出现错误")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n脚本结束执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    main()
