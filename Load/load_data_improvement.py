#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全球电力负荷时序数据改进脚本（基于相似性匹配）
作者: AI Assistant
日期: 2025-07-28

功能：
1. 基于GDP、人口等指标计算国家相似性
2. 为每个缺失详细时序数据的国家选择最相似的参考国家
3. 生成基于相似性加权的个性化负荷曲线
4. 确保生成的时序数据总和严格等于原有的年度负荷总量
5. 添加适当的随机变化使负荷曲线更加真实
"""

import pandas as pd
import numpy as np
import warnings
from datetime import datetime
import os
from sklearn.preprocessing import StandardScaler
from sklearn.metrics.pairwise import euclidean_distances

# 忽略警告信息
warnings.filterwarnings('ignore')

class LoadDataImprover:
    """基于相似性匹配的负荷数据改进器"""

    def __init__(self, input_file='Load_TimeSeries_2023.xlsx', output_file='Load_TimeSeries_2023_Improved.xlsx',
                 gdp_file='GDP_INT-Export-07-28-2025_17-43-58.csv',
                 pop_file='Population_INT-Export-07-28-2025_17-44-34.csv'):
        """
        初始化改进器

        参数:
        input_file: 输入Excel文件路径
        output_file: 输出Excel文件路径
        gdp_file: GDP数据文件路径
        pop_file: 人口数据文件路径
        """
        self.input_file = input_file
        self.output_file = output_file
        self.gdp_file = gdp_file
        self.pop_file = pop_file

        # 需要改进的16个国家列表
        self.target_countries = [
            'BFA', 'COG', 'GRL', 'HKG', 'KNA', 'KOS', 'LCA', 'MAC',
            'NRU', 'PSE', 'SSD', 'SUR', 'TLS', 'TON', 'TWN', 'WSM'
        ]

        # 相似性匹配参数
        self.num_reference_countries = 8  # 每个目标国家选择的参考国家数量
        self.min_similarity_threshold = 0.1  # 最低相似度阈值

        # 数据存储
        self.load_data = None
        self.gdp_data = None
        self.pop_data = None
        self.improved_data = None
        self.similarity_results = {}  # 存储每个国家的相似性匹配结果
        
    def load_all_data(self):
        """加载所有数据文件"""
        print("正在加载所有数据文件...")

        # 加载负荷数据
        try:
            self.load_data = pd.read_excel(self.input_file, engine='openpyxl', sheet_name=0)
            print(f"✓ 成功加载负荷数据，形状: {self.load_data.shape}")
        except Exception as e:
            print(f"✗ 加载负荷数据失败: {e}")
            return False

        # 加载GDP数据
        try:
            gdp_raw = pd.read_csv(self.gdp_file, skiprows=3)
            gdp_raw.columns = ['API', 'Country'] + [str(year) for year in range(1980, 2025)]
            self.gdp_data = self._process_auxiliary_data(gdp_raw, 'INTL.4701-34-', '2023')
            print(f"✓ 成功加载GDP数据，包含 {len(self.gdp_data)} 个国家")
        except Exception as e:
            print(f"✗ 加载GDP数据失败: {e}")
            return False

        # 加载人口数据
        try:
            pop_raw = pd.read_csv(self.pop_file, skiprows=3)
            pop_raw.columns = ['API', 'Country'] + [str(year) for year in range(1980, 2025)]
            self.pop_data = self._process_auxiliary_data(pop_raw, 'INTL.4702-33-', '2023')
            print(f"✓ 成功加载人口数据，包含 {len(self.pop_data)} 个国家")
        except Exception as e:
            print(f"✗ 加载人口数据失败: {e}")
            return False

        return True

    def _process_auxiliary_data(self, raw_data, api_prefix, target_year):
        """处理辅助数据（GDP或人口）"""
        processed_data = {}

        for _, row in raw_data.iterrows():
            api = row['API']
            if pd.notna(api) and api_prefix in str(api):
                # 提取国家代码
                country_code = str(api).split('-')[2]

                # 获取目标年份的数据
                if target_year in raw_data.columns:
                    value = row[target_year]
                    if pd.notna(value) and str(value) != 'NA' and str(value) != '':
                        try:
                            processed_data[country_code] = float(value)
                        except (ValueError, TypeError):
                            continue

        return processed_data
    
    def calculate_similarity(self, target_country):
        """计算目标国家与所有候选参考国家的相似性"""
        # 获取目标国家的指标
        target_indicators = self._get_country_indicators(target_country)
        if target_indicators is None:
            print(f"  ✗ {target_country} 缺少必要的经济指标数据")
            return None

        # 获取所有候选参考国家
        candidate_countries = self._get_candidate_countries()
        if len(candidate_countries) == 0:
            print(f"  ✗ 没有找到有效的候选参考国家")
            return None

        # 计算相似性
        similarities = []
        all_indicators = []
        country_codes = []

        # 收集所有国家的指标
        for country in candidate_countries:
            indicators = self._get_country_indicators(country)
            if indicators is not None:
                all_indicators.append(indicators)
                country_codes.append(country)

        if len(all_indicators) == 0:
            return None

        # 标准化指标
        all_indicators = np.array(all_indicators)
        target_indicators = np.array(target_indicators).reshape(1, -1)

        scaler = StandardScaler()
        all_indicators_scaled = scaler.fit_transform(all_indicators)
        target_indicators_scaled = scaler.transform(target_indicators)

        # 计算欧氏距离并转换为相似度
        distances = euclidean_distances(target_indicators_scaled, all_indicators_scaled)[0]
        similarities = 1 / (1 + distances)

        # 创建相似性结果
        similarity_results = list(zip(country_codes, similarities))
        similarity_results.sort(key=lambda x: x[1], reverse=True)  # 按相似度降序排列

        return similarity_results

    def _get_country_indicators(self, country_code):
        """获取国家的经济社会指标"""
        # 检查数据可用性
        if country_code not in self.gdp_data or country_code not in self.pop_data:
            return None

        gdp = self.gdp_data[country_code]
        population = self.pop_data[country_code]

        # 计算指标
        gdp_per_capita = gdp / population if population > 0 else 0

        # 获取负荷密度（如果有负荷数据）
        load_density = 0
        if country_code in self.load_data.columns:
            annual_load = self.load_data[country_code].sum()
            load_density = annual_load / population if population > 0 else 0

        return [
            np.log(gdp + 1),  # 对数GDP（避免极值影响）
            np.log(population + 1),  # 对数人口
            np.log(gdp_per_capita + 1),  # 对数人均GDP
            np.log(load_density + 1)  # 对数负荷密度
        ]

    def _get_candidate_countries(self):
        """获取候选参考国家列表"""
        candidates = []

        for country in self.load_data.columns[1:]:  # 排除Hours列
            if country not in self.target_countries:  # 排除目标国家
                # 检查负荷数据质量
                country_data = self.load_data[country].dropna()
                if len(country_data) == 8760 and country_data.std() > 0:
                    # 检查变异系数是否合理
                    cv = country_data.std() / country_data.mean()
                    if cv > 0.05:  # 变异系数大于5%
                        candidates.append(country)

        return candidates
    
    def generate_similarity_based_curve(self, annual_total, country_code):
        """
        基于相似性匹配为单个国家生成改进的负荷曲线

        参数:
        annual_total: 年度负荷总量
        country_code: 国家代码

        返回:
        改进的8760小时负荷数据
        """
        # 计算相似性并选择参考国家
        similarity_results = self.calculate_similarity(country_code)

        if similarity_results is None:
            # 如果无法计算相似性，使用默认方法
            print(f"  ⚠ {country_code} 使用默认参考模式")
            return self._generate_default_curve(annual_total, country_code)

        # 选择最相似的参考国家
        selected_references = similarity_results[:self.num_reference_countries]

        # 过滤掉相似度过低的国家
        selected_references = [(country, sim) for country, sim in selected_references
                             if sim >= self.min_similarity_threshold]

        if len(selected_references) == 0:
            print(f"  ⚠ {country_code} 没有足够相似的参考国家，使用默认方法")
            return self._generate_default_curve(annual_total, country_code)

        # 存储相似性结果用于报告
        self.similarity_results[country_code] = selected_references

        # 计算加权平均模式
        weighted_pattern = self._calculate_weighted_pattern(selected_references)

        # 添加个性化随机变化
        personalized_pattern = self._add_personalized_variation(weighted_pattern, country_code)

        # 缩放到目标年度总量
        current_sum = np.sum(personalized_pattern)
        scaled_pattern = personalized_pattern * (annual_total / current_sum)

        return scaled_pattern

    def _calculate_weighted_pattern(self, selected_references):
        """计算基于相似度权重的负荷模式"""
        # 提取权重
        countries, similarities = zip(*selected_references)
        weights = np.array(similarities)
        weights = weights / np.sum(weights)  # 归一化权重

        # 收集并标准化参考国家的负荷数据
        patterns = []
        for country in countries:
            if country in self.load_data.columns:
                country_data = self.load_data[country].dropna()
                if len(country_data) == 8760:
                    # 标准化到0-1范围
                    normalized = (country_data - country_data.min()) / (country_data.max() - country_data.min())
                    patterns.append(normalized.values)

        if len(patterns) == 0:
            raise ValueError("没有有效的参考国家负荷数据")

        # 计算加权平均
        patterns_array = np.array(patterns)
        weighted_pattern = np.average(patterns_array, axis=0, weights=weights[:len(patterns)])

        return weighted_pattern

    def _add_personalized_variation(self, base_pattern, country_code):
        """添加个性化随机变化"""
        # 为每个国家设置固定种子，确保可重复性
        np.random.seed(hash(country_code) % 2**32)

        # 生成平滑的随机变化
        random_variation = np.random.normal(0, 0.1, 8760)
        # 应用移动平均平滑随机变化
        window_size = 24  # 24小时窗口
        smoothed_variation = np.convolve(random_variation, np.ones(window_size)/window_size, mode='same')

        # 将随机变化添加到基础模式
        variation_strength = 0.15  # 15%的随机变化强度
        modified_pattern = base_pattern + smoothed_variation * variation_strength

        # 确保所有值为正
        modified_pattern = np.maximum(modified_pattern, 0.1)

        return modified_pattern

    def _generate_default_curve(self, annual_total, country_code):
        """生成默认负荷曲线（当无法进行相似性匹配时使用）"""
        # 使用简单的典型日负荷模式
        typical_daily_pattern = np.array([
            0.6, 0.55, 0.5, 0.48, 0.5, 0.55, 0.65, 0.75, 0.85, 0.9, 0.95, 1.0,
            1.0, 0.98, 0.95, 0.98, 1.05, 1.15, 1.25, 1.3, 1.2, 1.0, 0.8, 0.7
        ])

        # 重复365天
        annual_pattern = np.tile(typical_daily_pattern, 365)

        # 添加随机变化
        np.random.seed(hash(country_code) % 2**32)
        random_variation = np.random.normal(0, 0.1, 8760)
        smoothed_variation = np.convolve(random_variation, np.ones(24)/24, mode='same')

        modified_pattern = annual_pattern + smoothed_variation * 0.2
        modified_pattern = np.maximum(modified_pattern, 0.1)

        # 缩放到目标总量
        current_sum = np.sum(modified_pattern)
        scaled_pattern = modified_pattern * (annual_total / current_sum)

        return scaled_pattern
    
    def improve_data(self):
        """改进所有目标国家的数据"""
        print("\n正在改进目标国家的负荷数据...")

        # 复制原始数据
        self.improved_data = self.load_data.copy()

        improvement_stats = []

        for i, country in enumerate(self.target_countries):
            if country in self.load_data.columns:
                print(f"正在处理 {country} ({i+1}/{len(self.target_countries)})...")

                # 获取原始年度总量
                original_data = self.load_data[country].dropna()
                annual_total = original_data.sum()
                original_cv = original_data.std() / original_data.mean() if original_data.mean() > 0 else 0

                # 生成改进的负荷曲线
                improved_curve = self.generate_similarity_based_curve(annual_total, country)

                # 更新数据
                self.improved_data[country] = improved_curve

                # 验证总量保持不变
                new_total = improved_curve.sum()
                total_error = abs(new_total - annual_total) / annual_total * 100

                # 计算新的变异系数
                new_cv = np.std(improved_curve) / np.mean(improved_curve)

                # 获取相似性信息
                similarity_info = ""
                if country in self.similarity_results:
                    top_refs = self.similarity_results[country][:3]  # 显示前3个最相似的国家
                    similarity_info = ", ".join([f"{ref}({sim:.3f})" for ref, sim in top_refs])

                improvement_stats.append({
                    'country': country,
                    'original_cv': original_cv,
                    'new_cv': new_cv,
                    'total_error_pct': total_error,
                    'annual_total': annual_total,
                    'similarity_info': similarity_info
                })

                print(f"  ✓ 原始变异系数: {original_cv:.6f}")
                print(f"  ✓ 新变异系数: {new_cv:.6f}")
                print(f"  ✓ 总量误差: {total_error:.6f}%")
                if similarity_info:
                    print(f"  ✓ 主要参考国家: {similarity_info}")
            else:
                print(f"  ✗ 未找到国家 {country} 的数据")

        return improvement_stats
    
    def save_improved_data(self):
        """保存改进后的数据"""
        print(f"\n正在保存改进后的数据到 {self.output_file}...")
        try:
            self.improved_data.to_excel(self.output_file, index=False, engine='openpyxl')
            print(f"✓ 成功保存改进后的数据")
            return True
        except Exception as e:
            print(f"✗ 保存数据失败: {e}")
            return False
    
    def print_summary_statistics(self, improvement_stats):
        """打印改进效果统计摘要"""
        print("\n" + "="*80)
        print("改进效果统计摘要（基于相似性匹配）")
        print("="*80)

        if not improvement_stats:
            print("没有改进任何国家的数据")
            return

        # 计算统计信息
        original_cvs = [stat['original_cv'] for stat in improvement_stats]
        new_cvs = [stat['new_cv'] for stat in improvement_stats]
        total_errors = [stat['total_error_pct'] for stat in improvement_stats]

        print(f"改进国家数量: {len(improvement_stats)}")
        print(f"平均原始变异系数: {np.mean(original_cvs):.6f}")
        print(f"平均新变异系数: {np.mean(new_cvs):.6f}")
        print(f"变异系数改进倍数: {np.mean(new_cvs)/np.mean(original_cvs) if np.mean(original_cvs) > 0 else float('inf'):.1f}x")
        print(f"平均总量误差: {np.mean(total_errors):.6f}%")
        print(f"最大总量误差: {np.max(total_errors):.6f}%")

        print("\n各国详细统计:")
        print("-" * 80)
        print(f"{'国家':<6} {'原始CV':<10} {'新CV':<10} {'总量误差%':<12} {'年度总量':<15} {'主要参考国家':<20}")
        print("-" * 80)

        for stat in improvement_stats:
            similarity_info = stat.get('similarity_info', 'N/A')[:18]  # 限制长度
            print(f"{stat['country']:<6} {stat['original_cv']:<10.6f} {stat['new_cv']:<10.6f} "
                  f"{stat['total_error_pct']:<12.6f} {stat['annual_total']:<15.2f} {similarity_info:<20}")

        print("="*80)

        # 打印相似性匹配详情
        print("\n相似性匹配详情:")
        print("-" * 80)
        for stat in improvement_stats:
            country = stat['country']
            if country in self.similarity_results:
                print(f"\n{country} 的参考国家（按相似度排序）:")
                for i, (ref_country, similarity) in enumerate(self.similarity_results[country][:5]):
                    print(f"  {i+1}. {ref_country}: 相似度 {similarity:.4f}")
            else:
                print(f"\n{country}: 使用默认模式（无相似性数据）")

        print("="*80)
    
    def run(self):
        """运行完整的改进流程"""
        print("开始全球电力负荷时序数据改进流程")
        print("="*60)
        
        # 1. 加载数据
        if not self.load_data():
            return False
        
        # 2. 提取参考模式
        if not self.extract_reference_pattern():
            return False
        
        # 3. 改进数据
        improvement_stats = self.improve_data()
        
        # 4. 保存数据
        if not self.save_improved_data():
            return False
        
        # 5. 打印统计摘要
        self.print_summary_statistics(improvement_stats)
        
        print(f"\n✓ 改进流程完成！输出文件: {self.output_file}")
        return True


def main():
    """主函数"""
    print(f"脚本开始执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查输入文件是否存在
    input_file = 'Load_TimeSeries_2023.xlsx'
    if not os.path.exists(input_file):
        print(f"✗ 输入文件 {input_file} 不存在")
        return
    
    # 创建改进器并运行
    improver = LoadDataImprover(input_file, 'Load_TimeSeries_2023_Improved.xlsx')
    
    try:
        success = improver.run()
        if success:
            print("\n🎉 负荷数据改进成功完成！")
        else:
            print("\n❌ 负荷数据改进过程中出现错误")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n脚本结束执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    main()
