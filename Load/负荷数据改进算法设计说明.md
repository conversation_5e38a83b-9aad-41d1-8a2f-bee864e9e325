# 全球电力负荷时序数据改进算法设计说明（基于相似性匹配）

## 1. 问题背景与目标

### 1.1 现状问题
- **数据文件**: `Load_TimeSeries_2023.xlsx` 包含全球202个国家的8760小时负荷数据
- **问题国家**: 16个国家（BFA, COG, GRL, HKG, KNA, KOS, LCA, MAC, NRU, PSE, SSD, SUR, TLS, TON, TWN, WSM）只有年度总量
- **当前处理**: 年度负荷被简单平均分配到所有8760小时，导致完全平坦的负荷曲线
- **问题影响**: 缺乏真实的日夜变化、季节变化，不符合实际电力系统运行规律

### 1.2 改进目标
1. **生成真实负荷模式**: 为16个国家创建具有合理日夜变化的负荷曲线
2. **基于相似性匹配**: 根据GDP、人口等指标找到相似的参考国家
3. **保持数据完整性**: 确保年度负荷总量严格不变
4. **增加合理变异**: 从变异系数0提升到合理水平
5. **保持数据格式**: 维持原有Excel文件结构和其他国家数据不变

## 2. 算法设计思路

### 2.1 核心思想
**基于经济社会相似性的负荷模式匹配**

```
加载辅助数据 → 计算相似性指标 → 选择相似参考国家 → 提取加权模式 → 个性化调整 → 生成负荷曲线
```

### 2.2 数据需求
- **负荷数据**: `Load_TimeSeries_2023.xlsx`
- **GDP数据**: `GDP_INT-Export-07-28-2025_17-43-58.csv`
- **人口数据**: `Population_INT-Export-07-28-2025_17-44-34.csv`
- **其他可能指标**: 工业化程度、城市化率、气候区域等

### 2.3 算法流程

#### 步骤1: 数据预处理与相似性计算
- **辅助数据加载**: 读取GDP、人口等经济社会指标
- **数据标准化**: 将所有指标标准化到相同量级
  ```
  标准化值 = (原始值 - 均值) / 标准差
  ```
- **相似性度量**: 计算目标国家与所有有完整负荷数据国家的相似度
  ```
  相似度 = 1 / (1 + 欧氏距离(标准化指标向量))
  ```

#### 步骤2: 动态参考国家选择
- **候选国家筛选**: 从186个有完整负荷数据的国家中筛选
  - 负荷数据完整性检查（8760小时，变异系数>0.05）
  - 辅助数据可用性检查
- **相似性排序**: 按相似度降序排列
- **参考国家选择**: 选择前5-10个最相似的国家作为参考
- **权重分配**: 根据相似度分配权重
  ```
  权重[i] = 相似度[i] / sum(所有选中国家的相似度)
  ```

#### 步骤3: 加权模式提取
- **数据标准化**: 将每个参考国家的负荷数据标准化到[0,1]区间
  ```
  标准化值 = (原始值 - 最小值) / (最大值 - 最小值)
  ```
- **加权平均**: 根据相似度权重计算加权平均模式
  ```
  加权模式[i] = Σ(权重[j] × 标准化负荷[j][i])  # j为参考国家，i为小时
  ```
- **模式验证**: 检查生成模式的合理性（变异系数、峰谷比等）

#### 步骤4: 个性化调整
- **随机种子设置**: 为每个国家设置基于国家代码的固定随机种子
  ```python
  np.random.seed(hash(country_code) % 2**32)
  ```
- **随机变化生成**: 生成平滑的随机变化
  ```python
  random_variation = np.random.normal(0, 0.1, 8760)
  smoothed_variation = 24小时移动平均(random_variation)
  ```
- **模式修正**: 将随机变化叠加到加权基础模式
  ```python
  修正模式 = 加权模式 + smoothed_variation × 调整强度
  ```

#### 步骤5: 负荷曲线生成
- **正值保证**: 确保所有负荷值为正
  ```python
  修正模式 = max(修正模式, 0.1)
  ```
- **总量缩放**: 缩放到目标年度总量
  ```python
  最终负荷曲线 = 修正模式 × (目标年度总量 / 修正模式总和)
  ```

### 2.4 关键技术特点

#### 2.4.1 相似性匹配策略
- **多维度指标**: 综合考虑GDP、人口、人均GDP等经济指标
- **标准化处理**: 消除不同指标量级差异的影响
- **动态选择**: 为每个目标国家选择最相似的参考国家集合
- **权重分配**: 根据相似度分配不同权重，相似度越高权重越大

#### 2.4.2 模式提取优化
- **质量筛选**: 只选择有完整数据且有变化的参考国家
- **加权平均**: 避免简单平均可能带来的偏差
- **模式验证**: 检查生成模式的电力系统合理性

#### 2.4.3 个性化调整
- **固定种子**: 确保结果可重复性
- **平滑处理**: 24小时移动平均避免过度随机
- **自适应强度**: 根据参考模式质量调整随机变化强度

#### 2.4.4 数据完整性保证
- **精确缩放**: 数学上保证总量完全一致
- **原数据保护**: 只修改目标16个国家，其他186个国家保持不变

## 3. 相似性指标体系

### 3.1 核心指标
- **GDP总量**: 反映经济规模
- **人口总量**: 反映用电需求基数
- **人均GDP**: 反映发展水平和用电强度
- **负荷密度**: 年度负荷总量/人口，反映电气化程度

### 3.2 扩展指标（如数据可用）
- **工业增加值占比**: 反映产业结构
- **城市化率**: 影响负荷模式
- **气候区域**: 影响季节性负荷
- **地理位置**: 纬度影响日照和温度模式

### 3.3 相似性计算方法
```python
# 标准化指标
indicators_normalized = (indicators - mean) / std

# 计算欧氏距离
distance = sqrt(sum((target_indicators - ref_indicators)^2))

# 转换为相似度
similarity = 1 / (1 + distance)
```

## 4. 预期效果分析

### 4.1 个性化匹配效果
- **小国匹配小国**: 如KNA可能匹配到类似的小岛国
- **发展中国家匹配**: 如BFA可能匹配到相似发展水平的非洲国家
- **发达地区匹配**: 如HKG、MAC可能匹配到发达城市经济体

### 4.2 负荷模式特征
- **因地制宜**: 不同类型国家获得不同的负荷模式
- **合理变异**: 变异系数根据参考国家自然形成
- **真实性提升**: 基于相似国家的真实数据模式

### 4.3 数据质量提升
- **总量误差**: 0.000000%（数学精确）
- **模式真实性**: 基于相似国家数据的加权模式
- **个体差异**: 每个国家有基于其特征的独特负荷曲线

## 5. 算法优势

### 5.1 科学性提升
- **相似性匹配**: 基于经济社会指标的科学匹配，而非主观选择
- **个性化模式**: 每个国家获得基于其特征的独特负荷模式
- **数据驱动**: 完全基于客观数据，减少人为偏差

### 5.2 适应性强
- **动态选择**: 根据数据可用性自动调整参考国家
- **权重优化**: 相似度越高的国家影响越大
- **多维度考虑**: 综合多个指标，避免单一指标的局限性

### 5.3 可扩展性
- **指标可扩展**: 可根据数据可用性增加更多相似性指标
- **方法可复用**: 可应用于其他年份或其他类型的时序数据
- **参数可调**: 相似性权重、参考国家数量等可灵活调整

## 6. 实施步骤

### 6.1 数据准备
1. **检查辅助数据**: 确认GDP、人口数据的完整性和格式
2. **数据清洗**: 处理缺失值、异常值
3. **国家代码统一**: 确保负荷数据和辅助数据的国家代码一致

### 6.2 算法实现
1. **相似性计算模块**: 实现多维度相似性计算
2. **动态选择模块**: 为每个目标国家选择最佳参考国家
3. **加权模式提取**: 实现基于相似度的加权平均
4. **质量验证模块**: 检查生成模式的合理性

### 6.3 参数调优
- **参考国家数量**: 建议5-10个，平衡质量和多样性
- **相似性阈值**: 设置最低相似度要求
- **权重分布**: 调整相似度到权重的转换函数

## 7. 预期挑战与解决方案

### 7.1 数据质量问题
- **挑战**: GDP、人口数据可能存在缺失或不准确
- **解决**: 实现多种相似性指标，当某些指标缺失时自动降权

### 7.2 相似性计算复杂性
- **挑战**: 不同指标的重要性权重难以确定
- **解决**: 实现多种权重方案，提供参数调整接口

### 7.3 计算效率
- **挑战**: 需要计算16×186的相似性矩阵
- **解决**: 优化算法实现，预计算可复用的中间结果

## 8. 验证方案

### 8.1 内部验证
- **总量一致性**: 验证年度负荷总量完全保持
- **模式合理性**: 检查变异系数、峰谷比等指标
- **相似性验证**: 确认选择的参考国家确实相似

### 8.2 外部验证
- **专家评估**: 请电力系统专家评估生成模式的合理性
- **对比分析**: 与已知真实数据的国家进行对比
- **敏感性分析**: 测试参数变化对结果的影响

---

**总结**: 改进后的算法通过引入经济社会相似性匹配，实现了更科学、更个性化的负荷模式生成，显著提升了数据的真实性和适用性。每个国家都能获得基于其自身特征的最适合的负荷模式，而不是使用统一的"大国平均"模式。
