name: global_ps_planning
channels:
  - defaults
  - https://repo.anaconda.com/pkgs/main
  - https://repo.anaconda.com/pkgs/r
dependencies:
  - bzip2=1.0.8=h80987f9_6
  - ca-certificates=2025.2.25=hca03da5_0
  - expat=2.7.1=h313beb8_0
  - libcxx=17.0.6=he5c5206_4
  - libffi=3.4.4=hca03da5_1
  - ncurses=6.4=h313beb8_0
  - openssl=3.0.16=h02f6b3c_0
  - pip=25.1=pyhc872135_2
  - python=3.9.23=h99c0cbc_0
  - readline=8.2=h1a28f6b_0
  - setuptools=78.1.1=py39hca03da5_0
  - sqlite=3.45.3=h80987f9_0
  - tk=8.6.14=h6ba3021_1
  - wheel=0.45.1=py39hca03da5_0
  - xz=5.6.4=h80987f9_1
  - zlib=1.2.13=h18a0788_1
  - pip:
      - contourpy==1.3.0
      - cycler==0.12.1
      - fonttools==4.58.5
      - gurobipy==12.0.2
      - importlib-resources==6.5.2
      - kiwisolver==1.4.7
      - matplotlib==3.9.4
      - multiprocessing-logging==0.3.4
      - numpy==2.0.2
      - packaging==25.0
      - pandas==2.3.1
      - pillow==11.3.0
      - pyparsing==3.2.3
      - python-dateutil==2.9.0.post0
      - pytz==2025.2
      - pyyaml==6.0.2
      - scipy==1.13.1
      - seaborn==0.13.2
      - six==1.17.0
      - tqdm==4.67.1
      - tzdata==2025.2
      - zipp==3.23.0
prefix: /opt/anaconda3/envs/global_ps_planning
