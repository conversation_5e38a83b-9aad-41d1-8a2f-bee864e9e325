"""
全球电力系统扩展规划模型约束条件模块
Model Constraints Module

实现所有数学模型中定义的约束条件
"""

import gurobipy as gp
from gurobipy import GRB
import logging

logger = logging.getLogger(__name__)

class ConstraintBuilder:
    """约束条件构建器"""
    
    def __init__(self, model_instance):
        """
        初始化约束构建器
        
        Args:
            model_instance: GlobalPowerModel实例
        """
        self.model_instance = model_instance
        self.model = model_instance.model
        self.variables = model_instance.variables
        self.data = model_instance.data
        self.params = model_instance.params
    
    def add_all_constraints(self):
        """添加所有约束条件"""
        logger.info("开始添加约束条件...")
        
        # 公式(2): 电力供需平衡约束
        self.add_power_balance_constraints()
        
        # 公式(3-12): 火电约束
        self.add_thermal_constraints()
        
        # 公式(13-18): 可再生能源约束
        self.add_renewable_constraints()
        
        # 公式(19): 生物质能约束
        self.add_biomass_constraints()
        
        # 公式(20-24): 核电和地热约束
        self.add_nuclear_geo_constraints()
        
        # 公式(25-33): 水电约束
        self.add_hydro_constraints()
        
        # 公式(34-38): 储能约束
        self.add_storage_constraints()
        
        # 公式(39): 输电约束
        self.add_transmission_constraints()
        
        # 公式(40): 可靠性约束
        self.add_reliability_constraints()
        
        # 公式(41-46): 变量非负性约束
        # （变量定义时已处理）
        
        logger.info("所有约束条件添加完成")
    
    def add_power_balance_constraints(self):
        """公式(2): 电力供需平衡约束"""
        countries = self.params.countries
        T = range(self.params.time_periods)
        
        for n in countries:
            for t in T:
                # 发电侧
                generation = 0
                
                # 火电出力
                for tech in self.params.thermal_techs:
                    generation += self.variables[f'p_{tech}'][n, t]
                
                # 可再生能源出力
                for tech in self.params.renewable_techs:
                    generation += self.variables[f'p_{tech}'][n, t]
                
                # 水电出力
                # 水库式水电出力（站点级别求和）
                for h in self.params.hydro_stations.get(n, []):
                    generation += self.variables['p_hydro_res'][n, h, t]
                
                # 径流式水电出力（国家级别）
                generation += self.variables['p_hydro_ror'][n, t]
                
                # 其他发电
                generation += self.variables['p_geo'][n, t]
                generation += self.variables['p_nuclear'][n, t]
                
                # 储能出力
                for tech in self.params.storage_techs:
                    generation += self.variables[f'p_dis_{tech}'][n, t]
                    generation -= self.variables[f'p_ch_{tech}'][n, t]
                
                # 输电（简化处理）
                transmission = 0
                for m in countries:
                    if m != n:
                        transmission += self.variables['p_trans'][m, n, t]
                        transmission -= self.variables['p_trans'][n, m, t]
                
                # 缺电
                generation += self.variables['shed'][n, t]
                
                # 需求（需要从数据中获取）
                demand = self.data.get('demand', {}).get((n, t), 0)
                
                # 平衡约束
                self.model.addConstr(
                    generation == demand + self.variables['slack'][n, t],
                    name=f'power_balance_{n}_{t}'
                )
    
    def add_thermal_constraints(self):
        """公式(3-12): 火电约束"""
        countries = self.params.countries
        T = range(self.params.time_periods)
        
        for tech in self.params.thermal_techs:
            for n in countries:
                # 现有装机容量
                existing_cap = self.data.get('existing_capacity', {}).get(f'{tech}_{n}', 0)
                
                # 技术参数
                min_up_time = self.data.get('min_up_time', {}).get(f'{tech}_{n}', 4)  # UT_n^g
                min_down_time = self.data.get('min_down_time', {}).get(f'{tech}_{n}', 2)  # DT_n^g
                
                for t in T:
                    # 公式(3): 火电运行变量约束
                    total_capacity = existing_cap + self.variables[f'I_{tech}'][n]
                    
                    self.model.addConstr(
                        self.variables[f'p_bar_{tech}'][n, t] <= total_capacity,
                        name=f'thermal_capacity_{tech}_{n}_{t}'
                    )
                    
                    self.model.addConstr(
                        self.variables[f'x_su_{tech}'][n, t] <= total_capacity,
                        name=f'thermal_startup_{tech}_{n}_{t}'
                    )
                    
                    self.model.addConstr(
                        self.variables[f'x_sd_{tech}'][n, t] <= total_capacity,
                        name=f'thermal_shutdown_{tech}_{n}_{t}'
                    )
                    
                    # 公式(4): 在线容量平衡约束
                    if t == 0:
                        # 初始条件
                        initial_online = self.data.get('initial_online', {}).get(f'{tech}_{n}', 0)
                        self.model.addConstr(
                            self.variables[f'p_bar_{tech}'][n, t] == 
                            initial_online + self.variables[f'x_su_{tech}'][n, t] - 
                            self.variables[f'x_sd_{tech}'][n, t],
                            name=f'online_balance_init_{tech}_{n}_{t}'
                        )
                    else:
                        self.model.addConstr(
                            self.variables[f'p_bar_{tech}'][n, t] == 
                            self.variables[f'p_bar_{tech}'][n, t-1] + 
                            self.variables[f'x_su_{tech}'][n, t] - 
                            self.variables[f'x_sd_{tech}'][n, t],
                            name=f'online_balance_{tech}_{n}_{t}'
                        )
                    
                    # 公式(5): 出力上下限约束
                    min_output_ratio = self.data.get('min_output', {}).get(f'{tech}_{n}', 0.3)
                    max_output_ratio = self.data.get('max_output', {}).get(f'{tech}_{n}', 1.0)
                    
                    self.model.addConstr(
                        self.variables[f'p_{tech}'][n, t] >= 
                        min_output_ratio * self.variables[f'p_bar_{tech}'][n, t],
                        name=f'min_output_{tech}_{n}_{t}'
                    )
                    
                    self.model.addConstr(
                        self.variables[f'p_{tech}'][n, t] <= 
                        max_output_ratio * self.variables[f'p_bar_{tech}'][n, t],
                        name=f'max_output_{tech}_{n}_{t}'
                    )
                    
                    # 公式(6-7): 爬坡约束
                    if t > 0:
                        ramp_up_rate = self.data.get('ramp_up', {}).get(f'{tech}_{n}', 0.5)
                        ramp_down_rate = self.data.get('ramp_down', {}).get(f'{tech}_{n}', 0.5)
                        
                        self.model.addConstr(
                            self.variables[f'p_{tech}'][n, t] - 
                            self.variables[f'p_{tech}'][n, t-1] <= 
                            ramp_up_rate * self.variables[f'p_bar_{tech}'][n, t],
                            name=f'ramp_up_{tech}_{n}_{t}'
                        )
                        
                        self.model.addConstr(
                            self.variables[f'p_{tech}'][n, t-1] - 
                            self.variables[f'p_{tech}'][n, t] <= 
                            ramp_down_rate * self.variables[f'p_bar_{tech}'][n, t],
                            name=f'ramp_down_{tech}_{n}_{t}'
                        )
                    
                    # 公式(8): 火电功率输出补充约束
                    # p_{n,t}^g ≤ σ̄_n^g · (p̄_{n,t}^g - x_{n,t}^{g,sd}) + σ_n^g · (x_{n,t}^{g,sd} + x_{n,t}^{g,su})
                    max_sigma = max_output_ratio  # σ̄_n^g
                    min_sigma = min_output_ratio  # σ_n^g
                    
                    self.model.addConstr(
                        self.variables[f'p_{tech}'][n, t] <= 
                        max_sigma * (self.variables[f'p_bar_{tech}'][n, t] - self.variables[f'x_sd_{tech}'][n, t]) +
                        min_sigma * (self.variables[f'x_sd_{tech}'][n, t] + self.variables[f'x_su_{tech}'][n, t]),
                        name=f'power_output_supplement_{tech}_{n}_{t}'
                    )
                
                # 公式(9): 火电停机约束
                # 0 ≤ x_{n,1}^{g,sd} ≤ p̄_{n,0}^g
                if self.params.time_periods > 0:
                    initial_online = self.data.get('initial_online', {}).get(f'{tech}_{n}', 0)
                    self.model.addConstr(
                        self.variables[f'x_sd_{tech}'][n, 0] >= 0,
                        name=f'shutdown_lower_bound_{tech}_{n}_0'
                    )
                    self.model.addConstr(
                        self.variables[f'x_sd_{tech}'][n, 0] <= initial_online,
                        name=f'shutdown_upper_bound_{tech}_{n}_0'
                    )
                
                # 公式(10): 火电停机约束（分段函数）
                for t in range(1, self.params.time_periods):
                    self.model.addConstr(
                        self.variables[f'x_sd_{tech}'][n, t] >= 0,
                        name=f'shutdown_lower_bound_{tech}_{n}_{t}'
                    )
                    
                    if t < min_up_time:
                        # 1 ≤ t < UT_n^g: p̄_{n,t}^g - ∑_{τ=0}^{t-1} x_{n,t-τ}^{g,su}
                        startup_sum = gp.quicksum(
                            self.variables[f'x_su_{tech}'][n, t-tau] for tau in range(t)
                        )
                        self.model.addConstr(
                            self.variables[f'x_sd_{tech}'][n, t] <= 
                            self.variables[f'p_bar_{tech}'][n, t-1] - startup_sum,
                            name=f'shutdown_upper_bound_early_{tech}_{n}_{t}'
                        )
                    else:
                        # UT_n^g ≤ t < T: p̄_{n,t}^g - ∑_{τ=0}^{UT_n^g-2} x_{n,t-τ}^{g,su}
                        startup_sum = gp.quicksum(
                            self.variables[f'x_su_{tech}'][n, t-tau] for tau in range(min_up_time-1)
                        )
                        self.model.addConstr(
                            self.variables[f'x_sd_{tech}'][n, t] <= 
                            self.variables[f'p_bar_{tech}'][n, t-1] - startup_sum,
                            name=f'shutdown_upper_bound_late_{tech}_{n}_{t}'
                        )
                
                # 公式(11): 火电启动约束
                # 0 ≤ x_{n,1}^{g,su} ≤ I_n^{g,0} + I_n^g - p̄_{n,0}^g
                if self.params.time_periods > 0:
                    initial_online = self.data.get('initial_online', {}).get(f'{tech}_{n}', 0)
                    self.model.addConstr(
                        self.variables[f'x_su_{tech}'][n, 0] >= 0,
                        name=f'startup_lower_bound_{tech}_{n}_0'
                    )
                    self.model.addConstr(
                        self.variables[f'x_su_{tech}'][n, 0] <= total_capacity - initial_online,
                        name=f'startup_upper_bound_{tech}_{n}_0'
                    )
                
                # 公式(12): 火电启动约束（分段函数）
                for t in range(1, self.params.time_periods):
                    self.model.addConstr(
                        self.variables[f'x_su_{tech}'][n, t] >= 0,
                        name=f'startup_lower_bound_{tech}_{n}_{t}'
                    )
                    
                    initial_online = self.data.get('initial_online', {}).get(f'{tech}_{n}', 0)
                    
                    if t < min_down_time:
                        # 1 ≤ t < DT_n^g: I_n^{g,0} + I_n^g - p̄_{n,0}^g - ∑_{τ=0}^{t-1} x_{n,t-τ}^{g,sd}
                        shutdown_sum = gp.quicksum(
                            self.variables[f'x_sd_{tech}'][n, t-tau] for tau in range(t)
                        )
                        self.model.addConstr(
                            self.variables[f'x_su_{tech}'][n, t] <= 
                            total_capacity - initial_online - shutdown_sum,
                            name=f'startup_upper_bound_early_{tech}_{n}_{t}'
                        )
                    else:
                        # DT_n^g ≤ t < T: I_n^{g,0} + I_n^g - p̄_{n,0}^g - ∑_{τ=0}^{DT_n^g-2} x_{n,t-τ}^{g,sd}
                        shutdown_sum = gp.quicksum(
                            self.variables[f'x_sd_{tech}'][n, t-tau] for tau in range(min_down_time-1)
                        )
                        self.model.addConstr(
                            self.variables[f'x_su_{tech}'][n, t] <= 
                            total_capacity - initial_online - shutdown_sum,
                            name=f'startup_upper_bound_late_{tech}_{n}_{t}'
                        )
    
    def add_renewable_constraints(self):
        """公式(13-18): 可再生能源约束"""
        countries = self.params.countries
        T = range(self.params.time_periods)
        
        for tech in self.params.renewable_techs:
            for n in countries:
                existing_cap = self.data.get('existing_capacity', {}).get(f'{tech}_{n}', 0)
                potential_limit = self.data.get('potential', {}).get(f'{tech}_{n}', float('inf'))
                
                # 潜力限制约束
                self.model.addConstr(
                    self.variables[f'I_{tech}'][n] <= potential_limit - existing_cap,
                    name=f'potential_limit_{tech}_{n}'
                )
                
                for t in T:
                    # 容量因子约束
                    capacity_factor = self.data.get('capacity_factors', {}).get((tech, n, t), 0)
                    total_capacity = existing_cap + self.variables[f'I_{tech}'][n]
                    
                    self.model.addConstr(
                        self.variables[f'p_{tech}'][n, t] <= 
                        capacity_factor * total_capacity,
                        name=f'capacity_factor_{tech}_{n}_{t}'
                    )
    
    def add_biomass_constraints(self):
        """公式(19): 生物质能约束"""
        # 生物质能已包含在火电约束中，这里添加潜力限制
        countries = self.params.countries
        
        if 'bio' in self.params.thermal_techs:
            for n in countries:
                existing_cap = self.data.get('existing_capacity', {}).get(f'bio_{n}', 0)
                potential_limit = self.data.get('potential', {}).get(f'bio_{n}', float('inf'))
                
                self.model.addConstr(
                    self.variables['I_bio'][n] <= potential_limit - existing_cap,
                    name=f'biomass_potential_{n}'
                )
    
    def add_nuclear_geo_constraints(self):
        """公式(20-24): 核电和地热约束"""
        countries = self.params.countries
        T = range(self.params.time_periods)
        months = range(12)
        
        for n in countries:
            # 核电月度容量系数约束（公式20）
            nuclear_cap = self.data.get('existing_capacity', {}).get(f'nuclear_{n}', 0)
            
            for m in months:
                monthly_capacity_factor = self.data.get('monthly_capacity_factors', {}).get(('nuclear', n, m), 0)
                
                # 获取该月份的所有小时
                month_hours = self._get_month_hours(m)
                
                for t in month_hours:
                    if t < self.params.time_periods:
                        self.model.addConstr(
                            self.variables['p_nuclear'][n, t] == 
                            monthly_capacity_factor * nuclear_cap,
                            name=f'nuclear_monthly_{n}_{t}'
                        )
            
            # 地热月度容量系数约束（公式21-22）
            geo_cap = self.data.get('existing_capacity', {}).get(f'geo_{n}', 0)
            
            for m in months:
                monthly_capacity_factor = self.data.get('monthly_capacity_factors', {}).get(('geo', n, m), 0)
                month_hours = self._get_month_hours(m)
                
                monthly_generation = gp.quicksum(
                    self.variables['p_geo'][n, t] for t in month_hours 
                    if t < self.params.time_periods
                )
                
                self.model.addConstr(
                    monthly_generation <= 
                    monthly_capacity_factor * geo_cap * len(month_hours),
                    name=f'geo_monthly_{n}_{m}'
                )
            
            for t in T:
                self.model.addConstr(
                    self.variables['p_geo'][n, t] <= geo_cap,
                    name=f'geo_capacity_{n}_{t}'
                )
            
            # 扩张限制约束（公式23-24）
            # 假设地热和核电不扩张
            if 'I_geo' in self.variables:
                self.model.addConstr(
                    self.variables['I_geo'][n] == 0,
                    name=f'geo_no_expansion_{n}'
                )
            
            if 'I_nuclear' in self.variables:
                self.model.addConstr(
                    self.variables['I_nuclear'][n] == 0,
                    name=f'nuclear_no_expansion_{n}'
                )
    
    def add_hydro_constraints(self):
        """公式(25-33): 水电约束 - 参考CISPO模型，支持站点级别建模"""
        countries = self.params.countries
        T = range(self.params.time_periods)
        
        for n in countries:
            # 公式(25): 径流式水电装机容量边界约束
            # I_n^{hydro_ror} ≤ I_n^{hydro_ror,p} - I_n^{hydro_ror,0}
            existing_ror_cap = self.data.get('existing_capacity', {}).get(f'hydro_ror_{n}', 0)
            potential_ror_cap = self.data.get('potential', {}).get(f'hydro_ror_{n}', float('inf'))
            
            self.model.addConstr(
                self.variables['I_hydro_ror'][n] <= potential_ror_cap - existing_ror_cap,
                name=f'ror_capacity_potential_{n}'
            )
            
            # 公式(26): 径流式水电出力约束  
            # p_{n,t}^{ror} ≤ α_{n,t}^{ror} · (I_n^{hydro_ror,0} + I_n^{hydro_ror})
            for t in T:
                capacity_factor_ror = self.data.get('capacity_factors', {}).get(('hydro_ror', n, t), 0.4)
                total_ror_capacity = existing_ror_cap + self.variables['I_hydro_ror'][n]
                
                self.model.addConstr(
                    self.variables['p_hydro_ror'][n, t] <= 
                    capacity_factor_ror * total_ror_capacity,
                    name=f'ror_capacity_factor_{n}_{t}'
                )
            
            # 水库式水电站点级别约束
            for h in self.params.hydro_stations.get(n, []):
                # 公式(27): 水库式水电装机容量约束（站点级别）
                # I_{n,h}^{hydro_res,0} + I_{n,h}^{hydro_res} ≤ I_{n,h}^{hydro_res,p}
                existing_res_cap = self.data.get('existing_capacity', {}).get(f'hydro_res_{n}_{h}', 0)
                potential_res_cap = self.data.get('potential', {}).get(f'hydro_res_{n}_{h}', float('inf'))
                
                self.model.addConstr(
                    existing_res_cap + self.variables['I_hydro_res'][n, h] <= potential_res_cap,
                    name=f'reservoir_capacity_potential_{n}_{h}'
                )
                
                # 公式(28): 水库首末时段一致性约束
                # v_{n,h,1} = v_{n,h,8760}
                if self.params.time_periods > 1:
                    self.model.addConstr(
                        self.variables['v'][n, h, 0] == self.variables['v'][n, h, self.params.time_periods-1],
                        name=f'reservoir_cycle_consistency_{n}_{h}'
                    )
                
                # 公式(29): 水库容量边界约束
                # V_{n,h}^{min} ≤ v_{n,h,t} ≤ V_{n,h}^{max}
                for t in T:
                    min_storage = self.data.get('min_storage', {}).get(f'{n}_{h}', 0)
                    max_storage = self.data.get('max_storage', {}).get(f'{n}_{h}', float('inf'))
                    
                    self.model.addConstr(
                        self.variables['v'][n, h, t] >= min_storage,
                        name=f'reservoir_min_level_{n}_{h}_{t}'
                    )
                    
                    self.model.addConstr(
                        self.variables['v'][n, h, t] <= max_storage,
                        name=f'reservoir_max_level_{n}_{h}_{t}'
                    )
                
                # 公式(30): 水库式水电发电与用水量关系约束
                # p_{n,h,t}^{res} = η_{n,h}^{hydro} · q_{n,h,t}^{out}
                for t in T:
                    hydro_efficiency = self.data.get('hydro_efficiency', {}).get(f'{n}_{h}', 0.85)
                    
                    self.model.addConstr(
                        self.variables['p_hydro_res'][n, h, t] == 
                        hydro_efficiency * self.variables['q_out'][n, h, t],
                        name=f'reservoir_generation_flow_relation_{n}_{h}_{t}'
                    )
                
                # 公式(31): 水库式水电出力装机容量约束
                # p_{n,h,t}^{res} ≤ I_{n,h}^{res,0} + I_{n,h}^{res}
                for t in T:
                    total_res_capacity = existing_res_cap + self.variables['I_hydro_res'][n, h]
                    
                    self.model.addConstr(
                        self.variables['p_hydro_res'][n, h, t] <= total_res_capacity,
                        name=f'reservoir_capacity_limit_{n}_{h}_{t}'
                    )
                
                # 公式(32): 水量平衡约束
                # q_{n,h,t}^{out} + q_{n,h,t}^{spill} ≤ Q_{n,h,t}^{in} + v_{n,h,t-1} - V_{n,h}^{min}
                for t in T:
                    inflow = self.data.get('hydro_inflow', {}).get((n, h, t), 0)
                    min_storage = self.data.get('min_storage', {}).get(f'{n}_{h}', 0)
                    
                    if t == 0:
                        # 初始时段使用初始库容
                        initial_storage = self.data.get('initial_storage', {}).get(f'{n}_{h}', 0)
                        self.model.addConstr(
                            self.variables['q_out'][n, h, t] + self.variables['q_spill'][n, h, t] <= 
                            inflow + initial_storage - min_storage,
                            name=f'water_balance_init_{n}_{h}_{t}'
                        )
                    else:
                        self.model.addConstr(
                            self.variables['q_out'][n, h, t] + self.variables['q_spill'][n, h, t] <= 
                            inflow + self.variables['v'][n, h, t-1] - min_storage,
                            name=f'water_balance_{n}_{h}_{t}'
                        )
                
                # 公式(33): 水库容量平衡方程
                # v_{n,h,t} = v_{n,h,t-1} + Q_{n,h,t}^{in} - q_{n,h,t}^{out} - q_{n,h,t}^{spill}
                for t in T:
                    inflow = self.data.get('hydro_inflow', {}).get((n, h, t), 0)
                    
                    if t == 0:
                        # 初始时段
                        initial_storage = self.data.get('initial_storage', {}).get(f'{n}_{h}', 0)
                        self.model.addConstr(
                            self.variables['v'][n, h, t] == 
                            initial_storage + inflow - 
                            self.variables['q_out'][n, h, t] - 
                            self.variables['q_spill'][n, h, t],
                            name=f'reservoir_level_balance_init_{n}_{h}_{t}'
                        )
                    else:
                        self.model.addConstr(
                            self.variables['v'][n, h, t] == 
                            self.variables['v'][n, h, t-1] + inflow - 
                            self.variables['q_out'][n, h, t] - 
                            self.variables['q_spill'][n, h, t],
                            name=f'reservoir_level_balance_{n}_{h}_{t}'
                        )
    
    def add_storage_constraints(self):
        """公式(34-38): 储能约束"""
        countries = self.params.countries
        T = range(self.params.time_periods)
        
        for tech in self.params.storage_techs:
            for n in countries:
                existing_power = self.data.get('existing_capacity', {}).get(f'{tech}_P_{n}', 0)
                existing_energy = self.data.get('existing_capacity', {}).get(f'{tech}_E_{n}', 0)
                
                for t in T:
                    # 公式(34-35): 功率约束
                    total_power = existing_power + self.variables[f'I_{tech}_P'][n]
                    
                    self.model.addConstr(
                        self.variables[f'p_ch_{tech}'][n, t] <= total_power,
                        name=f'storage_ch_power_{tech}_{n}_{t}'
                    )
                    
                    self.model.addConstr(
                        self.variables[f'p_dis_{tech}'][n, t] <= total_power,
                        name=f'storage_dis_power_{tech}_{n}_{t}'
                    )
                    
                    # 公式(36): 能量水平约束
                    total_energy = existing_energy + self.variables[f'I_{tech}_E'][n]
                    min_energy_ratio = self.data.get('min_energy_ratio', {}).get(tech, 0.1)
                    max_energy_ratio = self.data.get('max_energy_ratio', {}).get(tech, 0.9)
                    
                    self.model.addConstr(
                        self.variables[f'e_{tech}'][n, t] >= 
                        min_energy_ratio * total_energy,
                        name=f'storage_min_energy_{tech}_{n}_{t}'
                    )
                    
                    self.model.addConstr(
                        self.variables[f'e_{tech}'][n, t] <= 
                        max_energy_ratio * total_energy,
                        name=f'storage_max_energy_{tech}_{n}_{t}'
                    )
                    
                    # 公式(37): 能量平衡约束
                    efficiency = self.data.get('storage_efficiency', {}).get(tech, 0.85)
                    
                    if t == 0:
                        initial_energy = self.data.get('initial_energy', {}).get(f'{tech}_{n}', 0)
                        self.model.addConstr(
                            self.variables[f'e_{tech}'][n, t] == 
                            initial_energy + 
                            self.variables[f'p_ch_{tech}'][n, t] * efficiency - 
                            self.variables[f'p_dis_{tech}'][n, t] / efficiency,
                            name=f'energy_balance_init_{tech}_{n}_{t}'
                        )
                    else:
                        self.model.addConstr(
                            self.variables[f'e_{tech}'][n, t] == 
                            self.variables[f'e_{tech}'][n, t-1] + 
                            self.variables[f'p_ch_{tech}'][n, t] * efficiency - 
                            self.variables[f'p_dis_{tech}'][n, t] / efficiency,
                            name=f'energy_balance_{tech}_{n}_{t}'
                        )
                
                # 公式(38): 首末时段平衡约束
                self.model.addConstr(
                    self.variables[f'e_{tech}'][n, 0] == 
                    self.variables[f'e_{tech}'][n, self.params.time_periods-1],
                    name=f'annual_balance_{tech}_{n}'
                )
    
    def add_transmission_constraints(self):
        """公式(39): 输电约束"""
        countries = self.params.countries
        T = range(self.params.time_periods)
        
        for n1 in countries:
            for n2 in countries:
                if n1 != n2:
                    existing_line = self.data.get('existing_transmission', {}).get((n1, n2), 0)
                    
                    for t in T:
                        total_capacity = existing_line + self.variables['L'][n1, n2]
                        
                        self.model.addConstr(
                            self.variables['p_trans'][n1, n2, t] <= total_capacity,
                            name=f'transmission_capacity_{n1}_{n2}_{t}'
                        )
                        
                        self.model.addConstr(
                            self.variables['p_trans'][n1, n2, t] >= -total_capacity,
                            name=f'transmission_capacity_neg_{n1}_{n2}_{t}'
                        )
    
    def add_reliability_constraints(self):
        """公式(40): 可靠性约束"""
        countries = self.params.countries
        
        for n in countries:
            total_reliable_capacity = 0
            
            # 火电容量贡献
            for tech in self.params.thermal_techs:
                existing_cap = self.data.get('existing_capacity', {}).get(f'{tech}_{n}', 0)
                reliability_factor = self.data.get('reliability_factors', {}).get(f'{tech}_{n}', 0.95)
                
                total_reliable_capacity += reliability_factor * (
                    existing_cap + self.variables[f'I_{tech}'][n]
                )
            
            # 可再生能源容量贡献
            for tech in self.params.renewable_techs:
                existing_cap = self.data.get('existing_capacity', {}).get(f'{tech}_{n}', 0)
                reliability_factor = self.data.get('reliability_factors', {}).get(f'{tech}_{n}', 0.1)
                
                total_reliable_capacity += reliability_factor * (
                    existing_cap + self.variables[f'I_{tech}'][n]
                )
            
            # 径流式水电容量贡献（国家级别）
            existing_ror_cap = self.data.get('existing_capacity', {}).get(f'hydro_ror_{n}', 0)
            reliability_factor_ror = self.data.get('reliability_factors', {}).get(f'hydro_ror_{n}', 0.8)
            
            total_reliable_capacity += reliability_factor_ror * (
                existing_ror_cap + self.variables['I_hydro_ror'][n]
            )
            
            # 水库式水电容量贡献（站点级别求和）
            for h in self.params.hydro_stations.get(n, []):
                existing_res_cap = self.data.get('existing_capacity', {}).get(f'hydro_res_{n}_{h}', 0)
                reliability_factor_res = self.data.get('reliability_factors', {}).get(f'hydro_res_{n}_{h}', 0.8)
                
                total_reliable_capacity += reliability_factor_res * (
                    existing_res_cap + self.variables['I_hydro_res'][n, h]
                )
            
            # 其他技术容量贡献
            for tech in self.params.other_techs:
                existing_cap = self.data.get('existing_capacity', {}).get(f'{tech}_{n}', 0)
                reliability_factor = self.data.get('reliability_factors', {}).get(f'{tech}_{n}', 0.9)
                
                total_reliable_capacity += reliability_factor * existing_cap
            
            # 可靠性需求
            reliability_requirement = self.data.get('reliability_requirement', {}).get(n, 0)
            
            self.model.addConstr(
                total_reliable_capacity >= reliability_requirement,
                name=f'reliability_{n}'
            )
    
    def _get_month_hours(self, month):
        """获取指定月份的小时索引"""
        # 简化实现，假设每月730小时（平均）
        hours_per_month = 730
        start_hour = month * hours_per_month
        end_hour = min((month + 1) * hours_per_month, self.params.time_periods)
        return range(start_hour, end_hour)


def add_constraints_to_model(model_instance):
    """为模型实例添加约束条件"""
    constraint_builder = ConstraintBuilder(model_instance)
    constraint_builder.add_all_constraints()
    return constraint_builder 