"""
全球电力系统扩展规划模型主运行脚本
Main Script for Global Power System Expansion Planning

整合数据管理、模型构建、约束添加和求解的完整流程
支持水库式水电站点级别建模
"""

import logging
import time
from pathlib import Path

# 模型组件导入
from global_power_model import GlobalPowerModel, ModelParameters
from data_manager import create_data_manager
from constraints import add_constraints_to_model

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('model_run.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def create_complete_model():
    """创建完整的全球电力系统模型"""
    
    # 1. 数据准备
    logger.info("=" * 60)
    logger.info("开始创建全球电力系统扩展规划模型")
    logger.info("=" * 60)
    
    # 创建数据管理器并加载数据
    logger.info("1. 加载数据...")
    data_manager = create_data_manager()
    model_data = data_manager.get_model_data()
    
    # 2. 获取水电站点信息
    hydro_stations = {}
    for country in data_manager.countries:
        hydro_stations[country] = data_manager._get_country_hydro_stations(country)
    
    # 3. 创建模型参数
    logger.info("2. 设置模型参数...")
    model_params = ModelParameters(
        countries=data_manager.countries,
        time_periods=100,  # 简化测试：100小时而非8760小时
        hydro_stations=hydro_stations  # 传递水电站点信息
    )
    
    logger.info(f"   - 国家数量: {len(model_params.countries)}")
    logger.info(f"   - 时间段数: {model_params.time_periods}")
    logger.info(f"   - 火电技术: {model_params.thermal_techs}")
    logger.info(f"   - 可再生能源: {model_params.renewable_techs}")
    logger.info(f"   - 水电技术: {model_params.hydro_techs}")
    logger.info(f"   - 水电站点数: {sum(len(stations) for stations in hydro_stations.values())}")
    
    # 4. 创建模型
    logger.info("3. 创建优化模型...")
    model = GlobalPowerModel(model_params)
    
    # 5. 加载数据到模型
    logger.info("4. 加载数据到模型...")
    model.load_data(model_data)
    
    # 6. 添加变量
    logger.info("5. 添加决策变量...")
    model.add_variables()
    
    # 7. 添加约束
    logger.info("6. 添加约束条件...")
    constraint_builder = add_constraints_to_model(model)
    
    # 8. 设置目标函数
    logger.info("7. 设置目标函数...")
    model.set_objective()
    
    logger.info("模型构建完成！")
    return model, constraint_builder

def solve_model(model):
    """求解模型"""
    logger.info("=" * 40)
    logger.info("开始求解模型")
    logger.info("=" * 40)
    
    start_time = time.time()
    
    try:
        # 设置求解参数
        logger.info("设置求解参数...")
        model.solve(time_limit=300, gap=0.01)  # 5分钟时间限制，1%间隙
        
        solve_time = time.time() - start_time
        logger.info(f"求解耗时: {solve_time:.2f} 秒")
        
        # 获取结果
        if model.model.status == 2:  # OPTIMAL
            logger.info("获取求解结果...")
            solution = model.get_solution()
            
            # 保存结果
            logger.info("保存求解结果...")
            model.save_results("global_power_solution")
            
            return solution
        else:
            logger.error(f"模型求解失败，状态码: {model.model.status}")
            return None
            
    except Exception as e:
        logger.error(f"求解过程中出现错误: {e}")
        return None

def analyze_results(solution):
    """分析求解结果"""
    if solution is None:
        logger.warning("无有效解，跳过结果分析")
        return
    
    logger.info("=" * 40)
    logger.info("结果分析")
    logger.info("=" * 40)
    
    # 分析装机容量结果
    logger.info("装机容量分析:")
    for tech, capacity_data in solution.get('capacity', {}).items():
        if tech == 'hydro_res':
            # 水库式水电站点级别分析
            logger.info(f"  {tech} (站点级别):")
            total_capacity = 0
            for country, stations in capacity_data.items():
                country_total = sum(stations.values())
                total_capacity += country_total
                if country_total > 0:
                    logger.info(f"    {country}: {country_total:.1f} MW ({len(stations)} 站点)")
            logger.info(f"    总计: {total_capacity:.1f} MW")
        else:
            # 其他技术（国家级别）
            total_capacity = sum(capacity_data.values())
            if total_capacity > 0:
                logger.info(f"  {tech}: {total_capacity:.1f} MW")
                # 显示前5个国家
                sorted_countries = sorted(capacity_data.items(), key=lambda x: x[1], reverse=True)[:5]
                for country, cap in sorted_countries:
                    if cap > 0:
                        logger.info(f"    {country}: {cap:.1f} MW")
    
    # 分析储能结果
    logger.info("\n储能系统分析:")
    for storage_type, capacity_data in solution.get('storage', {}).items():
        total_capacity = sum(capacity_data.values())
        if total_capacity > 0:
            logger.info(f"  {storage_type}: {total_capacity:.1f} MW/MWh")

def run_simplified_test():
    """运行简化测试版本"""
    logger.info("运行简化测试版本（5个国家，100小时）...")
    
    # 创建简化数据管理器
    from data_manager import DataManager
    
    dm = DataManager()
    dm.countries = ['CHN', 'USA', 'DEU', 'CAN', 'BRA']  # 5个代表性国家
    dm.load_demand_data()
    dm.load_capacity_factors()
    dm.load_existing_capacity()
    dm.load_cost_data()
    dm.load_potential_data()
    
    model_data = dm.get_model_data()
    
    # 获取水电站点信息
    hydro_stations = {}
    for country in dm.countries:
        hydro_stations[country] = dm._get_country_hydro_stations(country)
    
    # 创建简化模型参数
    model_params = ModelParameters(
        countries=dm.countries,
        time_periods=24,  # 24小时测试
        hydro_stations=hydro_stations
    )
    
    # 创建和求解模型
    model = GlobalPowerModel(model_params)
    model.load_data(model_data)
    model.add_variables()
    
    # 添加约束
    constraint_builder = add_constraints_to_model(model)
    
    # 设置目标函数
    model.set_objective()
    
    # 求解
    logger.info("开始求解简化模型...")
    solution = solve_model(model)
    
    # 分析结果
    analyze_results(solution)
    
    return model, solution

def main():
    """主函数"""
    logger.info("全球电力系统扩展规划模型启动")
    
    try:
        # 检查是否运行完整模型
        run_full_model = False  # 设置为False运行简化测试
        
        if run_full_model:
            # 运行完整模型
            model, constraint_builder = create_complete_model()
            solution = solve_model(model)
            analyze_results(solution)
        else:
            # 运行简化测试
            model, solution = run_simplified_test()
        
        logger.info("=" * 60)
        logger.info("模型运行完成")
        logger.info("=" * 60)
        
        return model, solution
        
    except Exception as e:
        logger.error(f"模型运行过程中出现错误: {e}")
        raise

if __name__ == "__main__":
    model, solution = main() 