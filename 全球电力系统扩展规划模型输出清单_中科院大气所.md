# 全球电力系统扩展规划模型输出清单
## 面向中科院大气所气象环境影响研究

---

## 📋 **模型概述**

本模型采用**双层嵌套建模框架**，构建了8760小时分辨率的电力系统扩展规划优化模型：

### **🌍 全球层（国家级）**
- **空间范围**：全球202个国家
- **功能**：宏观容量配置和跨国电力贸易优化
- **输出**：国家级装机容量和发电量时序

### **🔍 国家层（网格级）**  
- **空间分辨率**：0.5° × 0.5°网格（约55km×55km）
- **功能**：国家内部风光资源精细化部署配置
- **输出**：网格级装机容量空间分布和发电时序

**双层模型为大气环境影响评估提供了从宏观到精细的多尺度能源基础设施数据，满足不同分辨率大气模型的数据需求。**

---

## 🌍 **核心输出类别**

### **1. 发电装机容量空间分布**
> *关键用途：识别能源基础设施对地表特征和局地气候的影响*

#### **🌍 1.1 国家级装机分布**

**可再生能源装机分布**
- **陆上风电装机容量** `I_{n}^{onw}` (MW)
  - 每个国家的陆上风电总装机容量
  - 支持国家级能源政策和排放清单研究
  
- **海上风电装机容量** `I_{n}^{offw}` (MW)
  - 每个国家的海上风电总装机容量
  - 评估海域开发对海洋-大气系统的宏观影响

- **太阳能光伏装机容量** `I_{n}^{solar}` (MW)
  - 每个国家的太阳能总装机容量
  - 国家级地表覆盖变化和碳排放分析

**传统能源装机分布**
- **火电装机容量** `I_{n}^{g}` (MW), g ∈ {coal, gas, oil, bio}
  - 煤电、气电、油电、生物质能的国家级装机分布
  - 国家级大气污染物和温室气体排放评估基础

- **水电装机容量** `I_{n,h}^{hydro_res}`, `I_{n}^{hydro_ror}` (MW)
  - 水库式和径流式水电装机容量
  - 国家级水资源利用和流域气候影响评估

#### **🔍 1.2 网格级装机分布（0.5°分辨率）**

**可再生能源精细分布**
- **陆上风电网格装机** `I_{i,j}^{onw}` (MW)
  - 每个0.5°网格内的风电装机容量密度
  - **大气应用**：精确计算地表粗糙度变化和风场扰动效应
  - **WRF耦合**：直接作为地表特征参数输入

- **海上风电网格装机** `I_{i,j}^{offw}` (MW)
  - 每个海洋网格内的风电装机容量密度
  - **大气应用**：量化海洋-大气界面动量和能量交换变化
  - **海洋模型耦合**：评估海上风电对海表温度和海流的影响

- **太阳能网格装机** `I_{i,j}^{solar}` (MW)
  - 每个网格内的光伏装机容量密度
  - **大气应用**：精确计算地表反照率变化和局地温度效应
  - **陆面模型耦合**：作为地表覆盖类型参数的修正

### **2. 发电量时空分布特征**
> *关键用途：量化不同时间尺度的排放强度和环境负荷*

#### **🌍 2.1 国家级发电时序（8760小时分辨率）**

**火电发电量时序**
- **国家火电出力** `p_{n,t}^{g}` (MW), t=1,2,...,8760
  - 每小时的国家级火电出力，支持国家排放清单编制
  - 为全球/区域大气模型提供排放源强时间变化

**可再生能源发电时序**
- **国家风光出力**：`p_{n,t}^{onw}`, `p_{n,t}^{offw}`, `p_{n,t}^{solar}` (MW)
  - 反映国家级可再生能源与气象条件的耦合关系
  - 支持能源气象学和气候变化影响评估研究

#### **🔍 2.2 网格级发电时序（0.5°×8760小时）**

**网格风光发电精细时序**
- **风电网格出力** `p_{i,j,t}^{onw}`, `p_{i,j,t}^{offw}` (MW)
  - 每个网格每小时的风电发电量
  - **大气应用**：评估风电运行对局地风场的动态反馈
  - **数值模拟**：为高分辨率WRF模型提供风电场运行状态

- **太阳能网格出力** `p_{i,j,t}^{solar}` (MW)
  - 每个网格每小时的光伏发电量
  - **大气应用**：量化光伏板对地表辐射平衡的实时影响
  - **陆面耦合**：评估大规模光伏电站的局地气候效应

**网格级容量因子时序**
- **风电容量因子** `CF_{i,j,t}^{wind} = p_{i,j,t}^{wind} / I_{i,j}^{wind}`
- **光伏容量因子** `CF_{i,j,t}^{solar} = p_{i,j,t}^{solar} / I_{i,j}^{solar}`
- **大气应用**：直接反映气象条件变化对能源系统的影响

#### **2.3 多尺度时间特征分析**

**季节性模式**
- **月度发电模式**：国家级和网格级月均发电量
- **季节变化**：揭示能源系统与季节性气象模式的耦合
- **极端天气响应**：量化极端气象事件对能源系统的冲击

**日变化模式** 
- **24小时发电曲线**：支持污染物日变化和光化学过程研究
- **储能运行模式**：`p_{n,t}^{z,ch}`, `p_{n,t}^{z,dis}` - 平滑可再生能源波动

### **3. 能源结构转型轨迹**
> *关键用途：评估能源转型对大气成分和气候的长期影响*

#### **3.1 技术占比变化**
- **可再生能源渗透率**
  - 风电/太阳能在总装机中的比重
  - 清洁能源替代化石能源的时空模式

- **化石能源退出轨迹**
  - 煤电装机和发电量变化趋势
  - 天然气作为过渡能源的作用

#### **3.2 区域能源流动**
- **跨国电力贸易** `p_{l,t}` (MW)
  - 输电走廊的功率传输时序
  - 能源跨区域优化配置对排放空间分布的影响

---

## 🔬 **大气环境研究关键输出**

### **4. 排放源强时空分布**
> *基于发电量数据可推算的排放特征*

#### **4.1 温室气体排放**
- **CO₂排放强度**
  - 基于火电发电量计算：`∑FC_{n}^{g} × p_{n,t}^{g}`
  - 8760小时分辨率的碳排放时序

- **排放空间分布**
  - 国家级排放清单
  - 电力行业排放占比变化

#### **4.2 大气污染物排放**
- **常规污染物**
  - SO₂、NOₓ、PM₂.₅排放（需结合排放因子）
  - 与火电机组类型和运行工况相关

- **排放时变特征**
  - 日变化、季变化、年际变化
  - 支持大气化学模型的排放清单

### **5. 地表能量平衡影响**
> *可再生能源部署的地表环境效应*

#### **5.1 地表特征变化**
- **风电场效应**
  - 风电装机密度空间分布
  - 对地表粗糙度和局地风场的影响

- **光伏电站效应**
  - 太阳能装机空间分布
  - 地表反照率变化和局地温度效应

#### **5.2 水资源影响**
- **水电开发** 
  - 水库式水电库容：`v_{n,h,t}`
  - 对流域水循环和局地气候的影响

---

## 📊 **数据格式与时空分辨率**

### **6. 双层空间分辨率体系**

#### **🌍 全球层（国家级）**
- **空间单元**：202个国家（ISO 3166标准编码）
- **结果汇总**：可按20个气候/地理区域进行汇总统计
- **坐标系统**：WGS84地理坐标系
- **应用场景**：全球/区域大气模型、国家排放清单

#### **🔍 国家层（网格级）**
- **空间分辨率**：0.5° × 0.5°经纬度网格（约55km×55km）
- **网格数量**：全球720×360个网格点
- **投影方式**：等经纬度投影，与主流气象数据一致
- **坐标范围**：经度-180°到+180°，纬度-90°到+90°
- **应用场景**：高分辨率WRF/CMAQ模型、局地气候评估

### **7. 时间分辨率规格**
- **基础时间步长**：1小时
- **时间跨度**：8760小时（完整年度，365天×24小时）
- **时间标准**：UTC协调世界时（可提供本地时间转换）
- **时间格式**：YYYY-MM-DD HH:00:00
- **闰年处理**：标准年365天，特殊需求可扩展至366天

### **8. 分层数据格式标准**

#### **🌍 国家级数据格式**
```
数据类型            | 维度                      | 单位    | 文件格式
国家装机容量       | [202国家 × 9技术类型]       | MW     | CSV/NetCDF
国家发电时序       | [202国家 × 9技术 × 8760h]   | MW     | NetCDF/HDF5
储能运行时序       | [202国家 × 2储能 × 8760h]   | MWh    | NetCDF
跨国输电时序       | [输电走廊 × 8760h]         | MW     | CSV/NetCDF
```

#### **🔍 网格级数据格式**
```
数据类型            | 维度                         | 单位      | 文件格式
网格装机密度       | [720×360网格 × 风光技术]      | MW/grid   | NetCDF/GeoTIFF
网格发电时序       | [720×360网格 × 风光 × 8760h]  | MW/grid   | NetCDF4
网格容量因子       | [720×360网格 × 风光 × 8760h]  | 无量纲     | NetCDF4
地表影响参数       | [720×360网格 × 地表特征]      | 多种单位   | NetCDF/GeoTIFF
```

#### **📋 元数据标准**
- **CF约定兼容**：遵循Climate and Forecast元数据约定
- **坐标变量**：lon, lat, time标准命名
- **属性信息**：包含单位、长名称、数据来源、处理方法
- **质量标志**：数据质量控制标识和缺失值处理

---

## 🎯 **大气环境应用建议**

### **9. 直接应用**
- **排放清单构建**：基于发电量数据计算实时排放
- **气象边界条件**：为大气模型提供能源系统边界条件
- **影响评估基础**：评估能源基础设施对局地气候的影响

### **10. 耦合研究方向**
- **WRF模型耦合**：将风光装机分布纳入地表特征
- **CMAQ/GEOS-Chem耦合**：集成排放清单进行空气质量模拟
- **气象反馈**：研究气候变化对可再生能源的反馈影响

### **11. 时间尺度匹配**
- **短期**：小时级数据支持污染过程研究
- **季节**：月度模式分析能源-气象耦合
- **长期**：年度趋势评估气候影响

---

## 📧 **数据交付说明**

### **输出文件清单**
1. **装机容量矩阵**：`[国家 × 技术类型]`
2. **发电量时序**：`[国家 × 技术类型 × 8760小时]`
3. **储能运行时序**：`[国家 × 储能类型 × 8760小时]`
4. **跨国输电时序**：`[输电走廊 × 8760小时]`
5. **系统运行统计**：年度/月度/日均统计指标

### **数据质量保证**
- 能量守恒验证
- 时序连续性检查
- 空间一致性校验

### **技术支持**
- 数据格式转换支持
- 模型耦合技术咨询
- 结果解释和应用指导

---

**联系方式**：[项目组联系信息]
**更新日期**：2025年01月
**版本号**：V1.0 