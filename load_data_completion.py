#!/usr/bin/env python3
"""
全球电力负荷时序数据补全脚本

基于相似性匹配算法，使用GDP、人口等特征为缺失时序数据的国家
生成8760小时的负荷曲线，替代简单的平均分配方法。

作者: AI Assistant
日期: 2025-07-28
"""

import pandas as pd
import numpy as np
import os
from sklearn.preprocessing import StandardScaler
from sklearn.metrics.pairwise import cosine_similarity
import warnings
warnings.filterwarnings('ignore')

class LoadDataCompletion:
    """负荷数据补全类"""
    
    def __init__(self):
        """初始化"""
        self.load_data = None
        self.gdp_data = None
        self.population_data = None
        self.missing_countries = ['COG', 'GRL', 'HKG', 'KNA', 'KOS', 'LCA', 
                                 'MAC', 'NRU', 'PSE', 'SSD', 'SUR', 'TLS', 
                                 'TON', 'TWN', 'WSM']
        self.country_features = {}
        self.similarity_matrix = None
        
    def load_all_data(self):
        """加载所有数据文件"""
        print("正在加载数据文件...")
        
        # 加载负荷数据
        self.load_data = pd.read_excel("Load/Load_TimeSeries_2023.xlsx")
        print(f"负荷数据形状: {self.load_data.shape}")
        
        # 加载GDP数据
        gdp_df = pd.read_csv("Load/GDP_INT-Export-07-28-2025_17-43-58.csv", skiprows=2)
        # 重新设置列名
        gdp_columns = ['API', 'Country'] + [str(year) for year in range(1980, 2025)]
        gdp_df.columns = gdp_columns[:len(gdp_df.columns)]
        self.gdp_data = gdp_df
        print(f"GDP数据形状: {self.gdp_data.shape}")
        
        # 加载人口数据
        pop_df = pd.read_csv("Load/Population_INT-Export-07-28-2025_17-44-34.csv", skiprows=2)
        # 重新设置列名
        pop_columns = ['API', 'Country'] + [str(year) for year in range(1980, 2025)]
        pop_df.columns = pop_columns[:len(pop_df.columns)]
        self.population_data = pop_df
        print(f"人口数据形状: {self.population_data.shape}")
        
    def extract_country_features(self):
        """提取国家特征数据"""
        print("正在提取国家特征...")

        for country in self.load_data.columns[1:]:  # 跳过Hours列
            features = {}

            # 获取年度负荷总量
            annual_load = self.load_data[country].sum()
            features['annual_load'] = annual_load

            # 获取GDP数据 - 使用简化的匹配方法
            gdp_value = self._get_country_data_simple(country, self.gdp_data, '2023')
            features['gdp'] = gdp_value if gdp_value is not None else 0

            # 获取人口数据 - 使用简化的匹配方法
            pop_value = self._get_country_data_simple(country, self.population_data, '2023')
            features['population'] = pop_value if pop_value is not None else 0

            # 计算人均负荷
            if features['population'] > 0:
                features['load_per_capita'] = annual_load / (features['population'] * 1000)  # 人口单位是千人
            else:
                features['load_per_capita'] = annual_load / 1000  # 默认假设1000千人

            # 计算负荷密度（负荷/GDP）
            if features['gdp'] > 0:
                features['load_intensity'] = annual_load / features['gdp']
            else:
                features['load_intensity'] = annual_load / 100  # 默认假设100亿美元GDP

            # 检查是否为缺失数据国家
            if country in self.missing_countries:
                # 检查负荷曲线是否为平均分配
                load_values = self.load_data[country].values
                is_flat = len(np.unique(np.round(load_values, 6))) <= 4  # 允许少量季节变化
                features['is_missing'] = is_flat
            else:
                features['is_missing'] = False

            self.country_features[country] = features

        print(f"提取了 {len(self.country_features)} 个国家的特征")

        # 打印一些示例特征
        missing_count = sum(1 for f in self.country_features.values() if f['is_missing'])
        print(f"识别出 {missing_count} 个缺失时序数据的国家")
        
    def _get_country_data_simple(self, country_code, data_df, year):
        """简化的国家数据获取方法"""
        try:
            # 创建一个简单的国家代码到名称的映射
            country_name_mapping = {
                'CHN': 'China', 'USA': 'United States', 'DEU': 'Germany', 'JPN': 'Japan',
                'GBR': 'United Kingdom', 'FRA': 'France', 'ITA': 'Italy', 'BRA': 'Brazil',
                'CAN': 'Canada', 'RUS': 'Russia', 'IND': 'India', 'AUS': 'Australia',
                'ESP': 'Spain', 'MEX': 'Mexico', 'KOR': 'Korea', 'IDN': 'Indonesia',
                'NLD': 'Netherlands', 'SAU': 'Saudi Arabia', 'TUR': 'Turkey', 'TWN': 'Taiwan',
                'CHE': 'Switzerland', 'BEL': 'Belgium', 'IRE': 'Ireland', 'ISR': 'Israel',
                'AUT': 'Austria', 'NGA': 'Nigeria', 'EGY': 'Egypt', 'ZAF': 'South Africa',
                'ARG': 'Argentina', 'THA': 'Thailand', 'BGD': 'Bangladesh', 'VNM': 'Vietnam',
                'PHL': 'Philippines', 'CHL': 'Chile', 'FIN': 'Finland', 'MYS': 'Malaysia',
                'NOR': 'Norway', 'DNK': 'Denmark', 'SGP': 'Singapore', 'NZL': 'New Zealand'
            }

            # 首先尝试使用映射
            if country_code in country_name_mapping:
                search_name = country_name_mapping[country_code]
                for _, row in data_df.iterrows():
                    if pd.notna(row['Country']):
                        country_name = str(row['Country']).strip()
                        if search_name.lower() in country_name.lower():
                            value = row.get(year, np.nan)
                            if pd.notna(value) and str(value) not in ['--', 'NA', '']:
                                try:
                                    return float(value)
                                except:
                                    pass

            # 如果映射失败，尝试直接匹配国家代码
            for _, row in data_df.iterrows():
                if pd.notna(row['Country']):
                    country_name = str(row['Country']).strip().upper()
                    if country_code.upper() in country_name:
                        value = row.get(year, np.nan)
                        if pd.notna(value) and str(value) not in ['--', 'NA', '']:
                            try:
                                return float(value)
                            except:
                                pass

        except Exception as e:
            pass  # 静默处理错误

        return None
        
    def calculate_similarity(self):
        """计算国家间相似性"""
        print("正在计算国家间相似性...")

        # 准备特征矩阵
        countries = list(self.country_features.keys())
        feature_names = ['annual_load', 'load_per_capita', 'load_intensity']

        feature_matrix = []
        valid_countries = []

        for country in countries:
            features = self.country_features[country]
            if not features['is_missing']:  # 只使用有完整时序数据的国家作为参考
                # 检查负荷曲线是否有变化（不是平均分配）
                load_curve = self.load_data[country].values
                curve_variation = load_curve.std() / load_curve.mean() if load_curve.mean() > 0 else 0

                if curve_variation > 0.01:  # 只选择有明显变化的负荷曲线
                    feature_vector = [features[f] for f in feature_names]
                    if all(f > 0 for f in feature_vector):  # 确保所有特征都有效
                        feature_matrix.append(feature_vector)
                        valid_countries.append(country)

        if len(feature_matrix) < 2:
            print("警告: 有效参考国家数量不足")
            return

        # 标准化特征
        scaler = StandardScaler()
        feature_matrix_scaled = scaler.fit_transform(feature_matrix)

        # 计算相似性矩阵
        self.similarity_matrix = cosine_similarity(feature_matrix_scaled)
        self.valid_countries = valid_countries

        print(f"计算了 {len(valid_countries)} 个参考国家的相似性矩阵")

        # 打印一些示例参考国家
        if len(valid_countries) > 0:
            print(f"示例参考国家: {valid_countries[:10]}")
        
    def find_similar_countries(self, target_country, top_k=3):
        """为目标国家找到最相似的参考国家"""
        if not hasattr(self, 'valid_countries') or len(self.valid_countries) == 0:
            print("错误: 没有有效的参考国家")
            return self._find_similar_by_load(target_country, top_k)

        target_features = self.country_features[target_country]
        feature_names = ['annual_load', 'load_per_capita', 'load_intensity']
        target_vector = [target_features[f] for f in feature_names]

        # 如果目标国家特征不完整，使用简单的年度负荷匹配
        if any(f <= 0 for f in target_vector):
            return self._find_similar_by_load(target_country, top_k)

        # 标准化目标特征
        scaler = StandardScaler()
        all_features = []
        for country in self.valid_countries:
            features = self.country_features[country]
            feature_vector = [features[f] for f in feature_names]
            all_features.append(feature_vector)
        all_features.append(target_vector)

        scaled_features = scaler.fit_transform(all_features)
        target_scaled = scaled_features[-1].reshape(1, -1)
        reference_scaled = scaled_features[:-1]

        # 计算相似性
        similarities = cosine_similarity(target_scaled, reference_scaled)[0]

        # 获取最相似的国家
        similar_indices = np.argsort(similarities)[::-1][:top_k]
        similar_countries = [(self.valid_countries[i], similarities[i]) for i in similar_indices]

        return similar_countries
        
    def _find_similar_by_load(self, target_country, top_k=3):
        """基于年度负荷总量找到相似国家"""
        target_load = self.country_features[target_country]['annual_load']

        # 如果没有valid_countries，创建一个基于所有非缺失国家的列表
        if not hasattr(self, 'valid_countries'):
            self.valid_countries = []
            for country in self.country_features:
                if not self.country_features[country]['is_missing']:
                    # 检查负荷曲线是否有变化
                    load_curve = self.load_data[country].values
                    curve_variation = load_curve.std() / load_curve.mean() if load_curve.mean() > 0 else 0
                    if curve_variation > 0.01:
                        self.valid_countries.append(country)

        candidates = []
        for country in self.valid_countries:
            if country != target_country:
                ref_load = self.country_features[country]['annual_load']
                if ref_load > 0:
                    similarity = 1 / (1 + abs(target_load - ref_load) / max(target_load, ref_load))
                    candidates.append((country, similarity))

        candidates.sort(key=lambda x: x[1], reverse=True)
        return candidates[:top_k]

    def generate_load_curve(self, target_country):
        """为目标国家生成负荷曲线"""
        print(f"正在为 {target_country} 生成负荷曲线...")

        # 获取数据的实际长度
        data_length = len(self.load_data)

        # 获取目标年度负荷总量
        target_annual_load = self.country_features[target_country]['annual_load']

        # 找到相似国家
        similar_countries = self.find_similar_countries(target_country, top_k=3)

        if not similar_countries:
            print(f"警告: 未找到 {target_country} 的相似国家，使用平均分配")
            return np.full(data_length, target_annual_load / data_length)

        print(f"为 {target_country} 找到的相似国家:")
        for country, similarity in similar_countries:
            print(f"  {country}: 相似度 {similarity:.3f}")

        # 获取相似国家的负荷曲线
        reference_curves = []
        weights = []

        for country, similarity in similar_countries:
            if country in self.load_data.columns:
                curve = self.load_data[country].values
                # 归一化到单位负荷
                annual_load = curve.sum()
                if annual_load > 0:
                    normalized_curve = curve / annual_load
                    reference_curves.append(normalized_curve)
                    weights.append(similarity)

        if not reference_curves:
            print(f"警告: 相似国家无有效负荷数据，使用平均分配")
            return np.full(data_length, target_annual_load / data_length)

        # 加权平均生成负荷模式
        weights = np.array(weights)
        weights = weights / weights.sum()  # 归一化权重

        combined_pattern = np.zeros(data_length)
        for i, curve in enumerate(reference_curves):
            combined_pattern += weights[i] * curve

        # 缩放到目标年度负荷总量
        generated_curve = combined_pattern * target_annual_load

        # 验证总量
        generated_total = generated_curve.sum()
        if abs(generated_total - target_annual_load) > 0.01:
            # 微调以确保总量匹配
            adjustment_factor = target_annual_load / generated_total
            generated_curve *= adjustment_factor

        return generated_curve

    def complete_missing_data(self):
        """补全所有缺失数据国家的负荷曲线"""
        print("开始补全缺失数据...")

        completed_data = self.load_data.copy()
        completion_report = {}

        for country in self.missing_countries:
            if country in self.country_features and self.country_features[country]['is_missing']:
                print(f"\n处理国家: {country}")

                # 生成新的负荷曲线
                new_curve = self.generate_load_curve(country)

                # 更新数据
                completed_data[country] = new_curve

                # 记录报告信息
                original_total = self.load_data[country].sum()
                new_total = new_curve.sum()

                completion_report[country] = {
                    'original_total': original_total,
                    'new_total': new_total,
                    'difference': abs(new_total - original_total),
                    'curve_variation': new_curve.std() / new_curve.mean() if new_curve.mean() > 0 else 0
                }

                print(f"  原始总量: {original_total:.2f} MWh")
                print(f"  新总量: {new_total:.2f} MWh")
                print(f"  差异: {abs(new_total - original_total):.2f} MWh")
                print(f"  曲线变异系数: {completion_report[country]['curve_variation']:.3f}")

        return completed_data, completion_report

    def save_results(self, completed_data, completion_report, output_file="Load_TimeSeries_2023_Completed.xlsx"):
        """保存结果"""
        print(f"\n保存结果到 {output_file}...")

        # 保存完整的负荷数据
        completed_data.to_excel(output_file, index=False)

        # 保存补全报告
        report_df = pd.DataFrame.from_dict(completion_report, orient='index')
        report_file = output_file.replace('.xlsx', '_Report.xlsx')
        report_df.to_excel(report_file)

        print(f"负荷数据已保存到: {output_file}")
        print(f"补全报告已保存到: {report_file}")

        # 打印总结
        print(f"\n=== 补全总结 ===")
        print(f"处理的国家数量: {len(completion_report)}")
        total_difference = sum(report['difference'] for report in completion_report.values())
        print(f"总负荷差异: {total_difference:.2f} MWh")
        avg_variation = np.mean([report['curve_variation'] for report in completion_report.values()])
        print(f"平均曲线变异系数: {avg_variation:.3f}")

    def run_completion(self):
        """运行完整的数据补全流程"""
        print("=== 全球电力负荷时序数据补全 ===\n")

        try:
            # 1. 加载数据
            self.load_all_data()

            # 2. 提取特征
            self.extract_country_features()

            # 3. 计算相似性
            self.calculate_similarity()

            # 4. 补全数据
            completed_data, completion_report = self.complete_missing_data()

            # 5. 保存结果
            self.save_results(completed_data, completion_report)

            print("\n数据补全完成！")

        except Exception as e:
            print(f"数据补全过程中出错: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    # 切换到正确的工作目录
    os.chdir("/Users/<USER>/PycharmProjects/Global_Power_System_Planning")

    # 创建补全器实例并运行
    completer = LoadDataCompletion()
    completer.run_completion()

if __name__ == "__main__":
    main()
