#!/usr/bin/env python3
"""
改进的全球电力负荷时序数据补全脚本
使用预选的参考国家进行负荷曲线生成
"""

import pandas as pd
import numpy as np
import os
from sklearn.preprocessing import StandardScaler
from sklearn.metrics.pairwise import cosine_similarity
import warnings
warnings.filterwarnings('ignore')

class ImprovedLoadDataCompletion:
    """改进的负荷数据补全类"""
    
    def __init__(self):
        self.load_data = None
        self.missing_countries = ['COG', 'GRL', 'HKG', 'KNA', 'KOS', 'LCA', 
                                 'MAC', 'NRU', 'PSE', 'SSD', 'SUR', 'TLS', 
                                 'TON', 'TWN', 'WSM']
        # 使用预选的有效参考国家
        self.reference_countries = []
        
    def load_data_file(self):
        """加载负荷数据文件"""
        print("正在加载负荷数据...")
        self.load_data = pd.read_excel("Load/Load_TimeSeries_2023.xlsx")
        print(f"负荷数据形状: {self.load_data.shape}")
        
        # 验证参考国家
        valid_refs = [c for c in self.reference_countries if c in self.load_data.columns]
        self.reference_countries = valid_refs
        print(f"有效参考国家: {len(self.reference_countries)} 个")
        
    def find_similar_countries_simple(self, target_country, top_k=3):
        """基于年度负荷总量的简单相似性匹配"""
        target_load = self.load_data[target_country].sum()
        
        similarities = []
        for ref_country in self.reference_countries:
            ref_load = self.load_data[ref_country].sum()
            if ref_load > 0:
                # 计算负荷总量相似性
                load_ratio = min(target_load, ref_load) / max(target_load, ref_load)
                similarities.append((ref_country, load_ratio))
        
        # 按相似性排序
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:top_k]
        
    def generate_load_curve(self, target_country):
        """为目标国家生成负荷曲线"""
        print(f"正在为 {target_country} 生成负荷曲线...")
        
        target_annual_load = self.load_data[target_country].sum()
        data_length = len(self.load_data)
        
        # 找到相似国家
        similar_countries = self.find_similar_countries_simple(target_country, top_k=3)
        
        if not similar_countries:
            print(f"警告: 未找到相似国家，使用平均分配")
            return np.full(data_length, target_annual_load / data_length)
        
        print(f"找到的相似国家:")
        for country, similarity in similar_countries:
            print(f"  {country}: 相似度 {similarity:.3f}")
        
        # 获取相似国家的负荷曲线并加权平均
        combined_pattern = np.zeros(data_length)
        total_weight = 0
        
        for country, similarity in similar_countries:
            curve = self.load_data[country].values
            annual_load = curve.sum()
            if annual_load > 0:
                normalized_curve = curve / annual_load
                combined_pattern += similarity * normalized_curve
                total_weight += similarity
        
        if total_weight > 0:
            combined_pattern /= total_weight
            generated_curve = combined_pattern * target_annual_load
        else:
            generated_curve = np.full(data_length, target_annual_load / data_length)
        
        return generated_curve
        
    def complete_all_missing_data(self):
        """补全所有缺失数据"""
        print("开始补全缺失数据...")
        
        completed_data = self.load_data.copy()
        completion_report = {}
        
        for country in self.missing_countries:
            if country in self.load_data.columns:
                print(f"\n处理国家: {country}")
                
                # 生成新的负荷曲线
                new_curve = self.generate_load_curve(country)
                
                # 更新数据
                completed_data[country] = new_curve
                
                # 记录报告
                original_total = self.load_data[country].sum()
                new_total = new_curve.sum()
                
                completion_report[country] = {
                    'original_total': original_total,
                    'new_total': new_total,
                    'difference': abs(new_total - original_total),
                    'curve_variation': new_curve.std() / new_curve.mean() if new_curve.mean() > 0 else 0
                }
                
                print(f"  原始总量: {original_total:.2f} MWh")
                print(f"  新总量: {new_total:.2f} MWh")
                print(f"  曲线变异系数: {completion_report[country]['curve_variation']:.3f}")
        
        return completed_data, completion_report
        
    def save_results(self, completed_data, completion_report):
        """保存结果"""
        output_file = "Load_TimeSeries_2023_Improved.xlsx"
        print(f"\n保存结果到 {output_file}...")
        
        completed_data.to_excel(output_file, index=False)
        
        report_df = pd.DataFrame.from_dict(completion_report, orient='index')
        report_file = output_file.replace('.xlsx', '_Report.xlsx')
        report_df.to_excel(report_file)
        
        print(f"负荷数据已保存到: {output_file}")
        print(f"补全报告已保存到: {report_file}")
        
        # 打印总结
        print(f"\n=== 补全总结 ===")
        print(f"处理的国家数量: {len(completion_report)}")
        avg_variation = np.mean([report['curve_variation'] for report in completion_report.values()])
        print(f"平均曲线变异系数: {avg_variation:.3f}")
        
    def run(self):
        """运行完整流程"""
        print("=== 改进的全球电力负荷时序数据补全 ===\n")
        
        try:
            self.load_data_file()
            completed_data, completion_report = self.complete_all_missing_data()
            self.save_results(completed_data, completion_report)
            print("\n数据补全完成！")
            
        except Exception as e:
            print(f"补全过程中出错: {e}")
            import traceback
            traceback.print_exc()

def main():
    os.chdir("/Users/<USER>/PycharmProjects/Global_Power_System_Planning")
    completer = ImprovedLoadDataCompletion()
    completer.run()

if __name__ == "__main__":
    main()
